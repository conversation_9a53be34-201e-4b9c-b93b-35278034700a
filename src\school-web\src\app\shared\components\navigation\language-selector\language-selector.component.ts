import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-language-selector',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    TranslateModule
  ],
  templateUrl: './language-selector.component.html',
  styleUrls: ['./language-selector.component.scss']
})
export class LanguageSelectorComponent implements OnInit {
  currentLang: string = 'en';
  
  languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'bn', name: 'বাংলা', flag: '🇧🇩' }
  ];
  
  constructor(private translate: TranslateService) {}
  
  ngOnInit(): void {
    this.currentLang = this.translate.currentLang || this.translate.defaultLang || 'en';
  }
  
  changeLanguage(langCode: string): void {
    this.translate.use(langCode);
    this.currentLang = langCode;
    localStorage.setItem('language', langCode);
  }
  
  getCurrentLanguage(): any {
    return this.languages.find(lang => lang.code === this.currentLang);
  }
}
