using School.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace School.Application.Features.Event
{
    public interface IEventService
    {
        Task<(IEnumerable<EventDto> Events, int TotalCount)> GetAllEventsAsync(EventFilterDto filter);
        Task<EventDto?> GetEventByIdAsync(Guid id);
        Task<Guid> CreateEventAsync(CreateEventDto eventDto);
        Task<bool> UpdateEventAsync(Guid id, UpdateEventDto eventDto);
        Task<bool> DeleteEventAsync(Guid id);
    }
}