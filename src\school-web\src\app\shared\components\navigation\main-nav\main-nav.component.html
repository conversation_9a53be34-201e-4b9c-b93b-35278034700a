<mat-sidenav-container class="sidenav-container">
  <!-- Mobile Drawer Menu -->
  <mat-sidenav #drawer class="sidenav" fixedInViewport
      [mode]="'over'"
      [opened]="false">
    <mat-toolbar color="primary" class="drawer-header">
      <span class="drawer-title">{{ 'SITE.TITLE' | translate }}</span>
      <button mat-icon-button class="drawer-close-button" (click)="drawer.close()" aria-label="Close menu">
        <mat-icon>close</mat-icon>
      </button>
    </mat-toolbar>
    <mat-nav-list>
      <!-- About Us -->
      <mat-expansion-panel>
        <mat-expansion-panel-header>
          <mat-panel-title>{{ 'NAV.ABOUT' | translate }}</mat-panel-title>
        </mat-expansion-panel-header>
        <a mat-list-item routerLink="/about" [class.active]="activeRoute === '/about'">
          {{ 'ABOUT.TITLE' | translate }}
        </a>
        <a mat-list-item routerLink="/about/history" [class.active]="activeRoute === '/about/history'">
          {{ 'ABOUT.HISTORY' | translate }}
        </a>
        <a mat-list-item routerLink="/about/mission" [class.active]="activeRoute === '/about/mission'">
          {{ 'ABOUT.MISSION' | translate }}
        </a>
        <a mat-list-item routerLink="/about/leadership" [class.active]="activeRoute === '/about/leadership'">
          {{ 'ABOUT.LEADERSHIP' | translate }}
        </a>
      </mat-expansion-panel>

      <!-- Academics -->
      <mat-expansion-panel>
        <mat-expansion-panel-header>
          <mat-panel-title>{{ 'NAV.ACADEMICS' | translate }}</mat-panel-title>
        </mat-expansion-panel-header>
        <a mat-list-item routerLink="/academics" [class.active]="activeRoute === '/academics'">
          {{ 'ACADEMICS.TITLE' | translate }}
        </a>
        <a mat-list-item routerLink="/academics/elementary" [class.active]="activeRoute === '/academics/elementary'">
          {{ 'ACADEMICS.ELEMENTARY' | translate }}
        </a>
        <a mat-list-item routerLink="/academics/middle" [class.active]="activeRoute === '/academics/middle'">
          {{ 'ACADEMICS.MIDDLE' | translate }}
        </a>
        <a mat-list-item routerLink="/academics/high" [class.active]="activeRoute === '/academics/high'">
          {{ 'ACADEMICS.HIGH' | translate }}
        </a>
      </mat-expansion-panel>

      <!-- Admissions -->
      <mat-expansion-panel>
        <mat-expansion-panel-header>
          <mat-panel-title>{{ 'NAV.ADMISSIONS' | translate }}</mat-panel-title>
        </mat-expansion-panel-header>
        <a mat-list-item routerLink="/admissions" [class.active]="activeRoute === '/admissions'">
          {{ 'ADMISSIONS.TITLE' | translate }}
        </a>
        <a mat-list-item routerLink="/tuition" [class.active]="activeRoute === '/tuition'">
          {{ 'ADMISSIONS.TUITION' | translate }}
        </a>
      </mat-expansion-panel>

      <!-- Campus Life -->
      <mat-expansion-panel>
        <mat-expansion-panel-header>
          <mat-panel-title>{{ 'NAV.CAMPUS_LIFE' | translate }}</mat-panel-title>
        </mat-expansion-panel-header>
        <a mat-list-item routerLink="/campus" [class.active]="activeRoute === '/campus'">
          {{ 'CAMPUS_LIFE.TITLE' | translate }}
        </a>
        <a mat-list-item routerLink="/campus/clubs" [class.active]="activeRoute === '/campus/clubs'">
          {{ 'CAMPUS_LIFE.CLUBS' | translate }}
        </a>
        <a mat-list-item routerLink="/campus/sports" [class.active]="activeRoute === '/campus/sports'">
          {{ 'CAMPUS_LIFE.SPORTS' | translate }}
        </a>
        <a mat-list-item routerLink="/campus/arts" [class.active]="activeRoute === '/campus/arts'">
          {{ 'CAMPUS_LIFE.ARTS' | translate }}
        </a>
      </mat-expansion-panel>

      <!-- News & Events -->
      <mat-expansion-panel>
        <mat-expansion-panel-header>
          <mat-panel-title>{{ 'NAV.NEWS_EVENTS' | translate }}</mat-panel-title>
        </mat-expansion-panel-header>
        <a mat-list-item routerLink="/news" [class.active]="activeRoute === '/news'">
          {{ 'NEWS.TITLE' | translate }}
        </a>
        <a mat-list-item routerLink="/events" [class.active]="activeRoute === '/events'">
          {{ 'EVENTS.TITLE' | translate }}
        </a>
      </mat-expansion-panel>

      <!-- Portals -->
      <mat-expansion-panel>
        <mat-expansion-panel-header>
          <mat-panel-title>{{ 'NAV.PORTALS' | translate }}</mat-panel-title>
        </mat-expansion-panel-header>
        <a mat-list-item routerLink="/student-portal" [class.active]="activeRoute === '/student-portal'">
          {{ 'QUICK_LINKS.STUDENT_PORTAL' | translate }}
        </a>
        <a mat-list-item routerLink="/parent-portal" [class.active]="activeRoute === '/parent-portal'">
          {{ 'QUICK_LINKS.PARENT_PORTAL' | translate }}
        </a>
        <a mat-list-item routerLink="/faculty-portal" [class.active]="activeRoute === '/faculty-portal'">
          {{ 'QUICK_LINKS.FACULTY_PORTAL' | translate }}
        </a>
      </mat-expansion-panel>

      <!-- Contact Us -->
      <a mat-list-item routerLink="/contact" [class.active]="activeRoute === '/contact'">
        {{ 'CONTACT.TITLE' | translate }}
      </a>

      <!-- Admin Panel (only visible for admins) -->
      <a *ngIf="isAdmin" mat-list-item routerLink="/admin" [class.active]="activeRoute.startsWith('/admin')">
        <mat-icon>admin_panel_settings</mat-icon>
        {{ 'NAV.ADMIN' | translate }}
      </a>

      <!-- Login/Logout -->
      <a *ngIf="!isLoggedIn" mat-list-item routerLink="/login" [class.active]="activeRoute === '/login'">
        <mat-icon>login</mat-icon>
        {{ 'NAV.LOGIN' | translate }}
      </a>
      <a *ngIf="isLoggedIn" mat-list-item (click)="authService.logout()">
        <mat-icon>logout</mat-icon>
        {{ 'NAV.LOGOUT' | translate }}
      </a>

      <!-- Language Selector for Mobile -->
      <div class="mobile-language-selector">
        <app-language-selector></app-language-selector>
      </div>
    </mat-nav-list>
  </mat-sidenav>

  <mat-sidenav-content>
    <!-- Top Toolbar -->
    <mat-toolbar #navbar class="toolbar" [class.fixed-nav]="isFixed" [class.mobile-toolbar]="isMobile">
      <!-- Mobile Menu Toggle (only visible on mobile) -->
      <button
        *ngIf="isMobile"
        type="button"
        aria-label="Toggle sidenav"
        mat-icon-button
        (click)="toggleMobileMenu()"
        class="menu-toggle">
        <mat-icon aria-label="Side nav toggle icon">menu</mat-icon>
      </button>

      <!-- School Brand -->
      <div class="school-brand">
        <a routerLink="/" class="home-link" title="{{ 'NAV.HOME' | translate }}">
          <!-- Show logo only when fixed in desktop view or always in mobile view -->
          <img *ngIf="isFixed || isMobile" src="assets/images/school-logo.png" alt="School Logo" class="school-logo">
          <!-- Show home icon when not fixed in desktop view -->
          <mat-icon *ngIf="!isFixed && !isMobile">home</mat-icon>
          <span class="school-name" *ngIf="isMobile && !isVerySmallScreen">{{ 'SITE.TITLE' | translate }}</span>
        </a>
      </div>

      <!-- Desktop Mega Menu Navigation -->
      <ng-container *ngIf="!isMobile">
        <nav class="mega-menu">
          <!-- About Us -->
          <app-about-menu [activeRoute]="activeRoute"></app-about-menu>

          <!-- Academics -->
          <app-academics-menu [activeRoute]="activeRoute"></app-academics-menu>

          <!-- Admissions -->
          <app-admissions-menu [activeRoute]="activeRoute"></app-admissions-menu>

          <!-- Campus Life -->
          <app-campus-menu [activeRoute]="activeRoute"></app-campus-menu>

          <!-- News & Events -->
          <app-news-events-menu [activeRoute]="activeRoute"></app-news-events-menu>

          <!-- Portals -->
          <app-portals-menu [activeRoute]="activeRoute"></app-portals-menu>

          <!-- Contact Us Direct Link -->
          <div class="menu-item">
            <a mat-button routerLink="/contact" class="menu-link" [class.active]="activeRoute.startsWith('/contact')">
              {{ 'CONTACT.TITLE' | translate }}
            </a>
          </div>
        </nav>

        <div class="nav-actions">
          <!-- Language Selector -->
          <app-language-selector class="nav-action-item"></app-language-selector>

          <!-- Quick Links -->
          <app-quick-links class="nav-action-item"></app-quick-links>

          <!-- Admin Button (only visible for admins) -->
          <div *ngIf="isAdmin" class="login-button nav-action-item">
            <a mat-raised-button color="warn" routerLink="/admin" class="login-link" [class.active]="activeRoute.startsWith('/admin')">
              <mat-icon>admin_panel_settings</mat-icon>
              {{ 'NAV.ADMIN' | translate }}
            </a>
          </div>

          <!-- Login/Logout Button -->
          <div class="login-button nav-action-item">
            <a *ngIf="!isLoggedIn" mat-raised-button color="accent" routerLink="/login" class="login-link" [class.active]="activeRoute.startsWith('/login')">
              <mat-icon>login</mat-icon>
              {{ 'NAV.LOGIN' | translate }}
            </a>
            <a *ngIf="isLoggedIn" mat-raised-button color="accent" (click)="authService.logout()" class="login-link">
              <mat-icon>logout</mat-icon>
              {{ 'NAV.LOGOUT' | translate }}
            </a>
          </div>
        </div>
      </ng-container>

      <!-- Mobile Right Actions -->
      <div class="mobile-actions" *ngIf="isMobile">
        <a mat-icon-button routerLink="/login" class="mobile-login-button">
          <mat-icon>login</mat-icon>
        </a>
        <app-language-selector *ngIf="!isVerySmallScreen"></app-language-selector>
      </div>
    </mat-toolbar>

    <!-- No content projection needed anymore -->
  </mat-sidenav-content>
</mat-sidenav-container>
