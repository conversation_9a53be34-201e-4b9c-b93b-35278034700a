using School.Domain.Common;

namespace School.Domain.Entities;

public class Alumni : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this alumni belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public int GraduationYear { get; set; }
    public string Profession { get; set; } = string.Empty;
    public string Organization { get; set; } = string.Empty;
    public string Designation { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string Achievements { get; set; } = string.Empty;
    public string LinkedInProfile { get; set; } = string.Empty;
    public string FacebookProfile { get; set; } = string.Empty;
    public string TwitterProfile { get; set; } = string.Empty;
    public bool IsFeatured { get; set; }
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }

    // Navigation properties
    public Guid? ProfileImageId { get; set; }
    public MediaItem? ProfileImage { get; set; }
    public ICollection<AlumniTranslation> Translations { get; set; } = new List<AlumniTranslation>();
    public ICollection<AlumniTestimonial> Testimonials { get; set; } = new List<AlumniTestimonial>();
    public DateTime? UpdatedAt { get; set; }
}
