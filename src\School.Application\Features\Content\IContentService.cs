using School.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace School.Application.Features.Content
{
    public interface IContentService
    {
        Task<(IEnumerable<ContentDto> Contents, int TotalCount)> GetAllContentAsync(ContentFilterDto filter);
        Task<ContentDto?> GetContentByIdAsync(Guid id);
        Task<ContentDto?> GetContentBySlugAsync(string slug);
        Task<Guid> CreateContentAsync(ContentCreateDto contentDto);
        Task<bool> UpdateContentAsync(Guid id, ContentUpdateDto contentDto);
        Task<bool> DeleteContentAsync(Guid id);
    }
}