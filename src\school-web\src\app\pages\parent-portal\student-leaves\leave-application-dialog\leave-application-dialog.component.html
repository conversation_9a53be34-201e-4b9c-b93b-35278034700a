<h2 mat-dialog-title>Apply for Leave</h2>

<div *ngIf="loading" class="loading-container">
  <mat-spinner diameter="40"></mat-spinner>
</div>

<form [formGroup]="leaveForm" (ngSubmit)="onSubmit()">
  <mat-dialog-content>
    <div class="form-fields">
      <mat-form-field appearance="outline">
        <mat-label>Leave Type</mat-label>
        <mat-select formControlName="type">
          <mat-option *ngFor="let type of leaveTypes" [value]="type.value">
            {{ type.label }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="leaveForm.get('type')?.hasError('required')">
          Leave type is required
        </mat-error>
      </mat-form-field>

      <div class="date-fields">
        <mat-form-field appearance="outline">
          <mat-label>Start Date</mat-label>
          <input matInput [matDatepicker]="startPicker" formControlName="startDate">
          <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
          <mat-datepicker #startPicker></mat-datepicker>
          <mat-error *ngIf="leaveForm.get('startDate')?.hasError('required')">
            Start date is required
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>End Date</mat-label>
          <input matInput [matDatepicker]="endPicker" formControlName="endDate">
          <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
          <mat-datepicker #endPicker></mat-datepicker>
          <mat-error *ngIf="leaveForm.get('endDate')?.hasError('required')">
            End date is required
          </mat-error>
        </mat-form-field>
      </div>

      <mat-form-field appearance="outline">
        <mat-label>Reason</mat-label>
        <textarea matInput formControlName="reason" rows="4"></textarea>
        <mat-error *ngIf="leaveForm.get('reason')?.hasError('required')">
          Reason is required
        </mat-error>
        <mat-error *ngIf="leaveForm.get('reason')?.hasError('minlength')">
          Reason must be at least 10 characters
        </mat-error>
        <mat-error *ngIf="leaveForm.get('reason')?.hasError('maxlength')">
          Reason cannot exceed 500 characters
        </mat-error>
      </mat-form-field>

      <div class="attachment-field">
        <label>Attachment (Optional)</label>
        <div class="file-input-container">
          <button type="button" mat-stroked-button (click)="fileInput.click()">
            <mat-icon>attach_file</mat-icon> Choose File
          </button>
          <span class="file-name">{{ leaveForm.get('attachment')?.value || 'No file chosen' }}</span>
          <input hidden type="file" #fileInput (change)="onFileSelected($event)">
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" [disabled]="loading" (click)="onCancel()">Cancel</button>
    <button mat-raised-button color="primary" type="submit" [disabled]="leaveForm.invalid || loading">
      <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
      <span *ngIf="!loading">Submit Application</span>
    </button>
  </mat-dialog-actions>
</form>
