using Carter;
using Microsoft.AspNetCore.Mvc;
using School.Application.Features.TenantSetup;
using School.Application.Features.TenantSetup.DTOs;

namespace School.API.Features.Tenant;

/// <summary>
/// Tenant management API endpoints
/// </summary>
public class TenantEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/tenants")
            .WithTags("Tenants");

        // Public tenant registration (no auth required)
        group.MapPost("/register", RegisterTenant)
            .WithName("RegisterTenant")
            .WithSummary("Register a new tenant")
            .Produces<TenantRegistrationResponseDto>(201)
            .Produces(400)
            .AllowAnonymous();

        group.MapGet("/check-availability/{subdomain}", CheckSubdomainAvailability)
            .WithName("CheckSubdomainAvailability")
            .WithSummary("Check if subdomain is available")
            .Produces<bool>()
            .AllowAnonymous();

        // Tenant management (requires admin auth)
        var adminGroup = app.MapGroup("/api/admin/tenants")
            .WithTags("Tenant Administration")
            .RequireAuthorization("SuperAdminPolicy");

        adminGroup.MapGet("/", GetAllTenants)
            .WithName("GetAllTenants")
            .WithSummary("Get all tenants")
            .Produces<(IEnumerable<TenantDto> Tenants, int TotalCount)>();

        adminGroup.MapGet("/{id:guid}", GetTenantById)
            .WithName("GetTenantById")
            .WithSummary("Get tenant by ID")
            .Produces<TenantDto>()
            .Produces(404);

        adminGroup.MapPut("/{id:guid}", UpdateTenant)
            .WithName("UpdateTenant")
            .WithSummary("Update tenant")
            .Produces(204)
            .Produces(400)
            .Produces(404);

        adminGroup.MapDelete("/{id:guid}", DeleteTenant)
            .WithName("DeleteTenant")
            .WithSummary("Delete tenant")
            .Produces(204)
            .Produces(400)
            .Produces(404);

        adminGroup.MapPatch("/{id:guid}/activate", ActivateTenant)
            .WithName("ActivateTenant")
            .WithSummary("Activate tenant")
            .Produces(204)
            .Produces(404);

        adminGroup.MapPatch("/{id:guid}/deactivate", DeactivateTenant)
            .WithName("DeactivateTenant")
            .WithSummary("Deactivate tenant")
            .Produces(204)
            .Produces(404);

        adminGroup.MapPatch("/{id:guid}/suspend", SuspendTenant)
            .WithName("SuspendTenant")
            .WithSummary("Suspend tenant")
            .Produces(204)
            .Produces(404);

        // Tenant setup endpoints (requires tenant context)
        var setupGroup = app.MapGroup("/api/tenant/setup")
            .WithTags("Tenant Setup")
            .RequireAuthorization("AdminPolicy");

        setupGroup.MapGet("/status", GetSetupStatus)
            .WithName("GetSetupStatus")
            .WithSummary("Get tenant setup status")
            .Produces<TenantSetupStatusDto>();

        setupGroup.MapPost("/basic-info", SetupBasicInfo)
            .WithName("SetupBasicInfo")
            .WithSummary("Setup basic tenant information")
            .Produces(200)
            .Produces(400);

        setupGroup.MapPost("/academic-structure", SetupAcademicStructure)
            .WithName("SetupAcademicStructure")
            .WithSummary("Setup academic structure")
            .Produces(200)
            .Produces(400);

        setupGroup.MapPost("/users", SetupUsers)
            .WithName("SetupUsers")
            .WithSummary("Setup initial users")
            .Produces(200)
            .Produces(400);

        setupGroup.MapPost("/complete", CompleteSetup)
            .WithName("CompleteSetup")
            .WithSummary("Complete tenant setup")
            .Produces(200)
            .Produces(400);
    }

    #region Endpoint Implementations

    private static async Task<IResult> RegisterTenant(TenantRegistrationRequestDto request, ITenantSetupService tenantSetupService)
    {
        try
        {
            var response = await tenantSetupService.RegisterTenantAsync(request);
            return Results.Created($"/api/tenants/{response.TenantId}", response);
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error registering tenant: {ex.Message}");
        }
    }

    private static async Task<IResult> CheckSubdomainAvailability(string subdomain, ITenantSetupService tenantSetupService)
    {
        try
        {
            var isAvailable = await tenantSetupService.IsSubdomainAvailableAsync(subdomain);
            return Results.Ok(isAvailable);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error checking subdomain availability: {ex.Message}");
        }
    }

    private static async Task<IResult> GetAllTenants(
        ITenantSetupService tenantSetupService,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        try
        {
            var filter = new TenantFilterDto
            {
                SearchTerm = searchTerm,
                IsActive = isActive,
                Page = page,
                PageSize = pageSize
            };

            var result = await tenantSetupService.GetAllTenantsAsync(filter);
            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving tenants: {ex.Message}");
        }
    }

    private static async Task<IResult> GetTenantById(Guid id, ITenantSetupService tenantSetupService)
    {
        try
        {
            var tenant = await tenantSetupService.GetTenantByIdAsync(id);
            return tenant != null ? Results.Ok(tenant) : Results.NotFound($"Tenant with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving tenant: {ex.Message}");
        }
    }

    private static async Task<IResult> UpdateTenant(Guid id, UpdateTenantDto tenantDto, ITenantSetupService tenantSetupService)
    {
        try
        {
            var success = await tenantSetupService.UpdateTenantAsync(id, tenantDto);
            return success ? Results.NoContent() : Results.NotFound($"Tenant with ID {id} not found");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error updating tenant: {ex.Message}");
        }
    }

    private static async Task<IResult> DeleteTenant(Guid id, ITenantSetupService tenantSetupService)
    {
        try
        {
            var success = await tenantSetupService.DeleteTenantAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Tenant with ID {id} not found");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error deleting tenant: {ex.Message}");
        }
    }

    private static async Task<IResult> ActivateTenant(Guid id, ITenantSetupService tenantSetupService)
    {
        try
        {
            var success = await tenantSetupService.ActivateTenantAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Tenant with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error activating tenant: {ex.Message}");
        }
    }

    private static async Task<IResult> DeactivateTenant(Guid id, ITenantSetupService tenantSetupService)
    {
        try
        {
            var success = await tenantSetupService.DeactivateTenantAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Tenant with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error deactivating tenant: {ex.Message}");
        }
    }

    private static async Task<IResult> SuspendTenant(Guid id, ITenantSetupService tenantSetupService)
    {
        try
        {
            var success = await tenantSetupService.SuspendTenantAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Tenant with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error suspending tenant: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSetupStatus(ITenantSetupService tenantSetupService)
    {
        try
        {
            var status = await tenantSetupService.GetSetupStatusAsync();
            return Results.Ok(status);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving setup status: {ex.Message}");
        }
    }

    private static async Task<IResult> SetupBasicInfo(SetupBasicInfoDto request, ITenantSetupService tenantSetupService)
    {
        try
        {
            await tenantSetupService.SetupBasicInfoAsync(request);
            return Results.Ok(new { message = "Basic information setup completed" });
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error setting up basic info: {ex.Message}");
        }
    }

    private static async Task<IResult> SetupAcademicStructure(SetupAcademicStructureDto request, ITenantSetupService tenantSetupService)
    {
        try
        {
            await tenantSetupService.SetupAcademicStructureAsync(request);
            return Results.Ok(new { message = "Academic structure setup completed" });
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error setting up academic structure: {ex.Message}");
        }
    }

    private static async Task<IResult> SetupUsers(SetupUsersDto request, ITenantSetupService tenantSetupService)
    {
        try
        {
            await tenantSetupService.SetupUsersAsync(request);
            return Results.Ok(new { message = "Users setup completed" });
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error setting up users: {ex.Message}");
        }
    }

    private static async Task<IResult> CompleteSetup(ITenantSetupService tenantSetupService)
    {
        try
        {
            await tenantSetupService.CompleteSetupAsync();
            return Results.Ok(new { message = "Tenant setup completed successfully" });
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error completing setup: {ex.Message}");
        }
    }

    #endregion
}
