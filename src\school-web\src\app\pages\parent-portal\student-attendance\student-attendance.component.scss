.attendance-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.filter-card {
  margin-bottom: 24px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.attendance-loading,
.attendance-error {
  margin-bottom: 24px;
}

.attendance-error {
  text-align: center;
  padding: 16px;

  mat-icon {
    vertical-align: middle;
    margin-right: 8px;
  }
}

.attendance-table-container {
  overflow-x: auto;
  margin-bottom: 24px;
}

.attendance-table {
  width: 100%;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.present {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.absent {
  background-color: #ffebee;
  color: #c62828;
}

.late {
  background-color: #fff8e1;
  color: #ff8f00;
}

.excused {
  background-color: #e3f2fd;
  color: #1565c0;
}

.on-leave {
  background-color: #f3e5f5;
  color: #6a1b9a;
}

.no-data {
  text-align: center;
  padding: 16px;
}

@media (max-width: 768px) {
  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }
}
