import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { EventService } from '../../../core/services/event.service';

@Component({
  selector: 'app-event-registration',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    TranslateModule
  ],
  templateUrl: './event-registration.component.html',
  styleUrls: ['./event-registration.component.scss']
})
export class EventRegistrationComponent implements OnInit {
  event: any;
  registrationForm!: FormGroup;
  isLoading = true;
  isSubmitting = false;
  error: string | null = null;
  registrationSuccess = false;
  currentLanguage: string;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private formBuilder: FormBuilder,
    private eventService: EventService,
    private snackBar: MatSnackBar,
    private translateService: TranslateService
  ) {
    this.currentLanguage = this.translateService.currentLang || 'en';
  }

  ngOnInit(): void {
    this.initForm();

    this.route.paramMap.subscribe(params => {
      const eventId = params.get('id');
      if (eventId) {
        this.loadEvent(+eventId);
      }
    });

    this.translateService.onLangChange.subscribe(event => {
      this.currentLanguage = event.lang;
    });
  }

  initForm(): void {
    this.registrationForm = this.formBuilder.group({
      name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required]],
      studentId: [''],
      additionalInfo: ['']
    });
  }

  loadEvent(id: number): void {
    this.isLoading = true;
    this.error = null;

    this.eventService.getEvent(id).subscribe({
      next: (event) => {
        this.event = event;

        // Check if event is valid for registration
        if (!event.requiresRegistration) {
          this.error = this.translateService.instant('EVENTS.REGISTRATION_NOT_REQUIRED');
          this.isLoading = false;
          return;
        }

        if (event.status !== 0) { // Not scheduled
          this.error = this.translateService.instant('EVENTS.EVENT_NOT_SCHEDULED');
          this.isLoading = false;
          return;
        }

        if (event.maxAttendees && event.registrationsCount >= event.maxAttendees) {
          this.error = this.translateService.instant('EVENTS.EVENT_FULL');
          this.isLoading = false;
          return;
        }

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading event:', error);
        this.error = this.translateService.instant('EVENTS.LOAD_ERROR');
        this.isLoading = false;
      }
    });
  }

  onSubmit(): void {
    if (this.registrationForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.registrationForm.controls).forEach(key => {
        const control = this.registrationForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;

    const registrationData = this.registrationForm.value;

    this.eventService.registerForEvent(this.event.id, registrationData).subscribe({
      next: (response) => {
        this.isSubmitting = false;
        this.registrationSuccess = true;
        this.snackBar.open(
          this.translateService.instant('EVENTS.REGISTRATION_SUCCESS'),
          this.translateService.instant('COMMON.CLOSE'),
          { duration: 5000 }
        );
      },
      error: (error) => {
        console.error('Error registering for event:', error);
        this.isSubmitting = false;

        let errorMessage = this.translateService.instant('EVENTS.REGISTRATION_ERROR');

        if (error.error && error.error.message) {
          if (error.error.message.includes('already registered')) {
            errorMessage = this.translateService.instant('EVENTS.ALREADY_REGISTERED');
          } else if (error.error.message.includes('maximum attendees')) {
            errorMessage = this.translateService.instant('EVENTS.EVENT_FULL');
          }
        }

        this.snackBar.open(
          errorMessage,
          this.translateService.instant('COMMON.CLOSE'),
          { duration: 5000 }
        );
      }
    });
  }

  getTranslation(item: any, field: string): string {
    if (!item) return '';

    // If current language is English, return the original content
    if (this.currentLanguage === 'en') {
      return item[field];
    }

    // Look for a translation in the current language
    const translation = item.translations?.find(
      (t: any) => t.languageCode === this.currentLanguage
    );

    // If translation exists and has the field, return it
    if (translation && translation[field]) {
      return translation[field];
    }

    // Fallback to original content
    return item[field];
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString(this.currentLanguage === 'bn' ? 'bn-BD' : 'en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  goToEvent(): void {
    this.router.navigate(['/events', this.event.id]);
  }

  goToEvents(): void {
    this.router.navigate(['/events']);
  }
}
