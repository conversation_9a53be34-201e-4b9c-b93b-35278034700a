using School.Application.DTOs.Common;
using School.Domain.Enums;

namespace School.Application.DTOs;

public class AcademicCalendarDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsAllDay { get; set; }
    public string Location { get; set; } = string.Empty;
    public AcademicCalendarType Type { get; set; }
    public string Color { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsPublic { get; set; }

    // Foreign key relationships
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }

    // Navigation properties for display
    public string AcademicYearName { get; set; } = string.Empty;
    public string TermName { get; set; } = string.Empty;

    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public List<AcademicCalendarTranslationDto> Translations { get; set; } = new List<AcademicCalendarTranslationDto>();
}

public class AcademicCalendarTranslationDto
{
    public Guid Id { get; set; }
    public Guid AcademicCalendarId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
}

// Enhanced Calendar DTOs for integrated functionality
public class CalendarEventDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsAllDay { get; set; }
    public string Location { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public CalendarEventType EventType { get; set; }
    public string EventTypeName { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsPublic { get; set; }
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }
    public string AcademicYearName { get; set; } = string.Empty;
    public string TermName { get; set; } = string.Empty;
    public bool IsRecurring { get; set; }
    public string Source { get; set; } = string.Empty; // "AcademicCalendar" or "Holiday"
}

public class CalendarStatisticsDto
{
    public int TotalEvents { get; set; }
    public int AcademicEvents { get; set; }
    public int Holidays { get; set; }
    public int ExamEvents { get; set; }
    public int AdmissionEvents { get; set; }
    public int CulturalEvents { get; set; }
    public int SportsEvents { get; set; }
    public int MeetingEvents { get; set; }
    public int OtherEvents { get; set; }
    public int PublicEvents { get; set; }
    public int PrivateEvents { get; set; }
    public int RecurringEvents { get; set; }
    public int OneTimeEvents { get; set; }
    public int CurrentMonthEvents { get; set; }
    public int UpcomingEvents { get; set; }
    public Dictionary<string, int> EventsByMonth { get; set; } = new();
    public Dictionary<string, int> EventsByType { get; set; } = new();
}

public enum CalendarEventType
{
    AcademicEvent = 0,
    Holiday = 1,
    Exam = 2,
    Admission = 3,
    Cultural = 4,
    Sports = 5,
    Meeting = 6,
    Other = 7
}

public class CreateAcademicCalendarDto
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsAllDay { get; set; }
    public string Location { get; set; } = string.Empty;
    public AcademicCalendarType Type { get; set; }
    public string Color { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public bool IsPublic { get; set; } = true;

    // Foreign key relationships
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }

    public List<CreateAcademicCalendarTranslationDto>? Translations { get; set; }
}

public class UpdateAcademicCalendarDto
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsAllDay { get; set; }
    public string Location { get; set; } = string.Empty;
    public AcademicCalendarType Type { get; set; }
    public string Color { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsPublic { get; set; }

    // Foreign key relationships
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }
}

public class CreateAcademicCalendarTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
}

public class UpdateAcademicCalendarTranslationDto
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
}

public class AcademicCalendarEventDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public DateTime Start { get; set; }
    public DateTime? End { get; set; }
    public bool AllDay { get; set; }
    public string Location { get; set; } = string.Empty;
    public AcademicCalendarType Type { get; set; }
    public string Color { get; set; } = string.Empty;
}

/// <summary>
/// Filter DTO for academic calendar queries
/// </summary>
public class AcademicCalendarFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filter by title
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// Filter by academic calendar type
    /// </summary>
    public AcademicCalendarType? Type { get; set; }

    /// <summary>
    /// Filter by academic year ID
    /// </summary>
    public Guid? AcademicYearId { get; set; }

    /// <summary>
    /// Filter by term ID
    /// </summary>
    public Guid? TermId { get; set; }

    /// <summary>
    /// Filter by start date
    /// </summary>
    public string? StartDate { get; set; }

    /// <summary>
    /// Filter by end date
    /// </summary>
    public string? EndDate { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter by public status
    /// </summary>
    public bool? IsPublic { get; set; }
}
