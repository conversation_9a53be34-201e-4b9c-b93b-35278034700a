<div class="academic-year-form-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        {{isEditMode ? 'Edit Academic Year' : 'Create Academic Year'}}
      </h1>
      <p class="page-subtitle">
        {{isEditMode ? 'Update academic year information and terms' : 'Set up a new academic year with terms'}}
      </p>
    </div>
    <div class="header-actions">
      <button mat-button (click)="cancel()">
        <mat-icon>close</mat-icon>
        Cancel
      </button>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Loading academic year...</p>
  </div>

  <!-- Stepper Form -->
  <mat-card *ngIf="!loading" class="form-card">
    <mat-horizontal-stepper #stepper linear>
      <!-- Step 1: Academic Year Information -->
      <mat-step [stepControl]="academicYearForm" label="Academic Year Information">
        <form [formGroup]="academicYearForm" class="step-form">
          <div class="form-section">
            <h3>Basic Information</h3>
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Academic Year Name</mat-label>
                <input matInput formControlName="name" placeholder="e.g., 2024-2025">
                <mat-error *ngIf="academicYearForm.get('name')?.hasError('required')">
                  Name is required
                </mat-error>
                <mat-error *ngIf="academicYearForm.get('name')?.hasError('maxlength')">
                  Name cannot exceed 100 characters
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Display Name</mat-label>
                <input matInput formControlName="displayName" placeholder="e.g., Academic Year 2024-2025">
                <mat-error *ngIf="academicYearForm.get('displayName')?.hasError('required')">
                  Display name is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Code</mat-label>
                <input matInput formControlName="code" placeholder="e.g., AY2024-25">
                <mat-error *ngIf="academicYearForm.get('code')?.hasError('required')">
                  Code is required
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Start Date</mat-label>
                <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                <mat-datepicker #startPicker></mat-datepicker>
                <mat-error *ngIf="academicYearForm.get('startDate')?.hasError('required')">
                  Start date is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>End Date</mat-label>
                <input matInput [matDatepicker]="endPicker" formControlName="endDate">
                <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                <mat-datepicker #endPicker></mat-datepicker>
                <mat-error *ngIf="academicYearForm.get('endDate')?.hasError('required')">
                  End date is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Status</mat-label>
                <mat-select formControlName="status">
                  <mat-option *ngFor="let status of statusOptions" [value]="status.value">
                    {{status.label}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-checkbox formControlName="isCurrentYear">
                Set as current academic year
              </mat-checkbox>
            </div>
          </div>

          <mat-divider></mat-divider>

          <div class="form-section">
            <h3>Academic Configuration</h3>
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Total Working Days</mat-label>
                <input matInput type="number" formControlName="totalWorkingDays" min="0" max="366">
                <mat-hint>Total number of working days in the academic year</mat-hint>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Total Holidays</mat-label>
                <input matInput type="number" formControlName="totalHolidays" min="0" max="366">
                <mat-hint>Total number of holidays in the academic year</mat-hint>
              </mat-form-field>
            </div>
          </div>

          <mat-divider></mat-divider>

          <div class="form-section">
            <h3>Registration & Admission Dates</h3>
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Registration Start Date</mat-label>
                <input matInput [matDatepicker]="regStartPicker" formControlName="registrationStartDate">
                <mat-datepicker-toggle matSuffix [for]="regStartPicker"></mat-datepicker-toggle>
                <mat-datepicker #regStartPicker></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Registration End Date</mat-label>
                <input matInput [matDatepicker]="regEndPicker" formControlName="registrationEndDate">
                <mat-datepicker-toggle matSuffix [for]="regEndPicker"></mat-datepicker-toggle>
                <mat-datepicker #regEndPicker></mat-datepicker>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Admission Start Date</mat-label>
                <input matInput [matDatepicker]="admStartPicker" formControlName="admissionStartDate">
                <mat-datepicker-toggle matSuffix [for]="admStartPicker"></mat-datepicker-toggle>
                <mat-datepicker #admStartPicker></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Admission End Date</mat-label>
                <input matInput [matDatepicker]="admEndPicker" formControlName="admissionEndDate">
                <mat-datepicker-toggle matSuffix [for]="admEndPicker"></mat-datepicker-toggle>
                <mat-datepicker #admEndPicker></mat-datepicker>
              </mat-form-field>
            </div>
          </div>

          <mat-divider></mat-divider>

          <div class="form-section">
            <h3>Additional Information</h3>
            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Description</mat-label>
                <textarea matInput formControlName="description" rows="3" maxlength="500"></textarea>
                <mat-hint align="end">{{academicYearForm.get('description')?.value?.length || 0}}/500</mat-hint>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Remarks</mat-label>
                <textarea matInput formControlName="remarks" rows="3" maxlength="500"></textarea>
                <mat-hint align="end">{{academicYearForm.get('remarks')?.value?.length || 0}}/500</mat-hint>
              </mat-form-field>
            </div>
          </div>

          <div class="step-actions">
            <button mat-raised-button color="primary" matStepperNext [disabled]="academicYearForm.invalid">
              Next: Configure Terms
              <mat-icon>arrow_forward</mat-icon>
            </button>
          </div>
        </form>
      </mat-step>

      <!-- Step 2: Terms Configuration -->
      <mat-step [stepControl]="termsForm" label="Terms Configuration">
        <form [formGroup]="termsForm" class="step-form">
          <div class="form-section">
            <div class="section-header">
              <h3>Academic Terms</h3>
              <button mat-raised-button color="accent" type="button" (click)="addTerm()">
                <mat-icon>add</mat-icon>
                Add Term
              </button>
            </div>

            <div class="terms-container" cdkDropList (cdkDropListDropped)="onTermDrop($event)">
              <div *ngFor="let termControl of termsArray.controls; let i = index" 
                   class="term-card" 
                   cdkDrag>
                <mat-card>
                  <mat-card-header>
                    <mat-card-title>
                      <mat-icon cdkDragHandle class="drag-handle">drag_indicator</mat-icon>
                      Term {{i + 1}}
                    </mat-card-title>
                    <div class="card-actions">
                      <button mat-icon-button 
                              type="button" 
                              (click)="removeTerm(i)"
                              [disabled]="termsArray.length <= 1"
                              matTooltip="Remove Term">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </div>
                  </mat-card-header>

                  <mat-card-content>
                    <div [formGroup]="$any(termControl)" class="term-form">
                      <div class="form-row">
                        <mat-form-field appearance="outline">
                          <mat-label>Term Name</mat-label>
                          <input matInput formControlName="name">
                          <mat-error *ngIf="termControl.get('name')?.hasError('required')">
                            Term name is required
                          </mat-error>
                        </mat-form-field>

                        <mat-form-field appearance="outline">
                          <mat-label>Code</mat-label>
                          <input matInput formControlName="code">
                          <mat-error *ngIf="termControl.get('code')?.hasError('required')">
                            Code is required
                          </mat-error>
                        </mat-form-field>

                        <mat-form-field appearance="outline">
                          <mat-label>Type</mat-label>
                          <mat-select formControlName="type">
                            <mat-option *ngFor="let type of termTypeOptions" [value]="type.value">
                              {{type.label}}
                            </mat-option>
                          </mat-select>
                        </mat-form-field>

                        <mat-form-field appearance="outline">
                          <mat-label>Status</mat-label>
                          <mat-select formControlName="status">
                            <mat-option *ngFor="let status of termStatusOptions" [value]="status.value">
                              {{status.label}}
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>

                      <div class="form-row">
                        <mat-form-field appearance="outline">
                          <mat-label>Start Date</mat-label>
                          <input matInput [matDatepicker]="termStartPicker" formControlName="startDate">
                          <mat-datepicker-toggle matSuffix [for]="termStartPicker"></mat-datepicker-toggle>
                          <mat-datepicker #termStartPicker></mat-datepicker>
                          <mat-error *ngIf="termControl.get('startDate')?.hasError('required')">
                            Start date is required
                          </mat-error>
                        </mat-form-field>

                        <mat-form-field appearance="outline">
                          <mat-label>End Date</mat-label>
                          <input matInput [matDatepicker]="termEndPicker" formControlName="endDate">
                          <mat-datepicker-toggle matSuffix [for]="termEndPicker"></mat-datepicker-toggle>
                          <mat-datepicker #termEndPicker></mat-datepicker>
                          <mat-error *ngIf="termControl.get('endDate')?.hasError('required')">
                            End date is required
                          </mat-error>
                        </mat-form-field>

                        <mat-form-field appearance="outline">
                          <mat-label>Working Days</mat-label>
                          <input matInput type="number" formControlName="totalWorkingDays" min="0" max="200">
                        </mat-form-field>

                        <mat-form-field appearance="outline">
                          <mat-label>Holidays</mat-label>
                          <input matInput type="number" formControlName="totalHolidays" min="0" max="200">
                        </mat-form-field>
                      </div>

                      <div class="form-row">
                        <mat-form-field appearance="outline" class="full-width">
                          <mat-label>Description</mat-label>
                          <textarea matInput formControlName="description" rows="2" maxlength="500"></textarea>
                        </mat-form-field>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>
              </div>
            </div>
          </div>

          <div class="step-actions">
            <button mat-button matStepperPrevious>
              <mat-icon>arrow_back</mat-icon>
              Previous
            </button>
            <button mat-raised-button color="primary" matStepperNext [disabled]="termsForm.invalid">
              Next: Review
              <mat-icon>arrow_forward</mat-icon>
            </button>
          </div>
        </form>
      </mat-step>

      <!-- Step 3: Review and Submit -->
      <mat-step [stepControl]="reviewForm" label="Review & Submit">
        <div class="review-section">
          <h3>Review Academic Year</h3>
          
          <mat-card class="review-card">
            <mat-card-header>
              <mat-card-title>Academic Year Information</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="review-item">
                <strong>Name:</strong> {{academicYearForm.get('displayName')?.value}}
              </div>
              <div class="review-item">
                <strong>Code:</strong> {{academicYearForm.get('code')?.value}}
              </div>
              <div class="review-item">
                <strong>Duration:</strong> 
                {{academicYearForm.get('startDate')?.value | date:'mediumDate'}} - 
                {{academicYearForm.get('endDate')?.value | date:'mediumDate'}}
              </div>
              <div class="review-item">
                <strong>Status:</strong>
                {{getStatusLabel(academicYearForm.get('status')?.value)}}
              </div>
              <div class="review-item" *ngIf="isCurrentYear()">
                <mat-chip color="primary" selected>Current Academic Year</mat-chip>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="review-card">
            <mat-card-header>
              <mat-card-title>Terms ({{getTermsCount()}})</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div *ngFor="let termControl of getTermControls(); let i = index" class="term-review">
                <div class="term-review-header">
                  <strong>{{getTermName(termControl)}}</strong>
                  <mat-chip [color]="getTermStatusColor(termControl)" selected>
                    {{getTermStatusTextForControl(termControl)}}
                  </mat-chip>
                </div>
                <div class="term-review-details">
                  <span>{{getTermTypeTextForControl(termControl)}}</span> •
                  <span>{{getTermDateRange(termControl)}}</span>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <form [formGroup]="reviewForm">
            <mat-checkbox formControlName="confirmed">
              I confirm that the information above is correct and want to 
              {{isEditMode ? 'update' : 'create'}} this academic year.
            </mat-checkbox>
          </form>
        </div>

        <div class="step-actions">
          <button mat-button matStepperPrevious>
            <mat-icon>arrow_back</mat-icon>
            Previous
          </button>
          <button mat-raised-button 
                  color="primary" 
                  (click)="onSubmit()"
                  [disabled]="reviewForm.invalid || saving">
            <mat-spinner *ngIf="saving" diameter="20"></mat-spinner>
            <mat-icon *ngIf="!saving">{{isEditMode ? 'save' : 'add'}}</mat-icon>
            {{isEditMode ? 'Update' : 'Create'}} Academic Year
          </button>
        </div>
      </mat-step>
    </mat-horizontal-stepper>
  </mat-card>
</div>
