using School.Application.Common.Mappings;
using School.Domain.Entities;
using School.Domain.Enums;

namespace School.Application.DTOs;

public class UserDto : IMapFrom<User>
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName => $"{FirstName} {LastName}".Trim();
    public UserRole Role { get; set; }
    public string RoleName => Role.ToString();
    public DateTime? LastLogin { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }

    // MFA Information
    public bool IsMfaEnabled { get; set; }
    public DateTime? LastMfaSetup { get; set; }

    // User Preferences
    public string PreferredLanguage { get; set; } = "en-US";
    public string PreferredTheme { get; set; } = "light";
    public string TimeZone { get; set; } = "Asia/Dhaka";

    // Profile Information
    public string? ProfileImageUrl { get; set; }
    public string? PhoneNumber { get; set; }
    public string? PhoneNumber2 { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public string? Address { get; set; }

    // Security Information (for admin view)
    public int FailedLoginAttempts { get; set; }
    public DateTime? LastFailedLogin { get; set; }
    public DateTime? AccountLockedUntil { get; set; }
    public DateTime? LastPasswordChange { get; set; }
}

public class UserCreateDto
{
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string ConfirmPassword { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public UserRole Role { get; set; } = UserRole.User;

    // Optional profile information
    public string? PhoneNumber { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public string? Address { get; set; }

    // User preferences
    public string PreferredLanguage { get; set; } = "en-US";
    public string PreferredTheme { get; set; } = "light";
    public string TimeZone { get; set; } = "Asia/Dhaka";
}

public class UserUpdateDto
{
    public string? Email { get; set; }
    public string? Password { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public UserRole? Role { get; set; }
    public bool? IsActive { get; set; }
}

public class LoginDto
{
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string? MfaCode { get; set; } // For MFA verification
    public bool RememberMe { get; set; } = false;
}

public class LoginResponseDto
{
    public string Token { get; set; } = string.Empty;
    public string? RefreshToken { get; set; }
    public DateTime Expiration { get; set; }
    public DateTime RefreshTokenExpiration { get; set; }
    public UserDto User { get; set; } = null!;
    public bool RequiresMfa { get; set; } = false;
    public string? MfaToken { get; set; } // Temporary token for MFA verification
}

// MFA Setup DTOs
public class MfaSetupRequestDto
{
    public string Password { get; set; } = string.Empty; // Confirm password for security
}

public class MfaSetupResponseDto
{
    public string Secret { get; set; } = string.Empty;
    public string QrCodeUrl { get; set; } = string.Empty;
    public string[] BackupCodes { get; set; } = Array.Empty<string>();
}

public class MfaVerifyDto
{
    public string Code { get; set; } = string.Empty;
    public string? MfaToken { get; set; } // For login verification
    public string? Secret { get; set; } // For setup verification
}

public class MfaDisableDto
{
    public string Password { get; set; } = string.Empty;
    public string MfaCode { get; set; } = string.Empty;
}

// Refresh Token DTOs
public class RefreshTokenDto
{
    public string RefreshToken { get; set; } = string.Empty;
}

public class RefreshTokenResponseDto
{
    public string Token { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public DateTime Expiration { get; set; }
    public DateTime RefreshTokenExpiration { get; set; }
}

// User Preferences DTOs
public class UserPreferencesDto
{
    public string PreferredLanguage { get; set; } = "en-US";
    public string PreferredTheme { get; set; } = "light";
    public string TimeZone { get; set; } = "Asia/Dhaka";
}

public class UpdateUserPreferencesDto
{
    public string? PreferredLanguage { get; set; }
    public string? PreferredTheme { get; set; }
    public string? TimeZone { get; set; }
}

// Password Management DTOs
public class ChangePasswordDto
{
    public string CurrentPassword { get; set; } = string.Empty;
    public string NewPassword { get; set; } = string.Empty;
    public string ConfirmPassword { get; set; } = string.Empty;
}

public class ForgotPasswordDto
{
    public string Email { get; set; } = string.Empty;
}

public class ResetPasswordDto
{
    public string Email { get; set; } = string.Empty;
    public string Token { get; set; } = string.Empty;
    public string NewPassword { get; set; } = string.Empty;
    public string ConfirmPassword { get; set; } = string.Empty;
}
