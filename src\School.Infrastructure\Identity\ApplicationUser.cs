using Microsoft.AspNetCore.Identity;
using School.Domain.Enums;
using System;
using System.Collections.Generic;

namespace School.Infrastructure.Identity
{
    public class ApplicationUser : IdentityUser
    {
        // Custom properties from our User entity
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public UserRole Role { get; set; } = UserRole.User;
        public DateTime? LastLogin { get; set; }
        public bool IsActive { get; set; } = true;

        // Multi-Factor Authentication
        public bool IsMfaEnabled { get; set; } = false;
        public string? MfaSecret { get; set; }
        public string? MfaBackupCodes { get; set; } // JSON array of backup codes
        public DateTime? LastMfaSetup { get; set; }

        // User Preferences
        public string PreferredLanguage { get; set; } = "en-US"; // en-US or bn-BD
        public string PreferredTheme { get; set; } = "light"; // light or dark
        public string TimeZone { get; set; } = "Asia/Dhaka";

        // Security Settings
        public DateTime? LastPasswordChange { get; set; }
        public int FailedLoginAttempts { get; set; } = 0;
        public DateTime? LastFailedLogin { get; set; }
        public DateTime? AccountLockedUntil { get; set; }

        // Profile Information
        public string? ProfileImageUrl { get; set; }
        public string? PhoneNumber2 { get; set; } // Alternative phone number
        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }

        // Audit properties from BaseEntity
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public string? CreatedBy { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public string? LastModifiedBy { get; set; }
        public bool IsDeleted { get; set; } = false;

        // Navigation properties
        public virtual ICollection<UserRefreshToken> RefreshTokens { get; set; } = new List<UserRefreshToken>();
        public virtual ICollection<UserLoginHistory> LoginHistory { get; set; } = new List<UserLoginHistory>();
    }

    // Refresh Token Entity
    public class UserRefreshToken
    {
        public int Id { get; set; }
        public string UserId { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public DateTime ExpiryDate { get; set; }
        public bool IsRevoked { get; set; } = false;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public string? CreatedByIp { get; set; }
        public DateTime? RevokedAt { get; set; }
        public string? RevokedByIp { get; set; }

        public virtual ApplicationUser User { get; set; } = null!;
    }

    // Login History Entity
    public class UserLoginHistory
    {
        public int Id { get; set; }
        public string UserId { get; set; } = string.Empty;
        public DateTime LoginTime { get; set; } = DateTime.UtcNow;
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public bool IsSuccessful { get; set; }
        public string? FailureReason { get; set; }
        public bool IsMfaUsed { get; set; } = false;

        public virtual ApplicationUser User { get; set; } = null!;
    }
}
