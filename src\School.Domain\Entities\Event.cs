using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class Event : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this event belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public TimeSpan? StartTime { get; set; }
    public TimeSpan? EndTime { get; set; }
    public string Location { get; set; } = string.Empty;
    public string Organizer { get; set; } = string.Empty;
    public string ContactInfo { get; set; } = string.Empty;
    public EventType Type { get; set; }
    public EventStatus Status { get; set; } = EventStatus.Scheduled;
    public bool IsAllDay { get; set; }
    public bool IsRecurring { get; set; }
    public RecurrenceType? RecurrenceType { get; set; }
    public int? RecurrenceInterval { get; set; }
    public DateTime? RecurrenceEndDate { get; set; }
    public bool IsPublic { get; set; } = true;
    public bool IsActive { get; set; } = true;
    public int? MaxAttendees { get; set; }
    public bool RequiresRegistration { get; set; }

    // Navigation properties
    public ICollection<EventTranslation> Translations { get; set; } = new List<EventTranslation>();
    public ICollection<EventImage> Images { get; set; } = new List<EventImage>();
    public ICollection<EventRegistration> Registrations { get; set; } = new List<EventRegistration>();
    public DateTime UpdatedAt { get; set; }
}
