.mfa-settings-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 1rem;
}

// Status Card
.mfa-status-card {
  margin-bottom: 2rem;
  border-radius: 16px;
  overflow: hidden;

  .status-header {
    display: flex;
    align-items: center;
    gap: 1rem;

    .security-icon {
      font-size: 2.5rem;
      height: 2.5rem;
      width: 2.5rem;
      color: var(--md-sys-color-outline);
      
      &.enabled {
        color: var(--md-sys-color-primary);
      }
    }

    .status-content {
      flex: 1;

      .status-title {
        margin: 0 0 0.5rem 0;
        font-size: 1.5rem;
        font-weight: 500;
        color: var(--md-sys-color-on-surface);
      }

      .status-description {
        margin: 0;
        color: var(--md-sys-color-on-surface-variant);
        line-height: 1.4;
      }
    }
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border-radius: 12px;
    background: var(--md-sys-color-surface-variant);
    margin-bottom: 1.5rem;

    &.enabled {
      background: var(--md-sys-color-primary-container);
      color: var(--md-sys-color-on-primary-container);

      mat-icon {
        color: var(--md-sys-color-primary);
      }
    }

    mat-icon {
      color: var(--md-sys-color-outline);
    }

    .status-text {
      font-weight: 500;
    }
  }

  .mfa-info {
    .benefits-list {
      margin: 1rem 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: var(--md-sys-color-on-surface-variant);
      }
    }
  }

  .action-button {
    height: 48px;
    border-radius: 24px;
    font-weight: 500;

    mat-icon {
      margin-right: 0.5rem;
    }
  }
}

// Setup Card
.setup-card, .disable-card {
  border-radius: 16px;
  overflow: hidden;

  .setup-header, .disable-header {
    display: flex;
    align-items: center;
    gap: 1rem;

    .setup-icon, .disable-icon {
      font-size: 2rem;
      height: 2rem;
      width: 2rem;
      color: var(--md-sys-color-primary);
    }

    .disable-icon {
      color: var(--md-sys-color-error);
    }

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 500;
    }
  }

  .step-content {
    padding: 1rem 0;

    .full-width {
      width: 100%;
      margin-bottom: 1rem;
    }

    .step-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      margin-top: 1.5rem;

      button {
        height: 40px;
        border-radius: 20px;

        mat-icon {
          margin-right: 0.25rem;
          
          &.spinning {
            animation: spin 1s linear infinite;
          }
        }
      }
    }
  }
}

// QR Code Section
.qr-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  margin: 1.5rem 0;

  .qr-code {
    padding: 1rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .qr-image {
      display: block;
      max-width: 200px;
      height: auto;
    }
  }

  .manual-entry {
    width: 100%;
    text-align: center;

    p {
      margin-bottom: 1rem;
      color: var(--md-sys-color-on-surface-variant);
    }

    .secret-field {
      width: 100%;
      max-width: 400px;
    }
  }
}

// Success Message
.success-message {
  text-align: center;
  margin-bottom: 2rem;

  .success-icon {
    font-size: 3rem;
    height: 3rem;
    width: 3rem;
    color: var(--md-sys-color-primary);
    margin-bottom: 1rem;
  }

  h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--md-sys-color-on-surface);
  }

  p {
    margin: 0;
    color: var(--md-sys-color-on-surface-variant);
  }
}

// Backup Codes Section
.backup-codes-section {
  h5 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--md-sys-color-on-surface);
  }

  .backup-codes-grid {
    margin-bottom: 1.5rem;

    mat-chip-set {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 0.5rem;
    }

    .backup-code {
      font-family: 'Courier New', monospace;
      font-weight: 600;
      background: var(--md-sys-color-surface-variant);
      color: var(--md-sys-color-on-surface-variant);
      border: 1px solid var(--md-sys-color-outline-variant);
    }
  }

  .backup-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 1.5rem;

    button {
      height: 40px;
      border-radius: 20px;

      mat-icon {
        margin-right: 0.25rem;
      }
    }
  }

  .warning-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--md-sys-color-error-container);
    color: var(--md-sys-color-on-error-container);
    border-radius: 12px;
    font-size: 0.875rem;

    mat-icon {
      color: var(--md-sys-color-error);
    }
  }
}

// Disable Flow
.disable-card {
  .warning-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--md-sys-color-error-container);
    color: var(--md-sys-color-on-error-container);
    border-radius: 12px;
    margin-bottom: 1.5rem;

    .warning-icon {
      color: var(--md-sys-color-error);
      font-size: 1.5rem;
      height: 1.5rem;
      width: 1.5rem;
    }

    p {
      margin: 0;
      font-weight: 500;
    }
  }

  .disable-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;

    button {
      height: 40px;
      border-radius: 20px;

      mat-icon {
        margin-right: 0.25rem;
        
        &.spinning {
          animation: spin 1s linear infinite;
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .mfa-settings-container {
    padding: 0.5rem;
  }

  .qr-section {
    .qr-code .qr-image {
      max-width: 150px;
    }

    .manual-entry .secret-field {
      max-width: 100%;
    }
  }

  .backup-codes-section {
    .backup-codes-grid mat-chip-set {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    .backup-actions {
      flex-direction: column;
      align-items: stretch;

      button {
        width: 100%;
      }
    }
  }

  .step-actions,
  .disable-actions {
    flex-direction: column;
    align-items: stretch;

    button {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .status-header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .setup-header,
  .disable-header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .backup-codes-section .backup-codes-grid mat-chip-set {
    grid-template-columns: repeat(2, 1fr);
  }
}
