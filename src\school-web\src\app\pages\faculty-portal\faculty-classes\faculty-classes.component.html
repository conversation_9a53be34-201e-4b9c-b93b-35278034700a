<div class="classes-container">
  <h1 class="page-title">My Classes</h1>

  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load class data. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadClasses()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!isLoading && !error && classes.length > 0" class="classes-content">
    <!-- Classes List -->
    <mat-card class="classes-card">
      <mat-card-header>
        <mat-card-title>My Classes</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <table mat-table [dataSource]="classes" class="classes-table">
          <!-- Class Name Column -->
          <ng-container matColumnDef="className">
            <th mat-header-cell *matHeaderCellDef>Class</th>
            <td mat-cell *matCellDef="let classItem">{{ classItem.className }}</td>
          </ng-container>

          <!-- Section Column -->
          <ng-container matColumnDef="section">
            <th mat-header-cell *matHeaderCellDef>Section</th>
            <td mat-cell *matCellDef="let classItem">{{ classItem.section }}</td>
          </ng-container>

          <!-- Subject Column -->
          <ng-container matColumnDef="subject">
            <th mat-header-cell *matHeaderCellDef>Subject</th>
            <td mat-cell *matCellDef="let classItem">{{ classItem.subject }}</td>
          </ng-container>

          <!-- Schedule Column -->
          <ng-container matColumnDef="schedule">
            <th mat-header-cell *matHeaderCellDef>Schedule</th>
            <td mat-cell *matCellDef="let classItem">{{ classItem.schedule }}</td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let classItem">
              <button mat-icon-button color="primary" (click)="viewClassDetails(classItem)" matTooltip="View Students">
                <mat-icon>visibility</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </mat-card-content>
    </mat-card>
  </div>

  <div *ngIf="!isLoading && !error && classes.length === 0" class="no-classes">
    <mat-card>
      <mat-card-content>
        <p>No classes assigned to you for the current academic year.</p>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Class Details View -->
  <div *ngIf="selectedClass" class="class-details">
    <mat-card>
      <mat-card-header>
        <mat-card-title>{{ selectedClass.className }} {{ selectedClass.section }} - {{ selectedClass.subject }}</mat-card-title>
        <mat-card-subtitle>{{ selectedClass.schedule }}</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="isLoadingStudents" class="loading-students">
          <mat-progress-bar mode="indeterminate"></mat-progress-bar>
          <p>Loading students...</p>
        </div>

        <div *ngIf="!isLoadingStudents && students.length > 0" class="students-list">
          <h3>Students ({{ students.length }})</h3>

          <table mat-table [dataSource]="students" class="students-table">
            <!-- ID Column -->
            <ng-container matColumnDef="id">
              <th mat-header-cell *matHeaderCellDef>ID</th>
              <td mat-cell *matCellDef="let student">{{ student.id }}</td>
            </ng-container>

            <!-- Name Column -->
            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef>Name</th>
              <td mat-cell *matCellDef="let student">{{ student.name }}</td>
            </ng-container>

            <!-- Roll Number Column -->
            <ng-container matColumnDef="rollNumber">
              <th mat-header-cell *matHeaderCellDef>Roll Number</th>
              <td mat-cell *matCellDef="let student">{{ student.rollNumber }}</td>
            </ng-container>

            <!-- Attendance Column -->
            <ng-container matColumnDef="attendance">
              <th mat-header-cell *matHeaderCellDef>Attendance</th>
              <td mat-cell *matCellDef="let student">
                <mat-progress-bar mode="determinate" [value]="student.attendance"></mat-progress-bar>
                <span class="attendance-value">{{ student.attendance }}%</span>
              </td>
            </ng-container>

            <!-- Performance Column -->
            <ng-container matColumnDef="performance">
              <th mat-header-cell *matHeaderCellDef>Performance</th>
              <td mat-cell *matCellDef="let student">{{ student.performance }}</td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="['id', 'name', 'rollNumber', 'attendance', 'performance']"></tr>
            <tr mat-row *matRowDef="let row; columns: ['id', 'name', 'rollNumber', 'attendance', 'performance'];"></tr>
          </table>
        </div>

        <div *ngIf="!isLoadingStudents && students.length === 0" class="no-students">
          <p>No students found in this class.</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="backToClasses()">
          <mat-icon>arrow_back</mat-icon> Back to Classes
        </button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
