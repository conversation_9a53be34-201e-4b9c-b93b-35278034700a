namespace School.Domain.Enums;

/// <summary>
/// Status of class teacher assignment
/// </summary>
public enum ClassTeacherStatus
{
    /// <summary>
    /// Assignment is active and current
    /// </summary>
    Active = 1,

    /// <summary>
    /// Assignment is temporarily suspended
    /// </summary>
    Suspended = 2,

    /// <summary>
    /// Assignment has been completed
    /// </summary>
    Completed = 3,

    /// <summary>
    /// Assignment was cancelled
    /// </summary>
    Cancelled = 4,

    /// <summary>
    /// Assignment is on hold
    /// </summary>
    OnHold = 5
}
