using School.Domain.Common;

namespace School.Domain.Entities;

public class AlumniTranslation : BaseEntity
{
    public Guid AlumniId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Profession { get; set; } = string.Empty;
    public string Organization { get; set; } = string.Empty;
    public string Designation { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string Achievements { get; set; } = string.Empty;

    // Navigation properties
    public Alumni Alumni { get; set; } = null!;
    public DateTime UpdatedAt { get; set; }
}
