using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace School.Application.DTOs
{
    public class ClubCreateDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        [Required]
        public string Description { get; set; }
        
        [StringLength(250)]
        public string ShortDescription { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Category { get; set; }
        
        [StringLength(100)]
        public string MeetingSchedule { get; set; }
        
        [StringLength(100)]
        public string Location { get; set; }
        
        public string Requirements { get; set; }
        
        public string JoinProcess { get; set; }
        
        [EmailAddress]
        public string ContactEmail { get; set; }
        
        [Url]
        public string Website { get; set; }
        
        [Url]
        public string Instagram { get; set; }
        
        [Url]
        public string Facebook { get; set; }
        
        public bool IsFeatured { get; set; }
        
        public int DisplayOrder { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public int? ProfileImageId { get; set; }
        
        [Url]
        public string ProfileImageUrl { get; set; }
        
        // Related collections
        public ICollection<ClubTranslationCreateDto> Translations { get; set; }
        public ICollection<ClubAdvisorCreateDto> Advisors { get; set; }
        public ICollection<ClubLeaderCreateDto> Leaders { get; set; }
        public ICollection<ClubActivityCreateDto> Activities { get; set; }
        public ICollection<ClubAchievementCreateDto> Achievements { get; set; }
        public ICollection<ClubEventCreateDto> Events { get; set; }
        public ICollection<ClubGalleryItemCreateDto> GalleryItems { get; set; }
    }
}
