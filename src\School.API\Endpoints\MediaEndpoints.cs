using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Domain.Enums;
using School.Application.Features.Media;
using System;
using System.Threading.Tasks;

namespace School.API.Endpoints;

public class MediaEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/media").WithTags("Media");

        group.MapGet("/", async ([AsParameters] MediaFilterDto filter, [FromServices] IMediaService mediaService) =>
        {
            try
            {
                var (mediaItems, totalCount) = await mediaService.GetAllMediaAsync(filter);
                var response = new { TotalCount = totalCount, Items = mediaItems };
                return ApiResults.ApiOk(response, "Media items retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error retrieving media items: {ex.Message}");
            }
        })
        .WithName("GetAllMedia")
        .WithOpenApi();

        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IMediaService mediaService) =>
        {
            try
            {
                var media = await mediaService.GetMediaByIdAsync(id);
                if (media == null)
                {
                    return ApiResults.ApiNotFound("Media not found");
                }
                return ApiResults.ApiOk(media, "Media item retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error retrieving media item: {ex.Message}");
            }
        })
        .WithName("GetMediaById")
        .WithOpenApi();

        group.MapPost("/upload", async (HttpContext context, [FromServices] IMediaService mediaService) =>
        {
            try
            {
                var form = await context.Request.ReadFormAsync();
                var file = form.Files.FirstOrDefault();

                if (file == null)
                {
                    return ApiResults.ApiBadRequest("No file uploaded");
                }

                // Parse media type
                MediaType mediaType = MediaType.Image;
                if (form.TryGetValue("type", out var typeValue) && Enum.TryParse<MediaType>(typeValue, out var parsedType))
                {
                    mediaType = parsedType;
                }

                // Create the DTO
                var mediaDto = new MediaItemCreateDto
                {
                    OriginalFileName = file.FileName,
                    MimeType = file.ContentType,
                    FileSize = file.Length,
                    Type = mediaType,
                    FileStream = file.OpenReadStream()
                };

                // Add optional fields if provided
                if (form.TryGetValue("altText", out var altText))
                {
                    mediaDto.AltText = altText;
                }

                if (form.TryGetValue("caption", out var caption))
                {
                    mediaDto.Caption = caption;
                }

                if (form.TryGetValue("contentId", out var contentIdValue) && Guid.TryParse(contentIdValue, out var contentId))
                {
                    mediaDto.ContentId = contentId;
                }

                var mediaId = await mediaService.UploadMediaAsync(mediaDto);
                return ApiResults.ApiCreated(new { id = mediaId }, $"/api/media/{mediaId}", "Media item uploaded successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error uploading file: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UploadMedia")
        .WithOpenApi();

        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] MediaItemUpdateDto mediaDto, [FromServices] IMediaService mediaService) =>
        {
            try
            {
                var updated = await mediaService.UpdateMediaAsync(id, mediaDto);
                if (!updated)
                {
                    return ApiResults.ApiNotFound("Media not found");
                }
                return ApiResults.ApiOk(new { id }, "Media item updated successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error updating media item: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateMedia")
        .WithOpenApi();

        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IMediaService mediaService) =>
        {
            try
            {
                var deleted = await mediaService.DeleteMediaAsync(id);
                if (!deleted)
                {
                    return ApiResults.ApiNotFound("Media not found");
                }
                return ApiResults.ApiOk(new { id }, "Media item deleted successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error deleting media item: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("DeleteMedia")
        .WithOpenApi();
    }
}
