import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { map, catchError, of } from 'rxjs';
import { TenantService } from '../services/tenant.service';
import { AuthService } from '../services/auth.service';

/**
 * Guard to ensure tenant is properly set up before allowing access to tenant-specific features
 * Enforces the flow: System Admin creates tenant → Tenant Admin receives credentials → Tenant Admin sets up school
 */
export const tenantSetupGuard: CanActivateFn = (route, state) => {
  const tenantService = inject(TenantService);
  const authService = inject(AuthService);
  const router = inject(Router);

  console.log('TenantSetupGuard - Checking tenant setup for route:', state.url);

  // If user is System Admin, allow access to system-level routes
  if (authService.isSystemAdmin()) {
    console.log('TenantSetupGuard - System Admin detected, allowing access');
    return true;
  }

  // For all other users, ensure tenant is properly set up
  return tenantService.getCurrentTenant().pipe(
    map(tenant => {
      if (!tenant) {
        console.log('TenantSetupGuard - No tenant found, checking user tenant access');

        // Check if user has access to any tenants
        const currentUser = authService.getCurrentUser();
        const userTenants = currentUser?.tenants || [];

        if (userTenants.length === 0) {
          console.log('TenantSetupGuard - User has no tenant access, redirecting to tenant creation');
          router.navigate(['/tenant-setup/create'], {
            queryParams: { returnUrl: state.url }
          });
          return false;
        } else if (userTenants.length === 1) {
          console.log('TenantSetupGuard - User has access to one tenant, setting it automatically');
          // Automatically set the tenant if user has access to only one
          const tenantSlug = userTenants[0].tenantSlug || userTenants[0].tenantId;
          tenantService.setDevelopmentTenant(tenantSlug);
          // Reload the page to reinitialize with the tenant
          window.location.reload();
          return false;
        } else {
          console.log('TenantSetupGuard - User has multiple tenants, redirecting to selection');
          router.navigate(['/tenant-setup/select'], {
            queryParams: { returnUrl: state.url }
          });
          return false;
        }
      }

      if (!tenant.isActive) {
        console.log('TenantSetupGuard - Tenant is inactive, redirecting to tenant setup');
        router.navigate(['/tenant-setup/inactive'], { 
          queryParams: { returnUrl: state.url } 
        });
        return false;
      }

      // Check if tenant setup is complete
      if (!tenant.isSetupComplete) {
        console.log('TenantSetupGuard - Tenant setup incomplete, redirecting to setup wizard');
        
        // Only Tenant Admins can complete setup
        if (!authService.isTenantAdmin()) {
          router.navigate(['/tenant-setup/pending'], { 
            queryParams: { returnUrl: state.url } 
          });
          return false;
        }

        router.navigate(['/tenant-setup/wizard'], { 
          queryParams: { returnUrl: state.url } 
        });
        return false;
      }

      // Check if user has access to this tenant
      const userTenants = authService.getCurrentUser()?.tenants || [];
      const hasAccess = userTenants.some((ut: any) => ut.tenantId === tenant.id && ut.isActive);
      
      if (!hasAccess) {
        console.log('TenantSetupGuard - User does not have access to this tenant');
        router.navigate(['/tenant-setup/access-denied'], { 
          queryParams: { returnUrl: state.url } 
        });
        return false;
      }

      console.log('TenantSetupGuard - Tenant setup complete, allowing access');
      return true;
    }),
    catchError(error => {
      console.error('TenantSetupGuard - Error checking tenant setup:', error);
      router.navigate(['/tenant-setup/error'], { 
        queryParams: { returnUrl: state.url } 
      });
      return of(false);
    })
  );
};
