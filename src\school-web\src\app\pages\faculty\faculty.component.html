<!-- Default Hero Section -->
<app-default-hero
  [translationPrefix]="'FACULTY'"
  [subtitle]="'FACULTY.HERO_DESCRIPTION' | translate"
  [description]="'FACULTY.HERO_EXTENDED_DESCRIPTION' | translate"
  [buttons]="[
    {label: 'FACULTY.MEET_OUR_TEAM' | translate, link: '#featured-faculty', isPrimary: true, icon: 'people'},
    {label: 'FACULTY.JOIN_OUR_TEAM' | translate, link: '/careers', isPrimary: false}
  ]">
</app-default-hero>

<!-- Introduction Section -->
<section class="intro-section">
  <div class="container">
    <div class="intro-content">
      <h2>{{ 'FACULTY.INTRO_TITLE' | translate }}</h2>
      <p>{{ 'FACULTY.INTRO_PARAGRAPH_1' | translate }}</p>
      <p>{{ 'FACULTY.INTRO_PARAGRAPH_2' | translate }}</p>
    </div>
  </div>
</section>

<!-- Featured Faculty Section -->
<section class="featured-faculty-section" *ngIf="featuredFaculty.length > 0">
  <div class="container">
    <h2 class="section-title">{{ 'FACULTY.FEATURED_TITLE' | translate }}</h2>
    <p class="section-description">{{ 'FACULTY.FEATURED_DESCRIPTION' | translate }}</p>

    <div class="featured-faculty-grid">
      <mat-card class="faculty-card featured-card" *ngFor="let faculty of featuredFaculty">
        <div class="faculty-image-container">
          <img mat-card-image [src]="faculty.image" [alt]="faculty.name" class="faculty-image">
        </div>
        <mat-card-header>
          <mat-card-title>{{ faculty.name }}</mat-card-title>
          <mat-card-subtitle>{{ faculty.title }}</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p class="faculty-department">{{ getDepartmentName(faculty.department) }}</p>
          <p class="faculty-bio">{{ faculty.shortBio }}</p>
          <div class="faculty-specializations">
            <mat-chip-set>
              <mat-chip *ngFor="let specialization of faculty.specializations.slice(0, 2)">
                {{ specialization }}
              </mat-chip>
            </mat-chip-set>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <a mat-raised-button color="primary" [routerLink]="['/faculty', faculty.id]">{{ 'FACULTY.VIEW_PROFILE' | translate }}</a>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</section>

<!-- Faculty Directory Section -->
<section class="faculty-directory-section">
  <div class="container">
    <h2 class="section-title">{{ 'FACULTY.DIRECTORY_TITLE' | translate }}</h2>
    <p class="section-description">{{ 'FACULTY.DIRECTORY_DESCRIPTION' | translate }}</p>

    <div class="directory-filters">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>{{ 'FACULTY.SEARCH_PLACEHOLDER' | translate }}</mat-label>
        <input matInput [(ngModel)]="searchTerm" (keyup)="filterFaculty()">
        <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="searchTerm=''; filterFaculty()">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-form-field appearance="outline" class="department-filter">
        <mat-label>{{ 'FACULTY.FILTER_BY_DEPARTMENT' | translate }}</mat-label>
        <mat-select [(ngModel)]="selectedDepartment" (selectionChange)="filterFaculty()">
          <mat-option *ngFor="let department of departments" [value]="department.id">{{ department.name }}</mat-option>
        </mat-select>
      </mat-form-field>

      <button mat-button color="primary" (click)="resetFilters()" class="reset-button">
        <mat-icon>refresh</mat-icon> {{ 'FACULTY.RESET_FILTERS' | translate }}
      </button>
    </div>

    <div class="faculty-grid">
      <mat-card class="faculty-card" *ngFor="let faculty of filteredFaculty">
        <div class="faculty-image-container">
          <img mat-card-image [src]="faculty.image" [alt]="faculty.name" class="faculty-image">
        </div>
        <mat-card-header>
          <mat-card-title>{{ faculty.name }}</mat-card-title>
          <mat-card-subtitle>{{ faculty.title }}</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p class="faculty-department">{{ getDepartmentName(faculty.department) }}</p>
          <p class="faculty-bio-short">{{ faculty.shortBio }}</p>
        </mat-card-content>
        <mat-card-actions>
          <a mat-raised-button color="primary" [routerLink]="['/faculty', faculty.id]">{{ 'FACULTY.VIEW_PROFILE' | translate }}</a>
        </mat-card-actions>
      </mat-card>

      <div class="no-results-message" *ngIf="filteredFaculty.length === 0">
        <p>{{ 'FACULTY.NO_RESULTS' | translate }}</p>
      </div>
    </div>
  </div>
</section>

<!-- Join Our Team Section -->
<section class="join-team-section">
  <div class="container">
    <div class="join-team-content">
      <h2>{{ 'FACULTY.JOIN_TEAM_TITLE' | translate }}</h2>
      <p>{{ 'FACULTY.JOIN_TEAM_DESCRIPTION' | translate }}</p>
      <a mat-raised-button color="primary" class="cta-button" routerLink="/careers">{{ 'FACULTY.VIEW_OPPORTUNITIES' | translate }}</a>
    </div>
  </div>
</section>
