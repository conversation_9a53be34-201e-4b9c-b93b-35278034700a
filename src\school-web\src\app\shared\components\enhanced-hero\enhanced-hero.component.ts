import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

export interface HeroButton {
  label: string;
  link: string;
  isPrimary?: boolean;
  icon?: string;
}

@Component({
  selector: 'app-enhanced-hero',
  templateUrl: './enhanced-hero.component.html',
  styleUrls: ['./enhanced-hero.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    RouterModule,
    TranslateModule
  ]
})
export class EnhancedHeroComponent implements OnInit {
  @Input() title: string = '';
  @Input() subtitle: string = '';
  @Input() description: string = '';
  @Input() backgroundImage: string = '';
  @Input() overlayImage: string = '';
  @Input() buttons: HeroButton[] = [];
  @Input() breadcrumbs: string[] = [];
  @Input() showPattern: boolean = true;
  @Input() alignment: 'left' | 'center' | 'right' = 'left';
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() animationEnabled: boolean = true;

  constructor() { }

  ngOnInit(): void {
    // Initialize component
  }

  get sizeClass(): string {
    return `hero-${this.size}`;
  }

  get alignmentClass(): string {
    return `align-${this.alignment}`;
  }

  get themeClass(): string {
    return `theme-${this.theme}`;
  }

  // Helper methods to determine text length classes
  getTitleClass(text: string | undefined): string {
    if (!text) return '';

    if (text.length > 80) {
      return 'very-long-text';
    } else if (text.length > 40) {
      return 'long-text';
    }
    return '';
  }

  getSubtitleClass(text: string | undefined): string {
    if (!text) return '';

    if (text.length > 100) {
      return 'very-long-text';
    } else if (text.length > 60) {
      return 'long-text';
    }
    return '';
  }

  getDescriptionClass(text: string | undefined): string {
    if (!text) return '';

    if (text.length > 300) {
      return 'very-long-text';
    } else if (text.length > 150) {
      return 'long-text';
    }
    return '';
  }
}
