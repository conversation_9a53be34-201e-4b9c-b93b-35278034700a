using School.Domain.Common;

using System;
using System.Collections.Generic;

namespace School.Domain.Entities
{
    public class ClubAchievement : BaseEntity
    {
        public Guid ClubId { get; set; }
        public string Description { get; set; }
        public int? Year { get; set; }
        public int DisplayOrder { get; set; }
        
        // Navigation properties
        public Club Club { get; set; }
        public ICollection<ClubAchievementTranslation> Translations { get; set; }
    }
}
