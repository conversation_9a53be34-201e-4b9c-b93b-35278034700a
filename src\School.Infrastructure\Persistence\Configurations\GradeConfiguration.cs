using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class GradeConfiguration : IEntityTypeConfiguration<Grade>
{
    public void Configure(EntityTypeBuilder<Grade> builder)
    {
        builder.ToTable("Grades");

        builder.HasKey(g => g.Id);

        // Configure properties
        builder.Property(g => g.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(g => g.Code)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(g => g.Description)
            .HasMaxLength(500);

        builder.Property(g => g.PromotionCriteria)
            .HasMaxLength(1000);

        builder.Property(g => g.Remarks)
            .HasMaxLength(500);

        builder.Property(g => g.CreatedBy)
            .HasMaxLength(450);

        builder.Property(g => g.LastModifiedBy)
            .HasMaxLength(450);

        builder.Property(g => g.MinPassingGrade)
            .HasPrecision(5, 2);

        // Configure relationships
        builder.HasOne(g => g.AcademicYear)
            .WithMany()
            .HasForeignKey(g => g.AcademicYearId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(g => g.Sections)
            .WithOne(s => s.Grade)
            .HasForeignKey(s => s.GradeId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(g => g.Students)
            .WithOne()
            .HasForeignKey("CurrentGradeId")
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasMany(g => g.Translations)
            .WithOne(t => t.Grade)
            .HasForeignKey(t => t.GradeId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(g => new { g.Code, g.AcademicYearId })
            .IsUnique();

        builder.HasIndex(g => new { g.Level, g.AcademicYearId })
            .IsUnique();

        builder.HasIndex(g => g.AcademicYearId);
        builder.HasIndex(g => g.EducationLevel);
        builder.HasIndex(g => g.IsActive);

        // Global query filter for multi-tenancy
        builder.HasQueryFilter(g => EF.Property<string>(g, "TenantId") == null || 
                                   EF.Property<string>(g, "TenantId") == "");
    }
}
