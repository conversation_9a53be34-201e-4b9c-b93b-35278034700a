using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ClubActivityConfiguration : IEntityTypeConfiguration<ClubActivity>
{
    public void Configure(EntityTypeBuilder<ClubActivity> builder)
    {
        builder.HasKey(a => a.Id);
        
        builder.Property(a => a.Description)
            .IsRequired();
            
        builder.Property(a => a.DisplayOrder)
            .HasDefaultValue(0);
            
        // Relationships
        builder.HasMany(a => a.Translations)
            .WithOne(t => t.ClubActivity)
            .HasForeignKey(t => t.ClubActivityId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
