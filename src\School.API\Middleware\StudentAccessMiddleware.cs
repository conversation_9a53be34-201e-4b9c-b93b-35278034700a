using System.Security.Claims;
using School.API.Common;

namespace School.API.Middleware
{
    public class StudentAccessMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<StudentAccessMiddleware> _logger;

        public StudentAccessMiddleware(RequestDelegate next, ILogger<StudentAccessMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Only check student access for the student by user ID endpoint
            if (context.Request.Path.StartsWithSegments("/api/students/by-user-id"))
            {
                // Get the user ID from the route
                var routeUserId = context.Request.RouteValues["userId"]?.ToString();
                if (!string.IsNullOrEmpty(routeUserId))
                {
                    // Check if the user is authenticated
                    if (context.User.Identity?.IsAuthenticated == true)
                    {
                        // Get the user's role
                        var userRole = context.User.FindFirst(ClaimTypes.Role)?.Value ??
                                      context.User.FindFirst("role")?.Value;

                        // If the user is a student, check if they're accessing their own data
                        if (userRole == "Student")
                        {
                            // Get the user ID from the claims
                            var userIdClaim = context.User.FindFirst("userId")?.Value ??
                                             context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                            // If the user ID in the route doesn't match the user ID in the claims,
                            // return a 403 Forbidden response
                            if (!string.IsNullOrEmpty(userIdClaim) && routeUserId != userIdClaim)
                            {
                                _logger.LogWarning("Student with ID {UserId} attempted to access data for student with ID {RouteUserId}",
                                    userIdClaim, routeUserId);

                                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                                await context.Response.WriteAsJsonAsync(
                                    ApiResponse.ErrorResponse(
                                        "You do not have permission to access this resource. Students can only access their own data.",
                                        StatusCodes.Status403Forbidden));
                                return;
                            }
                        }
                    }
                }
            }

            // Call the next middleware in the pipeline
            await _next(context);
        }
    }

    // Extension method to add the middleware to the pipeline
    public static class StudentAccessMiddlewareExtensions
    {
        public static IApplicationBuilder UseStudentAccessMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<StudentAccessMiddleware>();
        }
    }
}
