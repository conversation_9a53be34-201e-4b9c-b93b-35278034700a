using School.Domain.Enums;
using School.Domain.Entities;

namespace School.Application.DTOs;

/// <summary>
/// DTO for ClassTeacher entity
/// </summary>
public class ClassTeacherDto
{
    public Guid Id { get; set; }
    public Guid FacultyId { get; set; }
    public string FacultyName { get; set; } = string.Empty;
    public string FacultyEmail { get; set; } = string.Empty;
    public string FacultyPhone { get; set; } = string.Empty;
    public Guid SectionId { get; set; }
    public string SectionName { get; set; } = string.Empty;
    public string GradeName { get; set; } = string.Empty;
    public Guid AcademicYearId { get; set; }
    public string AcademicYearName { get; set; } = string.Empty;
    public Guid? TermId { get; set; }
    public string? TermName { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsActive { get; set; }
    public bool IsPrimary { get; set; }
    public string Responsibilities { get; set; } = string.Empty;
    public string SpecialDuties { get; set; } = string.Empty;
    public string ContactSchedule { get; set; } = string.Empty;
    public string OfficeHours { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public ClassTeacherStatus Status { get; set; }
    public int StudentCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }

    // Navigation properties
    public FacultyDto? Faculty { get; set; }
    public SectionDto? Section { get; set; }
}

/// <summary>
/// DTO for creating a new ClassTeacher assignment
/// </summary>
public class CreateClassTeacherDto
{
    public Guid FacultyId { get; set; }
    public Guid SectionId { get; set; }
    public Guid AcademicYearId { get; set; }
    public Guid? TermId { get; set; }
    public DateTime StartDate { get; set; } = DateTime.UtcNow;
    public DateTime? EndDate { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsPrimary { get; set; } = true;
    public string Responsibilities { get; set; } = string.Empty;
    public string SpecialDuties { get; set; } = string.Empty;
    public string ContactSchedule { get; set; } = string.Empty;
    public string OfficeHours { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public ClassTeacherStatus Status { get; set; } = ClassTeacherStatus.Active;
}

/// <summary>
/// DTO for updating a ClassTeacher assignment
/// </summary>
public class UpdateClassTeacherDto
{
    public Guid FacultyId { get; set; }
    public Guid SectionId { get; set; }
    public Guid AcademicYearId { get; set; }
    public Guid? TermId { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsActive { get; set; }
    public bool IsPrimary { get; set; }
    public string Responsibilities { get; set; } = string.Empty;
    public string SpecialDuties { get; set; } = string.Empty;
    public string ContactSchedule { get; set; } = string.Empty;
    public string OfficeHours { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public ClassTeacherStatus Status { get; set; }
}

/// <summary>
/// DTO for ClassTeacher filtering and pagination
/// </summary>
public class ClassTeacherFilterDto
{
    public string? SearchTerm { get; set; }
    public Guid? FacultyId { get; set; }
    public Guid? SectionId { get; set; }
    public Guid? GradeId { get; set; }
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsPrimary { get; set; }
    public ClassTeacherStatus? Status { get; set; }
    public DateTime? StartDateFrom { get; set; }
    public DateTime? StartDateTo { get; set; }
    public DateTime? EndDateFrom { get; set; }
    public DateTime? EndDateTo { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string SortBy { get; set; } = "StartDate";
    public bool SortDescending { get; set; } = true;
}

/// <summary>
/// DTO for ClassTeacher assignment wizard
/// </summary>
public class ClassTeacherAssignmentWizardDto
{
    public Guid AcademicYearId { get; set; }
    public List<ClassTeacherAssignmentDto> Assignments { get; set; } = new();
}

/// <summary>
/// DTO for individual ClassTeacher assignment in wizard
/// </summary>
public class ClassTeacherAssignmentDto
{
    public Guid SectionId { get; set; }
    public string SectionName { get; set; } = string.Empty;
    public string GradeName { get; set; } = string.Empty;
    public Guid? FacultyId { get; set; }
    public string? FacultyName { get; set; }
    public bool IsPrimary { get; set; } = true;
    public DateTime StartDate { get; set; } = DateTime.UtcNow;
    public DateTime? EndDate { get; set; }
    public string Responsibilities { get; set; } = string.Empty;
    public string ContactSchedule { get; set; } = string.Empty;
    public string OfficeHours { get; set; } = string.Empty;
}

/// <summary>
/// DTO for bulk ClassTeacher operations
/// </summary>
public class BulkClassTeacherOperationDto
{
    public List<Guid> ClassTeacherIds { get; set; } = new();
    public string Operation { get; set; } = string.Empty; // "activate", "deactivate", "complete", "cancel"
    public DateTime? EffectiveDate { get; set; }
    public string? Reason { get; set; }
}

/// <summary>
/// DTO for ClassTeacher workload analysis
/// </summary>
public class ClassTeacherWorkloadDto
{
    public Guid FacultyId { get; set; }
    public string FacultyName { get; set; } = string.Empty;
    public int TotalSections { get; set; }
    public int TotalStudents { get; set; }
    public int PrimarySections { get; set; }
    public int SecondarySections { get; set; }
    public List<string> GradeNames { get; set; } = new();
    public List<string> SectionNames { get; set; } = new();
    public decimal WorkloadScore { get; set; } // Calculated based on students and sections
    public bool IsOverloaded { get; set; }
    public string Recommendations { get; set; } = string.Empty;
}

/// <summary>
/// DTO for ClassTeacher performance summary
/// </summary>
public class ClassTeacherPerformanceDto
{
    public Guid ClassTeacherId { get; set; }
    public string FacultyName { get; set; } = string.Empty;
    public string SectionName { get; set; } = string.Empty;
    public int StudentCount { get; set; }
    public decimal AverageAttendance { get; set; }
    public decimal AverageGrades { get; set; }
    public int ParentMeetings { get; set; }
    public int StudentCounselingSessions { get; set; }
    public string PerformanceNotes { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
}
