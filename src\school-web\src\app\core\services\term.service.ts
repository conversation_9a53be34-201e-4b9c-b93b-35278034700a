import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ApiResponse } from '../models/api-response.model';
import { PagedResult } from '../models/paged-result.model';
import {
  Term,
  CreateTerm,
  UpdateTerm,
  TermFilter,
  TermStatistics,
  CreateTermTranslation,
  UpdateTermTranslation,
  TermTranslation,
  TermType,
  TermStatus
} from '../models/academic-year.model';

@Injectable({
  providedIn: 'root'
})
export class TermService {
  private readonly apiUrl = `${environment.apiUrl}/terms`;

  constructor(private http: HttpClient) {}

  // Term CRUD operations
  getTerms(filter: TermFilter): Observable<ApiResponse<PagedResult<Term>>> {
    let params = new HttpParams()
      .set('page', filter.page.toString())
      .set('pageSize', filter.pageSize.toString())
      .set('sortDescending', filter.sortDescending.toString());

    if (filter.academicYearId) {
      params = params.set('academicYearId', filter.academicYearId);
    }
    if (filter.name) {
      params = params.set('name', filter.name);
    }
    if (filter.code) {
      params = params.set('code', filter.code);
    }
    if (filter.type !== undefined) {
      params = params.set('type', filter.type.toString());
    }
    if (filter.status !== undefined) {
      params = params.set('status', filter.status.toString());
    }
    if (filter.startDateFrom) {
      params = params.set('startDateFrom', filter.startDateFrom.toISOString());
    }
    if (filter.startDateTo) {
      params = params.set('startDateTo', filter.startDateTo.toISOString());
    }
    if (filter.endDateFrom) {
      params = params.set('endDateFrom', filter.endDateFrom.toISOString());
    }
    if (filter.endDateTo) {
      params = params.set('endDateTo', filter.endDateTo.toISOString());
    }
    if (filter.sortBy) {
      params = params.set('sortBy', filter.sortBy);
    }

    return this.http.get<ApiResponse<PagedResult<Term>>>(this.apiUrl, { params });
  }

  getTerm(id: string): Observable<ApiResponse<Term>> {
    return this.http.get<ApiResponse<Term>>(`${this.apiUrl}/${id}`);
  }

  getTermsByAcademicYear(academicYearId: string): Observable<ApiResponse<Term[]>> {
    return this.http.get<ApiResponse<Term[]>>(`${this.apiUrl}/academic-year/${academicYearId}`);
  }

  getCurrentTerm(): Observable<ApiResponse<Term>> {
    return this.http.get<ApiResponse<Term>>(`${this.apiUrl}/current`);
  }

  getActiveTermByAcademicYear(academicYearId: string): Observable<ApiResponse<Term>> {
    return this.http.get<ApiResponse<Term>>(`${this.apiUrl}/academic-year/${academicYearId}/active`);
  }

  createTerm(term: CreateTerm): Observable<ApiResponse<string>> {
    return this.http.post<ApiResponse<string>>(this.apiUrl, term);
  }

  updateTerm(id: string, term: UpdateTerm): Observable<ApiResponse<void>> {
    return this.http.put<ApiResponse<void>>(`${this.apiUrl}/${id}`, term);
  }

  deleteTerm(id: string): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  // Term status management
  activateTerm(id: string): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/${id}/activate`, {});
  }

  completeTerm(id: string): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/${id}/complete`, {});
  }

  cancelTerm(id: string): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/${id}/cancel`, {});
  }

  // Term management utilities
  reorderTerms(academicYearId: string, termIds: string[]): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/academic-year/${academicYearId}/reorder`, termIds);
  }

  // Statistics
  getTermStatistics(id: string): Observable<ApiResponse<TermStatistics>> {
    return this.http.get<ApiResponse<TermStatistics>>(`${this.apiUrl}/${id}/statistics`);
  }

  // Translation management
  addTranslation(termId: string, translation: CreateTermTranslation): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/${termId}/translations`, translation);
  }

  updateTranslation(termId: string, languageCode: string, translation: UpdateTermTranslation): Observable<ApiResponse<void>> {
    return this.http.put<ApiResponse<void>>(`${this.apiUrl}/${termId}/translations/${languageCode}`, translation);
  }

  deleteTranslation(termId: string, languageCode: string): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${termId}/translations/${languageCode}`);
  }

  getTranslations(termId: string): Observable<ApiResponse<TermTranslation[]>> {
    return this.http.get<ApiResponse<TermTranslation[]>>(`${this.apiUrl}/${termId}/translations`);
  }

  // Utility methods
  getTermTypeText(type: TermType): string {
    const typeMap: { [key in TermType]: string } = {
      [TermType.Semester]: 'Semester',
      [TermType.Trimester]: 'Trimester',
      [TermType.Quarter]: 'Quarter',
      [TermType.Annual]: 'Annual',
      [TermType.Custom]: 'Custom'
    };
    return typeMap[type] || 'Unknown';
  }

  getTermStatusText(status: TermStatus): string {
    const statusMap: { [key in TermStatus]: string } = {
      [TermStatus.Planned]: 'Planned',
      [TermStatus.Active]: 'Active',
      [TermStatus.Completed]: 'Completed',
      [TermStatus.Cancelled]: 'Cancelled'
    };
    return statusMap[status] || 'Unknown';
  }

  getTermStatusColor(status: TermStatus): string {
    const colorMap: { [key in TermStatus]: string } = {
      [TermStatus.Planned]: 'warn',      // Planned - Orange
      [TermStatus.Active]: 'primary',    // Active - Blue
      [TermStatus.Completed]: 'accent',  // Completed - Green
      [TermStatus.Cancelled]: 'warn'     // Cancelled - Red
    };
    return colorMap[status] || '';
  }

  getTermTypeOptions(): { value: TermType; label: string }[] {
    return [
      { value: TermType.Semester, label: 'Semester' },
      { value: TermType.Trimester, label: 'Trimester' },
      { value: TermType.Quarter, label: 'Quarter' },
      { value: TermType.Annual, label: 'Annual' },
      { value: TermType.Custom, label: 'Custom' }
    ];
  }

  getTermStatusOptions(): { value: TermStatus; label: string }[] {
    return [
      { value: TermStatus.Planned, label: 'Planned' },
      { value: TermStatus.Active, label: 'Active' },
      { value: TermStatus.Completed, label: 'Completed' },
      { value: TermStatus.Cancelled, label: 'Cancelled' }
    ];
  }
}
