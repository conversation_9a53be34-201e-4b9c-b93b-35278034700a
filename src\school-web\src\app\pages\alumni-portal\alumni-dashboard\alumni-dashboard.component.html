<div class="dashboard-container" *ngIf="!loading">
  <!-- Welcome Section -->
  <div class="welcome-section">
    <mat-card class="welcome-card">
      <mat-card-content>
        <div class="welcome-content">
          <div class="welcome-text">
            <h1>Welcome back, {{ alumni?.name }}!</h1>
            <p>Class of {{ alumni?.graduationYear }} | {{ alumni?.profession }}</p>
            <p *ngIf="alumni?.currentCompany">Currently at {{ alumni?.currentCompany }}</p>
          </div>
          <div class="welcome-avatar">
            <img *ngIf="alumni?.profileImage?.filePath" [src]="alumni.profileImage.filePath" [alt]="alumni.name">
            <mat-icon *ngIf="!alumni?.profileImage?.filePath">person</mat-icon>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Dashboard Stats -->
  <div class="stats-section">
    <div class="stats-grid">
      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon network">people</mat-icon>
            <div class="stat-info">
              <h3>{{ dashboardStats.networkConnections }}</h3>
              <p>Network Connections</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon events">event</mat-icon>
            <div class="stat-info">
              <h3>{{ dashboardStats.upcomingEvents }}</h3>
              <p>Upcoming Events</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon donations">volunteer_activism</mat-icon>
            <div class="stat-info">
              <h3>${{ dashboardStats.totalDonations }}</h3>
              <p>Total Donations</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon jobs">work</mat-icon>
            <div class="stat-info">
              <h3>{{ dashboardStats.jobPostings }}</h3>
              <p>Job Postings</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="main-content">
    <!-- Quick Actions -->
    <div class="quick-actions-section">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Quick Actions</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="actions-grid">
            <button mat-raised-button 
                    *ngFor="let action of quickActions"
                    [color]="action.color"
                    [routerLink]="action.route"
                    class="action-button">
              <mat-icon>{{ action.icon }}</mat-icon>
              <div class="action-text">
                <span class="action-title">{{ action.title }}</span>
                <span class="action-description">{{ action.description }}</span>
              </div>
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Recent Activities -->
    <div class="activities-section">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Recent Activities</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="activities-list">
            <div *ngFor="let activity of recentActivities" class="activity-item">
              <mat-icon class="activity-icon">{{ activity.icon }}</mat-icon>
              <div class="activity-content">
                <h4>{{ activity.title }}</h4>
                <p>{{ activity.description }}</p>
                <span class="activity-date">{{ activity.date | date:'short' }}</span>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>

<div *ngIf="loading" class="loading-container">
  <mat-spinner></mat-spinner>
</div>
