using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.Common.Models;
using School.Application.DTOs.TenantSetup;
using School.Domain.Entities;
using School.Domain.Enums;
using AcademicYearEntity = School.Domain.Entities.AcademicYear;
using TermEntity = School.Domain.Entities.Term;
using OrganizationEntity = School.Domain.Entities.Organization;

namespace School.Application.Features.TenantSetup;

/// <summary>
/// Service for tenant setup operations
/// </summary>
public class TenantSetupService : ITenantSetupService
{
    private readonly IApplicationDbContext _context;
    private readonly ILogger<TenantSetupService> _logger;
    private readonly ITenantContext _tenantContext;

    public TenantSetupService(
        IApplicationDbContext context,
        ILogger<TenantSetupService> logger,
        ITenantContext tenantContext)
    {
        _context = context;
        _logger = logger;
        _tenantContext = tenantContext;
    }

    public async Task<TenantSetupStatusDto?> GetSetupStatusAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get the organization (tenant)
            var organization = await _context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId, cancellationToken);

            if (organization == null)
            {
                return null;
            }

            // Initialize setup steps if they don't exist
            await InitializeSetupStepsAsync(tenantId, cancellationToken);

            // Get setup steps for this tenant
            var setupSteps = await _context.TenantSetupSteps
                .Where(s => s.TenantId == tenantId)
                .OrderBy(s => s.OrderIndex)
                .ToListAsync(cancellationToken);

            var completedSteps = setupSteps
                .Where(s => s.IsCompleted)
                .Select(s => s.StepId)
                .ToList();

            var totalSteps = setupSteps.Count;
            var completedCount = completedSteps.Count;
            var setupProgress = totalSteps > 0 ? (completedCount * 100) / totalSteps : 0;

            // Find current step (first incomplete required step)
            var currentStepEntity = setupSteps
                .Where(s => !s.IsCompleted && !s.IsSkipped && s.IsRequired)
                .OrderBy(s => s.OrderIndex)
                .FirstOrDefault();

            var currentStep = currentStepEntity?.StepId ?? "complete";
            var isSetupComplete = organization.Status == OrganizationStatus.Active &&
                                setupSteps.All(s => s.IsCompleted || (!s.IsRequired && s.IsSkipped));

            return new TenantSetupStatusDto
            {
                TenantId = tenantId,
                IsSetupComplete = isSetupComplete,
                CompletedSteps = completedSteps,
                CurrentStep = currentStep,
                SetupProgress = setupProgress,
                LastUpdated = setupSteps.Where(s => s.CompletedAt.HasValue)
                                      .OrderByDescending(s => s.CompletedAt)
                                      .FirstOrDefault()?.CompletedAt ?? organization.CreatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting setup status for tenant {TenantId}", tenantId);
            return null;
        }
    }

    public async Task<ApiResult> CompleteSchoolProfileAsync(Guid tenantId, SchoolProfileSetupDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var organization = await _context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId, cancellationToken);

            if (organization == null)
            {
                return ApiResult.FailureResult(new[] { "Organization not found" });
            }

            // Update organization with school profile data
            organization.Name = request.SchoolName;
            organization.Type = Enum.Parse<OrganizationType>(request.SchoolType);
            organization.ContactEmail = request.ContactInfo.Email;
            organization.ContactPhone = request.ContactInfo.Phone;
            organization.Website = request.ContactInfo.Website;
            organization.TimeZone = request.Settings.TimeZone;
            organization.Currency = request.Settings.Currency;
            organization.DefaultLanguage = request.Settings.Language;
            // Note: Organization entity doesn't have UpdatedAt, it inherits from BaseEntity which has UpdatedAt

            // You might want to store additional details in a separate SchoolProfile entity
            // For now, we'll use the organization entity

            await _context.SaveChangesAsync(cancellationToken);

            // Mark the step as complete
            await MarkStepCompleteAsync(tenantId, "school-profile", cancellationToken);

            _logger.LogInformation("School profile setup completed for tenant {TenantId}", tenantId);
            return ApiResult.SuccessResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing school profile setup for tenant {TenantId}", tenantId);
            return ApiResult.FailureResult(new[] { $"Error completing school profile setup: {ex.Message}" });
        }
    }

    public async Task<ApiResult> CompleteAcademicStructureAsync(Guid tenantId, AcademicStructureSetupDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Set tenant context
            _tenantContext.SetCurrentTenantId(tenantId);

            // Create default academic year if it doesn't exist
            var currentYear = DateTime.Now.Year;
            var existingAcademicYear = await _context.AcademicYears
                .FirstOrDefaultAsync(ay => ay.TenantId == tenantId, cancellationToken);

            if (existingAcademicYear == null)
            {
                var academicYear = new AcademicYearEntity
                {
                    Id = Guid.NewGuid(),
                    Name = $"{currentYear}-{currentYear + 1}",
                    DisplayName = $"Academic Year {currentYear}-{currentYear + 1}",
                    Code = $"AY{currentYear}-{currentYear + 1}",
                    StartDate = new DateTime(currentYear, 4, 1), // Default to April 1st
                    EndDate = new DateTime(currentYear + 1, 3, 31),
                    Status = AcademicYearStatus.Active,
                    IsCurrentYear = true,
                    TenantId = tenantId
                };

                _context.AcademicYears.Add(academicYear);

                // Create terms if requested
                if (request.HasTerms)
                {
                    var termDuration = 365 / request.TermCount;
                    for (int i = 1; i <= request.TermCount; i++)
                    {
                        var termStartDate = academicYear.StartDate.AddDays((i - 1) * termDuration);
                        var termEndDate = i == request.TermCount 
                            ? academicYear.EndDate 
                            : termStartDate.AddDays(termDuration - 1);

                        var term = new TermEntity
                        {
                            Id = Guid.NewGuid(),
                            Name = $"Term {i}",
                            Code = $"T{i}",
                            Type = TermType.Semester,
                            Status = TermStatus.Planned,
                            StartDate = termStartDate,
                            EndDate = termEndDate,
                            OrderIndex = i,
                            AcademicYearId = academicYear.Id,
                            TenantId = tenantId
                        };

                        _context.Terms.Add(term);
                    }
                }
            }

            await _context.SaveChangesAsync(cancellationToken);

            // Mark the step as complete
            await MarkStepCompleteAsync(tenantId, "academic-structure", cancellationToken);

            _logger.LogInformation("Academic structure setup completed for tenant {TenantId}", tenantId);
            return ApiResult.SuccessResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing academic structure setup for tenant {TenantId}", tenantId);
            return ApiResult.FailureResult(new[] { $"Error completing academic structure setup: {ex.Message}" });
        }
    }

    public async Task<ApiResult> CompleteUserRolesAsync(Guid tenantId, UserRolesSetupDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Set tenant context
            _tenantContext.SetCurrentTenantId(tenantId);

            // User roles setup - this would typically involve setting up role-based permissions
            // Since there's no Roles DbSet in the current schema, we'll just log this step
            // In a real implementation, you would set up role-based access control here

            // Mark the step as complete
            await MarkStepCompleteAsync(tenantId, "user-roles", cancellationToken);

            _logger.LogInformation("User roles setup completed for tenant {TenantId}", tenantId);
            return ApiResult.SuccessResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing user roles setup for tenant {TenantId}", tenantId);
            return ApiResult.FailureResult(new[] { $"Error completing user roles setup: {ex.Message}" });
        }
    }

    public async Task<ApiResult> CompleteInitialUsersAsync(Guid tenantId, InitialUsersSetupDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!request.CreateInitialUsers)
            {
                _logger.LogInformation("Initial users creation skipped for tenant {TenantId}", tenantId);
                return ApiResult.SuccessResult();
            }

            // Set tenant context
            _tenantContext.SetCurrentTenantId(tenantId);

            // This is a simplified implementation
            // In a real scenario, you would integrate with your user management system
            // and send invitation emails to the specified email addresses

            // Mark the step as complete
            await MarkStepCompleteAsync(tenantId, "initial-users", cancellationToken);

            _logger.LogInformation("Initial users setup completed for tenant {TenantId}", tenantId);
            return ApiResult.SuccessResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing initial users setup for tenant {TenantId}", tenantId);
            return ApiResult.FailureResult(new[] { $"Error completing initial users setup: {ex.Message}" });
        }
    }

    public async Task<ApiResult> CompleteSystemSettingsAsync(Guid tenantId, SystemSettingsSetupDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var organization = await _context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId, cancellationToken);

            if (organization == null)
            {
                return ApiResult.FailureResult(new[] { "Organization not found" });
            }

            // Update organization with system settings
            organization.TimeZone = request.TimeZone;
            organization.Currency = request.Currency;
            organization.DefaultLanguage = request.Language;
            // Note: BaseEntity automatically handles UpdatedAt

            await _context.SaveChangesAsync(cancellationToken);

            // Mark the step as complete
            await MarkStepCompleteAsync(tenantId, "system-settings", cancellationToken);

            _logger.LogInformation("System settings setup completed for tenant {TenantId}", tenantId);
            return ApiResult.SuccessResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing system settings setup for tenant {TenantId}", tenantId);
            return ApiResult.FailureResult(new[] { $"Error completing system settings setup: {ex.Message}" });
        }
    }

    public async Task<ApiResult> MarkStepCompleteAsync(Guid tenantId, string stepId, CancellationToken cancellationToken = default)
    {
        try
        {
            var step = await _context.TenantSetupSteps
                .FirstOrDefaultAsync(s => s.TenantId == tenantId && s.StepId == stepId, cancellationToken);

            if (step == null)
            {
                return ApiResult.FailureResult(new[] { $"Setup step '{stepId}' not found for tenant" });
            }

            step.IsCompleted = true;
            step.CompletedAt = DateTime.UtcNow;
            step.IsSkipped = false; // Reset skip status if previously skipped

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Step {StepId} marked as complete for tenant {TenantId}", stepId, tenantId);
            return ApiResult.SuccessResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking step {StepId} as complete for tenant {TenantId}", stepId, tenantId);
            return ApiResult.FailureResult(new[] { $"Error marking step as complete: {ex.Message}" });
        }
    }

    public async Task<ApiResult> SkipOptionalStepAsync(Guid tenantId, string stepId, CancellationToken cancellationToken = default)
    {
        try
        {
            var step = await _context.TenantSetupSteps
                .FirstOrDefaultAsync(s => s.TenantId == tenantId && s.StepId == stepId, cancellationToken);

            if (step == null)
            {
                return ApiResult.FailureResult(new[] { $"Setup step '{stepId}' not found for tenant" });
            }

            if (step.IsRequired)
            {
                return ApiResult.FailureResult(new[] { $"Cannot skip required step '{stepId}'" });
            }

            step.IsSkipped = true;
            step.SkippedAt = DateTime.UtcNow;
            step.IsCompleted = false; // Reset completion status if previously completed

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Optional step {StepId} skipped for tenant {TenantId}", stepId, tenantId);
            return ApiResult.SuccessResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error skipping step {StepId} for tenant {TenantId}", stepId, tenantId);
            return ApiResult.FailureResult(new[] { $"Error skipping step: {ex.Message}" });
        }
    }

    public async Task<ApiResult> FinalizeSetupAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var organization = await _context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId, cancellationToken);

            if (organization == null)
            {
                return ApiResult.FailureResult(new[] { "Organization not found" });
            }

            // Mark setup as complete - using Status field
            organization.Status = OrganizationStatus.Active;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Tenant setup finalized for tenant {TenantId}", tenantId);
            return ApiResult.SuccessResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finalizing setup for tenant {TenantId}", tenantId);
            return ApiResult.FailureResult(new[] { $"Error finalizing setup: {ex.Message}" });
        }
    }

    public async Task<ApiResult> ResetSetupAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var organization = await _context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId, cancellationToken);

            if (organization == null)
            {
                return ApiResult.FailureResult(new[] { "Organization not found" });
            }

            // Reset setup status - using Status field
            organization.Status = OrganizationStatus.Pending;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Tenant setup reset for tenant {TenantId}", tenantId);
            return ApiResult.SuccessResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting setup for tenant {TenantId}", tenantId);
            return ApiResult.FailureResult(new[] { $"Error resetting setup: {ex.Message}" });
        }
    }

    /// <summary>
    /// Initializes the setup steps for a tenant if they don't exist
    /// </summary>
    private async Task InitializeSetupStepsAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        var existingSteps = await _context.TenantSetupSteps
            .Where(s => s.TenantId == tenantId)
            .ToListAsync(cancellationToken);

        if (existingSteps.Any())
        {
            return; // Steps already initialized
        }

        var defaultSteps = new[]
        {
            new TenantSetupStep
            {
                Id = Guid.NewGuid(),
                TenantId = tenantId,
                StepId = "school-profile",
                StepName = "School Profile",
                Description = "Set up basic school information and contact details",
                OrderIndex = 1,
                IsRequired = true
            },
            new TenantSetupStep
            {
                Id = Guid.NewGuid(),
                TenantId = tenantId,
                StepId = "academic-structure",
                StepName = "Academic Structure",
                Description = "Configure grades, academic years, and terms",
                OrderIndex = 2,
                IsRequired = true
            },
            new TenantSetupStep
            {
                Id = Guid.NewGuid(),
                TenantId = tenantId,
                StepId = "user-roles",
                StepName = "User Roles & Permissions",
                Description = "Set up user roles and permission structure",
                OrderIndex = 3,
                IsRequired = true
            },
            new TenantSetupStep
            {
                Id = Guid.NewGuid(),
                TenantId = tenantId,
                StepId = "initial-users",
                StepName = "Initial Users",
                Description = "Create initial faculty and staff accounts",
                OrderIndex = 4,
                IsRequired = false
            },
            new TenantSetupStep
            {
                Id = Guid.NewGuid(),
                TenantId = tenantId,
                StepId = "system-settings",
                StepName = "System Settings",
                Description = "Configure system preferences and integrations",
                OrderIndex = 5,
                IsRequired = false
            }
        };

        _context.TenantSetupSteps.AddRange(defaultSteps);
        await _context.SaveChangesAsync(cancellationToken);
    }
}
