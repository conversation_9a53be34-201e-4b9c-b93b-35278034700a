using School.Domain.Common;

namespace School.Domain.Entities;

public class FacultyEducation : BaseEntity
{
    public Guid FacultyId { get; set; }
    public Faculty? Faculty { get; set; }
    
    public string Degree { get; set; } = string.Empty;
    public string Institution { get; set; } = string.Empty;
    public int? Year { get; set; }
    public int DisplayOrder { get; set; }
    public string Major { get; set; }
}
