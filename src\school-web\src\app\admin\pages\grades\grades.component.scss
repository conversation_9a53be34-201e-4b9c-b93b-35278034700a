.grades-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  mat-card {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
  }

  mat-toolbar {
    border-radius: 8px 8px 0 0;
    
    .spacer {
      flex: 1 1 auto;
    }
  }
}

.filter-section {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
  flex-wrap: wrap;

  .search-field {
    flex: 1;
    min-width: 250px;
  }

  .filter-field {
    min-width: 200px;
  }

  button {
    height: 56px;
  }
}

.table-container {
  overflow-x: auto;
  margin-bottom: 16px;

  .grades-table {
    width: 100%;
    
    th {
      font-weight: 600;
      color: #333;
      background-color: #f5f5f5;
    }

    td {
      padding: 12px 8px;
    }
  }
}

.grade-code {
  font-weight: 600;
  color: #1976d2;
  background-color: #e3f2fd;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
}

.grade-info {
  .grade-name {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }

  .grade-description {
    display: block;
    color: #666;
    font-size: 0.75rem;
    line-height: 1.2;
  }
}

.grade-level {
  font-weight: 500;
  color: #4caf50;
  background-color: #e8f5e8;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
}

.education-level {
  color: #ff9800;
  font-weight: 500;
}

.max-students {
  font-weight: 500;
  color: #9c27b0;
}

.academic-year {
  color: #607d8b;
  font-weight: 500;
}

.status-text {
  margin-left: 8px;
  font-size: 0.875rem;
  font-weight: 500;

  &.active {
    color: #4caf50;
  }

  &.inactive {
    color: #f44336;
  }
}

.action-buttons {
  display: flex;
  gap: 4px;

  button {
    width: 36px;
    height: 36px;
    
    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;

  mat-spinner {
    margin-bottom: 16px;
  }
}

.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #666;

  .no-data-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #ccc;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    color: #333;
  }

  p {
    margin: 0 0 24px 0;
    max-width: 400px;
    line-height: 1.5;
  }
}

// Responsive design
@media (max-width: 768px) {
  .grades-container {
    padding: 16px;
  }

  .filter-section {
    flex-direction: column;
    align-items: stretch;

    .search-field,
    .filter-field {
      min-width: auto;
    }
  }

  .table-container {
    .grades-table {
      font-size: 0.875rem;

      th, td {
        padding: 8px 4px;
      }
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .grades-container {
    mat-card {
      background-color: #424242;
      color: #fff;
    }
  }

  .table-container {
    .grades-table {
      th {
        background-color: #616161;
        color: #fff;
      }
    }
  }

  .grade-info {
    .grade-name {
      color: #fff;
    }

    .grade-description {
      color: #ccc;
    }
  }

  .no-data-container {
    color: #ccc;

    h3 {
      color: #fff;
    }
  }
}
