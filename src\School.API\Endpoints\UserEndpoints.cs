using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using School.API.Common;
using School.Application.DTOs;
using School.Application.Features.User;
using School.Domain.Entities;

namespace School.API.Endpoints;

public class UserEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/users")
            .WithTags("Users");

        // Get all users (Admin only)
        group.MapGet("/", async ([FromServices] IUserService userService) =>
        {
            var users = await userService.GetAllUsersAsync();
            return ApiResults.ApiOk(users, "Users retrieved successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("GetAllUsers")
        .WithOpenApi();

        // Get user by ID
        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IUserService userService) =>
        {
            var user = await userService.GetUserByIdAsync(id);
            if (user == null)
            {
                return ApiResults.ApiNotFound("User not found");
            }
            return ApiResults.ApiOk(user, "User retrieved successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("GetUserById")
        .WithOpenApi();

        // Update user
        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UserUpdateDto userDto, [FromServices] IUserService userService) =>
        {
            var updatedUser = await userService.UpdateUserAsync(id, userDto);
            if (updatedUser == null)
            {
                return ApiResults.ApiNotFound("User not found");
            }
            return ApiResults.ApiOk(updatedUser, "User updated successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("UpdateUser")
        .WithOpenApi();

        // Delete user
        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IUserService userService) =>
        {
            var success = await userService.DeleteUserAsync(id);
            if (!success)
            {
                return ApiResults.ApiNotFound("User not found");
            }
            return ApiResults.ApiOk("User deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteUser")
        .WithOpenApi();
    }
}
