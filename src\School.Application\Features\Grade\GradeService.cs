using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Domain.Entities;
using School.Domain.Enums;

namespace School.Application.Features.Grade;

/// <summary>
/// Service implementation for Grade management operations
/// </summary>
public class GradeService : IGradeService
{
    private readonly IApplicationDbContext _context;
    private readonly IMapper _mapper;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<GradeService> _logger;

    public GradeService(
        IApplicationDbContext context,
        IMapper mapper,
        ICurrentUserService currentUserService,
        ILogger<GradeService> logger)
    {
        _context = context;
        _mapper = mapper;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<(IEnumerable<GradeDto> Grades, int TotalCount)> GetAllGradesAsync(GradeFilterDto filter)
    {
        // Global query filters automatically handle tenant isolation
        var query = _context.Grades
            .Include(g => g.AcademicYear)
            .Include(g => g.Sections)
            .Include(g => g.Students)
            .Include(g => g.Translations.Where(t => t.LanguageCode == filter.LanguageCode))
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(filter.SearchTerm))
        {
            query = query.Where(g => g.Name.Contains(filter.SearchTerm) || 
                                   g.Code.Contains(filter.SearchTerm) ||
                                   g.Description.Contains(filter.SearchTerm));
        }

        if (filter.AcademicYearId.HasValue)
        {
            query = query.Where(g => g.AcademicYearId == filter.AcademicYearId.Value);
        }

        if (filter.EducationLevel.HasValue)
        {
            query = query.Where(g => g.EducationLevel == filter.EducationLevel.Value);
        }

        if (filter.IsActive.HasValue)
        {
            query = query.Where(g => g.IsActive == filter.IsActive.Value);
        }

        if (filter.MinLevel.HasValue)
        {
            query = query.Where(g => g.Level >= filter.MinLevel.Value);
        }

        if (filter.MaxLevel.HasValue)
        {
            query = query.Where(g => g.Level <= filter.MaxLevel.Value);
        }

        // Apply sorting
        query = filter.SortBy.ToLower() switch
        {
            "name" => filter.SortDescending ? query.OrderByDescending(g => g.Name) : query.OrderBy(g => g.Name),
            "code" => filter.SortDescending ? query.OrderByDescending(g => g.Code) : query.OrderBy(g => g.Code),
            "level" => filter.SortDescending ? query.OrderByDescending(g => g.Level) : query.OrderBy(g => g.Level),
            "displayorder" => filter.SortDescending ? query.OrderByDescending(g => g.DisplayOrder) : query.OrderBy(g => g.DisplayOrder),
            _ => query.OrderBy(g => g.Level)
        };

        var totalCount = await query.CountAsync();

        var grades = await query
            .Skip((filter.Page - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        var gradeDtos = _mapper.Map<IEnumerable<GradeDto>>(grades);

        return (gradeDtos, totalCount);
    }

    public async Task<GradeDto?> GetGradeByIdAsync(Guid id)
    {
        // Global query filters automatically handle tenant isolation
        var grade = await _context.Grades
            .Where(g => g.Id == id)
            .Include(g => g.AcademicYear)
            .Include(g => g.Sections)
            .Include(g => g.Students)
            .Include(g => g.Translations)
            .FirstOrDefaultAsync();

        return grade != null ? _mapper.Map<GradeDto>(grade) : null;
    }

    public async Task<IEnumerable<GradeDto>> GetGradesByAcademicYearAsync(Guid academicYearId)
    {
        // Global query filters automatically handle tenant isolation
        var grades = await _context.Grades
            .Where(g => g.AcademicYearId == academicYearId)
            .Include(g => g.AcademicYear)
            .Include(g => g.Sections)
            .OrderBy(g => g.Level)
            .ThenBy(g => g.DisplayOrder)
            .ToListAsync();

        return _mapper.Map<IEnumerable<GradeDto>>(grades);
    }

    public async Task<IEnumerable<GradeDto>> GetActiveGradesAsync(Guid academicYearId)
    {
        // Global query filters automatically handle tenant isolation
        var grades = await _context.Grades
            .Where(g => g.AcademicYearId == academicYearId && g.IsActive)
            .Include(g => g.AcademicYear)
            .Include(g => g.Sections)
            .OrderBy(g => g.Level)
            .ThenBy(g => g.DisplayOrder)
            .ToListAsync();

        return _mapper.Map<IEnumerable<GradeDto>>(grades);
    }

    public async Task<Guid> CreateGradeAsync(CreateGradeDto gradeDto)
    {
        var userId = _currentUserService.UserId;

        var grade = _mapper.Map<Domain.Entities.Grade>(gradeDto);
        grade.CreatedBy = userId?.ToString() ?? "System";
        grade.CreatedAt = DateTime.UtcNow;

        _context.Grades.Add(grade);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Grade {GradeName} created successfully", grade.Name);

        return grade.Id;
    }

    public async Task<bool> UpdateGradeAsync(Guid id, UpdateGradeDto gradeDto)
    {
        var userId = _currentUserService.UserId;

        // Global query filters automatically handle tenant isolation
        var grade = await _context.Grades
            .Where(g => g.Id == id)
            .FirstOrDefaultAsync();

        if (grade == null)
        {
            return false;
        }

        _mapper.Map(gradeDto, grade);
        grade.LastModifiedBy = userId?.ToString() ?? "System";
        grade.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Grade {GradeId} updated successfully", id);

        return true;
    }

    public async Task<bool> DeleteGradeAsync(Guid id)
    {
        var grade = await _context.Grades
            .Where(g => g.Id == id)
            .Include(g => g.Sections)
            .Include(g => g.Students)
            .FirstOrDefaultAsync();

        if (grade == null)
        {
            return false;
        }

        // Check if grade can be deleted (no students or sections)
        if (grade.Sections.Count > 0 || grade.Students.Count > 0)
        {
            _logger.LogWarning("Cannot delete grade {GradeId} - has associated sections or students", id);
            return false;
        }

        _context.Grades.Remove(grade);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Grade {GradeId} deleted successfully", id);

        return true;
    }

    public async Task<bool> ActivateGradeAsync(Guid id)
    {
        return await UpdateGradeStatusAsync(id, true);
    }

    public async Task<bool> DeactivateGradeAsync(Guid id)
    {
        return await UpdateGradeStatusAsync(id, false);
    }

    private async Task<bool> UpdateGradeStatusAsync(Guid id, bool isActive)
    {
        var userId = _currentUserService.UserId;

        var grade = await _context.Grades
            .Where(g => g.Id == id)
            .FirstOrDefaultAsync();

        if (grade == null)
        {
            return false;
        }

        grade.IsActive = isActive;
        grade.LastModifiedBy = userId?.ToString() ?? "System";
        grade.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Grade {GradeId} status updated to {Status}",
            id, isActive ? "Active" : "Inactive");

        return true;
    }

    public async Task<bool> UpdateDisplayOrderAsync(Guid id, int newOrder)
    {
        var userId = _currentUserService.UserId;

        var grade = await _context.Grades
            .Where(g => g.Id == id)
            .FirstOrDefaultAsync();

        if (grade == null)
        {
            return false;
        }

        grade.DisplayOrder = newOrder;
        grade.LastModifiedBy = userId?.ToString() ?? "System";
        grade.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> ValidateGradeCodeAsync(string code, Guid academicYearId, Guid? excludeId = null)
    {
        var query = _context.Grades
            .Where(g => g.Code == code && g.AcademicYearId == academicYearId);

        if (excludeId.HasValue)
        {
            query = query.Where(g => g.Id != excludeId.Value);
        }

        return !await query.AnyAsync();
    }

    public async Task<bool> ValidateGradeLevelAsync(int level, Guid academicYearId, Guid? excludeId = null)
    {
        var query = _context.Grades
            .Where(g => g.Level == level && g.AcademicYearId == academicYearId);

        if (excludeId.HasValue)
        {
            query = query.Where(g => g.Id != excludeId.Value);
        }

        return !await query.AnyAsync();
    }

    public async Task<bool> CanDeleteGradeAsync(Guid id)
    {
        var grade = await _context.Grades
            .Where(g => g.Id == id)
            .Include(g => g.Sections)
            .Include(g => g.Students)
            .FirstOrDefaultAsync();

        return grade != null && grade.Sections.Count == 0 && grade.Students.Count == 0;
    }

    public async Task<GradeStatisticsDto> GetGradeStatisticsAsync(Guid id)
    {
        var grade = await _context.Grades
            .Where(g => g.Id == id)
            .Include(g => g.Sections)
            .Include(g => g.Students)
            .FirstOrDefaultAsync();

        if (grade == null)
        {
            throw new ArgumentException("Grade not found", nameof(id));
        }

        var statistics = new GradeStatisticsDto
        {
            GradeId = grade.Id,
            GradeName = grade.Name,
            TotalSections = grade.Sections.Count,
            TotalStudents = grade.Students.Count,
            TotalCapacity = grade.Sections.Sum(s => s.Capacity),
            ActiveSections = grade.Sections.Count(s => s.IsActive),
            InactiveSections = grade.Sections.Count(s => !s.IsActive)
        };

        statistics.UtilizationPercentage = statistics.TotalCapacity > 0
            ? (decimal)statistics.TotalStudents / statistics.TotalCapacity * 100
            : 0;

        return statistics;
    }

    public async Task<IEnumerable<GradeStatisticsDto>> GetAllGradeStatisticsAsync(Guid academicYearId)
    {
        var grades = await _context.Grades
            .Where(g => g.AcademicYearId == academicYearId)
            .Include(g => g.Sections)
            .Include(g => g.Students)
            .ToListAsync();

        var statisticsList = new List<GradeStatisticsDto>();

        foreach (var grade in grades)
        {
            var statistics = new GradeStatisticsDto
            {
                GradeId = grade.Id,
                GradeName = grade.Name,
                TotalSections = grade.Sections.Count,
                TotalStudents = grade.Students.Count,
                TotalCapacity = grade.Sections.Sum(s => s.Capacity),
                ActiveSections = grade.Sections.Count(s => s.IsActive),
                InactiveSections = grade.Sections.Count(s => !s.IsActive)
            };

            statistics.UtilizationPercentage = statistics.TotalCapacity > 0
                ? (decimal)statistics.TotalStudents / statistics.TotalCapacity * 100
                : 0;

            statisticsList.Add(statistics);
        }

        return statisticsList;
    }

    public async Task<int> GetTotalStudentsByGradeAsync(Guid gradeId)
    {
        return await _context.Students
            .Where(s => s.CurrentGradeId == gradeId)
            .CountAsync();
    }

    public async Task<int> GetTotalSectionsByGradeAsync(Guid gradeId)
    {
        return await _context.Sections
            .Where(s => s.GradeId == gradeId)
            .CountAsync();
    }

    public async Task<bool> UpdateGradeCapacityAsync(Guid id, int newCapacity)
    {
        var userId = _currentUserService.UserId;

        var grade = await _context.Grades
            .Where(g => g.Id == id)
            .FirstOrDefaultAsync();

        if (grade == null)
        {
            return false;
        }

        grade.MaxStudents = newCapacity;
        grade.LastModifiedBy = userId?.ToString() ?? "System";
        grade.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> CheckGradeCapacityAsync(Guid gradeId)
    {
        var grade = await _context.Grades
            .Where(g => g.Id == gradeId)
            .Include(g => g.Students)
            .FirstOrDefaultAsync();

        if (grade == null)
        {
            return false;
        }

        return grade.Students.Count <= grade.MaxStudents;
    }

    public async Task<IEnumerable<GradeDto>> GetOverCapacityGradesAsync(Guid academicYearId)
    {
        var grades = await _context.Grades
            .Where(g => g.AcademicYearId == academicYearId)
            .Include(g => g.Students)
            .Include(g => g.AcademicYear)
            .Where(g => g.Students.Count > g.MaxStudents)
            .ToListAsync();

        return _mapper.Map<IEnumerable<GradeDto>>(grades);
    }

    public async Task<IEnumerable<GradeDto>> GetPromotionEligibleGradesAsync(Guid academicYearId)
    {
        var grades = await _context.Grades
            .Where(g => g.AcademicYearId == academicYearId && g.IsActive)
            .Include(g => g.AcademicYear)
            .Include(g => g.Students)
            .Where(g => g.Students.Count > 0) // Only grades with students
            .OrderBy(g => g.Level)
            .ToListAsync();

        return _mapper.Map<IEnumerable<GradeDto>>(grades);
    }

    public async Task<bool> PromoteStudentsToNextGradeAsync(Guid fromGradeId, Guid toGradeId, List<Guid> studentIds)
    {
        var userId = _currentUserService.UserId;

        var students = await _context.Students
            .Where(s => studentIds.Contains(s.Id) && s.CurrentGradeId == fromGradeId)
            .ToListAsync();

        if (students.Count == 0)
        {
            return false;
        }

        foreach (var student in students)
        {
            student.CurrentGradeId = toGradeId;
            student.LastModifiedBy = userId?.ToString() ?? "System";
            student.LastModifiedAt = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();

        _logger.LogInformation("Promoted {StudentCount} students from grade {FromGradeId} to grade {ToGradeId}",
            students.Count, fromGradeId, toGradeId);

        return true;
    }

    public async Task<bool> AddTranslationAsync(Guid gradeId, CreateGradeTranslationDto translationDto)
    {
        var userId = _currentUserService.UserId;

        var grade = await _context.Grades
            .Where(g => g.Id == gradeId)
            .FirstOrDefaultAsync();

        if (grade == null)
        {
            return false;
        }

        var translation = _mapper.Map<GradeTranslation>(translationDto);
        translation.GradeId = gradeId;
        translation.CreatedBy = userId?.ToString() ?? "System";
        translation.CreatedAt = DateTime.UtcNow;

        _context.GradeTranslations.Add(translation);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> UpdateTranslationAsync(Guid gradeId, UpdateGradeTranslationDto translationDto)
    {
        var userId = _currentUserService.UserId;

        var translation = await _context.GradeTranslations
            .Where(t => t.Id == translationDto.Id && t.GradeId == gradeId)
            .Include(t => t.Grade)
            .FirstOrDefaultAsync();

        if (translation == null)
        {
            return false;
        }

        _mapper.Map(translationDto, translation);
        translation.LastModifiedBy = userId?.ToString() ?? "System";
        translation.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> DeleteTranslationAsync(Guid gradeId, string languageCode)
    {
        var translation = await _context.GradeTranslations
            .Where(t => t.GradeId == gradeId && t.LanguageCode == languageCode)
            .Include(t => t.Grade)
            .FirstOrDefaultAsync();

        if (translation == null)
        {
            return false;
        }

        _context.GradeTranslations.Remove(translation);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<IEnumerable<GradeTranslationDto>> GetTranslationsAsync(Guid gradeId)
    {
        var translations = await _context.GradeTranslations
            .Where(t => t.GradeId == gradeId)
            .Include(t => t.Grade)
            .ToListAsync();

        return _mapper.Map<IEnumerable<GradeTranslationDto>>(translations);
    }

    public async Task<bool> BulkUpdateGradeStatusAsync(List<Guid> gradeIds, bool isActive)
    {
        var userId = _currentUserService.UserId;
        var grades = await _context.Grades
            .Where(g => gradeIds.Contains(g.Id))
            .ToListAsync();

        foreach (var grade in grades)
        {
            grade.IsActive = isActive;
            grade.LastModifiedBy = userId?.ToString() ?? "System";
            grade.LastModifiedAt = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();
        _logger.LogInformation("Bulk updated {Count} grades status to {Status}", grades.Count, isActive ? "Active" : "Inactive");
        return true;
    }

    public async Task<bool> BulkDeleteGradesAsync(List<Guid> gradeIds)
    {
        var grades = await _context.Grades
            .Where(g => gradeIds.Contains(g.Id))
            .Include(g => g.Sections)
            .Include(g => g.Students)
            .ToListAsync();

        var gradesToDelete = grades.Where(g => g.Sections.Count == 0 && g.Students.Count == 0).ToList();

        if (gradesToDelete.Any())
        {
            _context.Grades.RemoveRange(gradesToDelete);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Bulk deleted {Count} grades", gradesToDelete.Count);
        }

        return gradesToDelete.Count == gradeIds.Count;
    }

    public async Task<bool> BulkUpdateDisplayOrderAsync(Dictionary<Guid, int> gradeOrders)
    {
        var userId = _currentUserService.UserId;
        var gradeIds = gradeOrders.Keys.ToList();
        var grades = await _context.Grades
            .Where(g => gradeIds.Contains(g.Id))
            .ToListAsync();

        foreach (var grade in grades)
        {
            if (gradeOrders.TryGetValue(grade.Id, out var newOrder))
            {
                grade.DisplayOrder = newOrder;
                grade.LastModifiedBy = userId?.ToString() ?? "System";
                grade.LastModifiedAt = DateTime.UtcNow;
            }
        }

        await _context.SaveChangesAsync();
        _logger.LogInformation("Bulk updated display order for {Count} grades", grades.Count);
        return true;
    }

    public async Task<bool> ImportGradesFromCsvAsync(Stream csvStream, Guid academicYearId)
    {
        // Implementation for CSV import would go here
        // For now, return false to indicate not implemented
        await Task.CompletedTask;
        return false;
    }

    public async Task<Stream> ExportGradesToCsvAsync(Guid academicYearId)
    {
        var grades = await _context.Grades
            .Where(g => g.AcademicYearId == academicYearId)
            .Include(g => g.AcademicYear)
            .OrderBy(g => g.Level)
            .ToListAsync();

        var csv = new System.Text.StringBuilder();
        csv.AppendLine("Name,Code,Level,EducationLevel,MinAge,MaxAge,MaxStudents,IsActive,DisplayOrder,PromotionCriteria,MinPassingGrade,Description,Remarks");

        foreach (var grade in grades)
        {
            csv.AppendLine($"{grade.Name},{grade.Code},{grade.Level},{grade.EducationLevel},{grade.MinAge},{grade.MaxAge},{grade.MaxStudents},{grade.IsActive},{grade.DisplayOrder},\"{grade.PromotionCriteria}\",{grade.MinPassingGrade},\"{grade.Description}\",\"{grade.Remarks}\"");
        }

        var bytes = System.Text.Encoding.UTF8.GetBytes(csv.ToString());
        return new MemoryStream(bytes);
    }

    public async Task<bool> DuplicateGradesToNewAcademicYearAsync(Guid sourceAcademicYearId, Guid targetAcademicYearId)
    {
        var sourceGrades = await _context.Grades
            .Where(g => g.AcademicYearId == sourceAcademicYearId)
            .ToListAsync();

        var userId = _currentUserService.UserId;

        foreach (var sourceGrade in sourceGrades)
        {
            var newGrade = new Domain.Entities.Grade
            {

                Name = sourceGrade.Name,
                Code = sourceGrade.Code,
                Level = sourceGrade.Level,
                EducationLevel = sourceGrade.EducationLevel,
                Description = sourceGrade.Description,
                MinAge = sourceGrade.MinAge,
                MaxAge = sourceGrade.MaxAge,
                MaxStudents = sourceGrade.MaxStudents,
                IsActive = sourceGrade.IsActive,
                DisplayOrder = sourceGrade.DisplayOrder,
                AcademicYearId = targetAcademicYearId,
                PromotionCriteria = sourceGrade.PromotionCriteria,
                MinPassingGrade = sourceGrade.MinPassingGrade,
                Remarks = sourceGrade.Remarks,
                CreatedBy = userId?.ToString() ?? "System",
                CreatedAt = DateTime.UtcNow
            };

            _context.Grades.Add(newGrade);
        }

        await _context.SaveChangesAsync();
        _logger.LogInformation("Duplicated {Count} grades from academic year {SourceId} to {TargetId}",
            sourceGrades.Count, sourceAcademicYearId, targetAcademicYearId);
        return true;
    }

    public async Task<bool> UpdatePromotionCriteriaAsync(Guid gradeId, string criteria)
    {
        var userId = _currentUserService.UserId;
        var grade = await _context.Grades
            .Where(g => g.Id == gradeId)
            .FirstOrDefaultAsync();

        if (grade == null) return false;

        grade.PromotionCriteria = criteria;
        grade.LastModifiedBy = userId?.ToString() ?? "System";
        grade.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UpdatePassingGradeAsync(Guid gradeId, decimal passingGrade)
    {
        var userId = _currentUserService.UserId;
        var grade = await _context.Grades
            .Where(g => g.Id == gradeId)
            .FirstOrDefaultAsync();

        if (grade == null) return false;

        grade.MinPassingGrade = passingGrade;
        grade.LastModifiedBy = userId?.ToString() ?? "System";
        grade.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<GradeDto>> GetGradesByEducationLevelAsync(Guid academicYearId, EducationLevel educationLevel)
    {
        var grades = await _context.Grades
            .Where(g => g.AcademicYearId == academicYearId && g.EducationLevel == educationLevel)
            .Include(g => g.AcademicYear)
            .Include(g => g.Sections)
            .OrderBy(g => g.Level)
            .ToListAsync();

        return _mapper.Map<IEnumerable<GradeDto>>(grades);
    }

    public async Task<IEnumerable<SectionDto>> GetGradeSectionsAsync(Guid gradeId)
    {
        var sections = await _context.Sections
            .Where(s => s.GradeId == gradeId)
            .Include(s => s.Grade)
            .Include(s => s.AcademicYear)
            .Include(s => s.ClassTeacher)
                .ThenInclude(ct => ct.Faculty)
            .OrderBy(s => s.DisplayOrder)
            .ToListAsync();

        return _mapper.Map<IEnumerable<SectionDto>>(sections);
    }

    public async Task<IEnumerable<StudentDto>> GetGradeStudentsAsync(Guid gradeId)
    {
        var students = await _context.Students
            .Where(s => s.CurrentGradeId == gradeId)
            .OrderBy(s => s.RollNumber)
            .ToListAsync();

        return _mapper.Map<IEnumerable<StudentDto>>(students);
    }

    public async Task<int> GetAvailableCapacityAsync(Guid gradeId)
    {
        var grade = await _context.Grades
            .Where(g => g.Id == gradeId)
            .Include(g => g.Students)
            .FirstOrDefaultAsync();

        if (grade == null) return 0;

        return Math.Max(0, grade.MaxStudents - grade.Students.Count);
    }

    public async Task<bool> ArchiveGradeAsync(Guid gradeId)
    {
        var userId = _currentUserService.UserId;
        var grade = await _context.Grades
            .Where(g => g.Id == gradeId)
            .FirstOrDefaultAsync();

        if (grade == null) return false;

        grade.IsActive = false;
        grade.LastModifiedBy = userId?.ToString() ?? "System";
        grade.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> RestoreGradeAsync(Guid gradeId)
    {
        var userId = _currentUserService.UserId;
        var grade = await _context.Grades
            .Where(g => g.Id == gradeId)
            .FirstOrDefaultAsync();

        if (grade == null) return false;

        grade.IsActive = true;
        grade.LastModifiedBy = userId?.ToString() ?? "System";
        grade.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<GradeDto>> GetArchivedGradesAsync(Guid academicYearId)
    {
        var grades = await _context.Grades
            .Where(g => g.AcademicYearId == academicYearId && !g.IsActive)
            .Include(g => g.AcademicYear)
            .Include(g => g.Sections)
            .OrderBy(g => g.Level)
            .ToListAsync();

        return _mapper.Map<IEnumerable<GradeDto>>(grades);
    }
}
