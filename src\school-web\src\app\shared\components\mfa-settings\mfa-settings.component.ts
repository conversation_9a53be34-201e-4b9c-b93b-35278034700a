import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatStepperModule } from '@angular/material/stepper';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AuthService } from '../../../core/services/auth.service';
import { Subject, takeUntil } from 'rxjs';

interface MfaSetupData {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

@Component({
  selector: 'app-mfa-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule,
    MatStepperModule,
    TranslateModule
  ],
  templateUrl: './mfa-settings.component.html',
  styleUrl: './mfa-settings.component.scss'
})
export class MfaSettingsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Forms
  setupForm!: FormGroup;
  verifyForm!: FormGroup;
  disableForm!: FormGroup;

  // State
  isLoading = false;
  isMfaEnabled = false;
  showSetupFlow = false;
  showDisableFlow = false;
  currentStep = 0;

  // MFA Data
  mfaSetupData: MfaSetupData | null = null;
  backupCodes: string[] = [];

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private translateService: TranslateService
  ) {
    this.initForms();
  }

  ngOnInit(): void {
    this.loadUserMfaStatus();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForms(): void {
    this.setupForm = this.fb.group({
      password: ['', [Validators.required, Validators.minLength(6)]]
    });

    this.verifyForm = this.fb.group({
      verificationCode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });

    this.disableForm = this.fb.group({
      password: ['', [Validators.required]],
      mfaCode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });
  }

  private loadUserMfaStatus(): void {
    // Get current user and check MFA status
    this.authService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe(user => {
        if (user) {
          this.isMfaEnabled = user.isMfaEnabled;
        }
      });
  }

  startMfaSetup(): void {
    if (this.setupForm.invalid) {
      return;
    }

    this.isLoading = true;
    const { password } = this.setupForm.value;

    this.authService.setupMfa(password).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.mfaSetupData = response;
        this.showSetupFlow = true;
        this.currentStep = 1;
      },
      error: (error) => {
        this.isLoading = false;
        console.error('MFA setup error:', error);
        this.snackBar.open(
          this.translateService.instant('MFA.SETUP_ERROR'),
          this.translateService.instant('COMMON.CLOSE'),
          { duration: 3000 }
        );
      }
    });
  }

  verifyAndEnableMfa(): void {
    if (this.verifyForm.invalid || !this.mfaSetupData) {
      return;
    }

    this.isLoading = true;
    const { verificationCode } = this.verifyForm.value;

    this.authService.enableMfa(this.mfaSetupData.secret, verificationCode).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.enabled) {
          this.isMfaEnabled = true;
          this.backupCodes = this.mfaSetupData!.backupCodes;
          this.currentStep = 2;
          this.snackBar.open(
            this.translateService.instant('MFA.ENABLED_SUCCESS'),
            this.translateService.instant('COMMON.CLOSE'),
            { duration: 3000 }
          );
        } else {
          this.snackBar.open(
            this.translateService.instant('MFA.INVALID_CODE'),
            this.translateService.instant('COMMON.CLOSE'),
            { duration: 3000 }
          );
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('MFA enable error:', error);
        this.snackBar.open(
          this.translateService.instant('MFA.ENABLE_ERROR'),
          this.translateService.instant('COMMON.CLOSE'),
          { duration: 3000 }
        );
      }
    });
  }

  disableMfa(): void {
    if (this.disableForm.invalid) {
      return;
    }

    this.isLoading = true;
    const { password, mfaCode } = this.disableForm.value;

    this.authService.disableMfa(password, mfaCode).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.disabled) {
          this.isMfaEnabled = false;
          this.showDisableFlow = false;
          this.snackBar.open(
            this.translateService.instant('MFA.DISABLED_SUCCESS'),
            this.translateService.instant('COMMON.CLOSE'),
            { duration: 3000 }
          );
        } else {
          this.snackBar.open(
            this.translateService.instant('MFA.DISABLE_ERROR'),
            this.translateService.instant('COMMON.CLOSE'),
            { duration: 3000 }
          );
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('MFA disable error:', error);
        this.snackBar.open(
          this.translateService.instant('MFA.DISABLE_ERROR'),
          this.translateService.instant('COMMON.CLOSE'),
          { duration: 3000 }
        );
      }
    });
  }

  cancelSetup(): void {
    this.showSetupFlow = false;
    this.currentStep = 0;
    this.mfaSetupData = null;
    this.setupForm.reset();
    this.verifyForm.reset();
  }

  cancelDisable(): void {
    this.showDisableFlow = false;
    this.disableForm.reset();
  }

  finishSetup(): void {
    this.showSetupFlow = false;
    this.currentStep = 0;
    this.mfaSetupData = null;
    this.setupForm.reset();
    this.verifyForm.reset();
  }

  downloadBackupCodes(): void {
    if (this.backupCodes.length === 0) return;

    const content = this.backupCodes.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'mfa-backup-codes.txt';
    link.click();
    window.URL.revokeObjectURL(url);
  }

  copyBackupCodes(): void {
    if (this.backupCodes.length === 0) return;

    const content = this.backupCodes.join('\n');
    navigator.clipboard.writeText(content).then(() => {
      this.snackBar.open(
        this.translateService.instant('MFA.BACKUP_CODES_COPIED'),
        this.translateService.instant('COMMON.CLOSE'),
        { duration: 2000 }
      );
    });
  }

  copySecret(): void {
    if (!this.mfaSetupData) return;

    navigator.clipboard.writeText(this.mfaSetupData.secret).then(() => {
      this.snackBar.open(
        this.translateService.instant('MFA.SECRET_COPIED'),
        this.translateService.instant('COMMON.CLOSE'),
        { duration: 2000 }
      );
    });
  }

  getErrorMessage(formName: string, fieldName: string): string {
    const form = formName === 'setup' ? this.setupForm :
                 formName === 'verify' ? this.verifyForm : this.disableForm;
    const field = form.get(fieldName);

    if (field?.hasError('required')) {
      return this.translateService.instant(`MFA.${fieldName.toUpperCase()}_REQUIRED`);
    }
    if (field?.hasError('minlength')) {
      return this.translateService.instant(`MFA.${fieldName.toUpperCase()}_MIN_LENGTH`);
    }
    if (field?.hasError('pattern')) {
      return this.translateService.instant(`MFA.${fieldName.toUpperCase()}_INVALID`);
    }
    return '';
  }
}
