import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { TranslateModule } from '@ngx-translate/core';
import { ClubService } from '../../../../core/services/club.service';
import { Club } from '../../../../core/models/club.model';

// Local interfaces for backward compatibility
interface MockClub {
  id: number;
  name: string;
  category: string;
  image: string;
  description: string;
  meetingSchedule: string;
  location: string;
  advisors: string[];
  studentLeaders?: {
    name: string;
    role: string;
    grade: string;
    image?: string;
  }[];
  activities: string[];
  achievements?: string[];
  requirements?: string;
  joinProcess?: string;
  contactEmail?: string;
  socialLinks?: {
    instagram?: string;
    facebook?: string;
    website?: string;
  };
  gallery?: string[];
  upcomingEvents?: {
    title: string;
    date: Date;
    description: string;
  }[];
}

@Component({
  selector: 'app-club-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatChipsModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    TranslateModule
  ],
  templateUrl: './club-detail.component.html',
  styleUrls: ['./club-detail.component.scss']
})
export class ClubDetailComponent implements OnInit {
  // Club data
  club: Club | null = null;

  // Loading state
  loading = true;

  // Error state
  error = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private clubService: ClubService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    // Get club ID from route params
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.loadClub(parseInt(id, 10));
      } else {
        this.error = true;
        this.loading = false;
      }
    });
  }

  /**
   * Load club by ID
   */
  private loadClub(id: number): void {
    this.loading = true;
    this.error = false;

    this.clubService.getClub(id).subscribe({
      next: (club) => {
        this.club = club;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading club:', error);
        this.error = true;
        this.loading = false;
        this.snackBar.open('Failed to load club details. Please try again later.', 'Close', {
          duration: 5000
        });

        // Use mock data for development/demo purposes
        this.useMockClub(id);
      }
    });
  }

  /**
   * Use mock club data for development/demo purposes
   */
  private useMockClub(id: number): void {
    // Mock clubs data
    const clubsData: MockClub[] = [
      {
        id: 1,
        name: 'Science Club',
        category: 'Academic',
        image: 'https://via.placeholder.com/800x400?text=Science+Club',
        description: 'The Science Club provides students with opportunities to explore scientific concepts beyond the classroom. Through hands-on experiments, field trips, and guest speakers, members develop a deeper understanding and appreciation of various scientific disciplines. The club also prepares students for science competitions and fairs, encouraging them to pursue their interests in STEM fields.',
        meetingSchedule: 'Every Tuesday, 3:30 PM - 5:00 PM',
        location: 'Science Lab 2',
        advisors: ['Dr. Ahmed Khan', 'Ms. Sarah Johnson'],
        studentLeaders: [
          {
            name: 'Rahul Patel',
            role: 'President',
            grade: '11',
            image: 'https://via.placeholder.com/100x100?text=Rahul'
          },
          {
            name: 'Aisha Rahman',
            role: 'Vice President',
            grade: '10',
            image: 'https://via.placeholder.com/100x100?text=Aisha'
          },
          {
            name: 'Michael Chen',
            role: 'Secretary',
            grade: '11',
            image: 'https://via.placeholder.com/100x100?text=Michael'
          }
        ],
        activities: [
          'Conducting scientific experiments',
          'Participating in science fairs and competitions',
          'Field trips to research facilities and museums',
          'Hosting guest speakers from scientific fields',
          'Collaborative research projects'
        ],
        achievements: [
          'First place in the Regional Science Fair 2023',
          'Third place in the National Science Olympiad 2022',
          'Published research in the Young Scientists Journal'
        ],
        requirements: 'Open to all students in grades 9-12 with an interest in science. No prior experience required.',
        joinProcess: 'Attend any meeting to join. Annual membership fee: $20 (covers materials for experiments and activities).',
        contactEmail: '<EMAIL>',
        socialLinks: {
          instagram: 'https://instagram.com/schoolscienceclub',
          website: 'https://school.edu/clubs/science'
        },
        gallery: [
          'https://via.placeholder.com/400x300?text=Science+Experiment',
          'https://via.placeholder.com/400x300?text=Science+Fair',
          'https://via.placeholder.com/400x300?text=Field+Trip'
        ],
        upcomingEvents: [
          {
            title: 'Science Fair Preparation Workshop',
            date: new Date('2023-11-15'),
            description: 'Learn how to prepare and present your science fair project effectively.'
          },
          {
            title: 'Guest Lecture: Renewable Energy',
            date: new Date('2023-12-05'),
            description: 'Dr. James Wilson from the University Research Center will discuss advances in renewable energy technologies.'
          }
        ]
      },
      {
        id: 2,
        name: 'Debate Club',
        category: 'Academic',
        image: 'https://via.placeholder.com/800x400?text=Debate+Club',
        description: 'The Debate Club helps students develop critical thinking, public speaking, and argumentation skills. Members participate in various debate formats and competitions, learning to research topics thoroughly and present arguments persuasively. The club fosters confidence, articulation, and the ability to consider multiple perspectives on complex issues.',
        meetingSchedule: 'Every Monday and Thursday, 4:00 PM - 5:30 PM',
        location: 'Room 205',
        advisors: ['Mr. Robert Williams', 'Ms. Priya Sharma'],
        studentLeaders: [
          {
            name: 'Sophia Lee',
            role: 'President',
            grade: '12',
            image: 'https://via.placeholder.com/100x100?text=Sophia'
          },
          {
            name: 'Ahmed Hassan',
            role: 'Vice President',
            grade: '11',
            image: 'https://via.placeholder.com/100x100?text=Ahmed'
          }
        ],
        activities: [
          'Parliamentary and policy debate practice',
          'Public speaking workshops',
          'Research and argument development',
          'Participation in regional and national debate tournaments',
          'Hosting interschool debate competitions'
        ],
        achievements: [
          'Champions of the State Debate Tournament 2023',
          'Finalists in the National High School Debate League',
          'Best Speaker Award at the Regional Debate Championship'
        ],
        requirements: 'Open to all students in grades 9-12. Commitment to attend regular meetings and tournaments is expected.',
        joinProcess: 'Attend an information session at the beginning of each semester or contact the club advisors.',
        contactEmail: '<EMAIL>',
        socialLinks: {
          instagram: 'https://instagram.com/schooldebateclub',
          facebook: 'https://facebook.com/schooldebateclub'
        },
        upcomingEvents: [
          {
            title: 'Public Speaking Workshop',
            date: new Date('2023-11-10'),
            description: 'Learn techniques to improve your public speaking skills and overcome stage fright.'
          },
          {
            title: 'Interschool Debate Tournament',
            date: new Date('2023-12-15'),
            description: 'Our annual tournament with participation from schools across the region.'
          }
        ]
      },
      {
        id: 3,
        name: 'Art Club',
        category: 'Arts',
        image: 'https://via.placeholder.com/800x400?text=Art+Club',
        description: 'The Art Club provides a creative space for students to explore various art forms and techniques. Members work on individual and collaborative projects, experiment with different media, and develop their artistic skills. The club organizes exhibitions, visits to art galleries, and workshops with professional artists to inspire and educate members.',
        meetingSchedule: 'Every Wednesday, 3:30 PM - 5:00 PM',
        location: 'Art Studio',
        advisors: ['Ms. Emily Chen', 'Mr. David Rodriguez'],
        studentLeaders: [
          {
            name: 'Fatima Ali',
            role: 'President',
            grade: '12',
            image: 'https://via.placeholder.com/100x100?text=Fatima'
          },
          {
            name: 'Jason Kim',
            role: 'Vice President',
            grade: '11',
            image: 'https://via.placeholder.com/100x100?text=Jason'
          }
        ],
        activities: [
          'Drawing and painting sessions',
          'Sculpture and ceramics workshops',
          'Digital art and photography projects',
          'Art exhibitions and competitions',
          'Field trips to museums and galleries'
        ],
        achievements: [
          'Best School Art Exhibition Award 2023',
          'Multiple winners in the Regional Youth Art Competition',
          'Community mural project at the local children\'s hospital'
        ],
        requirements: 'Open to all students with an interest in art. No prior experience necessary.',
        joinProcess: 'Drop in to any meeting to join. Materials fee: $30 per semester.',
        contactEmail: '<EMAIL>',
        socialLinks: {
          instagram: 'https://instagram.com/schoolartclub'
        },
        gallery: [
          'https://via.placeholder.com/400x300?text=Student+Artwork+1',
          'https://via.placeholder.com/400x300?text=Student+Artwork+2',
          'https://via.placeholder.com/400x300?text=Art+Exhibition'
        ],
        upcomingEvents: [
          {
            title: 'Watercolor Workshop',
            date: new Date('2023-11-22'),
            description: 'Learn watercolor techniques with guest artist Maria Gonzalez.'
          },
          {
            title: 'Winter Art Exhibition',
            date: new Date('2023-12-10'),
            description: 'Annual exhibition showcasing student artwork from throughout the semester.'
          }
        ]
      },
      {
        id: 4,
        name: 'Environmental Club',
        category: 'Service',
        image: 'https://via.placeholder.com/800x400?text=Environmental+Club',
        description: 'The Environmental Club is dedicated to promoting sustainability and environmental awareness within our school and community. Members engage in conservation projects, educational campaigns, and advocacy efforts to address environmental issues. The club works to reduce the school\'s ecological footprint and inspire positive change through action and education.',
        meetingSchedule: 'Every Friday, 3:30 PM - 4:30 PM',
        location: 'Room 103',
        advisors: ['Ms. Lisa Green', 'Mr. Thomas Brown'],
        studentLeaders: [
          {
            name: 'Maya Johnson',
            role: 'President',
            grade: '11',
            image: 'https://via.placeholder.com/100x100?text=Maya'
          },
          {
            name: 'Carlos Rodriguez',
            role: 'Vice President',
            grade: '10',
            image: 'https://via.placeholder.com/100x100?text=Carlos'
          }
        ],
        activities: [
          'Campus recycling and waste reduction initiatives',
          'Tree planting and garden maintenance',
          'Environmental awareness campaigns',
          'Community clean-up events',
          'Sustainable living workshops'
        ],
        achievements: [
          'Established the school\'s composting program',
          'Reduced campus plastic waste by 40%',
          'Received the Green Schools Recognition Award'
        ],
        requirements: 'Open to all students who are passionate about environmental issues.',
        joinProcess: 'Attend any meeting to join or sign up at the club fair.',
        contactEmail: '<EMAIL>',
        socialLinks: {
          instagram: 'https://instagram.com/schoolenvironmentalclub',
          website: 'https://school.edu/clubs/environmental'
        },
        gallery: [
          'https://via.placeholder.com/400x300?text=Tree+Planting',
          'https://via.placeholder.com/400x300?text=Beach+Cleanup',
          'https://via.placeholder.com/400x300?text=Recycling+Drive'
        ],
        upcomingEvents: [
          {
            title: 'Community Clean-up Day',
            date: new Date('2023-11-18'),
            description: 'Join us for a clean-up event at the local park.'
          },
          {
            title: 'Sustainable Holiday Workshop',
            date: new Date('2023-12-08'),
            description: 'Learn how to reduce waste during the holiday season with eco-friendly gift wrapping and decorations.'
          }
        ]
      },
      {
        id: 5,
        name: 'Robotics Club',
        category: 'STEM',
        image: 'https://via.placeholder.com/800x400?text=Robotics+Club',
        description: 'The Robotics Club provides hands-on experience in designing, building, and programming robots. Members work in teams to create robots for various competitions and challenges, developing skills in engineering, programming, and problem-solving. The club emphasizes collaboration, innovation, and the practical application of STEM concepts.',
        meetingSchedule: 'Tuesday and Thursday, 3:30 PM - 5:30 PM',
        location: 'Technology Lab',
        advisors: ['Mr. James Wilson', 'Dr. Anita Patel'],
        studentLeaders: [
          {
            name: 'Daniel Lee',
            role: 'President',
            grade: '12',
            image: 'https://via.placeholder.com/100x100?text=Daniel'
          },
          {
            name: 'Zara Khan',
            role: 'Vice President',
            grade: '11',
            image: 'https://via.placeholder.com/100x100?text=Zara'
          },
          {
            name: 'Tyler Johnson',
            role: 'Technical Lead',
            grade: '12',
            image: 'https://via.placeholder.com/100x100?text=Tyler'
          }
        ],
        activities: [
          'Robot design and construction',
          'Programming and testing',
          'Participation in robotics competitions',
          'STEM outreach to elementary schools',
          'Workshops on engineering and programming concepts'
        ],
        achievements: [
          'First place in the Regional Robotics Competition 2023',
          'Qualification for the National Robotics Championship',
          'Innovation Award at the Tech Challenge'
        ],
        requirements: 'Open to students in grades 9-12. No prior experience required, but commitment to team projects is essential.',
        joinProcess: 'Attend an information session at the beginning of the school year. Team selection process for competition teams.',
        contactEmail: '<EMAIL>',
        socialLinks: {
          instagram: 'https://instagram.com/schoolroboticsclub',
          website: 'https://school.edu/clubs/robotics'
        },
        gallery: [
          'https://via.placeholder.com/400x300?text=Robot+Building',
          'https://via.placeholder.com/400x300?text=Competition',
          'https://via.placeholder.com/400x300?text=Team+Working'
        ],
        upcomingEvents: [
          {
            title: 'Robotics Workshop for Beginners',
            date: new Date('2023-11-14'),
            description: 'Introduction to basic robotics concepts and programming.'
          },
          {
            title: 'Regional Robotics Competition Prep',
            date: new Date('2023-12-12'),
            description: 'Final preparation for the upcoming regional competition in January.'
          }
        ]
      }
    ];

    const mockClub = clubsData.find(club => club.id === id);

    if (mockClub) {
      // Convert mock club to Club interface
      this.club = {
        id: mockClub.id,
        name: mockClub.name,
        description: mockClub.description,
        shortDescription: mockClub.description.substring(0, 100) + '...',
        category: mockClub.category,
        meetingSchedule: mockClub.meetingSchedule,
        location: mockClub.location,
        requirements: mockClub.requirements || '',
        joinProcess: mockClub.joinProcess || '',
        contactEmail: mockClub.contactEmail || '',
        website: mockClub.socialLinks?.website || '',
        instagram: mockClub.socialLinks?.instagram || '',
        facebook: mockClub.socialLinks?.facebook || '',
        isFeatured: false,
        displayOrder: 0,
        isActive: true,
        profileImageUrl: mockClub.image,
        createdAt: new Date(),
        advisors: mockClub.advisors.map((name, index) => ({
          id: index + 1,
          clubId: mockClub.id,
          name: name,
          email: '',
          phone: '',
          displayOrder: index
        })),
        leaders: mockClub.studentLeaders?.map((leader, index) => ({
          id: index + 1,
          clubId: mockClub.id,
          name: leader.name,
          role: leader.role,
          grade: leader.grade,
          profileImageUrl: leader.image,
          displayOrder: index
        })) || [],
        activities: mockClub.activities.map((activity, index) => ({
          id: index + 1,
          clubId: mockClub.id,
          description: activity,
          displayOrder: index
        })),
        achievements: mockClub.achievements?.map((achievement, index) => ({
          id: index + 1,
          clubId: mockClub.id,
          description: achievement,
          displayOrder: index
        })) || [],
        events: mockClub.upcomingEvents?.map((event, index) => ({
          id: index + 1,
          clubId: mockClub.id,
          title: event.title,
          date: event.date,
          time: event.date.toLocaleTimeString(),
          location: '',
          description: event.description,
          isActive: true
        })) || [],
        galleryItems: mockClub.gallery?.map((url, index) => ({
          id: index + 1,
          clubId: mockClub.id,
          imageUrl: url,
          caption: '',
          displayOrder: index
        })) || []
      };
      this.loading = false;
    }
  }

  /**
   * Navigate back to clubs listing
   */
  goBack(): void {
    this.router.navigate(['/campus/clubs']);
  }

  /**
   * Retry loading club data
   */
  retryLoading(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loadClub(parseInt(id, 10));
    }
  }

  /**
   * Format date for display
   */
  formatDate(date: Date): string {
    if (!date) return '';

    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };

    // Handle both Date objects and ISO strings
    const dateObj = date instanceof Date ? date : new Date(date);
    return dateObj.toLocaleDateString('en-US', options);
  }

  /**
   * Check if advisors is an array of ClubAdvisor objects
   */
  isAdvisorArray(advisors: any[]): boolean {
    return advisors && advisors.length > 0 && typeof advisors[0] === 'object';
  }

  /**
   * Get advisor names as a comma-separated string
   */
  getAdvisorNames(advisors: any[]): string {
    if (!advisors || advisors.length === 0) return '';

    if (typeof advisors[0] === 'string') {
      return advisors.join(', ');
    } else if (typeof advisors[0] === 'object') {
      return advisors.map(a => a.name).join(', ');
    }

    return '';
  }
}
