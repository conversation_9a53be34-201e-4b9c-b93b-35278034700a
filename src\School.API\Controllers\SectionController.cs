using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.Application.DTOs;
using School.Application.Features.Section;
using School.Domain.Enums;

namespace School.API.Controllers;

/// <summary>
/// API controller for Section management operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SectionController : ControllerBase
{
    private readonly ISectionService _sectionService;
    private readonly ILogger<SectionController> _logger;

    public SectionController(ISectionService sectionService, ILogger<SectionController> logger)
    {
        _sectionService = sectionService;
        _logger = logger;
    }

    /// <summary>
    /// Get all sections with filtering and pagination
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<object>> GetSections([FromQuery] SectionFilterDto filter)
    {
        try
        {
            var (sections, totalCount) = await _sectionService.GetAllSectionsAsync(filter);
            
            return Ok(new
            {
                data = sections,
                totalCount,
                page = filter.Page,
                pageSize = filter.PageSize,
                totalPages = (int)Math.Ceiling((double)totalCount / filter.PageSize)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving sections");
            return StatusCode(500, "An error occurred while retrieving sections");
        }
    }

    /// <summary>
    /// Get section by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<SectionDto>> GetSection(Guid id)
    {
        try
        {
            var section = await _sectionService.GetSectionByIdAsync(id);
            
            if (section == null)
            {
                return NotFound($"Section with ID {id} not found");
            }

            return Ok(section);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving section {SectionId}", id);
            return StatusCode(500, "An error occurred while retrieving the section");
        }
    }

    /// <summary>
    /// Get sections by grade
    /// </summary>
    [HttpGet("grade/{gradeId}")]
    public async Task<ActionResult<IEnumerable<SectionDto>>> GetSectionsByGrade(Guid gradeId)
    {
        try
        {
            var sections = await _sectionService.GetSectionsByGradeAsync(gradeId);
            return Ok(sections);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving sections for grade {GradeId}", gradeId);
            return StatusCode(500, "An error occurred while retrieving sections");
        }
    }

    /// <summary>
    /// Get sections by academic year
    /// </summary>
    [HttpGet("academic-year/{academicYearId}")]
    public async Task<ActionResult<IEnumerable<SectionDto>>> GetSectionsByAcademicYear(Guid academicYearId)
    {
        try
        {
            var sections = await _sectionService.GetSectionsByAcademicYearAsync(academicYearId);
            return Ok(sections);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving sections for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while retrieving sections");
        }
    }

    /// <summary>
    /// Get active sections by academic year
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/active")]
    public async Task<ActionResult<IEnumerable<SectionDto>>> GetActiveSections(Guid academicYearId)
    {
        try
        {
            var sections = await _sectionService.GetActiveSectionsAsync(academicYearId);
            return Ok(sections);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active sections for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while retrieving active sections");
        }
    }

    /// <summary>
    /// Create a new section
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<object>> CreateSection([FromBody] CreateSectionDto sectionDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Validate section code uniqueness
            var isCodeValid = await _sectionService.ValidateSectionCodeAsync(sectionDto.Code, sectionDto.GradeId);
            if (!isCodeValid)
            {
                return BadRequest($"Section code '{sectionDto.Code}' already exists for this grade");
            }

            // Validate section name uniqueness
            var isNameValid = await _sectionService.ValidateSectionNameAsync(sectionDto.Name, sectionDto.GradeId);
            if (!isNameValid)
            {
                return BadRequest($"Section name '{sectionDto.Name}' already exists for this grade");
            }

            var sectionId = await _sectionService.CreateSectionAsync(sectionDto);
            
            return CreatedAtAction(nameof(GetSection), new { id = sectionId }, new { id = sectionId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating section");
            return StatusCode(500, "An error occurred while creating the section");
        }
    }

    /// <summary>
    /// Update an existing section
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult> UpdateSection(Guid id, [FromBody] UpdateSectionDto sectionDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Validate section code uniqueness (excluding current section)
            var isCodeValid = await _sectionService.ValidateSectionCodeAsync(sectionDto.Code, sectionDto.GradeId, id);
            if (!isCodeValid)
            {
                return BadRequest($"Section code '{sectionDto.Code}' already exists for this grade");
            }

            // Validate section name uniqueness (excluding current section)
            var isNameValid = await _sectionService.ValidateSectionNameAsync(sectionDto.Name, sectionDto.GradeId, id);
            if (!isNameValid)
            {
                return BadRequest($"Section name '{sectionDto.Name}' already exists for this grade");
            }

            var success = await _sectionService.UpdateSectionAsync(id, sectionDto);
            
            if (!success)
            {
                return NotFound($"Section with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating section {SectionId}", id);
            return StatusCode(500, "An error occurred while updating the section");
        }
    }

    /// <summary>
    /// Delete a section
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteSection(Guid id)
    {
        try
        {
            // Check if section can be deleted
            var canDelete = await _sectionService.CanDeleteSectionAsync(id);
            if (!canDelete)
            {
                return BadRequest("Cannot delete section that has enrolled students");
            }

            var success = await _sectionService.DeleteSectionAsync(id);
            
            if (!success)
            {
                return NotFound($"Section with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting section {SectionId}", id);
            return StatusCode(500, "An error occurred while deleting the section");
        }
    }

    /// <summary>
    /// Activate a section
    /// </summary>
    [HttpPatch("{id}/activate")]
    public async Task<ActionResult> ActivateSection(Guid id)
    {
        try
        {
            var success = await _sectionService.ActivateSectionAsync(id);
            
            if (!success)
            {
                return NotFound($"Section with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating section {SectionId}", id);
            return StatusCode(500, "An error occurred while activating the section");
        }
    }

    /// <summary>
    /// Deactivate a section
    /// </summary>
    [HttpPatch("{id}/deactivate")]
    public async Task<ActionResult> DeactivateSection(Guid id)
    {
        try
        {
            var success = await _sectionService.DeactivateSectionAsync(id);
            
            if (!success)
            {
                return NotFound($"Section with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating section {SectionId}", id);
            return StatusCode(500, "An error occurred while deactivating the section");
        }
    }

    /// <summary>
    /// Get section statistics
    /// </summary>
    [HttpGet("{id}/statistics")]
    public async Task<ActionResult<SectionStatisticsDto>> GetSectionStatistics(Guid id)
    {
        try
        {
            var statistics = await _sectionService.GetSectionStatisticsAsync(id);
            return Ok(statistics);
        }
        catch (ArgumentException)
        {
            return NotFound($"Section with ID {id} not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving section statistics for {SectionId}", id);
            return StatusCode(500, "An error occurred while retrieving section statistics");
        }
    }

    /// <summary>
    /// Update section capacity
    /// </summary>
    [HttpPatch("{id}/capacity")]
    public async Task<ActionResult> UpdateCapacity(Guid id, [FromBody] int newCapacity)
    {
        try
        {
            if (newCapacity <= 0)
            {
                return BadRequest("Capacity must be greater than 0");
            }

            var success = await _sectionService.UpdateSectionCapacityAsync(id, newCapacity);
            
            if (!success)
            {
                return NotFound($"Section with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating capacity for section {SectionId}", id);
            return StatusCode(500, "An error occurred while updating the capacity");
        }
    }

    /// <summary>
    /// Update section display order
    /// </summary>
    [HttpPatch("{id}/display-order")]
    public async Task<ActionResult> UpdateDisplayOrder(Guid id, [FromBody] int newOrder)
    {
        try
        {
            var success = await _sectionService.UpdateDisplayOrderAsync(id, newOrder);

            if (!success)
            {
                return NotFound($"Section with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating display order for section {SectionId}", id);
            return StatusCode(500, "An error occurred while updating the display order");
        }
    }

    /// <summary>
    /// Enroll student in section
    /// </summary>
    [HttpPost("{id}/students/{studentId}")]
    public async Task<ActionResult> EnrollStudent(Guid id, Guid studentId)
    {
        try
        {
            var success = await _sectionService.EnrollStudentAsync(id, studentId);

            if (!success)
            {
                return BadRequest("Failed to enroll student. Section may be at capacity or student/section not found.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enrolling student {StudentId} in section {SectionId}", studentId, id);
            return StatusCode(500, "An error occurred while enrolling the student");
        }
    }

    /// <summary>
    /// Unenroll student from section
    /// </summary>
    [HttpDelete("{id}/students/{studentId}")]
    public async Task<ActionResult> UnenrollStudent(Guid id, Guid studentId)
    {
        try
        {
            var success = await _sectionService.UnenrollStudentAsync(id, studentId);

            if (!success)
            {
                return NotFound("Student not found in this section");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unenrolling student {StudentId} from section {SectionId}", studentId, id);
            return StatusCode(500, "An error occurred while unenrolling the student");
        }
    }

    /// <summary>
    /// Transfer student between sections
    /// </summary>
    [HttpPost("transfer-student")]
    public async Task<ActionResult> TransferStudent([FromBody] TransferStudentDto transferDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var success = await _sectionService.TransferStudentAsync(
                transferDto.StudentId,
                transferDto.FromSectionId,
                transferDto.ToSectionId);

            if (!success)
            {
                return BadRequest("Failed to transfer student. Target section may be at capacity.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error transferring student {StudentId} from section {FromSectionId} to {ToSectionId}",
                transferDto.StudentId, transferDto.FromSectionId, transferDto.ToSectionId);
            return StatusCode(500, "An error occurred while transferring the student");
        }
    }

    /// <summary>
    /// Assign class teacher to section
    /// </summary>
    [HttpPost("{id}/class-teacher/{facultyId}")]
    public async Task<ActionResult> AssignClassTeacher(Guid id, Guid facultyId)
    {
        try
        {
            var success = await _sectionService.AssignClassTeacherAsync(id, facultyId);

            if (!success)
            {
                return BadRequest("Failed to assign class teacher. Section or faculty not found.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning class teacher {FacultyId} to section {SectionId}", facultyId, id);
            return StatusCode(500, "An error occurred while assigning the class teacher");
        }
    }

    /// <summary>
    /// Remove class teacher from section
    /// </summary>
    [HttpDelete("{id}/class-teacher")]
    public async Task<ActionResult> RemoveClassTeacher(Guid id)
    {
        try
        {
            var success = await _sectionService.RemoveClassTeacherAsync(id);

            if (!success)
            {
                return NotFound($"Section with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing class teacher from section {SectionId}", id);
            return StatusCode(500, "An error occurred while removing the class teacher");
        }
    }

    /// <summary>
    /// Get section students
    /// </summary>
    [HttpGet("{id}/students")]
    public async Task<ActionResult<IEnumerable<StudentDto>>> GetSectionStudents(Guid id)
    {
        try
        {
            var students = await _sectionService.GetSectionStudentsAsync(id);
            return Ok(students);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving students for section {SectionId}", id);
            return StatusCode(500, "An error occurred while retrieving section students");
        }
    }

    /// <summary>
    /// Get available slots in section
    /// </summary>
    [HttpGet("{id}/available-slots")]
    public async Task<ActionResult<int>> GetAvailableSlots(Guid id)
    {
        try
        {
            var slots = await _sectionService.GetAvailableSlotsAsync(id);
            return Ok(slots);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving available slots for section {SectionId}", id);
            return StatusCode(500, "An error occurred while retrieving available slots");
        }
    }

    /// <summary>
    /// Get sections by shift
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/shift/{shift}")]
    public async Task<ActionResult<IEnumerable<SectionDto>>> GetSectionsByShift(Guid academicYearId, ShiftType shift)
    {
        try
        {
            var sections = await _sectionService.GetSectionsByShiftAsync(academicYearId, shift);
            return Ok(sections);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving sections by shift {Shift} for academic year {AcademicYearId}",
                shift, academicYearId);
            return StatusCode(500, "An error occurred while retrieving sections");
        }
    }

    /// <summary>
    /// Get sections by teaching medium
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/medium/{medium}")]
    public async Task<ActionResult<IEnumerable<SectionDto>>> GetSectionsByMedium(Guid academicYearId, TeachingMedium medium)
    {
        try
        {
            var sections = await _sectionService.GetSectionsByMediumAsync(academicYearId, medium);
            return Ok(sections);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving sections by medium {Medium} for academic year {AcademicYearId}",
                medium, academicYearId);
            return StatusCode(500, "An error occurred while retrieving sections");
        }
    }

    /// <summary>
    /// Export sections to CSV
    /// </summary>
    [HttpGet("grade/{gradeId}/export")]
    public async Task<ActionResult> ExportSections(Guid gradeId)
    {
        try
        {
            var csvStream = await _sectionService.ExportSectionsToCsvAsync(gradeId);
            return File(csvStream, "text/csv", $"sections_{gradeId}_{DateTime.Now:yyyyMMdd}.csv");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting sections for grade {GradeId}", gradeId);
            return StatusCode(500, "An error occurred while exporting sections");
        }
    }
}
