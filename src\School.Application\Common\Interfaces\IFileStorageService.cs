using System.IO;
using System.Threading.Tasks;

namespace School.Application.Common.Interfaces
{
    public interface IFileStorageService
    {
        /// <summary>
        /// Saves a file to the storage system
        /// </summary>
        /// <param name="fileStream">The file stream to save</param>
        /// <param name="fileName">The name of the file</param>
        /// <param name="subFolder">The subfolder to save the file in</param>
        /// <returns>The URL to the saved file</returns>
        Task<string> SaveFileAsync(Stream fileStream, string fileName, string subFolder = "");

        /// <summary>
        /// Deletes a file from the storage system
        /// </summary>
        /// <param name="filePath">The path to the file to delete</param>
        /// <returns>True if the file was deleted, false otherwise</returns>
        Task<bool> DeleteFileAsync(string filePath);

        /// <summary>
        /// Gets the URL for a file
        /// </summary>
        /// <param name="fileName">The name of the file or the file path</param>
        /// <param name="subFolder">The subfolder where the file is located (ignored if fileName is a path)</param>
        /// <returns>The URL to the file</returns>
        string GetFileUrl(string fileName, string subFolder = "");
    }
}
