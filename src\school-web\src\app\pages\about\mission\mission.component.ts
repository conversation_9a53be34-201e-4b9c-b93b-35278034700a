import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';
import { EnhancedHeroComponent } from '../../../shared/components/enhanced-hero/enhanced-hero.component';

@Component({
  selector: 'app-mission',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    TranslateModule,
    EnhancedHeroComponent
  ],
  templateUrl: './mission.component.html',
  styleUrls: ['./mission.component.scss']
})
export class MissionComponent {
  // Core values of the school
  coreValues = [
    {
      title: 'Excellence',
      description: 'We strive for excellence in all aspects of education, challenging our students to reach their highest potential academically, socially, and personally.',
      icon: 'star'
    },
    {
      title: 'Integrity',
      description: 'We foster honesty, ethics, and responsibility, encouraging our students to act with integrity in all their endeavors and relationships.',
      icon: 'verified_user'
    },
    {
      title: 'Respect',
      description: 'We promote respect for self, others, and the environment, valuing diversity and cultivating a community of mutual understanding and appreciation.',
      icon: 'people'
    },
    {
      title: 'Innovation',
      description: 'We embrace innovation and creativity, preparing students to adapt to a rapidly changing world with critical thinking and problem-solving skills.',
      icon: 'lightbulb'
    },
    {
      title: 'Community',
      description: 'We build a supportive community where students, parents, and staff collaborate to create a nurturing and inclusive learning environment.',
      icon: 'diversity_3'
    },
    {
      title: 'Global Citizenship',
      description: 'We develop global citizens who understand their responsibilities to society and are prepared to make positive contributions to the world.',
      icon: 'public'
    }
  ];

  // Strategic goals
  strategicGoals = [
    {
      title: 'Academic Excellence',
      description: 'Provide a rigorous and comprehensive curriculum that challenges students to achieve their highest potential and prepares them for success in higher education and beyond.',
      icon: 'school'
    },
    {
      title: 'Character Development',
      description: 'Foster the development of strong moral character, ethical values, and leadership skills that will guide students throughout their lives.',
      icon: 'psychology'
    },
    {
      title: 'Innovative Learning',
      description: 'Implement innovative teaching methods and technologies that enhance learning experiences and develop 21st-century skills.',
      icon: 'smart_toy'
    },
    {
      title: 'Global Perspective',
      description: 'Cultivate a global perspective that prepares students to thrive in an interconnected world and contribute to solving global challenges.',
      icon: 'language'
    }
  ];
}
