<div class="notice-form-container">
  <div class="page-header">
    <div class="header-left">
      <button mat-icon-button routerLink="/admin/notices" aria-label="Back">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <h1>{{ isEditMode ? ('admin.notices.edit' | translate) : ('admin.notices.create' | translate) }}</h1>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Error Message -->
  <div class="error-container" *ngIf="error">
    <mat-card class="error-card">
      <mat-card-content>
        <mat-icon color="warn">error</mat-icon>
        <p>{{ 'admin.common.error_loading' | translate }}</p>
        <button mat-raised-button color="primary" (click)="loadNotice(noticeId!)">
          <mat-icon>refresh</mat-icon>
          {{ 'admin.common.retry' | translate }}
        </button>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Notice Form -->
  <div class="form-container" *ngIf="!isLoading && !error">
    <mat-card>
      <mat-card-content>
        <mat-tab-group>
          <!-- Default Language (English) Tab -->
          <mat-tab>
            <ng-template mat-tab-label>
              <span class="tab-label">English (Default)</span>
            </ng-template>
            <form [formGroup]="noticeForm" class="notice-form">
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'admin.notices.title_field' | translate }}</mat-label>
                  <input matInput formControlName="title" required>
                  <mat-error *ngIf="noticeForm.get('title')?.hasError('required')">
                    {{ 'admin.common.field_required' | translate }}
                  </mat-error>
                  <mat-error *ngIf="noticeForm.get('title')?.hasError('maxlength')">
                    {{ 'admin.common.max_length' | translate: { length: 200 } }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'admin.notices.content_field' | translate }}</mat-label>
                  <textarea matInput formControlName="content" rows="6" required></textarea>
                  <mat-error *ngIf="noticeForm.get('content')?.hasError('required')">
                    {{ 'admin.common.field_required' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row two-columns">
                <mat-form-field appearance="outline">
                  <mat-label>{{ 'admin.notices.category_field' | translate }}</mat-label>
                  <mat-select formControlName="category" required>
                    <mat-option *ngFor="let category of categories" [value]="category.value">
                      {{ category.label }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="noticeForm.get('category')?.hasError('required')">
                    {{ 'admin.common.field_required' | translate }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>{{ 'admin.notices.priority_field' | translate }}</mat-label>
                  <mat-select formControlName="priority" required>
                    <mat-option *ngFor="let priority of priorities" [value]="priority.value">
                      {{ priority.label }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="noticeForm.get('priority')?.hasError('required')">
                    {{ 'admin.common.field_required' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row two-columns">
                <mat-form-field appearance="outline">
                  <mat-label>{{ 'admin.notices.start_date_field' | translate }}</mat-label>
                  <input matInput [matDatepicker]="startDatePicker" formControlName="startDate" required>
                  <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
                  <mat-datepicker #startDatePicker></mat-datepicker>
                  <mat-error *ngIf="noticeForm.get('startDate')?.hasError('required')">
                    {{ 'admin.common.field_required' | translate }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>{{ 'admin.notices.end_date_field' | translate }}</mat-label>
                  <input matInput [matDatepicker]="endDatePicker" formControlName="endDate">
                  <mat-datepicker-toggle matSuffix [for]="endDatePicker"></mat-datepicker-toggle>
                  <mat-datepicker #endDatePicker></mat-datepicker>
                  <mat-hint>{{ 'admin.notices.end_date_hint' | translate }}</mat-hint>
                  <mat-error *ngIf="noticeForm.hasError('endDateBeforeStartDate')">
                    {{ 'admin.notices.end_date_before_start_date' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-slide-toggle formControlName="isActive" color="primary">
                  {{ 'admin.notices.is_active' | translate }}
                </mat-slide-toggle>
              </div>
            </form>
          </mat-tab>

          <!-- Translation Tabs -->
          <mat-tab *ngFor="let lang of getNonEnglishLanguages()">
            <ng-template mat-tab-label>
              <span class="tab-label">{{ lang.name }} (Translation)</span>
              <mat-icon *ngIf="hasFormContent(translationForms[lang.code])"
                        class="translation-indicator" color="primary">check_circle</mat-icon>
            </ng-template>
            <form [formGroup]="translationForms[lang.code]" class="notice-form">
                <div class="form-row">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>{{ 'admin.notices.title_field' | translate }}</mat-label>
                    <input matInput formControlName="title" required>
                    <mat-error *ngIf="translationForms[lang.code].get('title')?.hasError('required')">
                      {{ 'admin.common.field_required' | translate }}
                    </mat-error>
                    <mat-error *ngIf="translationForms[lang.code].get('title')?.hasError('maxlength')">
                      {{ 'admin.common.max_length' | translate: { length: 200 } }}
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>{{ 'admin.notices.content_field' | translate }}</mat-label>
                    <textarea matInput formControlName="content" rows="6" required></textarea>
                    <mat-error *ngIf="translationForms[lang.code].get('content')?.hasError('required')">
                      {{ 'admin.common.field_required' | translate }}
                    </mat-error>
                  </mat-form-field>
                </div>
              </form>
          </mat-tab>
        </mat-tab-group>
      </mat-card-content>
    </mat-card>

    <mat-card class="action-card">
      <mat-card-content>
        <div class="form-actions">
          <div class="action-message">
            <mat-icon color="primary">info</mat-icon>
            <span>{{ isEditMode ? 'admin.notices.edit_message' : 'admin.notices.create_message' | translate }}</span>
          </div>
          <div class="action-buttons">
            <button mat-button (click)="cancel()">
              {{ 'admin.common.cancel' | translate }}
            </button>
            <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="isSaving">
              <mat-icon>save</mat-icon>
              {{ isEditMode ? ('admin.common.update' | translate) : ('admin.common.create' | translate) }}
              <mat-spinner *ngIf="isSaving" diameter="20" class="spinner-button"></mat-spinner>
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
