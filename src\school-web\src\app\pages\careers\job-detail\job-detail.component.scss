// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Loading and Error States
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
  text-align: center;
  
  .loading-icon, .error-icon {
    font-size: 60px;
    height: 60px;
    width: 60px;
    margin-bottom: 20px;
    color: $primary-color;
  }
  
  h2 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: $text-color;
  }
  
  p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: $dark-gray;
    max-width: 600px;
  }
}

// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Job Header
.job-header {
  padding: 40px 0;
  
  .back-button {
    margin-bottom: 20px;
    
    mat-icon {
      margin-right: 5px;
    }
  }
  
  .job-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 20px;
    }
    
    .job-title-section {
      .job-title {
        font-size: 2.5rem;
        margin-bottom: 20px;
        color: $text-color;
      }
      
      .job-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        
        .meta-item {
          display: flex;
          align-items: center;
          color: $dark-gray;
          
          mat-icon {
            font-size: 20px;
            height: 20px;
            width: 20px;
            margin-right: 8px;
            color: $primary-color;
          }
        }
      }
    }
    
    .job-actions {
      display: flex;
      gap: 10px;
      
      @media (max-width: 576px) {
        flex-direction: column;
        width: 100%;
        
        a, button {
          width: 100%;
        }
      }
    }
  }
}

// Job Content
.job-content {
  padding: 40px 0;
  background-color: $light-gray;
  
  .content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    
    @media (max-width: 992px) {
      grid-template-columns: 1fr;
    }
    
    .main-content {
      .description-section, .responsibilities-section, .qualifications-section, .benefits-section, .application-process-section {
        background-color: $white;
        border-radius: $border-radius;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: $box-shadow;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        h2 {
          font-size: 1.8rem;
          margin-bottom: 20px;
          color: $text-color;
          position: relative;
          padding-bottom: 10px;
          
          &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: $primary-color;
          }
        }
        
        p {
          font-size: 1.1rem;
          line-height: 1.6;
          color: $text-color;
          margin-bottom: 20px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
        
        ul {
          padding-left: 20px;
          margin-bottom: 0;
          
          li {
            font-size: 1.1rem;
            line-height: 1.6;
            color: $text-color;
            margin-bottom: 10px;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
    
    .sidebar {
      .application-card, .contact-card, .share-card {
        background-color: $white;
        border-radius: $border-radius;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: $box-shadow;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        h3 {
          font-size: 1.3rem;
          margin-bottom: 20px;
          color: $text-color;
          position: relative;
          padding-bottom: 10px;
          
          &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 3px;
            background-color: $primary-color;
          }
        }
        
        h4 {
          font-size: 1.1rem;
          margin-bottom: 5px;
          color: $text-color;
        }
        
        p {
          font-size: 1rem;
          margin-bottom: 15px;
          color: $dark-gray;
        }
      }
      
      .application-card {
        .application-deadline, .start-date, .salary {
          margin-bottom: 20px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
        
        .deadline-status {
          display: flex;
          align-items: center;
          margin-top: 10px;
          
          mat-icon {
            font-size: 20px;
            height: 20px;
            width: 20px;
            margin-right: 8px;
            
            &:first-child {
              color: $accent-color;
            }
            
            &:last-child {
              color: $primary-color;
            }
          }
          
          span {
            font-weight: 500;
          }
        }
        
        .application-actions {
          margin-top: 20px;
          
          .apply-button {
            width: 100%;
          }
        }
      }
      
      .contact-card {
        p {
          margin-bottom: 20px;
        }
        
        a {
          display: flex;
          align-items: center;
          justify-content: center;
          
          mat-icon {
            margin-right: 8px;
          }
        }
      }
      
      .share-card {
        .share-buttons {
          display: flex;
          gap: 15px;
          
          a {
            transition: transform 0.3s;
            
            &:hover {
              transform: translateY(-3px);
            }
          }
        }
      }
    }
  }
}

// Call to Action
.cta-section {
  background-color: $primary-color;
  color: $white;
  padding: 60px 0;
  text-align: center;
  
  h2 {
    font-size: 2rem;
    margin-bottom: 20px;
  }
  
  p {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    
    @media (max-width: 576px) {
      flex-direction: column;
      align-items: center;
      
      a, button {
        width: 100%;
        max-width: 250px;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .job-header {
    padding: 30px 0;
    
    .job-header-content {
      .job-title-section {
        .job-title {
          font-size: 2rem;
        }
      }
    }
  }
  
  .job-content {
    padding: 30px 0;
    
    .main-content {
      .description-section, .responsibilities-section, .qualifications-section, .benefits-section, .application-process-section {
        padding: 25px;
        
        h2 {
          font-size: 1.6rem;
        }
        
        p, ul li {
          font-size: 1rem;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .job-header {
    .job-header-content {
      .job-title-section {
        .job-title {
          font-size: 1.8rem;
        }
        
        .job-meta {
          flex-direction: column;
          gap: 10px;
        }
      }
    }
  }
  
  .job-content {
    .main-content {
      .description-section, .responsibilities-section, .qualifications-section, .benefits-section, .application-process-section {
        padding: 20px;
        
        h2 {
          font-size: 1.5rem;
        }
      }
    }
  }
  
  .cta-section {
    padding: 40px 0;
    
    h2 {
      font-size: 1.8rem;
    }
    
    p {
      font-size: 1.1rem;
    }
  }
}
