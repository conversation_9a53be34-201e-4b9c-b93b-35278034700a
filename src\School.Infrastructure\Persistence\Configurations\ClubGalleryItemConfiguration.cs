using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ClubGalleryItemConfiguration : IEntityTypeConfiguration<ClubGalleryItem>
{
    public void Configure(EntityTypeBuilder<ClubGalleryItem> builder)
    {
        builder.HasKey(g => g.Id);
        
        builder.Property(g => g.ImageUrl)
            .HasMaxLength(255);
            
        builder.Property(g => g.Caption)
            .HasMaxLength(255);
            
        builder.Property(g => g.DisplayOrder)
            .HasDefaultValue(0);
    }
}
