using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Infrastructure.Identity;

namespace School.Infrastructure.Persistence.Configurations
{
    public class UserRefreshTokenConfiguration : IEntityTypeConfiguration<UserRefreshToken>
    {
        public void Configure(EntityTypeBuilder<UserRefreshToken> builder)
        {
            builder.ToTable("UserRefreshTokens");

            builder.HasKey(rt => rt.Id);

            builder.Property(rt => rt.UserId)
                .IsRequired()
                .HasMaxLength(450); // Standard Identity user ID length

            builder.Property(rt => rt.Token)
                .IsRequired()
                .HasMaxLength(500);

            builder.Property(rt => rt.ExpiryDate)
                .IsRequired();

            builder.Property(rt => rt.IsRevoked)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(rt => rt.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("NOW()");

            builder.Property(rt => rt.CreatedByIp)
                .HasMaxLength(45); // IPv6 max length

            builder.Property(rt => rt.RevokedByIp)
                .HasMaxLength(45);

            // Relationships
            builder.HasOne(rt => rt.User)
                .WithMany(u => u.RefreshTokens)
                .HasForeignKey(rt => rt.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(rt => rt.Token)
                .IsUnique()
                .HasDatabaseName("IX_UserRefreshTokens_Token");

            builder.HasIndex(rt => rt.UserId)
                .HasDatabaseName("IX_UserRefreshTokens_UserId");

            builder.HasIndex(rt => rt.ExpiryDate)
                .HasDatabaseName("IX_UserRefreshTokens_ExpiryDate");

            builder.HasIndex(rt => new { rt.UserId, rt.IsRevoked })
                .HasDatabaseName("IX_UserRefreshTokens_UserId_IsRevoked");
        }
    }
}
