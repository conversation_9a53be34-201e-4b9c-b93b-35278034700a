using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class FacultyPublicationConfiguration : IEntityTypeConfiguration<FacultyPublication>
{
    public void Configure(EntityTypeBuilder<FacultyPublication> builder)
    {
        builder.Property(t => t.Title)
            .HasMaxLength(300)
            .IsRequired();

        builder.Property(t => t.Journal)
            .HasMaxLength(200);

        builder.Property(t => t.Url)
            .HasMaxLength(500);

        builder.HasOne(t => t.Faculty)
            .WithMany(t => t.Publications)
            .HasForeignKey(t => t.FacultyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasQueryFilter(fp => !fp.IsDeleted);
    }
}
