import { Component, Input, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-portals-menu',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    TranslateModule
  ],
  templateUrl: './portals-menu.component.html',
  styleUrls: ['./portals-menu.component.scss']
})
export class PortalsMenuComponent {
  @Input() activeRoute: string = '';
  @ViewChild('portalsMenuTrigger') portalsMenuTrigger!: MatMenuTrigger;
  
  private closeTimeout: any;
  
  openMenu(trigger: MatMenuTrigger): void {
    if (this.closeTimeout) {
      clearTimeout(this.closeTimeout);
      this.closeTimeout = null;
    }
    trigger.openMenu();
  }
  
  closeMenu(): void {
    this.closeTimeout = setTimeout(() => {
      if (this.portalsMenuTrigger.menuOpen) {
        this.portalsMenuTrigger.closeMenu();
      }
    }, 100);
  }
  
  clearCloseTimeout(): void {
    if (this.closeTimeout) {
      clearTimeout(this.closeTimeout);
      this.closeTimeout = null;
    }
  }
}
