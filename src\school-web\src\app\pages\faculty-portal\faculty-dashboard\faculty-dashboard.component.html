<div class="dashboard-container">
  <h1 class="page-title">Faculty Dashboard</h1>

  <div *ngIf="loading.faculty" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error.faculty" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load faculty data. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadFacultyData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading.faculty && !error.faculty && faculty" class="dashboard-content">
    <!-- Faculty Info Card -->
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>Faculty Information</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">Name:</span>
            <span class="info-value">{{ faculty.name || (faculty.firstName + ' ' + faculty.lastName) }}</span>
          </div>
          <div class="info-item" *ngIf="faculty.employeeId">
            <span class="info-label">Employee ID:</span>
            <span class="info-value">{{ faculty.employeeId }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Title:</span>
            <span class="info-value">{{ faculty.title || faculty.designation }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Department:</span>
            <span class="info-value">{{ faculty.department }}</span>
          </div>
          <div class="info-item" *ngIf="faculty.isClassTeacher">
            <span class="info-label">Class Teacher:</span>
            <span class="info-value">Class {{ faculty.assignedGrade }}-{{ faculty.assignedSection }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Email:</span>
            <span class="info-value">{{ faculty.email }}</span>
          </div>
          <div class="info-item" *ngIf="faculty.office">
            <span class="info-label">Office:</span>
            <span class="info-value">{{ faculty.office }}</span>
          </div>

          <!-- Social Links -->
          <div class="info-item" *ngIf="faculty.website || faculty.linkedIn || faculty.twitter || faculty.researchGate">
            <span class="info-label">Social Links:</span>
            <span class="info-value social-links">
              <a *ngIf="faculty.website" [href]="faculty.website" target="_blank" class="social-link">
                <mat-icon>language</mat-icon>
              </a>
              <a *ngIf="faculty.linkedIn" [href]="faculty.linkedIn" target="_blank" class="social-link">
                <mat-icon>work</mat-icon>
              </a>
              <a *ngIf="faculty.twitter" [href]="faculty.twitter" target="_blank" class="social-link">
                <mat-icon>chat</mat-icon>
              </a>
              <a *ngIf="faculty.researchGate" [href]="faculty.researchGate" target="_blank" class="social-link">
                <mat-icon>school</mat-icon>
              </a>
            </span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Today's Schedule Card -->
    <mat-card class="schedule-card">
      <mat-card-header>
        <mat-card-title>Today's Schedule ({{ getCurrentDayName() }})</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="loading.schedule" class="loading-indicator">
          <mat-progress-bar mode="indeterminate"></mat-progress-bar>
        </div>

        <div *ngIf="error.schedule" class="error-message">
          <p>Failed to load schedule. <a (click)="loadTodaySchedule()">Retry</a></p>
        </div>

        <div *ngIf="!loading.schedule && !error.schedule">
          <div *ngIf="todaySchedule.length === 0" class="no-schedule">
            <p>No classes scheduled for today.</p>
          </div>

          <div *ngIf="todaySchedule.length > 0" class="schedule-list">
            <div *ngFor="let period of todaySchedule" class="schedule-item">
              <div class="period-number">{{ period.periodNumber }}</div>
              <div class="period-details">
                <div class="period-subject">{{ period.subjectName }}</div>
                <div class="period-class">Class {{ period.grade }}-{{ period.section }}</div>
                <div class="period-time">{{ period.startTime }} - {{ period.endTime }}</div>
                <div class="period-room">Room: {{ period.roomNumber }}</div>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" routerLink="/faculty-portal/schedule">View Full Schedule</button>
      </mat-card-actions>
    </mat-card>

    <!-- Pending Leave Applications Card -->
    <mat-card class="leaves-card">
      <mat-card-header>
        <mat-card-title>Pending Leave Applications</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="loading.leaves" class="loading-indicator">
          <mat-progress-bar mode="indeterminate"></mat-progress-bar>
        </div>

        <div *ngIf="error.leaves" class="error-message">
          <p>Failed to load leave applications. <a (click)="loadPendingLeaves()">Retry</a></p>
        </div>

        <div *ngIf="!loading.leaves && !error.leaves">
          <div *ngIf="pendingLeaves.length === 0" class="no-leaves">
            <p>No pending leave applications.</p>
          </div>

          <div *ngIf="pendingLeaves.length > 0" class="leaves-list">
            <mat-accordion>
              <mat-expansion-panel *ngFor="let leave of pendingLeaves">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    Student Name <!-- Will be populated from API in real implementation -->
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ getLeaveTypeLabel(leave.type) }} Leave ({{ calculateLeaveDays(leave.startDate, leave.endDate) }} days)
                  </mat-panel-description>
                </mat-expansion-panel-header>

                <div class="leave-details">
                  <div class="leave-dates">
                    <div class="leave-date">
                      <span class="leave-label">From:</span>
                      <span class="leave-value">{{ leave.startDate | date:'mediumDate' }}</span>
                    </div>
                    <div class="leave-date">
                      <span class="leave-label">To:</span>
                      <span class="leave-value">{{ leave.endDate | date:'mediumDate' }}</span>
                    </div>
                  </div>

                  <div class="leave-reason">
                    <span class="leave-label">Reason:</span>
                    <span class="leave-value">{{ leave.reason }}</span>
                  </div>

                  <div class="leave-attachment" *ngIf="leave.attachmentPath">
                    <span class="leave-label">Attachment:</span>
                    <a class="leave-value" [href]="leave.attachmentPath" target="_blank">View Attachment</a>
                  </div>

                  <div class="leave-applied">
                    <span class="leave-label">Applied On:</span>
                    <span class="leave-value">{{ leave.createdAt | date:'medium' }}</span>
                  </div>
                </div>

                <mat-action-row>
                  <button mat-button color="warn" (click)="rejectLeave(leave.id)">Reject</button>
                  <button mat-button color="primary" (click)="approveLeave(leave.id)">Approve</button>
                </mat-action-row>
              </mat-expansion-panel>
            </mat-accordion>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" routerLink="/faculty-portal/leaves">View All Leave Applications</button>
      </mat-card-actions>
    </mat-card>

    <!-- Quick Actions Card -->
    <mat-card class="actions-card">
      <mat-card-header>
        <mat-card-title>Quick Actions</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="actions-grid">
          <button mat-raised-button color="primary" routerLink="/faculty-portal/attendance">
            <mat-icon>event_available</mat-icon>
            Record Attendance
          </button>
          <button mat-raised-button color="primary" routerLink="/faculty-portal/results">
            <mat-icon>assessment</mat-icon>
            Enter Results
          </button>
          <button mat-raised-button color="primary" routerLink="/faculty-portal/classes">
            <mat-icon>class</mat-icon>
            View My Classes
          </button>
          <button mat-raised-button color="primary" routerLink="/faculty-portal/leaves">
            <mat-icon>event_busy</mat-icon>
            Manage Leave Applications
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
