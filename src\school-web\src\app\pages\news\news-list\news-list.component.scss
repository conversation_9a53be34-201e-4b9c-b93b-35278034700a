// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('/assets/images/news-hero.jpg');
  background-size: cover;
  background-position: center;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  margin-bottom: 2rem;

  .hero-content {
    max-width: 800px;
    padding: 0 20px;

    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    .hero-description {
      font-size: 1.5rem;
      font-weight: 300;
    }
  }
}

// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 60px;
}

// Filters Section
.filters-section {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
  align-items: center;
  
  .search-filter {
    flex-grow: 1;
    min-width: 300px;
    
    .search-field {
      width: 100%;
    }
  }
  
  .category-filter {
    width: 200px;
  }
  
  .reset-filter {
    button {
      height: 56px;
      
      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

// Results Count
.results-count {
  margin-bottom: 20px;
  
  p {
    color: $dark-gray;
    font-size: 0.9rem;
  }
}

// No Results
.no-results {
  text-align: center;
  padding: 60px 0;
  
  mat-icon {
    font-size: 60px;
    height: 60px;
    width: 60px;
    color: $medium-gray;
    margin-bottom: 20px;
  }
  
  h2 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: $text-color;
  }
  
  p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: $dark-gray;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }
}

// News Grid
.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
  
  .news-card {
    border-radius: $border-radius;
    overflow: hidden;
    box-shadow: $box-shadow;
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
    display: flex;
    flex-direction: column;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }
    
    .news-image {
      height: 200px;
      position: relative;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s;
        
        &:hover {
          transform: scale(1.05);
        }
      }
      
      .news-category {
        position: absolute;
        top: 15px;
        left: 15px;
        background-color: $primary-color;
        color: $white;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        text-transform: uppercase;
      }
    }
    
    mat-card-content {
      padding: 20px;
      flex-grow: 1;
      
      .news-meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        
        .news-date, .news-author {
          display: flex;
          align-items: center;
          color: $dark-gray;
          font-size: 0.8rem;
          
          mat-icon {
            font-size: 16px;
            height: 16px;
            width: 16px;
            margin-right: 5px;
          }
        }
      }
      
      .news-title {
        font-size: 1.5rem;
        line-height: 1.3;
        margin-bottom: 15px;
        color: $text-color;
      }
      
      .news-excerpt {
        color: $dark-gray;
        line-height: 1.6;
        margin-bottom: 20px;
        
        // Limit to 3 lines
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .news-tags {
        margin-bottom: 15px;
        
        mat-chip-set {
          mat-chip {
            font-size: 0.8rem;
          }
        }
      }
    }
    
    mat-card-actions {
      padding: 0 20px 20px;
      margin: 0;
    }
  }
}

// Pagination
.pagination {
  display: flex;
  justify-content: center;
}

// Responsive Adjustments
@media (max-width: 992px) {
  .hero-section {
    height: 250px;
    
    .hero-content {
      h1 {
        font-size: 2.5rem;
      }
      
      .hero-description {
        font-size: 1.2rem;
      }
    }
  }
  
  .filters-section {
    flex-direction: column;
    align-items: stretch;
    
    .search-filter, .category-filter {
      width: 100%;
    }
  }
}

@media (max-width: 576px) {
  .hero-section {
    height: 200px;
    
    .hero-content {
      h1 {
        font-size: 2rem;
      }
      
      .hero-description {
        font-size: 1rem;
      }
    }
  }
}
