/**
 * <PERSON><PERSON><PERSON> to check for unused translation keys
 * 
 * This script scans the codebase for translation key usage and identifies keys
 * in the translation files that are not used in the application.
 * 
 * Usage: 
 * 1. Place this file in the i18n directory
 * 2. Run with Node.js: node check-unused-translations.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Define paths
const basePath = __dirname;
const srcPath = path.join(basePath, '..', '..', '..');
const enPath = path.join(basePath, 'en');

// Function to read JSON file
function readJsonFile(filePath) {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    return null;
  }
}

// Function to get all translation keys from a JSON object
function getAllKeys(obj, prefix = '') {
  let keys = [];
  
  for (const key in obj) {
    const currentKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      // Recursively get keys from nested objects
      keys = keys.concat(getAllKeys(obj[key], currentKey));
    } else {
      keys.push(currentKey);
    }
  }
  
  return keys;
}

// Function to search for a key in the codebase
function searchForKey(key) {
  try {
    // Escape special characters for grep
    const escapedKey = key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    
    // Use grep to search for the key in all HTML and TS files
    const result = execSync(`grep -r "${escapedKey}" ${srcPath} --include="*.html" --include="*.ts" | wc -l`, { encoding: 'utf8' });
    
    return parseInt(result.trim()) > 0;
  } catch (error) {
    // If grep returns non-zero (no matches), it will throw an error
    return false;
  }
}

// Main function
function main() {
  console.log('Checking for unused translation keys...');
  
  // Get all English translation files
  const enFiles = fs.readdirSync(enPath).filter(file => file.endsWith('.json'));
  
  let totalKeys = 0;
  let unusedKeys = 0;
  
  // Process each translation file
  for (const file of enFiles) {
    const filePath = path.join(enPath, file);
    const translations = readJsonFile(filePath);
    
    if (!translations) {
      continue;
    }
    
    // Get all keys from the translation file
    const keys = getAllKeys(translations);
    totalKeys += keys.length;
    
    console.log(`\nChecking ${file} (${keys.length} keys)...`);
    
    // Check each key for usage
    const fileUnusedKeys = [];
    for (const key of keys) {
      const isUsed = searchForKey(key);
      
      if (!isUsed) {
        fileUnusedKeys.push(key);
        unusedKeys++;
      }
    }
    
    // Report unused keys for this file
    if (fileUnusedKeys.length > 0) {
      console.log(`Found ${fileUnusedKeys.length} unused keys in ${file}:`);
      fileUnusedKeys.forEach(key => console.log(`  - ${key}`));
    } else {
      console.log(`No unused keys found in ${file}`);
    }
  }
  
  // Summary
  console.log(`\nSummary:`);
  console.log(`Total keys: ${totalKeys}`);
  console.log(`Unused keys: ${unusedKeys} (${Math.round(unusedKeys / totalKeys * 100)}%)`);
}

// Run the main function
main();
