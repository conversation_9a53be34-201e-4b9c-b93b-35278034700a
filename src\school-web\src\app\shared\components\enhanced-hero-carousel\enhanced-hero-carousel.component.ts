import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

export interface EnhancedCarouselSlide {
  image: string;
  title: string;
  subtitle: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
  secondaryButtonText?: string;
  secondaryButtonLink?: string;
}

@Component({
  selector: 'app-enhanced-hero-carousel',
  templateUrl: './enhanced-hero-carousel.component.html',
  styleUrls: ['./enhanced-hero-carousel.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    RouterModule,
    TranslateModule
  ]
})
export class EnhancedHeroCarouselComponent implements OnInit, OnDestroy {
  @Input() slides: EnhancedCarouselSlide[] = [];
  @Input() autoPlay: boolean = true;
  @Input() interval: number = 5000;
  @Input() showControls: boolean = true;
  @Input() showIndicators: boolean = true;
  @Input() animationDuration: number = 800;
  @Input() theme: 'light' | 'dark' = 'dark';
  @Input() size: 'small' | 'medium' | 'large' = 'large';
  @Input() overlayOpacity: number = 0.5;

  currentSlideIndex: number = 0;
  private intervalId: any;

  // Default slide if none provided
  private defaultSlide: EnhancedCarouselSlide = {
    image: '',
    title: 'Welcome to Our School',
    subtitle: 'Providing quality education for a brighter future',
    description: 'Join our community of learners and discover your potential in an environment that nurtures excellence.'
  };

  constructor() { }

  ngOnInit(): void {
    // Initialize with default slide if no slides provided
    if (!this.slides || this.slides.length === 0) {
      this.slides = [this.defaultSlide];
    }

    if (this.autoPlay && this.slides.length > 1) {
      this.startSlideShow();
    }
  }

  ngOnDestroy(): void {
    this.stopSlideShow();
  }

  startSlideShow(): void {
    this.stopSlideShow(); // Clear any existing interval
    this.intervalId = setInterval(() => {
      this.nextSlide();
    }, this.interval);
  }

  stopSlideShow(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  resetInterval(): void {
    if (this.autoPlay) {
      this.stopSlideShow();
      this.startSlideShow();
    }
  }

  nextSlide(): void {
    if (this.slides.length > 1) {
      this.currentSlideIndex = (this.currentSlideIndex + 1) % this.slides.length;
      this.resetInterval();
    }
  }

  prevSlide(): void {
    if (this.slides.length > 1) {
      this.currentSlideIndex = (this.currentSlideIndex - 1 + this.slides.length) % this.slides.length;
      this.resetInterval();
    }
  }

  setCurrentSlide(index: number): void {
    if (index >= 0 && index < this.slides.length) {
      this.currentSlideIndex = index;
      this.resetInterval();
    }
  }

  get themeClass(): string {
    return `theme-${this.theme}`;
  }

  get sizeClass(): string {
    return `size-${this.size}`;
  }

  // Helper methods to determine text length classes
  getTitleClass(text: string | undefined): string {
    if (!text) return '';

    if (text.length > 80) {
      return 'very-long-text';
    } else if (text.length > 40) {
      return 'long-text';
    }
    return '';
  }

  getSubtitleClass(text: string | undefined): string {
    if (!text) return '';

    if (text.length > 100) {
      return 'very-long-text';
    } else if (text.length > 60) {
      return 'long-text';
    }
    return '';
  }

  getDescriptionClass(text: string | undefined): string {
    if (!text) return '';

    if (text.length > 300) {
      return 'very-long-text';
    } else if (text.length > 150) {
      return 'long-text';
    }
    return '';
  }
}
