using School.Domain.Common;

namespace School.Domain.Entities;

public class FacultyTranslation : BaseEntity
{
    public Guid FacultyId { get; set; }
    public Faculty? Faculty { get; set; }

    public string LanguageCode { get; set; } = string.Empty; // en, bn
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string ShortBio { get; set; } = string.Empty;
}
