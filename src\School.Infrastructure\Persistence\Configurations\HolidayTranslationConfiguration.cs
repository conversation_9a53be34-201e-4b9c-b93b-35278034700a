using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class HolidayTranslationConfiguration : IEntityTypeConfiguration<HolidayTranslation>
{
    public void Configure(EntityTypeBuilder<HolidayTranslation> builder)
    {
        builder.ToTable("HolidayTranslations");

        builder.HasKey(ht => ht.Id);

        builder.Property(ht => ht.Id)
            .ValueGeneratedOnAdd();

        builder.Property(ht => ht.HolidayId)
            .IsRequired();

        builder.Property(ht => ht.LanguageCode)
            .IsRequired()
            .HasMaxLength(10); // e.g., "en-US", "ar-SA", "fr-FR"

        builder.Property(ht => ht.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(ht => ht.Description)
            .HasMaxLength(1000);

        builder.Property(ht => ht.Remarks)
            .HasMaxLength(1000);

        // Audit fields
        builder.Property(ht => ht.CreatedAt)
            .IsRequired();

        builder.Property(ht => ht.CreatedBy)
            .HasMaxLength(450);

        builder.Property(ht => ht.LastModifiedAt);

        builder.Property(ht => ht.LastModifiedBy)
            .HasMaxLength(450);

        // Relationships
        builder.HasOne(ht => ht.Holiday)
            .WithMany(h => h.Translations)
            .HasForeignKey(ht => ht.HolidayId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(ht => ht.HolidayId)
            .HasDatabaseName("IX_HolidayTranslations_HolidayId");

        builder.HasIndex(ht => ht.LanguageCode)
            .HasDatabaseName("IX_HolidayTranslations_LanguageCode");

        // Unique constraint to prevent duplicate translations for the same holiday and language
        builder.HasIndex(ht => new { ht.HolidayId, ht.LanguageCode })
            .IsUnique()
            .HasDatabaseName("IX_HolidayTranslations_Holiday_Language_Unique");
    }
}
