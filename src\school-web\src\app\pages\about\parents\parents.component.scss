@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('/assets/images/parents-hero.jpg');
  background-size: cover;
  background-position: center;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  margin-bottom: 2rem;

  .hero-content {
    max-width: 800px;
    padding: 0 20px;

    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    .hero-description {
      font-size: 1.5rem;
      font-weight: 300;
    }
  }
}

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Section Styles
section {
  padding: $section-padding;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: $dark-gray;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Introduction Section
.intro-section {
  background-color: $white;

  .intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Parent Involvement Section
.involvement-section {
  background-color: $light-gray;

  .involvement-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .involvement-card {
      border-radius: $border-radius;
      padding: 30px;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .card-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 70px;
        height: 70px;
        background-color: $primary-color;
        border-radius: 50%;
        margin-bottom: 20px;

        mat-icon {
          font-size: 35px;
          height: 35px;
          width: 35px;
          color: $white;
        }
      }

      mat-card-content {
        flex-grow: 1;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 15px;
          color: $text-color;
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }

      mat-card-actions {
        padding-top: 20px;

        a {
          display: flex;
          align-items: center;

          mat-icon {
            margin-left: 5px;
          }
        }
      }
    }
  }
}

// Parent Resources Section
.resources-section {
  background-color: $white;

  .resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .resource-card {
      border-radius: $border-radius;
      padding: 30px;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .card-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 70px;
        height: 70px;
        background-color: $accent-color;
        border-radius: 50%;
        margin-bottom: 20px;

        mat-icon {
          font-size: 35px;
          height: 35px;
          width: 35px;
          color: $white;
        }
      }

      mat-card-content {
        flex-grow: 1;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 15px;
          color: $text-color;
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }

      mat-card-actions {
        padding-top: 20px;

        a {
          display: flex;
          align-items: center;

          mat-icon {
            margin-left: 5px;
          }
        }
      }
    }
  }
}

// Parent Portal Section
.portal-section {
  background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -15%));
  color: $white;

  .portal-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h2 {
      color: $white;

      &:after {
        background-color: $white;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
    }

    .portal-features {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 40px;

      .feature-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: $border-radius;
        padding: 20px;
        transition: transform 0.3s, background-color 0.3s;

        &:hover {
          transform: translateY(-5px);
          background-color: rgba(255, 255, 255, 0.2);
        }

        mat-icon {
          font-size: 40px;
          height: 40px;
          width: 40px;
          margin-bottom: 15px;
        }

        span {
          text-align: center;
          font-weight: 500;
        }
      }
    }

    a {
      padding: 10px 30px;
      font-size: 1.1rem;
    }
  }
}

// FAQ Section
.faq-section {
  background-color: $light-gray;

  .faq-accordion {
    max-width: 800px;
    margin: 0 auto 30px;

    ::ng-deep .mat-expansion-panel {
      margin-bottom: 15px;
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ::ng-deep .mat-expansion-panel-header {
      padding: 20px;
    }

    ::ng-deep .mat-expansion-panel-header-title {
      color: $text-color;
      font-weight: 500;
    }

    ::ng-deep .mat-expansion-panel-body {
      padding: 0 24px 20px;

      p {
        color: $dark-gray;
        line-height: 1.6;
        margin-bottom: 0;
      }
    }
  }

  .more-questions {
    text-align: center;
    margin-top: 30px;

    p {
      margin-bottom: 15px;
      color: $text-color;
      font-size: 1.1rem;
    }
  }
}

// Parent Testimonials Section
.testimonials-section {
  background-color: $white;

  .testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .testimonial-card {
      border-radius: $border-radius;
      padding: 30px;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .quote-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        background-color: $light-gray;
        border-radius: 50%;
        margin-bottom: 20px;

        mat-icon {
          font-size: 25px;
          height: 25px;
          width: 25px;
          color: $primary-color;
        }
      }

      mat-card-content {
        .testimonial-quote {
          font-size: 1.1rem;
          line-height: 1.6;
          color: $text-color;
          font-style: italic;
          margin-bottom: 20px;
        }

        .testimonial-author {
          display: flex;
          align-items: center;

          .author-image {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 15px;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .author-name {
            color: $dark-gray;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// Call to Action Section
.cta-section {
  background: linear-gradient(135deg, $accent-color, color.adjust($accent-color, $lightness: -15%));
  color: $white;

  .cta-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h2 {
      color: $white;

      &:after {
        background-color: $white;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
    }

    .cta-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;

      a {
        padding: 10px 30px;
        font-size: 1.1rem;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .hero-section {
    height: 350px;

    .hero-content h1 {
      font-size: 2.5rem;
    }
  }

  section {
    padding: 60px 0;

    h2 {
      font-size: 2rem;
    }
  }

  .involvement-grid, .resources-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 300px;

    .hero-content {
      h1 {
        font-size: 2rem;
      }

      .hero-description {
        font-size: 1.2rem;
      }
    }
  }

  .portal-features {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .cta-section {
    .cta-buttons {
      flex-direction: column;
      align-items: center;

      a {
        width: 100%;
        max-width: 300px;
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .hero-section {
    height: 250px;

    .hero-content h1 {
      font-size: 1.8rem;
    }
  }

  section h2 {
    font-size: 1.8rem;
  }

  .involvement-grid, .resources-grid, .testimonials-grid {
    grid-template-columns: 1fr;
  }
}
