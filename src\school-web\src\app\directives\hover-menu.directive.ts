import { Directive, ElementRef, HostListener, Input, OnDestroy, OnInit } from '@angular/core';
import { MatMenu } from '@angular/material/menu';
import { Subject, takeUntil } from 'rxjs';

@Directive({
  selector: '[appHoverMenu]',
  standalone: true
})
export class HoverMenuDirective implements OnInit, OnDestroy {
  @Input() appHoverMenu!: MatMenu;
  
  private isOpen = false;
  private destroy$ = new Subject<void>();
  private timeoutId: any;

  constructor(private elementRef: ElementRef) {}

  ngOnInit(): void {
    // Add event listeners to the menu panel
    this.appHoverMenu.closed.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.isOpen = false;
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  @HostListener('mouseenter')
  onMouseEnter(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
    if (!this.isOpen) {
      this.isOpen = true;
      this.openMenu();
    }
  }

  @HostListener('mouseleave')
  onMouseLeave(): void {
    this.timeoutId = setTimeout(() => {
      if (this.isOpen) {
        this.closeMenu();
      }
    }, 300); // Delay to allow moving to the menu
  }

  private openMenu(): void {
    const menuTrigger = this.elementRef.nativeElement.querySelector('.menu-trigger');
    if (menuTrigger) {
      menuTrigger.click();
    }
  }

  private closeMenu(): void {
    this.isOpen = false;
    // Find and click the backdrop to close the menu
    const backdrop = document.querySelector('.cdk-overlay-backdrop');
    if (backdrop) {
      (backdrop as HTMLElement).click();
    }
  }
}
