import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../shared/components/default-hero/default-hero.component';

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './about.component.html',
  styleUrl: './about.component.scss'
})
export class AboutComponent implements OnInit {
  activeSection: string = '';
  showFloatingNav: boolean = false;

  // Leadership team data
  leadershipTeam = [
    {
      name: 'Dr. <PERSON>',
      title: 'Principal',
      image: 'https://via.placeholder.com/300x400?text=Dr.+<PERSON>+<PERSON>',
      bio: 'Dr<PERSON> <PERSON> has over 20 years of experience in education leadership. She holds a Ph.D. in Educational Administration and is committed to fostering a learning environment where every student can thrive.'
    },
    {
      name: 'Mr. Robert Chen',
      title: 'Vice Principal, Academics',
      image: 'https://via.placeholder.com/300x400?text=Mr.+Robert+Chen',
      bio: 'With a background in curriculum development, Mr. Chen oversees all academic programs. He works closely with department heads to ensure educational excellence across all grade levels.'
    },
    {
      name: 'Ms. Priya Patel',
      title: 'Vice Principal, Student Affairs',
      image: 'https://via.placeholder.com/300x400?text=Ms.+Priya+Patel',
      bio: 'Ms. Patel focuses on student development beyond academics. She manages extracurricular activities, counseling services, and student support programs to ensure a well-rounded educational experience.'
    },
    {
      name: 'Dr. James Wilson',
      title: 'Director of Admissions',
      image: 'https://via.placeholder.com/300x400?text=Dr.+James+Wilson',
      bio: 'Dr. Wilson leads our admissions process, ensuring that we maintain a diverse and talented student body. He also coordinates with the financial aid office to make our school accessible to deserving students.'
    }
  ];

  // Timeline events for school history
  historyTimeline = [
    {
      year: 1985,
      title: 'Foundation',
      description: 'Our school was founded with a vision to provide quality education that balances academic excellence with character development.'
    },
    {
      year: 1995,
      title: 'Campus Expansion',
      description: 'A major expansion added new facilities including a state-of-the-art science building and sports complex.'
    },
    {
      year: 2005,
      title: 'Technology Integration',
      description: 'Pioneered a comprehensive technology program, integrating digital learning tools across all grade levels.'
    },
    {
      year: 2010,
      title: 'International Recognition',
      description: 'Received international accreditation and established exchange programs with schools around the world.'
    },
    {
      year: 2018,
      title: 'Innovation Center',
      description: 'Opened a dedicated innovation center to foster creativity, entrepreneurship, and project-based learning.'
    },
    {
      year: 2023,
      title: 'Sustainability Initiative',
      description: 'Launched a comprehensive sustainability program, including solar power installation and a zero-waste campus initiative.'
    }
  ];

  constructor() { }

  ngOnInit(): void {
    // Initialize the component
  }

  @HostListener('window:scroll', [])
  onWindowScroll() {
    // Show floating nav after scrolling past the hero section
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
    this.showFloatingNav = scrollPosition > 300;

    // Determine active section based on scroll position
    this.updateActiveSection();
  }

  updateActiveSection(): void {
    const sections = ['history', 'mission', 'leadership'];

    for (const section of sections) {
      const element = document.getElementById(section);
      if (element) {
        const rect = element.getBoundingClientRect();
        if (rect.top <= 100 && rect.bottom >= 100) {
          this.activeSection = section;
          break;
        }
      }
    }
  }

  scrollToSection(sectionId: string): void {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }
}
