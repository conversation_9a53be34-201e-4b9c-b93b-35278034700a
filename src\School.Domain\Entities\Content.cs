using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class Content : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this content belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public ContentType Type { get; set; }
    public string MetaDescription { get; set; } = string.Empty;
    public bool IsPublished { get; set; } = false;
    public DateTime? PublishedAt { get; set; }

    // Hierarchical relationship
    public Guid? ParentId { get; set; }
    public Content? Parent { get; set; }
    public ICollection<Content> Children { get; set; } = new List<Content>();

    // Audit fields
    public Guid CreatedById { get; set; }
    public User? CreatedBy { get; set; }

    public Guid? LastModifiedById { get; set; }
    public User? LastModifiedBy { get; set; }

    // Related entities
    public ICollection<ContentTranslation> Translations { get; set; } = new List<ContentTranslation>();
    public ICollection<MediaItem> MediaItems { get; set; } = new List<MediaItem>();
}
