using School.Application.DTOs;

namespace School.Application.Features.AcademicYear;

public interface IAcademicYearService
{
    // Academic Year CRUD operations
    Task<(IEnumerable<AcademicYearDto> AcademicYears, int TotalCount)> GetAllAcademicYearsAsync(AcademicYearFilterDto filter);
    Task<AcademicYearDto?> GetAcademicYearByIdAsync(Guid id);
    Task<AcademicYearDto?> GetCurrentAcademicYearAsync();
    Task<Guid> CreateAcademicYearAsync(CreateAcademicYearDto academicYearDto);
    Task<bool> UpdateAcademicYearAsync(Guid id, UpdateAcademicYearDto academicYearDto);
    Task<bool> DeleteAcademicYearAsync(Guid id);
    
    // Academic Year status management
    Task<bool> SetCurrentAcademicYearAsync(Guid id);
    Task<bool> ActivateAcademicYearAsync(Guid id);
    Task<bool> CompleteAcademicYearAsync(Guid id);
    Task<bool> ArchiveAcademicYearAsync(Guid id);
    
    // Academic Year validation
    Task<bool> ValidateAcademicYearDatesAsync(DateTime startDate, DateTime endDate, Guid? excludeId = null);
    Task<bool> CanDeleteAcademicYearAsync(Guid id);
    
    // Translation management
    Task<bool> AddTranslationAsync(Guid academicYearId, CreateAcademicYearTranslationDto translationDto);
    Task<bool> UpdateTranslationAsync(Guid academicYearId, string languageCode, UpdateAcademicYearTranslationDto translationDto);
    Task<bool> DeleteTranslationAsync(Guid academicYearId, string languageCode);
    Task<IEnumerable<AcademicYearTranslationDto>> GetTranslationsAsync(Guid academicYearId);
    
    // Academic Year statistics
    Task<int> GetTotalStudentsInAcademicYearAsync(Guid academicYearId);
    Task<int> GetTotalTermsInAcademicYearAsync(Guid academicYearId);
    Task<Dictionary<string, int>> GetAcademicYearStatisticsAsync(Guid academicYearId);
}
