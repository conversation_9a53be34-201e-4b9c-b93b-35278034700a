using School.Application.DTOs.Common;
using School.Domain.Enums;

namespace School.Application.DTOs;

public class EventDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public TimeSpan? StartTime { get; set; }
    public TimeSpan? EndTime { get; set; }
    public string Location { get; set; } = string.Empty;
    public string Organizer { get; set; } = string.Empty;
    public string ContactInfo { get; set; } = string.Empty;
    public EventType Type { get; set; }
    public EventStatus Status { get; set; }
    public bool IsAllDay { get; set; }
    public bool IsRecurring { get; set; }
    public RecurrenceType? RecurrenceType { get; set; }
    public int? RecurrenceInterval { get; set; }
    public DateTime? RecurrenceEndDate { get; set; }
    public bool IsPublic { get; set; }
    public bool IsActive { get; set; }
    public int? MaxAttendees { get; set; }
    public bool RequiresRegistration { get; set; }
    public int RegistrationsCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public List<EventTranslationDto> Translations { get; set; } = new List<EventTranslationDto>();
    public List<EventImageDto> Images { get; set; } = new List<EventImageDto>();
}

public class EventTranslationDto
{
    public Guid Id { get; set; }
    public Guid EventId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Organizer { get; set; }
}

public class EventImageDto
{
    public Guid Id { get; set; }
    public Guid EventId { get; set; }
    public Guid MediaItemId { get; set; }
    public int DisplayOrder { get; set; }
    public string ImagePath { get; set; } = string.Empty;
    public string Caption { get; set; } = string.Empty;
    public bool IsMain { get; set; }
}

public class EventRegistrationDto
{
    public Guid Id { get; set; }
    public Guid EventId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string? StudentId { get; set; }
    public string? AdditionalInfo { get; set; }
    public RegistrationStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
}

public class CreateEventDto
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public TimeSpan? StartTime { get; set; }
    public TimeSpan? EndTime { get; set; }
    public string Location { get; set; } = string.Empty;
    public string Organizer { get; set; } = string.Empty;
    public string ContactInfo { get; set; } = string.Empty;
    public EventType Type { get; set; }
    public EventStatus Status { get; set; } = EventStatus.Scheduled;
    public bool IsAllDay { get; set; }
    public bool IsRecurring { get; set; }
    public RecurrenceType? RecurrenceType { get; set; }
    public int? RecurrenceInterval { get; set; }
    public DateTime? RecurrenceEndDate { get; set; }
    public bool IsPublic { get; set; } = true;
    public bool IsActive { get; set; } = true;
    public int? MaxAttendees { get; set; }
    public bool RequiresRegistration { get; set; }
    public List<Guid> ImageIds { get; set; } = new List<Guid>();
    public List<CreateEventTranslationDto>? Translations { get; set; }
}

public class UpdateEventDto
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public TimeSpan? StartTime { get; set; }
    public TimeSpan? EndTime { get; set; }
    public string Location { get; set; } = string.Empty;
    public string Organizer { get; set; } = string.Empty;
    public string ContactInfo { get; set; } = string.Empty;
    public EventType Type { get; set; }
    public EventStatus Status { get; set; }
    public bool IsAllDay { get; set; }
    public bool IsRecurring { get; set; }
    public RecurrenceType? RecurrenceType { get; set; }
    public int? RecurrenceInterval { get; set; }
    public DateTime? RecurrenceEndDate { get; set; }
    public bool IsPublic { get; set; }
    public bool IsActive { get; set; }
    public int? MaxAttendees { get; set; }
    public bool RequiresRegistration { get; set; }
    public List<Guid> ImageIds { get; set; } = new List<Guid>();
    public List<UpdateEventTranslationDto>? Translations { get; set; }
}

public class CreateEventTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Organizer { get; set; }
}

public class UpdateEventTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Organizer { get; set; }
}

public class CreateEventRegistrationDto
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string? StudentId { get; set; }
    public string? AdditionalInfo { get; set; }
}

public class UpdateEventRegistrationDto
{
    public RegistrationStatus Status { get; set; }
}

public class EventCalendarDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public DateTime Start { get; set; }
    public DateTime? End { get; set; }
    public bool AllDay { get; set; }
    public string Location { get; set; } = string.Empty;
    public EventType Type { get; set; }
    public EventStatus Status { get; set; }
    public string Color { get; set; } = string.Empty;
}

/// <summary>
/// Filter DTO for event queries
/// </summary>
public class EventFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filter by event name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Filter by event type
    /// </summary>
    public EventType? Type { get; set; }

    /// <summary>
    /// Filter by event status
    /// </summary>
    public EventStatus? Status { get; set; }

    /// <summary>
    /// Filter by start date (from)
    /// </summary>
    public DateTime? StartDateFrom { get; set; }

    /// <summary>
    /// Filter by start date (to)
    /// </summary>
    public DateTime? StartDateTo { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter by public status
    /// </summary>
    public bool? IsPublic { get; set; }
}
