using System;
using System.ComponentModel.DataAnnotations;

namespace School.Application.DTOs
{
    public class ClubLeaderUpdateDto
    {
        public Guid id { get; set; }
        
        public int? StudentId { get; set; }
        
        [StringLength(100)]
        public string Name { get; set; }
        
        [StringLength(50)]
        public string Role { get; set; }
        
        [StringLength(20)]
        public string Grade { get; set; }
        
        public int? ProfileImageId { get; set; }
        
        [Url]
        public string ProfileImageUrl { get; set; }
        
        public int? DisplayOrder { get; set; }
    }
}
