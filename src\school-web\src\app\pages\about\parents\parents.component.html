<!-- Hero Section -->
<section class="hero-section">
  <div class="hero-content">
    <h1>{{ 'ABOUT.PARENTS_TITLE' | translate }}</h1>
    <p class="hero-description">{{ 'ABOUT.PARENTS_SUBTITLE' | translate }}</p>
  </div>
</section>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'ABOUT.PARENTS_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'ABOUT.PARENTS_INTRO_P1' | translate }}</p>
      <p>{{ 'ABOUT.PARENTS_INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Parent Involvement Section -->
  <section class="involvement-section">
    <h2>{{ 'ABOUT.PARENT_INVOLVEMENT' | translate }}</h2>
    <p class="section-intro">{{ 'ABOUT.PARENT_INVOLVEMENT_INTRO' | translate }}</p>
    
    <div class="involvement-grid">
      <mat-card class="involvement-card" *ngFor="let opportunity of involvementOpportunities">
        <div class="card-icon">
          <mat-icon>{{opportunity.icon}}</mat-icon>
        </div>
        <mat-card-content>
          <h3>{{opportunity.title}}</h3>
          <p>{{opportunity.description}}</p>
        </mat-card-content>
        <mat-card-actions>
          <a mat-button color="primary" [routerLink]="opportunity.link">
            {{ 'ABOUT.LEARN_MORE' | translate }}
            <mat-icon>arrow_forward</mat-icon>
          </a>
        </mat-card-actions>
      </mat-card>
    </div>
  </section>

  <!-- Parent Resources Section -->
  <section class="resources-section">
    <h2>{{ 'ABOUT.PARENT_RESOURCES' | translate }}</h2>
    <p class="section-intro">{{ 'ABOUT.PARENT_RESOURCES_INTRO' | translate }}</p>
    
    <div class="resources-grid">
      <mat-card class="resource-card" *ngFor="let resource of parentResources">
        <div class="card-icon">
          <mat-icon>{{resource.icon}}</mat-icon>
        </div>
        <mat-card-content>
          <h3>{{resource.title}}</h3>
          <p>{{resource.description}}</p>
        </mat-card-content>
        <mat-card-actions>
          <a mat-button color="primary" [routerLink]="resource.link">
            {{ 'ABOUT.ACCESS_RESOURCE' | translate }}
            <mat-icon>arrow_forward</mat-icon>
          </a>
        </mat-card-actions>
      </mat-card>
    </div>
  </section>

  <!-- Parent Portal Section -->
  <section class="portal-section">
    <div class="portal-content">
      <h2>{{ 'ABOUT.PARENT_PORTAL' | translate }}</h2>
      <p>{{ 'ABOUT.PARENT_PORTAL_DESCRIPTION' | translate }}</p>
      <div class="portal-features">
        <div class="feature-item">
          <mat-icon>grade</mat-icon>
          <span>{{ 'ABOUT.GRADES_ASSIGNMENTS' | translate }}</span>
        </div>
        <div class="feature-item">
          <mat-icon>event_note</mat-icon>
          <span>{{ 'ABOUT.ATTENDANCE_RECORDS' | translate }}</span>
        </div>
        <div class="feature-item">
          <mat-icon>email</mat-icon>
          <span>{{ 'ABOUT.TEACHER_COMMUNICATION' | translate }}</span>
        </div>
        <div class="feature-item">
          <mat-icon>payments</mat-icon>
          <span>{{ 'ABOUT.ONLINE_PAYMENTS' | translate }}</span>
        </div>
        <div class="feature-item">
          <mat-icon>description</mat-icon>
          <span>{{ 'ABOUT.SCHOOL_FORMS' | translate }}</span>
        </div>
        <div class="feature-item">
          <mat-icon>calendar_today</mat-icon>
          <span>{{ 'ABOUT.CALENDAR_EVENTS' | translate }}</span>
        </div>
      </div>
      <a mat-raised-button color="primary" routerLink="/parent-portal">
        {{ 'ABOUT.ACCESS_PORTAL' | translate }}
      </a>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="faq-section">
    <h2>{{ 'ABOUT.FREQUENTLY_ASKED_QUESTIONS' | translate }}</h2>
    <p class="section-intro">{{ 'ABOUT.FAQ_INTRO' | translate }}</p>
    
    <mat-accordion class="faq-accordion">
      <mat-expansion-panel *ngFor="let faq of faqs">
        <mat-expansion-panel-header>
          <mat-panel-title>
            {{faq.question}}
          </mat-panel-title>
        </mat-expansion-panel-header>
        <p>{{faq.answer}}</p>
      </mat-expansion-panel>
    </mat-accordion>
    
    <div class="more-questions">
      <p>{{ 'ABOUT.MORE_QUESTIONS' | translate }}</p>
      <a mat-button color="primary" routerLink="/contact">
        {{ 'ABOUT.CONTACT_US' | translate }}
      </a>
    </div>
  </section>

  <!-- Parent Testimonials Section -->
  <section class="testimonials-section">
    <h2>{{ 'ABOUT.PARENT_TESTIMONIALS' | translate }}</h2>
    <p class="section-intro">{{ 'ABOUT.TESTIMONIALS_INTRO' | translate }}</p>
    
    <div class="testimonials-grid">
      <mat-card class="testimonial-card" *ngFor="let testimonial of testimonials">
        <div class="quote-icon">
          <mat-icon>format_quote</mat-icon>
        </div>
        <mat-card-content>
          <p class="testimonial-quote">{{testimonial.quote}}</p>
          <div class="testimonial-author">
            <div class="author-image">
              <img [src]="testimonial.image" [alt]="testimonial.parent">
            </div>
            <div class="author-name">{{testimonial.parent}}</div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Call to Action Section -->
  <section class="cta-section">
    <div class="cta-content">
      <h2>{{ 'ABOUT.GET_INVOLVED' | translate }}</h2>
      <p>{{ 'ABOUT.GET_INVOLVED_TEXT' | translate }}</p>
      <div class="cta-buttons">
        <a mat-raised-button color="primary" routerLink="/parent-portal/volunteer">
          {{ 'ABOUT.VOLUNTEER' | translate }}
        </a>
        <a mat-stroked-button color="primary" routerLink="/contact">
          {{ 'ABOUT.CONTACT_US' | translate }}
        </a>
      </div>
    </div>
  </section>
</div>
