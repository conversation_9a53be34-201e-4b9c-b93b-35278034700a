using School.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace School.Application.Features.AcademicCalendar
{
    public interface IAcademicCalendarService
    {
        // Academic Calendar CRUD operations
        Task<(IEnumerable<AcademicCalendarDto> AcademicCalendars, int TotalCount)> GetAllAcademicCalendarsAsync(AcademicCalendarFilterDto filter);
        Task<AcademicCalendarDto?> GetAcademicCalendarByIdAsync(Guid id);
        Task<Guid> CreateAcademicCalendarAsync(CreateAcademicCalendarDto academicCalendarDto);
        Task<bool> UpdateAcademicCalendarAsync(Guid id, UpdateAcademicCalendarDto academicCalendarDto);
        Task<bool> DeleteAcademicCalendarAsync(Guid id);

        // Enhanced Calendar Events (with Holiday integration)
        Task<IEnumerable<AcademicCalendarEventDto>> GetCalendarEventsAsync(int academicYear = 0, string? semester = null);
        Task<IEnumerable<CalendarEventDto>> GetIntegratedCalendarEventsAsync(DateTime startDate, DateTime endDate, Guid? academicYearId = null, Guid? termId = null);
        Task<IEnumerable<CalendarEventDto>> GetAcademicYearCalendarAsync(Guid academicYearId);
        Task<IEnumerable<CalendarEventDto>> GetTermCalendarAsync(Guid termId);
        Task<IEnumerable<CalendarEventDto>> GetCurrentAcademicYearCalendarAsync();

        // Calendar Statistics and Analytics
        Task<CalendarStatisticsDto> GetCalendarStatisticsAsync(Guid? academicYearId = null, Guid? termId = null);
        Task<int> GetTotalEventsInPeriodAsync(DateTime startDate, DateTime endDate, Guid? academicYearId = null, Guid? termId = null);
        Task<Dictionary<string, int>> GetEventTypeDistributionAsync(Guid? academicYearId = null, Guid? termId = null);

        // Calendar Validation
        Task<bool> ValidateEventDatesAsync(Guid? academicYearId, Guid? termId, DateTime startDate, DateTime endDate, Guid? excludeId = null);
        Task<IEnumerable<AcademicCalendarDto>> GetOverlappingEventsAsync(Guid? academicYearId, Guid? termId, DateTime startDate, DateTime endDate, Guid? excludeId = null);

        // Translation methods
        Task<bool> AddTranslationAsync(Guid academicCalendarId, CreateAcademicCalendarTranslationDto translationDto);
        Task<bool> UpdateTranslationAsync(Guid academicCalendarId, string languageCode, UpdateAcademicCalendarTranslationDto translationDto);
        Task<bool> DeleteTranslationAsync(Guid academicCalendarId, string languageCode);
        Task<IEnumerable<AcademicCalendarTranslationDto>> GetTranslationsAsync(Guid academicCalendarId);
    }
}
