<div class="class-teachers-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>
        <mat-toolbar color="primary">
          <span>{{ 'CLASS_TEACHERS.TITLE' | translate }}</span>
          <span class="spacer"></span>
          <button mat-raised-button color="accent" (click)="openAssignDialog()">
            <mat-icon>person_add</mat-icon>
            {{ 'CLASS_TEACHERS.ASSIGN_TEACHER' | translate }}
          </button>
        </mat-toolbar>
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <!-- Search and Filter Section -->
      <div class="filter-section">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>{{ 'COMMON.SEARCH' | translate }}</mat-label>
          <input matInput 
                 [(ngModel)]="searchTerm" 
                 (keyup.enter)="onSearch()"
                 placeholder="{{ 'CLASS_TEACHERS.SEARCH_PLACEHOLDER' | translate }}">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>{{ 'CLASS_TEACHERS.GRADE' | translate }}</mat-label>
          <mat-select [(ngModel)]="selectedGrade" (selectionChange)="onGradeChange()">
            <mat-option value="">{{ 'COMMON.ALL' | translate }}</mat-option>
            <mat-option *ngFor="let grade of grades" [value]="grade.id">
              {{ grade.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>{{ 'CLASS_TEACHERS.SECTION' | translate }}</mat-label>
          <mat-select [(ngModel)]="selectedSection" (selectionChange)="onSearch()">
            <mat-option value="">{{ 'COMMON.ALL' | translate }}</mat-option>
            <mat-option *ngFor="let section of sections" [value]="section.id">
              {{ section.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>{{ 'CLASS_TEACHERS.ACADEMIC_YEAR' | translate }}</mat-label>
          <mat-select [(ngModel)]="selectedAcademicYear" (selectionChange)="onSearch()">
            <mat-option value="">{{ 'COMMON.ALL' | translate }}</mat-option>
            <mat-option *ngFor="let year of academicYears" [value]="year.id">
              {{ year.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>{{ 'CLASS_TEACHERS.STATUS' | translate }}</mat-label>
          <mat-select [(ngModel)]="selectedStatus" (selectionChange)="onSearch()">
            <mat-option value="">{{ 'COMMON.ALL' | translate }}</mat-option>
            <mat-option *ngFor="let status of statusOptions" [value]="status.value">
              {{ status.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-raised-button color="primary" (click)="onSearch()">
          <mat-icon>search</mat-icon>
          {{ 'COMMON.SEARCH' | translate }}
        </button>

        <button mat-button (click)="clearFilters()">
          <mat-icon>clear</mat-icon>
          {{ 'COMMON.CLEAR' | translate }}
        </button>
      </div>

      <!-- Data Table -->
      <div class="table-container">
        <table mat-table [dataSource]="classTeachers" class="class-teachers-table" matSort>
          <!-- Faculty Column -->
          <ng-container matColumnDef="faculty">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'CLASS_TEACHERS.FACULTY' | translate }}
            </th>
            <td mat-cell *matCellDef="let teacher">
              <div class="faculty-info">
                <span class="faculty-name">{{ teacher.facultyName }}</span>
                <small class="faculty-email">{{ teacher.facultyEmail }}</small>
              </div>
            </td>
          </ng-container>

          <!-- Section Column -->
          <ng-container matColumnDef="section">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'CLASS_TEACHERS.SECTION' | translate }}
            </th>
            <td mat-cell *matCellDef="let teacher">
              <div class="section-info">
                <span class="section-name">{{ teacher.sectionName }}</span>
                <mat-chip class="section-code">{{ teacher.sectionCode }}</mat-chip>
              </div>
            </td>
          </ng-container>

          <!-- Grade Column -->
          <ng-container matColumnDef="grade">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'CLASS_TEACHERS.GRADE' | translate }}
            </th>
            <td mat-cell *matCellDef="let teacher">
              <span class="grade-name">{{ teacher.gradeName }}</span>
            </td>
          </ng-container>

          <!-- Workload Column -->
          <ng-container matColumnDef="workload">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'CLASS_TEACHERS.WORKLOAD' | translate }}
            </th>
            <td mat-cell *matCellDef="let teacher">
              <div class="workload-info">
                <mat-chip [color]="getWorkloadColor(teacher.workloadPercentage)">
                  {{ teacher.workloadPercentage }}%
                </mat-chip>
                <small class="student-count" 
                       matTooltip="{{ 'CLASS_TEACHERS.STUDENT_COUNT' | translate }}">
                  <mat-icon>people</mat-icon>
                  {{ teacher.studentCount }}
                </small>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'COMMON.STATUS' | translate }}
            </th>
            <td mat-cell *matCellDef="let teacher">
              <div class="status-info">
                <mat-chip [color]="getStatusColor(teacher.status)">
                  {{ teacher.status }}
                </mat-chip>
                <mat-slide-toggle 
                  [checked]="teacher.isActive"
                  (change)="toggleStatus(teacher)"
                  [color]="'primary'"
                  matTooltip="{{ 'CLASS_TEACHERS.TOGGLE_STATUS' | translate }}">
                </mat-slide-toggle>
              </div>
            </td>
          </ng-container>

          <!-- Start Date Column -->
          <ng-container matColumnDef="startDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'CLASS_TEACHERS.START_DATE' | translate }}
            </th>
            <td mat-cell *matCellDef="let teacher">
              <span class="start-date">{{ teacher.startDate | date:'shortDate' }}</span>
            </td>
          </ng-container>

          <!-- Academic Year Column -->
          <ng-container matColumnDef="academicYear">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'CLASS_TEACHERS.ACADEMIC_YEAR' | translate }}
            </th>
            <td mat-cell *matCellDef="let teacher">
              <span class="academic-year">{{ teacher.academicYearName }}</span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'COMMON.ACTIONS' | translate }}
            </th>
            <td mat-cell *matCellDef="let teacher">
              <div class="action-buttons">
                <button mat-icon-button 
                        color="primary" 
                        (click)="openReassignDialog(teacher)"
                        matTooltip="{{ 'CLASS_TEACHERS.REASSIGN' | translate }}">
                  <mat-icon>swap_horiz</mat-icon>
                </button>
                <button mat-icon-button 
                        color="warn" 
                        (click)="removeClassTeacher(teacher)"
                        matTooltip="{{ 'CLASS_TEACHERS.REMOVE' | translate }}">
                  <mat-icon>person_remove</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- Loading Indicator -->
        <div *ngIf="loading" class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
          <p>{{ 'COMMON.LOADING' | translate }}</p>
        </div>

        <!-- No Data Message -->
        <div *ngIf="!loading && classTeachers.length === 0" class="no-data-container">
          <mat-icon class="no-data-icon">school</mat-icon>
          <h3>{{ 'CLASS_TEACHERS.NO_ASSIGNMENTS_FOUND' | translate }}</h3>
          <p>{{ 'CLASS_TEACHERS.NO_ASSIGNMENTS_MESSAGE' | translate }}</p>
          <button mat-raised-button color="primary" (click)="openAssignDialog()">
            <mat-icon>person_add</mat-icon>
            {{ 'CLASS_TEACHERS.ASSIGN_FIRST_TEACHER' | translate }}
          </button>
        </div>
      </div>

      <!-- Pagination -->
      <mat-paginator 
        [length]="totalCount"
        [pageSize]="pageSize"
        [pageSizeOptions]="[5, 10, 25, 50]"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
