using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class MediaItemConfiguration : IEntityTypeConfiguration<MediaItem>
{
    public void Configure(EntityTypeBuilder<MediaItem> builder)
    {
        builder.Property(t => t.FileName)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(t => t.OriginalFileName)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(t => t.FilePath)
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(t => t.MimeType)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(t => t.AltText)
            .HasMaxLength(255);

        builder.Property(t => t.Caption)
            .HasMaxLength(500);

        builder.HasOne(t => t.Content)
            .WithMany(t => t.MediaItems)
            .HasForeignKey(t => t.ContentId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(t => t.UploadedBy)
            .WithMany(t => t.UploadedMedia)
            .HasForeignKey(t => t.UploadedById)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasQueryFilter(m => !m.IsDeleted);
    }
}
