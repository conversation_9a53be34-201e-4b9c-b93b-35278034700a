using AutoMapper;
using School.Application.DTOs;
using School.Domain.Entities;

namespace School.Application.Common.Mappings;

public class ClubMappingProfile : Profile
{
    public ClubMappingProfile()
    {
        // Club mappings
        CreateMap<Club, ClubDto>();
        CreateMap<ClubCreateDto, Club>();
        
        // ClubTranslation mappings
        CreateMap<ClubTranslation, ClubTranslationDto>();
        CreateMap<ClubTranslationCreateDto, ClubTranslation>();
        
        // ClubAdvisor mappings
        CreateMap<ClubAdvisor, ClubAdvisorDto>();
        CreateMap<ClubAdvisorCreateDto, ClubAdvisor>();
        
        // ClubLeader mappings
        CreateMap<ClubLeader, ClubLeaderDto>();
        CreateMap<ClubLeaderCreateDto, ClubLeader>();
        
        // ClubActivity mappings
        CreateMap<ClubActivity, ClubActivityDto>();
        CreateMap<ClubActivityCreateDto, ClubActivity>();
        
        // ClubActivityTranslation mappings
        CreateMap<ClubActivityTranslation, ClubActivityTranslationDto>();
        CreateMap<ClubActivityTranslationCreateDto, ClubActivityTranslation>();
        
        // ClubAchievement mappings
        CreateMap<ClubAchievement, ClubAchievementDto>();
        CreateMap<ClubAchievementCreateDto, ClubAchievement>();
        
        // ClubAchievementTranslation mappings
        CreateMap<ClubAchievementTranslation, ClubAchievementTranslationDto>();
        CreateMap<ClubAchievementTranslationCreateDto, ClubAchievementTranslation>();
        
        // ClubEvent mappings
        CreateMap<ClubEvent, ClubEventDto>();
        CreateMap<ClubEventCreateDto, ClubEvent>();
        
        // ClubEventTranslation mappings
        CreateMap<ClubEventTranslation, ClubEventTranslationDto>();
        CreateMap<ClubEventTranslationCreateDto, ClubEventTranslation>();
        
        // ClubGalleryItem mappings
        CreateMap<ClubGalleryItem, ClubGalleryItemDto>();
        CreateMap<ClubGalleryItemCreateDto, ClubGalleryItem>();
    }
}
