using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using School.API.Common;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.TuitionFee;
using School.Domain.Entities;
using School.Domain.Enums;

namespace School.API.Features.TuitionFee;

public class TuitionFeeEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/tuition-fees")
            .WithTags("Tuition Fees");

        group.MapGet("/", async ([AsParameters] TuitionFeeFilterDto filter, [FromServices] ITuitionFeeService tuitionFeeService) =>
        {
            var tuitionFees = await tuitionFeeService.GetAllTuitionFeesAsync(filter.Level, filter.Type, filter.IsActive);
            return ApiResults.ApiOk(tuitionFees, "Tuition fees retrieved successfully");
        })
        .WithName("GetAllTuitionFees")
        .WithOpenApi();

        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] ITuitionFeeService tuitionFeeService) =>
        {
            var tuitionFee = await tuitionFeeService.GetTuitionFeeByIdAsync(id);
            if (tuitionFee == null)
            {
                return ApiResults.ApiNotFound("Tuition fee not found");
            }
            return ApiResults.ApiOk(tuitionFee, "Tuition fee retrieved successfully");
        })
        .WithName("GetTuitionFeeById")
        .WithOpenApi();

        group.MapPost("/", async ([FromBody] TuitionFeeCreateDto tuitionFeeDto, [FromServices] ITuitionFeeService tuitionFeeService) =>
        {
            var tuitionFeeId = await tuitionFeeService.CreateTuitionFeeAsync(tuitionFeeDto);
            return ApiResults.ApiCreated(new { id = tuitionFeeId }, $"/api/tuition-fees/{tuitionFeeId}", "Tuition fee created successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("CreateTuitionFee")
        .WithOpenApi();

        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] TuitionFeeUpdateDto tuitionFeeDto, [FromServices] ITuitionFeeService tuitionFeeService) =>
        {
            var updated = await tuitionFeeService.UpdateTuitionFeeAsync(id, tuitionFeeDto);
            if (!updated)
            {
                return ApiResults.ApiNotFound("Tuition fee not found");
            }
            return ApiResults.ApiOk(null, "Tuition fee updated successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("UpdateTuitionFee")
        .WithOpenApi();

        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] ITuitionFeeService tuitionFeeService) =>
        {
            var deleted = await tuitionFeeService.DeleteTuitionFeeAsync(id);
            if (!deleted)
            {
                return ApiResults.ApiNotFound("Tuition fee not found");
            }
            return ApiResults.ApiOk(null, "Tuition fee deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteTuitionFee")
        .WithOpenApi();
    }
}
