using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.DTOs;
using School.Application.Features.Department;

namespace School.API.Endpoints;

public class DepartmentEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/departments").WithTags("Departments");

        // Get all departments with filtering and pagination
        group.MapGet("/", async ([AsParameters] DepartmentFilterDto filter, [FromServices] IDepartmentService departmentService) =>
        {
            var (departments, totalCount) = await departmentService.GetAllDepartmentsAsync(filter);
            var response = new { TotalCount = totalCount, Items = departments };
            return ApiResults.ApiOk(response, "Departments retrieved successfully");
        }).WithName("GetAllDepartments").WithOpenApi();

        // Get department by ID
        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IDepartmentService departmentService) =>
        {
            var department = await departmentService.GetDepartmentByIdAsync(id);
            if (department == null)
            {
                return ApiResults.ApiNotFound("Department not found");
            }
            return ApiResults.ApiOk(department, "Department retrieved successfully");
        }).WithName("GetDepartmentById").WithOpenApi();

        // Get department by code
        group.MapGet("/code/{code}", async ([FromRoute] string code, [FromServices] IDepartmentService departmentService) =>
        {
            var department = await departmentService.GetDepartmentByCodeAsync(code);
            if (department == null)
            {
                return ApiResults.ApiNotFound("Department not found");
            }
            return ApiResults.ApiOk(department, "Department retrieved successfully");
        }).WithName("GetDepartmentByCode").WithOpenApi();

        // Create new department
        group.MapPost("/", async ([FromBody] CreateDepartmentDto departmentDto, [FromServices] IDepartmentService departmentService) =>
        {
            var departmentId = await departmentService.CreateDepartmentAsync(departmentDto);
            return ApiResults.ApiCreated($"/api/departments/{departmentId}", departmentId.ToString(), "Department created successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("CreateDepartment")
        .WithOpenApi();

        // Update department
        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateDepartmentDto departmentDto, [FromServices] IDepartmentService departmentService) =>
        {
            var result = await departmentService.UpdateDepartmentAsync(id, departmentDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Department not found");
            }
            return ApiResults.ApiOk("Department updated successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateDepartment")
        .WithOpenApi();

        // Delete department
        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IDepartmentService departmentService) =>
        {
            var result = await departmentService.DeleteDepartmentAsync(id);
            if (!result)
            {
                return ApiResults.ApiNotFound("Department not found");
            }
            return ApiResults.ApiOk("Department deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteDepartment")
        .WithOpenApi();

        // Translation endpoints
        var translationGroup = group.MapGroup("/{departmentId}/translations");

        // Add translation
        translationGroup.MapPost("/", async ([FromRoute] Guid departmentId, [FromBody] CreateDepartmentTranslationDto translationDto, [FromServices] IDepartmentService departmentService) =>
        {
            var result = await departmentService.AddTranslationAsync(departmentId, translationDto);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Translation already exists or department not found");
            }
            return ApiResults.ApiOk("Translation added successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("AddDepartmentTranslation")
        .WithOpenApi();

        // Update translation
        translationGroup.MapPut("/{languageCode}", async ([FromRoute] Guid departmentId, [FromRoute] string languageCode, [FromBody] UpdateDepartmentTranslationDto translationDto, [FromServices] IDepartmentService departmentService) =>
        {
            var result = await departmentService.UpdateTranslationAsync(departmentId, languageCode, translationDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Translation not found");
            }
            return ApiResults.ApiOk("Translation updated successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateDepartmentTranslation")
        .WithOpenApi();

        // Delete translation
        translationGroup.MapDelete("/{languageCode}", async ([FromRoute] Guid departmentId, [FromRoute] string languageCode, [FromServices] IDepartmentService departmentService) =>
        {
            var result = await departmentService.DeleteTranslationAsync(departmentId, languageCode);
            if (!result)
            {
                return ApiResults.ApiNotFound("Translation not found");
            }
            return ApiResults.ApiOk("Translation deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteDepartmentTranslation")
        .WithOpenApi();
    }
}
