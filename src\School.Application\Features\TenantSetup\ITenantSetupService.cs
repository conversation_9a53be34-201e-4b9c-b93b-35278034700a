using School.Application.Common.Models;
using School.Application.DTOs.TenantSetup;

namespace School.Application.Features.TenantSetup;

/// <summary>
/// Service interface for tenant setup operations
/// </summary>
public interface ITenantSetupService
{
    /// <summary>
    /// Gets the current setup status for a tenant
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The setup status</returns>
    Task<TenantSetupStatusDto?> GetSetupStatusAsync(Guid tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Completes the school profile setup step
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="request">The school profile setup data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation</returns>
    Task<ApiResult> CompleteSchoolProfileAsync(Guid tenantId, SchoolProfileSetupDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Completes the academic structure setup step
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="request">The academic structure setup data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation</returns>
    Task<ApiResult> CompleteAcademicStructureAsync(Guid tenantId, AcademicStructureSetupDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Completes the user roles setup step
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="request">The user roles setup data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation</returns>
    Task<ApiResult> CompleteUserRolesAsync(Guid tenantId, UserRolesSetupDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Completes the initial users setup step
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="request">The initial users setup data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation</returns>
    Task<ApiResult> CompleteInitialUsersAsync(Guid tenantId, InitialUsersSetupDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Completes the system settings setup step
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="request">The system settings setup data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation</returns>
    Task<ApiResult> CompleteSystemSettingsAsync(Guid tenantId, SystemSettingsSetupDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Marks a specific setup step as complete
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="stepId">The step ID to mark as complete</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation</returns>
    Task<ApiResult> MarkStepCompleteAsync(Guid tenantId, string stepId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Skips an optional setup step
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="stepId">The step ID to skip</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation</returns>
    Task<ApiResult> SkipOptionalStepAsync(Guid tenantId, string stepId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Finalizes the tenant setup process
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation</returns>
    Task<ApiResult> FinalizeSetupAsync(Guid tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Resets the tenant setup process
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the operation</returns>
    Task<ApiResult> ResetSetupAsync(Guid tenantId, CancellationToken cancellationToken = default);
}
