using School.Domain.Common;

namespace School.Domain.Entities;

public class AcademicYearTranslation : BaseEntity
{
    public Guid AcademicYearId { get; set; }
    public string LanguageCode { get; set; } = string.Empty; // e.g., "bn-BD", "en-US"
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    
    // Navigation properties
    public AcademicYear AcademicYear { get; set; } = null!;
}
