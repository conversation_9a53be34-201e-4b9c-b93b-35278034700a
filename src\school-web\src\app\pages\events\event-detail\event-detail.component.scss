// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Loading and Error States
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
  text-align: center;
  
  .loading-icon, .error-icon {
    font-size: 60px;
    height: 60px;
    width: 60px;
    margin-bottom: 20px;
    color: $primary-color;
  }
  
  h2 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: $text-color;
  }
  
  p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: $dark-gray;
    max-width: 600px;
  }
}

// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Event Header
.event-header {
  padding: 40px 0;
  
  .back-button {
    margin-bottom: 20px;
    
    mat-icon {
      margin-right: 5px;
    }
  }
  
  .event-image {
    position: relative;
    border-radius: $border-radius;
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: $box-shadow;
    
    img {
      width: 100%;
      height: auto;
      display: block;
    }
    
    .event-category {
      position: absolute;
      top: 20px;
      left: 20px;
      background-color: $primary-color;
      color: $white;
      padding: 5px 15px;
      border-radius: 20px;
      font-size: 0.9rem;
      text-transform: uppercase;
    }
    
    .event-status {
      position: absolute;
      top: 20px;
      right: 20px;
      background-color: $accent-color;
      color: $white;
      padding: 5px 15px;
      border-radius: 20px;
      font-size: 0.9rem;
      text-transform: uppercase;
    }
  }
  
  .event-header-content {
    .event-title {
      font-size: 2.5rem;
      margin-bottom: 20px;
      color: $text-color;
    }
    
    .event-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
      
      .meta-item {
        display: flex;
        align-items: center;
        color: $dark-gray;
        
        mat-icon {
          font-size: 20px;
          height: 20px;
          width: 20px;
          margin-right: 8px;
          color: $primary-color;
        }
      }
    }
    
    .event-tags {
      margin-bottom: 20px;
    }
  }
}

// Event Content
.event-content {
  padding: 40px 0;
  background-color: $light-gray;
  
  .content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    
    @media (max-width: 992px) {
      grid-template-columns: 1fr;
    }
    
    .main-content {
      .description-section, .schedule-section, .speakers-section {
        background-color: $white;
        border-radius: $border-radius;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: $box-shadow;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        h2 {
          font-size: 1.8rem;
          margin-bottom: 20px;
          color: $text-color;
          position: relative;
          padding-bottom: 10px;
          
          &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: $primary-color;
          }
        }
        
        p {
          font-size: 1.1rem;
          line-height: 1.6;
          color: $text-color;
        }
      }
      
      .schedule-list {
        .schedule-item {
          display: flex;
          margin-bottom: 15px;
          padding-bottom: 15px;
          border-bottom: 1px solid $medium-gray;
          
          &:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
          }
          
          .schedule-time {
            flex: 0 0 40%;
            font-weight: 500;
            color: $primary-color;
          }
          
          .schedule-activity {
            flex: 0 0 60%;
            color: $text-color;
          }
        }
      }
      
      .speakers-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        
        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }
        
        .speaker-card {
          display: flex;
          align-items: center;
          
          .speaker-image {
            flex: 0 0 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 20px;
            
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          
          .speaker-info {
            flex: 1;
            
            .speaker-name {
              font-size: 1.3rem;
              margin: 0 0 5px;
              color: $text-color;
            }
            
            .speaker-title {
              font-size: 1rem;
              margin: 0 0 10px;
              color: $primary-color;
            }
            
            .speaker-bio {
              font-size: 0.9rem;
              margin: 0;
              color: $dark-gray;
              line-height: 1.5;
            }
          }
        }
      }
    }
    
    .sidebar {
      .registration-card, .contact-card, .share-card, .calendar-card {
        background-color: $white;
        border-radius: $border-radius;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: $box-shadow;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        h3 {
          font-size: 1.3rem;
          margin-bottom: 20px;
          color: $text-color;
          position: relative;
          padding-bottom: 10px;
          
          &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 3px;
            background-color: $primary-color;
          }
        }
      }
      
      .registration-card {
        .registration-closed {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          
          mat-icon {
            font-size: 40px;
            height: 40px;
            width: 40px;
            margin-bottom: 15px;
            color: $accent-color;
          }
          
          p {
            font-size: 1.1rem;
            color: $dark-gray;
            margin: 0;
          }
        }
        
        .registration-open {
          p {
            font-size: 1.1rem;
            margin-bottom: 15px;
            color: $text-color;
          }
          
          .deadline {
            margin-bottom: 20px;
            color: $dark-gray;
          }
          
          .register-button {
            width: 100%;
          }
        }
      }
      
      .contact-card {
        .contact-info {
          .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            mat-icon {
              font-size: 20px;
              height: 20px;
              width: 20px;
              margin-right: 10px;
              color: $primary-color;
            }
            
            a {
              color: $dark-gray;
              text-decoration: none;
              
              &:hover {
                color: $primary-color;
                text-decoration: underline;
              }
            }
          }
        }
      }
      
      .share-card {
        .share-buttons {
          display: flex;
          gap: 15px;
          
          a {
            transition: transform 0.3s;
            
            &:hover {
              transform: translateY(-3px);
            }
          }
        }
      }
      
      .calendar-card {
        .calendar-buttons {
          display: flex;
          flex-direction: column;
          gap: 10px;
          
          a {
            display: flex;
            align-items: center;
            justify-content: center;
            
            mat-icon {
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
}

// Call to Action Section
.cta-section {
  background-color: $primary-color;
  color: $white;
  padding: 60px 0;
  text-align: center;
  
  h2 {
    font-size: 2rem;
    margin-bottom: 20px;
  }
  
  p {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    
    @media (max-width: 576px) {
      flex-direction: column;
      align-items: center;
      
      a, button {
        width: 100%;
        max-width: 250px;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .event-header {
    padding: 30px 0;
    
    .event-header-content {
      .event-title {
        font-size: 2rem;
      }
      
      .event-meta {
        flex-direction: column;
        gap: 10px;
      }
    }
  }
  
  .event-content {
    padding: 30px 0;
  }
}

@media (max-width: 576px) {
  .event-header {
    .event-header-content {
      .event-title {
        font-size: 1.8rem;
      }
    }
  }
  
  .event-content {
    .main-content {
      .description-section, .schedule-section, .speakers-section {
        padding: 20px;
        
        h2 {
          font-size: 1.5rem;
        }
        
        p {
          font-size: 1rem;
        }
      }
      
      .schedule-list {
        .schedule-item {
          flex-direction: column;
          
          .schedule-time {
            margin-bottom: 5px;
          }
        }
      }
      
      .speakers-grid {
        .speaker-card {
          flex-direction: column;
          text-align: center;
          
          .speaker-image {
            margin-right: 0;
            margin-bottom: 15px;
          }
        }
      }
    }
  }
  
  .cta-section {
    padding: 40px 0;
    
    h2 {
      font-size: 1.8rem;
    }
    
    p {
      font-size: 1.1rem;
    }
  }
}
