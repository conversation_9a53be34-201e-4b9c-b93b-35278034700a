<div class="term-list-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">Terms</h1>
      <p class="page-subtitle">Manage academic terms and semesters</p>
    </div>
    <div class="header-actions">
      <button mat-raised-button color="primary" routerLink="/admin/terms/create">
        <mat-icon>add</mat-icon>
        Create Term
      </button>
    </div>
  </div>

  <!-- Filters -->
  <mat-card class="filter-card">
    <mat-card-header>
      <mat-card-title>Filters</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="filterForm" class="filter-form">
        <div class="filter-row">
          <mat-form-field appearance="outline">
            <mat-label>Academic Year</mat-label>
            <mat-select formControlName="academicYearId">
              <mat-option value="">All Academic Years</mat-option>
              <mat-option *ngFor="let year of academicYears" [value]="year.id">
                {{year.displayName}}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Name</mat-label>
            <input matInput formControlName="name" placeholder="Search by name">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Code</mat-label>
            <input matInput formControlName="code" placeholder="Search by code">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Type</mat-label>
            <mat-select formControlName="type">
              <mat-option value="">All Types</mat-option>
              <mat-option *ngFor="let type of typeOptions" [value]="type.value">
                {{type.label}}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Status</mat-label>
            <mat-select formControlName="status">
              <mat-option value="">All Statuses</mat-option>
              <mat-option *ngFor="let status of statusOptions" [value]="status.value">
                {{status.label}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="filter-actions">
          <button mat-button type="button" (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            Clear Filters
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Data Table -->
  <mat-card class="table-card">
    <mat-card-content>
      <div class="table-container">
        <table mat-table [dataSource]="dataSource" matSort class="term-table">
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
            <td mat-cell *matCellDef="let term">
              <strong>{{term.name}}</strong>
            </td>
          </ng-container>

          <!-- Code Column -->
          <ng-container matColumnDef="code">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Code</th>
            <td mat-cell *matCellDef="let term">
              <code>{{term.code}}</code>
            </td>
          </ng-container>

          <!-- Academic Year Column -->
          <ng-container matColumnDef="academicYear">
            <th mat-header-cell *matHeaderCellDef>Academic Year</th>
            <td mat-cell *matCellDef="let term">
              {{term.academicYearName}}
            </td>
          </ng-container>

          <!-- Type Column -->
          <ng-container matColumnDef="type">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Type</th>
            <td mat-cell *matCellDef="let term">
              <mat-chip color="accent" selected>
                {{getTypeText(term.type)}}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Start Date Column -->
          <ng-container matColumnDef="startDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Start Date</th>
            <td mat-cell *matCellDef="let term">
              {{term.startDate | date:'mediumDate'}}
            </td>
          </ng-container>

          <!-- End Date Column -->
          <ng-container matColumnDef="endDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>End Date</th>
            <td mat-cell *matCellDef="let term">
              {{term.endDate | date:'mediumDate'}}
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let term">
              <mat-chip [color]="getStatusColor(term.status)" selected>
                {{getStatusText(term.status)}}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Order Index Column -->
          <ng-container matColumnDef="orderIndex">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Order</th>
            <td mat-cell *matCellDef="let term">
              <mat-chip color="primary" selected>
                {{term.orderIndex}}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let term">
              <div class="action-buttons">
                <button mat-icon-button 
                        [routerLink]="['/admin/terms', term.id]"
                        matTooltip="View Details">
                  <mat-icon>visibility</mat-icon>
                </button>

                <button mat-icon-button 
                        [routerLink]="['/admin/terms/edit', term.id]"
                        matTooltip="Edit">
                  <mat-icon>edit</mat-icon>
                </button>

                <button mat-icon-button 
                        *ngIf="term.status === 0"
                        (click)="activateTerm(term)"
                        matTooltip="Activate">
                  <mat-icon>play_arrow</mat-icon>
                </button>

                <button mat-icon-button 
                        *ngIf="term.status === 1"
                        (click)="completeTerm(term)"
                        matTooltip="Complete">
                  <mat-icon>check_circle</mat-icon>
                </button>

                <button mat-icon-button 
                        color="warn"
                        (click)="deleteTerm(term)"
                        matTooltip="Delete">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- Loading Spinner -->
        <div *ngIf="loading" class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
        </div>

        <!-- No Data Message -->
        <div *ngIf="!loading && dataSource.data.length === 0" class="no-data">
          <mat-icon>schedule</mat-icon>
          <h3>No Terms Found</h3>
          <p>Create your first term to get started.</p>
          <button mat-raised-button color="primary" routerLink="/admin/terms/create">
            Create Term
          </button>
        </div>
      </div>

      <!-- Paginator -->
      <mat-paginator 
        [length]="totalCount"
        [pageSize]="currentFilter.pageSize"
        [pageSizeOptions]="[5, 10, 25, 50]"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
