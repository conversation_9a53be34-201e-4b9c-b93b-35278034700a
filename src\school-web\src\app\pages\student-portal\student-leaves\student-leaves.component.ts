import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { StudentService } from '../../../core/services/student.service';
import { AuthService } from '../../../core/services/auth.service';
import { Student, StudentLeave, LeaveStatus, LeaveType } from '../../../core/models/student.model';
import { LeaveApplicationDialogComponent } from './leave-application-dialog/leave-application-dialog.component';

@Component({
  selector: 'app-student-leaves',
  templateUrl: './student-leaves.component.html',
  styleUrls: ['./student-leaves.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DatePipe,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatDialogModule,
    MatSnackBarModule
  ]
})
export class StudentLeavesComponent implements OnInit {
  student: Student | null = null;
  leaves: StudentLeave[] = [];

  filterForm: FormGroup;

  loading = {
    student: true,
    leaves: true
  };

  error = {
    student: false,
    leaves: false
  };

  leaveTypes = [
    { value: LeaveType.Sick, label: 'Sick' },
    { value: LeaveType.Personal, label: 'Personal' },
    { value: LeaveType.Family, label: 'Family' },
    { value: LeaveType.Religious, label: 'Religious' },
    { value: LeaveType.Other, label: 'Other' }
  ];

  leaveStatuses = [
    { value: LeaveStatus.Pending, label: 'Pending' },
    { value: LeaveStatus.Approved, label: 'Approved' },
    { value: LeaveStatus.Rejected, label: 'Rejected' },
    { value: LeaveStatus.Cancelled, label: 'Cancelled' }
  ];

  // Loading and error states are defined above

  constructor(
    private studentService: StudentService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.filterForm = this.formBuilder.group({
      status: [null]
    });
  }

  ngOnInit(): void {
    this.loadStudentData();
  }

  loadStudentData(): void {
    this.loading.student = true;

    // In a real application, you would fetch the student by user ID
    // For now, we'll use a mock student ID
    this.studentService.getStudentByStudentId('S2023-001')
      .subscribe({
        next: (student) => {
          this.student = student;
          this.loading.student = false;
          this.loadLeaves();
        },
        error: (err) => {
          console.error('Error loading student data:', err);
          this.error.student = true;
          this.loading.student = false;
          this.snackBar.open('Failed to load student data', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  loadLeaves(): void {
    if (!this.student) return;

    this.loading.leaves = true;
    this.error.leaves = false;

    const filters = this.filterForm.value;

    this.studentService.getStudentLeaves(this.student.id, undefined, undefined, filters.status)
      .subscribe({
        next: (leaves) => {
          this.leaves = leaves;
          this.loading.leaves = false;
        },
        error: (err) => {
          console.error('Error loading leaves:', err);
          this.error.leaves = true;
          this.loading.leaves = false;
          this.snackBar.open('Failed to load leave applications', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  applyFilter(): void {
    this.loadLeaves();
  }

  resetFilter(): void {
    this.filterForm.reset();
    this.loadLeaves();
  }

  getLeaveTypeLabel(type: LeaveType | undefined): string {
    if (type === undefined) return 'Unknown';
    return this.leaveTypes.find(t => t.value === type)?.label || 'Unknown';
  }

  getStatusLabel(status: LeaveStatus | undefined): string {
    if (status === undefined) return 'Unknown';
    return this.leaveStatuses.find(s => s.value === status)?.label || 'Unknown';
  }

  getStatusClass(status: LeaveStatus | undefined): string {
    if (status === undefined) return 'pending';
    switch (status) {
      case LeaveStatus.Approved: return 'approved';
      case LeaveStatus.Pending: return 'pending';
      case LeaveStatus.Rejected: return 'rejected';
      case LeaveStatus.Cancelled: return 'cancelled';
      default: return '';
    }
  }

  calculateLeaveDays(startDate: Date | undefined, endDate: Date | undefined): number {
    if (!startDate || !endDate) return 0;
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  }

  openLeaveApplicationDialog(): void {
    if (!this.student) return;

    const dialogRef = this.dialog.open(LeaveApplicationDialogComponent, {
      width: '600px',
      data: { studentId: this.student.id }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadLeaves();
        this.snackBar.open('Leave application submitted successfully', 'Close', {
          duration: 3000
        });
      }
    });
  }

  cancelLeave(leaveId: number): void {
    if (!this.student) return;

    this.studentService.updateLeaveStatus(this.student.id, leaveId, {
      status: LeaveStatus.Cancelled,
      approvedBy: '',
      comments: 'Cancelled by student'
    }).subscribe({
      next: () => {
        this.loadLeaves();
        this.snackBar.open('Leave application cancelled successfully', 'Close', {
          duration: 3000
        });
      },
      error: (err) => {
        console.error('Error cancelling leave:', err);
        this.snackBar.open('Failed to cancel leave application', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  canCancelLeave(leave: StudentLeave): boolean {
    return leave.status === LeaveStatus.Pending;
  }
}
