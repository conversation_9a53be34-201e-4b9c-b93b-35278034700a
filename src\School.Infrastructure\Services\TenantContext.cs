using Microsoft.AspNetCore.Http;
using School.Application.Common.Interfaces;

namespace School.Infrastructure.Services;

/// <summary>
/// Implementation of tenant context using HTTP context and thread-local storage
/// </summary>
public class TenantContext : ITenantContext
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private static readonly AsyncLocal<Guid?> _tenantId = new();

    public TenantContext(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public Guid? GetCurrentTenantId()
    {
        // First try to get from HTTP context (for web requests)
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.Items.ContainsKey("TenantId") == true)
        {
            var tenantIdFromContext = httpContext.Items["TenantId"];
            if (tenantIdFromContext is Guid tenantId)
            {
                return tenantId;
            }
        }

        // Fallback to thread-local storage (for background tasks, tests, etc.)
        return _tenantId.Value;
    }

    public void SetCurrentTenantId(Guid? tenantId)
    {
        // Set in HTTP context if available
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext != null)
        {
            httpContext.Items["TenantId"] = tenantId;
        }

        // Also set in thread-local storage
        _tenantId.Value = tenantId;
    }

    public bool HasTenantContext()
    {
        return GetCurrentTenantId().HasValue;
    }

    public void ClearTenantContext()
    {
        // Clear from HTTP context if available
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext != null)
        {
            httpContext.Items.Remove("TenantId");
        }

        // Clear from thread-local storage
        _tenantId.Value = null;
    }
}
