using School.Application.DTOs;
using System.Threading.Tasks;

namespace School.Application.Features.Auth
{
    public interface IMfaService
    {
        /// <summary>
        /// Generate MFA setup information for a user
        /// </summary>
        Task<MfaSetupResponseDto> GenerateMfaSetupAsync(string userId, string password);
        
        /// <summary>
        /// Verify MFA setup with the provided code
        /// </summary>
        Task<bool> VerifyMfaSetupAsync(string userId, string secret, string code);
        
        /// <summary>
        /// Enable MFA for a user after successful verification
        /// </summary>
        Task<bool> EnableMfaAsync(string userId, string secret, string code);
        
        /// <summary>
        /// Disable MFA for a user
        /// </summary>
        Task<bool> DisableMfaAsync(string userId, string password, string mfaCode);
        
        /// <summary>
        /// Verify MFA code during login
        /// </summary>
        Task<bool> VerifyMfaCodeAsync(string userId, string code);
        
        /// <summary>
        /// Verify MFA backup code
        /// </summary>
        Task<bool> VerifyBackupCodeAsync(string userId, string backupCode);
        
        /// <summary>
        /// Generate new backup codes
        /// </summary>
        Task<string[]> GenerateNewBackupCodesAsync(string userId, string password, string mfaCode);
        
        /// <summary>
        /// Generate QR code URL for MFA setup
        /// </summary>
        string GenerateQrCodeUrl(string userEmail, string secret, string issuer = "School Management System");
    }
}
