using Microsoft.EntityFrameworkCore;
using School.Application.Common.Interfaces;
using School.Application.Common.Models;
using School.Application.DTOs;
using School.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace School.Infrastructure.Persistence.Repositories;

public class ClubRepository : Repository<Club>, IClubRepository
{
    public ClubRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<PagedList<Club>> GetClubsAsync(ClubFilterDto filter, CancellationToken cancellationToken = default)
    {
        var query = _dbContext.Clubs
            .Include(c => c.Translations)
            .Include(c => c.Advisors)
            .Include(c => c.Leaders)
            .Include(c => c.Activities).ThenInclude(a => a.Translations)
            .Include(c => c.Achievements).ThenInclude(a => a.Translations)
            .Include(c => c.Events).ThenInclude(e => e.Translations)
            .Include(c => c.GalleryItems)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(filter.Name))
        {
            query = query.Where(c => c.Name.Contains(filter.Name) ||
                                    c.Translations.Any(t => t.Name.Contains(filter.Name)));
        }

        if (!string.IsNullOrEmpty(filter.Category))
        {
            query = query.Where(c => c.Category == filter.Category);
        }

        if (filter.IsFeatured.HasValue)
        {
            query = query.Where(c => c.IsFeatured == filter.IsFeatured.Value);
        }

        if (filter.IsActive.HasValue)
        {
            query = query.Where(c => c.IsActive == filter.IsActive.Value);
        }

        // Apply sorting
        query = ApplySorting(query, filter.SortBy, filter.SortDirection);

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var page = filter.Page ?? 1;
        var pageSize = filter.PageSize ?? 10;
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        // Create paged list
        return new PagedList<Club>(items, totalCount, page, pageSize);
    }

    public async Task<Club> GetClubByIdWithDetailsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _dbContext.Clubs
            .Include(c => c.Translations)
            .Include(c => c.Advisors).ThenInclude(a => a.Faculty)
            .Include(c => c.Leaders).ThenInclude(l => l.Student)
            .Include(c => c.Activities).ThenInclude(a => a.Translations)
            .Include(c => c.Achievements).ThenInclude(a => a.Translations)
            .Include(c => c.Events).ThenInclude(e => e.Translations)
            .Include(c => c.GalleryItems)
            .FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<PagedList<Club>> GetFeaturedClubsAsync(int page = 1, int pageSize = 10, CancellationToken cancellationToken = default)
    {
        var query = _dbContext.Clubs
            .Where(c => c.IsFeatured && c.IsActive)
            .Include(c => c.Translations)
            .OrderBy(c => c.DisplayOrder)
            .AsQueryable();

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        // Create paged list
        return new PagedList<Club>(items, totalCount, page, pageSize);
    }

    public async Task<List<string>> GetClubCategoriesAsync(CancellationToken cancellationToken = default)
    {
        return await _dbContext.Clubs
            .Where(c => c.IsActive)
            .Select(c => c.Category)
            .Distinct()
            .OrderBy(c => c)
            .ToListAsync(cancellationToken);
    }

    private IQueryable<Club> ApplySorting(IQueryable<Club> query, string sortBy, string sortDirection)
    {
        if (string.IsNullOrEmpty(sortBy))
        {
            // Default sorting
            return query.OrderBy(c => c.DisplayOrder).ThenBy(c => c.Name);
        }

        var isDescending = sortDirection?.ToLower() == "desc";

        switch (sortBy.ToLower())
        {
            case "name":
                return isDescending ? query.OrderByDescending(c => c.Name) : query.OrderBy(c => c.Name);
            case "category":
                return isDescending ? query.OrderByDescending(c => c.Category) : query.OrderBy(c => c.Category);
            case "displayorder":
                return isDescending ? query.OrderByDescending(c => c.DisplayOrder) : query.OrderBy(c => c.DisplayOrder);
            case "createdat":
                return isDescending ? query.OrderByDescending(c => c.CreatedAt) : query.OrderBy(c => c.CreatedAt);
            default:
                return query.OrderBy(c => c.DisplayOrder).ThenBy(c => c.Name);
        }
    }
}
