import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { FacultyService } from '../../../core/services/faculty.service';
import { AuthService } from '../../../core/services/auth.service';
import { FacultyDetail, FacultySubject } from '../../../core/models/faculty.model';
import { ExamType } from '../../../core/models/student.model';
import { RecordResultsDialogComponent } from './record-results-dialog/record-results-dialog.component';

@Component({
  selector: 'app-faculty-results',
  templateUrl: './faculty-results.component.html',
  styleUrls: ['./faculty-results.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatTableModule,
    MatDialogModule,
    MatSnackBarModule
  ]
})
export class FacultyResultsComponent implements OnInit {
  faculty: FacultyDetail | null = null;
  subjects: FacultySubject[] = [];
  filterForm: FormGroup;

  loading = {
    faculty: true,
    subjects: false,
    results: false
  };

  error = {
    faculty: false,
    subjects: false,
    results: false
  };

  resultsData: any[] = [];
  displayedColumns: string[] = ['rollNumber', 'name', 'marksObtained', 'totalMarks', 'percentage', 'grade'];

  examTypes = [
    { value: ExamType.HalfYearly, label: 'Half Yearly' },
    { value: ExamType.Annual, label: 'Annual' },
    { value: ExamType.ClassTest, label: 'Class Test' },
    { value: ExamType.SSC, label: 'SSC' }
  ];

  constructor(
    private facultyService: FacultyService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    const currentYear = new Date().getFullYear();

    this.filterForm = this.formBuilder.group({
      subject: ['', Validators.required],
      examType: [ExamType.Annual, Validators.required],
      academicYear: [currentYear, Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadFacultyData();
  }

  loadFacultyData(): void {
    this.loading.faculty = true;

    // In a real application, you would fetch the faculty by user ID
    const userId = this.authService.getCurrentUserId();
    if (!userId) {
      this.error.faculty = true;
      this.loading.faculty = false;
      this.snackBar.open('User ID not found. Please log in again.', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    // For now, we'll use a mock faculty ID
    this.facultyService.getFaculty(1)
      .subscribe({
        next: (faculty) => {
          this.faculty = faculty;
          this.loading.faculty = false;
          this.loadSubjects();
        },
        error: (err) => {
          console.error('Error loading faculty data:', err);
          this.error.faculty = true;
          this.loading.faculty = false;
          this.snackBar.open('Failed to load faculty data', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  loadSubjects(): void {
    if (!this.faculty) return;

    this.loading.subjects = true;
    this.error.subjects = false;

    const currentYear = new Date().getFullYear();

    this.facultyService.getFacultySubjects(this.faculty.id, currentYear)
      .subscribe({
        next: (subjects) => {
          this.subjects = subjects;
          this.loading.subjects = false;

          // If there's only one subject, select it automatically
          if (subjects.length === 1) {
            this.filterForm.patchValue({
              subject: subjects[0]
            });
          }
        },
        error: (err) => {
          console.error('Error loading subjects:', err);
          this.error.subjects = true;
          this.loading.subjects = false;
          this.snackBar.open('Failed to load assigned subjects', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  loadResults(): void {
    if (!this.faculty || !this.filterForm.valid) return;

    this.loading.results = true;
    this.error.results = false;

    const formValues = this.filterForm.value;
    const subject = formValues.subject;

    this.facultyService.getClassResults(
      this.faculty.id,
      subject.grade,
      subject.section,
      subject.subjectCode,
      formValues.examType,
      formValues.academicYear
    ).subscribe({
      next: (data) => {
        this.resultsData = data;
        this.loading.results = false;
      },
      error: (err) => {
        console.error('Error loading results data:', err);
        this.error.results = true;
        this.loading.results = false;
        this.snackBar.open('Failed to load results data', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  openRecordResultsDialog(): void {
    if (!this.faculty || !this.filterForm.valid) {
      this.snackBar.open('Please select subject, exam type, and academic year first', 'Close', {
        duration: 3000
      });
      return;
    }

    const formValues = this.filterForm.value;
    const subject = formValues.subject;

    const dialogRef = this.dialog.open(RecordResultsDialogComponent, {
      width: '800px',
      data: {
        facultyId: this.faculty.id,
        grade: subject.grade,
        section: subject.section,
        subjectCode: subject.subjectCode,
        subjectName: subject.subjectName,
        examType: formValues.examType,
        academicYear: formValues.academicYear,
        resultsData: this.resultsData
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadResults();
        this.snackBar.open('Results recorded successfully', 'Close', {
          duration: 3000
        });
      }
    });
  }

  getExamTypeLabel(examType: ExamType): string {
    return this.examTypes.find(type => type.value === examType)?.label || 'Unknown';
  }

  getGradeColor(grade: string): string {
    switch (grade) {
      case 'A+': return '#2e7d32';
      case 'A': return '#388e3c';
      case 'A-': return '#43a047';
      case 'B+': return '#689f38';
      case 'B': return '#7cb342';
      case 'C+': return '#ffa000';
      case 'C': return '#ffb300';
      case 'D': return '#f57c00';
      case 'F': return '#d32f2f';
      default: return '#757575';
    }
  }
}
