{"CLASS_TEACHERS": {"TITLE": "Class Teacher Management", "ASSIGN_TEACHER": "Assign Teacher", "REASSIGN_TEACHER": "Reassign Teacher", "REMOVE_TEACHER": "Remove Teacher", "SEARCH_PLACEHOLDER": "Search by faculty name, section, or grade...", "NO_ASSIGNMENTS_FOUND": "No Class Teacher Assignments Found", "NO_ASSIGNMENTS_MESSAGE": "No class teachers have been assigned yet. Assign your first class teacher to get started.", "ASSIGN_FIRST_TEACHER": "Assign First Teacher", "ASSIGNMENT_DETAILS": "Assignment Details", "RESPONSIBILITIES": "Responsibilities", "SCHEDULE": "Schedule & Contact", "FACULTY": "Faculty", "SECTION": "Section", "GRADE": "Grade", "ACADEMIC_YEAR": "Academic Year", "START_DATE": "Start Date", "END_DATE": "End Date", "STATUS": "Status", "WORKLOAD": {"PERCENTAGE": "Workload Percentage", "TOTAL_SECTIONS": "Total Sections", "TOTAL_STUDENTS": "Total Students", "OVERLOADED": "Overloaded", "NORMAL": "Normal", "LIGHT": "Light"}, "STUDENT_COUNT": "Student Count", "IS_PRIMARY": "Primary Class Teacher", "IS_PRIMARY_HINT": "Primary class teachers have additional administrative responsibilities", "ALREADY_ASSIGNED": "Already Assigned", "RESPONSIBILITIES_PLACEHOLDER": "Enter main responsibilities and duties", "SPECIAL_DUTIES": "Special Duties", "SPECIAL_DUTIES_PLACEHOLDER": "Enter any special duties or additional responsibilities", "CONTACT_SCHEDULE": "Contact Schedule", "CONTACT_SCHEDULE_PLACEHOLDER": "Enter when parents can contact (e.g., Mon-Fri 2-4 PM)", "OFFICE_HOURS": "Office Hours", "OFFICE_HOURS_PLACEHOLDER": "Enter office hours for student consultations", "ACTIONS": {"ASSIGN": "Assign", "REASSIGN": "Reassign", "REMOVE": "Remove", "ACTIVATE": "Activate", "SUSPEND": "Suspend", "COMPLETE": "Complete Assignment", "VIEW_WORKLOAD": "View Workload", "VIEW_PERFORMANCE": "View Performance", "TRANSFER": "Transfer", "EXPORT": "Export", "BULK_ASSIGN": "Bulk Assign"}, "STATUS_OPTIONS": {"ACTIVE": "Active", "INACTIVE": "Inactive", "SUSPENDED": "Suspended", "COMPLETED": "Completed"}, "PERFORMANCE": {"ATTENDANCE_RATE": "Attendance Rate", "PERFORMANCE_SCORE": "Performance Score", "PARENT_SATISFACTION": "Parent Satisfaction", "ACHIEVEMENTS": "Achievements"}, "MESSAGES": {"ASSIGNED_SUCCESS": "Class teacher assigned successfully", "REASSIGNED_SUCCESS": "Class teacher reassigned successfully", "REMOVED_SUCCESS": "Class teacher removed successfully", "STATUS_UPDATED_SUCCESS": "Status updated successfully", "ACTIVATED_SUCCESS": "Class teacher activated successfully", "SUSPENDED_SUCCESS": "Class teacher suspended successfully", "COMPLETED_SUCCESS": "Assignment completed successfully", "TRANSFERRED_SUCCESS": "Class teacher transferred successfully", "BULK_ASSIGNED_SUCCESS": "Class teachers assigned successfully", "EXPORTED_SUCCESS": "Class teacher data exported successfully", "ASSIGN_ERROR": "Error assigning class teacher", "REASSIGN_ERROR": "Error reassigning class teacher", "REMOVE_ERROR": "Error removing class teacher", "STATUS_UPDATE_ERROR": "Error updating status", "LOAD_ERROR": "Error loading class teacher data", "TRANSFER_ERROR": "Error transferring class teacher", "VALIDATION_ERROR": "Please check the form for errors", "REMOVE_CONFIRMATION": "Are you sure you want to remove this class teacher assignment?", "REMOVE_WARNING": "This will remove the teacher from the section and may affect student management.", "SUSPEND_CONFIRMATION": "Are you sure you want to suspend this class teacher?", "SUSPEND_WARNING": "The teacher will be temporarily removed from active duties.", "COMPLETE_CONFIRMATION": "Are you sure you want to complete this assignment?", "COMPLETE_WARNING": "This will mark the assignment as finished and may require a new teacher.", "WORKLOAD_WARNING": "This faculty member has a high workload. Consider redistributing assignments.", "SECTION_ALREADY_ASSIGNED": "This section already has a class teacher assigned.", "FACULTY_OVERLOADED": "This faculty member is overloaded with assignments."}, "FILTERS": {"ALL_GRADES": "All Grades", "ALL_SECTIONS": "All Sections", "ALL_FACULTY": "All Faculty", "ALL_ACADEMIC_YEARS": "All Academic Years", "ALL_STATUS": "All Status", "ACTIVE_ONLY": "Active Only", "INACTIVE_ONLY": "Inactive Only", "PRIMARY_ONLY": "Primary Teachers Only", "OVERLOADED_ONLY": "Overloaded Faculty Only"}, "SORT": {"FACULTY_NAME_ASC": "Faculty Name (A-Z)", "FACULTY_NAME_DESC": "Faculty Name (Z-A)", "SECTION_NAME_ASC": "Section Name (A-Z)", "SECTION_NAME_DESC": "Section Name (Z-A)", "WORKLOAD_ASC": "Workload (Low to High)", "WORKLOAD_DESC": "Workload (High to Low)", "START_DATE_ASC": "Start Date (Oldest First)", "START_DATE_DESC": "Start Date (Newest First)"}, "VALIDATION": {"FACULTY_REQUIRED": "Faculty selection is required", "SECTION_REQUIRED": "Section selection is required", "GRADE_REQUIRED": "Grade selection is required", "ACADEMIC_YEAR_REQUIRED": "Academic year selection is required", "START_DATE_REQUIRED": "Start date is required", "RESPONSIBILITIES_MAX_LENGTH": "Responsibilities cannot exceed 1000 characters", "SPECIAL_DUTIES_MAX_LENGTH": "Special duties cannot exceed 1000 characters", "CONTACT_SCHEDULE_MAX_LENGTH": "Contact schedule cannot exceed 500 characters", "OFFICE_HOURS_MAX_LENGTH": "Office hours cannot exceed 500 characters"}, "REPORTS": {"WORKLOAD_REPORT": "Faculty Workload Report", "ASSIGNMENT_REPORT": "Class Teacher Assignment Report", "PERFORMANCE_REPORT": "Class Teacher Performance Report", "SECTION_COVERAGE": "Section Coverage Report"}, "BULK_OPERATIONS": {"SELECT_SECTIONS": "Select Sections", "SELECT_FACULTY": "Select Faculty", "ASSIGN_MULTIPLE": "Assign Multiple Teachers", "REASSIGN_MULTIPLE": "Reassign Multiple Teachers", "REMOVE_MULTIPLE": "Remove Multiple Assignments", "UPDATE_STATUS_MULTIPLE": "Update Multiple Status"}, "TOGGLE_STATUS": "Toggle Active Status", "VIEW_DETAILS": "View Assignment Details", "EDIT_ASSIGNMENT": "Edit Assignment", "ASSIGNMENT_HISTORY": "Assignment History"}}