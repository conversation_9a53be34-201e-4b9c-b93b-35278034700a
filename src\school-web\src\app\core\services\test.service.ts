import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TestService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Make a test request with the token explicitly added
   */
  testWithExplicitToken(token: string): Observable<any> {
    console.log('Making test request with explicit token');
    console.log('Token:', token ? token.substring(0, 10) + '...' : 'null');
    
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`
    });

    return this.http.get(`${this.apiUrl}/test`, { headers });
  }

  /**
   * Make a test request without explicitly adding the token
   * This should rely on the interceptor to add the token
   */
  testWithInterceptor(): Observable<any> {
    console.log('Making test request with interceptor');
    
    return this.http.get(`${this.apiUrl}/test`);
  }
}
