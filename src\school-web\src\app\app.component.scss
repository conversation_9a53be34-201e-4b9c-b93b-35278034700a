// Main container for public layout
.main-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  transition: padding-top 0.3s ease;
}

// Admin container for admin layout
.admin-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// Auth container for login/register layout
.auth-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  background-image: linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)),
                    url('/assets/images/school-background.jpg');
  background-size: cover;
  background-position: center;
}

// Header section (contains banner and nav)
.header-section {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1000;
  width: 100%;

  // Remove any potential gaps between children
  > * {
    margin: 0;
  }
}

// Main content
.main-content {
  flex: 1;
  padding: 24px;
  transition: padding 0.3s ease;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  &.mobile-content {
    padding-top: 16px;
  }
}

// Responsive styles for main content
@media (max-width: 991px) { // Mobile breakpoint
  .header-section {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }

  .main-content {
    padding: 64px 16px 16px; // Top padding for fixed navbar + content padding
  }
}

@media (max-width: 480px) { // Very small screens
  .main-content {
    padding: 56px 12px 12px; // Smaller navbar height + smaller content padding
  }
}

// Tenant setup container styles (minimal - components handle their own layout)
.tenant-setup-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;

  // Ensure no interference from parent styles
  margin: 0;
  padding: 0;
  border: none;
  background: transparent;

  // Allow components to take full control
  router-outlet + * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
