import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { ParentService } from '../../../core/services/parent.service';
import { AuthService } from '../../../core/services/auth.service';
import { ParentDetail } from '../../../core/models/parent.model';

@Component({
  selector: 'app-parent-dashboard',
  templateUrl: './parent-dashboard.component.html',
  styleUrls: ['./parent-dashboard.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ]
})
export class ParentDashboardComponent implements OnInit {
  parent: ParentDetail | null = null;
  loading = true;
  error = false;

  constructor(
    private parentService: ParentService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadParentData();
  }

  loadParentData(): void {
    this.loading = true;
    const user = this.authService.getCurrentUser();
    const userId = user?.id;

    if (userId) {
      // If we have a user ID, get the parent by user ID
      this.parentService.getParentByUserId(userId).subscribe({
        next: (parent) => {
          this.parent = parent;
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading parent data:', err);
          this.error = true;
          this.loading = false;
          this.showErrorMessage('Failed to load parent data. Please try again later.');
        }
      });
    } else {
      // Fallback to mock data if no user ID is available
      this.parentService.getParent(1).subscribe({
        next: (parent) => {
          this.parent = parent;
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading parent data:', err);
          this.error = true;
          this.loading = false;
          this.showErrorMessage('Failed to load parent data. Please try again later.');
        }
      });
    }
  }

  getStudentRoute(studentId: number, route: string): string {
    return `/parent-portal/student/${studentId}/${route}`;
  }

  /**
   * Show an error message
   */
  private showErrorMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }
}
