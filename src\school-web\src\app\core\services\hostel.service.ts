import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { ErrorHandlerService } from './error-handler.service';
import { ApiResponseHandlerService } from './api-response-handler.service';
import { ApiResponse } from '../models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class HostelService extends BaseApiService {
  constructor(
    protected override http: HttpClient,
    protected override errorHandler: ErrorHandlerService,
    private apiResponseHandler: ApiResponseHandlerService
  ) {
    super(http, errorHandler);
  }

  getHostelFacilities(params: any = {}): Observable<any> {
    let httpParams = new HttpParams();

    if (params.type !== undefined) httpParams = httpParams.set('type', params.type.toString());
    if (params.gender !== undefined) httpParams = httpParams.set('gender', params.gender.toString());
    if (params.isActive !== undefined) httpParams = httpParams.set('isActive', params.isActive.toString());

    return this.apiResponseHandler.processResponse<any>(
      this.http.get<ApiResponse<any>>(`${this.apiUrl}/hostel-facilities`, { params: httpParams }),
      false,
      'Failed to retrieve hostel facilities'
    );
  }

  getHostelFacility(id: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.get<ApiResponse<any>>(`${this.apiUrl}/hostel-facilities/${id}`),
      false,
      `Failed to retrieve hostel facility with ID ${id}`
    );
  }

  createHostelFacility(facility: any): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.post<ApiResponse<any>>(`${this.apiUrl}/hostel-facilities`, facility),
      true,
      'Failed to create hostel facility'
    );
  }

  updateHostelFacility(id: number, facility: any): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/hostel-facilities/${id}`, facility),
      true,
      'Failed to update hostel facility'
    );
  }

  deleteHostelFacility(id: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/hostel-facilities/${id}`),
      true,
      'Failed to delete hostel facility'
    );
  }

  addHostelFacilityTranslation(facilityId: number, translation: any): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.post<ApiResponse<any>>(`${this.apiUrl}/hostel-facilities/${facilityId}/translations`, translation),
      true,
      'Failed to add hostel facility translation'
    );
  }

  updateHostelFacilityTranslation(facilityId: number, translationId: number, translation: any): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/hostel-facilities/${facilityId}/translations/${translationId}`, translation),
      true,
      'Failed to update hostel facility translation'
    );
  }

  deleteHostelFacilityTranslation(facilityId: number, translationId: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/hostel-facilities/${facilityId}/translations/${translationId}`),
      true,
      'Failed to delete hostel facility translation'
    );
  }
}
