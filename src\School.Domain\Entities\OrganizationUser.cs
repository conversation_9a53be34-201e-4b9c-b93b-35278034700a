using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

/// <summary>
/// Represents the relationship between a user and an organization
/// Allows users to belong to multiple organizations with different roles
/// </summary>
public class OrganizationUser : BaseEntity
{
    /// <summary>
    /// ID of the organization
    /// </summary>
    public Guid OrganizationId { get; set; }

    /// <summary>
    /// ID of the user (from Identity system)
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Role of the user within this organization
    /// </summary>
    public OrganizationRole Role { get; set; } = OrganizationRole.Member;

    /// <summary>
    /// Whether the user is currently active in this organization
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Date when the user joined this organization
    /// </summary>
    public DateTime JoinedDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Date when the user left this organization (if applicable)
    /// </summary>
    public DateTime? LeftDate { get; set; }

    /// <summary>
    /// Whether this is the user's primary organization
    /// </summary>
    public bool IsPrimary { get; set; } = false;

    /// <summary>
    /// Custom permissions for this user in this organization
    /// JSON string containing specific permissions
    /// </summary>
    public string? CustomPermissions { get; set; }

    /// <summary>
    /// Notes about this user's role or access
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Who invited this user to the organization
    /// </summary>
    public string? InvitedBy { get; set; }

    /// <summary>
    /// Date when the invitation was sent
    /// </summary>
    public DateTime? InvitedDate { get; set; }

    /// <summary>
    /// Date when the invitation was accepted
    /// </summary>
    public DateTime? AcceptedDate { get; set; }

    /// <summary>
    /// Navigation properties
    /// </summary>
    public Organization Organization { get; set; } = null!;
}
