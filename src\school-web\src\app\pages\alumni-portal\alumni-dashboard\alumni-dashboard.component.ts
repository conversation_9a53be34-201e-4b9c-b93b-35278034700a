import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { AuthService } from '../../../core/services/auth.service';
import { AlumniService } from '../../../core/services/alumni.service';
import { Alumni } from '../../../core/models/alumni.model';

@Component({
  selector: 'app-alumni-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatGridListModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './alumni-dashboard.component.html',
  styleUrls: ['./alumni-dashboard.component.scss']
})
export class AlumniDashboardComponent implements OnInit {
  alumni: Alumni | null = null;
  loading = true;
  
  dashboardStats = {
    networkConnections: 0,
    upcomingEvents: 0,
    totalDonations: 0,
    jobPostings: 0
  };

  recentActivities = [
    {
      type: 'event',
      title: 'Annual Alumni Reunion',
      description: 'Join us for the annual reunion celebration',
      date: new Date('2024-06-15'),
      icon: 'event'
    },
    {
      type: 'network',
      title: 'New Connection Request',
      description: 'John Smith (Class of 2015) wants to connect',
      date: new Date('2024-01-10'),
      icon: 'people'
    },
    {
      type: 'job',
      title: 'New Job Posting',
      description: 'Software Engineer position at Tech Corp',
      date: new Date('2024-01-08'),
      icon: 'work'
    }
  ];

  quickActions = [
    {
      title: 'Update Profile',
      description: 'Keep your information current',
      icon: 'person',
      route: '/alumni-portal/profile',
      color: 'primary'
    },
    {
      title: 'Browse Network',
      description: 'Connect with fellow alumni',
      icon: 'people',
      route: '/alumni-portal/network',
      color: 'accent'
    },
    {
      title: 'View Events',
      description: 'See upcoming alumni events',
      icon: 'event',
      route: '/alumni-portal/events',
      color: 'primary'
    },
    {
      title: 'Make Donation',
      description: 'Support your alma mater',
      icon: 'volunteer_activism',
      route: '/alumni-portal/donations',
      color: 'warn'
    }
  ];

  constructor(
    private authService: AuthService,
    private alumniService: AlumniService
  ) { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    this.loading = true;
    
    const currentUser = this.authService.getCurrentUser();
    if (currentUser) {
      this.alumniService.getAlumniById(currentUser.id).subscribe({
        next: (alumni) => {
          this.alumni = alumni;
          this.loadDashboardStats();
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading alumni data:', error);
          this.loading = false;
        }
      });
    }
  }

  loadDashboardStats(): void {
    // In a real application, these would be API calls
    this.dashboardStats = {
      networkConnections: 45,
      upcomingEvents: 3,
      totalDonations: 2500,
      jobPostings: 12
    };
  }
}
