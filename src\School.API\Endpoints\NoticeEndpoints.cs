using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Notice;
using System;
using System.Threading.Tasks;

namespace School.API.Endpoints;

public class NoticeEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/notices")
            .WithTags("Notices");

        group.MapGet("/", async ([AsParameters] NoticeFilterDto filter, [FromServices] INoticeService noticeService) =>
        {
            try
            {
                var (items, totalCount) = await noticeService.GetAllNoticesAsync(filter);
                var response = new
                {
                    TotalCount = totalCount,
                    Page = filter.Page,
                    PageSize = filter.PageSize,
                    TotalPages = (int)Math.Ceiling(totalCount / (double)filter.PageSize),
                    Items = items
                };
                return ApiResults.ApiOk(response, "Notices retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error retrieving notices: {ex.Message}");
            }
        })
        .WithName("GetAllNotices")
        .WithOpenApi();

        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] INoticeService noticeService) =>
        {
            try
            {
                var notice = await noticeService.GetNoticeByIdAsync(id);
                if (notice == null)
                {
                    return ApiResults.ApiNotFound("Notice not found");
                }
                return ApiResults.ApiOk(notice, "Notice retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error retrieving notice: {ex.Message}");
            }
        })
        .WithName("GetNoticeById")
        .WithOpenApi();

        group.MapPost("/", async ([FromBody] NoticeCreateDto noticeDto, [FromServices] ICurrentUserService currentUserService, [FromServices] INoticeService noticeService) =>
        {
            try
            {
                if (!currentUserService.IsAuthenticated || !currentUserService.UserId.HasValue)
                {
                    return ApiResults.ApiUnauthorized("User not authenticated");
                }

                // Get the current user ID
                var userId = currentUserService.UserId;
                if (userId == null)
                {
                    return ApiResults.ApiBadRequest("Invalid user ID");
                }

                var noticeId = await noticeService.CreateNoticeAsync(noticeDto, userId.Value);
                return ApiResults.ApiCreated(new { id = noticeId }, $"/api/notices/{noticeId}", "Notice created successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error creating notice: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("CreateNotice")
        .WithOpenApi();

        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] NoticeUpdateDto noticeDto, [FromServices] ICurrentUserService currentUserService, [FromServices] INoticeService noticeService) =>
        {
            try
            {
                if (!currentUserService.IsAuthenticated || !currentUserService.UserId.HasValue)
                {
                    return ApiResults.ApiUnauthorized("User not authenticated");
                }

                var updated = await noticeService.UpdateNoticeAsync(id, noticeDto);
                if (!updated)
                {
                    return ApiResults.ApiNotFound("Notice not found");
                }
                return ApiResults.ApiOk(new { id }, "Notice updated successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error updating notice: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateNotice")
        .WithOpenApi();

        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] ICurrentUserService currentUserService, [FromServices] INoticeService noticeService) =>
        {
            try
            {
                if (!currentUserService.IsAuthenticated || !currentUserService.UserId.HasValue)
                {
                    return ApiResults.ApiUnauthorized("User not authenticated");
                }

                var deleted = await noticeService.DeleteNoticeAsync(id);
                if (!deleted)
                {
                    return ApiResults.ApiNotFound("Notice not found");
                }
                return ApiResults.ApiOk(new { id }, "Notice deleted successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error deleting notice: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("DeleteNotice")
        .WithOpenApi();
    }
}
