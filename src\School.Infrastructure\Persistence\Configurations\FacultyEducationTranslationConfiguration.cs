using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class FacultyEducationTranslationConfiguration : IEntityTypeConfiguration<FacultyEducationTranslation>
{
    public void Configure(EntityTypeBuilder<FacultyEducationTranslation> builder)
    {
        builder.Property(t => t.LanguageCode)
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(t => t.Degree)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(t => t.Institution)
            .HasMaxLength(200)
            .IsRequired();

        builder.HasOne(t => t.FacultyEducation)
            .WithMany()
            .HasForeignKey(t => t.FacultyEducationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(t => new { t.FacultyEducationId, t.LanguageCode })
            .IsUnique();

        builder.HasQueryFilter(fet => !fet.IsDeleted);
    }
}
