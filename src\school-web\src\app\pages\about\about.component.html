<!-- Default Hero Section -->
<app-default-hero
  [translationPrefix]="'ABOUT'"
  [buttons]="[
    {label: 'ABOUT.EXPLORE_HISTORY' | translate, link: '#history', isPrimary: true, icon: 'history'},
    {label: 'ABOUT.MEET_LEADERSHIP' | translate, link: '#leadership', isPrimary: false}
  ]">
</app-default-hero>

<!-- Floating Navigation Sidebar -->
<div class="floating-nav" [class.visible]="showFloatingNav">
  <div class="floating-nav-content">
    <h3>{{ 'ABOUT.QUICK_NAV' | translate }}</h3>
    <ul class="floating-nav-links">
      <li>
        <a (click)="scrollToSection('history')" [class.active]="activeSection === 'history'">
          <mat-icon>history</mat-icon>
          <span>{{ 'ABOUT.HISTORY' | translate }}</span>
        </a>
      </li>
      <li>
        <a (click)="scrollToSection('mission')" [class.active]="activeSection === 'mission'">
          <mat-icon>lightbulb</mat-icon>
          <span>{{ 'ABOUT.MISSION' | translate }}</span>
        </a>
      </li>
      <li>
        <a (click)="scrollToSection('leadership')" [class.active]="activeSection === 'leadership'">
          <mat-icon>people</mat-icon>
          <span>{{ 'ABOUT.LEADERSHIP' | translate }}</span>
        </a>
      </li>
    </ul>
  </div>
</div>

<!-- Main Content -->
<div class="container">
  <!-- History Section -->
  <section id="history" class="content-section">
    <h2>{{ 'ABOUT.HISTORY' | translate }}</h2>
    <div class="history-timeline">
      <div class="timeline-item" *ngFor="let event of historyTimeline">
        <div class="timeline-year">
          <span>{{event.year}}</span>
        </div>
        <div class="timeline-content">
          <h3>{{event.title}}</h3>
          <p>{{event.description}}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Mission & Vision Section -->
  <section id="mission" class="content-section">
    <h2>{{ 'ABOUT.MISSION_VISION' | translate }}</h2>

    <div class="mission-vision-container">
      <div class="mission-box">
        <h3>{{ 'ABOUT.OUR_MISSION' | translate }}</h3>
        <p>{{ 'ABOUT.MISSION_TEXT' | translate }}</p>
      </div>

      <div class="vision-box">
        <h3>{{ 'ABOUT.OUR_VISION' | translate }}</h3>
        <p>{{ 'ABOUT.VISION_TEXT' | translate }}</p>
      </div>
    </div>

    <div class="values-container">
      <h3>{{ 'ABOUT.OUR_VALUES' | translate }}</h3>
      <div class="values-grid">
        <div class="value-item">
          <mat-icon>school</mat-icon>
          <h4>{{ 'ABOUT.VALUE_EXCELLENCE' | translate }}</h4>
          <p>{{ 'ABOUT.VALUE_EXCELLENCE_DESC' | translate }}</p>
        </div>
        <div class="value-item">
          <mat-icon>diversity_3</mat-icon>
          <h4>{{ 'ABOUT.VALUE_INCLUSION' | translate }}</h4>
          <p>{{ 'ABOUT.VALUE_INCLUSION_DESC' | translate }}</p>
        </div>
        <div class="value-item">
          <mat-icon>psychology</mat-icon>
          <h4>{{ 'ABOUT.VALUE_INNOVATION' | translate }}</h4>
          <p>{{ 'ABOUT.VALUE_INNOVATION_DESC' | translate }}</p>
        </div>
        <div class="value-item">
          <mat-icon>handshake</mat-icon>
          <h4>{{ 'ABOUT.VALUE_INTEGRITY' | translate }}</h4>
          <p>{{ 'ABOUT.VALUE_INTEGRITY_DESC' | translate }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Leadership Section -->
  <section id="leadership" class="content-section">
    <h2>{{ 'ABOUT.LEADERSHIP' | translate }}</h2>
    <p class="section-intro">{{ 'ABOUT.LEADERSHIP_INTRO' | translate }}</p>

    <div class="leadership-grid">
      <mat-card class="leader-card" *ngFor="let leader of leadershipTeam">
        <img mat-card-image [src]="leader.image" [alt]="leader.name">
        <mat-card-content>
          <h3>{{leader.name}}</h3>
          <h4>{{leader.title}}</h4>
          <p>{{leader.bio}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>
</div>
