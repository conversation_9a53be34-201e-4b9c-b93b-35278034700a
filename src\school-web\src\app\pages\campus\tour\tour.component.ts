import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../../shared/components/default-hero/default-hero.component';

@Component({
  selector: 'app-tour',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './tour.component.html',
  styleUrls: ['./tour.component.scss']
})
export class TourComponent {
  // Tour locations
  tourLocations = [
    {
      name: 'Main Academic Building',
      description: 'Our main academic building houses classrooms, laboratories, and administrative offices in a modern, technology-enhanced environment designed to support collaborative learning.',
      image: 'assets/images/campus/tour/academic-building.jpg',
      features: [
        'Smart classrooms with interactive technology',
        'Science and computer laboratories',
        'Administrative offices',
        'Student support services'
      ]
    },
    {
      name: 'Library and Media Center',
      description: 'Our state-of-the-art library and media center provides resources, research support, and collaborative spaces for students to explore, discover, and create.',
      image: 'assets/images/campus/tour/library.jpg',
      features: [
        'Extensive print and digital collections',
        'Research and study spaces',
        'Media production studio',
        'Technology resources'
      ]
    },
    {
      name: 'Arts Center',
      description: 'The arts center houses facilities for visual arts, music, and theater, providing spaces for creative expression and artistic development.',
      image: 'assets/images/campus/tour/arts-center.jpg',
      features: [
        'Visual arts studios',
        'Music rehearsal rooms',
        'Theater with professional lighting and sound',
        'Gallery space for student exhibitions'
      ]
    },
    {
      name: 'Athletic Complex',
      description: 'Our athletic complex includes indoor and outdoor facilities that support physical education, competitive sports, and recreational activities.',
      image: 'assets/images/campus/tour/athletic-complex.jpg',
      features: [
        'Gymnasium with basketball and volleyball courts',
        'Soccer and cricket fields',
        'Fitness center with modern equipment',
        'Locker rooms and team meeting spaces'
      ]
    },
    {
      name: 'Student Center',
      description: 'The student center serves as a hub for student life, providing spaces for dining, socializing, club activities, and student services.',
      image: 'assets/images/campus/tour/student-center.jpg',
      features: [
        'Dining hall and café',
        'Student lounge areas',
        'Club meeting rooms',
        'Student government offices'
      ]
    },
    {
      name: 'Residential Halls',
      description: 'Our residential halls provide comfortable, supportive living environments for boarding students, with spaces designed to foster community and academic success.',
      image: 'assets/images/campus/tour/residential-halls.jpg',
      features: [
        'Comfortable student rooms',
        'Common lounges and study areas',
        'Resident faculty apartments',
        'Laundry and kitchen facilities'
      ]
    }
  ];

  // Virtual tour videos
  tourVideos = [
    {
      title: 'Campus Overview',
      description: 'Take a bird\'s eye view of our beautiful campus and see all our facilities in this comprehensive tour.',
      thumbnail: 'assets/images/campus/tour/video-campus.jpg',
      videoUrl: 'https://www.youtube.com/embed/example1'
    },
    {
      title: 'Academic Facilities',
      description: 'Explore our classrooms, laboratories, and learning spaces in this detailed tour of our academic facilities.',
      thumbnail: 'assets/images/campus/tour/video-academic.jpg',
      videoUrl: 'https://www.youtube.com/embed/example2'
    },
    {
      title: 'Student Life',
      description: 'Get a glimpse of daily life for our students, from classes to extracurricular activities and residential life.',
      thumbnail: 'assets/images/campus/tour/video-student-life.jpg',
      videoUrl: 'https://www.youtube.com/embed/example3'
    }
  ];

  // 360° panoramas
  panoramas = [
    {
      title: 'Main Entrance',
      description: 'View our welcoming main entrance and administrative reception area.',
      image: 'assets/images/campus/tour/panorama-entrance.jpg'
    },
    {
      title: 'Library Reading Room',
      description: 'Explore our spacious library reading room with study areas and research resources.',
      image: 'assets/images/campus/tour/panorama-library.jpg'
    },
    {
      title: 'Science Laboratory',
      description: 'Take a look at one of our fully-equipped science laboratories.',
      image: 'assets/images/campus/tour/panorama-lab.jpg'
    },
    {
      title: 'Auditorium',
      description: 'View our auditorium where school assemblies, performances, and special events take place.',
      image: 'assets/images/campus/tour/panorama-auditorium.jpg'
    }
  ];
}
