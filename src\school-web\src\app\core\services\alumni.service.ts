import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { ErrorHandlerService } from './error-handler.service';
import { ApiResponseHandlerService } from './api-response-handler.service';
import {
  Alumni,
  AlumniFilter,
  AlumniTestimonial,
  CreateAlumni,
  UpdateAlumni,
  CreateAlumniTestimonial,
  UpdateAlumniTestimonial,
  CreateAlumniTranslation,
  UpdateAlumniTranslation
} from '../models/alumni.model';
import { ApiResponse } from '../models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class AlumniService extends BaseApiService {
  constructor(
    protected override http: HttpClient,
    protected override errorHandler: ErrorHandlerService,
    private apiResponseHandler: ApiResponseHandlerService
  ) {
    super(http, errorHandler);
  }

  /**
   * Get all alumni with filtering and pagination
   */
  getAllAlumni(filter: AlumniFilter = {}): Observable<{ totalCount: number, items: Alumni[] }> {
    const params = this.buildParams(filter);
    return this.apiResponseHandler.processResponse<{ totalCount: number, items: Alumni[] }>(
      this.http.get<ApiResponse<{ totalCount: number, items: Alumni[] }>>(`${this.apiUrl}/alumni`, { params }),
      false,
      'Failed to retrieve alumni'
    );
  }

  /**
   * Get alumni by ID
   */
  getAlumniById(id: number): Observable<Alumni> {
    return this.apiResponseHandler.processResponse<Alumni>(
      this.http.get<ApiResponse<Alumni>>(`${this.apiUrl}/alumni/${id}`),
      false,
      `Failed to retrieve alumni with ID ${id}`
    );
  }

  /**
   * Create new alumni
   */
  createAlumni(alumni: CreateAlumni): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.post<ApiResponse<{ id: number }>>(`${this.apiUrl}/alumni`, alumni),
      true,
      'Alumni created successfully'
    );
  }

  /**
   * Update alumni
   */
  updateAlumni(id: number, alumni: UpdateAlumni): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.put<ApiResponse<{ id: number }>>(`${this.apiUrl}/alumni/${id}`, alumni),
      true,
      'Alumni updated successfully'
    );
  }

  /**
   * Delete alumni
   */
  deleteAlumni(id: number): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.delete<ApiResponse<{ id: number }>>(`${this.apiUrl}/alumni/${id}`),
      true,
      'Alumni deleted successfully'
    );
  }

  /**
   * Get testimonial by ID
   */
  getTestimonialById(id: number): Observable<AlumniTestimonial> {
    return this.apiResponseHandler.processResponse<AlumniTestimonial>(
      this.http.get<ApiResponse<AlumniTestimonial>>(`${this.apiUrl}/alumni/testimonials/${id}`),
      false,
      `Failed to retrieve testimonial with ID ${id}`
    );
  }

  /**
   * Create new testimonial
   */
  createTestimonial(testimonial: CreateAlumniTestimonial): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.post<ApiResponse<{ id: number }>>(`${this.apiUrl}/alumni/testimonials`, testimonial),
      true,
      'Testimonial created successfully'
    );
  }

  /**
   * Update testimonial
   */
  updateTestimonial(id: number, testimonial: UpdateAlumniTestimonial): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.put<ApiResponse<{ id: number }>>(`${this.apiUrl}/alumni/testimonials/${id}`, testimonial),
      true,
      'Testimonial updated successfully'
    );
  }

  /**
   * Delete testimonial
   */
  deleteTestimonial(id: number): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.delete<ApiResponse<{ id: number }>>(`${this.apiUrl}/alumni/testimonials/${id}`),
      true,
      'Testimonial deleted successfully'
    );
  }

  /**
   * Add translation to alumni
   */
  addTranslation(alumniId: number, translation: CreateAlumniTranslation): Observable<{ alumniId: number, languageCode: string }> {
    return this.apiResponseHandler.processResponse<{ alumniId: number, languageCode: string }>(
      this.http.post<ApiResponse<{ alumniId: number, languageCode: string }>>(`${this.apiUrl}/alumni/${alumniId}/translations`, translation),
      true,
      'Translation added successfully'
    );
  }

  /**
   * Update translation
   */
  updateTranslation(alumniId: number, languageCode: string, translation: UpdateAlumniTranslation): Observable<{ alumniId: number, languageCode: string }> {
    return this.apiResponseHandler.processResponse<{ alumniId: number, languageCode: string }>(
      this.http.put<ApiResponse<{ alumniId: number, languageCode: string }>>(`${this.apiUrl}/alumni/${alumniId}/translations/${languageCode}`, translation),
      true,
      'Translation updated successfully'
    );
  }

  /**
   * Delete translation
   */
  deleteTranslation(alumniId: number, languageCode: string): Observable<{ alumniId: number, languageCode: string }> {
    return this.apiResponseHandler.processResponse<{ alumniId: number, languageCode: string }>(
      this.http.delete<ApiResponse<{ alumniId: number, languageCode: string }>>(`${this.apiUrl}/alumni/${alumniId}/translations/${languageCode}`),
      true,
      'Translation deleted successfully'
    );
  }


}
