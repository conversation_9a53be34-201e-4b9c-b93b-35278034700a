using School.Application.DTOs.Common;
using School.Domain.Enums;

namespace School.Application.DTOs;

public class ParentDto
{
    public Guid Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName => $"{FirstName} {LastName}";
    public GenderType Gender { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string AlternatePhone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string Occupation { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public Guid? ProfileImageId { get; set; }
    public MediaItemDto? ProfileImage { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class ParentDetailDto : ParentDto
{
    public List<ParentStudentDto> Students { get; set; } = new List<ParentStudentDto>();
}

public class ParentStudentDto
{
    public Guid Id { get; set; }
    public Guid StudentId { get; set; }
    public Guid ParentId { get; set; }
    public ParentRelationType RelationType { get; set; }
    public bool IsPrimaryContact { get; set; }
    public StudentDto Student { get; set; } = null!;
}

public class CreateParentDto
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public GenderType Gender { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string AlternatePhone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string Occupation { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public Guid? ProfileImageId { get; set; }
}

public class UpdateParentDto
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public GenderType Gender { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string AlternatePhone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string Occupation { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public Guid? ProfileImageId { get; set; }
}

/// <summary>
/// Filter DTO for parent queries
/// </summary>
public class ParentFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filter by parent name (first or last)
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Filter by email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Filter by phone
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter by student ID (to find parents of a specific student)
    /// </summary>
    public Guid? StudentId { get; set; }
}
