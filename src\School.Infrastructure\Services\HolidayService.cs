using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.Common.Models;
using School.Application.DTOs;
using School.Domain.Entities;
using School.Domain.Enums;

namespace School.Infrastructure.Services;

public interface IHolidayService
{
    Task<PagedList<HolidayDto>> GetHolidaysAsync(HolidayFilterDto filter);
    Task<HolidayDto?> GetHolidayByIdAsync(Guid id);
    Task<IEnumerable<HolidayDto>> GetHolidaysByAcademicYearAsync(Guid academicYearId);
    Task<IEnumerable<HolidayDto>> GetHolidaysByTermAsync(Guid termId);
    Task<IEnumerable<HolidayDto>> GetHolidaysByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<IEnumerable<HolidayEventDto>> GetHolidayEventsAsync(DateTime startDate, DateTime endDate, Guid? academicYearId = null, Guid? termId = null);
    Task<Guid> CreateHolidayAsync(CreateHolidayDto holidayDto);
    Task<bool> UpdateHolidayAsync(Guid id, UpdateHolidayDto holidayDto);
    Task<bool> DeleteHolidayAsync(Guid id);
    Task<bool> ActivateHolidayAsync(Guid id);
    Task<bool> DeactivateHolidayAsync(Guid id);
    
    // Holiday validation
    Task<bool> ValidateHolidayDatesAsync(Guid? academicYearId, Guid? termId, DateTime startDate, DateTime endDate, Guid? excludeId = null);
    Task<IEnumerable<HolidayDto>> GetOverlappingHolidaysAsync(Guid? academicYearId, Guid? termId, DateTime startDate, DateTime endDate, Guid? excludeId = null);
    
    // Recurring holidays
    Task<IEnumerable<HolidayDto>> GenerateRecurringHolidaysAsync(Guid holidayId, DateTime fromDate, DateTime toDate);
    Task<bool> UpdateRecurringHolidaySeriesAsync(Guid holidayId, UpdateHolidayDto holidayDto, bool updateSeries = false);
    
    // Translation management
    Task<bool> AddTranslationAsync(Guid holidayId, CreateHolidayTranslationDto translationDto);
    Task<bool> UpdateTranslationAsync(Guid holidayId, string languageCode, UpdateHolidayTranslationDto translationDto);
    Task<bool> DeleteTranslationAsync(Guid holidayId, string languageCode);
    Task<IEnumerable<HolidayTranslationDto>> GetTranslationsAsync(Guid holidayId);
    
    // Statistics
    Task<Dictionary<string, int>> GetHolidayStatisticsAsync(Guid? academicYearId = null, Guid? termId = null);
    Task<int> GetTotalHolidaysInPeriodAsync(DateTime startDate, DateTime endDate, Guid? academicYearId = null, Guid? termId = null);
}

public class HolidayService : IHolidayService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<HolidayService> _logger;

    public HolidayService(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        ILogger<HolidayService> logger)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<PagedList<HolidayDto>> GetHolidaysAsync(HolidayFilterDto filter)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var query = repository.AsQueryable()
            .Include(h => h.AcademicYear)
            .Include(h => h.Term)
            .Include(h => h.Translations)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(filter.Name))
        {
            query = query.Where(h => h.Name.Contains(filter.Name));
        }

        if (filter.Type.HasValue)
        {
            query = query.Where(h => h.Type == filter.Type.Value);
        }

        if (filter.AcademicYearId.HasValue)
        {
            query = query.Where(h => h.AcademicYearId == filter.AcademicYearId.Value);
        }

        if (filter.TermId.HasValue)
        {
            query = query.Where(h => h.TermId == filter.TermId.Value);
        }

        if (filter.StartDate.HasValue)
        {
            query = query.Where(h => h.StartDate >= filter.StartDate.Value);
        }

        if (filter.EndDate.HasValue)
        {
            query = query.Where(h => h.EndDate <= filter.EndDate.Value);
        }

        if (filter.IsActive.HasValue)
        {
            query = query.Where(h => h.IsActive == filter.IsActive.Value);
        }

        if (filter.IsPublic.HasValue)
        {
            query = query.Where(h => h.IsPublic == filter.IsPublic.Value);
        }

        if (filter.IsRecurring.HasValue)
        {
            query = query.Where(h => h.IsRecurring == filter.IsRecurring.Value);
        }

        // Apply sorting
        if (!string.IsNullOrEmpty(filter.SortBy))
        {
            var isDescending = filter.SortDirection?.ToLower() == "desc";
            query = filter.SortBy.ToLower() switch
            {
                "name" => isDescending ? query.OrderByDescending(h => h.Name) : query.OrderBy(h => h.Name),
                "startdate" => isDescending ? query.OrderByDescending(h => h.StartDate) : query.OrderBy(h => h.StartDate),
                "enddate" => isDescending ? query.OrderByDescending(h => h.EndDate) : query.OrderBy(h => h.EndDate),
                "type" => isDescending ? query.OrderByDescending(h => h.Type) : query.OrderBy(h => h.Type),
                "createdat" => isDescending ? query.OrderByDescending(h => h.CreatedAt) : query.OrderBy(h => h.CreatedAt),
                _ => query.OrderBy(h => h.StartDate)
            };
        }
        else
        {
            query = query.OrderBy(h => h.StartDate);
        }

        var totalCount = await query.CountAsync();
        var holidays = await query
            .Skip((filter.Page - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .Select(h => new HolidayDto
            {
                Id = h.Id,
                Name = h.Name,
                Description = h.Description,
                StartDate = h.StartDate,
                EndDate = h.EndDate,
                Type = h.Type,
                IsRecurring = h.IsRecurring,
                RecurrencePattern = h.RecurrencePattern,
                Color = h.Color,
                IsActive = h.IsActive,
                IsPublic = h.IsPublic,
                Remarks = h.Remarks,
                AcademicYearId = h.AcademicYearId,
                TermId = h.TermId,
                AcademicYearName = h.AcademicYear != null ? h.AcademicYear.DisplayName : string.Empty,
                TermName = h.Term != null ? h.Term.Name : string.Empty,
                CreatedAt = h.CreatedAt,
                LastModifiedAt = h.LastModifiedAt,
                Translations = h.Translations.Select(t => new HolidayTranslationDto
                {
                    Id = t.Id,
                    HolidayId = t.HolidayId,
                    LanguageCode = t.LanguageCode,
                    Name = t.Name,
                    Description = t.Description,
                    Remarks = t.Remarks
                }).ToList()
            })
            .ToListAsync();

        return new PagedList<HolidayDto>(holidays, totalCount, filter.Page, filter.PageSize);
    }

    public async Task<HolidayDto?> GetHolidayByIdAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var holiday = await repository.AsQueryable()
            .Include(h => h.AcademicYear)
            .Include(h => h.Term)
            .Include(h => h.Translations)
            .FirstOrDefaultAsync(h => h.Id == id);

        if (holiday == null)
            return null;

        return new HolidayDto
        {
            Id = holiday.Id,
            Name = holiday.Name,
            Description = holiday.Description,
            StartDate = holiday.StartDate,
            EndDate = holiday.EndDate,
            Type = holiday.Type,
            IsRecurring = holiday.IsRecurring,
            RecurrencePattern = holiday.RecurrencePattern,
            Color = holiday.Color,
            IsActive = holiday.IsActive,
            IsPublic = holiday.IsPublic,
            Remarks = holiday.Remarks,
            AcademicYearId = holiday.AcademicYearId,
            TermId = holiday.TermId,
            AcademicYearName = holiday.AcademicYear?.DisplayName ?? string.Empty,
            TermName = holiday.Term?.Name ?? string.Empty,
            CreatedAt = holiday.CreatedAt,
            LastModifiedAt = holiday.LastModifiedAt,
            Translations = holiday.Translations.Select(t => new HolidayTranslationDto
            {
                Id = t.Id,
                HolidayId = t.HolidayId,
                LanguageCode = t.LanguageCode,
                Name = t.Name,
                Description = t.Description,
                Remarks = t.Remarks
            }).ToList()
        };
    }

    public async Task<IEnumerable<HolidayDto>> GetHolidaysByAcademicYearAsync(Guid academicYearId)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var holidays = await repository.AsQueryable()
            .Include(h => h.AcademicYear)
            .Include(h => h.Term)
            .Where(h => h.AcademicYearId == academicYearId && h.IsActive)
            .OrderBy(h => h.StartDate)
            .Select(h => new HolidayDto
            {
                Id = h.Id,
                Name = h.Name,
                Description = h.Description,
                StartDate = h.StartDate,
                EndDate = h.EndDate,
                Type = h.Type,
                IsRecurring = h.IsRecurring,
                Color = h.Color,
                IsActive = h.IsActive,
                IsPublic = h.IsPublic,
                AcademicYearId = h.AcademicYearId,
                TermId = h.TermId,
                AcademicYearName = h.AcademicYear != null ? h.AcademicYear.DisplayName : string.Empty,
                TermName = h.Term != null ? h.Term.Name : string.Empty,
                CreatedAt = h.CreatedAt,
                LastModifiedAt = h.LastModifiedAt
            })
            .ToListAsync();

        return holidays;
    }

    public async Task<IEnumerable<HolidayDto>> GetHolidaysByTermAsync(Guid termId)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var holidays = await repository.AsQueryable()
            .Include(h => h.AcademicYear)
            .Include(h => h.Term)
            .Where(h => h.TermId == termId && h.IsActive)
            .OrderBy(h => h.StartDate)
            .Select(h => new HolidayDto
            {
                Id = h.Id,
                Name = h.Name,
                Description = h.Description,
                StartDate = h.StartDate,
                EndDate = h.EndDate,
                Type = h.Type,
                IsRecurring = h.IsRecurring,
                Color = h.Color,
                IsActive = h.IsActive,
                IsPublic = h.IsPublic,
                AcademicYearId = h.AcademicYearId,
                TermId = h.TermId,
                AcademicYearName = h.AcademicYear != null ? h.AcademicYear.DisplayName : string.Empty,
                TermName = h.Term != null ? h.Term.Name : string.Empty,
                CreatedAt = h.CreatedAt,
                LastModifiedAt = h.LastModifiedAt
            })
            .ToListAsync();

        return holidays;
    }

    public async Task<IEnumerable<HolidayDto>> GetHolidaysByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var holidays = await repository.AsQueryable()
            .Include(h => h.AcademicYear)
            .Include(h => h.Term)
            .Where(h => h.IsActive && h.IsPublic &&
                       ((h.StartDate >= startDate && h.StartDate <= endDate) ||
                        (h.EndDate >= startDate && h.EndDate <= endDate) ||
                        (h.StartDate <= startDate && h.EndDate >= endDate)))
            .OrderBy(h => h.StartDate)
            .Select(h => new HolidayDto
            {
                Id = h.Id,
                Name = h.Name,
                Description = h.Description,
                StartDate = h.StartDate,
                EndDate = h.EndDate,
                Type = h.Type,
                IsRecurring = h.IsRecurring,
                Color = h.Color,
                IsActive = h.IsActive,
                IsPublic = h.IsPublic,
                AcademicYearId = h.AcademicYearId,
                TermId = h.TermId,
                AcademicYearName = h.AcademicYear != null ? h.AcademicYear.DisplayName : string.Empty,
                TermName = h.Term != null ? h.Term.Name : string.Empty,
                CreatedAt = h.CreatedAt,
                LastModifiedAt = h.LastModifiedAt
            })
            .ToListAsync();

        return holidays;
    }

    public async Task<IEnumerable<HolidayEventDto>> GetHolidayEventsAsync(DateTime startDate, DateTime endDate, Guid? academicYearId = null, Guid? termId = null)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var query = repository.AsQueryable()
            .Where(h => h.IsActive && h.IsPublic &&
                       ((h.StartDate >= startDate && h.StartDate <= endDate) ||
                        (h.EndDate >= startDate && h.EndDate <= endDate) ||
                        (h.StartDate <= startDate && h.EndDate >= endDate)));

        if (academicYearId.HasValue)
        {
            query = query.Where(h => h.AcademicYearId == academicYearId.Value);
        }

        if (termId.HasValue)
        {
            query = query.Where(h => h.TermId == termId.Value);
        }

        var holidays = await query
            .OrderBy(h => h.StartDate)
            .Select(h => new HolidayEventDto
            {
                Id = h.Id,
                Name = h.Name,
                Start = h.StartDate,
                End = h.EndDate,
                AllDay = h.StartDate.Date == h.EndDate.Date,
                Type = h.Type,
                Color = h.Color,
                IsRecurring = h.IsRecurring
            })
            .ToListAsync();

        return holidays;
    }

    public async Task<Guid> CreateHolidayAsync(CreateHolidayDto holidayDto)
    {
        var repository = _unitOfWork.Repository<Holiday>();

        // Validate dates
        if (!await ValidateHolidayDatesAsync(holidayDto.AcademicYearId, holidayDto.TermId, holidayDto.StartDate, holidayDto.EndDate))
        {
            throw new InvalidOperationException("Holiday dates overlap with existing holiday in the same period.");
        }

        var holiday = new Holiday
        {
            Name = holidayDto.Name,
            Description = holidayDto.Description,
            StartDate = holidayDto.StartDate,
            EndDate = holidayDto.EndDate,
            Type = holidayDto.Type,
            IsRecurring = holidayDto.IsRecurring,
            RecurrencePattern = holidayDto.RecurrencePattern,
            Color = holidayDto.Color,
            IsActive = holidayDto.IsActive,
            IsPublic = holidayDto.IsPublic,
            Remarks = holidayDto.Remarks,
            AcademicYearId = holidayDto.AcademicYearId,
            TermId = holidayDto.TermId,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _currentUserService.UserId.ToString()
        };

        await repository.AddAsync(holiday);
        await _unitOfWork.SaveChangesAsync();

        // Add translations if provided
        if (holidayDto.Translations?.Any() == true)
        {
            foreach (var translation in holidayDto.Translations)
            {
                await AddTranslationAsync(holiday.Id, translation);
            }
        }

        _logger.LogInformation("Holiday created with ID {HolidayId}", holiday.Id);
        return holiday.Id;
    }

    public async Task<bool> UpdateHolidayAsync(Guid id, UpdateHolidayDto holidayDto)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var holiday = await repository.AsQueryable().FirstOrDefaultAsync(h => h.Id == id);

        if (holiday == null)
            return false;

        // Validate dates if changed
        if (holiday.StartDate != holidayDto.StartDate || holiday.EndDate != holidayDto.EndDate ||
            holiday.AcademicYearId != holidayDto.AcademicYearId || holiday.TermId != holidayDto.TermId)
        {
            if (!await ValidateHolidayDatesAsync(holidayDto.AcademicYearId, holidayDto.TermId, holidayDto.StartDate, holidayDto.EndDate, id))
            {
                throw new InvalidOperationException("Holiday dates overlap with existing holiday in the same period.");
            }
        }

        holiday.Name = holidayDto.Name;
        holiday.Description = holidayDto.Description;
        holiday.StartDate = holidayDto.StartDate;
        holiday.EndDate = holidayDto.EndDate;
        holiday.Type = holidayDto.Type;
        holiday.IsRecurring = holidayDto.IsRecurring;
        holiday.RecurrencePattern = holidayDto.RecurrencePattern;
        holiday.Color = holidayDto.Color;
        holiday.IsActive = holidayDto.IsActive;
        holiday.IsPublic = holidayDto.IsPublic;
        holiday.Remarks = holidayDto.Remarks;
        holiday.AcademicYearId = holidayDto.AcademicYearId;
        holiday.TermId = holidayDto.TermId;
        holiday.LastModifiedAt = DateTime.UtcNow;
        holiday.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(holiday);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Holiday updated with ID {HolidayId}", holiday.Id);
        return true;
    }

    public async Task<bool> DeleteHolidayAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        await repository.DeleteByIdAsync(id);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Holiday deleted with ID {HolidayId}", id);
        return true;
    }

    public async Task<bool> ActivateHolidayAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var holiday = await repository.AsQueryable().FirstOrDefaultAsync(h => h.Id == id);

        if (holiday == null)
            return false;

        holiday.IsActive = true;
        holiday.LastModifiedAt = DateTime.UtcNow;
        holiday.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(holiday);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Holiday activated with ID {HolidayId}", id);
        return true;
    }

    public async Task<bool> DeactivateHolidayAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var holiday = await repository.AsQueryable().FirstOrDefaultAsync(h => h.Id == id);

        if (holiday == null)
            return false;

        holiday.IsActive = false;
        holiday.LastModifiedAt = DateTime.UtcNow;
        holiday.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(holiday);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Holiday deactivated with ID {HolidayId}", id);
        return true;
    }

    public async Task<bool> ValidateHolidayDatesAsync(Guid? academicYearId, Guid? termId, DateTime startDate, DateTime endDate, Guid? excludeId = null)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var query = repository.AsQueryable()
            .Where(h => h.IsActive &&
                       ((h.StartDate >= startDate && h.StartDate <= endDate) ||
                        (h.EndDate >= startDate && h.EndDate <= endDate) ||
                        (h.StartDate <= startDate && h.EndDate >= endDate)));

        if (excludeId.HasValue)
        {
            query = query.Where(h => h.Id != excludeId.Value);
        }

        if (academicYearId.HasValue)
        {
            query = query.Where(h => h.AcademicYearId == academicYearId.Value);
        }

        if (termId.HasValue)
        {
            query = query.Where(h => h.TermId == termId.Value);
        }

        var overlappingCount = await query.CountAsync();
        return overlappingCount == 0;
    }

    public async Task<IEnumerable<HolidayDto>> GetOverlappingHolidaysAsync(Guid? academicYearId, Guid? termId, DateTime startDate, DateTime endDate, Guid? excludeId = null)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var query = repository.AsQueryable()
            .Include(h => h.AcademicYear)
            .Include(h => h.Term)
            .Where(h => h.IsActive &&
                       ((h.StartDate >= startDate && h.StartDate <= endDate) ||
                        (h.EndDate >= startDate && h.EndDate <= endDate) ||
                        (h.StartDate <= startDate && h.EndDate >= endDate)));

        if (excludeId.HasValue)
        {
            query = query.Where(h => h.Id != excludeId.Value);
        }

        if (academicYearId.HasValue)
        {
            query = query.Where(h => h.AcademicYearId == academicYearId.Value);
        }

        if (termId.HasValue)
        {
            query = query.Where(h => h.TermId == termId.Value);
        }

        var holidays = await query
            .OrderBy(h => h.StartDate)
            .Select(h => new HolidayDto
            {
                Id = h.Id,
                Name = h.Name,
                Description = h.Description,
                StartDate = h.StartDate,
                EndDate = h.EndDate,
                Type = h.Type,
                IsRecurring = h.IsRecurring,
                Color = h.Color,
                IsActive = h.IsActive,
                IsPublic = h.IsPublic,
                AcademicYearId = h.AcademicYearId,
                TermId = h.TermId,
                AcademicYearName = h.AcademicYear != null ? h.AcademicYear.DisplayName : string.Empty,
                TermName = h.Term != null ? h.Term.Name : string.Empty,
                CreatedAt = h.CreatedAt,
                LastModifiedAt = h.LastModifiedAt
            })
            .ToListAsync();

        return holidays;
    }

    public async Task<IEnumerable<HolidayDto>> GenerateRecurringHolidaysAsync(Guid holidayId, DateTime fromDate, DateTime toDate)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var baseHoliday = await repository.AsQueryable()
            .Include(h => h.AcademicYear)
            .Include(h => h.Term)
            .FirstOrDefaultAsync(h => h.Id == holidayId && h.IsRecurring);

        if (baseHoliday == null || baseHoliday.RecurrencePattern == null)
            return [];

        var generatedHolidays = new List<HolidayDto>();
        var currentDate = fromDate;

        // Simple yearly recurrence implementation
        if (baseHoliday.RecurrencePattern.Type == RecurrenceType.Yearly)
        {
            while (currentDate <= toDate)
            {
                var holidayStart = new DateTime(currentDate.Year, baseHoliday.StartDate.Month, baseHoliday.StartDate.Day);
                var holidayEnd = new DateTime(currentDate.Year, baseHoliday.EndDate.Month, baseHoliday.EndDate.Day);

                if (holidayStart >= fromDate && holidayStart <= toDate)
                {
                    generatedHolidays.Add(new HolidayDto
                    {
                        Id = Guid.NewGuid(), // Generated ID for display purposes
                        Name = baseHoliday.Name,
                        Description = baseHoliday.Description,
                        StartDate = holidayStart,
                        EndDate = holidayEnd,
                        Type = baseHoliday.Type,
                        IsRecurring = true,
                        Color = baseHoliday.Color,
                        IsActive = baseHoliday.IsActive,
                        IsPublic = baseHoliday.IsPublic,
                        AcademicYearId = baseHoliday.AcademicYearId,
                        TermId = baseHoliday.TermId,
                        AcademicYearName = baseHoliday.AcademicYear?.DisplayName ?? string.Empty,
                        TermName = baseHoliday.Term?.Name ?? string.Empty
                    });
                }

                currentDate = currentDate.AddYears(1);
            }
        }

        return generatedHolidays;
    }

    public async Task<bool> UpdateRecurringHolidaySeriesAsync(Guid holidayId, UpdateHolidayDto holidayDto, bool updateSeries = false)
    {
        if (!updateSeries)
        {
            // Update only this instance
            return await UpdateHolidayAsync(holidayId, holidayDto);
        }

        // Update the base recurring holiday
        var repository = _unitOfWork.Repository<Holiday>();
        var baseHoliday = await repository.AsQueryable().FirstOrDefaultAsync(h => h.Id == holidayId && h.IsRecurring);

        if (baseHoliday == null)
            return false;

        // Update the base holiday which will affect all future generated instances
        return await UpdateHolidayAsync(holidayId, holidayDto);
    }

    public async Task<bool> AddTranslationAsync(Guid holidayId, CreateHolidayTranslationDto translationDto)
    {
        var holidayRepository = _unitOfWork.Repository<Holiday>();
        var holiday = await holidayRepository.GetByIdAsync(holidayId);

        if (holiday == null) return false;

        // Check if translation already exists
        var translationRepository = _unitOfWork.Repository<HolidayTranslation>();
        var existingTranslation = await translationRepository.AsQueryable()
            .FirstOrDefaultAsync(t => t.HolidayId == holidayId && t.LanguageCode == translationDto.LanguageCode);

        if (existingTranslation != null)
            return false; // Translation already exists

        var translation = new HolidayTranslation
        {
            HolidayId = holidayId,
            LanguageCode = translationDto.LanguageCode,
            Name = translationDto.Name,
            Description = translationDto.Description,
            Remarks = translationDto.Remarks,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _currentUserService.UserId.ToString()
        };

        await translationRepository.AddAsync(translation);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Translation added for Holiday {HolidayId} in language {LanguageCode}", holidayId, translationDto.LanguageCode);
        return true;
    }

    public async Task<bool> UpdateTranslationAsync(Guid holidayId, string languageCode, UpdateHolidayTranslationDto translationDto)
    {
        var translationRepository = _unitOfWork.Repository<HolidayTranslation>();
        var translation = await translationRepository.AsQueryable()
            .FirstOrDefaultAsync(t => t.HolidayId == holidayId && t.LanguageCode == languageCode);

        if (translation == null)
            return false;

        translation.Name = translationDto.Name;
        translation.Description = translationDto.Description;
        translation.Remarks = translationDto.Remarks;
        translation.LastModifiedAt = DateTime.UtcNow;
        translation.LastModifiedBy = _currentUserService.UserId.ToString();

        await translationRepository.UpdateAsync(translation);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Translation updated for Holiday {HolidayId} in language {LanguageCode}", holidayId, languageCode);
        return true;
    }

    public async Task<bool> DeleteTranslationAsync(Guid holidayId, string languageCode)
    {
        var translationRepository = _unitOfWork.Repository<HolidayTranslation>();
        var translation = await translationRepository.AsQueryable()
            .FirstOrDefaultAsync(t => t.HolidayId == holidayId && t.LanguageCode == languageCode);

        if (translation == null)
            return false;

        await translationRepository.DeleteAsync(translation);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Translation deleted for Holiday {HolidayId} in language {LanguageCode}", holidayId, languageCode);
        return true;
    }

    public async Task<IEnumerable<HolidayTranslationDto>> GetTranslationsAsync(Guid holidayId)
    {
        var translationRepository = _unitOfWork.Repository<HolidayTranslation>();
        var translations = await translationRepository.AsQueryable()
            .Where(t => t.HolidayId == holidayId)
            .Select(t => new HolidayTranslationDto
            {
                Id = t.Id,
                HolidayId = t.HolidayId,
                LanguageCode = t.LanguageCode,
                Name = t.Name,
                Description = t.Description,
                Remarks = t.Remarks
            })
            .ToListAsync();

        return translations;
    }

    public async Task<Dictionary<string, int>> GetHolidayStatisticsAsync(Guid? academicYearId = null, Guid? termId = null)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var query = repository.AsQueryable().Where(h => h.IsActive);

        if (academicYearId.HasValue)
        {
            query = query.Where(h => h.AcademicYearId == academicYearId.Value);
        }

        if (termId.HasValue)
        {
            query = query.Where(h => h.TermId == termId.Value);
        }

        var statistics = new Dictionary<string, int>();

        // Total holidays
        statistics["Total"] = await query.CountAsync();

        // By type
        var typeStats = await query
            .GroupBy(h => h.Type)
            .Select(g => new { Type = g.Key.ToString(), Count = g.Count() })
            .ToListAsync();

        foreach (var stat in typeStats)
        {
            statistics[stat.Type] = stat.Count;
        }

        // Public vs Private
        statistics["Public"] = await query.CountAsync(h => h.IsPublic);
        statistics["Private"] = await query.CountAsync(h => !h.IsPublic);

        // Recurring vs One-time
        statistics["Recurring"] = await query.CountAsync(h => h.IsRecurring);
        statistics["OneTime"] = await query.CountAsync(h => !h.IsRecurring);

        // Current month holidays
        var currentMonth = DateTime.UtcNow;
        var startOfMonth = new DateTime(currentMonth.Year, currentMonth.Month, 1);
        var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

        statistics["CurrentMonth"] = await query.CountAsync(h =>
            h.StartDate >= startOfMonth && h.StartDate <= endOfMonth);

        // Upcoming holidays (next 30 days)
        var today = DateTime.UtcNow.Date;
        var next30Days = today.AddDays(30);

        statistics["Upcoming30Days"] = await query.CountAsync(h =>
            h.StartDate >= today && h.StartDate <= next30Days);

        return statistics;
    }

    public async Task<int> GetTotalHolidaysInPeriodAsync(DateTime startDate, DateTime endDate, Guid? academicYearId = null, Guid? termId = null)
    {
        var repository = _unitOfWork.Repository<Holiday>();
        var query = repository.AsQueryable()
            .Where(h => h.IsActive &&
                       ((h.StartDate >= startDate && h.StartDate <= endDate) ||
                        (h.EndDate >= startDate && h.EndDate <= endDate) ||
                        (h.StartDate <= startDate && h.EndDate >= endDate)));

        if (academicYearId.HasValue)
        {
            query = query.Where(h => h.AcademicYearId == academicYearId.Value);
        }

        if (termId.HasValue)
        {
            query = query.Where(h => h.TermId == termId.Value);
        }

        return await query.CountAsync();
    }
}
