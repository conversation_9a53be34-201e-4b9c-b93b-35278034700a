<!-- Floating Navigation Sidebar -->
<div class="floating-nav" [class.visible]="showFloatingNav">
  <div class="floating-nav-content">
    <h3>Quick Navigation</h3>
    <ul class="floating-nav-links">
      <li>
        <a [routerLink]="[]" [fragment]="'fee-structure'" [class.active]="activeSection === 'fee-structure'">
          <mat-icon>list_alt</mat-icon>
          <span>Fee Structure</span>
        </a>
      </li>
      <li>
        <a [routerLink]="[]" [fragment]="'payment-plans'" [class.active]="activeSection === 'payment-plans'">
          <mat-icon>payments</mat-icon>
          <span>Payment Plans</span>
        </a>
      </li>
      <li>
        <a [routerLink]="[]" [fragment]="'financial-aid'" [class.active]="activeSection === 'financial-aid'">
          <mat-icon>volunteer_activism</mat-icon>
          <span>Financial Aid</span>
        </a>
      </li>
      <li>
        <a [routerLink]="[]" [fragment]="'faq'" [class.active]="activeSection === 'faq'">
          <mat-icon>help_outline</mat-icon>
          <span>FAQ</span>
        </a>
      </li>
    </ul>
  </div>
</div>

<!-- Hero Section -->
<app-default-hero
  title="Tuition & Fees"
  subtitle="Investing in your child's education and future"
  theme="dark"
  size="large"
  alignment="center"
  backgroundImage="assets/images/tuition-hero.jpg">
</app-default-hero>

<ng-template #academicYearTemplate>
  <p class="academic-year">Academic Year {{ academicYear }}</p>
</ng-template>

<!-- Introduction Section -->
<section class="intro-section">
  <div class="container">
    <div class="intro-content">
      <h2>Our Approach to Tuition</h2>
      <p>At our school, we strive to provide exceptional educational experiences while maintaining reasonable and transparent fee structures. Our tuition fees are designed to support high-quality teaching, modern facilities, and comprehensive resources for all students.</p>
      <p>We understand that education is an investment in your child's future, and we are committed to ensuring that this investment yields the best possible returns in terms of academic excellence, personal growth, and future opportunities.</p>

      <div class="quick-links">
        <h3>Quick Links</h3>
        <div class="links-container">
          <a [routerLink]="[]" [fragment]="'fee-structure'" class="quick-link">
            <mat-icon>list_alt</mat-icon>
            <span>Fee Structure</span>
          </a>
          <a [routerLink]="[]" [fragment]="'payment-plans'" class="quick-link">
            <mat-icon>payments</mat-icon>
            <span>Payment Plans</span>
          </a>
          <a [routerLink]="[]" [fragment]="'financial-aid'" class="quick-link">
            <mat-icon>volunteer_activism</mat-icon>
            <span>Financial Aid</span>
          </a>
          <a [routerLink]="[]" [fragment]="'faq'" class="quick-link">
            <mat-icon>help_outline</mat-icon>
            <span>FAQ</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Fee Structure Section -->
<section id="fee-structure" class="fee-structure-section">
  <div class="container">
    <h2 class="section-title">Fee Structure by Education Level</h2>
    <p class="section-description">Our fee structure is organized by educational levels, from Play Group to Secondary School. Click on each level to view detailed fee information.</p>

    <mat-accordion class="fee-accordion" multi>
      <mat-expansion-panel *ngFor="let level of educationLevels">
        <mat-expansion-panel-header>
          <mat-panel-title>
            {{ level.name }}
          </mat-panel-title>
          <mat-panel-description>
            {{ level.grades }} | Ages: {{ level.ageRange }}
          </mat-panel-description>
        </mat-expansion-panel-header>

        <div class="level-details">
          <div class="level-summary">
            <div class="summary-item">
              <h4>Estimated Annual Cost</h4>
              <p class="cost">{{ formatCurrency(calculateAnnualCost(level)) }}</p>
              <p class="note">*Excluding optional fees</p>
            </div>
          </div>

          <mat-tab-group>
            <mat-tab label="Tuition Fees">
              <div class="fees-table-container">
                <table class="fees-table">
                  <thead>
                    <tr>
                      <th>Fee Type</th>
                      <th>Amount</th>
                      <th>Frequency</th>
                      <th>Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let fee of level.tuitionFees">
                      <td>{{ fee.name }}</td>
                      <td>{{ formatCurrency(fee.amount) }}</td>
                      <td>{{ getFrequencyText(fee.frequency) }}</td>
                      <td>{{ fee.description }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </mat-tab>

            <mat-tab label="Additional Fees">
              <div class="fees-table-container">
                <table class="fees-table">
                  <thead>
                    <tr>
                      <th>Fee Type</th>
                      <th>Amount</th>
                      <th>Frequency</th>
                      <th>Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let fee of level.additionalFees" [class.optional-fee]="fee.optional">
                      <td>
                        {{ fee.name }}
                        <span class="optional-tag" *ngIf="fee.optional">Optional</span>
                      </td>
                      <td>{{ formatCurrency(fee.amount) }}</td>
                      <td>{{ getFrequencyText(fee.frequency) }}</td>
                      <td>{{ fee.description }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </mat-tab>

            <mat-tab label="Discounts & Scholarships" *ngIf="level.discounts && level.discounts.length > 0">
              <div class="discounts-container">
                <mat-card class="discount-card" *ngFor="let discount of level.discounts">
                  <mat-card-header>
                    <mat-icon mat-card-avatar>savings</mat-icon>
                    <mat-card-title>{{ discount.name }}</mat-card-title>
                  </mat-card-header>
                  <mat-card-content>
                    <p>{{ discount.description }}</p>
                    <p class="discount-amount">{{ discount.amount }}</p>
                  </mat-card-content>
                </mat-card>
              </div>
            </mat-tab>
          </mat-tab-group>
        </div>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</section>

<!-- Payment Plans Section -->
<section id="payment-plans" class="payment-plans-section">
  <div class="container">
    <h2 class="section-title">Payment Plans</h2>
    <p class="section-description">We offer flexible payment options to accommodate different family budgets and preferences.</p>

    <div class="payment-plans-container">
      <mat-card class="payment-plan-card" *ngFor="let plan of paymentPlans">
        <mat-card-header>
          <mat-icon mat-card-avatar>calendar_today</mat-icon>
          <mat-card-title>{{ plan.name }}</mat-card-title>
          <mat-card-subtitle *ngIf="plan.discount" class="discount-subtitle">{{ plan.discount }}</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p>{{ plan.description }}</p>
          <p *ngIf="plan.dueDate" class="due-date">
            <strong>Due Date:</strong> {{ plan.dueDate }}
          </p>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</section>

<!-- Financial Aid Section -->
<section id="financial-aid" class="financial-aid-section">
  <div class="container">
    <h2 class="section-title">Financial Aid & Scholarships</h2>
    <div class="financial-aid-content">
      <div class="aid-info">
        <h3>Financial Aid Program</h3>
        <p>We believe that quality education should be accessible to deserving students regardless of financial circumstances. Our financial aid program is designed to support families who demonstrate financial need.</p>
        <p>Financial aid applications are reviewed by a committee and decisions are made based on:</p>
        <ul>
          <li>Family income and financial situation</li>
          <li>Number of children in the family</li>
          <li>Special circumstances</li>
          <li>Available funds for the academic year</li>
        </ul>
        <p>Financial aid can cover up to 50% of the total fees depending on the assessment.</p>
        <button mat-raised-button color="primary">Apply for Financial Aid</button>
      </div>

      <div class="scholarship-info">
        <h3>Merit Scholarships</h3>
        <p>Our school offers merit-based scholarships to recognize and reward academic excellence, leadership, and special talents. Scholarships are awarded based on:</p>
        <ul>
          <li>Academic performance</li>
          <li>Entrance examination results</li>
          <li>Special talents in arts, sports, or other areas</li>
          <li>Leadership qualities</li>
        </ul>
        <p>Merit scholarships can cover between 10% to 40% of tuition fees depending on the level and criteria.</p>
        <button mat-raised-button color="primary">Learn About Scholarships</button>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section id="faq" class="faq-section">
  <div class="container">
    <h2 class="section-title">Frequently Asked Questions</h2>
    <p class="section-description">Find answers to common questions about our tuition fees, payment processes, and financial policies.</p>

    <mat-accordion class="faq-accordion">
      <mat-expansion-panel *ngFor="let faq of faqItems">
        <mat-expansion-panel-header>
          <mat-panel-title>
            {{ faq.question }}
          </mat-panel-title>
        </mat-expansion-panel-header>
        <p>{{ faq.answer }}</p>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</section>

<!-- Contact Section -->
<section class="contact-section">
  <div class="container">
    <h2 class="section-title">Have More Questions?</h2>
    <p class="section-description">Our finance office is here to help with any questions regarding tuition, fees, or payment options.</p>

    <div class="contact-info">
      <div class="contact-item">
        <mat-icon>email</mat-icon>
        <div class="contact-details">
          <h3>Email</h3>
          <p>finance[at]school.edu</p>
        </div>
      </div>

      <div class="contact-item">
        <mat-icon>phone</mat-icon>
        <div class="contact-details">
          <h3>Phone</h3>
          <p>+880 2 XXXX XXXX</p>
        </div>
      </div>

      <div class="contact-item">
        <mat-icon>location_on</mat-icon>
        <div class="contact-details">
          <h3>Visit</h3>
          <p>Finance Office, Main Campus<br>Monday to Friday, 9:00 AM - 4:00 PM</p>
        </div>
      </div>
    </div>

    <div class="cta-buttons">
      <button mat-raised-button color="primary">Schedule a Meeting</button>
      <button mat-stroked-button>Download Fee Structure</button>
    </div>
  </div>
</section>
