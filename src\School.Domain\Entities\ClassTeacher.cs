using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

/// <summary>
/// Represents a class teacher assignment for a specific section
/// </summary>
public class ClassTeacher : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this class teacher assignment belongs to
    /// </summary>
    public Guid TenantId { get; set; }

    /// <summary>
    /// Faculty member assigned as class teacher
    /// </summary>
    public Guid FacultyId { get; set; }

    /// <summary>
    /// Section assigned to this class teacher
    /// </summary>
    public Guid SectionId { get; set; }

    /// <summary>
    /// Academic year for this assignment
    /// </summary>
    public Guid AcademicYearId { get; set; }

    /// <summary>
    /// Term for this assignment (optional - can be for entire year)
    /// </summary>
    public Guid? TermId { get; set; }

    /// <summary>
    /// Start date of the assignment
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// End date of the assignment (optional)
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Whether this assignment is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Whether this is the primary class teacher (in case of multiple teachers)
    /// </summary>
    public bool IsPrimary { get; set; } = true;

    /// <summary>
    /// Responsibilities of the class teacher
    /// </summary>
    public string Responsibilities { get; set; } = string.Empty;

    /// <summary>
    /// Special duties assigned
    /// </summary>
    public string SpecialDuties { get; set; } = string.Empty;

    /// <summary>
    /// Contact schedule (when parents can contact)
    /// </summary>
    public string ContactSchedule { get; set; } = string.Empty;

    /// <summary>
    /// Office hours for student consultation
    /// </summary>
    public string OfficeHours { get; set; } = string.Empty;

    /// <summary>
    /// Remarks or additional notes
    /// </summary>
    public string Remarks { get; set; } = string.Empty;

    /// <summary>
    /// Assignment status
    /// </summary>
    public ClassTeacherStatus Status { get; set; } = ClassTeacherStatus.Active;

    // Navigation properties
    /// <summary>
    /// Faculty member assigned as class teacher
    /// </summary>
    public Faculty Faculty { get; set; } = null!;

    /// <summary>
    /// Section assigned to this class teacher
    /// </summary>
    public Section Section { get; set; } = null!;

    /// <summary>
    /// Academic year for this assignment
    /// </summary>
    public AcademicYear AcademicYear { get; set; } = null!;

    /// <summary>
    /// Term for this assignment (if applicable)
    /// </summary>
    public Term? Term { get; set; }

    /// <summary>
    /// Students under this class teacher's supervision
    /// </summary>
    public ICollection<Student> Students { get; set; } = new List<Student>();
}


