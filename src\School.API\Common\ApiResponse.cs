using System.Net;

namespace School.API.Common;

/// <summary>
/// Unified API response structure for all endpoints
/// </summary>
/// <typeparam name="T">Type of data being returned</typeparam>
public class ApiResponse<T>
{
    /// <summary>
    /// Indicates if the request was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Message providing additional information about the response
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// The data payload of the response
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// HTTP status code of the response
    /// </summary>
    public int StatusCode { get; set; }

    /// <summary>
    /// List of error messages if any
    /// </summary>
    public string[]? Errors { get; set; }

    /// <summary>
    /// Creates a successful response with data
    /// </summary>
    public static ApiResponse<T> SuccessResponse(T data, string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Message = message,
            Data = data,
            StatusCode = (int)HttpStatusCode.OK,
            Errors = Array.Empty<string>()
        };
    }

    /// <summary>
    /// Creates an error response
    /// </summary>
    public static ApiResponse<T> ErrorResponse(string message, T? data = default, int statusCode = 400, string[]? errors = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            Data = data,
            StatusCode = statusCode,
            Errors = errors ?? new[] { message }
        };
    }
}

/// <summary>
/// Unified API response structure without data payload
/// </summary>
public class ApiResponse
{
    /// <summary>
    /// Indicates if the request was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Message providing additional information about the response
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// HTTP status code of the response
    /// </summary>
    public int StatusCode { get; set; }

    /// <summary>
    /// List of error messages if any
    /// </summary>
    public string[]? Errors { get; set; }

    /// <summary>
    /// Creates a successful response
    /// </summary>
    public static ApiResponse SuccessResponse(string? message = null)
    {
        return new ApiResponse
        {
            Success = true,
            Message = message,
            StatusCode = (int)HttpStatusCode.OK,
            Errors = Array.Empty<string>()
        };
    }

    /// <summary>
    /// Creates an error response
    /// </summary>
    public static ApiResponse ErrorResponse(string message, int statusCode = 400, string[]? errors = null)
    {
        return new ApiResponse
        {
            Success = false,
            Message = message,
            StatusCode = statusCode,
            Errors = errors ?? new[] { message }
        };
    }
}
