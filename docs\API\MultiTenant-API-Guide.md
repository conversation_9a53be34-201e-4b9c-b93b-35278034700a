# Multi-Tenant API Guide

## Overview

The School Management System now supports multi-tenancy, allowing multiple schools/organizations to use the same platform with complete data isolation. This guide covers the multi-tenant API endpoints and usage patterns.

## Tenant Detection

The system supports multiple methods for tenant identification:

### 1. Subdomain-based Routing
- **Format**: `{tenant-slug}.edumanage.com`
- **Example**: `greenwood-school.edumanage.com`
- **Use Case**: Primary method for production deployments

### 2. Custom Domain Routing
- **Format**: Custom domain mapped to a tenant
- **Example**: `myschool.edu` → mapped to specific tenant
- **Use Case**: Schools with their own domains

### 3. Header-based (Development)
- **Header**: `X-Tenant: {tenant-slug}`
- **Example**: `X-Tenant: test-school`
- **Use Case**: Development and testing

### 4. Query Parameter (Development)
- **Format**: `?tenant={tenant-slug}`
- **Example**: `localhost:4200?tenant=test-school`
- **Use Case**: Local development

## API Endpoints

### Tenant Information

#### Get Current Tenant
```http
GET /api/tenant/current
```

**Headers:**
```
X-Tenant: school-slug (for development)
```

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "name": "Greenwood Elementary School",
  "slug": "greenwood-school",
  "displayName": "Greenwood Elementary",
  "type": "PrimarySchool",
  "status": "Active",
  "isActive": true,
  "isTrialActive": true,
  "trialEndDate": "2024-08-19T00:00:00Z",
  "customDomain": null,
  "defaultLanguage": "en-US",
  "timeZone": "America/New_York",
  "currency": "USD"
}
```

**Error Responses:**
- `404 Not Found`: Tenant not found or inactive
- `400 Bad Request`: No tenant identifier provided

#### Check Tenant Exists
```http
GET /api/tenant/check/{identifier}
```

**Parameters:**
- `identifier`: Tenant slug or domain to check

**Response:**
```json
{
  "exists": true,
  "identifier": "greenwood-school",
  "tenantId": "123e4567-e89b-12d3-a456-************",
  "tenantName": "Greenwood Elementary School",
  "foundBy": "slug"
}
```

#### List All Tenants (Admin Only)
```http
GET /api/tenant/list
Authorization: Bearer {admin-token}
```

**Response:**
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Greenwood Elementary School",
    "slug": "greenwood-school",
    "displayName": "Greenwood Elementary",
    "type": "PrimarySchool",
    "status": "Active",
    "customDomain": null,
    "joinedDate": "2024-01-15T10:30:00Z",
    "isTrialActive": true,
    "trialEndDate": "2024-08-19T00:00:00Z"
  }
]
```

## Authentication with Multi-Tenancy

### Login with Tenant Context

When logging in, the system automatically includes tenant context:

```http
POST /api/auth/login
Content-Type: application/json
X-Tenant: greenwood-school

{
  "username": "<EMAIL>",
  "password": "SecurePassword123",
  "rememberMe": false
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "refresh_token_here",
  "user": {
    "id": "user123",
    "username": "<EMAIL>",
    "email": "<EMAIL>",
    "role": "Administrator",
    "tenantId": "123e4567-e89b-12d3-a456-************",
    "tenantName": "Greenwood Elementary School"
  },
  "expiresAt": "2024-07-20T10:30:00Z"
}
```

### JWT Token Claims

Multi-tenant JWT tokens include additional claims:

```json
{
  "sub": "user123",
  "email": "<EMAIL>",
  "role": "Administrator",
  "tenant_id": "123e4567-e89b-12d3-a456-************",
  "tenant_slug": "greenwood-school",
  "tenant_name": "Greenwood Elementary School",
  "iat": 1642680000,
  "exp": 1642766400
}
```

## Data Isolation

### Automatic Tenant Filtering

All API endpoints automatically filter data by tenant:

```http
GET /api/students
X-Tenant: greenwood-school
```

This will only return students belonging to the "greenwood-school" tenant.

### Cross-Tenant Access Prevention

Attempting to access data from another tenant will result in empty results or 404 errors:

```http
GET /api/students/456  # Student belongs to different tenant
X-Tenant: greenwood-school
```

**Response:** `404 Not Found`

## Error Handling

### Tenant Not Found
```json
{
  "error": "TenantNotFound",
  "message": "The specified tenant could not be found or is inactive",
  "details": {
    "identifier": "non-existent-school",
    "timestamp": "2024-07-19T10:30:00Z"
  }
}
```

### No Tenant Context
```json
{
  "error": "NoTenantContext",
  "message": "Tenant identifier required for this operation",
  "details": {
    "supportedMethods": ["subdomain", "custom-domain", "X-Tenant header"],
    "timestamp": "2024-07-19T10:30:00Z"
  }
}
```

### Insufficient Tenant Access
```json
{
  "error": "InsufficientTenantAccess",
  "message": "User does not have access to the specified tenant",
  "details": {
    "userId": "user123",
    "tenantId": "123e4567-e89b-12d3-a456-************",
    "timestamp": "2024-07-19T10:30:00Z"
  }
}
```

## Best Practices

### 1. Always Include Tenant Context
Ensure all API requests include proper tenant identification through one of the supported methods.

### 2. Handle Tenant Errors Gracefully
Implement proper error handling for tenant-related errors in your client applications.

### 3. Cache Tenant Information
Cache tenant information on the client side to reduce API calls, but respect cache expiration.

### 4. Validate Tenant Access
Always validate that users have access to the tenant they're trying to access.

### 5. Use HTTPS in Production
Always use HTTPS in production to protect tenant data and authentication tokens.

## Development and Testing

### Local Development Setup

1. **Using Query Parameters:**
   ```
   http://localhost:4200?tenant=test-school
   ```

2. **Using Headers:**
   ```bash
   curl -H "X-Tenant: test-school" http://localhost:7130/api/tenant/current
   ```

3. **Using Subdomain (with hosts file):**
   ```
   # Add to hosts file
   127.0.0.1 test-school.localhost
   
   # Access via
   http://test-school.localhost:4200
   ```

### Testing Tenant Isolation

Use the provided integration tests to verify tenant isolation:

```bash
dotnet test tests/School.IntegrationTests/MultiTenant/TenantIsolationTests.cs
```

## Migration from Single-Tenant

### Existing Data Migration

When migrating from single-tenant to multi-tenant:

1. **Create Default Organization:**
   ```sql
   INSERT INTO Organizations (Id, Name, Slug, DisplayName, Type, Status, IsActive)
   VALUES (NEWID(), 'Default School', 'default-school', 'Default School', 3, 2, 1);
   ```

2. **Update Existing Records:**
   ```sql
   UPDATE Students SET TenantId = (SELECT Id FROM Organizations WHERE Slug = 'default-school');
   UPDATE Faculty SET TenantId = (SELECT Id FROM Organizations WHERE Slug = 'default-school');
   -- Repeat for all tenant entities
   ```

### API Client Updates

Update your API clients to include tenant context:

```typescript
// Before (single-tenant)
const students = await api.get('/api/students');

// After (multi-tenant)
const students = await api.get('/api/students', {
  headers: { 'X-Tenant': 'school-slug' }
});
```

## Security Considerations

1. **Tenant Isolation**: Data is automatically isolated by tenant using global query filters
2. **Access Control**: Users can only access tenants they're explicitly granted access to
3. **Token Security**: JWT tokens include tenant claims for additional validation
4. **Audit Logging**: All tenant operations are logged for security auditing
5. **Rate Limiting**: Consider implementing tenant-specific rate limiting

## Support and Troubleshooting

### Common Issues

1. **"Tenant not found" errors**: Verify tenant slug/domain is correct and active
2. **Empty data results**: Check tenant context is properly set
3. **Authentication failures**: Ensure user has access to the specified tenant
4. **Performance issues**: Monitor tenant cache hit rates and optimize accordingly

### Debugging

Enable debug logging to troubleshoot tenant issues:

```json
{
  "Logging": {
    "LogLevel": {
      "School.Infrastructure.Services.TenantService": "Debug",
      "School.Infrastructure.Middleware.TenantMiddleware": "Debug"
    }
  }
}
```

This will provide detailed logs about tenant detection and context setting.

## Production Deployment

### DNS Configuration

For subdomain-based routing, configure wildcard DNS:

```
*.edumanage.com → Your server IP
```

### Load Balancer Configuration

Configure your load balancer to handle wildcard subdomains:

```nginx
server {
    listen 80;
    server_name *.edumanage.com;

    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### SSL Certificate

Use wildcard SSL certificate for subdomain support:

```bash
# Using Let's Encrypt with certbot
certbot certonly --dns-cloudflare --dns-cloudflare-credentials ~/.secrets/certbot/cloudflare.ini -d "*.edumanage.com"
```

### Environment Variables

Set production environment variables:

```bash
ASPNETCORE_ENVIRONMENT=Production
ConnectionStrings__DefaultConnection="Server=...;Database=SchoolManagement;..."
JWT__Secret="your-production-jwt-secret"
JWT__Issuer="https://edumanage.com"
JWT__Audience="https://edumanage.com"
```
