<!-- Default Hero Section -->
<app-default-hero
  [translationPrefix]="'CAMPUS_LIFE'"
  [theme]="'dark'"
  [buttons]="[
    {label: 'CAMPUS_LIFE.EXPLORE_CLUBS' | translate, link: '/campus/clubs', isPrimary: true, icon: 'groups'},
    {label: 'CAMPUS_LIFE.TOUR' | translate, link: '/campus/tour', isPrimary: false, icon: 'explore'}
  ]">
</app-default-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'CAMPUS_LIFE.INTRO_TITLE' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.INTRO_P1' | translate }}</p>
      <p>{{ 'CAMPUS_LIFE.INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Campus Features Section -->
  <section class="features-section">
    <h2>{{ 'CAMPUS_LIFE.EXPLORE_CAMPUS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.EXPLORE_INTRO' | translate }}</p>

    <div class="features-grid">
      <mat-card class="feature-card" *ngFor="let feature of campusFeatures">
        <div class="feature-image">
          <img [src]="feature.image" [alt]="feature.title | translate">
          <div class="feature-icon">
            <mat-icon>{{feature.icon}}</mat-icon>
          </div>
        </div>
        <mat-card-content>
          <h3>{{ feature.title | translate }}</h3>
          <p>{{ feature.description | translate }}</p>
        </mat-card-content>
        <mat-card-actions>
          <a mat-button color="primary" [routerLink]="feature.link">
            {{ 'CAMPUS_LIFE.LEARN_MORE' | translate }}
            <mat-icon>arrow_forward</mat-icon>
          </a>
        </mat-card-actions>
      </mat-card>
    </div>
  </section>

  <!-- Campus Highlights Section -->
  <section class="highlights-section">
    <h2>{{ 'CAMPUS_LIFE.CAMPUS_HIGHLIGHTS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.HIGHLIGHTS_INTRO' | translate }}</p>

    <div class="highlights-container">
      <div class="highlight-item" *ngFor="let highlight of campusHighlights; let i = index" [ngClass]="{'highlight-reverse': i % 2 !== 0}">
        <div class="highlight-image">
          <img [src]="highlight.image" [alt]="highlight.title | translate">
        </div>
        <div class="highlight-content">
          <h3>{{ highlight.title | translate }}</h3>
          <p>{{ highlight.description | translate }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Upcoming Events Section -->
  <section class="events-section">
    <h2>{{ 'CAMPUS_LIFE.UPCOMING_EVENTS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.EVENTS_INTRO' | translate }}</p>

    <div class="events-container">
      <div class="event-card" *ngFor="let event of upcomingEvents">
        <div class="event-date">
          <div class="month-day">{{event.date | date:'MMM d'}}</div>
          <div class="year">{{event.date | date:'yyyy'}}</div>
        </div>
        <div class="event-details">
          <h3>{{event.title}}</h3>
          <div class="event-info">
            <div class="info-item">
              <mat-icon>schedule</mat-icon>
              <span>{{event.time}}</span>
            </div>
            <div class="info-item">
              <mat-icon>location_on</mat-icon>
              <span>{{event.location}}</span>
            </div>
          </div>
          <p>{{event.description}}</p>
        </div>
      </div>
    </div>

    <div class="events-cta">
      <a mat-raised-button color="primary" routerLink="/events">
        {{ 'CAMPUS_LIFE.VIEW_ALL_EVENTS' | translate }}
      </a>
    </div>
  </section>

  <!-- Student Testimonials Section -->
  <section class="testimonials-section">
    <h2>{{ 'CAMPUS_LIFE.STUDENT_VOICES' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.TESTIMONIALS_INTRO' | translate }}</p>

    <div class="testimonials-container">
      <div class="testimonial-card" *ngFor="let testimonial of studentTestimonials">
        <div class="testimonial-image">
          <img [src]="testimonial.image" [alt]="testimonial.student | translate">
        </div>
        <div class="testimonial-content">
          <div class="quote-icon">
            <mat-icon>format_quote</mat-icon>
          </div>
          <p class="testimonial-quote">{{ testimonial.quote | translate }}</p>
          <p class="testimonial-student">{{ testimonial.student | translate }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Campus Tour Section -->
  <section class="tour-section">
    <div class="tour-content">
      <h2>{{ 'CAMPUS_LIFE.VISIT_CAMPUS' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.TOUR_TEXT' | translate }}</p>
      <div class="tour-buttons">
        <a mat-raised-button color="primary" routerLink="/admissions/visit">
          {{ 'CAMPUS_LIFE.SCHEDULE_TOUR' | translate }}
        </a>
        <a mat-stroked-button color="primary" routerLink="/campus/tour">
          {{ 'CAMPUS_LIFE.VIRTUAL_TOUR' | translate }}
        </a>
      </div>
    </div>
  </section>
</div>
