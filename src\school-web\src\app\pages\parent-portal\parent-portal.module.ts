import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatNativeDateModule } from '@angular/material/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';
import { MatDialogModule } from '@angular/material/dialog';

import { ParentPortalComponent } from './parent-portal.component';
import { ParentDashboardComponent } from './parent-dashboard/parent-dashboard.component';
import { ParentProfileComponent } from './parent-profile/parent-profile.component';
// Import components from their correct paths
import { StudentAttendanceComponent } from './student-attendance/student-attendance.component';
import { StudentFeesComponent } from './student-fees/student-fees.component';
import { StudentResultsComponent } from './student-results/student-results.component';
import { StudentLeavesComponent } from './student-leaves/student-leaves.component';
import { LeaveApplicationDialogComponent } from './student-leaves/leave-application-dialog/leave-application-dialog.component';

const routes: Routes = [
  {
    path: '',
    component: ParentPortalComponent,
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: ParentDashboardComponent },
      { path: 'profile', component: ParentProfileComponent },
      { path: 'student/:id/attendance', component: StudentAttendanceComponent },
      { path: 'student/:id/fees', component: StudentFeesComponent },
      { path: 'student/:id/results', component: StudentResultsComponent },
      { path: 'student/:id/leaves', component: StudentLeavesComponent }
    ]
  }
];

@NgModule({
  declarations: [
    // All components are now standalone
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatListModule,
    MatMenuModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatSidenavModule,
    MatSnackBarModule,
    MatSortModule,
    MatTableModule,
    MatTabsModule,
    MatToolbarModule,
    MatTooltipModule,
    MatNativeDateModule,
    MatExpansionModule,
    MatChipsModule,
    MatBadgeModule,
    MatDialogModule,

    // Import standalone components
    ParentPortalComponent,
    ParentDashboardComponent,
    ParentProfileComponent,
    StudentAttendanceComponent,
    StudentFeesComponent,
    StudentResultsComponent,
    StudentLeavesComponent,
    LeaveApplicationDialogComponent
  ]
})
export class ParentPortalModule { }
