using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.Common.Models;
using School.Domain.Enums;
using System.Security.Claims;
using System.Threading.Tasks;

namespace School.Infrastructure.Identity
{
    public class IdentityService : IIdentityService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly ILogger<IdentityService> _logger;
        private readonly ICurrentUserService _currentUserService;

        public IdentityService(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            RoleManager<IdentityRole> roleManager,
            ILogger<IdentityService> logger,
            ICurrentUserService currentUserService)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _roleManager = roleManager;
            _logger = logger;
            _currentUserService = currentUserService;
        }

        public async Task<string?> GetUserNameAsync(Guid userId)
        {
            var user = await _userManager.Users.FirstOrDefaultAsync(u => u.Id == userId.ToString());
            return user?.UserName;
        }

        public async Task<bool> IsInRoleAsync(Guid userId, string role)
        {
            var user = await _userManager.Users.FirstOrDefaultAsync(u => u.Id == userId.ToString());
            if (user == null)
            {
                return false;
            }

            return await _userManager.IsInRoleAsync(user, role);
        }

        public async Task<bool> AuthorizeAsync(Guid userId, string policyName)
        {
            var user = await _userManager.Users.FirstOrDefaultAsync(u => u.Id == userId.ToString());

            if (user == null)
            {
                return false;
            }

            // Get user roles
            var roles = await _userManager.GetRolesAsync(user);

            // Simple policy check based on role
            return policyName switch
            {
                "AdminPolicy" => roles.Contains("Admin") || roles.Contains("SystemAdmin"),
                "StudentPolicy" => roles.Contains("Student") || roles.Contains("Admin") || roles.Contains("SystemAdmin"),
                "EditorPolicy" => roles.Contains("Admin") || roles.Contains("SystemAdmin") || roles.Contains("Editor"),
                "TeacherPolicy" => roles.Contains("Admin") || roles.Contains("SystemAdmin") || roles.Contains("Faculty"),
                "ManagerPolicy" => roles.Contains("Admin") || roles.Contains("SystemAdmin") || roles.Contains("Manager"),
                "AlumniPolicy" => roles.Contains("Admin") || roles.Contains("SystemAdmin") || roles.Contains("Alumni"),
                "StudentSelfOrAdmin" => roles.Contains("Admin") || roles.Contains("SystemAdmin") || roles.Contains("Student"),
                "UserPolicy" => true, // All authenticated users
                _ => false
            };
        }

        public async Task<(ApiResult Result, Guid UserId)> CreateUserAsync(string userName, string password)
        {
            // Check if user already exists
            var existingUser = await _userManager.FindByNameAsync(userName);
            if (existingUser != null)
            {
                return (ApiResult.FailureResult(new[] { "User with this username already exists" }), Guid.Empty);
            }

            var user = new ApplicationUser
            {
                UserName = userName,
                Email = $"{userName}@example.com", // This should be provided in a real app
                FirstName = "New",
                LastName = "User",
                Role = UserRole.User,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = _currentUserService.UserId?.ToString()
            };

            var result = await _userManager.CreateAsync(user, password);

            if (!result.Succeeded)
            {
                var errors = result.Errors.Select(e => e.Description).ToArray();
                return (ApiResult.FailureResult(errors), Guid.Empty);
            }

            // Add user to the User role
            await _userManager.AddToRoleAsync(user, "User");

            _logger.LogInformation("User {UserName} created with ID {UserId}", userName, user.Id);

            return (ApiResult.SuccessResult(), Guid.Parse(user.Id));
        }

        public async Task<ApiResult> DeleteUserAsync(Guid userId)
        {
            var user = await _userManager.Users.FirstOrDefaultAsync(u => u.Id == userId.ToString());

            if (user == null)
            {
                return ApiResult.FailureResult(new[] { "User not found" });
            }

            // Soft delete by setting IsDeleted flag
            user.IsDeleted = true;
            var result = await _userManager.UpdateAsync(user);

            if (!result.Succeeded)
            {
                var errors = result.Errors.Select(e => e.Description).ToArray();
                return ApiResult.FailureResult(errors);
            }

            _logger.LogInformation("User {UserId} deleted", userId);

            return ApiResult.SuccessResult();
        }
    }
}
