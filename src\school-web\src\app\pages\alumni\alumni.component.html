<!-- Default Hero Section -->
<app-default-hero
  [translationPrefix]="'ALUMNI'"
  [theme]="'dark'"
  [buttons]="[
    {label: 'ALUMNI.JOIN_NETWORK' | translate, link: '#contact-section', isPrimary: true, icon: 'group_add'},
    {label: 'ALUMNI.EXPLORE_DIRECTORY' | translate, link: '#directory-section', isPrimary: false}
  ]">
</app-default-hero>

<!-- Featured Alumni Section -->
<section class="featured-alumni-section">
  <div class="container">
    <h2 class="section-title">Featured Alumni</h2>
    <p class="section-subtitle">Meet some of our distinguished graduates who are making a difference in the world</p>

    <div class="featured-alumni-grid">
      <mat-card class="alumni-card featured-card" *ngFor="let alumni of featuredAlumni">
        <div class="alumni-image-container">
          <img mat-card-image [src]="alumni.profileImage?.filePath" [alt]="alumni.name" class="alumni-image">
        </div>
        <mat-card-header>
          <mat-card-title>{{ alumni.name }}</mat-card-title>
          <mat-card-subtitle>{{ alumni.profession }} | Class of {{ alumni.graduationYear }}</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p class="alumni-bio">{{ alumni.biography }}</p>
          <div class="achievements">
            <h4>Key Achievements:</h4>
            <p>{{ alumni.achievements }}</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</section>

<!-- Alumni Events Section -->
<section class="events-section">
  <div class="container">
    <h2 class="section-title">Alumni Events</h2>
    <p class="section-subtitle">Stay connected with your alma mater through our various events and reunions</p>

    <mat-tab-group class="events-tabs" mat-align-tabs="center">
      <mat-tab label="Upcoming Events">
        <div class="events-container">
          <mat-card class="event-card" *ngFor="let event of upcomingEvents">
            <div class="event-image-container" *ngIf="event.image">
              <img mat-card-image [src]="event.image" [alt]="event.title" class="event-image">
            </div>
            <mat-card-header>
              <mat-card-title>{{ event.title }}</mat-card-title>
              <mat-card-subtitle>
                <div class="event-date">
                  <mat-icon>event</mat-icon>
                  <span>{{ formatEventDate(event.date) }}</span>
                </div>
                <div class="event-location">
                  <mat-icon>location_on</mat-icon>
                  <span>{{ event.location }}</span>
                </div>
              </mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>{{ event.description }}</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button color="primary">Register</button>
              <button mat-button>Learn More</button>
            </mat-card-actions>
          </mat-card>

          <div class="no-events-message" *ngIf="upcomingEvents.length === 0">
            <p>No upcoming events at this time. Check back soon!</p>
          </div>
        </div>
      </mat-tab>

      <mat-tab label="Past Events">
        <div class="events-container">
          <mat-card class="event-card" *ngFor="let event of pastEvents">
            <div class="event-image-container" *ngIf="event.image">
              <img mat-card-image [src]="event.image" [alt]="event.title" class="event-image">
            </div>
            <mat-card-header>
              <mat-card-title>{{ event.title }}</mat-card-title>
              <mat-card-subtitle>
                <div class="event-date">
                  <mat-icon>event</mat-icon>
                  <span>{{ formatEventDate(event.date) }}</span>
                </div>
                <div class="event-location">
                  <mat-icon>location_on</mat-icon>
                  <span>{{ event.location }}</span>
                </div>
              </mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>{{ event.description }}</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button>View Photos</button>
            </mat-card-actions>
          </mat-card>

          <div class="no-events-message" *ngIf="pastEvents.length === 0">
            <p>No past events to display.</p>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</section>

<!-- Alumni Directory Section -->
<section id="directory-section" class="directory-section">
  <div class="container">
    <h2 class="section-title">Alumni Directory</h2>
    <p class="section-subtitle">Find and connect with fellow graduates from all years</p>

    <div class="directory-filters">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search by name or profession</mat-label>
        <input matInput [(ngModel)]="searchTerm" (keyup)="filterAlumni()">
        <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="searchTerm=''; filterAlumni()">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-form-field appearance="outline" class="year-filter">
        <mat-label>Graduation Year</mat-label>
        <mat-select [(ngModel)]="selectedYear" (selectionChange)="filterAlumni()">
          <mat-option [value]="null">All Years</mat-option>
          <mat-option *ngFor="let year of graduationYears" [value]="year">{{ year }}</mat-option>
        </mat-select>
      </mat-form-field>

      <button mat-button color="primary" (click)="resetFilters()" class="reset-button">
        <mat-icon>refresh</mat-icon> Reset Filters
      </button>
    </div>

    <div class="alumni-grid">
      <mat-card class="alumni-card" *ngFor="let alumni of filteredAlumni">
        <div class="alumni-image-container">
          <img mat-card-image [src]="alumni.profileImage?.filePath" [alt]="alumni.name" class="alumni-image">
        </div>
        <mat-card-header>
          <mat-card-title>{{ alumni.name }}</mat-card-title>
          <mat-card-subtitle>{{ alumni.profession }} | Class of {{ alumni.graduationYear }}</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p class="alumni-bio-short">{{ alumni.biography.substring(0, 100) }}...</p>
        </mat-card-content>
        <mat-card-actions>
          <a mat-button color="primary" [routerLink]="['/alumni', alumni.id]">{{ 'ALUMNI.VIEW_PROFILE' | translate }}</a>
          <button mat-button>Connect</button>
        </mat-card-actions>
      </mat-card>

      <div class="no-results-message" *ngIf="filteredAlumni.length === 0">
        <p>No alumni found matching your search criteria. Please try different filters.</p>
      </div>
    </div>
  </div>
</section>

<!-- Get Involved Section -->
<section class="get-involved-section">
  <div class="container">
    <h2 class="section-title">Get Involved</h2>
    <p class="section-subtitle">There are many ways to give back and stay connected with your alma mater</p>

    <div class="involvement-options">
      <mat-card class="involvement-card">
        <mat-icon class="involvement-icon">volunteer_activism</mat-icon>
        <h3>Volunteer</h3>
        <p>Share your time and expertise with current students through mentoring, guest lectures, or career days.</p>
        <button mat-stroked-button color="primary">Learn More</button>
      </mat-card>

      <mat-card class="involvement-card">
        <mat-icon class="involvement-icon">school</mat-icon>
        <h3>Scholarships</h3>
        <p>Support the next generation of students by contributing to our scholarship funds.</p>
        <button mat-stroked-button color="primary">Donate Now</button>
      </mat-card>

      <mat-card class="involvement-card">
        <mat-icon class="involvement-icon">event</mat-icon>
        <h3>Host an Event</h3>
        <p>Organize a reunion, networking event, or fundraiser for your graduating class.</p>
        <button mat-stroked-button color="primary">Contact Us</button>
      </mat-card>

      <mat-card class="involvement-card">
        <mat-icon class="involvement-icon">work</mat-icon>
        <h3>Career Opportunities</h3>
        <p>Share job openings or internships at your company with fellow alumni and current students.</p>
        <button mat-stroked-button color="primary">Post a Job</button>
      </mat-card>
    </div>
  </div>
</section>

<!-- Contact Section -->
<section id="contact-section" class="contact-section">
  <div class="container">
    <h2 class="section-title">Stay Connected</h2>
    <p class="section-subtitle">Update your information and stay in touch with the alumni community</p>

    <div class="contact-content">
      <div class="contact-info">
        <h3>Alumni Office</h3>
        <p><mat-icon>email</mat-icon> alumni.school.edu</p>
        <p><mat-icon>phone</mat-icon> (*************</p>
        <p><mat-icon>location_on</mat-icon> Alumni Relations Office, Main Campus</p>

        <h3 class="social-heading">Follow Us</h3>
        <div class="social-icons">
          <a href="#" class="social-icon" aria-label="Facebook">
            <mat-icon>public</mat-icon>
          </a>
          <a href="#" class="social-icon" aria-label="Twitter">
            <mat-icon>chat</mat-icon>
          </a>
          <a href="#" class="social-icon" aria-label="LinkedIn">
            <mat-icon>business</mat-icon>
          </a>
          <a href="#" class="social-icon" aria-label="Instagram">
            <mat-icon>photo_camera</mat-icon>
          </a>
        </div>
      </div>

      <div class="newsletter-signup">
        <h3>Subscribe to Alumni Newsletter</h3>
        <p>Receive updates on alumni events, achievements, and opportunities.</p>
        <mat-form-field appearance="outline" class="email-field">
          <mat-label>Email Address</mat-label>
          <input matInput placeholder="<EMAIL>">
        </mat-form-field>
        <button mat-raised-button color="primary">Subscribe</button>
      </div>
    </div>
  </div>
</section>
