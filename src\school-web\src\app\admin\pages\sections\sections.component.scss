.sections-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;

  mat-card {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
  }

  mat-toolbar {
    border-radius: 8px 8px 0 0;
    
    .spacer {
      flex: 1 1 auto;
    }
  }
}

.filter-section {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
  flex-wrap: wrap;

  .search-field {
    flex: 1;
    min-width: 250px;
  }

  .filter-field {
    min-width: 150px;
  }

  button {
    height: 56px;
  }
}

.table-container {
  overflow-x: auto;
  margin-bottom: 16px;

  .sections-table {
    width: 100%;
    
    th {
      font-weight: 600;
      color: #333;
      background-color: #f5f5f5;
    }

    td {
      padding: 12px 8px;
    }
  }
}

.section-code {
  font-weight: 600;
  color: #1976d2;
  background-color: #e3f2fd;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
}

.section-info {
  .section-name {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }

  .section-description {
    display: block;
    color: #666;
    font-size: 0.75rem;
    line-height: 1.2;
  }
}

.grade-name {
  font-weight: 500;
  color: #4caf50;
  background-color: #e8f5e8;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
}

.type-chip {
  background-color: #ff9800;
  color: white;
  font-size: 0.75rem;
  height: 24px;
  line-height: 24px;
}

.medium-chip {
  background-color: #9c27b0;
  color: white;
  font-size: 0.75rem;
  height: 24px;
  line-height: 24px;
}

.capacity-info {
  min-width: 120px;

  .capacity-text {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    font-size: 0.875rem;
  }

  mat-progress-bar {
    height: 6px;
    border-radius: 3px;
    margin-bottom: 2px;
  }

  .capacity-percentage {
    color: #666;
    font-size: 0.75rem;
  }
}

.classroom-info {
  .classroom-name {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
  }

  .room-number {
    display: block;
    color: #666;
    font-size: 0.75rem;
  }
}

.academic-year {
  color: #607d8b;
  font-weight: 500;
}

.status-text {
  margin-left: 8px;
  font-size: 0.875rem;
  font-weight: 500;

  &.active {
    color: #4caf50;
  }

  &.inactive {
    color: #f44336;
  }
}

.action-buttons {
  display: flex;
  gap: 4px;

  button {
    width: 36px;
    height: 36px;
    
    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;

  mat-spinner {
    margin-bottom: 16px;
  }
}

.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #666;

  .no-data-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #ccc;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    color: #333;
  }

  p {
    margin: 0 0 24px 0;
    max-width: 400px;
    line-height: 1.5;
  }
}

// Responsive design
@media (max-width: 1200px) {
  .sections-container {
    padding: 16px;
  }

  .filter-section {
    .filter-field {
      min-width: 120px;
    }
  }
}

@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;

    .search-field,
    .filter-field {
      min-width: auto;
    }
  }

  .table-container {
    .sections-table {
      font-size: 0.875rem;

      th, td {
        padding: 8px 4px;
      }
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .capacity-info {
    min-width: 100px;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .sections-container {
    mat-card {
      background-color: #424242;
      color: #fff;
    }
  }

  .table-container {
    .sections-table {
      th {
        background-color: #616161;
        color: #fff;
      }
    }
  }

  .section-info {
    .section-name {
      color: #fff;
    }

    .section-description {
      color: #ccc;
    }
  }

  .capacity-info {
    .capacity-text {
      color: #fff;
    }

    .capacity-percentage {
      color: #ccc;
    }
  }

  .classroom-info {
    .classroom-name {
      color: #fff;
    }

    .room-number {
      color: #ccc;
    }
  }

  .no-data-container {
    color: #ccc;

    h3 {
      color: #fff;
    }
  }
}

// Chip animations
mat-chip {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

// Progress bar customization
mat-progress-bar {
  .mat-mdc-progress-bar-buffer {
    background-color: #e0e0e0;
  }
}

// Table row hover effect
.sections-table {
  tr.mat-mdc-row:hover {
    background-color: #f5f5f5;
  }
}

@media (prefers-color-scheme: dark) {
  .sections-table {
    tr.mat-mdc-row:hover {
      background-color: #616161;
    }
  }
}
