import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-parents',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatExpansionModule,
    MatTabsModule,
    RouterModule,
    TranslateModule
  ],
  templateUrl: './parents.component.html',
  styleUrls: ['./parents.component.scss']
})
export class ParentsComponent {
  // Parent involvement opportunities
  involvementOpportunities = [
    {
      title: 'Parent-Teacher Association (PTA)',
      description: 'Join our active PTA to support school initiatives, organize events, and build community. The PTA meets monthly and welcomes all parents.',
      icon: 'groups',
      link: '/parent-portal/pta'
    },
    {
      title: 'Volunteer Program',
      description: 'Contribute your time and talents to enhance our school community. Volunteer opportunities include classroom assistance, event planning, and more.',
      icon: 'volunteer_activism',
      link: '/parent-portal/volunteer'
    },
    {
      title: 'School Committees',
      description: 'Serve on school committees focused on curriculum development, diversity and inclusion, facilities improvement, and other important areas.',
      icon: 'meeting_room',
      link: '/parent-portal/committees'
    },
    {
      title: 'Fundraising Initiatives',
      description: 'Support our fundraising efforts to enhance educational programs, improve facilities, and provide scholarships for students in need.',
      icon: 'savings',
      link: '/parent-portal/fundraising'
    },
    {
      title: 'Parent Education Workshops',
      description: 'Attend workshops designed to help parents support their children\'s learning and development at home and navigate educational challenges.',
      icon: 'school',
      link: '/parent-portal/workshops'
    },
    {
      title: 'Community Events',
      description: 'Participate in and help organize community-building events such as cultural celebrations, family picnics, and holiday gatherings.',
      icon: 'celebration',
      link: '/parent-portal/events'
    }
  ];

  // Parent resources
  parentResources = [
    {
      title: 'Parent Portal',
      description: 'Access your child\'s academic information, attendance records, and school communications through our secure online portal.',
      icon: 'computer',
      link: '/parent-portal'
    },
    {
      title: 'School Calendar',
      description: 'Stay informed about important dates, holidays, school events, and academic deadlines throughout the year.',
      icon: 'calendar_month',
      link: '/calendar'
    },
    {
      title: 'Parent Handbook',
      description: 'Download our comprehensive parent handbook for information on school policies, procedures, and expectations.',
      icon: 'menu_book',
      link: '/resources/parent-handbook'
    },
    {
      title: 'Academic Resources',
      description: 'Access resources to support your child\'s learning at home, including curriculum guides, reading lists, and educational websites.',
      icon: 'auto_stories',
      link: '/resources/academic'
    },
    {
      title: 'Health & Wellness',
      description: 'Find information about school health services, nutrition guidelines, mental health resources, and physical activity recommendations.',
      icon: 'favorite',
      link: '/resources/health'
    },
    {
      title: 'Transportation',
      description: 'Learn about bus routes, schedules, transportation policies, and procedures for drop-off and pick-up.',
      icon: 'directions_bus',
      link: '/resources/transportation'
    }
  ];

  // FAQ items
  faqs = [
    {
      question: 'How can I stay informed about my child\'s academic progress?',
      answer: 'You can monitor your child\'s academic progress through our Parent Portal, which provides access to grades, assignments, and teacher comments. Additionally, we schedule parent-teacher conferences twice a year and send progress reports at regular intervals. Teachers are also available for individual meetings upon request.'
    },
    {
      question: 'What is the school\'s policy on attendance and absences?',
      answer: 'Regular attendance is essential for academic success. If your child will be absent, please notify the school office by 8:30 AM. For planned absences of more than two days, please submit a written notice in advance. Students are responsible for making up missed work, and teachers will provide reasonable support for this process.'
    },
    {
      question: 'How does the school communicate with parents?',
      answer: 'We use multiple channels to communicate with parents, including email newsletters, the Parent Portal, our school website, and social media platforms. Important announcements are sent via email and text messages. Each teacher also maintains regular communication through class-specific channels.'
    },
    {
      question: 'What opportunities are there for parent involvement?',
      answer: 'We welcome parent involvement in many ways, including joining the PTA, volunteering in classrooms or at events, serving on school committees, chaperoning field trips, sharing expertise as guest speakers, and supporting fundraising initiatives. Please contact our Parent Coordinator for more information.'
    },
    {
      question: 'How does the school handle homework assignments?',
      answer: 'Homework policies vary by grade level, with increasing expectations as students advance. Generally, homework is designed to reinforce classroom learning, develop independent study habits, and provide opportunities for deeper exploration of topics. Teachers post assignments on the Parent Portal and provide clear guidelines for completion.'
    },
    {
      question: 'What support services are available for students with special needs?',
      answer: 'Our school provides a range of support services for students with special needs, including individualized education plans (IEPs), classroom accommodations, specialized instruction, counseling services, and speech and occupational therapy. We have a dedicated Student Support Team that works closely with teachers and parents to ensure all students can succeed.'
    }
  ];

  // Testimonials from parents
  testimonials = [
    {
      quote: 'The teachers and staff truly care about each student\'s success. My daughter has thrived academically and socially since joining this school community.',
      parent: 'Maria Rodriguez, Parent of 5th Grader',
      image: 'assets/images/parents/testimonial1.jpg'
    },
    {
      quote: 'I appreciate the school\'s commitment to both academic excellence and character development. The values my son is learning here will serve him well throughout his life.',
      parent: 'David Chen, Parent of 8th Grader',
      image: 'assets/images/parents/testimonial2.jpg'
    },
    {
      quote: 'The parent community is welcoming and engaged. Through the PTA, I\'ve formed meaningful connections with other families and found ways to contribute to the school.',
      parent: 'Sarah Johnson, Parent of 3rd and 6th Graders',
      image: 'assets/images/parents/testimonial3.jpg'
    },
    {
      quote: 'Communication between teachers and parents is excellent. I always feel informed about my child\'s progress and upcoming events at the school.',
      parent: 'Michael Williams, Parent of Kindergartner',
      image: 'assets/images/parents/testimonial4.jpg'
    }
  ];
}
