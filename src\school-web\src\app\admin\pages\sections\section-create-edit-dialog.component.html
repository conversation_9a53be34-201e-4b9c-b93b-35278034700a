<div class="dialog-container">
  <h2 mat-dialog-title>
    <mat-icon>class</mat-icon>
    {{ getTitle() | translate }}
  </h2>

  <mat-dialog-content>
    <form [formGroup]="sectionForm" class="section-form">
      <mat-tab-group>
        <!-- Basic Information Tab -->
        <mat-tab label="{{ 'SECTIONS.BASIC_INFORMATION' | translate }}">
          <div class="tab-content">
            <div class="form-row">
              <mat-form-field appearance="outline" class="form-field">
                <mat-label>{{ 'SECTIONS.NAME' | translate }}</mat-label>
                <input matInput formControlName="name" placeholder="{{ 'SECTIONS.NAME_PLACEHOLDER' | translate }}">
                <mat-error *ngIf="sectionForm.get('name')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
                <mat-error *ngIf="sectionForm.get('name')?.hasError('maxlength')">
                  {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 100} }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>{{ 'SECTIONS.CODE' | translate }}</mat-label>
                <input matInput formControlName="code" placeholder="{{ 'SECTIONS.CODE_PLACEHOLDER' | translate }}">
                <mat-error *ngIf="sectionForm.get('code')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
                <mat-error *ngIf="sectionForm.get('code')?.hasError('maxlength')">
                  {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 20} }}
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="form-field">
                <mat-label>{{ 'SECTIONS.GRADE' | translate }}</mat-label>
                <mat-select formControlName="gradeId">
                  <mat-option *ngFor="let grade of grades" [value]="grade.id">
                    {{ grade.name }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="sectionForm.get('gradeId')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>{{ 'SECTIONS.ACADEMIC_YEAR' | translate }}</mat-label>
                <mat-select formControlName="academicYearId">
                  <mat-option *ngFor="let year of academicYears" [value]="year.id">
                    {{ year.name }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="sectionForm.get('academicYearId')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
              </mat-form-field>
            </div>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>{{ 'SECTIONS.DESCRIPTION' | translate }}</mat-label>
              <textarea matInput formControlName="description" rows="3" 
                        placeholder="{{ 'SECTIONS.DESCRIPTION_PLACEHOLDER' | translate }}"></textarea>
              <mat-error *ngIf="sectionForm.get('description')?.hasError('maxlength')">
                {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 500} }}
              </mat-error>
            </mat-form-field>
          </div>
        </mat-tab>

        <!-- Section Configuration Tab -->
        <mat-tab label="{{ 'SECTIONS.CONFIGURATION' | translate }}">
          <div class="tab-content">
            <div class="form-row">
              <mat-form-field appearance="outline" class="form-field">
                <mat-label>{{ 'SECTIONS.TYPE' | translate }}</mat-label>
                <mat-select formControlName="type">
                  <mat-option *ngFor="let type of sectionTypes" [value]="type.value">
                    {{ type.label }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="sectionForm.get('type')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>{{ 'SECTIONS.MEDIUM' | translate }}</mat-label>
                <mat-select formControlName="medium">
                  <mat-option *ngFor="let medium of mediums" [value]="medium.value">
                    {{ medium.label }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="sectionForm.get('medium')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="form-field">
                <mat-label>{{ 'SECTIONS.SHIFT' | translate }}</mat-label>
                <mat-select formControlName="shift">
                  <mat-option *ngFor="let shift of shifts" [value]="shift.value">
                    {{ shift.label }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="sectionForm.get('shift')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>{{ 'SECTIONS.CAPACITY' | translate }}</mat-label>
                <input matInput type="number" formControlName="capacity" min="1" max="200">
                <mat-hint>{{ 'SECTIONS.CAPACITY_HINT' | translate }}</mat-hint>
                <mat-error *ngIf="sectionForm.get('capacity')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
                <mat-error *ngIf="sectionForm.get('capacity')?.hasError('min') || sectionForm.get('capacity')?.hasError('max')">
                  {{ 'VALIDATION.RANGE' | translate: {min: 1, max: 200} }}
                </mat-error>
              </mat-form-field>
            </div>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>{{ 'SECTIONS.DISPLAY_ORDER' | translate }}</mat-label>
              <input matInput type="number" formControlName="displayOrder" min="1">
              <mat-error *ngIf="sectionForm.get('displayOrder')?.hasError('required')">
                {{ 'VALIDATION.REQUIRED' | translate }}
              </mat-error>
              <mat-error *ngIf="sectionForm.get('displayOrder')?.hasError('min')">
                {{ 'VALIDATION.MIN_VALUE' | translate: {min: 1} }}
              </mat-error>
            </mat-form-field>
          </div>
        </mat-tab>

        <!-- Classroom Information Tab -->
        <mat-tab label="{{ 'SECTIONS.CLASSROOM_INFO' | translate }}">
          <div class="tab-content">
            <div class="form-row">
              <mat-form-field appearance="outline" class="form-field">
                <mat-label>{{ 'SECTIONS.CLASSROOM' | translate }}</mat-label>
                <input matInput formControlName="classroom" placeholder="{{ 'SECTIONS.CLASSROOM_PLACEHOLDER' | translate }}">
                <mat-error *ngIf="sectionForm.get('classroom')?.hasError('maxlength')">
                  {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 100} }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>{{ 'SECTIONS.ROOM_NUMBER' | translate }}</mat-label>
                <input matInput formControlName="roomNumber" placeholder="{{ 'SECTIONS.ROOM_NUMBER_PLACEHOLDER' | translate }}">
                <mat-error *ngIf="sectionForm.get('roomNumber')?.hasError('maxlength')">
                  {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 50} }}
                </mat-error>
              </mat-form-field>
            </div>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>{{ 'SECTIONS.REQUIREMENTS' | translate }}</mat-label>
              <textarea matInput formControlName="requirements" rows="4" 
                        placeholder="{{ 'SECTIONS.REQUIREMENTS_PLACEHOLDER' | translate }}"></textarea>
              <mat-error *ngIf="sectionForm.get('requirements')?.hasError('maxlength')">
                {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 1000} }}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>{{ 'SECTIONS.REMARKS' | translate }}</mat-label>
              <textarea matInput formControlName="remarks" rows="2" 
                        placeholder="{{ 'SECTIONS.REMARKS_PLACEHOLDER' | translate }}"></textarea>
              <mat-error *ngIf="sectionForm.get('remarks')?.hasError('maxlength')">
                {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 500} }}
              </mat-error>
            </mat-form-field>
          </div>
        </mat-tab>
      </mat-tab-group>

      <!-- Status Section -->
      <div class="status-section">
        <mat-slide-toggle formControlName="isActive" color="primary">
          {{ 'SECTIONS.IS_ACTIVE' | translate }}
        </mat-slide-toggle>
        <small class="status-hint">{{ 'SECTIONS.IS_ACTIVE_HINT' | translate }}</small>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()" [disabled]="loading">
      {{ 'COMMON.CANCEL' | translate }}
    </button>
    <button mat-raised-button color="primary" (click)="onSubmit()" 
            [disabled]="!sectionForm.valid || loading">
      <mat-spinner *ngIf="loading" diameter="20" class="button-spinner"></mat-spinner>
      <span *ngIf="!loading">{{ getSubmitButtonText() | translate }}</span>
    </button>
  </mat-dialog-actions>
</div>
