<div class="dialog-container">
  <h2 mat-dialog-title>
    <mat-icon>person_add</mat-icon>
    {{ getTitle() | translate }}
  </h2>

  <mat-dialog-content>
    <form [formGroup]="assignmentForm" class="assignment-form">
      <!-- Basic Assignment Section -->
      <div class="form-section">
        <h3 class="section-title">{{ 'CLASS_TEACHERS.ASSIGNMENT_DETAILS' | translate }}</h3>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'CLASS_TEACHERS.ACADEMIC_YEAR' | translate }}</mat-label>
            <mat-select formControlName="academicYearId">
              <mat-option *ngFor="let year of academicYears" [value]="year.id">
                {{ year.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="assignmentForm.get('academicYearId')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'CLASS_TEACHERS.FACULTY' | translate }}</mat-label>
            <mat-select formControlName="facultyId">
              <mat-option *ngFor="let faculty of availableFaculty" [value]="faculty.id">
                {{ faculty.name }} ({{ faculty.email }})
              </mat-option>
            </mat-select>
            <mat-error *ngIf="assignmentForm.get('facultyId')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'CLASS_TEACHERS.GRADE' | translate }}</mat-label>
            <mat-select formControlName="gradeId">
              <mat-option *ngFor="let grade of grades" [value]="grade.id">
                {{ grade.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="assignmentForm.get('gradeId')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'CLASS_TEACHERS.SECTION' | translate }}</mat-label>
            <mat-select formControlName="sectionId">
              <mat-option *ngFor="let section of sections" 
                         [value]="section.id"
                         [disabled]="!isSectionAvailable(section.id)">
                {{ section.name }}
                <span *ngIf="!isSectionAvailable(section.id)" class="assigned-indicator">
                  ({{ 'CLASS_TEACHERS.ALREADY_ASSIGNED' | translate }})
                </span>
              </mat-option>
            </mat-select>
            <mat-error *ngIf="assignmentForm.get('sectionId')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'CLASS_TEACHERS.START_DATE' | translate }}</mat-label>
            <input matInput [matDatepicker]="startDatePicker" formControlName="startDate">
            <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #startDatePicker></mat-datepicker>
            <mat-error *ngIf="assignmentForm.get('startDate')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>

          <div class="primary-toggle">
            <mat-slide-toggle formControlName="isPrimary" color="primary">
              {{ 'CLASS_TEACHERS.IS_PRIMARY' | translate }}
            </mat-slide-toggle>
            <small class="toggle-hint">{{ 'CLASS_TEACHERS.IS_PRIMARY_HINT' | translate }}</small>
          </div>
        </div>
      </div>

      <!-- Responsibilities Section -->
      <div class="form-section">
        <h3 class="section-title">{{ 'CLASS_TEACHERS.RESPONSIBILITIES' | translate }}</h3>
        
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'CLASS_TEACHERS.RESPONSIBILITIES' | translate }}</mat-label>
          <textarea matInput formControlName="responsibilities" rows="3" 
                    placeholder="{{ 'CLASS_TEACHERS.RESPONSIBILITIES_PLACEHOLDER' | translate }}"></textarea>
          <mat-error *ngIf="assignmentForm.get('responsibilities')?.hasError('maxlength')">
            {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 1000} }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'CLASS_TEACHERS.SPECIAL_DUTIES' | translate }}</mat-label>
          <textarea matInput formControlName="specialDuties" rows="3" 
                    placeholder="{{ 'CLASS_TEACHERS.SPECIAL_DUTIES_PLACEHOLDER' | translate }}"></textarea>
          <mat-error *ngIf="assignmentForm.get('specialDuties')?.hasError('maxlength')">
            {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 1000} }}
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Schedule Section -->
      <div class="form-section">
        <h3 class="section-title">{{ 'CLASS_TEACHERS.SCHEDULE' | translate }}</h3>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'CLASS_TEACHERS.CONTACT_SCHEDULE' | translate }}</mat-label>
            <textarea matInput formControlName="contactSchedule" rows="2" 
                      placeholder="{{ 'CLASS_TEACHERS.CONTACT_SCHEDULE_PLACEHOLDER' | translate }}"></textarea>
            <mat-error *ngIf="assignmentForm.get('contactSchedule')?.hasError('maxlength')">
              {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 500} }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'CLASS_TEACHERS.OFFICE_HOURS' | translate }}</mat-label>
            <textarea matInput formControlName="officeHours" rows="2" 
                      placeholder="{{ 'CLASS_TEACHERS.OFFICE_HOURS_PLACEHOLDER' | translate }}"></textarea>
            <mat-error *ngIf="assignmentForm.get('officeHours')?.hasError('maxlength')">
              {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 500} }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()" [disabled]="loading">
      {{ 'COMMON.CANCEL' | translate }}
    </button>
    <button mat-raised-button color="primary" (click)="onSubmit()" 
            [disabled]="!assignmentForm.valid || loading">
      <mat-spinner *ngIf="loading" diameter="20" class="button-spinner"></mat-spinner>
      <span *ngIf="!loading">{{ getSubmitButtonText() | translate }}</span>
    </button>
  </mat-dialog-actions>
</div>
