using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ClubAdvisorConfiguration : IEntityTypeConfiguration<ClubAdvisor>
{
    public void Configure(EntityTypeBuilder<ClubAdvisor> builder)
    {
        builder.HasKey(a => a.Id);
        
        builder.Property(a => a.Name)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(a => a.Email)
            .HasMaxLength(100);
            
        builder.Property(a => a.Phone)
            .HasMaxLength(20);
            
        builder.Property(a => a.DisplayOrder)
            .HasDefaultValue(0);
            
        // Relationships
        builder.HasOne(a => a.Faculty)
            .WithMany()
            .HasForeignKey(a => a.FacultyId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
