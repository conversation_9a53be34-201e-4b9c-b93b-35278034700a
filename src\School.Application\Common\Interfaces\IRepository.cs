using School.Domain.Common;
using System.Linq.Expressions;

namespace School.Application.Common.Interfaces;

/// <summary>
/// Generic repository interface for CRUD operations
/// </summary>
/// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
public interface IRepository<T> where T : BaseEntity
{
    // Query methods
    Task<T?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<T?> GetByIdAsync(Guid id, string[] includes, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<T>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IReadOnlyList<T>> GetAllAsync(string[] includes, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<T>> FindAsync(Expression<Func<T, bool>> predicate, string[] includes, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<T>> FindAsync(Expression<Func<T, bool>> predicate, int skip, int take, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<T>> FindAsync(Expression<Func<T, bool>> predicate, int skip, int take, string[] includes, CancellationToken cancellationToken = default);
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);

    // Command methods
    Task<T> AddAsync(T entity, string? userId = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, string? userId = null, CancellationToken cancellationToken = default);
    Task UpdateAsync(T entity, string? userId = null, CancellationToken cancellationToken = default);
    Task UpdateRangeAsync(IEnumerable<T> entities, string? userId = null, CancellationToken cancellationToken = default);
    Task DeleteAsync(T entity, string? userId = null, bool softDelete = true, CancellationToken cancellationToken = default);
    Task DeleteRangeAsync(IEnumerable<T> entities, string? userId = null, bool softDelete = true, CancellationToken cancellationToken = default);
    Task DeleteByIdAsync(Guid id, string? userId = null, bool softDelete = true, CancellationToken cancellationToken = default);

    // Advanced query methods
    IQueryable<T> AsQueryable();
    IQueryable<T> AsQueryable(params string[] includes);
}
