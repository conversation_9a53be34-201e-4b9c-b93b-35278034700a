<div class="dashboard-container">
  <!-- Welcome Section -->
  <div class="welcome-section">
    <h2 class="welcome-title">{{ 'admin.dashboard.welcome' | translate }}</h2>
    <p class="welcome-subtitle">Here's what's happening with your school today.</p>
  </div>

  <!-- Stats Cards -->
  <div class="stats-grid">
    <mat-card class="stats-card dashboard-card" *ngFor="let stat of statsCards">
      <div class="stats-card-content">
        <div class="stats-info">
          <h3 class="stats-title stats-label">{{ stat.title }}</h3>
          <div class="stats-count stats-number">{{ stat.count }}</div>
          <div class="stats-trend">
            <mat-icon>trending_up</mat-icon>
            <span>{{ stat.increase }}% {{ stat.period }}</span>
          </div>
        </div>
        <div class="stats-icon">
          <mat-icon>{{ stat.icon }}</mat-icon>
        </div>
      </div>
    </mat-card>
  </div>

  <!-- Main Content Grid -->
  <div class="content-grid">
    <!-- Recent Activity -->
    <mat-card class="activity-card dashboard-card">
      <mat-card-header>
        <mat-card-title>{{ 'admin.dashboard.recent_activity' | translate }}</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="activity-list">
          <div class="activity-item" *ngFor="let activity of recentActivities">
            <div class="activity-icon">
              <mat-icon>{{ activity.icon }}</mat-icon>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.action }}</div>
              <div class="activity-time">{{ activity.user }} • {{ activity.time }}</div>
            </div>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions align="end">
        <button mat-button color="primary">View All Activity</button>
      </mat-card-actions>
    </mat-card>

    <!-- Pending Tasks -->
    <mat-card class="tasks-card dashboard-card">
      <mat-card-header>
        <mat-card-title>{{ 'admin.dashboard.pending_tasks' | translate }}</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="task-list">
          <div class="task-item" *ngFor="let task of pendingTasks" [ngClass]="getPriorityClass(task.priority)">
            <div class="task-content">
              <div class="task-title">{{ task.task }}</div>
              <div class="task-meta">
                <span class="task-priority">{{ task.priority }}</span>
                <span class="task-deadline">{{ task.deadline }}</span>
                <span class="task-progress">{{ task.progress }}%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" [style.width.%]="task.progress"></div>
              </div>
            </div>
            <div class="task-actions">
              <button mat-icon-button color="primary" matTooltip="Edit">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="accent" matTooltip="Complete">
                <mat-icon>check_circle</mat-icon>
              </button>
            </div>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions align="end">
        <button mat-button color="primary">View All Tasks</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Quick Actions -->
  <mat-card class="quick-actions-card dashboard-card">
    <mat-card-header>
      <mat-card-title>Quick Actions</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="quick-action-grid">
        <button class="quick-action-btn" mat-button>
          <div class="action-icon">
            <mat-icon>add_circle</mat-icon>
          </div>
          <div class="action-title">Add Notice</div>
          <div class="action-description">Create new announcement</div>
        </button>
        <button class="quick-action-btn" mat-button>
          <div class="action-icon">
            <mat-icon>person_add</mat-icon>
          </div>
          <div class="action-title">Add User</div>
          <div class="action-description">Register new user</div>
        </button>
        <button class="quick-action-btn" mat-button>
          <div class="action-icon">
            <mat-icon>cloud_upload</mat-icon>
          </div>
          <div class="action-title">Upload Media</div>
          <div class="action-description">Add files and images</div>
        </button>
        <button class="quick-action-btn" mat-button>
          <div class="action-icon">
            <mat-icon>edit</mat-icon>
          </div>
          <div class="action-title">Edit Content</div>
          <div class="action-description">Update website content</div>
        </button>
        <button class="quick-action-btn" mat-button>
          <div class="action-icon">
            <mat-icon>settings</mat-icon>
          </div>
          <div class="action-title">Settings</div>
          <div class="action-description">Configure system</div>
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</div>
