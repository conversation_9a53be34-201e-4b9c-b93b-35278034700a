using School.Application.Common.Mappings;
using School.Application.DTOs.Common;
using School.Domain.Entities;
using School.Domain.Enums;

namespace School.Application.DTOs;

public class ContentDto : IMapFrom<Content>
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public ContentType Type { get; set; }
    public string MetaDescription { get; set; } = string.Empty;
    public bool IsPublished { get; set; }
    public DateTime? PublishedAt { get; set; }
    public int? ParentId { get; set; }
    public int CreatedById { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
    public int? LastModifiedById { get; set; }
    public string? LastModifiedByName { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public List<ContentTranslationDto> Translations { get; set; } = new();
    public List<MediaItemDto> MediaItems { get; set; } = new();
}

public class ContentCreateDto
{
    public string Title { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public ContentType Type { get; set; }
    public string MetaDescription { get; set; } = string.Empty;
    public bool IsPublished { get; set; }
    public int? ParentId { get; set; }
    public List<ContentTranslationCreateDto>? Translations { get; set; }
}

public class ContentUpdateDto
{
    public string? Title { get; set; }
    public string? Slug { get; set; }
    public string? Body { get; set; }
    public ContentType? Type { get; set; }
    public string? MetaDescription { get; set; }
    public bool? IsPublished { get; set; }
    public int? ParentId { get; set; }
}

public class ContentTranslationDto : IMapFrom<ContentTranslation>
{
    public Guid id { get; set; }
    public int ContentId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string MetaDescription { get; set; } = string.Empty;
}

public class ContentTranslationCreateDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string MetaDescription { get; set; } = string.Empty;
}

public class ContentTranslationUpdateDto
{
    public string? Title { get; set; }
    public string? Body { get; set; }
    public string? MetaDescription { get; set; }
}

/// <summary>
/// Filter DTO for content queries
/// </summary>
public class ContentFilterDto : BaseFilterDto
{
    /// <summary>
    /// Search term for title or body
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// Filter by content type
    /// </summary>
    public ContentType? Type { get; set; }

    /// <summary>
    /// Filter by published status
    /// </summary>
    public bool? Published { get; set; }

    /// <summary>
    /// Filter by parent ID
    /// </summary>
    public int? ParentId { get; set; }
}
