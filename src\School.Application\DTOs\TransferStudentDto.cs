using System.ComponentModel.DataAnnotations;

namespace School.Application.DTOs;

/// <summary>
/// DTO for transferring student between sections
/// </summary>
public class TransferStudentDto
{
    /// <summary>
    /// Student ID to transfer
    /// </summary>
    [Required]
    public Guid StudentId { get; set; }

    /// <summary>
    /// Source section ID
    /// </summary>
    [Required]
    public Guid FromSectionId { get; set; }

    /// <summary>
    /// Target section ID
    /// </summary>
    [Required]
    public Guid ToSectionId { get; set; }

    /// <summary>
    /// Reason for transfer
    /// </summary>
    public string? Reason { get; set; }
}
