using System.ComponentModel.DataAnnotations;
using School.Domain.Enums;

namespace School.Application.DTOs;

/// <summary>
/// DTO for updating class teacher status
/// </summary>
public class UpdateClassTeacherStatusDto
{
    /// <summary>
    /// New status for the class teacher
    /// </summary>
    [Required]
    public ClassTeacherStatus Status { get; set; }

    /// <summary>
    /// Reason for status change
    /// </summary>
    public string? Reason { get; set; }
}
