using School.Application.DTOs;

namespace School.Application.Features.Notice;

public interface INoticeService
{
    Task<(List<NoticeDto> Items, int TotalCount)> GetAllNoticesAsync(NoticeFilterDto filter);
    Task<NoticeDto?> GetNoticeByIdAsync(Guid id);
    Task<Guid> CreateNoticeAsync(NoticeCreateDto noticeDto, Guid createdById);
    Task<bool> UpdateNoticeAsync(Guid id, NoticeUpdateDto noticeDto);
    Task<bool> DeleteNoticeAsync(Guid id);
}