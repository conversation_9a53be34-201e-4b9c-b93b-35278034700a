import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormGroup, FormBuilder, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { StudentService } from '../../../core/services/student.service';
import { AuthService } from '../../../core/services/auth.service';
import { Student, StudentAttendance } from '../../../core/models/student.model';

@Component({
  selector: 'app-student-attendance',
  templateUrl: './student-attendance.component.html',
  styleUrls: ['./student-attendance.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DatePipe,

    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatSnackBarModule
  ]
})
export class StudentAttendanceComponent implements OnInit {
  student: Student | null = null;
  attendanceRecords: StudentAttendance[] = [];
  dataSource = new MatTableDataSource<StudentAttendance>([]);
  displayedColumns: string[] = ['date', 'status', 'period', 'subjectCode', 'remarks'];

  filterForm: FormGroup;

  loading = {
    student: true,
    attendance: true
  };

  error = {
    student: false,
    attendance: false
  };

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private studentService: StudentService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    this.filterForm = this.formBuilder.group({
      fromDate: [null],
      toDate: [null]
    });
  }

  ngOnInit(): void {
    this.loadStudentData();
  }

  loadStudentData(): void {
    this.loading.student = true;

    // In a real application, you would fetch the student by user ID
    // For now, we'll use a mock student ID
    this.studentService.getStudentByStudentId('S2023-001')
      .subscribe({
        next: (student) => {
          this.student = student;
          this.loading.student = false;
          this.loadAttendanceData();
        },
        error: (err) => {
          console.error('Error loading student data:', err);
          this.error.student = true;
          this.loading.student = false;
          this.snackBar.open('Failed to load student data', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  loadAttendanceData(): void {
    if (!this.student) return;

    this.loading.attendance = true;

    const filters = this.filterForm.value;
    const fromDate = filters.fromDate ? new Date(filters.fromDate) : undefined;
    const toDate = filters.toDate ? new Date(filters.toDate) : undefined;

    this.studentService.getStudentAttendance(this.student.id, fromDate, toDate)
      .subscribe({
        next: (attendance) => {
          this.attendanceRecords = attendance;
          this.dataSource.data = attendance;
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
          this.loading.attendance = false;
        },
        error: (err) => {
          console.error('Error loading attendance data:', err);
          this.error.attendance = true;
          this.loading.attendance = false;
          this.snackBar.open('Failed to load attendance records', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  applyFilter(): void {
    this.loadAttendanceData();
  }

  resetFilter(): void {
    this.filterForm.reset();
    this.loadAttendanceData();
  }

  getStatusClass(status: number): string {
    switch (status) {
      case 0: return 'present';
      case 1: return 'absent';
      case 2: return 'late';
      case 3: return 'excused';
      case 4: return 'on-leave';
      default: return '';
    }
  }

  getStatusText(status: number): string {
    switch (status) {
      case 0: return 'Present';
      case 1: return 'Absent';
      case 2: return 'Late';
      case 3: return 'Excused';
      case 4: return 'On Leave';
      default: return 'Unknown';
    }
  }

  calculateAttendanceStats(): { present: number, absent: number, late: number, excused: number, onLeave: number, total: number, presentPercentage: number } {
    const total = this.attendanceRecords.length;
    const present = this.attendanceRecords.filter(a => a.status === 0).length;
    const absent = this.attendanceRecords.filter(a => a.status === 1).length;
    const late = this.attendanceRecords.filter(a => a.status === 2).length;
    const excused = this.attendanceRecords.filter(a => a.status === 3).length;
    const onLeave = this.attendanceRecords.filter(a => a.status === 4).length;

    const presentPercentage = total > 0 ? ((present + excused + late) / total) * 100 : 0;

    return { present, absent, late, excused, onLeave, total, presentPercentage };
  }
}
