.tenant-form-container {
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  gap: 16px;

  .header-content {
    flex: 1;

    .page-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 500;
      color: var(--primary-color);

      mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
      }
    }

    .page-subtitle {
      margin: 0;
      color: var(--text-secondary);
      font-size: 16px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

.form-section {
  margin-bottom: 24px;

  mat-card-header {
    margin-bottom: 16px;

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 500;

      mat-icon {
        color: var(--primary-color);
      }
    }

    mat-card-subtitle {
      margin-top: 4px;
      color: var(--text-secondary);
    }
  }
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: flex-start;

  &:last-child {
    margin-bottom: 0;
  }

  .full-width {
    flex: 1;
  }

  .half-width {
    flex: 1;
    min-width: 0;
  }

  .third-width {
    flex: 1;
    min-width: 0;
  }

  .quarter-width {
    flex: 1;
    min-width: 0;
  }
}

.trial-checkbox {
  margin: 8px 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--border-color);
}

// Form field customizations
mat-form-field {
  width: 100%;

  &.full-width {
    width: 100%;
  }

  .mat-mdc-form-field-hint-wrapper {
    font-size: 12px;
  }

  .mat-mdc-form-field-error-wrapper {
    font-size: 12px;
  }
}

// Button customizations
button {
  display: flex;
  align-items: center;
  gap: 8px;

  mat-spinner {
    margin-right: 4px;
  }
}

// Card customizations
mat-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;

  mat-card-content {
    padding: 16px 24px 24px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .tenant-form-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;

    .header-actions {
      justify-content: flex-end;
    }
  }

  .form-row {
    flex-direction: column;
    gap: 0;

    .half-width,
    .third-width,
    .quarter-width {
      width: 100%;
    }
  }

  .form-actions {
    flex-direction: column-reverse;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .page-header {
    .page-title {
      font-size: 24px;

      mat-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
      }
    }
  }

  mat-card {
    mat-card-content {
      padding: 12px 16px 16px;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  mat-card {
    background-color: var(--surface-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

// Loading state
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

// Validation styles
.mat-mdc-form-field.mat-form-field-invalid {
  .mat-mdc-text-field-wrapper {
    .mat-mdc-form-field-flex {
      .mat-mdc-floating-label {
        color: var(--warn-color);
      }
    }
  }
}

// Custom checkbox styles
.trial-checkbox {
  .mat-mdc-checkbox-frame {
    border-color: var(--primary-color);
  }

  &.mat-mdc-checkbox-checked {
    .mat-mdc-checkbox-background {
      background-color: var(--primary-color);
    }
  }
}
