.attendance-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.attendance-content {
  max-width: 1200px;
  margin: 0 auto;
}

.filter-card {
  margin-bottom: 24px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.stats-card {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: #f5f5f5;
}

.stat-value {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}

.present {
  color: #2e7d32;
}

.absent {
  color: #c62828;
}

.late {
  color: #ff8f00;
}

.excused {
  color: #1565c0;
}

.on-leave {
  color: #6a1b9a;
}

.percentage {
  grid-column: span 2;
  background-color: #e8f5e9;

  .stat-value {
    font-size: 32px;
    color: #2e7d32;
  }
}

.table-container {
  position: relative;
  min-height: 400px;
  overflow: auto;
}

.table-loading-shade,
.table-error-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c62828;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }

  span {
    margin-bottom: 16px;
  }
}

.attendance-table {
  width: 100%;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.present {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.absent {
  background-color: #ffebee;
  color: #c62828;
}

.late {
  background-color: #fff8e1;
  color: #ff8f00;
}

.excused {
  background-color: #e3f2fd;
  color: #1565c0;
}

.on-leave {
  background-color: #f3e5f5;
  color: #6a1b9a;
}

@media (max-width: 768px) {
  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .percentage {
    grid-column: span 2;
  }
}
