import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ContentService } from '../../core/services/content.service';

@Component({
  selector: 'app-content',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatDividerModule,
    MatChipsModule,
    TranslateModule
  ],
  templateUrl: './content.component.html',
  styleUrls: ['./content.component.scss']
})
export class ContentComponent implements OnInit {
  content: any = null;
  isLoading = true;
  error: string | null = null;
  slug: string = '';
  currentLanguage: string = 'en';

  constructor(
    private route: ActivatedRoute,
    private contentService: ContentService,
    private translateService: TranslateService
  ) {
    this.currentLanguage = this.translateService.currentLang || 'en';
  }

  ngOnInit(): void {
    // Check for slug in route params
    this.route.params.subscribe(params => {
      if (params['slug']) {
        this.slug = params['slug'];
        this.loadContent();
      }
    });

    // Check for slug in route data (for routes with fixed slugs)
    this.route.data.subscribe(data => {
      if (data['slug']) {
        this.slug = data['slug'];
        this.loadContent();
      }
    });

    this.translateService.onLangChange.subscribe(event => {
      this.currentLanguage = event.lang;
      if (this.content) {
        // Refresh content display when language changes
        this.loadContent();
      }
    });
  }

  getMediaTypeIcon(type: string): string {
    switch (type.toLowerCase()) {
      case 'image':
        return 'image';
      case 'video':
        return 'videocam';
      case 'document':
        return 'description';
      case 'pdf':
        return 'picture_as_pdf';
      default:
        return 'insert_drive_file';
    }
  }

  getContentTypeIcon(type: number): string {
    switch (type) {
      case 1: // Article
        return 'article';
      case 2: // News
        return 'feed';
      case 3: // Event
        return 'event';
      case 4: // Page
        return 'description';
      default:
        return 'article';
    }
  }

  getContentTypeLabel(type: number): string {
    switch (type) {
      case 1:
        return 'Article';
      case 2:
        return 'News';
      case 3:
        return 'Event';
      case 4:
        return 'Page';
      default:
        return 'Content';
    }
  }

  loadContent(): void {
    this.isLoading = true;
    this.error = null;

    this.contentService.getContentBySlug(this.slug).subscribe({
      next: (content) => {
        this.content = content;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading content:', error);
        this.error = 'Failed to load content. Please try again later.';
        this.isLoading = false;
      }
    });
  }

  getTranslation(field: string): string {
    if (!this.content) return '';

    // For Bengali language
    if (this.currentLanguage === 'bn') {
      // Look for translation in Bengali
      const translation = this.content.translations?.find(
        (t: any) => t.languageCode === 'bn'
      );

      if (translation && translation[field]) {
        return translation[field];
      }
    }

    // Default to English content
    return this.content[field] || '';
  }
}


