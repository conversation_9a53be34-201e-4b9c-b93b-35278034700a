.dashboard-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.dashboard-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.info-card {
  grid-column: span 2;
}

.gpa-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
}

.gpa-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}

.gpa-value {
  font-size: 48px;
  font-weight: 500;
  color: #3f51b5;
}

.gpa-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  margin-top: 8px;
}

.attendance-list,
.fees-list,
.results-list,
.leaves-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attendance-item,
.fee-item,
.result-item,
.leave-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.attendance-date {
  font-weight: 500;
}

.attendance-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.present {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.absent {
  background-color: #ffebee;
  color: #c62828;
}

.late {
  background-color: #fff8e1;
  color: #ff8f00;
}

.excused {
  background-color: #e3f2fd;
  color: #1565c0;
}

.on-leave {
  background-color: #f3e5f5;
  color: #6a1b9a;
}

.fee-info,
.result-info,
.leave-info {
  display: flex;
  flex-direction: column;
}

.fee-type,
.result-subject,
.leave-type {
  font-weight: 500;
}

.fee-description,
.result-exam,
.leave-dates {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
}

.fee-amount,
.result-marks {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.fee-due,
.result-grade {
  font-weight: 500;
}

.fee-date,
.result-score {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
}

.leave-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.pending {
  background-color: #fff8e1;
  color: #ff8f00;
}

.no-data {
  color: rgba(0, 0, 0, 0.6);
  font-style: italic;
  text-align: center;
  padding: 16px 0;
}

.error-message {
  color: #c62828;
  text-align: center;
  padding: 16px 0;
}

mat-card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px 16px;
}

@media (max-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }

  .info-card {
    grid-column: span 1;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
