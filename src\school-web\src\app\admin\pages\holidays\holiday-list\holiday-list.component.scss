.holiday-list-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;

      .page-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0;
        font-size: 2rem;
        font-weight: 500;
        color: var(--primary-color);

        mat-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
        }
      }
    }
  }

  .filters-card {
    margin-bottom: 24px;

    .filters-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: flex-end;

      .search-field {
        flex: 1;
        min-width: 250px;
      }

      mat-form-field {
        min-width: 150px;
      }

      .status-filters {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-left: 16px;

        mat-slide-toggle {
          font-size: 14px;
        }
      }
    }
  }

  .table-card {
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px;
      gap: 16px;

      p {
        margin: 0;
        color: var(--text-secondary);
      }
    }

    .table-container {
      .holidays-table {
        width: 100%;
        
        .mat-mdc-header-cell {
          font-weight: 600;
          color: var(--text-primary);
        }

        .mat-mdc-cell {
          padding: 16px 8px;
        }

        // Holiday Name Column
        .holiday-name {
          display: flex;
          align-items: center;
          gap: 12px;

          .color-indicator {
            width: 4px;
            height: 40px;
            border-radius: 2px;
            flex-shrink: 0;
          }

          .name-content {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .name {
              font-weight: 500;
              color: var(--text-primary);
            }

            .description {
              font-size: 12px;
              color: var(--text-secondary);
              line-height: 1.3;
              max-width: 200px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }

        // Date Info
        .date-info {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .date {
            font-weight: 500;
          }

          .duration {
            font-size: 12px;
            color: var(--text-secondary);
          }

          .upcoming-chip {
            align-self: flex-start;
            font-size: 10px;
            height: 20px;
            line-height: 20px;
          }
        }

        // Status Indicators
        .status-indicators {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;

          mat-chip {
            font-size: 11px;
            height: 24px;
            line-height: 24px;

            &.active-chip {
              background-color: #4CAF50;
              color: white;
            }

            &.inactive-chip {
              background-color: #9E9E9E;
              color: white;
            }

            &.public-chip {
              background-color: #2196F3;
              color: white;
            }

            &.recurring-chip {
              background-color: #FF9800;
              color: white;
            }

            &.current-chip {
              background-color: #E91E63;
              color: white;
            }
          }
        }

        // Actions
        .actions-container {
          display: flex;
          justify-content: center;

          .delete-action {
            color: #f44336;

            mat-icon {
              color: #f44336;
            }
          }
        }
      }

      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 64px 24px;
        text-align: center;
        color: var(--text-secondary);

        mat-icon {
          font-size: 4rem;
          width: 4rem;
          height: 4rem;
          margin-bottom: 16px;
          opacity: 0.5;
        }

        h3 {
          margin: 0 0 8px 0;
          font-weight: 500;
        }

        p {
          margin: 0 0 24px 0;
          max-width: 400px;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .holiday-list-container {
    padding: 16px;

    .page-header .header-content {
      flex-direction: column;
      align-items: stretch;

      .page-title {
        font-size: 1.5rem;
        justify-content: center;
      }
    }

    .filters-card .filters-row {
      flex-direction: column;

      .search-field,
      mat-form-field {
        width: 100%;
        min-width: unset;
      }

      .status-filters {
        margin-left: 0;
        flex-direction: row;
        justify-content: space-around;
      }
    }

    .table-container {
      overflow-x: auto;

      .holidays-table {
        min-width: 800px;
      }
    }
  }
}

@media (max-width: 480px) {
  .holiday-list-container {
    padding: 12px;

    .page-header .header-content .page-title {
      font-size: 1.25rem;

      mat-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }
    }

    .filters-card .filters-row .status-filters {
      flex-direction: column;
      gap: 12px;
    }
  }
}

// Dark theme support
.dark-theme {
  .holiday-list-container {
    .page-header .header-content .page-title {
      color: var(--primary-color-light);
    }

    .table-container .holidays-table {
      .mat-mdc-header-cell {
        color: var(--text-primary-dark);
      }

      .holiday-name .name-content {
        .name {
          color: var(--text-primary-dark);
        }

        .description {
          color: var(--text-secondary-dark);
        }
      }

      .date-info .duration {
        color: var(--text-secondary-dark);
      }
    }

    .no-data {
      color: var(--text-secondary-dark);
    }

    .loading-container p {
      color: var(--text-secondary-dark);
    }
  }
}

// Print styles
@media print {
  .holiday-list-container {
    .page-header,
    .filters-card {
      display: none;
    }

    .table-container {
      .holidays-table {
        .actions-container {
          display: none;
        }
      }

      mat-paginator {
        display: none;
      }
    }
  }
}
