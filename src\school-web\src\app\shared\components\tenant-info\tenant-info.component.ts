import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TenantService, TenantInfo } from '../../../core/services/tenant.service';

@Component({
  selector: 'app-tenant-info',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="tenant-info" *ngIf="currentTenant">
      <div class="tenant-badge" [class.trial]="currentTenant.isTrialActive">
        <div class="tenant-logo" *ngIf="currentTenant.customDomain">
          <img [src]="getTenantLogoUrl()" [alt]="currentTenant.displayName + ' logo'" />
        </div>
        <div class="tenant-details">
          <div class="tenant-name">{{ currentTenant.displayName }}</div>
          <div class="tenant-type">{{ getTenantTypeDisplay() }}</div>
          <div class="trial-indicator" *ngIf="currentTenant.isTrialActive">
            <span class="trial-badge">Trial</span>
            <span class="trial-days" *ngIf="getTrialDaysRemaining() > 0">
              {{ getTrialDaysRemaining() }} days left
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Loading state -->
    <div class="tenant-loading" *ngIf="!tenantLoaded">
      <div class="loading-spinner"></div>
      <span>Loading organization...</span>
    </div>
  `,
  styles: [`
    .tenant-info {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .tenant-badge {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .tenant-badge.trial {
      border-left: 3px solid #ff9800;
      padding-left: 12px;
    }

    .tenant-logo img {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      object-fit: cover;
    }

    .tenant-details {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .tenant-name {
      font-weight: 600;
      font-size: 14px;
      color: white;
      line-height: 1.2;
    }

    .tenant-type {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.8);
      text-transform: capitalize;
    }

    .trial-indicator {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-top: 2px;
    }

    .trial-badge {
      background: #ff9800;
      color: white;
      font-size: 10px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 10px;
      text-transform: uppercase;
    }

    .trial-days {
      font-size: 11px;
      color: #ffcc80;
      font-weight: 500;
    }

    .tenant-loading {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Dark theme adjustments */
    :host-context(.dark-theme) .tenant-info {
      background: rgba(0, 0, 0, 0.2);
      border-color: rgba(255, 255, 255, 0.1);
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
      .tenant-info {
        padding: 6px 8px;
      }
      
      .tenant-name {
        font-size: 13px;
      }
      
      .tenant-type {
        font-size: 11px;
      }
      
      .tenant-logo img {
        width: 24px;
        height: 24px;
      }
    }

    /* Compact mode for smaller screens */
    @media (max-width: 480px) {
      .tenant-details {
        gap: 1px;
      }
      
      .trial-indicator {
        margin-top: 1px;
      }
      
      .trial-days {
        display: none; /* Hide trial days on very small screens */
      }
    }
  `]
})
export class TenantInfoComponent implements OnInit, OnDestroy {
  currentTenant: TenantInfo | null = null;
  tenantLoaded: boolean = false;
  private destroy$ = new Subject<void>();

  constructor(private tenantService: TenantService) {}

  ngOnInit(): void {
    // Subscribe to current tenant changes
    this.tenantService.currentTenant$
      .pipe(takeUntil(this.destroy$))
      .subscribe(tenant => {
        this.currentTenant = tenant;
      });

    // Subscribe to tenant loaded state
    this.tenantService.tenantLoaded$
      .pipe(takeUntil(this.destroy$))
      .subscribe(loaded => {
        this.tenantLoaded = loaded;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getTenantTypeDisplay(): string {
    if (!this.currentTenant) return '';
    
    // Convert enum value to display text
    switch (this.currentTenant.type.toLowerCase()) {
      case 'primaryschool':
        return 'Primary School';
      case 'secondaryschool':
        return 'Secondary School';
      case 'school':
        return 'School';
      case 'college':
        return 'College';
      case 'university':
        return 'University';
      case 'technicalinstitute':
        return 'Technical Institute';
      case 'trainingcenter':
        return 'Training Center';
      case 'academy':
        return 'Academy';
      case 'internationalschool':
        return 'International School';
      case 'onlineschool':
        return 'Online School';
      default:
        return this.currentTenant.type;
    }
  }

  getTenantLogoUrl(): string {
    if (!this.currentTenant) return '';
    
    // You can implement logo URL logic here
    // For now, return a placeholder or default logo
    return `/assets/images/tenants/${this.currentTenant.slug}/logo.png`;
  }

  getTrialDaysRemaining(): number {
    if (!this.currentTenant?.trialEndDate) return 0;
    
    const trialEnd = new Date(this.currentTenant.trialEndDate);
    const now = new Date();
    const diffTime = trialEnd.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  }
}
