import { Component, OnInit } from '@angular/core';
import { CommonModule, CurrencyPipe, DatePipe } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatError } from '@angular/material/form-field';

// Services and models
import { StudentService } from '../../../core/services/student.service';
import { AuthService } from '../../../core/services/auth.service';
import { Student, StudentFee, FeeType, PaymentStatus } from '../../../core/models/student.model';

@Component({
  selector: 'app-student-fees',
  templateUrl: './student-fees.component.html',
  styleUrls: ['./student-fees.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CurrencyPipe,
    DatePipe,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatSnackBarModule,
    MatError
  ]
})
export class StudentFeesComponent implements OnInit {
  student: Student | null = null;
  fees: StudentFee[] = [];

  filterForm: FormGroup;

  loading = {
    student: true,
    fees: true
  };

  error = {
    student: false,
    fees: false
  };

  feeTypes = [
    { value: FeeType.Tuition, label: 'Tuition' },
    { value: FeeType.Admission, label: 'Admission' },
    { value: FeeType.Examination, label: 'Examination' },
    { value: FeeType.Library, label: 'Library' },
    { value: FeeType.Laboratory, label: 'Laboratory' },
    { value: FeeType.Transport, label: 'Transport' },
    { value: FeeType.Hostel, label: 'Hostel' },
    { value: FeeType.Other, label: 'Other' }
  ];

  paymentStatuses = [
    { value: PaymentStatus.Pending, label: 'Pending' },
    { value: PaymentStatus.Partial, label: 'Partial' },
    { value: PaymentStatus.Paid, label: 'Paid' },
    { value: PaymentStatus.Overdue, label: 'Overdue' },
    { value: PaymentStatus.Waived, label: 'Waived' }
  ];

  // Loading and error states are defined above

  constructor(
    private studentService: StudentService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    const currentYear = new Date().getFullYear();

    this.filterForm = this.formBuilder.group({
      academicYear: [currentYear]
    });
  }

  ngOnInit(): void {
    this.loadStudentData();
  }

  loadStudentData(): void {
    this.loading.student = true;

    // In a real application, you would fetch the student by user ID
    // For now, we'll use a mock student ID
    this.studentService.getStudentByStudentId('S2023-001')
      .subscribe({
        next: (student) => {
          this.student = student;
          this.loading.student = false;
          this.loadFees();
        },
        error: (err) => {
          console.error('Error loading student data:', err);
          this.error.student = true;
          this.loading.student = false;
          this.snackBar.open('Failed to load student data', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  loadFees(): void {
    if (!this.student) return;

    this.loading.fees = true;
    this.error.fees = false;

    const filters = this.filterForm.value;

    this.studentService.getStudentFees(this.student.id, filters.academicYear)
      .subscribe({
        next: (fees) => {
          this.fees = fees;
          this.loading.fees = false;
        },
        error: (err) => {
          console.error('Error loading fees:', err);
          this.error.fees = true;
          this.loading.fees = false;
          this.snackBar.open('Failed to load fee information', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  applyFilter(): void {
    this.loadFees();
  }

  getFeeTypeLabel(type: FeeType): string {
    return this.feeTypes.find(t => t.value === type)?.label || 'Unknown';
  }

  getStatusLabel(status: PaymentStatus): string {
    return this.paymentStatuses.find(s => s.value === status)?.label || 'Unknown';
  }

  getStatusClass(status: PaymentStatus): string {
    switch (status) {
      case PaymentStatus.Paid: return 'paid';
      case PaymentStatus.Partial: return 'partial';
      case PaymentStatus.Pending: return 'pending';
      case PaymentStatus.Overdue: return 'overdue';
      case PaymentStatus.Waived: return 'waived';
      default: return '';
    }
  }

  calculateTotalFees(): number {
    return this.fees.reduce((total, fee) => total + fee.amount, 0);
  }

  calculateTotalPaid(): number {
    return this.fees.reduce((total, fee) => total + fee.paidAmount, 0);
  }

  calculateTotalDue(): number {
    return this.fees.reduce((total, fee) => total + fee.dueAmount, 0);
  }

  isPastDue(dueDate: Date): boolean {
    return new Date(dueDate) < new Date();
  }
}
