import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ApiResponse } from '../models/api-response.model';
import { PagedResult } from '../models/paged-result.model';
import {
  AcademicYear,
  CreateAcademicYear,
  UpdateAcademicYear,
  AcademicYearFilter,
  AcademicYearStatistics,
  CreateAcademicYearTranslation,
  UpdateAcademicYearTranslation,
  AcademicYearTranslation
} from '../models/academic-year.model';

@Injectable({
  providedIn: 'root'
})
export class AcademicYearService {
  private readonly apiUrl = `${environment.apiUrl}/academic-years`;

  constructor(private http: HttpClient) {}

  // Academic Year CRUD operations
  getAcademicYears(filter: AcademicYearFilter): Observable<ApiResponse<PagedResult<AcademicYear>>> {
    let params = new HttpParams()
      .set('page', filter.page.toString())
      .set('pageSize', filter.pageSize.toString())
      .set('sortDescending', filter.sortDescending.toString());

    if (filter.name) {
      params = params.set('name', filter.name);
    }
    if (filter.code) {
      params = params.set('code', filter.code);
    }
    if (filter.status !== undefined) {
      params = params.set('status', filter.status.toString());
    }
    if (filter.isCurrentYear !== undefined) {
      params = params.set('isCurrentYear', filter.isCurrentYear.toString());
    }
    if (filter.startDateFrom) {
      params = params.set('startDateFrom', filter.startDateFrom.toISOString());
    }
    if (filter.startDateTo) {
      params = params.set('startDateTo', filter.startDateTo.toISOString());
    }
    if (filter.endDateFrom) {
      params = params.set('endDateFrom', filter.endDateFrom.toISOString());
    }
    if (filter.endDateTo) {
      params = params.set('endDateTo', filter.endDateTo.toISOString());
    }
    if (filter.sortBy) {
      params = params.set('sortBy', filter.sortBy);
    }

    return this.http.get<ApiResponse<PagedResult<AcademicYear>>>(this.apiUrl, { params });
  }

  getAcademicYear(id: string): Observable<ApiResponse<AcademicYear>> {
    return this.http.get<ApiResponse<AcademicYear>>(`${this.apiUrl}/${id}`);
  }

  getCurrentAcademicYear(): Observable<ApiResponse<AcademicYear>> {
    return this.http.get<ApiResponse<AcademicYear>>(`${this.apiUrl}/current`);
  }

  getActiveAcademicYears(): Observable<AcademicYear[]> {
    return this.http.get<AcademicYear[]>(`${this.apiUrl}/active`);
  }

  createAcademicYear(academicYear: CreateAcademicYear): Observable<ApiResponse<string>> {
    return this.http.post<ApiResponse<string>>(this.apiUrl, academicYear);
  }

  updateAcademicYear(id: string, academicYear: UpdateAcademicYear): Observable<ApiResponse<void>> {
    return this.http.put<ApiResponse<void>>(`${this.apiUrl}/${id}`, academicYear);
  }

  deleteAcademicYear(id: string): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  // Academic Year status management
  setCurrentAcademicYear(id: string): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/${id}/set-current`, {});
  }

  activateAcademicYear(id: string): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/${id}/activate`, {});
  }

  completeAcademicYear(id: string): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/${id}/complete`, {});
  }

  archiveAcademicYear(id: string): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/${id}/archive`, {});
  }

  // Statistics
  getAcademicYearStatistics(id: string): Observable<ApiResponse<AcademicYearStatistics>> {
    return this.http.get<ApiResponse<AcademicYearStatistics>>(`${this.apiUrl}/${id}/statistics`);
  }

  // Translation management
  addTranslation(academicYearId: string, translation: CreateAcademicYearTranslation): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/${academicYearId}/translations`, translation);
  }

  updateTranslation(academicYearId: string, languageCode: string, translation: UpdateAcademicYearTranslation): Observable<ApiResponse<void>> {
    return this.http.put<ApiResponse<void>>(`${this.apiUrl}/${academicYearId}/translations/${languageCode}`, translation);
  }

  deleteTranslation(academicYearId: string, languageCode: string): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${academicYearId}/translations/${languageCode}`);
  }

  getTranslations(academicYearId: string): Observable<ApiResponse<AcademicYearTranslation[]>> {
    return this.http.get<ApiResponse<AcademicYearTranslation[]>>(`${this.apiUrl}/${academicYearId}/translations`);
  }

  // Utility methods
  getAcademicYearStatusText(status: number): string {
    const statusMap: { [key: number]: string } = {
      0: 'Draft',
      1: 'Active',
      2: 'Completed',
      3: 'Archived',
      4: 'Cancelled'
    };
    return statusMap[status] || 'Unknown';
  }

  getAcademicYearStatusColor(status: number): string {
    const colorMap: { [key: number]: string } = {
      0: 'warn',      // Draft - Orange
      1: 'primary',   // Active - Blue
      2: 'accent',    // Completed - Green
      3: '',          // Archived - Default
      4: 'warn'       // Cancelled - Red
    };
    return colorMap[status] || '';
  }
}
