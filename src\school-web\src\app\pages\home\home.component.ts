import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { EnhancedHeroCarouselComponent, EnhancedCarouselSlide } from '../../shared/components/enhanced-hero-carousel/enhanced-hero-carousel.component';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    EnhancedHeroCarouselComponent
  ]
})
export class HomeComponent {
  enhancedHeroSlides: EnhancedCarouselSlide[] = [
    {
      image: 'assets/images/hero/slide1.jpg',
      title: 'Welcome to the school',
      subtitle: 'Nurturing Excellence, Building Character, Shaping Future Leaders',
      description: 'Join our vibrant community where students thrive academically and personally in a supportive environment.',
      buttonText: 'Apply Now',
      buttonLink: '/admissions',
      secondaryButtonText: 'Learn More',
      secondaryButtonLink: '/about'
    },
    {
      image: 'assets/images/hero/slide2.jpg',
      title: 'Excellence in Education',
      subtitle: 'Providing Quality Education Since 1998',
      description: 'Our rigorous curriculum and dedicated faculty ensure students receive a world-class education.',
      buttonText: 'Explore Academics',
      buttonLink: '/academics',
      secondaryButtonText: 'Meet Our Faculty',
      secondaryButtonLink: '/faculty'
    },
    {
      image: 'assets/images/hero/slide3.jpg',
      title: 'Building Tomorrow\'s Leaders',
      subtitle: 'Comprehensive Development Through Modern Education',
      description: 'We focus on developing well-rounded individuals prepared for future challenges and opportunities.',
      buttonText: 'Student Life',
      buttonLink: '/student-life',
      secondaryButtonText: 'Alumni Success',
      secondaryButtonLink: '/alumni'
    }
  ];

  // Check if images exist and set up default slides if needed
  constructor() {
    // If in a real environment, we would check if the images exist
    // For now, we'll ensure the component handles missing images gracefully
    this.ensureValidSlides();
  }

  private ensureValidSlides(): void {
    // If no slides are provided, the component will use its default slide
    if (this.enhancedHeroSlides.length === 0) {
      this.enhancedHeroSlides = [{
        image: '',
        title: 'Welcome to Shahab Uddin Memorial Academy',
        subtitle: 'Nurturing Excellence, Building Character, Shaping Future Leaders',
        description: 'Join our vibrant community where students thrive academically and personally in a supportive environment.'
      }];
    }
  }

  // Latest news data
  latestNews = [
    {
      title: 'SUMA Students Win National Science Competition',
      date: '2024-03-15',
      image: 'assets/images/news/science-competition.jpg',
      excerpt: 'Our talented students secured first place in the National Science Innovation Challenge...'
    },
    {
      title: 'New STEM Innovation Center Opening',
      date: '2024-03-10',
      image: 'assets/images/news/stem-center.jpg',
      excerpt: 'State-of-the-art facility featuring advanced laboratories and research equipment...'
    },
    {
      title: 'Annual Cultural Festival Highlights',
      date: '2024-03-05',
      image: 'assets/images/news/cultural-fest.jpg',
      excerpt: 'Celebrating diversity through performances, exhibitions, and international cuisine...'
    }
  ];
}
