import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { FacultyService } from '../../../core/services/faculty.service';
import { AuthService } from '../../../core/services/auth.service';
import { FacultyDetail, FacultySchedule, DayOfWeek } from '../../../core/models/faculty.model';

@Component({
  selector: 'app-faculty-schedule',
  templateUrl: './faculty-schedule.component.html',
  styleUrls: ['./faculty-schedule.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatTableModule,
    MatSnackBarModule
  ]
})
export class FacultyScheduleComponent implements OnInit {
  faculty: FacultyDetail | null = null;
  schedule: FacultySchedule[] = [];
  selectedDay: DayOfWeek | null = null;

  loading = {
    faculty: true,
    schedule: false
  };

  error = {
    faculty: false,
    schedule: false
  };

  days = [
    { value: DayOfWeek.Sunday, label: 'Sunday' },
    { value: DayOfWeek.Monday, label: 'Monday' },
    { value: DayOfWeek.Tuesday, label: 'Tuesday' },
    { value: DayOfWeek.Wednesday, label: 'Wednesday' },
    { value: DayOfWeek.Thursday, label: 'Thursday' },
    { value: DayOfWeek.Friday, label: 'Friday' },
    { value: DayOfWeek.Saturday, label: 'Saturday' }
  ];

  constructor(
    private facultyService: FacultyService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadFacultyData();
    // Set the selected day to the current day of the week
    this.selectedDay = new Date().getDay() as DayOfWeek;
  }

  loadFacultyData(): void {
    this.loading.faculty = true;

    // In a real application, you would fetch the faculty by user ID
    const userId = this.authService.getCurrentUserId();
    if (!userId) {
      this.error.faculty = true;
      this.loading.faculty = false;
      this.snackBar.open('User ID not found. Please log in again.', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    // For now, we'll use a mock faculty ID
    this.facultyService.getFaculty(1)
      .subscribe({
        next: (faculty) => {
          this.faculty = faculty;
          this.loading.faculty = false;
          this.loadSchedule();
        },
        error: (err) => {
          console.error('Error loading faculty data:', err);
          this.error.faculty = true;
          this.loading.faculty = false;
          this.snackBar.open('Failed to load faculty data', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  loadSchedule(): void {
    if (!this.faculty || this.selectedDay === null) return;

    this.loading.schedule = true;
    this.error.schedule = false;

    this.facultyService.getFacultySchedule(this.faculty.id, this.selectedDay)
      .subscribe({
        next: (schedule) => {
          this.schedule = schedule.sort((a, b) => a.periodNumber - b.periodNumber);
          this.loading.schedule = false;
        },
        error: (err) => {
          console.error('Error loading schedule:', err);
          this.error.schedule = true;
          this.loading.schedule = false;
          this.snackBar.open('Failed to load schedule', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  onDayChange(): void {
    this.loadSchedule();
  }

  getDayName(day: DayOfWeek | null): string {
    if (day === null) return 'No day selected';
    return this.days.find(d => d.value === day)?.label || 'Unknown';
  }

  getCurrentDayName(): string {
    const today = new Date().getDay();
    return this.getDayName(today as DayOfWeek);
  }

  isCurrentDay(day: DayOfWeek): boolean {
    return day === new Date().getDay();
  }
}
