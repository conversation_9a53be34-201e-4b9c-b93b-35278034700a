// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Global Styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

section {
  padding: $section-padding;
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 0.5rem;
  color: $text-color;
}

.section-subtitle {
  font-size: 1.2rem;
  text-align: center;
  margin-bottom: 3rem;
  color: $dark-gray;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

// Hero Section
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), linear-gradient(to right, #3f51b5, #2196f3);
  background-size: cover;
  background-position: center;
  color: $white;
  text-align: center;
  padding: 150px 0;
  margin-bottom: 0;

  .hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;

    h1 {
      font-size: 3.5rem;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    p {
      font-size: 1.5rem;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .cta-button {
      padding: 12px 32px;
      font-size: 1.1rem;
      font-weight: 500;
    }
  }
}

// Featured Alumni Section
.featured-alumni-section {
  background-color: $white;

  .featured-alumni-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
  }

  .alumni-card {
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    &.featured-card {
      border-top: 4px solid $primary-color;
    }
  }

  .alumni-image-container {
    height: 250px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  mat-card-header {
    padding-top: 16px;
  }

  mat-card-title {
    font-size: 1.5rem;
    margin-bottom: 8px;
  }

  mat-card-subtitle {
    color: $dark-gray;
    font-size: 1rem;
  }

  .alumni-bio {
    margin: 16px 0;
    line-height: 1.6;
  }

  .achievements {
    margin: 16px 0;

    h4 {
      font-size: 1.1rem;
      margin-bottom: 8px;
      color: $text-color;
    }

    ul {
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        line-height: 1.4;
      }
    }
  }

  .testimonial {
    background-color: $light-gray;
    padding: 16px;
    border-left: 4px solid $primary-color;
    margin: 20px 0 10px;
    font-style: italic;

    p {
      margin: 0;
      line-height: 1.6;
    }
  }
}

// Events Section
.events-section {
  background-color: $light-gray;

  .events-tabs {
    margin-top: 30px;
  }

  .events-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    padding: 30px 0;
  }

  .event-card {
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }
  }

  .event-image-container {
    height: 200px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  mat-card-header {
    padding-top: 16px;
  }

  mat-card-title {
    font-size: 1.3rem;
    margin-bottom: 12px;
  }

  mat-card-subtitle {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .event-date, .event-location {
    display: flex;
    align-items: center;
    gap: 8px;
    color: $dark-gray;

    mat-icon {
      font-size: 18px;
      height: 18px;
      width: 18px;
      line-height: 18px;
    }
  }

  mat-card-content {
    flex-grow: 1;
    padding: 16px;

    p {
      line-height: 1.6;
      margin: 0;
    }
  }

  mat-card-actions {
    padding: 16px;
    display: flex;
    justify-content: flex-start;
    gap: 8px;
  }

  .no-events-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 0;
    color: $dark-gray;
    font-size: 1.1rem;
  }
}

// Directory Section
.directory-section {
  background-color: $white;

  .directory-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
    align-items: center;

    .search-field {
      flex: 1 1 300px;
    }

    .year-filter {
      flex: 0 1 200px;
    }

    .reset-button {
      height: 56px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .alumni-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
  }

  .alumni-card {
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }
  }

  .alumni-image-container {
    height: 180px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .alumni-bio-short {
    color: $dark-gray;
    line-height: 1.5;
    margin: 10px 0;
  }

  mat-card-actions {
    margin-top: auto;
    padding: 16px;
    display: flex;
    justify-content: space-between;
  }

  .no-results-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 0;
    color: $dark-gray;
    font-size: 1.1rem;
  }
}

// Get Involved Section
.get-involved-section {
  background-color: $light-gray;

  .involvement-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
  }

  .involvement-card {
    text-align: center;
    padding: 30px 20px;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .involvement-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 20px;
      color: $primary-color;
    }

    h3 {
      font-size: 1.5rem;
      margin-bottom: 15px;
      color: $text-color;
    }

    p {
      margin-bottom: 20px;
      line-height: 1.6;
      color: $dark-gray;
    }

    button {
      margin-top: auto;
    }
  }
}

// Contact Section
.contact-section {
  background-color: $white;

  .contact-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 50px;
    margin-top: 40px;
  }

  .contact-info {
    h3 {
      font-size: 1.5rem;
      margin-bottom: 20px;
      color: $text-color;
    }

    p {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;
      color: $dark-gray;

      mat-icon {
        color: $primary-color;
      }
    }

    .social-heading {
      margin-top: 30px;
    }

    .social-icons {
      display: flex;
      gap: 15px;
      margin-top: 15px;

      .social-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: $light-gray;
        color: $primary-color;
        transition: background-color 0.3s ease, color 0.3s ease;

        &:hover {
          background-color: $primary-color;
          color: $white;
        }

        mat-icon {
          font-size: 20px;
          height: 20px;
          width: 20px;
        }
      }
    }
  }

  .newsletter-signup {
    h3 {
      font-size: 1.5rem;
      margin-bottom: 15px;
      color: $text-color;
    }

    p {
      margin-bottom: 20px;
      color: $dark-gray;
      line-height: 1.6;
    }

    .email-field {
      width: 100%;
      margin-bottom: 20px;
    }

    button {
      padding: 8px 24px;
    }
  }
}

// Responsive Adjustments
@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  .hero-section {
    padding: 100px 0;

    .hero-content {
      h1 {
        font-size: 2.5rem;
      }

      p {
        font-size: 1.2rem;
      }
    }
  }

  .featured-alumni-grid,
  .events-container,
  .alumni-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .directory-filters {
    flex-direction: column;
    align-items: stretch;

    .search-field,
    .year-filter {
      flex: 1 1 100%;
    }
  }

  .involvement-options {
    grid-template-columns: 1fr;
  }

  .contact-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  section {
    padding: 60px 0;
  }

  .hero-section {
    padding: 80px 0;

    .hero-content {
      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }
  }

  .featured-alumni-grid,
  .events-container,
  .alumni-grid {
    grid-template-columns: 1fr;
  }

  .alumni-card,
  .event-card {
    max-width: 100%;
  }
}
