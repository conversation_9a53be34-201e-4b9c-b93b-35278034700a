using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ClubLeaderConfiguration : IEntityTypeConfiguration<ClubLeader>
{
    public void Configure(EntityTypeBuilder<ClubLeader> builder)
    {
        builder.HasKey(l => l.Id);
        
        builder.Property(l => l.Name)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(l => l.Role)
            .IsRequired()
            .HasMaxLength(50);
            
        builder.Property(l => l.Grade)
            .HasMaxLength(20);
            
        builder.Property(l => l.ProfileImageUrl)
            .HasMaxLength(255);
            
        builder.Property(l => l.DisplayOrder)
            .HasDefaultValue(0);
            
        // Relationships
        builder.HasOne(l => l.Student)
            .WithMany()
            .HasForeignKey(l => l.StudentId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
