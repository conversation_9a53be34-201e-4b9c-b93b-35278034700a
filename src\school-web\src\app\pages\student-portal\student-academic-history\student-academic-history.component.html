<div class="history-container">
  <h1 class="page-title">Academic History</h1>

  <div *ngIf="loading.student" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error.student" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load student data. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadStudentData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading.student && !error.student && student" class="history-content">
    <!-- Loading Indicator -->
    <div *ngIf="loading.history" class="history-loading">
      <mat-progress-bar mode="indeterminate"></mat-progress-bar>
    </div>

    <!-- Error Message -->
    <div *ngIf="error.history" class="history-error">
      <mat-error>
        <mat-icon>error</mat-icon>
        <span>Failed to load academic history. Please try again.</span>
        <button mat-button color="warn" (click)="loadAcademicHistory()">Retry</button>
      </mat-error>
    </div>

    <!-- No History Message -->
    <div *ngIf="!loading.history && !error.history && academicHistory.length === 0" class="no-history">
      <mat-card>
        <mat-card-content>
          <p>No academic history records found.</p>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Timeline -->
    <div *ngIf="!loading.history && !error.history && academicHistory.length > 0" class="timeline">
      <div *ngFor="let record of academicHistory; let i = index" class="timeline-item" [ngClass]="{'current-year': isCurrentYear(record.academicYear)}">
        <div class="timeline-badge">
          <span class="year">{{ record.academicYear }}</span>
        </div>

        <mat-card class="timeline-card">
          <mat-card-header>
            <mat-card-title>
              Class {{ record.grade }} - {{ record.section }}
              <span *ngIf="isCurrentYear(record.academicYear)" class="current-badge">Current</span>
            </mat-card-title>
            <mat-card-subtitle>
              {{ getMediumLabel(record.medium) }} Medium | {{ getShiftLabel(record.shift) }} Shift
            </mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <div class="history-details">
              <div class="detail-item">
                <span class="detail-label">Student ID:</span>
                <span class="detail-value">{{ record.studentIdForYear }}</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">Roll Number:</span>
                <span class="detail-value">{{ record.rollNumber }}</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">Final GPA:</span>
                <span class="detail-value" [style.color]="getGradeColor(record.finalGPA)">
                  {{ record.finalGPA !== undefined ? record.finalGPA.toFixed(2) : 'N/A' }}
                  <span class="grade-badge" *ngIf="record.finalGPA !== undefined" [style.background-color]="getGradeColor(record.finalGPA)">
                    {{ getGradeLabel(record.finalGPA) }}
                  </span>
                </span>
              </div>

              <div class="detail-item" *ngIf="record.classTeacher">
                <span class="detail-label">Class Teacher:</span>
                <span class="detail-value">{{ record.classTeacher.name || 'Not assigned' }}</span>
              </div>

              <div class="detail-item" *ngIf="record.remarks">
                <span class="detail-label">Remarks:</span>
                <span class="detail-value">{{ record.remarks }}</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">Promotion Status:</span>
                <span class="detail-value" [ngClass]="{'promoted': record.isPromoted, 'not-promoted': !record.isPromoted}">
                  {{ record.isPromoted ? 'Promoted' : 'Not Promoted' }}
                </span>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
</div>
