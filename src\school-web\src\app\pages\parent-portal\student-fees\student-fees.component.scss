.fees-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.fees-loading,
.fees-error {
  margin-bottom: 24px;
}

.fees-error {
  text-align: center;
  padding: 16px;

  mat-icon {
    vertical-align: middle;
    margin-right: 8px;
  }
}

.fees-table-container {
  overflow-x: auto;
  margin-bottom: 24px;
}

.fees-table {
  width: 100%;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.paid {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.unpaid {
  background-color: #e3f2fd;
  color: #1565c0;
}

.overdue {
  background-color: #ffebee;
  color: #c62828;
}

.partial {
  background-color: #fff8e1;
  color: #ff8f00;
}

.no-data {
  text-align: center;
  padding: 16px;
}
