using <PERSON>;
using Microsoft.AspNetCore.Mvc;
using School.Application.Features.Auth;
using School.Application.Features.Auth.DTOs;

namespace School.API.Features.Auth;

/// <summary>
/// Authentication API endpoints
/// </summary>
public class AuthEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/auth")
            .WithTags("Authentication")
            .AllowAnonymous();

        // Authentication endpoints
        group.MapPost("/login", Login)
            .WithName("Login")
            .WithSummary("User login")
            .Produces<LoginResponseDto>()
            .Produces(400)
            .Produces(401);

        group.MapPost("/refresh", RefreshToken)
            .WithName("RefreshToken")
            .WithSummary("Refresh access token")
            .Produces<LoginResponseDto>()
            .Produces(400)
            .Produces(401);

        group.MapPost("/logout", Logout)
            .WithName("Logout")
            .WithSummary("User logout")
            .Produces(200)
            .RequireAuthorization();

        group.MapPost("/forgot-password", ForgotPassword)
            .WithName("ForgotPassword")
            .WithSummary("Request password reset")
            .Produces(200)
            .Produces(400);

        group.MapPost("/reset-password", ResetPassword)
            .WithName("ResetPassword")
            .WithSummary("Reset password with token")
            .Produces(200)
            .Produces(400);

        group.MapPost("/change-password", ChangePassword)
            .WithName("ChangePassword")
            .WithSummary("Change user password")
            .Produces(200)
            .Produces(400)
            .RequireAuthorization();

        group.MapGet("/me", GetCurrentUser)
            .WithName("GetCurrentUser")
            .WithSummary("Get current user information")
            .Produces<UserInfoDto>()
            .Produces(401)
            .RequireAuthorization();

        group.MapPost("/verify-email", VerifyEmail)
            .WithName("VerifyEmail")
            .WithSummary("Verify email address")
            .Produces(200)
            .Produces(400);

        group.MapPost("/resend-verification", ResendEmailVerification)
            .WithName("ResendEmailVerification")
            .WithSummary("Resend email verification")
            .Produces(200)
            .Produces(400);
    }

    #region Endpoint Implementations

    private static async Task<IResult> Login(LoginRequestDto request, IAuthService authService)
    {
        try
        {
            var response = await authService.LoginAsync(request);
            return Results.Ok(response);
        }
        catch (UnauthorizedAccessException ex)
        {
            return Results.Unauthorized();
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error during login: {ex.Message}");
        }
    }

    private static async Task<IResult> RefreshToken(RefreshTokenRequestDto request, IAuthService authService)
    {
        try
        {
            var response = await authService.RefreshTokenAsync(request);
            return Results.Ok(response);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Unauthorized();
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error refreshing token: {ex.Message}");
        }
    }

    private static async Task<IResult> Logout(IAuthService authService, HttpContext context)
    {
        try
        {
            await authService.LogoutAsync();
            return Results.Ok(new { message = "Logged out successfully" });
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error during logout: {ex.Message}");
        }
    }

    private static async Task<IResult> ForgotPassword(ForgotPasswordRequestDto request, IAuthService authService)
    {
        try
        {
            await authService.ForgotPasswordAsync(request);
            return Results.Ok(new { message = "Password reset email sent" });
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error processing forgot password: {ex.Message}");
        }
    }

    private static async Task<IResult> ResetPassword(ResetPasswordRequestDto request, IAuthService authService)
    {
        try
        {
            await authService.ResetPasswordAsync(request);
            return Results.Ok(new { message = "Password reset successfully" });
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error resetting password: {ex.Message}");
        }
    }

    private static async Task<IResult> ChangePassword(ChangePasswordRequestDto request, IAuthService authService)
    {
        try
        {
            await authService.ChangePasswordAsync(request);
            return Results.Ok(new { message = "Password changed successfully" });
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Unauthorized();
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error changing password: {ex.Message}");
        }
    }

    private static async Task<IResult> GetCurrentUser(IAuthService authService)
    {
        try
        {
            var user = await authService.GetCurrentUserAsync();
            return Results.Ok(user);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Unauthorized();
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving current user: {ex.Message}");
        }
    }

    private static async Task<IResult> VerifyEmail(VerifyEmailRequestDto request, IAuthService authService)
    {
        try
        {
            await authService.VerifyEmailAsync(request);
            return Results.Ok(new { message = "Email verified successfully" });
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error verifying email: {ex.Message}");
        }
    }

    private static async Task<IResult> ResendEmailVerification(ResendVerificationRequestDto request, IAuthService authService)
    {
        try
        {
            await authService.ResendEmailVerificationAsync(request);
            return Results.Ok(new { message = "Verification email sent" });
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error sending verification email: {ex.Message}");
        }
    }

    #endregion
}
