import { <PERSON>mpo<PERSON>, OnInit, After<PERSON>iew<PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { ViewportScroller } from '@angular/common';
import { Subscription } from 'rxjs';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../shared/components/default-hero/default-hero.component';

// Interface for fee structure
interface FeeItem {
  name: string;
  amount: number;
  frequency: 'one-time' | 'monthly' | 'quarterly' | 'annually';
  description?: string;
  optional?: boolean;
}

// Interface for education level
interface EducationLevel {
  id: string;
  name: string;
  grades: string;
  ageRange: string;
  tuitionFees: FeeItem[];
  additionalFees: FeeItem[];
  discounts?: {
    name: string;
    description: string;
    amount: string;
  }[];
}

// Interface for payment plan
interface PaymentPlan {
  name: string;
  description: string;
  discount?: string;
  dueDate?: string;
}

// Interface for FAQ item
interface FaqItem {
  question: string;
  answer: string;
}

@Component({
  selector: 'app-tuition',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatTabsModule,
    MatExpansionModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatDividerModule,
    MatTooltipModule,
    MatChipsModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './tuition.component.html',
  styleUrl: './tuition.component.scss'
})
export class TuitionComponent implements OnInit, AfterViewInit, OnDestroy {
  // Education levels with fee structures
  educationLevels: EducationLevel[] = [];

  // Payment plans
  paymentPlans: PaymentPlan[] = [];

  // FAQs
  faqItems: FaqItem[] = [];

  // Displayed columns for fee tables
  displayedColumns: string[] = ['name', 'amount', 'frequency', 'description'];

  // Academic year
  academicYear = '2024-2025';

  // Currency symbol
  currencySymbol = 'Tk.'; // Bangladeshi Taka

  // Floating navigation properties
  showFloatingNav = false;
  activeSection = '';
  private fragmentSubscription: Subscription | null = null;
  private sections = ['fee-structure', 'payment-plans', 'financial-aid', 'faq'];
  private sectionOffsets: {[key: string]: number} = {};

  constructor(
    private route: ActivatedRoute,
    private viewportScroller: ViewportScroller
  ) { }

  ngOnInit(): void {
    this.initializeEducationLevels();
    this.initializePaymentPlans();
    this.initializeFaqs();
  }

  ngAfterViewInit(): void {
    // Calculate section offsets after view is initialized
    setTimeout(() => {
      this.calculateSectionOffsets();
    }, 500);

    // Handle fragment navigation
    this.fragmentSubscription = this.route.fragment.subscribe(fragment => {
      if (fragment) {
        // Wait for the DOM to be ready
        setTimeout(() => {
          this.viewportScroller.scrollToAnchor(fragment);
          this.activeSection = fragment;
        }, 100);
      }
    });
  }

  ngOnDestroy(): void {
    // Clean up subscription
    if (this.fragmentSubscription) {
      this.fragmentSubscription.unsubscribe();
    }
  }

  /**
   * Listen for scroll events to update active section and show/hide floating nav
   */
  @HostListener('window:scroll', ['$event'])
  onWindowScroll() {
    // Show floating nav after scrolling past intro section
    const introSection = document.querySelector('.intro-section');
    if (introSection) {
      const introBottom = introSection.getBoundingClientRect().bottom;
      this.showFloatingNav = introBottom < 0;
    }

    // Update active section based on scroll position
    this.updateActiveSection();
  }

  /**
   * Calculate the offset positions of each section
   */
  private calculateSectionOffsets(): void {
    this.sections.forEach(sectionId => {
      const element = document.getElementById(sectionId);
      if (element) {
        this.sectionOffsets[sectionId] = element.offsetTop - 100; // Subtract header height
      }
    });
  }

  /**
   * Update the active section based on scroll position
   */
  private updateActiveSection(): void {
    const scrollPosition = window.scrollY;

    // Find the section that is currently in view
    for (let i = this.sections.length - 1; i >= 0; i--) {
      const sectionId = this.sections[i];
      if (scrollPosition >= this.sectionOffsets[sectionId]) {
        if (this.activeSection !== sectionId) {
          this.activeSection = sectionId;
        }
        break;
      }
    }
  }

  /**
   * Initialize education levels with fee structures
   */
  initializeEducationLevels(): void {
    this.educationLevels = [
      {
        id: 'play-group',
        name: 'Play Group',
        grades: 'Pre-School',
        ageRange: '3-4 years',
        tuitionFees: [
          {
            name: 'Admission Fee',
            amount: 15000,
            frequency: 'one-time',
            description: 'One-time payment at the time of admission'
          },
          {
            name: 'Tuition Fee',
            amount: 5000,
            frequency: 'monthly',
            description: 'Due on the 5th of each month'
          },
          {
            name: 'Development Fee',
            amount: 10000,
            frequency: 'annually',
            description: 'For school infrastructure and facilities'
          }
        ],
        additionalFees: [
          {
            name: 'Books and Materials',
            amount: 8000,
            frequency: 'annually',
            description: 'Includes all textbooks and learning materials'
          },
          {
            name: 'Uniform',
            amount: 4500,
            frequency: 'annually',
            description: 'Two sets of regular uniform'
          },
          {
            name: 'Meal Plan',
            amount: 2500,
            frequency: 'monthly',
            description: 'Includes breakfast and lunch',
            optional: true
          }
        ],
        discounts: [
          {
            name: 'Sibling Discount',
            description: 'For families with more than one child enrolled',
            amount: '10% off tuition for second child, 15% for third and subsequent children'
          }
        ]
      },
      {
        id: 'nursery',
        name: 'Nursery',
        grades: 'Pre-School',
        ageRange: '4-5 years',
        tuitionFees: [
          {
            name: 'Admission Fee',
            amount: 15000,
            frequency: 'one-time',
            description: 'One-time payment at the time of admission'
          },
          {
            name: 'Tuition Fee',
            amount: 5500,
            frequency: 'monthly',
            description: 'Due on the 5th of each month'
          },
          {
            name: 'Development Fee',
            amount: 10000,
            frequency: 'annually',
            description: 'For school infrastructure and facilities'
          }
        ],
        additionalFees: [
          {
            name: 'Books and Materials',
            amount: 8500,
            frequency: 'annually',
            description: 'Includes all textbooks and learning materials'
          },
          {
            name: 'Uniform',
            amount: 4500,
            frequency: 'annually',
            description: 'Two sets of regular uniform'
          },
          {
            name: 'Meal Plan',
            amount: 2500,
            frequency: 'monthly',
            description: 'Includes breakfast and lunch',
            optional: true
          }
        ],
        discounts: [
          {
            name: 'Sibling Discount',
            description: 'For families with more than one child enrolled',
            amount: '10% off tuition for second child, 15% for third and subsequent children'
          }
        ]
      },
      {
        id: 'kg',
        name: 'Kindergarten',
        grades: 'KG',
        ageRange: '5-6 years',
        tuitionFees: [
          {
            name: 'Admission Fee',
            amount: 18000,
            frequency: 'one-time',
            description: 'One-time payment at the time of admission'
          },
          {
            name: 'Tuition Fee',
            amount: 6000,
            frequency: 'monthly',
            description: 'Due on the 5th of each month'
          },
          {
            name: 'Development Fee',
            amount: 12000,
            frequency: 'annually',
            description: 'For school infrastructure and facilities'
          }
        ],
        additionalFees: [
          {
            name: 'Books and Materials',
            amount: 9000,
            frequency: 'annually',
            description: 'Includes all textbooks and learning materials'
          },
          {
            name: 'Uniform',
            amount: 5000,
            frequency: 'annually',
            description: 'Two sets of regular uniform'
          },
          {
            name: 'Meal Plan',
            amount: 2500,
            frequency: 'monthly',
            description: 'Includes breakfast and lunch',
            optional: true
          },
          {
            name: 'Computer Lab',
            amount: 2000,
            frequency: 'annually',
            description: 'Access to computer facilities'
          }
        ],
        discounts: [
          {
            name: 'Sibling Discount',
            description: 'For families with more than one child enrolled',
            amount: '10% off tuition for second child, 15% for third and subsequent children'
          },
          {
            name: 'Early Payment Discount',
            description: 'For full annual payment in advance',
            amount: '5% off annual tuition fees'
          }
        ]
      },
      {
        id: 'primary',
        name: 'Primary School',
        grades: 'Classes 1-5',
        ageRange: '6-11 years',
        tuitionFees: [
          {
            name: 'Admission Fee',
            amount: 20000,
            frequency: 'one-time',
            description: 'One-time payment at the time of admission'
          },
          {
            name: 'Tuition Fee (Classes 1-3)',
            amount: 7000,
            frequency: 'monthly',
            description: 'Due on the 5th of each month'
          },
          {
            name: 'Tuition Fee (Classes 4-5)',
            amount: 7500,
            frequency: 'monthly',
            description: 'Due on the 5th of each month'
          },
          {
            name: 'Development Fee',
            amount: 15000,
            frequency: 'annually',
            description: 'For school infrastructure and facilities'
          }
        ],
        additionalFees: [
          {
            name: 'Books and Materials',
            amount: 12000,
            frequency: 'annually',
            description: 'Includes all textbooks and learning materials'
          },
          {
            name: 'Uniform',
            amount: 6000,
            frequency: 'annually',
            description: 'Two sets of regular uniform and one set of PE uniform'
          },
          {
            name: 'Meal Plan',
            amount: 3000,
            frequency: 'monthly',
            description: 'Includes breakfast and lunch',
            optional: true
          },
          {
            name: 'Computer Lab',
            amount: 3000,
            frequency: 'annually',
            description: 'Access to computer facilities'
          },
          {
            name: 'Library Fee',
            amount: 2000,
            frequency: 'annually',
            description: 'Access to library resources'
          },
          {
            name: 'Extracurricular Activities',
            amount: 5000,
            frequency: 'annually',
            description: 'Includes sports, arts, and clubs',
            optional: true
          }
        ],
        discounts: [
          {
            name: 'Sibling Discount',
            description: 'For families with more than one child enrolled',
            amount: '10% off tuition for second child, 15% for third and subsequent children'
          },
          {
            name: 'Early Payment Discount',
            description: 'For full annual payment in advance',
            amount: '5% off annual tuition fees'
          },
          {
            name: 'Merit Scholarship',
            description: 'Based on academic performance',
            amount: 'Up to 25% off tuition fees'
          }
        ]
      },
      {
        id: 'middle',
        name: 'Middle School',
        grades: 'Classes 6-8',
        ageRange: '11-14 years',
        tuitionFees: [
          {
            name: 'Admission Fee',
            amount: 25000,
            frequency: 'one-time',
            description: 'One-time payment at the time of admission'
          },
          {
            name: 'Tuition Fee',
            amount: 8500,
            frequency: 'monthly',
            description: 'Due on the 5th of each month'
          },
          {
            name: 'Development Fee',
            amount: 18000,
            frequency: 'annually',
            description: 'For school infrastructure and facilities'
          }
        ],
        additionalFees: [
          {
            name: 'Books and Materials',
            amount: 15000,
            frequency: 'annually',
            description: 'Includes all textbooks and learning materials'
          },
          {
            name: 'Uniform',
            amount: 7000,
            frequency: 'annually',
            description: 'Two sets of regular uniform and one set of PE uniform'
          },
          {
            name: 'Meal Plan',
            amount: 3500,
            frequency: 'monthly',
            description: 'Includes breakfast and lunch',
            optional: true
          },
          {
            name: 'Computer Lab',
            amount: 4000,
            frequency: 'annually',
            description: 'Access to computer facilities and programming courses'
          },
          {
            name: 'Science Lab',
            amount: 4000,
            frequency: 'annually',
            description: 'Access to science laboratory facilities'
          },
          {
            name: 'Library Fee',
            amount: 2500,
            frequency: 'annually',
            description: 'Access to library resources'
          },
          {
            name: 'Extracurricular Activities',
            amount: 6000,
            frequency: 'annually',
            description: 'Includes sports, arts, and clubs',
            optional: true
          }
        ],
        discounts: [
          {
            name: 'Sibling Discount',
            description: 'For families with more than one child enrolled',
            amount: '10% off tuition for second child, 15% for third and subsequent children'
          },
          {
            name: 'Early Payment Discount',
            description: 'For full annual payment in advance',
            amount: '5% off annual tuition fees'
          },
          {
            name: 'Merit Scholarship',
            description: 'Based on academic performance',
            amount: 'Up to 30% off tuition fees'
          }
        ]
      },
      {
        id: 'secondary',
        name: 'Secondary School',
        grades: 'Classes 9-10',
        ageRange: '14-16 years',
        tuitionFees: [
          {
            name: 'Admission Fee',
            amount: 30000,
            frequency: 'one-time',
            description: 'One-time payment at the time of admission'
          },
          {
            name: 'Tuition Fee',
            amount: 10000,
            frequency: 'monthly',
            description: 'Due on the 5th of each month'
          },
          {
            name: 'Development Fee',
            amount: 20000,
            frequency: 'annually',
            description: 'For school infrastructure and facilities'
          },
          {
            name: 'Examination Fee',
            amount: 5000,
            frequency: 'annually',
            description: 'For internal and mock examinations'
          }
        ],
        additionalFees: [
          {
            name: 'Books and Materials',
            amount: 18000,
            frequency: 'annually',
            description: 'Includes all textbooks and learning materials'
          },
          {
            name: 'Uniform',
            amount: 8000,
            frequency: 'annually',
            description: 'Two sets of regular uniform and one set of PE uniform'
          },
          {
            name: 'Meal Plan',
            amount: 4000,
            frequency: 'monthly',
            description: 'Includes breakfast and lunch',
            optional: true
          },
          {
            name: 'Computer Lab',
            amount: 5000,
            frequency: 'annually',
            description: 'Access to computer facilities and advanced programming courses'
          },
          {
            name: 'Science Lab',
            amount: 6000,
            frequency: 'annually',
            description: 'Access to physics, chemistry, and biology laboratory facilities'
          },
          {
            name: 'Library Fee',
            amount: 3000,
            frequency: 'annually',
            description: 'Access to library resources and research materials'
          },
          {
            name: 'Career Counseling',
            amount: 3000,
            frequency: 'annually',
            description: 'Career guidance and counseling services'
          },
          {
            name: 'Extracurricular Activities',
            amount: 7000,
            frequency: 'annually',
            description: 'Includes sports, arts, and clubs',
            optional: true
          }
        ],
        discounts: [
          {
            name: 'Sibling Discount',
            description: 'For families with more than one child enrolled',
            amount: '10% off tuition for second child, 15% for third and subsequent children'
          },
          {
            name: 'Early Payment Discount',
            description: 'For full annual payment in advance',
            amount: '5% off annual tuition fees'
          },
          {
            name: 'Merit Scholarship',
            description: 'Based on academic performance',
            amount: 'Up to 40% off tuition fees'
          },
          {
            name: 'Financial Aid',
            description: 'Based on financial need assessment',
            amount: 'Up to 50% off total fees'
          }
        ]
      }
    ];
  }

  /**
   * Initialize payment plans
   */
  initializePaymentPlans(): void {
    this.paymentPlans = [
      {
        name: 'Monthly Payment Plan',
        description: 'Pay tuition fees on a monthly basis, due on the 5th of each month.',
        dueDate: '5th of each month'
      },
      {
        name: 'Quarterly Payment Plan',
        description: 'Pay tuition fees in four installments throughout the academic year.',
        discount: '2% discount on tuition fees',
        dueDate: 'July 15, October 15, January 15, April 15'
      },
      {
        name: 'Semi-Annual Payment Plan',
        description: 'Pay tuition fees in two installments for the academic year.',
        discount: '3% discount on tuition fees',
        dueDate: 'July 15 and January 15'
      },
      {
        name: 'Annual Payment Plan',
        description: 'Pay the full year\'s tuition in advance.',
        discount: '5% discount on tuition fees',
        dueDate: 'July 15'
      }
    ];
  }

  /**
   * Initialize FAQs
   */
  initializeFaqs(): void {
    this.faqItems = [
      {
        question: 'When are tuition fees due?',
        answer: 'Monthly tuition fees are due on the 5th of each month. For other payment plans, please refer to the specific due dates listed in the payment plans section.'
      },
      {
        question: 'Are there any additional fees not listed here?',
        answer: 'The fees listed cover most regular expenses. However, there may be additional costs for special events, field trips, or extracurricular activities that are optional. Parents will be notified in advance of any such expenses.'
      },
      {
        question: 'Is there a sibling discount available?',
        answer: 'Yes, we offer a 10% discount on tuition fees for the second child and a 15% discount for the third and subsequent children enrolled from the same family.'
      },
      {
        question: 'What forms of payment are accepted?',
        answer: 'We accept bank transfers, credit/debit cards, and mobile banking options like bKash and Nagad. Cash payments can be made directly at the accounts office.'
      },
      {
        question: 'Is financial aid available?',
        answer: 'Yes, we offer financial aid based on need assessment. Families can apply for financial aid by submitting the required documentation to the admissions office. Decisions are made on a case-by-case basis.'
      },
      {
        question: 'What is the refund policy if my child withdraws from school?',
        answer: 'Admission and development fees are non-refundable. Tuition fees may be refunded on a pro-rata basis if written notice is given at least one month in advance of withdrawal. Please contact the accounts office for specific details.'
      },
      {
        question: 'Are the fees subject to change?',
        answer: 'Fees are reviewed annually and may be adjusted for the new academic year. Parents will be notified of any changes at least three months in advance.'
      },
      {
        question: 'Is the meal plan mandatory?',
        answer: 'No, the meal plan is optional. Parents can choose whether to enroll their children in the school meal program or provide packed lunches.'
      }
    ];
  }

  /**
   * Format currency with the appropriate symbol
   */
  formatCurrency(amount: number): string {
    return `${this.currencySymbol}${amount.toLocaleString()}`;
  }

  /**
   * Get the frequency display text
   */
  getFrequencyText(frequency: string): string {
    switch (frequency) {
      case 'one-time': return 'One-time';
      case 'monthly': return 'Monthly';
      case 'quarterly': return 'Quarterly';
      case 'annually': return 'Annually';
      default: return frequency;
    }
  }

  /**
   * Calculate and return the total annual cost for a level
   * This is a simplified calculation and might need adjustment based on exact requirements
   */
  calculateAnnualCost(level: EducationLevel): number {
    let total = 0;

    // Add one-time admission fee
    const admissionFee = level.tuitionFees.find(fee => fee.name.includes('Admission'));
    if (admissionFee) {
      total += admissionFee.amount;
    }

    // Add monthly fees * 12
    level.tuitionFees.forEach(fee => {
      if (fee.frequency === 'monthly') {
        total += fee.amount * 12;
      } else if (fee.frequency === 'annually') {
        total += fee.amount;
      }
    });

    // Add required additional fees (excluding optional ones)
    level.additionalFees.forEach(fee => {
      if (!fee.optional) {
        if (fee.frequency === 'monthly') {
          total += fee.amount * 12;
        } else if (fee.frequency === 'annually') {
          total += fee.amount;
        }
      }
    });

    return total;
  }
}
