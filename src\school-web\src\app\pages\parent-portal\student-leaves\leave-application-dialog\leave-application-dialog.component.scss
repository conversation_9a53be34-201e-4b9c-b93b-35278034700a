.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.date-fields {
  display: flex;
  gap: 16px;
}

.attachment-field {
  margin-top: 8px;
  
  label {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 8px;
    display: block;
  }
}

.file-input-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.file-name {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.87);
}

mat-dialog-actions {
  padding: 16px 0;
}

button[type="submit"] {
  min-width: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
}

mat-spinner {
  margin-right: 8px;
}

@media (max-width: 600px) {
  .date-fields {
    flex-direction: column;
    gap: 0;
  }
  
  .file-input-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    
    button {
      width: 100%;
    }
  }
}
