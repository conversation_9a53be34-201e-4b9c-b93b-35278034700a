<div class="enhanced-hero"
     [ngClass]="[sizeClass, alignmentClass, themeClass]"
     [style.backgroundImage]="backgroundImage ? 'url(' + backgroundImage + ')' : ''">

  <!-- Overlay with pattern -->
  <div class="hero-overlay" *ngIf="showPattern"></div>

  <!-- Decorative elements -->
  <div class="hero-decorations">
    <div class="circle-decoration circle-1"></div>
    <div class="circle-decoration circle-2"></div>
    <div class="circle-decoration circle-3"></div>
  </div>

  <!-- Optional overlay image (like a school mascot or emblem) -->
  <div class="overlay-image" *ngIf="overlayImage">
    <img [src]="overlayImage" alt="School emblem" class="emblem">
  </div>

  <!-- Content container -->
  <div class="hero-container">
    <!-- Breadcrumbs if provided -->
    <div class="breadcrumbs" *ngIf="breadcrumbs && breadcrumbs.length > 0">
      <span *ngFor="let crumb of breadcrumbs; let last = last; let i = index">
        <a [routerLink]="['/']" *ngIf="i === 0">Home</a>
        <a [routerLink]="['/' + crumb.toLowerCase()]" *ngIf="i > 0">{{crumb}}</a>
        <mat-icon *ngIf="!last">chevron_right</mat-icon>
      </span>
    </div>

    <!-- Main content -->
    <div class="hero-content">
      <h1 class="hero-title" [class.animated]="animationEnabled" [ngClass]="getTitleClass(title)">{{title}}</h1>
      <h2 class="hero-subtitle" *ngIf="subtitle" [class.animated]="animationEnabled" [ngClass]="getSubtitleClass(subtitle)">{{subtitle}}</h2>
      <p class="hero-description" *ngIf="description" [class.animated]="animationEnabled" [ngClass]="getDescriptionClass(description)">{{description}}</p>

      <!-- Action buttons -->
      <div class="hero-buttons" *ngIf="buttons && buttons.length > 0" [class.animated]="animationEnabled">
        <ng-container *ngFor="let button of buttons">
          <a mat-raised-button
             *ngIf="button.isPrimary"
             color="primary"
             [routerLink]="button.link">
            <mat-icon *ngIf="button.icon">{{button.icon}}</mat-icon>
            {{button.label}}
          </a>
          <a mat-stroked-button
             *ngIf="!button.isPrimary"
             color="primary"
             [routerLink]="button.link">
            <mat-icon *ngIf="button.icon">{{button.icon}}</mat-icon>
            {{button.label}}
          </a>
        </ng-container>
      </div>

      <!-- Slot for additional content -->
      <div class="additional-content">
        <ng-content></ng-content>
      </div>
    </div>
  </div>

  <!-- Decorative wave shape at the bottom -->
  <div class="hero-wave">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 100" preserveAspectRatio="none">
      <path d="M0,50 C150,100 350,0 500,50 C650,100 850,0 1000,50 C1150,100 1350,0 1440,50 L1440,100 L0,100 Z"
            fill="white"></path>
    </svg>
  </div>
</div>
