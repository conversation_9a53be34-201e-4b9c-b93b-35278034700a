.dialog-container {
  max-width: 600px;
  width: 100%;

  h2[mat-dialog-title] {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 16px 0;
    color: #1976d2;
    font-weight: 500;

    mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
  }
}

.grade-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;

  .form-section {
    .section-title {
      font-size: 1.1rem;
      font-weight: 500;
      color: #333;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #e0e0e0;
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      .form-field {
        flex: 1;
      }
    }

    .full-width {
      width: 100%;
    }
  }

  .status-toggle {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .status-hint {
      color: #666;
      font-size: 0.875rem;
      margin-left: 4px;
    }
  }
}

mat-dialog-actions {
  padding: 16px 0 0 0;
  margin: 0;

  button {
    margin-left: 8px;

    .button-spinner {
      margin-right: 8px;
    }
  }
}

// Form field customizations
mat-form-field {
  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
  }

  .mat-mdc-form-field-hint-wrapper {
    margin-top: 4px;
  }
}

// Responsive design
@media (max-width: 600px) {
  .dialog-container {
    max-width: 100%;
    margin: 0;
  }

  .grade-form {
    .form-section {
      .form-row {
        flex-direction: column;
        gap: 8px;
      }
    }
  }

  mat-dialog-actions {
    flex-direction: column-reverse;
    gap: 8px;

    button {
      margin: 0;
      width: 100%;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .dialog-container {
    h2[mat-dialog-title] {
      color: #90caf9;
    }
  }

  .grade-form {
    .form-section {
      .section-title {
        color: #fff;
        border-bottom-color: #424242;
      }
    }

    .status-toggle {
      .status-hint {
        color: #ccc;
      }
    }
  }
}

// Custom scrollbar for form
.grade-form::-webkit-scrollbar {
  width: 6px;
}

.grade-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.grade-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.grade-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// Animation for form sections
.form-section {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Focus styles
mat-form-field.mat-focused {
  .mat-mdc-form-field-outline-thick {
    color: #1976d2;
  }
}

// Error state styling
mat-form-field.mat-form-field-invalid {
  .mat-mdc-form-field-outline-thick {
    color: #f44336;
  }
}

// Slide toggle customization
mat-slide-toggle {
  .mat-mdc-slide-toggle-bar {
    height: 20px;
    border-radius: 10px;
  }

  .mat-mdc-slide-toggle-thumb {
    width: 16px;
    height: 16px;
  }
}

// Button loading state
button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-spinner {
  display: inline-block;
  vertical-align: middle;
}
