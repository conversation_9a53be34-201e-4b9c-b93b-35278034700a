.leaves-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.filter-card {
  margin-bottom: 24px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.leaves-loading,
.leaves-error {
  margin-bottom: 24px;
}

.leaves-error {
  text-align: center;
  padding: 16px;

  mat-icon {
    vertical-align: middle;
    margin-right: 8px;
  }
}

.no-leaves {
  text-align: center;
  padding: 16px;
}

.leaves-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.leave-card {
  margin-bottom: 0;
}

.leave-status {
  margin-left: auto;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.approved {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.pending {
  background-color: #fff8e1;
  color: #ff8f00;
}

.rejected {
  background-color: #ffebee;
  color: #c62828;
}

.cancelled {
  background-color: #f5f5f5;
  color: #757575;
}

.leave-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.leave-reason,
.leave-attachment,
.leave-applied,
.leave-comments {
  display: flex;
  flex-direction: column;
}

.leave-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.leave-value {
  font-size: 16px;
}

mat-card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px 16px;
}

@media (max-width: 768px) {
  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-actions {
    flex-direction: column;
    
    button {
      width: 100%;
    }
  }
}
