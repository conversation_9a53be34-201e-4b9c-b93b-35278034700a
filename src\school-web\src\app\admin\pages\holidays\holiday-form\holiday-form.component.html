<div class="holiday-form-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <mat-icon>{{ isEditMode ? 'edit' : 'add' }}</mat-icon>
        {{ isEditMode ? 'Edit Holiday' : 'Create Holiday' }}
      </h1>
      <div class="header-actions">
        <button mat-stroked-button (click)="cancel()">
          <mat-icon>close</mat-icon>
          Cancel
        </button>
        <button mat-raised-button color="primary" 
                (click)="onSubmit()" 
                [disabled]="saving || holidayForm.invalid">
          <mat-icon>{{ saving ? 'hourglass_empty' : 'save' }}</mat-icon>
          {{ saving ? 'Saving...' : (isEditMode ? 'Update' : 'Create') }}
        </button>
      </div>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Loading holiday data...</p>
  </div>

  <!-- Form Content -->
  <div *ngIf="!loading" class="form-content">
    <form [formGroup]="holidayForm" (ngSubmit)="onSubmit()">
      
      <mat-tab-group>
        <!-- Basic Information Tab -->
        <mat-tab label="Basic Information">
          <div class="tab-content">
            <mat-card>
              <mat-card-header>
                <mat-card-title>Holiday Details</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="form-row">
                  <!-- Name -->
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Holiday Name *</mat-label>
                    <input matInput formControlName="name" placeholder="Enter holiday name">
                    <mat-error *ngIf="holidayForm.get('name')?.errors?.['required']">
                      Holiday name is required
                    </mat-error>
                    <mat-error *ngIf="holidayForm.get('name')?.errors?.['maxlength']">
                      Holiday name cannot exceed 200 characters
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <!-- Description -->
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Description</mat-label>
                    <textarea matInput formControlName="description" 
                             placeholder="Enter holiday description" rows="3"></textarea>
                    <mat-error *ngIf="holidayForm.get('description')?.errors?.['maxlength']">
                      Description cannot exceed 1000 characters
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <!-- Start Date -->
                  <mat-form-field appearance="outline" class="half-width">
                    <mat-label>Start Date *</mat-label>
                    <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                    <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                    <mat-datepicker #startPicker></mat-datepicker>
                    <mat-error *ngIf="holidayForm.get('startDate')?.errors?.['required']">
                      Start date is required
                    </mat-error>
                  </mat-form-field>

                  <!-- End Date -->
                  <mat-form-field appearance="outline" class="half-width">
                    <mat-label>End Date *</mat-label>
                    <input matInput [matDatepicker]="endPicker" formControlName="endDate">
                    <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                    <mat-datepicker #endPicker></mat-datepicker>
                    <mat-error *ngIf="holidayForm.get('endDate')?.errors?.['required']">
                      End date is required
                    </mat-error>
                    <mat-error *ngIf="holidayForm.errors?.['dateRange']">
                      {{ holidayForm.errors?.['dateRange'].message }}
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <!-- Holiday Type -->
                  <mat-form-field appearance="outline" class="half-width">
                    <mat-label>Holiday Type *</mat-label>
                    <mat-select formControlName="type">
                      <mat-option *ngFor="let type of holidayTypes" [value]="type.value">
                        <div class="type-option">
                          <div class="color-indicator" [style.background-color]="type.color"></div>
                          {{ type.label }}
                        </div>
                      </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <!-- Color -->
                  <mat-form-field appearance="outline" class="half-width">
                    <mat-label>Color</mat-label>
                    <mat-select formControlName="color">
                      <mat-option *ngFor="let color of colorOptions" [value]="color">
                        <div class="color-option">
                          <div class="color-preview" [style.background-color]="color"></div>
                          {{ color }}
                        </div>
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <!-- Academic Year -->
                  <mat-form-field appearance="outline" class="half-width">
                    <mat-label>Academic Year</mat-label>
                    <mat-select formControlName="academicYearId">
                      <mat-option [value]="">None</mat-option>
                      <mat-option *ngFor="let year of academicYears" [value]="year.id">
                        {{ year.displayName }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <!-- Term -->
                  <mat-form-field appearance="outline" class="half-width">
                    <mat-label>Term</mat-label>
                    <mat-select formControlName="termId" [disabled]="!holidayForm.get('academicYearId')?.value">
                      <mat-option [value]="">None</mat-option>
                      <mat-option *ngFor="let term of terms" [value]="term.id">
                        {{ term.name }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <!-- Status Toggles -->
                  <div class="toggle-group">
                    <mat-slide-toggle formControlName="isActive">
                      Active
                    </mat-slide-toggle>
                    
                    <mat-slide-toggle formControlName="isPublic">
                      Public Holiday
                    </mat-slide-toggle>
                  </div>
                </div>

                <div class="form-row">
                  <!-- Remarks -->
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Remarks</mat-label>
                    <textarea matInput formControlName="remarks" 
                             placeholder="Additional notes or remarks" rows="2"></textarea>
                    <mat-error *ngIf="holidayForm.get('remarks')?.errors?.['maxlength']">
                      Remarks cannot exceed 1000 characters
                    </mat-error>
                  </mat-form-field>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>

        <!-- Recurrence Tab -->
        <mat-tab label="Recurrence">
          <div class="tab-content">
            <mat-card>
              <mat-card-header>
                <mat-card-title>Recurrence Settings</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="form-row">
                  <mat-slide-toggle formControlName="isRecurring">
                    This is a recurring holiday
                  </mat-slide-toggle>
                </div>

                <div *ngIf="holidayForm.get('isRecurring')?.value" formGroupName="recurrencePattern">
                  <div class="form-row">
                    <!-- Recurrence Type -->
                    <mat-form-field appearance="outline" class="half-width">
                      <mat-label>Recurrence Type *</mat-label>
                      <mat-select formControlName="type">
                        <mat-option *ngFor="let type of recurrenceTypes" [value]="type.value">
                          {{ type.label }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <!-- Interval -->
                    <mat-form-field appearance="outline" class="half-width">
                      <mat-label>Repeat Every</mat-label>
                      <input matInput type="number" formControlName="interval" min="1" max="100">
                      <mat-hint>How often to repeat (e.g., every 2 weeks)</mat-hint>
                    </mat-form-field>
                  </div>

                  <!-- Weekly specific -->
                  <div *ngIf="holidayForm.get('recurrencePattern.type')?.value === 2" class="form-row">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Days of Week *</mat-label>
                      <mat-select formControlName="daysOfWeek" multiple>
                        <mat-option *ngFor="let day of daysOfWeek" [value]="day.value">
                          {{ day.label }}
                        </mat-option>
                      </mat-select>
                      <mat-error *ngIf="getRecurrenceErrorMessage('daysOfWeek')">
                        {{ getRecurrenceErrorMessage('daysOfWeek') }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <!-- Monthly specific -->
                  <div *ngIf="holidayForm.get('recurrencePattern.type')?.value === 3" class="form-row">
                    <mat-form-field appearance="outline" class="half-width">
                      <mat-label>Day of Month *</mat-label>
                      <input matInput type="number" formControlName="dayOfMonth" min="1" max="31">
                      <mat-error *ngIf="getRecurrenceErrorMessage('dayOfMonth')">
                        {{ getRecurrenceErrorMessage('dayOfMonth') }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <!-- Yearly specific -->
                  <div *ngIf="holidayForm.get('recurrencePattern.type')?.value === 4" class="form-row">
                    <mat-form-field appearance="outline" class="half-width">
                      <mat-label>Month of Year *</mat-label>
                      <mat-select formControlName="monthOfYear">
                        <mat-option value="1">January</mat-option>
                        <mat-option value="2">February</mat-option>
                        <mat-option value="3">March</mat-option>
                        <mat-option value="4">April</mat-option>
                        <mat-option value="5">May</mat-option>
                        <mat-option value="6">June</mat-option>
                        <mat-option value="7">July</mat-option>
                        <mat-option value="8">August</mat-option>
                        <mat-option value="9">September</mat-option>
                        <mat-option value="10">October</mat-option>
                        <mat-option value="11">November</mat-option>
                        <mat-option value="12">December</mat-option>
                      </mat-select>
                      <mat-error *ngIf="getRecurrenceErrorMessage('monthOfYear')">
                        {{ getRecurrenceErrorMessage('monthOfYear') }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <!-- Custom pattern -->
                  <div *ngIf="holidayForm.get('recurrencePattern.type')?.value === 5" class="form-row">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Custom Pattern *</mat-label>
                      <textarea matInput formControlName="customPattern" 
                               placeholder="Describe the custom recurrence pattern" rows="3"></textarea>
                      <mat-error *ngIf="getRecurrenceErrorMessage('customPattern')">
                        {{ getRecurrenceErrorMessage('customPattern') }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-row">
                    <!-- End Date -->
                    <mat-form-field appearance="outline" class="half-width">
                      <mat-label>Recurrence End Date</mat-label>
                      <input matInput [matDatepicker]="recurrenceEndPicker" formControlName="endDate">
                      <mat-datepicker-toggle matSuffix [for]="recurrenceEndPicker"></mat-datepicker-toggle>
                      <mat-datepicker #recurrenceEndPicker></mat-datepicker>
                      <mat-hint>Leave empty for no end date</mat-hint>
                    </mat-form-field>

                    <!-- Max Occurrences -->
                    <mat-form-field appearance="outline" class="half-width">
                      <mat-label>Max Occurrences</mat-label>
                      <input matInput type="number" formControlName="maxOccurrences" min="1" max="1000">
                      <mat-hint>Maximum number of occurrences</mat-hint>
                    </mat-form-field>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>

        <!-- Translations Tab -->
        <mat-tab label="Translations" *ngIf="getNonEnglishLanguages().length > 0">
          <div class="tab-content">
            <mat-card>
              <mat-card-header>
                <mat-card-title>Multi-language Support</mat-card-title>
                <mat-card-subtitle>Provide translations for different languages</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <mat-expansion-panel *ngFor="let language of getNonEnglishLanguages()" class="translation-panel">
                  <mat-expansion-panel-header>
                    <mat-panel-title>
                      {{ language.name }} ({{ language.code }})
                    </mat-panel-title>
                  </mat-expansion-panel-header>
                  
                  <div [formGroup]="translationForms[language.code]" class="translation-form">
                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Holiday Name in {{ language.name }}</mat-label>
                        <input matInput formControlName="name" 
                               [placeholder]="'Enter holiday name in ' + language.name">
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Description in {{ language.name }}</mat-label>
                        <textarea matInput formControlName="description" 
                                 [placeholder]="'Enter description in ' + language.name" rows="3"></textarea>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Remarks in {{ language.name }}</mat-label>
                        <textarea matInput formControlName="remarks" 
                                 [placeholder]="'Enter remarks in ' + language.name" rows="2"></textarea>
                      </mat-form-field>
                    </div>
                  </div>
                </mat-expansion-panel>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>
      </mat-tab-group>

      <!-- Form Actions -->
      <div class="form-actions">
        <button type="button" mat-stroked-button (click)="cancel()">
          <mat-icon>close</mat-icon>
          Cancel
        </button>
        
        <button type="submit" mat-raised-button color="primary" 
                [disabled]="saving || holidayForm.invalid">
          <mat-icon>{{ saving ? 'hourglass_empty' : 'save' }}</mat-icon>
          {{ saving ? 'Saving...' : (isEditMode ? 'Update Holiday' : 'Create Holiday') }}
        </button>
      </div>
    </form>
  </div>
</div>
