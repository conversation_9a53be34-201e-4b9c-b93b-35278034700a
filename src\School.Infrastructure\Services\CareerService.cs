using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Career;
using School.Domain.Entities;
using School.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Services
{
    public class CareerService : ICareerService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<CareerService> _logger;

        public CareerService(
            IUnitOfWork unitOfWork,
            ICurrentUserService currentUserService,
            ILogger<CareerService> logger)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        public async Task<(IEnumerable<CareerDto> Careers, int TotalCount)> GetAllCareersAsync(CareerFilterDto filter)
        {
            var repository = _unitOfWork.Repository<Career>();
            var query = repository.AsQueryable("Translations");

            // Apply filters
            if (!string.IsNullOrEmpty(filter.Title))
            {
                query = query.Where(c => c.Title.Contains(filter.Title));
            }

            if (!string.IsNullOrEmpty(filter.Department))
            {
                query = query.Where(c => c.Department.Contains(filter.Department));
            }

            if (!string.IsNullOrEmpty(filter.Location))
            {
                query = query.Where(c => c.Location.Contains(filter.Location));
            }

            if (filter.Status.HasValue)
            {
                query = query.Where(c => c.Status == filter.Status.Value);
            }

            if (filter.IsFeatured.HasValue)
            {
                query = query.Where(c => c.IsFeatured == filter.IsFeatured.Value);
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Get paginated results
            var careers = await query
                .OrderByDescending(c => c.PostedDate)
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(c => new CareerDto
                {
                    Id = c.Id,
                    Title = c.Title,
                    Department = c.Department,
                    Location = c.Location,
                    Description = c.Description,
                    Responsibilities = c.Responsibilities,
                    Qualifications = c.Qualifications,
                    Experience = c.Experience,
                    Salary = c.Salary,
                    ApplicationDeadline = c.ApplicationDeadline,
                    PostedDate = c.PostedDate,
                    Status = c.Status,
                    ContactEmail = c.ContactEmail,
                    ContactPhone = c.ContactPhone,
                    ApplicationUrl = c.ApplicationUrl,
                    IsFeatured = c.IsFeatured,
                    Vacancies = c.Vacancies,
                    EmploymentType = c.EmploymentType,
                    IsActive = c.IsActive,
                    CreatedAt = c.CreatedAt,
                    LastModifiedAt = c.LastModifiedAt,
                    Translations = c.Translations.Select(t => new CareerTranslationDto
                    {
                        Id = t.Id,
                        CareerId = t.CareerId,
                        LanguageCode = t.LanguageCode,
                        Title = t.Title,
                        Department = t.Department,
                        Location = t.Location,
                        Description = t.Description,
                        Responsibilities = t.Responsibilities,
                        Qualifications = t.Qualifications,
                        Experience = t.Experience,
                        Salary = t.Salary,
                        EmploymentType = t.EmploymentType
                    }).ToList()
                })
                .ToListAsync();

            return (careers, totalCount);
        }

        public async Task<CareerDetailDto?> GetCareerByIdAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<Career>();
            var career = await repository.GetByIdAsync(id, new[] { "Translations", "Applications" });

            if (career == null) return null;

            return new CareerDetailDto
            {
                Id = career.Id,
                Title = career.Title,
                Department = career.Department,
                Location = career.Location,
                Description = career.Description,
                Responsibilities = career.Responsibilities,
                Qualifications = career.Qualifications,
                Experience = career.Experience,
                Salary = career.Salary,
                ApplicationDeadline = career.ApplicationDeadline,
                PostedDate = career.PostedDate,
                Status = career.Status,
                ContactEmail = career.ContactEmail,
                ContactPhone = career.ContactPhone,
                ApplicationUrl = career.ApplicationUrl,
                IsFeatured = career.IsFeatured,
                Vacancies = career.Vacancies,
                EmploymentType = career.EmploymentType,
                IsActive = career.IsActive,
                CreatedAt = career.CreatedAt,
                LastModifiedAt = career.LastModifiedAt,
                Translations = career.Translations.Select(t => new CareerTranslationDto
                {
                    Id = t.Id,
                    CareerId = t.CareerId,
                    LanguageCode = t.LanguageCode,
                    Title = t.Title,
                    Department = t.Department,
                    Location = t.Location,
                    Description = t.Description,
                    Responsibilities = t.Responsibilities,
                    Qualifications = t.Qualifications,
                    Experience = t.Experience,
                    Salary = t.Salary,
                    EmploymentType = t.EmploymentType
                }).ToList(),
                Applications = career.Applications.Select(a => new CareerApplicationDto
                {
                    Id = a.Id,
                    CareerId = a.CareerId,
                    ApplicantName = a.ApplicantName,
                    Email = a.Email,
                    Phone = a.Phone,
                    CoverLetter = a.CoverLetter,
                    ResumeFilePath = a.ResumeFilePath,
                    Status = a.Status,
                    ReviewComments = a.ReviewComments,
                    ReviewedBy = a.ReviewedBy,
                    ReviewedAt = a.ReviewedAt,
                    CreatedAt = a.CreatedAt,
                    LastModifiedAt = a.LastModifiedAt
                }).ToList()
            };
        }

        public async Task<Guid> CreateCareerAsync(CreateCareerDto careerDto)
        {
            var repository = _unitOfWork.Repository<Career>();

            var career = new Career
            {
                Title = careerDto.Title,
                Department = careerDto.Department,
                Location = careerDto.Location,
                Description = careerDto.Description,
                Responsibilities = careerDto.Responsibilities,
                Qualifications = careerDto.Qualifications,
                Experience = careerDto.Experience,
                Salary = careerDto.Salary,
                ApplicationDeadline = careerDto.ApplicationDeadline,
                PostedDate = careerDto.PostedDate,
                Status = careerDto.Status,
                ContactEmail = careerDto.ContactEmail,
                ContactPhone = careerDto.ContactPhone,
                ApplicationUrl = careerDto.ApplicationUrl,
                IsFeatured = careerDto.IsFeatured,
                Vacancies = careerDto.Vacancies,
                EmploymentType = careerDto.EmploymentType,
                IsActive = careerDto.IsActive,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = _currentUserService.UserId.ToString()
            };

            await repository.AddAsync(career);
            await _unitOfWork.SaveChangesAsync();

            // Add translations if provided
            if (careerDto.Translations != null && careerDto.Translations.Any())
            {
                var translationRepository = _unitOfWork.Repository<CareerTranslation>();

                foreach (var translationDto in careerDto.Translations)
                {
                    var translation = new CareerTranslation
                    {
                        CareerId = career.Id,
                        LanguageCode = translationDto.LanguageCode,
                        Title = translationDto.Title,
                        Department = translationDto.Department,
                        Location = translationDto.Location,
                        Description = translationDto.Description,
                        Responsibilities = translationDto.Responsibilities,
                        Qualifications = translationDto.Qualifications,
                        Experience = translationDto.Experience,
                        Salary = translationDto.Salary,
                        EmploymentType = translationDto.EmploymentType,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = _currentUserService.UserId.ToString()
                    };

                    await translationRepository.AddAsync(translation);
                }

                await _unitOfWork.SaveChangesAsync();
            }

            _logger.LogInformation("Career created with ID {CareerId}", career.Id);
            return career.Id;
        }

        public async Task<bool> UpdateCareerAsync(Guid id, UpdateCareerDto careerDto)
        {
            var repository = _unitOfWork.Repository<Career>();
            var career = await repository.GetByIdAsync(id);

            if (career == null) return false;

            // Update properties
            if (careerDto.Title != null) career.Title = careerDto.Title;
            if (careerDto.Department != null) career.Department = careerDto.Department;
            if (careerDto.Location != null) career.Location = careerDto.Location;
            if (careerDto.Description != null) career.Description = careerDto.Description;
            if (careerDto.Responsibilities != null) career.Responsibilities = careerDto.Responsibilities;
            if (careerDto.Qualifications != null) career.Qualifications = careerDto.Qualifications;
            if (careerDto.Experience != null) career.Experience = careerDto.Experience;
            if (careerDto.Salary != null) career.Salary = careerDto.Salary;
            if (careerDto.ApplicationDeadline != null) career.ApplicationDeadline = careerDto.ApplicationDeadline;
            if (careerDto.Status != null) career.Status = careerDto.Status;
            if (careerDto.ContactEmail != null) career.ContactEmail = careerDto.ContactEmail;
            if (careerDto.ContactPhone != null) career.ContactPhone = careerDto.ContactPhone;
            if (careerDto.ApplicationUrl != null) career.ApplicationUrl = careerDto.ApplicationUrl;
            if (careerDto.IsFeatured != null) career.IsFeatured = careerDto.IsFeatured;
            if (careerDto.Vacancies != null) career.Vacancies = careerDto.Vacancies;
            if (careerDto.EmploymentType != null) career.EmploymentType = careerDto.EmploymentType;
            if (careerDto.IsActive != null) career.IsActive = careerDto.IsActive;
            // LastModifiedAt is set below
            career.LastModifiedBy = _currentUserService.UserId.ToString();
            career.LastModifiedAt = DateTime.UtcNow;

            await repository.UpdateAsync(career);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Career updated with ID {CareerId}", career.Id);
            return true;
        }

        public async Task<bool> DeleteCareerAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<Career>();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Career deleted with ID {CareerId}", id);
            return true;
        }

        public async Task<bool> UpdateCareerStatusAsync(Guid id, CareerStatus status)
        {
            var repository = _unitOfWork.Repository<Career>();
            var career = await repository.GetByIdAsync(id);

            if (career == null) return false;

            career.Status = status;
            // LastModifiedAt is set below
            career.LastModifiedBy = _currentUserService.UserId.ToString();
            career.LastModifiedAt = DateTime.UtcNow;

            await repository.UpdateAsync(career);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Career status updated to {Status} for ID {CareerId}", status, id);
            return true;
        }

        #region Translation Methods

        public async Task<bool> AddTranslationAsync(Guid careerId, CreateCareerTranslationDto translationDto)
        {
            var careerRepository = _unitOfWork.Repository<Career>();
            var career = await careerRepository.GetByIdAsync(careerId);

            if (career == null) return false;

            var translationRepository = _unitOfWork.Repository<CareerTranslation>();

            // Check if translation for this language already exists
            var existingTranslations = await translationRepository.FindAsync(
                t => t.CareerId == careerId && t.LanguageCode == translationDto.LanguageCode);

            if (existingTranslations.Any())
            {
                _logger.LogWarning("Translation for language {LanguageCode} already exists for career {CareerId}",
                    translationDto.LanguageCode, careerId);
                return false;
            }

            var translation = new CareerTranslation
            {
                CareerId = careerId,
                LanguageCode = translationDto.LanguageCode,
                Title = translationDto.Title,
                Department = translationDto.Department,
                Location = translationDto.Location,
                Description = translationDto.Description,
                Responsibilities = translationDto.Responsibilities,
                Qualifications = translationDto.Qualifications,
                Experience = translationDto.Experience,
                Salary = translationDto.Salary,
                EmploymentType = translationDto.EmploymentType,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = _currentUserService.UserId.ToString()
            };

            await translationRepository.AddAsync(translation);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Translation added for career {CareerId} in language {LanguageCode}",
                careerId, translationDto.LanguageCode);
            return true;
        }

        public async Task<bool> UpdateTranslationAsync(Guid careerId, string languageCode, UpdateCareerTranslationDto translationDto)
        {
            var translationRepository = _unitOfWork.Repository<CareerTranslation>();

            var translations = await translationRepository.FindAsync(
                t => t.CareerId == careerId && t.LanguageCode == languageCode);

            if (!translations.Any()) return false;

            var translation = translations.First();

            // Update properties
            translation.Title = translationDto.Title ?? translation.Title;
            translation.Department = translationDto.Department ?? translation.Department;
            translation.Location = translationDto.Location ?? translation.Location;
            translation.Description = translationDto.Description ?? translation.Description;
            translation.Responsibilities = translationDto.Responsibilities ?? translation.Responsibilities;
            translation.Qualifications = translationDto.Qualifications ?? translation.Qualifications;
            translation.Experience = translationDto.Experience ?? translation.Experience;
            translation.Salary = translationDto.Salary ?? translation.Salary;
            translation.EmploymentType = translationDto.EmploymentType ?? translation.EmploymentType;
            // LastModifiedAt is set below
            translation.LastModifiedBy = _currentUserService.UserId.ToString();
            translation.LastModifiedAt = DateTime.UtcNow;

            await translationRepository.UpdateAsync(translation);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Translation updated for career {CareerId} in language {LanguageCode}",
                careerId, languageCode);
            return true;
        }

        public async Task<bool> DeleteTranslationAsync(Guid careerId, string languageCode)
        {
            var translationRepository = _unitOfWork.Repository<CareerTranslation>();

            var translations = await translationRepository.FindAsync(
                t => t.CareerId == careerId && t.LanguageCode == languageCode);

            if (!translations.Any()) return false;

            var translation = translations.First();

            await translationRepository.DeleteAsync(translation);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Translation deleted for career {CareerId} in language {LanguageCode}",
                careerId, languageCode);
            return true;
        }

        #endregion

        #region Application Methods

        public async Task<(IEnumerable<CareerApplicationDto> Applications, int TotalCount)> GetCareerApplicationsAsync(CareerApplicationFilterDto filter)
        {
            var repository = _unitOfWork.Repository<CareerApplication>();
            var query = repository.AsQueryable("Career");

            // Apply filters
            if (filter.CareerId.HasValue)
            {
                query = query.Where(a => a.CareerId == filter.CareerId.Value);
            }

            if (!string.IsNullOrEmpty(filter.ApplicantName))
            {
                query = query.Where(a => a.ApplicantName.Contains(filter.ApplicantName));
            }

            if (!string.IsNullOrEmpty(filter.Email))
            {
                query = query.Where(a => a.Email.Contains(filter.Email));
            }

            if (filter.Status.HasValue)
            {
                query = query.Where(a => a.Status == filter.Status.Value);
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Get paginated results
            var applications = await query
                .OrderByDescending(a => a.CreatedAt)
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(a => new CareerApplicationDto
                {
                    Id = a.Id,
                    CareerId = a.CareerId,
                    ApplicantName = a.ApplicantName,
                    Email = a.Email,
                    Phone = a.Phone,
                    CoverLetter = a.CoverLetter,
                    ResumeFilePath = a.ResumeFilePath,
                    Status = a.Status,
                    ReviewComments = a.ReviewComments,
                    ReviewedBy = a.ReviewedBy,
                    ReviewedAt = a.ReviewedAt,
                    CreatedAt = a.CreatedAt,
                    LastModifiedAt = a.LastModifiedAt,
                    // Career title is retrieved from the navigation property
                })
                .ToListAsync();

            return (applications, totalCount);
        }

        public async Task<CareerApplicationDto?> GetApplicationByIdAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<CareerApplication>();
            var application = await repository.GetByIdAsync(id, new[] { "Career" });

            if (application == null) return null;

            return new CareerApplicationDto
            {
                Id = application.Id,
                CareerId = application.CareerId,
                ApplicantName = application.ApplicantName,
                Email = application.Email,
                Phone = application.Phone,
                CoverLetter = application.CoverLetter,
                ResumeFilePath = application.ResumeFilePath,
                Status = application.Status,
                ReviewComments = application.ReviewComments,
                ReviewedBy = application.ReviewedBy,
                ReviewedAt = application.ReviewedAt,
                CreatedAt = application.CreatedAt,
                LastModifiedAt = application.LastModifiedAt,
                // Career title is retrieved from the navigation property
            };
        }

        public async Task<Guid> CreateApplicationAsync(CreateCareerApplicationDto applicationDto)
        {
            var careerRepository = _unitOfWork.Repository<Career>();
            var career = await careerRepository.GetByIdAsync(applicationDto.CareerId);

            if (career == null || career.Status != CareerStatus.Open || !career.IsActive)
            {
                _logger.LogWarning("Application failed: Career {CareerId} is not available for applications", applicationDto.CareerId);
                throw new InvalidOperationException("Career is not available for applications");
            }

            if (career.ApplicationDeadline < DateTime.UtcNow)
            {
                _logger.LogWarning("Application failed: Application deadline has passed for career {CareerId}", applicationDto.CareerId);
                throw new InvalidOperationException("Application deadline has passed");
            }

            var applicationRepository = _unitOfWork.Repository<CareerApplication>();

            var application = new CareerApplication
            {
                CareerId = applicationDto.CareerId,
                ApplicantName = applicationDto.ApplicantName,
                Email = applicationDto.Email,
                Phone = applicationDto.Phone,
                CoverLetter = applicationDto.CoverLetter,
                ResumeFilePath = applicationDto.ResumeFilePath,
                Status = ApplicationStatus.Submitted,
                CreatedAt = DateTime.UtcNow
            };

            await applicationRepository.AddAsync(application);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Application created with ID {ApplicationId} for career {CareerId}",
                application.Id, applicationDto.CareerId);
            return application.Id;
        }

        public async Task<bool> UpdateApplicationStatusAsync(Guid id, UpdateCareerApplicationStatusDto statusDto)
        {
            var repository = _unitOfWork.Repository<CareerApplication>();
            var application = await repository.GetByIdAsync(id);

            if (application == null) return false;

            application.Status = statusDto.Status;
            application.ReviewComments = statusDto.ReviewComments;
            application.ReviewedBy = statusDto.ReviewedBy ?? _currentUserService.Username ?? "System";
            application.ReviewedAt = DateTime.UtcNow;
            // LastModifiedAt is set below
            application.LastModifiedBy = _currentUserService.UserId.ToString();
            application.LastModifiedAt = DateTime.UtcNow;

            await repository.UpdateAsync(application);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Application status updated to {Status} for ID {ApplicationId}",
                statusDto.Status, id);
            return true;
        }

        public async Task<bool> DeleteApplicationAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<CareerApplication>();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Application deleted with ID {ApplicationId}", id);
            return true;
        }

        #endregion
    }
}
