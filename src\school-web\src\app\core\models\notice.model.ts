import { BaseFilter } from './base.model';

export interface Notice {
  id: string;
  title: string;
  content: string;
  category: string;
  startDate: Date;
  endDate?: Date;
  priority: number;
  isActive: boolean;
  createdById?: string;
  createdByName?: string;
  createdAt: Date;
  lastModifiedAt?: Date;
  translations?: NoticeTranslation[];
}

export interface NoticeTranslation {
  id?: string;
  noticeId?: string;
  languageCode: string;
  title: string;
  content: string;
}

export interface CreateNotice {
  title: string;
  content: string;
  category: string;
  startDate: Date;
  endDate?: Date;
  priority: number;
  isActive: boolean;
  translations?: Omit<NoticeTranslation, 'id' | 'noticeId'>[];
}

export interface UpdateNotice {
  title: string;
  content: string;
  category: string;
  startDate: Date;
  endDate?: Date;
  priority: number;
  isActive: boolean;
  translations?: Omit<NoticeTranslation, 'id' | 'noticeId'>[];
}

export interface NoticeFilter extends BaseFilter {
  category?: string;
  isActive?: boolean;
  search?: string;
  priority?: number;
  startDateFrom?: Date;
  startDateTo?: Date;
}
