import { BaseModel } from './base.model';

export enum AcademicYearStatus {
  Draft = 0,
  Active = 1,
  Completed = 2,
  Archived = 3,
  Cancelled = 4
}

export enum TermType {
  Semester = 0,
  Trimester = 1,
  Quarter = 2,
  Annual = 3,
  Custom = 4
}

export enum TermStatus {
  Planned = 0,
  Active = 1,
  Completed = 2,
  Cancelled = 3
}

export interface AcademicYearTranslation {
  id: string;
  academicYearId: string;
  languageCode: string;
  displayName: string;
  description: string;
  remarks: string;
}

export interface CreateAcademicYearTranslation {
  languageCode: string;
  displayName: string;
  description: string;
  remarks: string;
}

export interface UpdateAcademicYearTranslation {
  displayName: string;
  description: string;
  remarks: string;
}

export interface TermTranslation {
  id: string;
  termId: string;
  languageCode: string;
  name: string;
  description: string;
  remarks: string;
}

export interface CreateTermTranslation {
  languageCode: string;
  name: string;
  description: string;
  remarks: string;
}

export interface UpdateTermTranslation {
  name: string;
  description: string;
  remarks: string;
}

export interface Term extends BaseModel {
  academicYearId: string;
  name: string;
  code: string;
  type: TermType;
  status: TermStatus;
  startDate: Date;
  endDate: Date;
  orderIndex: number;
  description: string;
  remarks: string;
  totalWorkingDays: number;
  totalHolidays: number;
  examStartDate?: Date;
  examEndDate?: Date;
  resultPublishDate?: Date;
  registrationDeadline?: Date;
  feePaymentDeadline?: Date;
  passingGrade?: number;
  maximumGrade?: number;
  gradingScale: string;
  academicYearName: string;
  translations: TermTranslation[];
}

export interface CreateTerm {
  academicYearId: string;
  name: string;
  code: string;
  type: TermType;
  status: TermStatus;
  startDate: Date;
  endDate: Date;
  orderIndex: number;
  description: string;
  remarks: string;
  totalWorkingDays: number;
  totalHolidays: number;
  examStartDate?: Date;
  examEndDate?: Date;
  resultPublishDate?: Date;
  registrationDeadline?: Date;
  feePaymentDeadline?: Date;
  passingGrade?: number;
  maximumGrade?: number;
  gradingScale: string;
  translations?: CreateTermTranslation[];
}

export interface UpdateTerm {
  name: string;
  code: string;
  type: TermType;
  status: TermStatus;
  startDate: Date;
  endDate: Date;
  orderIndex: number;
  description: string;
  remarks: string;
  totalWorkingDays: number;
  totalHolidays: number;
  examStartDate?: Date;
  examEndDate?: Date;
  resultPublishDate?: Date;
  registrationDeadline?: Date;
  feePaymentDeadline?: Date;
  passingGrade?: number;
  maximumGrade?: number;
  gradingScale: string;
}

export interface AcademicYear extends BaseModel {
  name: string;
  displayName: string;
  code: string;
  startDate: Date;
  endDate: Date;
  status: AcademicYearStatus;
  isCurrentYear: boolean;
  description: string;
  remarks: string;
  totalWorkingDays: number;
  totalHolidays: number;
  registrationStartDate?: Date;
  registrationEndDate?: Date;
  admissionStartDate?: Date;
  admissionEndDate?: Date;
  terms: Term[];
  translations: AcademicYearTranslation[];
}

export interface CreateAcademicYear {
  name: string;
  displayName: string;
  code: string;
  startDate: Date;
  endDate: Date;
  status: AcademicYearStatus;
  isCurrentYear: boolean;
  description: string;
  remarks: string;
  totalWorkingDays: number;
  totalHolidays: number;
  registrationStartDate?: Date;
  registrationEndDate?: Date;
  admissionStartDate?: Date;
  admissionEndDate?: Date;
  terms?: CreateTerm[];
  translations?: CreateAcademicYearTranslation[];
}

export interface UpdateAcademicYear {
  name: string;
  displayName: string;
  code: string;
  startDate: Date;
  endDate: Date;
  status: AcademicYearStatus;
  isCurrentYear: boolean;
  description: string;
  remarks: string;
  totalWorkingDays: number;
  totalHolidays: number;
  registrationStartDate?: Date;
  registrationEndDate?: Date;
  admissionStartDate?: Date;
  admissionEndDate?: Date;
}

export interface AcademicYearFilter {
  name?: string;
  code?: string;
  status?: AcademicYearStatus;
  isCurrentYear?: boolean;
  startDateFrom?: Date;
  startDateTo?: Date;
  endDateFrom?: Date;
  endDateTo?: Date;
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDescending: boolean;
}

export interface TermFilter {
  academicYearId?: string;
  name?: string;
  code?: string;
  type?: TermType;
  status?: TermStatus;
  startDateFrom?: Date;
  startDateTo?: Date;
  endDateFrom?: Date;
  endDateTo?: Date;
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDescending: boolean;
}

export interface AcademicYearStatistics {
  totalStudents: number;
  totalTerms: number;
  totalEvents: number;
}

export interface TermStatistics {
  totalStudents: number;
  totalEvents: number;
}
