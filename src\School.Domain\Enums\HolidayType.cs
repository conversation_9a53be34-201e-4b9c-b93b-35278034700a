namespace School.Domain.Enums;

public enum HolidayType
{
    /// <summary>
    /// National holidays (e.g., Independence Day, Victory Day)
    /// </summary>
    National = 0,
    
    /// <summary>
    /// Religious holidays (e.g., Eid, Christmas, Diwali)
    /// </summary>
    Religious = 1,
    
    /// <summary>
    /// Academic holidays specific to the institution
    /// </summary>
    Academic = 2,
    
    /// <summary>
    /// Seasonal breaks (e.g., Summer break, Winter break)
    /// </summary>
    SeasonalBreak = 3,
    
    /// <summary>
    /// Exam periods
    /// </summary>
    ExamPeriod = 4,
    
    /// <summary>
    /// Administrative holidays
    /// </summary>
    Administrative = 5,
    
    /// <summary>
    /// Cultural or local holidays
    /// </summary>
    Cultural = 6,
    
    /// <summary>
    /// Emergency or unplanned holidays
    /// </summary>
    Emergency = 7,
    
    /// <summary>
    /// Custom holidays defined by the institution
    /// </summary>
    Custom = 8
}
