using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class FacultySpecializationTranslationConfiguration : IEntityTypeConfiguration<FacultySpecializationTranslation>
{
    public void Configure(EntityTypeBuilder<FacultySpecializationTranslation> builder)
    {
        builder.Property(t => t.LanguageCode)
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(t => t.Name)
            .HasMaxLength(200)
            .IsRequired();

        builder.HasOne(t => t.FacultySpecialization)
            .WithMany()
            .HasForeignKey(t => t.FacultySpecializationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(t => new { t.FacultySpecializationId, t.LanguageCode })
            .IsUnique();

        builder.HasQueryFilter(fst => !fst.IsDeleted);
    }
}
