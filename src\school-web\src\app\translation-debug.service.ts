import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { TranslationValidatorService } from './translation-validator.service';
import { Observable, of, forkJoin } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class TranslationDebugService {
  // Flag to enable/disable verbose logging
  private verbose = false;

  constructor(
    private http: HttpClient,
    private translate: TranslateService,
    private validator: TranslationValidatorService
  ) {}

  /**
   * Enable or disable verbose logging
   */
  setVerbose(verbose: boolean): void {
    this.verbose = verbose;
    console.log(`Verbose logging ${verbose ? 'enabled' : 'disabled'}`);
  }

  /**
   * Debug method to check if translation files are accessible
   */
  checkTranslationFiles(): Observable<any> {
    console.log('Checking translation files...');

    // Check translation service state
    console.log('Current language:', this.translate.currentLang);
    console.log('Default language:', this.translate.defaultLang);
    console.log('Available languages:', this.translate.getLangs());

    // Try to get some translations
    this.translate.get(['SITE.TITLE', 'NAV.ABOUT']).subscribe({
      next: (translations) => {
        console.log('Sample translations retrieved:', translations);
      },
      error: (error) => {
        console.error('Error getting translations:', error);
      }
    });

    // Load legacy files and modules configuration in parallel
    const enLegacy$ = this.http.get('./assets/i18n/en.json').pipe(
      map(data => {
        if (this.verbose) {
          console.log('English legacy file loaded successfully:', data);
        } else {
          console.log('English legacy file loaded successfully');
        }
        return { en: data, status: 'success' };
      }),
      catchError(error => {
        console.error('Error loading English legacy file:', error.message);
        return of({ en: null, status: 'error', error: error.message });
      })
    );

    const bnLegacy$ = this.http.get('./assets/i18n/bn.json').pipe(
      map(data => {
        if (this.verbose) {
          console.log('Bengali legacy file loaded successfully:', data);
        } else {
          console.log('Bengali legacy file loaded successfully');
        }
        return { bn: data, status: 'success' };
      }),
      catchError(error => {
        console.error('Error loading Bengali legacy file:', error.message);
        return of({ bn: null, status: 'error', error: error.message });
      })
    );

    const modules$ = this.http.get<any>('./assets/i18n/modules.json').pipe(
      map(data => {
        console.log('Available translation modules:', data.modules);
        return { modules: data.modules, languages: data.languages, status: 'success' };
      }),
      catchError(error => {
        console.warn('Could not load modules configuration:', error.message);
        return of({ modules: [], languages: [], status: 'error', error: error.message });
      })
    );

    // Combine all observables and return a single result
    return forkJoin({
      en: enLegacy$,
      bn: bnLegacy$,
      config: modules$
    }).pipe(
      map((results: any) => ({
        legacy: {
          en: results.en,
          bn: results.bn
        },
        modules: results.config.modules,
        languages: results.config.languages,
        status: 'success'
      })),
      catchError(error => {
        console.error('Error checking translation files:', error);
        return of({ status: 'error', error: error.message });
      })
    );
  }

  /**
   * Validate translation files for missing keys
   * @param targetLang Optional target language to validate against English (default: 'bn')
   * @returns Observable with validation results
   */
  validateTranslations(targetLang: string = 'bn'): Observable<any> {
    console.log(`Validating translations for ${targetLang} against English...`);

    return this.validator.validateTranslations(targetLang).pipe(
      map(results => {
        if (this.verbose) {
          console.log('Translation validation results:', results);
        }

        // Count modules with issues
        const modulesWithIssues = Object.keys(results).filter(
          module => results[module].status !== 'complete'
        );

        if (modulesWithIssues.length > 0) {
          console.warn(`Found ${modulesWithIssues.length} modules with missing translations:`, modulesWithIssues);

          // Log detailed information about missing keys
          modulesWithIssues.forEach(module => {
            if (results[module].status === 'missing') {
              console.error(`Module '${module}' is missing in ${targetLang} translation`);
            } else if (results[module].status === 'incomplete') {
              console.warn(`Module '${module}' has ${results[module].count} missing keys in ${targetLang} translation:`);

              if (this.verbose || results[module].count < 10) {
                // Show all missing keys if verbose or if there are fewer than 10
                results[module].missingKeys.forEach((key: string) => {
                  console.warn(`  - ${key}`);
                });
              } else {
                // Show only the first 5 keys if there are many
                results[module].missingKeys.slice(0, 5).forEach((key: string) => {
                  console.warn(`  - ${key}`);
                });
                console.warn(`  ... and ${results[module].count - 5} more keys`);
              }
            }
          });
        } else {
          console.log('✓ All translation modules are complete!');
        }

        return results;
      }),
      catchError(error => {
        console.error('Error validating translations:', error);
        return of({ error: error.message });
      })
    );
  }

  /**
   * Generate template for missing translations
   * @param module Module name to generate template for
   * @param targetLang Target language (default: 'bn')
   */
  generateTranslationTemplate(module: string, targetLang: string = 'bn'): Observable<any> {
    console.log(`Generating translation template for module '${module}' in ${targetLang}...`);

    return this.validator.generateTranslationTemplate(module, targetLang).pipe(
      map(template => {
        console.log(`Template generated for module '${module}' in ${targetLang}:`);
        console.log(JSON.stringify(template, null, 2));
        return template;
      }),
      catchError(error => {
        console.error(`Error generating template for module '${module}':`, error);
        return of({ error: error.message });
      })
    );
  }
}
