import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { StudentService } from '../../../core/services/student.service';
import { AuthService } from '../../../core/services/auth.service';
import { Student, StudentResult, ExamType } from '../../../core/models/student.model';

@Component({
  selector: 'app-student-results',
  templateUrl: './student-results.component.html',
  styleUrls: ['./student-results.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatSnackBarModule
  ]
})
export class StudentResultsComponent implements OnInit {
  student: Student | null = null;
  results: StudentResult[] = [];
  gpa: number | null = null;

  filterForm: FormGroup;

  examTypes = [
    { value: ExamType.HalfYearly, label: 'Half Yearly' },
    { value: ExamType.Annual, label: 'Annual' },
    { value: ExamType.ClassTest, label: 'Class Test' },
    { value: ExamType.SSC, label: 'SSC' }
  ];

  loading = {
    student: true,
    results: false,
    gpa: false
  };

  error = {
    student: false,
    results: false,
    gpa: false
  };

  constructor(
    private studentService: StudentService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    const currentYear = new Date().getFullYear();

    this.filterForm = this.formBuilder.group({
      academicYear: [currentYear],
      examType: [ExamType.Annual]
    });
  }

  ngOnInit(): void {
    this.loadStudentData();
  }

  loadStudentData(): void {
    this.loading.student = true;

    // In a real application, you would fetch the student by user ID
    // For now, we'll use a mock student ID
    this.studentService.getStudentByStudentId('S2023-001')
      .subscribe({
        next: (student) => {
          this.student = student;
          this.loading.student = false;
          this.loadResults();
        },
        error: (err) => {
          console.error('Error loading student data:', err);
          this.error.student = true;
          this.loading.student = false;
          this.snackBar.open('Failed to load student data', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  loadResults(): void {
    if (!this.student) return;

    this.loading.results = true;
    this.error.results = false;

    const filters = this.filterForm.value;

    this.studentService.getStudentResults(this.student.id, filters.academicYear, filters.examType)
      .subscribe({
        next: (results) => {
          this.results = results;
          this.loading.results = false;
          this.calculateGPA();
        },
        error: (err) => {
          console.error('Error loading results:', err);
          this.error.results = true;
          this.loading.results = false;
          this.snackBar.open('Failed to load results', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  calculateGPA(): void {
    if (!this.student) return;

    this.loading.gpa = true;
    this.error.gpa = false;

    const filters = this.filterForm.value;

    this.studentService.calculateGPA(this.student.id, filters.academicYear, filters.examType)
      .subscribe({
        next: (response) => {
          this.gpa = response.gpa;
          this.loading.gpa = false;
        },
        error: (err) => {
          console.error('Error calculating GPA:', err);
          this.error.gpa = true;
          this.loading.gpa = false;
          this.snackBar.open('Failed to calculate GPA', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  applyFilter(): void {
    this.loadResults();
  }

  getGradeColor(grade: string): string {
    switch (grade) {
      case 'A+': return '#2e7d32';
      case 'A': return '#388e3c';
      case 'A-': return '#43a047';
      case 'B+': return '#689f38';
      case 'B': return '#7cb342';
      case 'C+': return '#ffa000';
      case 'C': return '#ffb300';
      case 'D': return '#f57c00';
      case 'F': return '#d32f2f';
      default: return '#757575';
    }
  }

  getExamTypeLabel(examType: ExamType): string {
    return this.examTypes.find(type => type.value === examType)?.label || 'Unknown';
  }

  getOptionalSubjectsCount(): number {
    if (!this.results || this.results.length === 0) return 0;
    return this.results.filter(r => r.isOptional).length;
  }
}
