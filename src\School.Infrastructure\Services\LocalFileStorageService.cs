using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using System;
using System.IO;
using System.Threading.Tasks;

namespace School.Infrastructure.Services;

public class LocalFileStorageService : IFileStorageService
{
    private readonly IWebHostEnvironment _environment;
    private readonly IConfiguration _configuration;
    private readonly ILogger<LocalFileStorageService> _logger;
    private readonly string _baseUrl;

    public LocalFileStorageService(
        IWebHostEnvironment environment,
        IConfiguration configuration,
        ILogger<LocalFileStorageService> logger)
    {
        _environment = environment;
        _configuration = configuration;
        _logger = logger;
        _baseUrl = _configuration["FileStorage:BaseUrl"] ?? "http://localhost:5003";

        // Ensure the uploads directory exists
        if (_environment.WebRootPath != null)
        {
            var uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads");
            if (!Directory.Exists(uploadsFolder))
            {
                Directory.CreateDirectory(uploadsFolder);
                _logger.LogInformation("Created uploads directory at {Path}", uploadsFolder);
            }
        }
        else
        {
            _logger.LogWarning("WebRootPath is null. File storage will not be available.");
        }
    }

    public async Task<string> SaveFileAsync(Stream fileStream, string fileName, string subFolder = "")
    {
        try
        {
            _logger.LogInformation("Saving file {FileName} to {SubFolder}", fileName, subFolder);

            if (_environment.WebRootPath == null)
            {
                _logger.LogWarning("WebRootPath is null. File storage will not be available.");
                return $"{_baseUrl}/uploads/{subFolder}/{fileName}";
            }

            var uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads", subFolder);

            // Create directory if it doesn't exist
            if (!Directory.Exists(uploadsFolder))
            {
                Directory.CreateDirectory(uploadsFolder);
                _logger.LogInformation("Created directory {Path}", uploadsFolder);
            }

            var filePath = Path.Combine(uploadsFolder, fileName);

            using (var fileStream2 = new FileStream(filePath, FileMode.Create))
            {
                await fileStream.CopyToAsync(fileStream2);
            }

            var fileUrl = GetFileUrl(fileName, subFolder);
            _logger.LogInformation("File saved successfully. URL: {FileUrl}", fileUrl);
            return fileUrl;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving file {FileName} to {SubFolder}: {ErrorMessage}", fileName, subFolder, ex.Message);
            throw;
        }
    }

    public Task<bool> DeleteFileAsync(string filePath)
    {
        try
        {
            _logger.LogInformation("Deleting file at path {FilePath}", filePath);

            // Extract the file name and subfolder from the path
            string[] pathParts = filePath.Split('/', '\\');
            string fileName = pathParts.Length > 0 ? pathParts[pathParts.Length - 1] : string.Empty;
            string subFolder = pathParts.Length > 1 ? pathParts[pathParts.Length - 2] : string.Empty;

            if (_environment.WebRootPath == null)
            {
                _logger.LogWarning("WebRootPath is null. File storage will not be available.");
                return Task.FromResult(false);
            }

            var fullPath = Path.Combine(_environment.WebRootPath, "uploads", subFolder, fileName);
            _logger.LogInformation("Resolved full path: {FullPath}", fullPath);

            if (File.Exists(fullPath))
            {
                File.Delete(fullPath);
                _logger.LogInformation("File deleted successfully");
                return Task.FromResult(true);
            }

            _logger.LogWarning("File not found at path {FullPath}", fullPath);
            return Task.FromResult(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file at path {FilePath}: {ErrorMessage}", filePath, ex.Message);
            throw;
        }
    }

    public string GetFileUrl(string fileName, string subFolder = "")
    {
        try
        {
            _logger.LogInformation("Getting URL for file {FileName} in {SubFolder}", fileName, subFolder);

            // If fileName contains path separators, it's a file path
            if (fileName.Contains('/') || fileName.Contains('\\'))
            {
                // Extract the file name and subfolder from the path
                string[] pathParts = fileName.Split('/', '\\');
                string extractedFileName = pathParts.Length > 0 ? pathParts[pathParts.Length - 1] : string.Empty;
                string extractedSubFolder = pathParts.Length > 1 ? pathParts[pathParts.Length - 2] : string.Empty;

                var url = $"{_baseUrl}/uploads/{extractedSubFolder}/{extractedFileName}";
                _logger.LogInformation("Generated URL from path: {Url}", url);
                return url;
            }

            // Otherwise, use the provided fileName and subFolder
            var directUrl = $"{_baseUrl}/uploads/{subFolder}/{fileName}";
            _logger.LogInformation("Generated URL from parameters: {Url}", directUrl);
            return directUrl;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating URL for file {FileName} in {SubFolder}: {ErrorMessage}", fileName, subFolder, ex.Message);
            // Return a basic URL even in case of error
            return $"{_baseUrl}/uploads/{subFolder}/{fileName}";
        }
    }
}
