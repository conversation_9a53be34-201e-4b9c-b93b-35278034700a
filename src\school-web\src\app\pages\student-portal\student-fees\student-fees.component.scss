.fees-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.fees-content {
  max-width: 1000px;
  margin: 0 auto;
}

.filter-card {
  margin-bottom: 24px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.summary-card {
  margin-bottom: 24px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: #f5f5f5;
}

.summary-value {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 8px;
}

.summary-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}

.paid {
  color: #2e7d32;
}

.pending {
  color: #ff8f00;
}

.partial {
  color: #1565c0;
}

.overdue {
  color: #c62828;
}

.waived {
  color: #6a1b9a;
}

.fees-loading,
.fees-error {
  margin-bottom: 24px;
}

.fees-error {
  text-align: center;
  padding: 16px;

  mat-icon {
    vertical-align: middle;
    margin-right: 8px;
  }
}

.no-fees {
  text-align: center;
  padding: 16px;
}

.fee-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.fee-card {
  display: flex;
  flex-direction: column;
}

.overdue-card {
  border: 1px solid #ffcdd2;
}

.fee-status {
  margin-left: auto;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.paid {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.partial {
  background-color: #e3f2fd;
  color: #1565c0;
}

.pending {
  background-color: #fff8e1;
  color: #ff8f00;
}

.overdue {
  background-color: #ffebee;
  color: #c62828;
}

.waived {
  background-color: #f3e5f5;
  color: #6a1b9a;
}

.fee-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 16px;
}

.fee-amount,
.fee-paid,
.fee-due,
.fee-due-date,
.fee-paid-date,
.fee-payment-method,
.fee-transaction,
.fee-remarks {
  display: flex;
  flex-direction: column;
}

.fee-remarks {
  grid-column: span 2;
}

.fee-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.fee-value {
  font-size: 16px;
  font-weight: 500;
}

.overdue-date {
  color: #c62828;
  display: flex;
  align-items: center;
}

.overdue-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin-left: 4px;
}

mat-card-actions {
  margin-top: auto;
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .fee-details {
    grid-template-columns: 1fr;
  }

  .fee-remarks {
    grid-column: span 1;
  }
}
