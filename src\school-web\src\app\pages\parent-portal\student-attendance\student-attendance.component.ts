import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { ParentService } from '../../../core/services/parent.service';
import { AuthService } from '../../../core/services/auth.service';
import { StudentAttendance } from '../../../core/models/student.model';

@Component({
  selector: 'app-student-attendance',
  templateUrl: './student-attendance.component.html',
  styleUrls: ['./student-attendance.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    DatePipe,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatTableModule,
    MatSnackBarModule
  ]
})
export class StudentAttendanceComponent implements OnInit {
  studentId: number = 0;
  attendanceRecords: StudentAttendance[] = [];
  filterForm: FormGroup;

  loading = {
    attendance: true
  };

  error = {
    attendance: false
  };

  displayedColumns: string[] = ['date', 'status', 'period', 'subjectCode', 'remarks'];

  constructor(
    private route: ActivatedRoute,
    private parentService: ParentService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    this.filterForm = this.formBuilder.group({
      fromDate: [oneMonthAgo],
      toDate: [today]
    });
  }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.studentId = +params['id'];
      this.loadAttendance();
    });
  }

  loadAttendance(): void {
    if (!this.studentId) return;

    this.loading.attendance = true;
    this.error.attendance = false;

    const parentId = 1; // In a real app, get from auth service
    const formValues = this.filterForm.value;

    this.parentService.getStudentAttendanceForParent(
      parentId,
      this.studentId,
      formValues.fromDate,
      formValues.toDate
    ).subscribe({
      next: (records) => {
        this.attendanceRecords = records;
        this.loading.attendance = false;
      },
      error: (err) => {
        console.error('Error loading attendance records:', err);
        this.error.attendance = true;
        this.loading.attendance = false;
        this.snackBar.open('Failed to load attendance records', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  getStatusClass(status: number): string {
    switch (status) {
      case 0: return 'present';
      case 1: return 'absent';
      case 2: return 'late';
      case 3: return 'excused';
      case 4: return 'on-leave';
      default: return '';
    }
  }

  getStatusText(status: number): string {
    switch (status) {
      case 0: return 'Present';
      case 1: return 'Absent';
      case 2: return 'Late';
      case 3: return 'Excused';
      case 4: return 'On Leave';
      default: return 'Unknown';
    }
  }
}
