import { <PERSON><PERSON><PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { Subject, takeUntil, combineLatest } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

// Services
import { CalendarService, CalendarEvent, CalendarFilter, CalendarEventType, CalendarStatistics } from '../../../../core/services/calendar.service';
import { AcademicYearService } from '../../../../core/services/academic-year.service';
import { HolidayService } from '../../../../core/services/holiday.service';

// Models
import { AcademicYear, Term } from '../../../../core/models/academic-year.model';

@Component({
  selector: 'app-calendar-view',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatChipsModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatButtonToggleModule
  ],
  template: `
    <div class="calendar-container">
      <!-- Header with controls -->
      <mat-card class="calendar-header">
        <mat-card-content>
          <div class="header-row">
            <div class="navigation-controls">
              <button mat-icon-button (click)="onPreviousPeriod()" [disabled]="loading">
                <mat-icon>chevron_left</mat-icon>
              </button>
              <button mat-button (click)="onToday()" [disabled]="loading">Today</button>
              <button mat-icon-button (click)="onNextPeriod()" [disabled]="loading">
                <mat-icon>chevron_right</mat-icon>
              </button>
              <h2 class="period-title">{{ getPeriodTitle() }}</h2>
            </div>

            <div class="view-controls">
              <mat-button-toggle-group [(value)]="currentView" (change)="onViewChange($event.value)">
                <mat-button-toggle value="day">Day</mat-button-toggle>
                <mat-button-toggle value="week">Week</mat-button-toggle>
                <mat-button-toggle value="month">Month</mat-button-toggle>
              </mat-button-toggle-group>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Filters -->
      <mat-card class="filters-card">
        <mat-card-content>
          <form [formGroup]="filterForm" class="filters-form">
            <div class="filter-row">
              <mat-form-field appearance="outline">
                <mat-label>Academic Year</mat-label>
                <mat-select formControlName="academicYearId">
                  <mat-option value="">All Academic Years</mat-option>
                  <mat-option *ngFor="let year of academicYears" [value]="year.id">
                    {{ year.displayName }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Term</mat-label>
                <mat-select formControlName="termId">
                  <mat-option value="">All Terms</mat-option>
                  <mat-option *ngFor="let term of terms" [value]="term.id">
                    {{ term.name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Event Types</mat-label>
                <mat-select formControlName="eventTypes" multiple>
                  <mat-option *ngFor="let type of eventTypes" [value]="type">
                    {{ eventTypeNames[type] }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="filter-row">
              <mat-checkbox formControlName="showHolidays">Show Holidays</mat-checkbox>
              <mat-checkbox formControlName="showAcademicEvents">Show Academic Events</mat-checkbox>
              <mat-checkbox formControlName="showPublicOnly">Public Events Only</mat-checkbox>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Statistics -->
      <mat-card class="statistics-card" *ngIf="statistics">
        <mat-card-header>
          <mat-card-title>Calendar Statistics</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-value">{{ statistics.totalEvents }}</span>
              <span class="stat-label">Total Events</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ statistics.academicEvents }}</span>
              <span class="stat-label">Academic Events</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ statistics.holidays }}</span>
              <span class="stat-label">Holidays</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ statistics.upcomingEvents }}</span>
              <span class="stat-label">Upcoming</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Calendar View -->
      <mat-card class="calendar-card">
        <mat-card-content>
          <div class="calendar-loading" *ngIf="loading">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Loading calendar events...</p>
          </div>

          <div class="calendar-content" *ngIf="!loading">
            <!-- Month View -->
            <div class="month-view" *ngIf="currentView === 'month'">
              <div class="month-grid">
                <div class="day-header" *ngFor="let day of ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']">
                  {{ day }}
                </div>
                <div class="day-cell"
                     *ngFor="let date of getMonthDates()"
                     [class.other-month]="!isSameMonth(date)"
                     [class.today]="isToday(date)"
                     [class.selected]="isSelected(date)">
                  <div class="date-number">{{ date.getDate() }}</div>
                  <div class="events-container">
                    <div class="event-item"
                         *ngFor="let event of getEventsForDate(date)"
                         [style.background-color]="getEventTypeColor(event.eventType)"
                         [matTooltip]="getEventTooltip(event)">
                      <span class="event-title">{{ event.title }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Week/Day View -->
            <div class="timeline-view" *ngIf="currentView === 'week' || currentView === 'day'">
              <div class="timeline-header">
                <div class="time-column"></div>
                <div class="date-column" *ngFor="let date of getViewDates()">
                  <div class="date-header">
                    <div class="day-name">{{ getDayName(date) }}</div>
                    <div class="date-number" [class.today]="isToday(date)">{{ date.getDate() }}</div>
                  </div>
                </div>
              </div>

              <div class="timeline-body">
                <div class="time-slots">
                  <div class="time-slot" *ngFor="let hour of getHours()">
                    <div class="time-label">{{ formatHour(hour) }}</div>
                    <div class="slot-columns">
                      <div class="slot-column" *ngFor="let date of getViewDates()">
                        <div class="events-in-slot">
                          <div class="timeline-event"
                               *ngFor="let event of getEventsForDateAndHour(date, hour)"
                               [style.background-color]="getEventTypeColor(event.eventType)"
                               [matTooltip]="getEventTooltip(event)">
                            <div class="event-title">{{ event.title }}</div>
                            <div class="event-time">{{ formatEventDuration(event) }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styleUrls: ['./calendar-view.component.scss']
})
export class CalendarViewComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // Form and filters
  filterForm: FormGroup;
  
  // Data
  events: CalendarEvent[] = [];
  academicYears: AcademicYear[] = [];
  terms: Term[] = [];
  statistics: CalendarStatistics | null = null;
  
  // UI State
  loading = false;
  selectedDate = new Date();
  currentView: 'month' | 'week' | 'day' = 'month';
  
  // Calendar configuration
  eventTypes = Object.values(CalendarEventType).filter(v => typeof v === 'number') as CalendarEventType[];
  eventTypeNames = {
    [CalendarEventType.AcademicEvent]: 'Academic Events',
    [CalendarEventType.Holiday]: 'Holidays',
    [CalendarEventType.Exam]: 'Exams',
    [CalendarEventType.Admission]: 'Admissions',
    [CalendarEventType.Cultural]: 'Cultural Events',
    [CalendarEventType.Sports]: 'Sports Events',
    [CalendarEventType.Meeting]: 'Meetings',
    [CalendarEventType.Other]: 'Other Events'
  };

  constructor(
    private fb: FormBuilder,
    private calendarService: CalendarService,
    private academicYearService: AcademicYearService,
    private holidayService: HolidayService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private cdr: ChangeDetectorRef
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadInitialData();
    this.setupFormSubscriptions();
    this.loadCalendarEvents();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    this.filterForm = this.fb.group({
      startDate: [startOfMonth],
      endDate: [endOfMonth],
      academicYearId: [''],
      termId: [''],
      eventTypes: [[]],
      showHolidays: [true],
      showAcademicEvents: [true],
      showPublicOnly: [false]
    });
  }

  private setupFormSubscriptions(): void {
    this.filterForm.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.loadCalendarEvents();
      });

    // Update terms when academic year changes
    this.filterForm.get('academicYearId')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(academicYearId => {
        this.loadTermsForAcademicYear(academicYearId);
        this.filterForm.patchValue({ termId: '' }, { emitEvent: false });
      });
  }

  private async loadInitialData(): Promise<void> {
    try {
      this.loading = true;
      
      // Load academic years
      const academicYearsResult = await this.academicYearService.getAcademicYears({
        page: 1,
        pageSize: 100,
        sortBy: 'startDate',
        sortDirection: 'desc'
      }).toPromise();
      
      if (academicYearsResult) {
        this.academicYears = academicYearsResult.items;
        
        // Set current academic year as default
        const currentYear = this.academicYears.find(ay => ay.isCurrentYear);
        if (currentYear) {
          this.filterForm.patchValue({ academicYearId: currentYear.id }, { emitEvent: false });
          await this.loadTermsForAcademicYear(currentYear.id);
        }
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
      this.showError('Failed to load initial data');
    } finally {
      this.loading = false;
    }
  }

  private async loadTermsForAcademicYear(academicYearId: string): Promise<void> {
    if (!academicYearId) {
      this.terms = [];
      return;
    }

    try {
      const terms = await this.academicYearService.getTermsByAcademicYear(academicYearId).toPromise();
      this.terms = terms || [];
    } catch (error) {
      console.error('Error loading terms:', error);
      this.terms = [];
    }
  }

  private async loadCalendarEvents(): Promise<void> {
    const formValue = this.filterForm.value;
    
    if (!formValue.startDate || !formValue.endDate) {
      return;
    }

    try {
      this.loading = true;

      const filter: CalendarFilter = {
        startDate: formValue.startDate,
        endDate: formValue.endDate,
        academicYearId: formValue.academicYearId || undefined,
        termId: formValue.termId || undefined,
        eventTypes: formValue.eventTypes || [],
        showHolidays: formValue.showHolidays,
        showAcademicEvents: formValue.showAcademicEvents,
        showPublicOnly: formValue.showPublicOnly
      };

      // Load events and statistics in parallel
      const [events, statistics] = await Promise.all([
        this.calendarService.getIntegratedCalendarEvents(filter).toPromise(),
        this.calendarService.getCalendarStatistics(filter.academicYearId, filter.termId).toPromise()
      ]);

      this.events = events || [];
      this.statistics = statistics || null;
      
      // Update service state
      this.calendarService.setCurrentFilter(filter);
      this.calendarService.updateEvents(this.events);
      if (this.statistics) {
        this.calendarService.updateStatistics(this.statistics);
      }

      this.cdr.detectChanges();
    } catch (error) {
      console.error('Error loading calendar events:', error);
      this.showError('Failed to load calendar events');
    } finally {
      this.loading = false;
    }
  }

  // UI Event Handlers
  onDateChange(date: Date): void {
    this.selectedDate = date;
    this.updateDateRange();
  }

  onViewChange(view: 'month' | 'week' | 'day'): void {
    this.currentView = view;
    this.updateDateRange();
  }

  onPreviousPeriod(): void {
    switch (this.currentView) {
      case 'month':
        this.selectedDate = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth() - 1, 1);
        break;
      case 'week':
        this.selectedDate = new Date(this.selectedDate.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'day':
        this.selectedDate = new Date(this.selectedDate.getTime() - 24 * 60 * 60 * 1000);
        break;
    }
    this.updateDateRange();
  }

  onNextPeriod(): void {
    switch (this.currentView) {
      case 'month':
        this.selectedDate = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth() + 1, 1);
        break;
      case 'week':
        this.selectedDate = new Date(this.selectedDate.getTime() + 7 * 24 * 60 * 60 * 1000);
        break;
      case 'day':
        this.selectedDate = new Date(this.selectedDate.getTime() + 24 * 60 * 60 * 1000);
        break;
    }
    this.updateDateRange();
  }

  onToday(): void {
    this.selectedDate = new Date();
    this.updateDateRange();
  }

  private updateDateRange(): void {
    let startDate: Date;
    let endDate: Date;

    switch (this.currentView) {
      case 'month':
        startDate = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth(), 1);
        endDate = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth() + 1, 0);
        break;
      case 'week':
        const dayOfWeek = this.selectedDate.getDay();
        startDate = new Date(this.selectedDate.getTime() - dayOfWeek * 24 * 60 * 60 * 1000);
        endDate = new Date(startDate.getTime() + 6 * 24 * 60 * 60 * 1000);
        break;
      case 'day':
        startDate = new Date(this.selectedDate);
        endDate = new Date(this.selectedDate);
        break;
      default:
        return;
    }

    this.filterForm.patchValue({ startDate, endDate });
  }

  // Utility Methods
  getEventTypeColor(eventType: CalendarEventType): string {
    return this.calendarService.getEventTypeColor(eventType);
  }

  getEventTypeIcon(eventType: CalendarEventType): string {
    return this.calendarService.getEventTypeIcon(eventType);
  }

  formatEventDuration(event: CalendarEvent): string {
    return this.calendarService.formatEventDuration(event);
  }

  getEventsForDate(date: Date): CalendarEvent[] {
    return this.events.filter(event => {
      const eventDate = new Date(event.startDate);
      return eventDate.toDateString() === date.toDateString();
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  // Template helper methods
  getPeriodTitle(): string {
    switch (this.currentView) {
      case 'month':
        return this.selectedDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
      case 'week':
        const weekStart = new Date(this.selectedDate.getTime() - this.selectedDate.getDay() * 24 * 60 * 60 * 1000);
        const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
        return `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
      case 'day':
        return this.selectedDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' });
      default:
        return '';
    }
  }

  getMonthDates(): Date[] {
    const year = this.selectedDate.getFullYear();
    const month = this.selectedDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay.getTime() - firstDay.getDay() * 24 * 60 * 60 * 1000);
    const endDate = new Date(lastDay.getTime() + (6 - lastDay.getDay()) * 24 * 60 * 60 * 1000);

    const dates: Date[] = [];
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      dates.push(new Date(date));
    }
    return dates;
  }

  getViewDates(): Date[] {
    if (this.currentView === 'day') {
      return [new Date(this.selectedDate)];
    } else if (this.currentView === 'week') {
      const dates: Date[] = [];
      const startOfWeek = new Date(this.selectedDate.getTime() - this.selectedDate.getDay() * 24 * 60 * 60 * 1000);
      for (let i = 0; i < 7; i++) {
        dates.push(new Date(startOfWeek.getTime() + i * 24 * 60 * 60 * 1000));
      }
      return dates;
    }
    return [];
  }

  getHours(): number[] {
    return Array.from({ length: 24 }, (_, i) => i);
  }

  isSameMonth(date: Date): boolean {
    return date.getMonth() === this.selectedDate.getMonth() && date.getFullYear() === this.selectedDate.getFullYear();
  }

  isToday(date: Date): boolean {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  }

  isSelected(date: Date): boolean {
    return date.toDateString() === this.selectedDate.toDateString();
  }

  getDayName(date: Date): string {
    return date.toLocaleDateString('en-US', { weekday: 'short' });
  }

  formatHour(hour: number): string {
    return hour === 0 ? '12 AM' : hour < 12 ? `${hour} AM` : hour === 12 ? '12 PM' : `${hour - 12} PM`;
  }

  getEventsForDateAndHour(date: Date, hour: number): CalendarEvent[] {
    return this.events.filter(event => {
      const eventDate = new Date(event.startDate);
      return eventDate.toDateString() === date.toDateString() &&
             (event.isAllDay || eventDate.getHours() === hour);
    });
  }

  getEventTooltip(event: CalendarEvent): string {
    let tooltip = `${event.title}\n`;
    if (event.description) {
      tooltip += `${event.description}\n`;
    }
    tooltip += `${this.formatEventDuration(event)}`;
    if (event.location) {
      tooltip += `\nLocation: ${event.location}`;
    }
    return tooltip;
  }
}
