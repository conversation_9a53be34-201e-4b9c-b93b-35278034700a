// Material Design 3 Login Component
// Using CSS custom properties that are set by the global theme

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideInFromRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInFromLeft {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

.login-container {
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
  background-color: var(--sys-color-background);
  color: var(--sys-color-on-background);
  position: relative;
  display: flex;
  flex-direction: column;

  .background-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;
    overflow: hidden;

    .gradient-orb {
      position: absolute;
      border-radius: 50%;
      filter: blur(60px);
      opacity: 0.1;
      animation: float 20s ease-in-out infinite;

      &.orb-1 {
        width: 400px;
        height: 400px;
        background: radial-gradient(circle, var(--sys-color-primary) 0%, transparent 70%);
        top: -200px;
        right: -200px;
      }

      &.orb-2 {
        width: 300px;
        height: 300px;
        background: radial-gradient(circle, var(--sys-color-primary) 0%, transparent 70%);
        bottom: -150px;
        left: -150px;
        animation-delay: -10s;
      }

      &.orb-3 {
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, var(--sys-color-primary) 0%, transparent 70%);
        top: 40%;
        left: 60%;
        transform: translate(-50%, -50%);
        animation-delay: -5s;
      }
    }
  }

  .top-nav {
    position: relative;
    z-index: 10;
    padding: 16px 24px;
    background-color: var(--sys-color-surface);
    backdrop-filter: blur(16px);
    border-bottom: 1px solid var(--sys-color-outline-variant);
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);

    .nav-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;

      .logo-brand {
        display: flex;
        align-items: center;
        gap: 12px;
        color: var(--sys-color-on-surface);

        .brand-icon {
          width: 48px;
          height: 48px;
          background-color: var(--sys-color-primary);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--sys-color-on-primary);
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);

          mat-icon {
            font-size: 28px;
            width: 28px;
            height: 28px;
          }
        }

        .brand-text {
          font-size: 1.375rem;
          font-weight: 700;
          color: var(--sys-color-primary);
          letter-spacing: -0.025em;
        }
      }

      .nav-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .language-selector .lang-button {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          border-radius: 20px;
          background-color: var(--sys-color-surface-variant);
          border: 1px solid var(--sys-color-outline-variant);
          color: var(--sys-color-on-surface-variant);
          font-weight: 500;
          transition: all 0.2s ease;

          &:hover {
            background-color: var(--sys-color-primary-container);
            border-color: var(--sys-color-primary);
            color: var(--sys-color-on-primary-container);
          }

          .flag {
            font-size: 1.125rem;
          }
        }

        .theme-toggle {
          width: 48px;
          height: 48px;
          border-radius: 24px;
          background-color: var(--sys-color-surface-variant);
          border: 1px solid var(--sys-color-outline-variant);
          color: var(--sys-color-on-surface-variant);
          transition: all 0.2s ease;

          &:hover {
            background-color: var(--sys-color-primary-container);
            border-color: var(--sys-color-primary);
            color: var(--sys-color-on-primary-container);
          }

          mat-icon {
            font-size: 24px;
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }

  .main-content {
    position: relative;
    z-index: 5;
    padding: 8px 24px;
    flex: 1;
    display: flex;
    align-items: start;
    overflow-y: auto;
    min-height: 0;

    .content-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 32px;
      max-width: 1200px;
      margin: 0 auto;
      width: 100%;
      align-items: start;
      min-height: 0;
      padding-top: 16px;

      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
        gap: 24px;
      }

      .welcome-section {
        animation: slideInFromLeft 0.8s ease;

        .welcome-content .welcome-header {
          margin-bottom: 16px;

          .welcome-title {
            font-size: 2rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 8px;
            color: var(--sys-color-on-surface);
            letter-spacing: -0.025em;

            @media (max-width: 768px) {
              font-size: 1.75rem;
            }

            @media (max-width: 480px) {
              font-size: 1.5rem;
            }
          }

          .welcome-subtitle {
            font-size: 1rem;
            color: var(--sys-color-on-surface-variant);
            line-height: 1.4;
            font-weight: 400;
          }
        }

        .features-list {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px;
            background-color: var(--sys-color-surface);
            border-radius: 8px;
            border: 1px solid var(--sys-color-outline-variant);
            transition: all 0.3s ease;
            box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2), 0px 1px 3px 1px rgba(0, 0, 0, 0.1);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
              background-color: var(--sys-color-primary-container);
              color: var(--sys-color-on-primary-container);
              border-color: var(--sys-color-primary);
            }

            mat-icon {
              color: var(--sys-color-primary);
              font-size: 24px;
              width: 24px;
              height: 24px;
              transition: color 0.3s ease;
            }

            &:hover mat-icon {
              color: var(--sys-color-on-primary-container);
            }

            span {
              font-size: 1rem;
              font-weight: 500;
              color: var(--sys-color-on-surface);
              transition: color 0.3s ease;
            }

            &:hover span {
              color: var(--sys-color-on-primary-container);
            }
          }
        }
      }

      .login-section {
        animation: slideInFromRight 0.8s ease;

        .login-card {
          background-color: var(--sys-color-surface);
          border-radius: 12px;
          padding: 14px;
          box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2), 0px 4px 8px 2px rgba(0, 0, 0, 0.1);
          border: 1px solid var(--sys-color-outline-variant);
          max-width: 480px;
          margin: 0 auto;
          height: calc(100vh - 10%);
          overflow: hidden;
          display: flex;
          flex-direction: column;

          .card-header {
            text-align: center;
            margin-bottom: 6px;
            flex-shrink: 0;

            .card-title {
              font-size: 1rem;
              font-weight: 600;
              color: var(--sys-color-on-surface);
              margin-bottom: 0px;
              letter-spacing: -0.025em;
            }
          }
        }
      }
    }
  }

  .card-content {
    display: flex;
    flex-direction: column;
    gap: 6px;
    flex: 1;
    min-height: 0;
    overflow-y: auto;

    .section-header {
      text-align: center;
      margin-bottom: 4px;

      .section-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--sys-color-on-surface);
        margin-bottom: 0px;
        letter-spacing: -0.025em;
      }
    }

      .mfa-icon-wrapper {
        margin-bottom: 16px;

        .mfa-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          color: var(--sys-color-primary);
        }
      }
    }

    .user-type-section {
      margin-bottom: 4px;
      flex-shrink: 0;

      .user-type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 12px;

        .user-type-card {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 8px 6px;
          background-color: var(--sys-color-surface);
          border: 1px solid var(--sys-color-outline-variant);
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          min-height: 60px;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
            border-color: var(--sys-color-primary);
            background-color: var(--sys-color-primary-container);
          }

          &.selected {
            background-color: var(--sys-color-primary-container);
            border-color: var(--sys-color-primary);
            box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
          }

          .card-icon {
            width: 32px;
            height: 32px;
            background-color: var(--sys-color-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 6px;
            transition: all 0.3s ease;

            mat-icon {
              font-size: 20px;
              width: 20px;
              height: 20px;
              color: var(--sys-color-on-primary);
            }
          }

          &:hover .card-icon,
          &.selected .card-icon {
            background-color: var(--sys-color-on-primary-container);

            mat-icon {
              color: var(--sys-color-primary-container);
            }
          }

          .card-content {
            text-align: center;
            flex: 1;

            .card-title {
              font-size: 0.9375rem;
              font-weight: 600;
              color: var(--sys-color-on-surface);
              margin-bottom: 4px;
              transition: color 0.3s ease;
            }

            .card-description {
              font-size: 0.75rem;
              color: var(--sys-color-on-surface-variant);
              line-height: 1.4;
              transition: color 0.3s ease;
            }
          }

          &:hover .card-content,
          &.selected .card-content {
            .card-title {
              color: var(--sys-color-on-primary-container);
            }

            .card-description {
              color: var(--sys-color-on-primary-container);
            }
          }

          .card-indicator {
            position: absolute;
            top: 12px;
            right: 12px;

            mat-icon {
              font-size: 20px;
              width: 20px;
              height: 20px;
              color: var(--sys-color-primary);
            }
          }
        }
      }
    }

    .form-section {
      margin-bottom: 6px;

      .login-form, .mfa-form {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .form-field {
          .full-width {
            width: 100%;

            .mat-mdc-form-field-subscript-wrapper {
              margin-top: 4px;
            }
          }
        }

        .form-options {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 0.875rem;
          margin-top: -8px;

          .remember-me {
            .mdc-checkbox {
              --mdc-checkbox-selected-checkmark-color: var(--sys-color-on-primary);
              --mdc-checkbox-selected-focus-icon-color: var(--sys-color-primary);
              --mdc-checkbox-selected-hover-icon-color: var(--sys-color-primary);
              --mdc-checkbox-selected-icon-color: var(--sys-color-primary);
              --mdc-checkbox-selected-pressed-icon-color: var(--sys-color-primary);
            }
          }

          .forgot-password-link {
            color: var(--sys-color-primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;

            &:hover {
              text-decoration: underline;
              color: var(--sys-color-primary);
            }
          }
        }

        .login-button, .verify-button {
          width: 100%;
          height: 36px;
          border-radius: 18px;
          font-weight: 600;
          font-size: 0.8125rem;
          background-color: var(--sys-color-primary);
          color: var(--sys-color-on-primary);
          margin-top: 6px;
          transition: all 0.3s ease;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2), 0px 1px 3px 1px rgba(0, 0, 0, 0.1);

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }

          .button-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            mat-icon {
              font-size: 20px;
              width: 20px;
              height: 20px;
            }
          }
        }
      }

      .mfa-actions {
        display: flex;
        gap: 16px;
        margin-top: 24px;

        .back-button {
          flex: 1;
          height: 56px;
          border: 2px solid var(--sys-color-outline);
          background-color: transparent;
          border-radius: 28px;
          color: var(--sys-color-on-surface);
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            background-color: var(--sys-color-surface-variant);
            border-color: var(--sys-color-primary);
          }
        }

        .verify-button {
          flex: 2;
        }
      }

      .backup-options {
        text-align: center;
        margin-top: 24px;
        padding-top: 24px;
        border-top: 1px solid var(--sys-color-outline-variant);

        .backup-text {
          font-size: 0.875rem;
          color: var(--sys-color-on-surface-variant);
          margin-bottom: 12px;
        }

        .backup-button {
          color: var(--sys-color-primary);
          font-weight: 500;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .additional-options {
      margin-top: 6px;
      padding-top: 6px;
      border-top: 1px solid var(--sys-color-outline-variant);

      .inline-links {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        font-size: 0.8125rem;

        .inline-link {
          background: none;
          border: none;
          color: var(--sys-color-primary);
          text-decoration: none;
          font-weight: 500;
          font-size: inherit;
          cursor: pointer;
          padding: 0;
          transition: all 0.2s ease;

          &:hover {
            color: var(--sys-color-primary);
            text-decoration: underline;
          }

          &:focus {
            outline: 2px solid var(--sys-color-primary);
            outline-offset: 2px;
            border-radius: 4px;
          }
        }

        .separator {
          color: var(--sys-color-on-surface-variant);
          font-weight: 300;
        }
      }

      .divider-with-text {
        text-align: center;
        margin-bottom: 8px;

        span {
          background-color: var(--sys-color-surface);
          padding: 0 16px;
          color: var(--sys-color-on-surface-variant);
          font-size: 0.875rem;
          font-weight: 500;
        }
      }

      .quick-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;

        .quick-action-link {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
          padding: 8px;
          border: 1px solid var(--sys-color-outline-variant);
          border-radius: 8px;
          background-color: transparent;
          color: var(--sys-color-on-surface);
          text-decoration: none;
          transition: all 0.3s ease;
          min-height: 45px;

          &:hover {
            background-color: var(--sys-color-surface-variant);
            border-color: var(--sys-color-primary);
            color: var(--sys-color-primary);
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
          }

          &:focus {
            outline: 2px solid var(--sys-color-primary);
            outline-offset: 2px;
          }

          &:active {
            transform: translateY(0);
            background-color: var(--sys-color-primary-container);
            color: var(--sys-color-on-primary-container);
          }

          mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }

          span {
            font-size: 0.75rem;
            font-weight: 500;
            text-align: center;
          }
        }
      }
    }
  }


.login-footer {
  background-color: var(--sys-color-surface);
  border-top: 1px solid var(--sys-color-outline-variant);
  padding: 8px;
  margin-top: auto;
  flex-shrink: 0;

  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    align-items: center;
    font-size: 0.875rem;

    .footer-section {
      display: flex;
      align-items: center;
      justify-content: center;

      @media (min-width: 768px) {
        &:first-child {
          justify-content: flex-start;
        }

        &:last-child {
          justify-content: flex-end;
        }
      }
    }

    .security-badge {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--sys-color-primary);
      font-weight: 500;

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }

    .support-info {
      color: var(--sys-color-on-surface-variant);
      text-align: center;

      .support-link {
        color: var(--sys-color-primary);
        text-decoration: none;
        font-weight: 500;
        margin-left: 8px;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .footer-links {
      display: flex;
      gap: 16px;
      align-items: center;

      .footer-link {
        color: var(--sys-color-on-surface-variant);
        text-decoration: none;
        font-weight: 400;
        transition: color 0.2s ease;

        &:hover {
          color: var(--sys-color-primary);
        }
      }

      .separator {
        color: var(--sys-color-outline);
      }
    }
  }
}

// Utility classes
.full-width {
  width: 100%;
}

.spinning {
  animation: spin 1s linear infinite;
}

// Responsive design
@media (max-width: 1024px) {
  .login-container .main-content .content-grid {
    gap: 32px;
  }
}

@media (max-width: 768px) {
  .login-container {
    .top-nav {
      padding: 12px 16px;

      .nav-content {
        .logo-brand {
          .brand-text {
            font-size: 1.125rem;
          }
        }

        .nav-controls {
          gap: 8px;

          .language-selector .lang-button {
            padding: 6px 12px;
            font-size: 0.875rem;
          }

          .theme-toggle {
            width: 40px;
            height: 40px;
          }
        }
      }
    }

    .main-content {
      padding: 8px 12px;

      .content-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        padding-top: 8px;

        .welcome-section {
          display: none; // Hide welcome section on mobile to save space
        }

        .login-section .login-card {
          padding: 20px;
          border-radius: 16px;
          max-height: calc(100vh - 160px);
        }
      }
    }
  }

  .login-footer .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 12px;

    .footer-section {
      justify-content: center !important;
    }

    .footer-links {
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  .login-container {
    .main-content {
      padding: 6px 8px;

      .content-grid {
        gap: 12px;
        padding-top: 6px;

        .user-type-section .user-type-grid {
          grid-template-columns: 1fr 1fr;
          gap: 6px;

          .user-type-card {
            padding: 10px 8px;
            min-height: 70px;

            .card-icon {
              width: 32px;
              height: 32px;
              margin-bottom: 6px;

              mat-icon {
                font-size: 20px;
                width: 20px;
                height: 20px;
              }
            }

            .card-content {
              .card-title {
                font-size: 0.8125rem;
              }

              .card-description {
                font-size: 0.625rem;
              }
            }
          }
        }

        .login-section .login-card {
          padding: 16px;
          max-height: calc(100vh - 120px);
        }

        .additional-options .quick-actions {
          grid-template-columns: 1fr;
          gap: 6px;

          .quick-action-link {
            min-height: 50px;
            padding: 10px;

            mat-icon {
              font-size: 20px;
              width: 20px;
              height: 20px;
            }

            span {
              font-size: 0.8125rem;
            }
          }
        }
      }
    }
  }
}
