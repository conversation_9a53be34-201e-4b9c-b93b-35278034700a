using Carter;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using School.API.Common;
using School.Application.Common.Models;
using School.Application.DTOs;
using School.Application.Features.Clubs;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace School.API.Endpoints;

public class ClubEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/clubs")
            .WithTags("Clubs");

        // Get all clubs with filtering and pagination
        group.MapGet("/", async (
            [AsParameters] ClubFilterDto filter,
            [FromServices] IClubService clubService,
            CancellationToken cancellationToken) =>
        {
            var result = await clubService.GetClubsAsync(filter, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk(result.Data, "Clubs retrieved successfully");
        })
        .WithName("GetClubs")
        .WithDescription("Get all clubs with filtering and pagination")
        .Produces<ApiResponse<PagedList<ClubDto>>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest);

        // Get club by ID
        group.MapGet("/{id}", async (
            [FromRoute] Guid id,
            [FromServices] IClubService clubService,
            CancellationToken cancellationToken) =>
        {
            var result = await clubService.GetClubByIdAsync(id, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiNotFound(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk(result.Data, "Club retrieved successfully");
        })
        .WithName("GetClubById")
        .WithDescription("Get a club by ID")
        .Produces<ApiResponse<ClubDto>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status404NotFound);

        // Create a new club
        group.MapPost("/", async (
            [FromBody] ClubCreateDto clubDto,
            [FromServices] IClubService clubService,
            CancellationToken cancellationToken) =>
        {
            var result = await clubService.CreateClubAsync(clubDto, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiCreated(result.Data, $"/api/clubs/{result.Data.Id}", "Club created successfully");
        })
        .WithName("CreateClub")
        .WithDescription("Create a new club")
        .Produces<ApiResponse<ClubDto>>(StatusCodes.Status201Created)
        .Produces(StatusCodes.Status400BadRequest);

        // Update an existing club
        group.MapPut("/{id}", async (
            [FromRoute] Guid id,
            [FromBody] ClubUpdateDto clubDto,
            [FromServices] IClubService clubService,
            CancellationToken cancellationToken) =>
        {
            var result = await clubService.UpdateClubAsync(id, clubDto, cancellationToken);
            if (!result.Success)
            {
                return result.Errors.Contains("Club not found")
                    ? ApiResults.ApiNotFound("Club not found")
                    : ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk(result.Data, "Club updated successfully");
        })
        .WithName("UpdateClub")
        .WithDescription("Update an existing club")
        .Produces<ApiResponse<ClubDto>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound);

        // Delete a club
        group.MapDelete("/{id}", async (
            [FromRoute] Guid id,
            [FromServices] IClubService clubService,
            CancellationToken cancellationToken) =>
        {
            var result = await clubService.DeleteClubAsync(id, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiNotFound(string.Join(", ", result.Errors));
            }
            return Results.NoContent();
        })
        .WithName("DeleteClub")
        .WithDescription("Delete a club")
        .Produces(StatusCodes.Status204NoContent)
        .Produces(StatusCodes.Status404NotFound);

        // Get featured clubs
        group.MapGet("/featured", async (
            [AsParameters] PaginationParams pagination,
            [FromServices] IClubService clubService,
            CancellationToken cancellationToken) =>
        {
            var result = await clubService.GetFeaturedClubsAsync(
                pagination.Page ?? 1,
                pagination.PageSize ?? 10,
                cancellationToken);

            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk(result.Data, "Featured clubs retrieved successfully");
        })
        .WithName("GetFeaturedClubs")
        .WithDescription("Get featured clubs")
        .Produces<ApiResponse<PagedList<ClubDto>>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest);

        // Get club categories
        group.MapGet("/categories", async (
            [FromServices] IClubService clubService,
            CancellationToken cancellationToken) =>
        {
            var result = await clubService.GetClubCategoriesAsync(cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk(result.Data, "Club categories retrieved successfully");
        })
        .WithName("GetClubCategories")
        .WithDescription("Get all club categories")
        .Produces<ApiResponse<List<string>>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest);
    }
}

public class PaginationParams
{
    public int? Page { get; set; }
    public int? PageSize { get; set; }
}
