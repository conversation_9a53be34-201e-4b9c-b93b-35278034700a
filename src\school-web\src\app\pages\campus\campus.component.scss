// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('/assets/images/campus-hero.jpg');
  background-size: cover;
  background-position: center;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  margin-bottom: 2rem;

  .hero-content {
    max-width: 800px;
    padding: 0 20px;

    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    .hero-description {
      font-size: 1.5rem;
      font-weight: 300;
    }
  }
}

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Section Styles
section {
  padding: $section-padding;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;
    
    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: $dark-gray;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Introduction Section
.intro-section {
  background-color: $white;
  
  .intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    
    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Features Section
.features-section {
  background-color: $light-gray;
  
  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
    
    .feature-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }
      
      .feature-image {
        height: 200px;
        overflow: hidden;
        position: relative;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;
          
          &:hover {
            transform: scale(1.05);
          }
        }
        
        .feature-icon {
          position: absolute;
          bottom: -25px;
          right: 20px;
          width: 50px;
          height: 50px;
          background-color: $primary-color;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: $box-shadow;
          
          mat-icon {
            color: $white;
            font-size: 25px;
            height: 25px;
            width: 25px;
          }
        }
      }
      
      mat-card-content {
        padding: 30px 20px 20px;
        flex-grow: 1;
        
        h3 {
          font-size: 1.5rem;
          margin-bottom: 15px;
          color: $text-color;
        }
        
        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
      
      mat-card-actions {
        padding: 0 20px 20px;
        
        a {
          display: flex;
          align-items: center;
          
          mat-icon {
            margin-left: 5px;
          }
        }
      }
    }
  }
}

// Highlights Section
.highlights-section {
  background-color: $white;
  
  .highlights-container {
    margin-top: 40px;
    
    .highlight-item {
      display: flex;
      align-items: center;
      margin-bottom: 60px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      &.highlight-reverse {
        flex-direction: row-reverse;
        
        .highlight-image {
          margin-right: 0;
          margin-left: 40px;
        }
      }
      
      .highlight-image {
        flex: 1;
        border-radius: $border-radius;
        overflow: hidden;
        box-shadow: $box-shadow;
        margin-right: 40px;
        
        img {
          width: 100%;
          height: auto;
          display: block;
          transition: transform 0.5s;
          
          &:hover {
            transform: scale(1.05);
          }
        }
      }
      
      .highlight-content {
        flex: 1;
        
        h3 {
          font-size: 2rem;
          margin-bottom: 20px;
          color: $text-color;
          position: relative;
          padding-bottom: 15px;
          
          &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 3px;
            background-color: $primary-color;
          }
        }
        
        p {
          font-size: 1.1rem;
          line-height: 1.6;
          color: $dark-gray;
        }
      }
    }
  }
}

// Events Section
.events-section {
  background-color: $light-gray;
  
  .events-container {
    margin-top: 40px;
    
    .event-card {
      display: flex;
      background-color: $white;
      border-radius: $border-radius;
      box-shadow: $box-shadow;
      margin-bottom: 20px;
      overflow: hidden;
      transition: transform 0.3s, box-shadow 0.3s;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }
      
      .event-date {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: $primary-color;
        color: $white;
        padding: 20px;
        min-width: 100px;
        text-align: center;
        
        .month-day {
          font-size: 1.2rem;
          font-weight: 500;
          margin-bottom: 5px;
        }
        
        .year {
          font-size: 1rem;
        }
      }
      
      .event-details {
        padding: 20px;
        flex-grow: 1;
        
        h3 {
          font-size: 1.5rem;
          margin-bottom: 15px;
          color: $text-color;
        }
        
        .event-info {
          display: flex;
          margin-bottom: 15px;
          
          .info-item {
            display: flex;
            align-items: center;
            margin-right: 20px;
            
            &:last-child {
              margin-right: 0;
            }
            
            mat-icon {
              color: $primary-color;
              margin-right: 5px;
              font-size: 18px;
              height: 18px;
              width: 18px;
            }
            
            span {
              color: $dark-gray;
              font-size: 0.9rem;
            }
          }
        }
        
        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }
  
  .events-cta {
    text-align: center;
    margin-top: 40px;
    
    a {
      padding: 10px 30px;
      font-size: 1.1rem;
    }
  }
}

// Testimonials Section
.testimonials-section {
  background-color: $white;
  
  .testimonials-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    margin-top: 40px;
    
    .testimonial-card {
      background-color: $light-gray;
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      width: 100%;
      max-width: 350px;
      transition: transform 0.3s, box-shadow 0.3s;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }
      
      .testimonial-image {
        height: 200px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .testimonial-content {
        padding: 30px;
        position: relative;
        
        .quote-icon {
          position: absolute;
          top: -25px;
          left: 30px;
          width: 50px;
          height: 50px;
          background-color: $primary-color;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: $box-shadow;
          
          mat-icon {
            color: $white;
            font-size: 25px;
            height: 25px;
            width: 25px;
          }
        }
        
        .testimonial-quote {
          font-style: italic;
          color: $text-color;
          line-height: 1.6;
          margin-bottom: 20px;
        }
        
        .testimonial-student {
          color: $primary-color;
          font-weight: 500;
          margin-bottom: 0;
        }
      }
    }
  }
}

// Tour Section
.tour-section {
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('/assets/images/campus-tour.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  color: $white;
  
  .tour-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    
    h2 {
      color: $white;
      
      &:after {
        background-color: $white;
      }
    }
    
    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
    }
    
    .tour-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
      
      a {
        padding: 10px 30px;
        font-size: 1.1rem;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .hero-section {
    height: 350px;
    
    .hero-content h1 {
      font-size: 2.5rem;
    }
  }
  
  section {
    padding: 60px 0;
    
    h2 {
      font-size: 2rem;
    }
  }
  
  .features-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  .highlight-item, .highlight-item.highlight-reverse {
    flex-direction: column;
    
    .highlight-image {
      margin-right: 0;
      margin-left: 0;
      margin-bottom: 30px;
      width: 100%;
    }
    
    .highlight-content {
      width: 100%;
    }
  }
  
  .testimonials-container {
    flex-direction: column;
    align-items: center;
    
    .testimonial-card {
      max-width: 100%;
    }
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 300px;
    
    .hero-content {
      h1 {
        font-size: 2rem;
      }
      
      .hero-description {
        font-size: 1.2rem;
      }
    }
  }
  
  .event-card {
    flex-direction: column;
    
    .event-date {
      padding: 10px;
      flex-direction: row;
      justify-content: center;
      
      .month-day {
        margin-bottom: 0;
        margin-right: 5px;
      }
    }
  }
  
  .tour-buttons {
    flex-direction: column;
    align-items: center;
    
    a {
      width: 100%;
      max-width: 300px;
      margin-bottom: 15px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

@media (max-width: 576px) {
  .hero-section {
    height: 250px;
    
    .hero-content h1 {
      font-size: 1.8rem;
    }
  }
  
  section h2 {
    font-size: 1.8rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}
