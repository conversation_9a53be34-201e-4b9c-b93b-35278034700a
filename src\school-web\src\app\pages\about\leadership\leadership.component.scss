@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section
.hero-section {
  // Global hero styles are applied from _hero.scss
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('/assets/images/leadership-hero.jpg');
  margin-bottom: 2rem;

  .hero-content {
    max-width: 800px;
    padding: 0 20px;

    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    .hero-description {
      font-size: 1.5rem;
      font-weight: 300;
    }
  }
}

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Introduction Section
.intro-section {
  padding: $section-padding;

  .intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h2 {
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
      color: $text-color;
      position: relative;
      padding-bottom: 0.5rem;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background-color: $primary-color;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Leadership Tabs Section
.leadership-tabs-section {
  padding-bottom: $section-padding;

  ::ng-deep .mat-mdc-tab-header {
    margin-bottom: 40px;
  }

  ::ng-deep .mat-mdc-tab-label-content {
    font-size: 1.1rem;
    font-weight: 500;
  }

  .tab-content {
    padding: 20px;

    h2 {
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
      color: $text-color;
      text-align: center;
      position: relative;
      padding-bottom: 0.5rem;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background-color: $primary-color;
      }
    }

    .tab-description {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 40px;
      color: $dark-gray;
      text-align: center;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }
  }

  // Executive Team Styles
  .leadership-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;

    .leadership-card {
      display: flex;
      flex-direction: row;
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;

      @media (max-width: 992px) {
        flex-direction: column;
      }

      .leader-image {
        width: 300px;
        flex-shrink: 0;

        @media (max-width: 992px) {
          width: 100%;
          height: 300px;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      mat-card-content {
        padding: 30px;
        flex-grow: 1;

        h3 {
          font-size: 1.8rem;
          margin-bottom: 5px;
          color: $text-color;
        }

        h4 {
          font-size: 1.3rem;
          margin-bottom: 20px;
          color: $primary-color;
          font-weight: 500;
        }

        .leader-bio {
          font-size: 1.1rem;
          line-height: 1.6;
          margin-bottom: 30px;
          color: $text-color;
        }

        .leader-details {
          .detail-section {
            margin-bottom: 25px;

            &:last-child {
              margin-bottom: 0;
            }

            h5 {
              font-size: 1.2rem;
              margin-bottom: 10px;
              color: $text-color;
              font-weight: 500;
              border-bottom: 1px solid $medium-gray;
              padding-bottom: 5px;
            }

            ul {
              padding-left: 20px;
              margin-bottom: 0;

              li {
                margin-bottom: 5px;
                color: $dark-gray;

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }

            .contact-info {
              .contact-item {
                display: flex;
                align-items: center;
                margin-bottom: 10px;

                &:last-child {
                  margin-bottom: 0;
                }

                mat-icon {
                  color: $primary-color;
                  margin-right: 10px;
                  font-size: 20px;
                  height: 20px;
                  width: 20px;
                }

                span {
                  color: $dark-gray;
                }
              }
            }

            .social-links {
              display: flex;
              margin-top: 15px;

              .social-link {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 40px;
                background-color: $primary-color;
                color: $white;
                border-radius: 50%;
                margin-right: 10px;
                text-decoration: none;
                transition: background-color 0.3s;

                &:hover {
                  background-color: color.adjust($primary-color, $lightness: -10%);
                }

                mat-icon {
                  font-size: 20px;
                  height: 20px;
                  width: 20px;
                }
              }
            }
          }
        }
      }
    }
  }

  // Board of Trustees Styles
  .board-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;

    .board-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      height: 100%;
      display: flex;
      flex-direction: column;
      transition: transform 0.3s, box-shadow 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .board-image {
        height: 250px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 5px;
          color: $text-color;
        }

        h4 {
          font-size: 1.1rem;
          margin-bottom: 5px;
          color: $primary-color;
          font-weight: 500;
        }

        .profession {
          font-size: 0.9rem;
          color: $dark-gray;
          margin-bottom: 15px;
          font-style: italic;
        }

        .board-bio {
          color: $text-color;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }

  // Organizational Structure Styles
  .structure-diagram {
    max-width: 800px;
    margin: 0 auto;

    .structure-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 30px;
      background-color: $light-gray;
      border-radius: $border-radius;
      padding: 20px;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .structure-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        background-color: $primary-color;
        color: $white;
        border-radius: 50%;
        font-size: 1.5rem;
        font-weight: bold;
        margin-right: 20px;
        flex-shrink: 0;
      }

      .structure-content {
        flex-grow: 1;

        h3 {
          font-size: 1.3rem;
          margin-bottom: 10px;
          color: $text-color;
        }

        p {
          margin-bottom: 0;
          color: $dark-gray;
          line-height: 1.6;
        }
      }
    }
  }
}

// Principal Message Section
.principal-message-section {
  padding: $section-padding;
  background-color: $light-gray;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 40px;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .message-content {
    display: flex;
    align-items: flex-start;
    background-color: $white;
    border-radius: $border-radius;
    overflow: hidden;
    box-shadow: $box-shadow;

    @media (max-width: 992px) {
      flex-direction: column;
    }

    .principal-image {
      width: 300px;
      flex-shrink: 0;

      @media (max-width: 992px) {
        width: 100%;
        height: 300px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .message-text {
      padding: 30px;
      flex-grow: 1;

      h3 {
        font-size: 1.8rem;
        margin-bottom: 20px;
        color: $text-color;
      }

      p {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 20px;
        color: $text-color;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .signature {
        margin-top: 30px;

        p {
          margin-bottom: 5px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .principal-name {
          font-size: 1.3rem;
          font-weight: 500;
          color: $text-color;
        }
      }
    }
  }
}

// Contact Section
.contact-section {
  padding: $section-padding;
  background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -15%));
  color: $white;
  text-align: center;

  .contact-content {
    max-width: 800px;
    margin: 0 auto;

    h2 {
      font-size: 2.5rem;
      margin-bottom: 20px;
      color: $white;
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
    }

    a {
      padding: 10px 30px;
      font-size: 1.1rem;
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .hero-section {
    height: 350px;

    .hero-content h1 {
      font-size: 2.5rem;
    }
  }

  .intro-section, .leadership-tabs-section, .principal-message-section, .contact-section {
    padding: 60px 0;

    h2 {
      font-size: 2rem;
    }
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 300px;

    .hero-content {
      h1 {
        font-size: 2rem;
      }

      .hero-description {
        font-size: 1.2rem;
      }
    }
  }

  .structure-item {
    flex-direction: column;

    .structure-number {
      margin-right: 0;
      margin-bottom: 15px;
    }
  }
}

@media (max-width: 576px) {
  .hero-section {
    height: 250px;

    .hero-content h1 {
      font-size: 1.8rem;
    }
  }

  .intro-section, .leadership-tabs-section, .principal-message-section, .contact-section {
    h2 {
      font-size: 1.8rem;
    }
  }
}
