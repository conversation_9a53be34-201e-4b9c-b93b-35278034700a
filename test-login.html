<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Login API Test</h1>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" value="admin" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="Admin@123456" required>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="rememberMe" checked> Remember Me
            </label>
        </div>
        
        <button type="submit">Test Login</button>
        <button type="button" onclick="clearResult()">Clear</button>
    </form>
    
    <div id="result"></div>

    <script>
        const API_URL = 'https://localhost:7130/api';
        
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing login...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        rememberMe: rememberMe
                    })
                });
                
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = responseText;
                }
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: responseData
                };
                
                resultDiv.innerHTML = `Response:\n${JSON.stringify(result, null, 2)}`;
                resultDiv.className = response.ok ? 'result success' : 'result error';
                
            } catch (error) {
                resultDiv.innerHTML = `Error:\n${error.message}\n\nStack:\n${error.stack}`;
                resultDiv.className = 'result error';
            }
        });
        
        function clearResult() {
            document.getElementById('result').innerHTML = '';
            document.getElementById('result').className = '';
        }
    </script>
</body>
</html>
