import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule } from '@ngx-translate/core';
import { EnhancedHeroComponent } from '../../shared/components/enhanced-hero/enhanced-hero.component';

interface Program {
  id: string;
  title: string;
  description: string;
  grades: string;
  features: string[];
  image: string;
}

interface Course {
  id: string;
  title: string;
  description: string;
  grade: string;
  department: string;
}

@Component({
  selector: 'app-academics',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTabsModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatExpansionModule,
    TranslateModule,
    EnhancedHeroComponent
  ],
  templateUrl: './academics.component.html',
  styleUrl: './academics.component.scss'
})
export class AcademicsComponent implements OnInit {
  activeSection: string = '';
  showFloatingNav: boolean = false;
  selectedTabIndex: number = 0;

  // Academic Programs
  programs: Program[] = [
    {
      id: 'elementary',
      title: 'Elementary School',
      description: 'Our elementary program focuses on building a strong foundation in core subjects while nurturing curiosity and creativity. Students develop fundamental skills in literacy, numeracy, science, and social studies through engaging, hands-on activities.',
      grades: 'Kindergarten - Grade 5',
      features: [
        'Balanced literacy approach with phonics instruction',
        'Singapore Math curriculum',
        'Inquiry-based science program',
        'Social-emotional learning integration',
        'Arts and music education',
        'Physical education and outdoor play'
      ],
      image: 'https://via.placeholder.com/600x400?text=Elementary+School'
    },
    {
      id: 'middle',
      title: 'Middle School',
      description: 'Our middle school program is designed to support students through the transition from childhood to adolescence. We provide a challenging academic environment while addressing the unique developmental needs of this age group.',
      grades: 'Grades 6-8',
      features: [
        'Departmentalized instruction with subject specialists',
        'Advanced math pathways',
        'Integrated science curriculum',
        'Foreign language options',
        'Digital literacy and technology skills',
        'Advisory program for social-emotional support'
      ],
      image: 'https://via.placeholder.com/600x400?text=Middle+School'
    },
    {
      id: 'high',
      title: 'High School',
      description: 'Our high school program prepares students for college and beyond through rigorous academics, diverse electives, and opportunities for leadership and personal growth. We emphasize critical thinking, communication, and real-world application of knowledge.',
      grades: 'Grades 9-12',
      features: [
        'College preparatory curriculum',
        'Advanced Placement (AP) courses',
        'Honors tracks in core subjects',
        'Comprehensive elective offerings',
        'College counseling services',
        'Internship and community service opportunities'
      ],
      image: 'https://via.placeholder.com/600x400?text=High+School'
    }
  ];

  // Special Programs
  specialPrograms = [
    {
      title: 'STEM Innovation Lab',
      description: 'A dedicated space for hands-on science, technology, engineering, and mathematics projects. Students engage in design thinking, coding, robotics, and scientific inquiry.',
      icon: 'science'
    },
    {
      title: 'Global Languages Program',
      description: 'Comprehensive language instruction in Spanish, French, and Mandarin Chinese, with cultural immersion experiences and international exchange opportunities.',
      icon: 'language'
    },
    {
      title: 'Arts Academy',
      description: 'Specialized instruction in visual arts, music, theater, and dance, with opportunities for performances, exhibitions, and collaboration with professional artists.',
      icon: 'palette'
    },
    {
      title: 'Gifted & Talented Program',
      description: 'Enrichment and acceleration opportunities for students demonstrating exceptional abilities, with personalized learning plans and advanced project work.',
      icon: 'psychology'
    }
  ];

  // Sample Courses
  courses: Course[] = [
    {
      id: 'math-101',
      title: 'Advanced Mathematics',
      description: 'This course covers advanced algebraic concepts, trigonometry, and pre-calculus fundamentals to prepare students for college-level mathematics.',
      grade: 'High School',
      department: 'Mathematics'
    },
    {
      id: 'sci-201',
      title: 'Environmental Science',
      description: 'Students explore ecological principles, environmental challenges, and sustainability solutions through laboratory work and field studies.',
      grade: 'High School',
      department: 'Science'
    },
    {
      id: 'eng-301',
      title: 'World Literature',
      description: 'This course examines literary masterpieces from diverse cultures and historical periods, developing critical reading and analytical writing skills.',
      grade: 'High School',
      department: 'English'
    },
    {
      id: 'hist-101',
      title: 'Global Perspectives',
      description: 'Students analyze historical events and contemporary issues from multiple viewpoints, developing research skills and cultural understanding.',
      grade: 'Middle School',
      department: 'Social Studies'
    },
    {
      id: 'art-201',
      title: 'Digital Media Arts',
      description: 'This course combines traditional art principles with digital tools, teaching photography, graphic design, and digital illustration.',
      grade: 'High School',
      department: 'Arts'
    },
    {
      id: 'tech-101',
      title: 'Computer Science Fundamentals',
      description: 'Students learn programming concepts, computational thinking, and app development through project-based learning.',
      grade: 'Middle School',
      department: 'Technology'
    }
  ];

  constructor() { }

  ngOnInit(): void {
    // Initialize the component
  }

  @HostListener('window:scroll', [])
  onWindowScroll() {
    // Show floating nav after scrolling past the hero section
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
    this.showFloatingNav = scrollPosition > 300;

    // Determine active section based on scroll position
    this.updateActiveSection();
  }

  updateActiveSection(): void {
    const sections = ['programs', 'special-programs', 'curriculum', 'resources'];

    for (const section of sections) {
      const element = document.getElementById(section);
      if (element) {
        const rect = element.getBoundingClientRect();
        if (rect.top <= 100 && rect.bottom >= 100) {
          this.activeSection = section;
          break;
        }
      }
    }
  }

  scrollToSection(sectionId: string): void {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  setSelectedTab(index: number): void {
    this.selectedTabIndex = index;
  }
}
