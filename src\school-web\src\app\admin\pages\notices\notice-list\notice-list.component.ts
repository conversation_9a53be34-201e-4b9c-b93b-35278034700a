import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatTableModule, MatTable } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { NoticeService } from '../../../../core/services/notice.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';
import { Notice, NoticeFilter } from '../../../../core/models/notice.model';

@Component({
  selector: 'app-notice-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './notice-list.component.html',
  styleUrls: ['./notice-list.component.scss']
})
export class NoticeListComponent implements OnInit {
  notices: Notice[] = [];
  displayedColumns: string[] = ['title', 'category', 'startDate', 'endDate', 'priority', 'isActive', 'actions'];
  totalCount = 0;
  isLoading = false;
  error = false;

  // Filter properties
  filter: NoticeFilter & { isActive: boolean | string | undefined } = {
    page: 1,
    pageSize: 10,
    search: '',
    category: '',
    isActive: undefined
  };

  categories = [
    { value: '', label: 'All Categories' },
    { value: 'Academic', label: 'Academic' },
    { value: 'Admission', label: 'Admission' },
    { value: 'Event', label: 'Event' },
    { value: 'Exam', label: 'Exam' },
    { value: 'Holiday', label: 'Holiday' },
    { value: 'General', label: 'General' }
  ];

  activeOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'true', label: 'Active' },
    { value: 'false', label: 'Inactive' }
  ];

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(MatTable) table!: MatTable<Notice>;

  constructor(
    private noticeService: NoticeService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadNotices();
  }

  loadNotices(): void {
    this.isLoading = true;
    this.error = false;

    // Convert isActive string to boolean if defined
    if (this.filter.isActive !== undefined) {
      const activeValue = this.filter.isActive;
      if (typeof activeValue === 'string') {
        if (activeValue !== '') {
          this.filter.isActive = activeValue === 'true';
        } else {
          this.filter.isActive = undefined;
        }
      }
    }

    this.noticeService.getAllNotices(this.filter).subscribe({
      next: (response) => {
        this.notices = response.items;
        this.totalCount = response.totalCount;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading notices:', error);
        this.error = true;
        this.isLoading = false;
        this.snackBar.open('Failed to load notices', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  applyFilter(): void {
    this.filter.page = 1; // Reset to first page when filtering
    this.loadNotices();
  }

  resetFilter(): void {
    this.filter = {
      page: 1,
      pageSize: 10,
      search: '',
      category: '',
      isActive: undefined
    } as NoticeFilter & { isActive: boolean | string | undefined };
    this.loadNotices();
  }

  onPageChange(event: any): void {
    this.filter.page = event.pageIndex + 1;
    this.filter.pageSize = event.pageSize;
    this.loadNotices();
  }

  deleteNotice(notice: Notice): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Notice',
        message: `Are you sure you want to delete the notice "${notice.title}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.noticeService.deleteNotice(notice.id).subscribe({
          next: () => {
            this.snackBar.open('Notice deleted successfully', 'Close', {
              duration: 3000,
              panelClass: ['success-snackbar']
            });
            this.loadNotices();
          },
          error: (error) => {
            console.error('Error deleting notice:', error);
            this.snackBar.open('Failed to delete notice', 'Close', {
              duration: 3000,
              panelClass: ['error-snackbar']
            });
          }
        });
      }
    });
  }

  getPriorityLabel(priority: number): string {
    switch (priority) {
      case 1: return 'Low';
      case 2: return 'Medium';
      case 3: return 'High';
      case 4: return 'Urgent';
      default: return 'Unknown';
    }
  }

  getPriorityClass(priority: number): string {
    switch (priority) {
      case 1: return 'priority-low';
      case 2: return 'priority-medium';
      case 3: return 'priority-high';
      case 4: return 'priority-urgent';
      default: return '';
    }
  }
}
