@use '../../../styles/portal-theme' as portal;

// Apply consistent portal theme
:host {
  @include portal.apply-portal-theme;
}

.staff-portal-container {
  @include portal.portal-base-layout;

  .portal-header {
    @include portal.portal-header-base;
    background: linear-gradient(135deg, var(--portal-primary) 0%, var(--portal-secondary) 100%);
    color: var(--portal-on-primary);
    padding: 1rem 2rem;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;

      .user-info {
        display: flex;
        align-items: center;
        gap: 1rem;

        .avatar {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;

          mat-icon {
            font-size: 24px;
            width: 24px;
            height: 24px;
          }
        }

        .user-details {
          .user-name {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
          }

          .user-role {
            font-size: 0.875rem;
            opacity: 0.9;
            margin: 0;
          }
        }
      }

      .header-actions {
        button {
          color: white;
        }
      }
    }
  }

  .portal-content {
    flex: 1;
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;

    .sidebar {
      @include portal.portal-sidebar-base;
      width: 280px;
      padding: 1.5rem 0;
      overflow-y: auto;

      .nav-menu {
        .nav-section {
          margin-bottom: 2rem;

          .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--sys-color-on-surface-variant);
            margin: 0 1.5rem 0.75rem;
            letter-spacing: 0.5px;
          }

          .nav-item {
            width: 100%;
            height: 48px;
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0 1.5rem;
            color: var(--portal-sidebar-text);
            text-decoration: none;
            border-radius: 0;
            justify-content: flex-start;
            font-weight: 500;
            transition: all 0.2s ease;

            &:hover {
              background: var(--portal-sidebar-hover-bg);
            }

            &.active {
              background: var(--portal-sidebar-active-bg);
              color: var(--portal-sidebar-active-text);
              border-right: 3px solid var(--portal-primary);

              mat-icon {
                color: var(--portal-primary);
              }
            }

            mat-icon {
              font-size: 20px;
              width: 20px;
              height: 20px;
              color: var(--sys-color-on-surface-variant);
            }

            span {
              font-size: 0.875rem;
            }
          }
        }
      }
    }

    .main-content {
      @include portal.portal-content-base;
      flex: 1;
      padding: 2rem;
      overflow-y: auto;

      .content-section {
        .section-header {
          margin-bottom: 2rem;

          h2 {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--sys-color-on-surface);
            margin: 0 0 0.5rem;
          }

          p {
            color: var(--sys-color-on-surface-variant);
            margin: 0;
          }
        }

        .dashboard-cards {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 1.5rem;

          .dashboard-card {
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            }

            mat-card-header {
              mat-icon[mat-card-avatar] {
                background: var(--sys-color-primary-container);
                color: var(--sys-color-on-primary-container);
                font-size: 24px;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              mat-card-title {
                font-size: 1rem;
                font-weight: 600;
              }
            }

            mat-card-content {
              .card-stat {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
                padding: 1rem 0;

                .stat-number {
                  font-size: 2.5rem;
                  font-weight: 700;
                  color: var(--sys-color-primary);
                  line-height: 1;
                }

                .stat-label {
                  font-size: 0.875rem;
                  color: var(--sys-color-on-surface-variant);
                  margin-top: 0.5rem;
                }
              }
            }

            mat-card-actions {
              padding: 0 16px 16px;

              button {
                color: var(--sys-color-primary);
                font-weight: 500;
              }
            }
          }
        }

        .section-content {
          .coming-soon {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--sys-color-on-surface-variant);

            mat-icon {
              font-size: 4rem;
              width: 4rem;
              height: 4rem;
              margin-bottom: 1rem;
              opacity: 0.5;
            }

            h3 {
              font-size: 1.5rem;
              font-weight: 600;
              margin: 0 0 0.5rem;
            }

            p {
              font-size: 1rem;
              margin: 0;
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 1024px) {
  .staff-portal-container {
    .portal-content {
      .sidebar {
        width: 240px;
      }

      .main-content {
        padding: 1.5rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .staff-portal-container {
    .portal-header {
      padding: 1rem;

      .header-content {
        .user-info {
          .avatar {
            width: 40px;
            height: 40px;

            mat-icon {
              font-size: 20px;
              width: 20px;
              height: 20px;
            }
          }

          .user-details {
            .user-name {
              font-size: 1.125rem;
            }

            .user-role {
              font-size: 0.8125rem;
            }
          }
        }
      }
    }

    .portal-content {
      flex-direction: column;

      .sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--sys-color-outline-variant);
        padding: 1rem 0;

        .nav-menu {
          display: flex;
          overflow-x: auto;
          gap: 1rem;
          padding: 0 1rem;

          .nav-section {
            margin-bottom: 0;
            min-width: max-content;

            .nav-section-title {
              display: none;
            }

            .nav-item {
              width: auto;
              min-width: 120px;
              padding: 0.75rem 1rem;
              border-radius: 8px;
              flex-direction: column;
              gap: 0.25rem;
              height: auto;

              &.active {
                border-right: none;
                border-bottom: 3px solid var(--sys-color-primary);
              }

              mat-icon {
                font-size: 18px;
                width: 18px;
                height: 18px;
              }

              span {
                font-size: 0.75rem;
              }
            }
          }
        }
      }

      .main-content {
        padding: 1rem;

        .content-section {
          .section-header {
            margin-bottom: 1.5rem;

            h2 {
              font-size: 1.5rem;
            }
          }

          .dashboard-cards {
            grid-template-columns: 1fr;
            gap: 1rem;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .staff-portal-container {
    .portal-header {
      .header-content {
        .user-info {
          gap: 0.75rem;

          .user-details {
            .user-name {
              font-size: 1rem;
            }
          }
        }
      }
    }

    .portal-content {
      .main-content {
        padding: 0.75rem;

        .content-section {
          .section-header {
            h2 {
              font-size: 1.25rem;
            }
          }
        }
      }
    }
  }
}
