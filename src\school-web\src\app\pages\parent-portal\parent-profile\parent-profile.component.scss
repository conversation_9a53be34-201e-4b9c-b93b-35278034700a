.profile-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.profile-content {
  max-width: 800px;
  margin: 0 auto;
}

.profile-card {
  margin-bottom: 24px;
}

.profile-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  mat-icon {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #757575;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;

  &.full-width {
    grid-column: span 2;
  }
}

.info-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
}

.associated-students {
  margin-top: 32px;

  h3 {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #333;
  }
}

.students-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.student-card {
  margin-bottom: 16px;
}

.student-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  mat-icon {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #757575;
  }
}

.relation-info {
  display: flex;
  gap: 24px;
  margin-top: 8px;
}

.relation-type,
.primary-contact {
  display: flex;
  flex-direction: column;
}

.relation-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.relation-value {
  font-size: 16px;
  font-weight: 500;
}

.no-students {
  text-align: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.6);
}

mat-card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px 16px;
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-item.full-width {
    grid-column: span 1;
  }

  .relation-info {
    flex-direction: column;
    gap: 8px;
  }
}
