using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Faculty;
using School.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Services
{
    public class FacultyService : IFacultyService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<FacultyService> _logger;

        public FacultyService(
            IUnitOfWork unitOfWork,
            ICurrentUserService currentUserService,
            ILogger<FacultyService> logger)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        public async Task<(IEnumerable<FacultyDto> Faculties, int TotalCount)> GetAllFacultiesAsync(FacultyFilterDto filter)
        {
            _logger.LogInformation("Getting all faculties with filter: {Filter}", new { filter.Name, filter.Department, filter.IsActive, filter.Page, filter.PageSize });

            var repository = _unitOfWork.Repository<Faculty>();
            var query = repository.AsQueryable("Translations", "Specializations", "Education", "Courses", "Publications", "Awards", "ProfileImage");

            // Apply filters
            if (!string.IsNullOrEmpty(filter.Name))
            {
                query = query.Where(f => f.Name.Contains(filter.Name));
            }

            if (!string.IsNullOrEmpty(filter.Department))
            {
                query = query.Where(f => f.Department.Contains(filter.Department));
            }

            if (filter.IsActive.HasValue)
            {
                query = query.Where(f => f.IsActive == filter.IsActive.Value);
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Get paginated results
            var faculties = await query
                .OrderBy(f => f.Name)
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(f => new FacultyDto
                {
                    Id = f.Id,
                    Name = f.Name,
                    Title = f.Title,
                    Email = f.Email,
                    Phone = f.Phone,
                    Office = f.Office,
                    Department = f.Department,
                    JoinedYear = f.JoinedYear,
                    IsFeatured = f.IsFeatured,
                    IsActive = f.IsActive,
                    DisplayOrder = f.DisplayOrder,
                    Website = f.Website,
                    LinkedIn = f.LinkedIn,
                    Twitter = f.Twitter,
                    ResearchGate = f.ResearchGate,
                    ProfileImageId = f.ProfileImageId,
                    CreatedAt = f.CreatedAt,
                    UpdatedAt = f.UpdatedAt
                })
                .ToListAsync();

            _logger.LogInformation("Retrieved {Count} faculties out of {TotalCount}", faculties.Count(), totalCount);
            return (faculties, totalCount);
        }

        public async Task<FacultyDto?> GetFacultyByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting faculty by ID: {FacultyId}", id);

            var repository = _unitOfWork.Repository<Faculty>();
            var faculty = await repository.GetByIdAsync(id, new[] {
                "Translations",
                "Specializations",
                "Education",
                "Courses",
                "Publications",
                "Awards"
            });

            if (faculty == null)
            {
                _logger.LogWarning("Faculty with ID {FacultyId} not found", id);
                return null;
            }

            return new FacultyDto
            {
                Id = faculty.Id,
                Name = faculty.Name,
                Title = faculty.Title,
                Email = faculty.Email,
                Phone = faculty.Phone,
                Office = faculty.Office,
                Biography = faculty.Biography,
                ShortBio = faculty.ShortBio,
                Department = faculty.Department,
                JoinedYear = faculty.JoinedYear,
                IsFeatured = faculty.IsFeatured,
                IsActive = faculty.IsActive,
                DisplayOrder = faculty.DisplayOrder,
                Website = faculty.Website,
                LinkedIn = faculty.LinkedIn,
                Twitter = faculty.Twitter,
                ResearchGate = faculty.ResearchGate,
                ProfileImageId = faculty.ProfileImageId,
                CreatedAt = faculty.CreatedAt,
                UpdatedAt = faculty.UpdatedAt,
                Education = faculty.Education?.Select(e => new FacultyEducationDto
                {
                    Id = e.Id,
                    Degree = e.Degree,
                    Institution = e.Institution,
                    Year = e.Year,
                    Major = e.Major
                }).ToList(),
                Specializations = faculty.Specializations?.Select(s => new FacultySpecializationDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Description = s.Description
                }).ToList(),
                Courses = faculty.Courses?.Select(c => new FacultyCourseDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Code = c.Code,
                    Description = c.Description
                }).ToList(),
                Publications = faculty.Publications?.Select(p => new FacultyPublicationDto
                {
                    Id = p.Id,
                    Title = p.Title,
                    Journal = p.Journal,
                    Year = p.Year,
                    Url = p.Url
                }).ToList(),
                Awards = faculty.Awards?.Select(a => new FacultyAwardDto
                {
                    Id = a.Id,
                    Name = a.Name,
                    Year = a.Year,
                    Description = a.Description
                }).ToList()
            };
        }

        public async Task<Guid> CreateFacultyAsync(FacultyCreateDto facultyDto)
        {
            _logger.LogInformation("Creating new faculty with name: {Name}", $"{facultyDto.Name} {facultyDto.Name}");

            try
            {
                var repository = _unitOfWork.Repository<Faculty>();

                // Check if email already exists
                if (!string.IsNullOrEmpty(facultyDto.Email))
                {
                    var existingFaculty = await repository.FindAsync(f => f.Email == facultyDto.Email);
                    if (existingFaculty.Any())
                    {
                        _logger.LogWarning("Faculty with email {Email} already exists", facultyDto.Email);
                        throw new InvalidOperationException($"Faculty with email '{facultyDto.Email}' already exists");
                    }
                }

                var faculty = new Faculty
                {
                    Name = facultyDto.Name,
                    Title = facultyDto.Title,
                    Email = facultyDto.Email,
                    Phone = facultyDto.Phone,
                    Office = facultyDto.Office,
                    Biography = facultyDto.Biography,
                    ShortBio = facultyDto.ShortBio,
                    Department = facultyDto.Department,
                    JoinedYear = facultyDto.JoinedYear,
                    IsFeatured = facultyDto.IsFeatured,
                    IsActive = facultyDto.IsActive,
                    DisplayOrder = facultyDto.DisplayOrder,
                    Website = facultyDto.Website,
                    LinkedIn = facultyDto.LinkedIn,
                    Twitter = facultyDto.Twitter,
                    ResearchGate = facultyDto.ResearchGate,
                    ProfileImageId = facultyDto.ProfileImageId
                    // CreatedAt and CreatedBy will be set by the repository
                };

                // Add translations if provided
                if (facultyDto.Translations != null && facultyDto.Translations.Any())
                {
                    faculty.Translations = facultyDto.Translations.Select(t => new FacultyTranslation
                    {
                        LanguageCode = t.LanguageCode,
                        Name = t.Name,
                        Title = t.Title,
                        Biography = t.Biography,
                        ShortBio = t.ShortBio
                        // CreatedAt and CreatedBy will be set by the repository
                    }).ToList();
                }

                // Add education if provided
                if (facultyDto.Education != null && facultyDto.Education.Any())
                {
                    faculty.Education = facultyDto.Education.Select(e => new FacultyEducation
                    {
                        Degree = e.Degree,
                        Institution = e.Institution,
                        Year = e.Year,
                        DisplayOrder = e.DisplayOrder
                        // CreatedAt and CreatedBy will be set by the repository
                    }).ToList();
                }

                // Add specializations if provided
                if (facultyDto.Specializations != null && facultyDto.Specializations.Any())
                {
                    faculty.Specializations = facultyDto.Specializations.Select(s => new FacultySpecialization
                    {
                        Name = s.Name,
                        DisplayOrder = s.DisplayOrder
                        // CreatedAt and CreatedBy will be set by the repository
                    }).ToList();
                }

                // Add courses if provided
                if (facultyDto.Courses != null && facultyDto.Courses.Any())
                {
                    faculty.Courses = facultyDto.Courses.Select(c => new FacultyCourse
                    {
                        Name = c.Name,
                        Description = c.Description,
                        DisplayOrder = c.DisplayOrder
                        // CreatedAt and CreatedBy will be set by the repository
                    }).ToList();
                }

                // Add publications if provided
                if (facultyDto.Publications != null && facultyDto.Publications.Any())
                {
                    faculty.Publications = facultyDto.Publications.Select(p => new FacultyPublication
                    {
                        Title = p.Title,
                        Journal = p.Journal,
                        Year = p.Year,
                        Url = p.Url,
                        DisplayOrder = p.DisplayOrder
                        // CreatedAt and CreatedBy will be set by the repository
                    }).ToList();
                }

                // Add awards if provided
                if (facultyDto.Awards != null && facultyDto.Awards.Any())
                {
                    faculty.Awards = facultyDto.Awards.Select(a => new FacultyAward
                    {
                        Name = a.Name,
                        Year = a.Year,
                        Organization = a.Organization,
                        DisplayOrder = a.DisplayOrder
                        // CreatedAt and CreatedBy will be set by the repository
                    }).ToList();
                }

                // Get the current user ID for audit trail
                var userId = _currentUserService.UserId?.ToString();

                // Pass the current user ID to the repository for audit trail
                await repository.AddAsync(faculty, userId);
                await _unitOfWork.SaveChangesAsync(userId);

                _logger.LogInformation("Faculty created successfully with ID: {FacultyId}", faculty.Id);
                return faculty.Id;
            }
            catch (Exception ex) when (ex is not InvalidOperationException)
            {
                _logger.LogError(ex, "Error creating faculty: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        public async Task<bool> UpdateFacultyAsync(Guid id, FacultyUpdateDto facultyDto)
        {
            _logger.LogInformation("Updating faculty with ID: {FacultyId}", id);

            try
            {
                var repository = _unitOfWork.Repository<Faculty>();
                var faculty = await repository.GetByIdAsync(id, new[] {
                    "Translations",
                    "Education",
                    "Specializations",
                    "Courses",
                    "Publications",
                    "Awards",
                    "ProfileImage"
                });

                if (faculty == null)
                {
                    _logger.LogWarning("Faculty with ID {FacultyId} not found for update", id);
                    return false;
                }

                // Check if email is being changed and already exists
                if (facultyDto.Email != null && facultyDto.Email != faculty.Email)
                {
                    var existingFaculty = await repository.FindAsync(f => f.Email == facultyDto.Email && f.Id != id);
                    if (existingFaculty.Any())
                    {
                        _logger.LogWarning("Faculty with email {Email} already exists", facultyDto.Email);
                        throw new InvalidOperationException($"Faculty with email '{facultyDto.Email}' already exists");
                    }
                }

                // Update basic properties
                faculty.Name = facultyDto.Name ?? faculty.Name;
                faculty.Title = facultyDto.Title ?? faculty.Title;
                faculty.Email = facultyDto.Email ?? faculty.Email;
                faculty.Phone = facultyDto.Phone ?? faculty.Phone;
                faculty.Office = facultyDto.Office ?? faculty.Office;
                faculty.Biography = facultyDto.Biography ?? faculty.Biography;
                faculty.ShortBio = facultyDto.ShortBio ?? faculty.ShortBio;
                faculty.Department = facultyDto.Department ?? faculty.Department;
                faculty.JoinedYear = facultyDto.JoinedYear ?? faculty.JoinedYear;
                faculty.IsFeatured = facultyDto.IsFeatured ?? faculty.IsFeatured;
                faculty.IsActive = facultyDto.IsActive ?? faculty.IsActive;
                faculty.DisplayOrder = facultyDto.DisplayOrder ?? faculty.DisplayOrder;
                faculty.Website = facultyDto.Website ?? faculty.Website;
                faculty.LinkedIn = facultyDto.LinkedIn ?? faculty.LinkedIn;
                faculty.Twitter = facultyDto.Twitter ?? faculty.Twitter;
                faculty.ResearchGate = facultyDto.ResearchGate ?? faculty.ResearchGate;
                faculty.ProfileImageId = facultyDto.ProfileImageId ?? faculty.ProfileImageId;
                faculty.UpdatedAt = DateTime.UtcNow;
                // LastModifiedBy and LastModifiedAt will be set by the repository

                // Update translations if provided
                if (facultyDto.Translations != null)
                {
                    // Remove existing translation entries
                    if (faculty.Translations != null)
                    {
                        var translationRepository = _unitOfWork.Repository<FacultyTranslation>();
                        foreach (var translation in faculty.Translations.ToList())
                        {
                            var translationUserId = _currentUserService.UserId?.ToString();
                            await translationRepository.DeleteAsync(translation, translationUserId);
                        }
                    }

                    // Add new translation entries
                    faculty.Translations = facultyDto.Translations.Select(t => new FacultyTranslation
                    {
                        LanguageCode = t.LanguageCode,
                        Name = t.Name,
                        Title = t.Title,
                        Biography = t.Biography,
                        ShortBio = t.ShortBio,
                        FacultyId = faculty.Id
                        // CreatedAt and CreatedBy will be set by the repository
                    }).ToList();
                }

                // Update education if provided
                if (facultyDto.Education != null)
                {
                    // Remove existing education entries
                    if (faculty.Education != null)
                    {
                        var educationRepository = _unitOfWork.Repository<FacultyEducation>();
                        foreach (var education in faculty.Education.ToList())
                        {
                            var educationUserId = _currentUserService.UserId?.ToString();
                            await educationRepository.DeleteAsync(education, educationUserId);
                        }
                    }

                    // Add new education entries
                    faculty.Education = facultyDto.Education.Select(e => new FacultyEducation
                    {
                        Degree = e.Degree,
                        Institution = e.Institution,
                        Year = e.Year,
                        DisplayOrder = (int)e.DisplayOrder,
                        FacultyId = faculty.Id
                        // CreatedAt and CreatedBy will be set by the repository
                    }).ToList();
                }

                // Update specializations if provided
                if (facultyDto.Specializations != null)
                {
                    // Remove existing specialization entries
                    if (faculty.Specializations != null)
                    {
                        var specializationRepository = _unitOfWork.Repository<FacultySpecialization>();
                        foreach (var specialization in faculty.Specializations.ToList())
                        {
                            var specializationUserId = _currentUserService.UserId?.ToString();
                            await specializationRepository.DeleteAsync(specialization, specializationUserId);
                        }
                    }

                    // Add new specialization entries
                    faculty.Specializations = facultyDto.Specializations.Select(s => new FacultySpecialization
                    {
                        Name = s.Name,
                        DisplayOrder = (int)s.DisplayOrder,
                        FacultyId = faculty.Id
                        // CreatedAt and CreatedBy will be set by the repository
                    }).ToList();
                }

                // Update courses if provided
                if (facultyDto.Courses != null)
                {
                    // Remove existing course entries
                    if (faculty.Courses != null)
                    {
                        var courseRepository = _unitOfWork.Repository<FacultyCourse>();
                        foreach (var course in faculty.Courses.ToList())
                        {
                            var courseUserId = _currentUserService.UserId?.ToString();
                            await courseRepository.DeleteAsync(course, courseUserId);
                        }
                    }

                    // Add new course entries
                    faculty.Courses = facultyDto.Courses.Select(c => new FacultyCourse
                    {
                        Name = c.Name,
                        Description = c.Description,
                        DisplayOrder = (int)c.DisplayOrder,
                        FacultyId = faculty.Id
                        // CreatedAt and CreatedBy will be set by the repository
                    }).ToList();
                }

                // Update publications if provided
                if (facultyDto.Publications != null)
                {
                    // Remove existing publication entries
                    if (faculty.Publications != null)
                    {
                        var publicationRepository = _unitOfWork.Repository<FacultyPublication>();
                        foreach (var publication in faculty.Publications.ToList())
                        {
                            var publicationUserId = _currentUserService.UserId?.ToString();
                            await publicationRepository.DeleteAsync(publication, publicationUserId);
                        }
                    }

                    // Add new publication entries
                    faculty.Publications = facultyDto.Publications.Select(p => new FacultyPublication
                    {
                        Title = p.Title,
                        Journal = p.Journal,
                        Year = p.Year,
                        Url = p.Url,
                        DisplayOrder = (int)p.DisplayOrder,
                        FacultyId = faculty.Id
                        // CreatedAt and CreatedBy will be set by the repository
                    }).ToList();
                }

                // Update awards if provided
                if (facultyDto.Awards != null)
                {
                    // Remove existing award entries
                    if (faculty.Awards != null)
                    {
                        var awardRepository = _unitOfWork.Repository<FacultyAward>();
                        foreach (var award in faculty.Awards.ToList())
                        {
                            var awardUserId = _currentUserService.UserId?.ToString();
                            await awardRepository.DeleteAsync(award, awardUserId);
                        }
                    }

                    // Add new award entries
                    faculty.Awards = facultyDto.Awards.Select(a => new FacultyAward
                    {
                        Name = a.Name,
                        Year = a.Year,
                        Organization = a.Organization,
                        DisplayOrder = (int)a.DisplayOrder,
                        FacultyId = faculty.Id
                        // CreatedAt and CreatedBy will be set by the repository
                    }).ToList();
                }

                // Get the current user ID for audit trail
                var userId = _currentUserService.UserId?.ToString();

                // Pass the current user ID to the repository for audit trail
                await repository.UpdateAsync(faculty, userId);
                await _unitOfWork.SaveChangesAsync(userId);

                _logger.LogInformation("Faculty with ID {FacultyId} updated successfully", id);
                return true;
            }
            catch (Exception ex) when (ex is not InvalidOperationException)
            {
                _logger.LogError(ex, "Error updating faculty with ID {FacultyId}: {ErrorMessage}", id, ex.Message);
                throw;
            }
        }

        public async Task<bool> DeleteFacultyAsync(Guid id)
        {
            _logger.LogInformation("Deleting faculty with ID: {FacultyId}", id);

            try
            {
                var repository = _unitOfWork.Repository<Faculty>();

                // Check if faculty exists
                var faculty = await repository.GetByIdAsync(id);
                if (faculty == null)
                {
                    _logger.LogWarning("Faculty with ID {FacultyId} not found for deletion", id);
                    return false;
                }

                // Get the current user ID for audit trail
                var userId = _currentUserService.UserId?.ToString();

                // Use the repository's DeleteByIdAsync method which handles soft delete
                await repository.DeleteByIdAsync(id, userId);
                await _unitOfWork.SaveChangesAsync(userId);

                _logger.LogInformation("Faculty with ID {FacultyId} deleted successfully", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting faculty with ID {FacultyId}: {ErrorMessage}", id, ex.Message);
                throw;
            }
        }
    }
}
