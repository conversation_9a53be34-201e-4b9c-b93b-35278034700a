using Microsoft.EntityFrameworkCore;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.HostelFacility;
using School.Domain.Entities;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Services
{
    public class HostelFacilityService : IHostelFacilityService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;

        public HostelFacilityService(IUnitOfWork unitOfWork, ICurrentUserService currentUserService)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
        }

        public async Task<(IEnumerable<HostelFacilityDto> Facilities, int TotalCount)> GetAllFacilitiesAsync(HostelFacilityFilterDto filter)
        {
            var repository = _unitOfWork.Repository<HostelFacility>();
            var query = repository.AsQueryable("Translations", "Images");

            // Apply filters
            if (!string.IsNullOrEmpty(filter.Name))
            {
                query = query.Where(f => f.Name.Contains(filter.Name));
            }

            if (filter.Type.HasValue)
            {
                query = query.Where(f => f.Type == filter.Type.Value);
            }

            if (filter.Gender.HasValue)
            {
                query = query.Where(f => f.Gender == filter.Gender.Value);
            }

            if (filter.IsActive.HasValue)
            {
                query = query.Where(f => f.IsActive == filter.IsActive.Value);
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Get paginated results
            var facilities = await query
                .OrderBy(f => f.DisplayOrder)
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(f => new HostelFacilityDto
                {
                    Id = f.Id,
                    Name = f.Name,
                    Description = f.Description,
                    Type = f.Type,
                    Gender = f.Gender,
                    Capacity = f.Capacity,
                    Location = f.Location,
                    MonthlyFee = f.MonthlyFee,
                    ContactInfo = f.ContactInfo,
                    HasWifi = f.HasWifi,
                    HasAC = f.HasAC,
                    HasMeals = f.HasMeals,
                    HasLaundry = f.HasLaundry,
                    HasStudyRoom = f.HasStudyRoom,
                    HasRecreationRoom = f.HasRecreationRoom,
                    IsActive = f.IsActive,
                    DisplayOrder = f.DisplayOrder,
                    CreatedAt = f.CreatedAt,
                    LastModifiedAt = f.LastModifiedAt,
                    Translations = f.Translations.Select(t => new HostelFacilityTranslationDto
                    {
                        Id = t.Id,
                        HostelFacilityId = t.HostelFacilityId,
                        LanguageCode = t.LanguageCode,
                        Name = t.Name,
                        Description = t.Description,
                        // No additional properties needed
                    }).ToList(),
                    Images = f.Images.Select(i => new HostelImageDto
                    {
                        Id = i.Id,
                        HostelFacilityId = i.HostelFacilityId,
                        MediaItemId = i.MediaItemId,
                        DisplayOrder = i.DisplayOrder
                    }).ToList()
                })
                .ToListAsync();

            return (facilities, totalCount);
        }

        public async Task<HostelFacilityDto?> GetFacilityByIdAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<HostelFacility>();
            var facility = await repository.GetByIdAsync(id, new[] { "Translations", "Images" });

            if (facility == null) return null;

            return new HostelFacilityDto
            {
                Id = facility.Id,
                Name = facility.Name,
                Description = facility.Description,
                Type = facility.Type,
                Gender = facility.Gender,
                Capacity = facility.Capacity,
                Location = facility.Location,
                MonthlyFee = facility.MonthlyFee,
                ContactInfo = facility.ContactInfo,
                HasWifi = facility.HasWifi,
                HasAC = facility.HasAC,
                HasMeals = facility.HasMeals,
                HasLaundry = facility.HasLaundry,
                HasStudyRoom = facility.HasStudyRoom,
                HasRecreationRoom = facility.HasRecreationRoom,
                IsActive = facility.IsActive,
                DisplayOrder = facility.DisplayOrder,
                CreatedAt = facility.CreatedAt,
                LastModifiedAt = facility.LastModifiedAt,
                Translations = facility.Translations.Select(t => new HostelFacilityTranslationDto
                {
                    Id = t.Id,
                    HostelFacilityId = t.HostelFacilityId,
                    LanguageCode = t.LanguageCode,
                    Name = t.Name,
                    Description = t.Description,
                    // No additional properties needed
                }).ToList(),
                Images = facility.Images.Select(i => new HostelImageDto
                {
                    Id = i.Id,
                    HostelFacilityId = i.HostelFacilityId,
                    MediaItemId = i.MediaItemId,
                    DisplayOrder = i.DisplayOrder
                }).ToList()
            };
        }

        public async Task<Guid> CreateFacilityAsync(CreateHostelFacilityDto facilityDto)
        {
            var repository = _unitOfWork.Repository<HostelFacility>();

            var facility = new HostelFacility
            {
                Name = facilityDto.Name,
                Description = facilityDto.Description,
                Type = facilityDto.Type,
                Gender = facilityDto.Gender,
                Capacity = facilityDto.Capacity,
                Location = facilityDto.Location,
                MonthlyFee = facilityDto.MonthlyFee,
                ContactInfo = facilityDto.ContactInfo,
                HasWifi = facilityDto.HasWifi,
                HasAC = facilityDto.HasAC,
                HasMeals = facilityDto.HasMeals,
                HasLaundry = facilityDto.HasLaundry,
                HasStudyRoom = facilityDto.HasStudyRoom,
                HasRecreationRoom = facilityDto.HasRecreationRoom,
                IsActive = facilityDto.IsActive,
                DisplayOrder = facilityDto.DisplayOrder,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Add translations if provided
            if (facilityDto.ImageIds != null && facilityDto.ImageIds.Any())
            {
                facility.Images = facilityDto.ImageIds.Select(imageId => new HostelImage
                {
                    MediaItemId = imageId,
                    DisplayOrder = 0 // Default display order
                }).ToList();
            }

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.AddAsync(facility, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            return facility.Id;
        }

        public async Task<bool> UpdateFacilityAsync(Guid id, UpdateHostelFacilityDto facilityDto)
        {
            var repository = _unitOfWork.Repository<HostelFacility>();
            var facility = await repository.GetByIdAsync(id);

            if (facility == null) return false;

            // Update properties
            facility.Name = facilityDto.Name ?? facility.Name;
            facility.Description = facilityDto.Description ?? facility.Description;
            facility.Type = facilityDto.Type;
            facility.Gender = facilityDto.Gender;
            facility.Capacity = facilityDto.Capacity;
            facility.Location = facilityDto.Location ?? facility.Location;
            facility.MonthlyFee = facilityDto.MonthlyFee;
            facility.ContactInfo = facilityDto.ContactInfo;
            facility.HasWifi = facilityDto.HasWifi;
            facility.HasAC = facilityDto.HasAC;
            facility.HasMeals = facilityDto.HasMeals;
            facility.HasLaundry = facilityDto.HasLaundry;
            facility.HasStudyRoom = facilityDto.HasStudyRoom;
            facility.HasRecreationRoom = facilityDto.HasRecreationRoom;
            facility.IsActive = facilityDto.IsActive;
            facility.DisplayOrder = facilityDto.DisplayOrder;
            // LastModifiedBy and LastModifiedAt will be set by the repository

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.UpdateAsync(facility, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            return true;
        }

        public async Task<bool> DeleteFacilityAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<HostelFacility>();

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            return true;
        }
    }
}
