<div class="leaves-container">
  <h1 class="page-title">Leave Applications</h1>

  <div *ngIf="loading.student" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error.student" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load student data. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadStudentData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading.student && !error.student && student" class="leaves-content">
    <!-- Action Bar -->
    <div class="action-bar">
      <button mat-raised-button color="primary" (click)="openLeaveApplicationDialog()">
        <mat-icon>add</mat-icon> Apply for Leave
      </button>

      <form [formGroup]="filterForm" class="filter-form">
        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select formControlName="status">
            <mat-option [value]="null">All</mat-option>
            <mat-option *ngFor="let status of leaveStatuses" [value]="status.value">
              {{ status.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-button color="primary" type="button" (click)="applyFilter()">Filter</button>
        <button mat-button type="button" (click)="resetFilter()">Reset</button>
      </form>
    </div>

    <!-- Loading Indicator -->
    <div *ngIf="loading.leaves" class="leaves-loading">
      <mat-progress-bar mode="indeterminate"></mat-progress-bar>
    </div>

    <!-- Error Message -->
    <div *ngIf="error.leaves" class="leaves-error">
      <mat-error>
        <mat-icon>error</mat-icon>
        <span>Failed to load leave applications. Please try again.</span>
        <button mat-button color="warn" (click)="loadLeaves()">Retry</button>
      </mat-error>
    </div>

    <!-- No Leaves Message -->
    <div *ngIf="!loading.leaves && !error.leaves && leaves.length === 0" class="no-leaves">
      <mat-card>
        <mat-card-content>
          <p>No leave applications found.</p>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Leave Cards -->
    <div *ngIf="!loading.leaves && !error.leaves && leaves.length > 0" class="leave-cards">
      <mat-card *ngFor="let leave of leaves" class="leave-card">
        <mat-card-header>
          <div mat-card-avatar class="leave-avatar" [ngClass]="getStatusClass(leave?.status)">
            <mat-icon>event_busy</mat-icon>
          </div>
          <mat-card-title>{{ getLeaveTypeLabel(leave?.type) }} Leave</mat-card-title>
          <mat-card-subtitle>
            {{ leave?.startDate | date:'mediumDate' }} - {{ leave?.endDate | date:'mediumDate' }}
            ({{ calculateLeaveDays(leave?.startDate, leave?.endDate) }} days)
          </mat-card-subtitle>
          <div class="leave-status">
            <span class="status-badge" [ngClass]="getStatusClass(leave?.status)">
              {{ getStatusLabel(leave?.status) }}
            </span>
          </div>
        </mat-card-header>
        <mat-card-content>
          <div class="leave-details">
            <div class="leave-reason">
              <span class="leave-label">Reason:</span>
              <span class="leave-value">{{ leave?.reason || 'No reason provided' }}</span>
            </div>

            <div class="leave-dates">
              <div class="leave-start">
                <span class="leave-label">From:</span>
                <span class="leave-value">{{ leave.startDate | date:'fullDate' }}</span>
              </div>
              <div class="leave-end">
                <span class="leave-label">To:</span>
                <span class="leave-value">{{ leave.endDate | date:'fullDate' }}</span>
              </div>
            </div>

            <div class="leave-approval" *ngIf="leave.status === 1 || leave.status === 2">
              <div class="leave-approved-by" *ngIf="leave.approvedBy">
                <span class="leave-label">{{ leave.status === 1 ? 'Approved' : 'Rejected' }} By:</span>
                <span class="leave-value">{{ leave.approvedBy }}</span>
              </div>
              <div class="leave-approved-at" *ngIf="leave.approvedAt">
                <span class="leave-label">{{ leave.status === 1 ? 'Approved' : 'Rejected' }} On:</span>
                <span class="leave-value">{{ leave.approvedAt | date:'medium' }}</span>
              </div>
            </div>

            <div class="leave-comments" *ngIf="leave.comments">
              <span class="leave-label">Comments:</span>
              <span class="leave-value">{{ leave.comments }}</span>
            </div>

            <div class="leave-attachment" *ngIf="leave.attachmentPath">
              <span class="leave-label">Attachment:</span>
              <a class="leave-value" [href]="leave.attachmentPath" target="_blank">View Attachment</a>
            </div>

            <div class="leave-created">
              <span class="leave-label">Applied On:</span>
              <span class="leave-value">{{ leave.createdAt | date:'medium' }}</span>
            </div>
          </div>
        </mat-card-content>
        <mat-card-actions *ngIf="canCancelLeave(leave)">
          <button mat-button color="warn" (click)="cancelLeave(leave.id)">Cancel Application</button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</div>
