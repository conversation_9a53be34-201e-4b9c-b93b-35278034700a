import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../../shared/components/default-hero/default-hero.component';

interface Program {
  title: string;
  description: string;
  icon: string;
}

interface Course {
  name: string;
  description: string;
  grade: string;
}

@Component({
  selector: 'app-elementary',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatDividerModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './elementary.component.html',
  styleUrls: ['./elementary.component.scss']
})
export class ElementaryComponent {
  // Key features of the elementary program
  keyFeatures: Program[] = [
    {
      title: 'Foundational Skills',
      description: 'Our elementary program focuses on building strong foundational skills in reading, writing, mathematics, and critical thinking that will serve students throughout their academic journey.',
      icon: 'foundation'
    },
    {
      title: 'Personalized Learning',
      description: 'We recognize that each child learns differently, and our teachers use a variety of instructional approaches to meet individual needs and learning styles.',
      icon: 'person'
    },
    {
      title: 'Inquiry-Based Learning',
      description: 'Students are encouraged to ask questions, explore concepts, and discover answers through hands-on activities and project-based learning experiences.',
      icon: 'psychology'
    },
    {
      title: 'Character Development',
      description: 'We emphasize the development of positive character traits such as responsibility, respect, empathy, and perseverance through our integrated character education program.',
      icon: 'emoji_people'
    },
    {
      title: 'Global Perspective',
      description: 'Students develop an understanding and appreciation of diverse cultures and global issues through our international curriculum and cultural studies.',
      icon: 'public'
    },
    {
      title: 'Technology Integration',
      description: 'Age-appropriate technology is integrated throughout the curriculum to enhance learning, develop digital literacy, and prepare students for the future.',
      icon: 'computer'
    }
  ];

  // Grade levels
  gradeLevels = [
    {
      name: 'Kindergarten (Ages 5-6)',
      description: 'Our kindergarten program focuses on developing early literacy and numeracy skills, social development, and a love for learning through play-based and structured activities.',
      subjects: ['Early Literacy', 'Numeracy', 'Science Exploration', 'Social Studies', 'Art & Music', 'Physical Education']
    },
    {
      name: 'Grades 1-2 (Ages 6-8)',
      description: 'In the early elementary years, students build fundamental reading, writing, and math skills while expanding their knowledge of the world through integrated thematic units.',
      subjects: ['Reading & Writing', 'Mathematics', 'Science', 'Social Studies', 'Art & Music', 'Physical Education', 'Technology']
    },
    {
      name: 'Grades 3-5 (Ages 8-11)',
      description: 'Upper elementary students deepen their academic skills and content knowledge while developing greater independence, critical thinking, and research abilities.',
      subjects: ['Language Arts', 'Mathematics', 'Science', 'Social Studies', 'World Languages', 'Art & Music', 'Physical Education', 'Technology']
    }
  ];

  // Sample courses
  sampleCourses: Course[] = [
    {
      name: 'Reading Workshop',
      description: 'Students develop reading comprehension strategies, fluency, and a love of literature through guided reading, independent reading, and literature discussions.',
      grade: 'Grades 1-5'
    },
    {
      name: 'Writers Workshop',
      description: 'Students learn the writing process and develop skills in various genres including narrative, informational, and opinion writing.',
      grade: 'Grades 1-5'
    },
    {
      name: 'Mathematics',
      description: 'Our math curriculum emphasizes conceptual understanding, procedural fluency, and problem-solving through hands-on activities and real-world applications.',
      grade: 'Grades K-5'
    },
    {
      name: 'Science Exploration',
      description: 'Students engage in inquiry-based investigations of life, earth, and physical sciences, developing scientific thinking and research skills.',
      grade: 'Grades K-5'
    },
    {
      name: 'Social Studies',
      description: 'Students explore communities, cultures, geography, and history through thematic units that integrate reading, writing, and research.',
      grade: 'Grades K-5'
    },
    {
      name: 'World Languages',
      description: 'Introduction to foreign language learning with a focus on basic vocabulary, cultural awareness, and conversational skills.',
      grade: 'Grades 3-5'
    }
  ];

  // Special programs
  specialPrograms = [
    {
      title: 'STEM Enrichment',
      description: 'Hands-on science, technology, engineering, and math activities that extend beyond the regular curriculum to foster innovation and problem-solving skills.',
      image: 'assets/images/academics/elementary-stem.jpg'
    },
    {
      title: 'Literacy Celebration',
      description: 'Annual event featuring author visits, book fairs, reading challenges, and creative writing projects to celebrate and promote a love of reading and writing.',
      image: 'assets/images/academics/elementary-literacy.jpg'
    },
    {
      title: 'Arts Integration',
      description: 'Visual and performing arts are integrated across the curriculum to enhance learning and provide opportunities for creative expression.',
      image: 'assets/images/academics/elementary-arts.jpg'
    },
    {
      title: 'Community Service',
      description: 'Age-appropriate service learning projects that connect classroom learning to real-world issues and develop social responsibility and empathy.',
      image: 'assets/images/academics/elementary-service.jpg'
    }
  ];

  // Daily schedule
  sampleSchedule = [
    { time: '8:00 - 8:15 AM', activity: 'Morning Meeting' },
    { time: '8:15 - 9:45 AM', activity: 'Literacy Block (Reading & Writing)' },
    { time: '9:45 - 10:00 AM', activity: 'Snack & Break' },
    { time: '10:00 - 11:00 AM', activity: 'Mathematics' },
    { time: '11:00 - 11:45 AM', activity: 'Science/Social Studies' },
    { time: '11:45 - 12:30 PM', activity: 'Lunch & Recess' },
    { time: '12:30 - 1:15 PM', activity: 'Specials (Art, Music, PE, Library, or Technology)' },
    { time: '1:15 - 2:15 PM', activity: 'Continued Science/Social Studies or Project Work' },
    { time: '2:15 - 2:45 PM', activity: 'Guided Reading/Small Group Instruction' },
    { time: '2:45 - 3:00 PM', activity: 'Closing Circle & Dismissal' }
  ];
}
