.results-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.filter-card {
  margin-bottom: 24px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.results-loading,
.results-error {
  margin-bottom: 24px;
}

.results-error {
  text-align: center;
  padding: 16px;

  mat-icon {
    vertical-align: middle;
    margin-right: 8px;
  }
}

.results-summary {
  margin-bottom: 24px;
}

.summary-content {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  align-items: center;
}

.summary-item {
  display: flex;
  flex-direction: column;
}

.summary-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.summary-value {
  font-size: 18px;
  font-weight: 500;
}

.summary-actions {
  margin-left: auto;
}

.results-table-container {
  overflow-x: auto;
  margin-bottom: 24px;
}

.results-table {
  width: 100%;
}

.grade-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.no-data {
  text-align: center;
  padding: 16px;
}

@media (max-width: 768px) {
  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .summary-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .summary-actions {
    margin-left: 0;
    margin-top: 16px;
    width: 100%;
    
    button {
      width: 100%;
    }
  }
}
