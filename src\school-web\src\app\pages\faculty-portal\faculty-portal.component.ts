import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';

// Angular Material imports
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { AuthService } from '../../core/services/auth.service';
import { FacultyService } from '../../core/services/faculty.service';
import { Faculty } from '../../core/models/faculty.model';
import { LanguageThemeSwitcherComponent } from '../../shared/components/ui/language-theme-switcher/language-theme-switcher.component';

@Component({
  selector: 'app-faculty-portal',
  templateUrl: './faculty-portal.component.html',
  styleUrls: ['./faculty-portal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatSidenavModule,
    MatToolbarModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatMenuModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    LanguageThemeSwitcherComponent
  ]
})
export class FacultyPortalComponent implements OnInit {
  faculty: Faculty | null = null;
  loading = true;
  error = false;

  navItems = [
    { label: 'Dashboard', icon: 'dashboard', route: '/faculty-portal/dashboard' },
    { label: 'Profile', icon: 'person', route: '/faculty-portal/profile' },
    { label: 'Schedule', icon: 'schedule', route: '/faculty-portal/schedule' },
    { label: 'My Classes', icon: 'class', route: '/faculty-portal/classes' },
    { label: 'Attendance', icon: 'event_available', route: '/faculty-portal/attendance' },
    { label: 'Results', icon: 'assessment', route: '/faculty-portal/results' },
    { label: 'Leave Applications', icon: 'event_busy', route: '/faculty-portal/leaves' }
  ];

  constructor(
    private authService: AuthService,
    private facultyService: FacultyService,
    private router: Router,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadFacultyData();
  }

  loadFacultyData(): void {
    this.loading = true;
    const userId = this.authService.getCurrentUserId();

    if (!userId) {
      this.error = true;
      this.loading = false;
      this.router.navigate(['/login']);
      return;
    }

    // Check if the user has the correct role
    if (!this.authService.hasRole('Faculty')) {
      this.error = true;
      this.loading = false;
      this.authService.navigateToUserPortal();
      return;
    }

    // Fetch the faculty by user ID
    this.facultyService.getFacultyByUserId(userId)
      .subscribe({
        next: (faculty) => {
          this.faculty = faculty;
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading faculty data:', err);
          this.error = true;
          this.loading = false;
          this.showErrorMessage('Failed to load faculty data. Please try again later.');
        }
      });
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
    this.showSuccessMessage('You have been successfully logged out.');
  }

  /**
   * Show a success message
   */
  private showSuccessMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  /**
   * Show an error message
   */
  private showErrorMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }
}
