using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class Student : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this student belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    // Basic Information
    public string StudentId { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public DateTime DateOfBirth { get; set; }
    public GenderType Gender { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string EmergencyContactName { get; set; } = string.Empty;
    public string EmergencyContactPhone { get; set; } = string.Empty;
    public string EmergencyContactRelation { get; set; } = string.Empty;

    // Medical Information
    public string BloodGroup { get; set; } = string.Empty;
    public string MedicalConditions { get; set; } = string.Empty;
    public string Allergies { get; set; } = string.Empty;

    // Academic Information
    public int CurrentGrade { get; set; } // Class 6-10 (legacy field)
    public string Section { get; set; } = string.Empty; // A, B, etc. (legacy field)

    // New Grade & Section Management (Sprint 3)
    public Guid? CurrentGradeId { get; set; } // Reference to Grade entity
    public Guid? CurrentSectionId { get; set; } // Reference to Section entity
    public Guid? ClassTeacherId { get; set; } // Reference to ClassTeacher entity

    public TeachingMedium Medium { get; set; } = TeachingMedium.Bengali;
    public ShiftType Shift { get; set; } = ShiftType.Morning;
    public int AcademicYear { get; set; } // Current academic year
    public int RollNumber { get; set; } // Roll number in current class
    public int AdmissionYear { get; set; } // Year of first admission
    public DateTime? GraduationDate { get; set; }

    // Status Information
    public bool IsActive { get; set; } = true;
    public bool IsHosteler { get; set; } = false;
    public string UserId { get; set; } = string.Empty;

    // Navigation properties
    public Guid? ProfileImageId { get; set; }
    public MediaItem? ProfileImage { get; set; }
    public Faculty? ClassTeacher { get; set; }

    // New Grade & Section Navigation Properties (Sprint 3)
    public Grade? CurrentGradeEntity { get; set; }
    public Section? CurrentSectionEntity { get; set; }
    public ClassTeacher? CurrentClassTeacher { get; set; }

    public ICollection<StudentParent> Parents { get; set; } = new List<StudentParent>();
    public ICollection<StudentAttendance> Attendances { get; set; } = new List<StudentAttendance>();
    public ICollection<StudentFee> Fees { get; set; } = new List<StudentFee>();
    public ICollection<StudentResult> Results { get; set; } = new List<StudentResult>();
    public ICollection<StudentLeave> Leaves { get; set; } = new List<StudentLeave>();
    public ICollection<StudentAcademicHistory> AcademicHistory { get; set; } = new List<StudentAcademicHistory>();
    public DateTime UpdatedAt { get; set; }
}
