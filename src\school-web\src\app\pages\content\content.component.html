<div class="content-container">
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <div class="error-container" *ngIf="error">
    <p class="error-message">{{ error }}</p>
    <button mat-raised-button color="primary" (click)="loadContent()">
      {{ 'COMMON.TRY_AGAIN' | translate }}
    </button>
  </div>

  <div class="content-wrapper" *ngIf="!isLoading && !error && content">
    <div class="content-header">
      <h1 class="content-title">{{ getTranslation('title') }}</h1>
      
      <div class="content-meta">
        <mat-chip-listbox>
          <mat-chip>
            <mat-icon>{{ getContentTypeIcon(content.type) }}</mat-icon>
            {{ getContentTypeLabel(content.type) }}
          </mat-chip>
        </mat-chip-listbox>
        
        <span class="content-date" *ngIf="content.publishedAt">
          {{ content.publishedAt | date }}
        </span>
      </div>
    </div>
    
    <div class="content-body">
      <div [innerHTML]="getTranslation('body')"></div>
    </div>
    
    <div class="content-media" *ngIf="content.mediaItems && content.mediaItems.length > 0">
      <h3>{{ 'CONTENT.RELATED_MEDIA' | translate }}</h3>
      
      <div class="media-grid">
        <div class="media-item" *ngFor="let media of content.mediaItems">
          <img *ngIf="media.type === 0" [src]="media.filePath" [alt]="media.altText || media.fileName">
          <div *ngIf="media.type !== 0" class="media-icon">
            <mat-icon>{{ getMediaTypeIcon(media.type) }}</mat-icon>
          </div>
          <div class="media-caption" *ngIf="media.caption">{{ media.caption }}</div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="not-found" *ngIf="!isLoading && !error && !content">
    <h2>{{ 'CONTENT.NOT_FOUND' | translate }}</h2>
    <p>{{ 'CONTENT.NOT_FOUND_MESSAGE' | translate }}</p>
    <button mat-raised-button color="primary" routerLink="/">
      {{ 'CONTENT.BACK_TO_HOME' | translate }}
    </button>
  </div>
</div>
