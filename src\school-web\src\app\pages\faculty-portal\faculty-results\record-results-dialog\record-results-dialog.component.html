<h2 mat-dialog-title>Record Results</h2>
<h3 class="dialog-subtitle">
  Class {{ data.grade }}-{{ data.section }} | {{ data.subjectName }} |
  {{ data.examType === 0 ? 'Half Yearly' :
     data.examType === 1 ? 'Annual' :
     data.examType === 2 ? 'Class Test' : 'SSC' }} Exam |
  {{ data.academicYear }}
</h3>

<div *ngIf="loading" class="loading-container">
  <mat-spinner diameter="40"></mat-spinner>
</div>

<form [formGroup]="resultsForm" (ngSubmit)="onSubmit()">
  <mat-dialog-content>
    <div class="total-marks-container">
      <mat-form-field appearance="outline">
        <mat-label>Total Marks</mat-label>
        <input matInput type="number" formControlName="totalMarks" (change)="updateTotalMarks()">
        <mat-error *ngIf="resultsForm.get('totalMarks')?.hasError('required')">
          Total marks is required
        </mat-error>
        <mat-error *ngIf="resultsForm.get('totalMarks')?.hasError('min')">
          Total marks must be greater than 0
        </mat-error>
      </mat-form-field>
    </div>

    <div class="students-table-container">
      <table class="students-table">
        <thead>
          <tr>
            <th>Roll No.</th>
            <th>Student Name</th>
            <th>Marks Obtained</th>
            <th>Percentage</th>
            <th>Grade</th>
            <th>Remarks</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let studentControl of students.controls; let i = index" [formGroup]="$any(studentControl)">
            <td>{{ studentControl.get('rollNumber')?.value }}</td>
            <td>{{ studentControl.get('firstName')?.value }} {{ studentControl.get('lastName')?.value }}</td>
            <td>
              <mat-form-field appearance="outline">
                <input matInput type="number" formControlName="marksObtained">
                <mat-error *ngIf="studentControl.get('marksObtained')?.hasError('required')">
                  Required
                </mat-error>
                <mat-error *ngIf="studentControl.get('marksObtained')?.hasError('min')">
                  Min 0
                </mat-error>
                <mat-error *ngIf="studentControl.get('marksObtained')?.hasError('max')">
                  Max {{ resultsForm.get('totalMarks')?.value }}
                </mat-error>
              </mat-form-field>
            </td>
            <td>
              {{ getPercentage(studentControl.get('marksObtained')?.value || 0) | number:'1.2-2' }}%
            </td>
            <td>
              <span class="grade-badge"
                    [style.background-color]="getGradeColor(getGrade(studentControl.get('marksObtained')?.value || 0))"
                    *ngIf="studentControl.get('marksObtained')?.value">
                {{ getGrade(studentControl.get('marksObtained')?.value || 0) }}
              </span>
            </td>
            <td>
              <mat-form-field appearance="outline">
                <input matInput formControlName="remarks" placeholder="Optional">
              </mat-form-field>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" [disabled]="loading" (click)="onCancel()">Cancel</button>
    <button mat-raised-button color="primary" type="submit" [disabled]="resultsForm.invalid || loading">
      <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
      <span *ngIf="!loading">Save Results</span>
    </button>
  </mat-dialog-actions>
</form>
