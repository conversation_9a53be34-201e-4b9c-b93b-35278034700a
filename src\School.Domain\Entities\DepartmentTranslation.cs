using School.Domain.Common;

namespace School.Domain.Entities;

public class DepartmentTranslation : BaseEntity
{
    public Guid DepartmentId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string HeadOfDepartment { get; set; } = string.Empty;

    // Navigation properties
    public Department Department { get; set; } = null!;
}
