<div class="media-list-container">
  <div class="page-header">
    <h1>{{ 'admin.media.title' | translate }}</h1>
    <a mat-raised-button color="primary" routerLink="/admin/media/upload">
      <mat-icon>cloud_upload</mat-icon>
      {{ 'admin.media.uploadNew' | translate }}
    </a>
  </div>

  <mat-card class="filter-card">
    <mat-card-content>
      <form [formGroup]="filterForm" (ngSubmit)="applyFilter()">
        <div class="filter-form">
          <mat-form-field appearance="outline">
            <mat-label>{{ 'admin.media.type' | translate }}</mat-label>
            <mat-select formControlName="type">
              <mat-option value="">{{ 'common.all' | translate }}</mat-option>
              <mat-option [value]="0">{{ 'admin.media.types.image' | translate }}</mat-option>
              <mat-option [value]="1">{{ 'admin.media.types.document' | translate }}</mat-option>
              <mat-option [value]="2">{{ 'admin.media.types.video' | translate }}</mat-option>
              <mat-option [value]="3">{{ 'admin.media.types.audio' | translate }}</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="search-field">
            <mat-label>{{ 'common.search' | translate }}</mat-label>
            <input matInput formControlName="search" placeholder="{{ 'admin.media.searchPlaceholder' | translate }}">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <div class="filter-actions">
            <button mat-raised-button color="primary" type="submit">
              {{ 'common.apply' | translate }}
            </button>
            <button mat-button type="button" (click)="resetFilter()">
              {{ 'common.reset' | translate }}
            </button>
          </div>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <div class="error-container" *ngIf="error">
    <p class="error-message">{{ error }}</p>
    <button mat-raised-button color="primary" (click)="loadMediaItems()">
      {{ 'common.tryAgain' | translate }}
    </button>
  </div>

  <div class="media-grid-container" *ngIf="!isLoading && !error">
    <div *ngIf="mediaItems.length === 0" class="no-media">
      <mat-icon class="empty-icon">image_not_supported</mat-icon>
      <p>{{ 'BUTTONS.EMPTY_STATE' | translate }} - {{ 'admin.media.noMedia' | translate }}</p>
      <a mat-raised-button color="primary" routerLink="/admin/media/upload">
        <mat-icon>cloud_upload</mat-icon>
        {{ 'admin.media.uploadNew' | translate }}
      </a>
    </div>

    <div class="media-grid" *ngIf="mediaItems.length > 0">

      <div class="media-item" *ngFor="let media of mediaItems" [class.selected]="selectedMedia?.id === media.id" (click)="selectMedia(media)">
        <div class="media-preview">
          <img *ngIf="media.type === 0" [src]="media.filePath" [alt]="media.altText || media.fileName">
          <div *ngIf="media.type !== 0" class="media-icon">
            <mat-icon>{{ getMediaTypeIcon(media.type) }}</mat-icon>
          </div>
        </div>
        <div class="media-info">
          <div class="media-title">{{ media.fileName }}</div>
          <div class="media-meta">
            <mat-chip-listbox>
              <mat-chip>{{ getMediaTypeLabel(media.type) }}</mat-chip>
            </mat-chip-listbox>
            <span>{{ getFileSize(media.fileSize) }}</span>
          </div>
        </div>
      </div>
    </div>

    <mat-paginator
      *ngIf="mediaItems.length > 0"
      [length]="totalCount"
      [pageSize]="pageSize"
      [pageSizeOptions]="[12, 24, 48, 96]"
      [pageIndex]="pageIndex"
      (page)="onPageChange($event)"
      showFirstLastButtons>
    </mat-paginator>
  </div>

  <div class="media-details" *ngIf="selectedMedia">
    <mat-card>
      <mat-card-header>
        <mat-card-title>{{ selectedMedia.fileName }}</mat-card-title>
        <mat-card-subtitle>
          {{ getMediaTypeLabel(selectedMedia.type) }} • {{ getFileSize(selectedMedia.fileSize) }}
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <div class="media-preview-large">
          <img *ngIf="selectedMedia.type === 0" [src]="selectedMedia.filePath" [alt]="selectedMedia.altText || selectedMedia.fileName">
          <div *ngIf="selectedMedia.type !== 0" class="media-icon-large">
            <mat-icon>{{ getMediaTypeIcon(selectedMedia.type) }}</mat-icon>
          </div>
        </div>

        <div class="media-details-info">
          <div class="detail-row">
            <span class="detail-label">{{ 'admin.media.originalFileName' | translate }}:</span>
            <span class="detail-value">{{ selectedMedia.originalFileName }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">{{ 'admin.media.mimeType' | translate }}:</span>
            <span class="detail-value">{{ selectedMedia.mimeType }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">{{ 'admin.media.uploadedBy' | translate }}:</span>
            <span class="detail-value">{{ selectedMedia.uploadedByName }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">{{ 'admin.media.uploadedAt' | translate }}:</span>
            <span class="detail-value">{{ selectedMedia.createdAt | date:'medium' }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">{{ 'admin.media.altText' | translate }}:</span>
            <span class="detail-value">{{ selectedMedia.altText || 'N/A' }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">{{ 'admin.media.caption' | translate }}:</span>
            <span class="detail-value">{{ selectedMedia.caption || 'N/A' }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">{{ 'admin.media.url' | translate }}:</span>
            <div class="url-container">
              <span class="detail-value url-value">{{ selectedMedia.filePath }}</span>
              <button mat-icon-button (click)="copyToClipboard(selectedMedia.filePath)" matTooltip="{{ 'admin.media.copyUrl' | translate }}">
                <mat-icon>content_copy</mat-icon>
              </button>
            </div>
          </div>
        </div>
      </mat-card-content>

      <mat-card-actions align="end">
        <a mat-button [href]="selectedMedia.filePath" target="_blank">
          <mat-icon>open_in_new</mat-icon>
          {{ 'admin.media.open' | translate }}
        </a>
        <button mat-button color="warn" (click)="deleteMedia(selectedMedia.id)">
          <mat-icon>delete</mat-icon>
          {{ 'common.delete' | translate }}
        </button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
