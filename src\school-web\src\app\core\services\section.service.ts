import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface Section {
  id: string;
  name: string;
  code: string;
  gradeId: string;
  gradeName: string;
  type: string;
  medium: string;
  shift: string;
  capacity: number;
  currentEnrollment: number;
  isActive: boolean;
  displayOrder: number;
  academicYearId: string;
  academicYearName: string;
  classroom: string;
  roomNumber: string;
  description: string;
  requirements: string;
  remarks: string;
  createdAt: Date;
  lastModifiedAt?: Date;
}

export interface CreateSectionDto {
  name: string;
  code: string;
  gradeId: string;
  type: string;
  medium: string;
  shift: string;
  capacity: number;
  isActive: boolean;
  displayOrder: number;
  academicYearId: string;
  classroom: string;
  roomNumber: string;
  description: string;
  requirements: string;
  remarks: string;
}

export interface UpdateSectionDto {
  name: string;
  code: string;
  gradeId: string;
  type: string;
  medium: string;
  shift: string;
  capacity: number;
  isActive: boolean;
  displayOrder: number;
  classroom: string;
  roomNumber: string;
  description: string;
  requirements: string;
  remarks: string;
}

export interface SectionFilterDto {
  page: number;
  pageSize: number;
  searchTerm?: string;
  gradeId?: string;
  academicYearId?: string;
  type?: string;
  medium?: string;
  shift?: string;
  isActive?: boolean;
  sortBy?: string;
  sortDirection?: string;
}

export interface SectionListResponse {
  data: Section[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface SectionStatistics {
  totalSections: number;
  activeSections: number;
  totalCapacity: number;
  totalEnrollment: number;
  averageCapacityUtilization: number;
  sectionsNearCapacity: number;
}

@Injectable({
  providedIn: 'root'
})
export class SectionService {
  private http = inject(HttpClient);
  private apiUrl = `${environment.apiUrl}/api/Section`;

  getSections(filter: SectionFilterDto): Observable<SectionListResponse> {
    let params = new HttpParams()
      .set('page', filter.page.toString())
      .set('pageSize', filter.pageSize.toString());

    if (filter.searchTerm) {
      params = params.set('searchTerm', filter.searchTerm);
    }
    if (filter.gradeId) {
      params = params.set('gradeId', filter.gradeId);
    }
    if (filter.academicYearId) {
      params = params.set('academicYearId', filter.academicYearId);
    }
    if (filter.type) {
      params = params.set('type', filter.type);
    }
    if (filter.medium) {
      params = params.set('medium', filter.medium);
    }
    if (filter.shift) {
      params = params.set('shift', filter.shift);
    }
    if (filter.isActive !== undefined) {
      params = params.set('isActive', filter.isActive.toString());
    }
    if (filter.sortBy) {
      params = params.set('sortBy', filter.sortBy);
    }
    if (filter.sortDirection) {
      params = params.set('sortDirection', filter.sortDirection);
    }

    return this.http.get<SectionListResponse>(this.apiUrl, { params });
  }

  getSection(id: string): Observable<Section> {
    return this.http.get<Section>(`${this.apiUrl}/${id}`);
  }

  getSectionsByGrade(gradeId: string): Observable<Section[]> {
    return this.http.get<Section[]>(`${this.apiUrl}/grade/${gradeId}`);
  }

  getSectionsByAcademicYear(academicYearId: string): Observable<Section[]> {
    return this.http.get<Section[]>(`${this.apiUrl}/academic-year/${academicYearId}`);
  }

  getActiveSections(gradeId: string): Observable<Section[]> {
    return this.http.get<Section[]>(`${this.apiUrl}/grade/${gradeId}/active`);
  }

  createSection(section: CreateSectionDto): Observable<{ id: string }> {
    return this.http.post<{ id: string }>(this.apiUrl, section);
  }

  updateSection(id: string, section: UpdateSectionDto): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, section);
  }

  deleteSection(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  updateSectionStatus(id: string, isActive: boolean): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/status`, { isActive });
  }

  updateSectionCapacity(id: string, capacity: number): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/capacity`, { capacity });
  }

  transferStudent(studentId: string, fromSectionId: string, toSectionId: string, reason?: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/transfer-student`, {
      studentId,
      fromSectionId,
      toSectionId,
      reason
    });
  }

  bulkTransferStudents(studentIds: string[], fromSectionId: string, toSectionId: string, reason?: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/bulk-transfer-students`, {
      studentIds,
      fromSectionId,
      toSectionId,
      reason
    });
  }

  reorderSections(sectionOrders: { id: string; displayOrder: number }[]): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/reorder`, { sectionOrders });
  }

  getSectionStatistics(academicYearId: string): Observable<SectionStatistics> {
    return this.http.get<SectionStatistics>(`${this.apiUrl}/academic-year/${academicYearId}/statistics`);
  }

  getSectionCapacityReport(academicYearId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/academic-year/${academicYearId}/capacity-report`);
  }

  exportSections(academicYearId: string): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/academic-year/${academicYearId}/export`, {
      responseType: 'blob'
    });
  }

  validateSectionCode(code: string, gradeId: string, excludeId?: string): Observable<{ isValid: boolean }> {
    let params = new HttpParams()
      .set('code', code)
      .set('gradeId', gradeId);

    if (excludeId) {
      params = params.set('excludeId', excludeId);
    }

    return this.http.get<{ isValid: boolean }>(`${this.apiUrl}/validate-code`, { params });
  }

  getSectionTypes(): Observable<{ value: string; label: string }[]> {
    return this.http.get<{ value: string; label: string }[]>(`${this.apiUrl}/types`);
  }

  getMediums(): Observable<{ value: string; label: string }[]> {
    return this.http.get<{ value: string; label: string }[]>(`${this.apiUrl}/mediums`);
  }

  getShifts(): Observable<{ value: string; label: string }[]> {
    return this.http.get<{ value: string; label: string }[]>(`${this.apiUrl}/shifts`);
  }

  checkSectionAvailability(gradeId: string, academicYearId: string): Observable<{ availableCapacity: number; totalCapacity: number }> {
    return this.http.get<{ availableCapacity: number; totalCapacity: number }>(
      `${this.apiUrl}/grade/${gradeId}/academic-year/${academicYearId}/availability`
    );
  }
}
