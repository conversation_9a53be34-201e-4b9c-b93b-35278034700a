import { MediaItem } from './media.model';
import { GenderType, ParentRelationType } from './student.model';
import { Student } from './student.model';

export interface Parent {
  id: number;
  firstName: string;
  lastName: string;
  fullName?: string;
  gender: GenderType;
  email: string;
  phone: string;
  alternatePhone: string;
  address: string;
  occupation: string;
  userId: string;
  isActive: boolean;
  profileImageId?: number;
  profileImage?: MediaItem;
  createdAt: Date;
  updatedAt?: Date;
}

export interface ParentDetail extends Parent {
  students: ParentStudent[];
}

export interface ParentStudent {
  id: number;
  studentId: number;
  parentId: number;
  relationType: ParentRelationType;
  isPrimaryContact: boolean;
  student: Student;
}

export interface CreateParent {
  firstName: string;
  lastName: string;
  gender: GenderType;
  email: string;
  phone: string;
  alternatePhone: string;
  address: string;
  occupation: string;
  userId: string;
  isActive: boolean;
  profileImageId?: number;
}

export interface UpdateParent {
  firstName: string;
  lastName: string;
  gender: GenderType;
  email: string;
  phone: string;
  alternatePhone: string;
  address: string;
  occupation: string;
  isActive: boolean;
  profileImageId?: number;
}

export interface ParentFilter {
  name?: string;
  email?: string;
  phone?: string;
  isActive?: boolean;
  studentId?: number;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: string;
}
