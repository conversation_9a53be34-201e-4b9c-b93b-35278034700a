# School Management System - Design System

## Overview

This design system establishes the visual and interaction standards for the School Management System, ensuring consistency, accessibility, and professional quality across all interfaces. It follows **Google Material Design 3 (Material You)** principles and guidelines, providing a modern, intuitive, and accessible user experience.

## Material Design Foundation

### Why Material Design 3?
- **Proven Framework**: Battle-tested design system used by millions
- **Accessibility First**: Built-in accessibility features and guidelines
- **Adaptive Design**: Dynamic color system and personalization
- **Cross-Platform**: Consistent experience across web and mobile
- **Component Library**: Rich set of pre-built, tested components
- **Documentation**: Comprehensive guidelines and best practices

## Design Principles (Material Design 3)

### 1. Material Design Core Principles

#### Expressive
- **Personal and Adaptive**: Dynamic color system that adapts to user preferences
- **Vibrant and Accessible**: High contrast ratios and inclusive design
- **Cohesive Brand Expression**: Reflects educational institution values

#### Adaptive
- **Responsive Layouts**: Seamless experience across all screen sizes
- **Dynamic Color**: Personalized color schemes based on user preferences
- **Flexible Components**: Adaptable to different content and contexts

#### Inclusive
- **Universal Accessibility**: WCAG 2.1 AA compliance built-in
- **Cultural Sensitivity**: Appropriate for Bengali and English users
- **Device Agnostic**: Works across all devices and input methods

### 2. Educational Context Principles

#### Clarity and Focus
- **Information Hierarchy**: Clear visual hierarchy using Material Design typography
- **Reduced Cognitive Load**: Progressive disclosure and focused interfaces
- **Intuitive Navigation**: Material Design navigation patterns
- **Task-Oriented Design**: Streamlined workflows for educational tasks

#### Trust and Reliability
- **Consistent Interactions**: Predictable Material Design patterns
- **Professional Appearance**: Clean, modern Material Design aesthetics
- **Data Security**: Visual indicators for secure operations
- **Error Prevention**: Clear validation and feedback systems

#### Efficiency and Performance
- **Fast Interactions**: Material Design motion and transitions
- **Optimized Loading**: Progressive enhancement and lazy loading
- **Keyboard Accessibility**: Full keyboard navigation support
- **Touch-Friendly**: Appropriate touch targets and gestures

### 3. Internationalization and Theming
- **Dual Language Support**: Bengali-Bangladesh (bn-BD) and English-US (en-US)
- **Material You Theming**: Dynamic color system with light and dark themes
- **Cultural Adaptation**: Respectful design patterns for both cultures
- **Localized Formatting**: Region-specific date, number, and currency formats

## Material Design 3 Color System

### Dynamic Color and Theming

The design system implements Material Design 3's dynamic color system, supporting both light and dark themes with automatic switching based on user preference or system settings.

#### Material Design 3 Color Tokens

##### Light Theme - Material Design 3 Colors
```css
/* Material Design 3 - Light Theme Primary Colors */
--md-sys-color-primary: #1976d2;
--md-sys-color-on-primary: #ffffff;
--md-sys-color-primary-container: #d3e3fd;
--md-sys-color-on-primary-container: #001c38;

/* Material Design 3 - Light Theme Secondary Colors */
--md-sys-color-secondary: #565f71;
--md-sys-color-on-secondary: #ffffff;
--md-sys-color-secondary-container: #dae2f9;
--md-sys-color-on-secondary-container: #131c2b;

/* Material Design 3 - Light Theme Tertiary Colors */
--md-sys-color-tertiary: #705575;
--md-sys-color-on-tertiary: #ffffff;
--md-sys-color-tertiary-container: #fad8fd;
--md-sys-color-on-tertiary-container: #28132e;

/* Material Design 3 - Light Theme Surface Colors */
--md-sys-color-surface: #fefbff;
--md-sys-color-on-surface: #1c1b1f;
--md-sys-color-surface-variant: #e1e2ec;
--md-sys-color-on-surface-variant: #44474f;
--md-sys-color-surface-container-lowest: #ffffff;
--md-sys-color-surface-container-low: #f6f2f7;
--md-sys-color-surface-container: #f0ecf1;
--md-sys-color-surface-container-high: #ebe6eb;
--md-sys-color-surface-container-highest: #e5e1e6;

/* Material Design 3 - Light Theme Background Colors */
--md-sys-color-background: #fefbff;
--md-sys-color-on-background: #1c1b1f;

/* Material Design 3 - Light Theme Error Colors */
--md-sys-color-error: #ba1a1a;
--md-sys-color-on-error: #ffffff;
--md-sys-color-error-container: #ffdad6;
--md-sys-color-on-error-container: #410002;

/* Material Design 3 - Light Theme Outline Colors */
--md-sys-color-outline: #74777f;
--md-sys-color-outline-variant: #c4c7cf;
```

##### Dark Theme - Material Design 3 Colors
```css
/* Material Design 3 - Dark Theme Primary Colors */
--md-sys-color-primary: #a4c8ff;
--md-sys-color-on-primary: #003062;
--md-sys-color-primary-container: #00468b;
--md-sys-color-on-primary-container: #d3e3fd;

/* Material Design 3 - Dark Theme Secondary Colors */
--md-sys-color-secondary: #bec6dc;
--md-sys-color-on-secondary: #283041;
--md-sys-color-secondary-container: #3e4759;
--md-sys-color-on-secondary-container: #dae2f9;

/* Material Design 3 - Dark Theme Tertiary Colors */
--md-sys-color-tertiary: #debcdf;
--md-sys-color-on-tertiary: #3f2844;
--md-sys-color-tertiary-container: #573e5c;
--md-sys-color-on-tertiary-container: #fad8fd;

/* Material Design 3 - Dark Theme Surface Colors */
--md-sys-color-surface: #141218;
--md-sys-color-on-surface: #e5e1e6;
--md-sys-color-surface-variant: #44474f;
--md-sys-color-on-surface-variant: #c4c7cf;
--md-sys-color-surface-container-lowest: #0f0d13;
--md-sys-color-surface-container-low: #1c1b1f;
--md-sys-color-surface-container: #201f23;
--md-sys-color-surface-container-high: #2b292d;
--md-sys-color-surface-container-highest: #363438;

/* Material Design 3 - Dark Theme Background Colors */
--md-sys-color-background: #141218;
--md-sys-color-on-background: #e5e1e6;

/* Material Design 3 - Dark Theme Error Colors */
--md-sys-color-error: #ffb4ab;
--md-sys-color-on-error: #690005;
--md-sys-color-error-container: #93000a;
--md-sys-color-on-error-container: #ffdad6;

/* Material Design 3 - Dark Theme Outline Colors */
--md-sys-color-outline: #8e9099;
--md-sys-color-outline-variant: #44474f;
```

#### Theme Variables (Dynamic)
```css
/* These variables change based on current theme */
:root {
  --primary-50: var(--light-primary-50);
  --primary-500: var(--light-primary-500);
  --bg-primary: var(--light-bg-primary);
  --text-primary: var(--light-text-primary);
  /* ... other variables */
}

[data-theme="dark"] {
  --primary-50: var(--dark-primary-50);
  --primary-500: var(--dark-primary-500);
  --bg-primary: var(--dark-bg-primary);
  --text-primary: var(--dark-text-primary);
  /* ... other variables */
}
```

#### Semantic Colors
```css
/* Success */
--success-light: #81c784;
--success-main: #4caf50;
--success-dark: #388e3c;

/* Warning */
--warning-light: #ffb74d;
--warning-main: #ff9800;
--warning-dark: #f57c00;

/* Error */
--error-light: #e57373;
--error-main: #f44336;
--error-dark: #d32f2f;

/* Info */
--info-light: #64b5f6;
--info-main: #2196f3;
--info-dark: #1976d2;
```

### Typography

#### Font Stack
```css
/* English Fonts */
--font-en-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
--font-en-secondary: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
--font-en-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

/* Bengali Fonts */
--font-bn-primary: 'Hind Siliguri', 'Noto Sans Bengali', 'SolaimanLipi', sans-serif;
--font-bn-secondary: 'Noto Sans Bengali', 'Kalpurush', 'SolaimanLipi', sans-serif;
--font-bn-mono: 'Noto Sans Bengali Mono', 'SolaimanLipi', monospace;

/* Dynamic Font Variables (changes based on language) */
--font-primary: var(--font-en-primary);
--font-secondary: var(--font-en-secondary);
--font-mono: var(--font-en-mono);

/* Language-specific font application */
[lang="bn-BD"] {
  --font-primary: var(--font-bn-primary);
  --font-secondary: var(--font-bn-secondary);
  --font-mono: var(--font-bn-mono);
}
```

#### Material Design 3 Typography Scale
```css
/* Material Design 3 Typography Scale */
--md-sys-typescale-display-large-font-size: 3.5rem;     /* 56px */
--md-sys-typescale-display-large-line-height: 4rem;     /* 64px */
--md-sys-typescale-display-large-font-weight: 400;

--md-sys-typescale-display-medium-font-size: 2.8125rem; /* 45px */
--md-sys-typescale-display-medium-line-height: 3.25rem; /* 52px */
--md-sys-typescale-display-medium-font-weight: 400;

--md-sys-typescale-display-small-font-size: 2.25rem;    /* 36px */
--md-sys-typescale-display-small-line-height: 2.75rem;  /* 44px */
--md-sys-typescale-display-small-font-weight: 400;

--md-sys-typescale-headline-large-font-size: 2rem;      /* 32px */
--md-sys-typescale-headline-large-line-height: 2.5rem;  /* 40px */
--md-sys-typescale-headline-large-font-weight: 400;

--md-sys-typescale-headline-medium-font-size: 1.75rem;  /* 28px */
--md-sys-typescale-headline-medium-line-height: 2.25rem;/* 36px */
--md-sys-typescale-headline-medium-font-weight: 400;

--md-sys-typescale-headline-small-font-size: 1.5rem;    /* 24px */
--md-sys-typescale-headline-small-line-height: 2rem;    /* 32px */
--md-sys-typescale-headline-small-font-weight: 400;

--md-sys-typescale-title-large-font-size: 1.375rem;     /* 22px */
--md-sys-typescale-title-large-line-height: 1.75rem;    /* 28px */
--md-sys-typescale-title-large-font-weight: 400;

--md-sys-typescale-title-medium-font-size: 1rem;        /* 16px */
--md-sys-typescale-title-medium-line-height: 1.5rem;    /* 24px */
--md-sys-typescale-title-medium-font-weight: 500;

--md-sys-typescale-title-small-font-size: 0.875rem;     /* 14px */
--md-sys-typescale-title-small-line-height: 1.25rem;    /* 20px */
--md-sys-typescale-title-small-font-weight: 500;

--md-sys-typescale-body-large-font-size: 1rem;          /* 16px */
--md-sys-typescale-body-large-line-height: 1.5rem;      /* 24px */
--md-sys-typescale-body-large-font-weight: 400;

--md-sys-typescale-body-medium-font-size: 0.875rem;     /* 14px */
--md-sys-typescale-body-medium-line-height: 1.25rem;    /* 20px */
--md-sys-typescale-body-medium-font-weight: 400;

--md-sys-typescale-body-small-font-size: 0.75rem;       /* 12px */
--md-sys-typescale-body-small-line-height: 1rem;        /* 16px */
--md-sys-typescale-body-small-font-weight: 400;

--md-sys-typescale-label-large-font-size: 0.875rem;     /* 14px */
--md-sys-typescale-label-large-line-height: 1.25rem;    /* 20px */
--md-sys-typescale-label-large-font-weight: 500;

--md-sys-typescale-label-medium-font-size: 0.75rem;     /* 12px */
--md-sys-typescale-label-medium-line-height: 1rem;      /* 16px */
--md-sys-typescale-label-medium-font-weight: 500;

--md-sys-typescale-label-small-font-size: 0.6875rem;    /* 11px */
--md-sys-typescale-label-small-line-height: 1rem;       /* 16px */
--md-sys-typescale-label-small-font-weight: 500;
```

### Spacing System

#### Spacing Scale (8px base unit)
```css
--space-1: 0.25rem;  /* 4px */
--space-2: 0.5rem;   /* 8px */
--space-3: 0.75rem;  /* 12px */
--space-4: 1rem;     /* 16px */
--space-5: 1.25rem;  /* 20px */
--space-6: 1.5rem;   /* 24px */
--space-8: 2rem;     /* 32px */
--space-10: 2.5rem;  /* 40px */
--space-12: 3rem;    /* 48px */
--space-16: 4rem;    /* 64px */
--space-20: 5rem;    /* 80px */
--space-24: 6rem;    /* 96px */
```

#### Layout Spacing
```css
/* Component Spacing */
--spacing-xs: var(--space-2);  /* 8px */
--spacing-sm: var(--space-4);  /* 16px */
--spacing-md: var(--space-6);  /* 24px */
--spacing-lg: var(--space-8);  /* 32px */
--spacing-xl: var(--space-12); /* 48px */

/* Section Spacing */
--section-spacing-sm: var(--space-16); /* 64px */
--section-spacing-md: var(--space-20); /* 80px */
--section-spacing-lg: var(--space-24); /* 96px */
```

### Border Radius
```css
--radius-sm: 0.25rem;  /* 4px - Small elements */
--radius-md: 0.5rem;   /* 8px - Default */
--radius-lg: 0.75rem;  /* 12px - Cards */
--radius-xl: 1rem;     /* 16px - Large cards */
--radius-full: 9999px; /* Fully rounded */
```

### Shadows
```css
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
```

## Material Design 3 Component Library

### Material Design 3 Buttons

#### Filled Button (Primary)
```css
.md-filled-button {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  padding: 10px 24px;
  border-radius: 20px;
  border: none;
  font-size: var(--md-sys-typescale-label-large-font-size);
  font-weight: var(--md-sys-typescale-label-large-font-weight);
  line-height: var(--md-sys-typescale-label-large-line-height);
  min-height: 40px;
  transition: all 0.2s cubic-bezier(0.2, 0, 0, 1);
  cursor: pointer;
}

.md-filled-button:hover {
  background-color: color-mix(in srgb, var(--md-sys-color-primary) 92%, var(--md-sys-color-on-primary) 8%);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3), 0 1px 3px 1px rgba(0, 0, 0, 0.15);
}

.md-filled-button:focus {
  background-color: color-mix(in srgb, var(--md-sys-color-primary) 88%, var(--md-sys-color-on-primary) 12%);
  outline: none;
}

.md-filled-button:active {
  background-color: color-mix(in srgb, var(--md-sys-color-primary) 88%, var(--md-sys-color-on-primary) 12%);
}
```

#### Outlined Button (Secondary)
```css
.md-outlined-button {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  border: 1px solid var(--md-sys-color-outline);
  padding: 10px 24px;
  border-radius: 20px;
  font-size: var(--md-sys-typescale-label-large-font-size);
  font-weight: var(--md-sys-typescale-label-large-font-weight);
  line-height: var(--md-sys-typescale-label-large-line-height);
  min-height: 40px;
  transition: all 0.2s cubic-bezier(0.2, 0, 0, 1);
  cursor: pointer;
}

.md-outlined-button:hover {
  background-color: color-mix(in srgb, var(--md-sys-color-primary) 8%, transparent);
  border-color: var(--md-sys-color-primary);
}

.md-outlined-button:focus {
  background-color: color-mix(in srgb, var(--md-sys-color-primary) 12%, transparent);
  border-color: var(--md-sys-color-primary);
  outline: none;
}
```

#### Text Button (Tertiary)
```css
.md-text-button {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  border: none;
  padding: 10px 12px;
  border-radius: 20px;
  font-size: var(--md-sys-typescale-label-large-font-size);
  font-weight: var(--md-sys-typescale-label-large-font-weight);
  line-height: var(--md-sys-typescale-label-large-line-height);
  min-height: 40px;
  transition: all 0.2s cubic-bezier(0.2, 0, 0, 1);
  cursor: pointer;
}

.md-text-button:hover {
  background-color: color-mix(in srgb, var(--md-sys-color-primary) 8%, transparent);
}
```

### Form Elements

#### Input Fields
```css
.form-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  font-size: var(--text-body);
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.form-input.error {
  border-color: var(--error-main);
}
```

#### Labels
```css
.form-label {
  display: block;
  font-size: var(--text-body-sm);
  font-weight: 500;
  color: var(--neutral-700);
  margin-bottom: var(--space-2);
}

.form-label.required::after {
  content: ' *';
  color: var(--error-main);
}
```

### Cards

#### Basic Card
```css
.card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--space-6);
  border: 1px solid var(--neutral-200);
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}
```

#### Dashboard Card
```css
.dashboard-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--space-6);
  border-left: 4px solid var(--primary-500);
}

.dashboard-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.dashboard-card-title {
  font-size: var(--text-h4);
  font-weight: 600;
  color: var(--neutral-800);
}

.dashboard-card-value {
  font-size: var(--text-h2);
  font-weight: 700;
  color: var(--primary-600);
}
```

## Layout System

### Grid System
```css
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-12 { grid-template-columns: repeat(12, 1fr); }
```

### Responsive Breakpoints
```css
/* Mobile First Approach */
--breakpoint-sm: 640px;   /* Small devices */
--breakpoint-md: 768px;   /* Medium devices */
--breakpoint-lg: 1024px;  /* Large devices */
--breakpoint-xl: 1280px;  /* Extra large devices */
--breakpoint-2xl: 1536px; /* 2X large devices */
```

## Navigation Patterns

### Primary Navigation
- Horizontal navigation bar for desktop
- Collapsible hamburger menu for mobile
- Clear visual hierarchy with active states
- Breadcrumb navigation for deep pages

### Secondary Navigation
- Sidebar navigation for admin sections
- Tab navigation for related content
- Pagination for data tables
- Filter and search interfaces

## Data Display Patterns

### Tables
- Responsive design with horizontal scroll
- Sortable columns with clear indicators
- Row hover states for better scanning
- Pagination and row count display

### Forms
- Logical grouping with fieldsets
- Clear validation messages
- Progressive disclosure for complex forms
- Auto-save functionality where appropriate

### Dashboards
- Card-based layout for metrics
- Interactive charts and graphs
- Responsive grid system
- Real-time data updates

## Accessibility Guidelines

### Keyboard Navigation
- Tab order follows logical flow
- All interactive elements are focusable
- Skip links for main content
- Escape key closes modals and dropdowns

### Screen Reader Support
- Semantic HTML structure
- ARIA labels and descriptions
- Alt text for images
- Form labels properly associated

### Color and Contrast
- Minimum 4.5:1 contrast ratio for normal text
- Minimum 3:1 contrast ratio for large text
- Color is not the only way to convey information
- Focus indicators are clearly visible

## Implementation Guidelines

### CSS Architecture
- Use CSS custom properties for theming
- Follow BEM methodology for class naming
- Implement utility classes for common patterns
- Use CSS Grid and Flexbox for layouts

### Component Development
- Create reusable Angular components
- Implement proper TypeScript interfaces
- Follow Angular style guide conventions
- Include comprehensive unit tests

### Performance Optimization
- Lazy load images and components
- Minimize CSS and JavaScript bundles
- Use efficient selectors and animations
- Implement proper caching strategies

## Internationalization (i18n)

### Language Support
The system supports two languages with full localization:

#### Bengali-Bangladesh (bn-BD)
- **Primary Language**: Bengali script (বাংলা)
- **Locale**: Bangladesh (BD)
- **Text Direction**: Left-to-right (LTR)
- **Number Format**: Bengali numerals and English numerals
- **Date Format**: DD/MM/YYYY (Bengali calendar support)
- **Currency**: Bangladeshi Taka (৳)

#### English-US (en-US)
- **Primary Language**: English
- **Locale**: United States (US)
- **Text Direction**: Left-to-right (LTR)
- **Number Format**: 1,234.56
- **Date Format**: MM/DD/YYYY
- **Currency**: US Dollar ($)

### Implementation Guidelines

#### Language Switching
```typescript
// Language service implementation
export class LanguageService {
  private currentLanguage = 'en-US';

  setLanguage(lang: 'en-US' | 'bn-BD') {
    this.currentLanguage = lang;
    document.documentElement.lang = lang;
    document.documentElement.dir = 'ltr'; // Both languages are LTR
    this.loadLanguageResources(lang);
  }
}
```

#### Text Content Guidelines
- **Key-based Translation**: Use descriptive keys for all text
- **Context Awareness**: Provide context for translators
- **Pluralization**: Handle singular/plural forms correctly
- **Variable Interpolation**: Support dynamic content insertion

#### Cultural Considerations
- **Bengali Typography**: Proper line spacing for Bengali text
- **Number Systems**: Support both Bengali and English numerals
- **Date Formats**: Respect local date conventions
- **Educational Terms**: Use appropriate academic terminology

## Theme System

### Theme Implementation

#### Theme Switching
```typescript
// Theme service implementation
export class ThemeService {
  private currentTheme: 'light' | 'dark' = 'light';

  setTheme(theme: 'light' | 'dark') {
    this.currentTheme = theme;
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('preferred-theme', theme);
  }

  initializeTheme() {
    const savedTheme = localStorage.getItem('preferred-theme');
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    this.setTheme(savedTheme as any || systemTheme);
  }
}
```

#### Theme-Aware Components
```css
/* Button component with theme support */
.btn-primary {
  background-color: var(--primary-500);
  color: var(--bg-primary);
  border: 1px solid var(--primary-500);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Theme-specific adjustments */
[data-theme="dark"] .btn-primary {
  box-shadow: 0 2px 8px rgba(66, 165, 245, 0.3);
}
```

#### Dark Theme Considerations
- **Contrast Ratios**: Maintain WCAG compliance in dark mode
- **Color Intensity**: Reduce bright colors to prevent eye strain
- **Surface Elevation**: Use subtle shadows and borders
- **Image Handling**: Provide dark-appropriate images where needed

### Responsive Design for Languages

#### Bengali Text Considerations
```css
/* Bengali-specific typography adjustments */
[lang="bn-BD"] {
  line-height: 1.6; /* Increased for Bengali characters */
  letter-spacing: 0.02em; /* Slight spacing for readability */
}

[lang="bn-BD"] .text-small {
  font-size: 0.9rem; /* Slightly larger for Bengali readability */
}

[lang="bn-BD"] .form-input {
  padding: 0.75rem 1rem; /* More padding for Bengali text */
}
```

#### Language-Specific Layouts
```css
/* Adjust layouts for different text lengths */
.nav-item {
  min-width: 120px; /* Accommodate longer Bengali text */
}

[lang="bn-BD"] .nav-item {
  min-width: 140px; /* Extra space for Bengali */
}

[lang="bn-BD"] .btn {
  padding: 0.75rem 1.5rem; /* More padding for Bengali buttons */
}
```

## Accessibility for Multilingual and Multi-theme

### Screen Reader Support
- **Language Announcement**: Proper lang attributes for content sections
- **Theme Changes**: Announce theme changes to screen readers
- **Cultural Context**: Provide context for cultural-specific content

### Keyboard Navigation
- **Consistent Patterns**: Same keyboard shortcuts across languages
- **Theme Switching**: Keyboard shortcut for theme toggle
- **Language Switching**: Accessible language selection

### Color and Contrast
- **Theme Compliance**: Both themes meet WCAG 2.1 AA standards
- **Language Visibility**: Ensure text is readable in both languages
- **Cultural Colors**: Respect cultural color meanings

This comprehensive design system ensures a professional, accessible, and culturally appropriate experience for all users across both languages and themes.
