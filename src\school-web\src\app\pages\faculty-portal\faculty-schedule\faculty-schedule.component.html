<div class="schedule-container">
  <h1 class="page-title">Class Schedule</h1>

  <div *ngIf="loading.faculty" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error.faculty" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load faculty data. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadFacultyData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading.faculty && !error.faculty && faculty" class="schedule-content">
    <!-- Day Selection -->
    <mat-card class="day-selection-card">
      <mat-card-content>
        <div class="day-selection">
          <mat-form-field appearance="outline">
            <mat-label>Select Day</mat-label>
            <mat-select [(ngModel)]="selectedDay" (selectionChange)="onDayChange()">
              <mat-option *ngFor="let day of days" [value]="day.value">
                {{ day.label }} {{ isCurrentDay(day.value) ? '(Today)' : '' }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Loading Indicator -->
    <div *ngIf="loading.schedule" class="schedule-loading">
      <mat-progress-bar mode="indeterminate"></mat-progress-bar>
    </div>

    <!-- Error Message -->
    <div *ngIf="error.schedule" class="schedule-error">
      <mat-error>
        <mat-icon>error</mat-icon>
        <span>Failed to load schedule. Please try again.</span>
        <button mat-button color="warn" (click)="loadSchedule()">Retry</button>
      </mat-error>
    </div>

    <!-- No Schedule Message -->
    <div *ngIf="!loading.schedule && !error.schedule && schedule.length === 0" class="no-schedule">
      <mat-card>
        <mat-card-content>
          <p>No classes scheduled for {{ getDayName(selectedDay) }}.</p>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Schedule Timeline -->
    <div *ngIf="!loading.schedule && !error.schedule && schedule.length > 0" class="schedule-timeline">
      <div class="timeline-header">
        <h2>{{ getDayName(selectedDay) }} Schedule</h2>
      </div>
      
      <div class="timeline">
        <div *ngFor="let period of schedule" class="timeline-item">
          <div class="timeline-badge">
            <span class="period-number">{{ period.periodNumber }}</span>
          </div>
          
          <mat-card class="timeline-card">
            <mat-card-header>
              <mat-card-title>{{ period.subjectName }}</mat-card-title>
              <mat-card-subtitle>
                {{ period.startTime }} - {{ period.endTime }}
              </mat-card-subtitle>
            </mat-card-header>
            
            <mat-card-content>
              <div class="period-details">
                <div class="detail-item">
                  <span class="detail-label">Class:</span>
                  <span class="detail-value">{{ period.grade }}-{{ period.section }}</span>
                </div>
                
                <div class="detail-item">
                  <span class="detail-label">Subject Code:</span>
                  <span class="detail-value">{{ period.subjectCode }}</span>
                </div>
                
                <div class="detail-item">
                  <span class="detail-label">Room:</span>
                  <span class="detail-value">{{ period.roomNumber }}</span>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
  </div>
</div>
