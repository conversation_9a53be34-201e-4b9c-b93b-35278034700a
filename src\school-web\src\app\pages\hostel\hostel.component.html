<div class="hostel-container">
  <div class="page-header">
    <h1>{{ 'HOSTEL.TITLE' | translate }}</h1>
    <p class="page-description">{{ 'HOSTEL.DESCRIPTION' | translate }}</p>
  </div>

  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <div class="error-container" *ngIf="error">
    <p class="error-message">{{ error }}</p>
    <button mat-raised-button color="primary" (click)="loadHostelFacilities()">
      {{ 'COMMON.TRY_AGAIN' | translate }}
    </button>
  </div>

  <div class="hostel-content" *ngIf="!isLoading && !error">
    <div class="intro-section">
      <div class="intro-text">
        <h2>{{ 'HOSTEL.INTRO_TITLE' | translate }}</h2>
        <p>{{ 'HOSTEL.INTRO_TEXT' | translate }}</p>
        <ul class="feature-list">
          <li>
            <mat-icon>security</mat-icon>
            <span>{{ 'HOSTEL.FEATURES.SECURITY' | translate }}</span>
          </li>
          <li>
            <mat-icon>restaurant</mat-icon>
            <span>{{ 'HOSTEL.FEATURES.MEALS' | translate }}</span>
          </li>
          <li>
            <mat-icon>local_laundry_service</mat-icon>
            <span>{{ 'HOSTEL.FEATURES.LAUNDRY' | translate }}</span>
          </li>
          <li>
            <mat-icon>wifi</mat-icon>
            <span>{{ 'HOSTEL.FEATURES.WIFI' | translate }}</span>
          </li>
          <li>
            <mat-icon>local_library</mat-icon>
            <span>{{ 'HOSTEL.FEATURES.STUDY_ROOM' | translate }}</span>
          </li>
          <li>
            <mat-icon>sports_esports</mat-icon>
            <span>{{ 'HOSTEL.FEATURES.RECREATION' | translate }}</span>
          </li>
        </ul>
      </div>
      <div class="intro-image">
        <img src="assets/images/hostel-main.jpg" alt="Hostel Building">
      </div>
    </div>

    <mat-tab-group animationDuration="0ms" mat-stretch-tabs="false" mat-align-tabs="start">
      <mat-tab *ngFor="let gender of facilitiesByGender | keyvalue" [label]="getGenderLabel(+gender.key)">
        <div class="facilities-container">
          <h2>{{ getGenderLabel(+gender.key) }} {{ 'HOSTEL.FACILITIES' | translate }}</h2>

          <div class="facilities-grid">
            <mat-card class="facility-card" *ngFor="let facility of gender.value">
              <mat-card-header>
                <mat-card-title>{{ getTranslation(facility, 'name') }}</mat-card-title>
                <mat-card-subtitle>
                  <mat-chip-listbox>
                    <mat-chip>{{ getHostelTypeLabel(facility.type) }}</mat-chip>
                  </mat-chip-listbox>
                </mat-card-subtitle>
              </mat-card-header>

              <div class="facility-images" *ngIf="facility.images && facility.images.length > 0">
                <img [src]="facility.images[0].mediaItem.filePath" [alt]="facility.images[0].mediaItem.altText || getTranslation(facility, 'name')">
              </div>

              <mat-card-content>
                <p class="facility-description">{{ getTranslation(facility, 'description') }}</p>

                <div class="facility-details">
                  <div class="detail-item">
                    <mat-icon>people</mat-icon>
                    <span>{{ 'HOSTEL.CAPACITY' | translate }}: {{ facility.capacity }}</span>
                  </div>
                  <div class="detail-item">
                    <mat-icon>payments</mat-icon>
                    <span>{{ 'HOSTEL.MONTHLY_FEE' | translate }}: {{ formatAmount(facility.monthlyFee) }}</span>
                  </div>
                  <div class="detail-item">
                    <mat-icon>location_on</mat-icon>
                    <span>{{ 'HOSTEL.LOCATION' | translate }}: {{ facility.location }}</span>
                  </div>
                </div>

                <div class="facility-amenities">
                  <h4>{{ 'HOSTEL.AMENITIES' | translate }}</h4>
                  <div class="amenities-grid">
                    <div class="amenity-item" [class.available]="facility.hasWifi">
                      <mat-icon>{{ facility.hasWifi ? 'check_circle' : 'cancel' }}</mat-icon>
                      <span>{{ 'HOSTEL.AMENITY.WIFI' | translate }}</span>
                    </div>
                    <div class="amenity-item" [class.available]="facility.hasAC">
                      <mat-icon>{{ facility.hasAC ? 'check_circle' : 'cancel' }}</mat-icon>
                      <span>{{ 'HOSTEL.AMENITY.AC' | translate }}</span>
                    </div>
                    <div class="amenity-item" [class.available]="facility.hasMeals">
                      <mat-icon>{{ facility.hasMeals ? 'check_circle' : 'cancel' }}</mat-icon>
                      <span>{{ 'HOSTEL.AMENITY.MEALS' | translate }}</span>
                    </div>
                    <div class="amenity-item" [class.available]="facility.hasLaundry">
                      <mat-icon>{{ facility.hasLaundry ? 'check_circle' : 'cancel' }}</mat-icon>
                      <span>{{ 'HOSTEL.AMENITY.LAUNDRY' | translate }}</span>
                    </div>
                    <div class="amenity-item" [class.available]="facility.hasStudyRoom">
                      <mat-icon>{{ facility.hasStudyRoom ? 'check_circle' : 'cancel' }}</mat-icon>
                      <span>{{ 'HOSTEL.AMENITY.STUDY_ROOM' | translate }}</span>
                    </div>
                    <div class="amenity-item" [class.available]="facility.hasRecreationRoom">
                      <mat-icon>{{ facility.hasRecreationRoom ? 'check_circle' : 'cancel' }}</mat-icon>
                      <span>{{ 'HOSTEL.AMENITY.RECREATION_ROOM' | translate }}</span>
                    </div>
                  </div>
                </div>
              </mat-card-content>

              <mat-card-actions align="end">
                <button mat-raised-button color="primary">
                  {{ 'HOSTEL.VIEW_DETAILS' | translate }}
                </button>
              </mat-card-actions>
            </mat-card>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>

    <div class="application-section">
      <h2>{{ 'HOSTEL.APPLICATION_PROCESS.TITLE' | translate }}</h2>
      <p>{{ 'HOSTEL.APPLICATION_PROCESS.DESCRIPTION' | translate }}</p>

      <mat-accordion>
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'HOSTEL.APPLICATION_PROCESS.STEP1.TITLE' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <p>{{ 'HOSTEL.APPLICATION_PROCESS.STEP1.DESCRIPTION' | translate }}</p>
        </mat-expansion-panel>

        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'HOSTEL.APPLICATION_PROCESS.STEP2.TITLE' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <p>{{ 'HOSTEL.APPLICATION_PROCESS.STEP2.DESCRIPTION' | translate }}</p>
        </mat-expansion-panel>

        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'HOSTEL.APPLICATION_PROCESS.STEP3.TITLE' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <p>{{ 'HOSTEL.APPLICATION_PROCESS.STEP3.DESCRIPTION' | translate }}</p>
        </mat-expansion-panel>

        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'HOSTEL.APPLICATION_PROCESS.STEP4.TITLE' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <p>{{ 'HOSTEL.APPLICATION_PROCESS.STEP4.DESCRIPTION' | translate }}</p>
        </mat-expansion-panel>
      </mat-accordion>

      <div class="application-cta">
        <button mat-raised-button color="primary">
          {{ 'HOSTEL.APPLY_NOW' | translate }}
        </button>
      </div>
    </div>

    <div class="contact-section">
      <h2>{{ 'HOSTEL.CONTACT.TITLE' | translate }}</h2>
      <p>{{ 'HOSTEL.CONTACT.DESCRIPTION' | translate }}</p>

      <div class="contact-info">
        <div class="contact-item">
          <mat-icon>phone</mat-icon>
          <div>
            <h4>{{ 'HOSTEL.CONTACT.PHONE' | translate }}</h4>
            <p>+880 1234-567890</p>
          </div>
        </div>

        <div class="contact-item">
          <mat-icon>email</mat-icon>
          <div>
            <h4>{{ 'HOSTEL.CONTACT.EMAIL' | translate }}</h4>
            <p>hostel&#64;school.edu.bd</p>
          </div>
        </div>

        <div class="contact-item">
          <mat-icon>location_on</mat-icon>
          <div>
            <h4>{{ 'HOSTEL.CONTACT.ADDRESS' | translate }}</h4>
            <p>123 School Road, Dhaka, Bangladesh</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
