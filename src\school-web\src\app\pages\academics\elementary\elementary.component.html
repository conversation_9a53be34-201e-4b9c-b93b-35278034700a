<!-- Hero Section -->
<app-default-hero
  translationPrefix="ACADEMICS"
  title="ACADEMICS.ELEMENTARY"
  subtitle="ACADEMICS.ELEMENTARY_SUBTITLE"
  theme="light"
  size="large"
  alignment="center"
  backgroundImage="assets/images/elementary-hero.jpg">
</app-default-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'ACADEMICS.ELEMENTARY_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'ACADEMICS.ELEMENTARY_INTRO_P1' | translate }}</p>
      <p>{{ 'ACADEMICS.ELEMENTARY_INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Key Features Section -->
  <section class="features-section">
    <h2>{{ 'ACADEMICS.KEY_FEATURES' | translate }}</h2>
    <div class="features-grid">
      <mat-card class="feature-card" *ngFor="let feature of keyFeatures">
        <div class="feature-icon">
          <mat-icon>{{feature.icon}}</mat-icon>
        </div>
        <mat-card-content>
          <h3>{{feature.title}}</h3>
          <p>{{feature.description}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Grade Levels Section -->
  <section class="grade-levels-section">
    <h2>{{ 'ACADEMICS.GRADE_LEVELS' | translate }}</h2>
    <div class="grade-levels-container">
      <mat-tab-group animationDuration="300ms">
        <mat-tab *ngFor="let grade of gradeLevels" [label]="grade.name">
          <div class="grade-content">
            <p class="grade-description">{{grade.description}}</p>

            <h3>{{ 'ACADEMICS.SUBJECTS' | translate }}</h3>
            <div class="subjects-list">
              <div class="subject-item" *ngFor="let subject of grade.subjects">
                <mat-icon>school</mat-icon>
                <span>{{subject}}</span>
              </div>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </section>

  <!-- Curriculum Section -->
  <section class="curriculum-section">
    <h2>{{ 'ACADEMICS.CURRICULUM' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.ELEMENTARY_CURRICULUM_INTRO' | translate }}</p>

    <div class="courses-container">
      <h3>{{ 'ACADEMICS.SAMPLE_COURSES' | translate }}</h3>
      <div class="courses-grid">
        <mat-card class="course-card" *ngFor="let course of sampleCourses">
          <mat-card-content>
            <h4>{{course.name}}</h4>
            <div class="course-grade">{{course.grade}}</div>
            <p>{{course.description}}</p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </section>

  <!-- Special Programs Section -->
  <section class="special-programs-section">
    <h2>{{ 'ACADEMICS.SPECIAL_PROGRAMS' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.ELEMENTARY_SPECIAL_PROGRAMS_INTRO' | translate }}</p>

    <div class="programs-grid">
      <mat-card class="program-card" *ngFor="let program of specialPrograms">
        <div class="program-image">
          <img [src]="program.image" [alt]="program.title">
        </div>
        <mat-card-content>
          <h3>{{program.title}}</h3>
          <p>{{program.description}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Daily Schedule Section -->
  <section class="schedule-section">
    <h2>{{ 'ACADEMICS.DAILY_SCHEDULE' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.SCHEDULE_INTRO' | translate }}</p>

    <div class="schedule-container">
      <div class="schedule-item" *ngFor="let item of sampleSchedule">
        <div class="schedule-time">{{item.time}}</div>
        <div class="schedule-activity">{{item.activity}}</div>
      </div>
    </div>

    <div class="schedule-note">
      <mat-icon>info</mat-icon>
      <p>{{ 'ACADEMICS.SCHEDULE_NOTE' | translate }}</p>
    </div>
  </section>

  <!-- Faculty Section -->
  <section class="faculty-section">
    <h2>{{ 'ACADEMICS.FACULTY' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.ELEMENTARY_FACULTY_INTRO' | translate }}</p>

    <div class="faculty-cta">
      <a mat-raised-button color="primary" routerLink="/faculty">
        {{ 'ACADEMICS.MEET_FACULTY' | translate }}
      </a>
    </div>
  </section>

  <!-- Call to Action Section -->
  <section class="cta-section">
    <div class="cta-content">
      <h2>{{ 'ACADEMICS.VISIT_ELEMENTARY' | translate }}</h2>
      <p>{{ 'ACADEMICS.VISIT_ELEMENTARY_TEXT' | translate }}</p>
      <div class="cta-buttons">
        <a mat-raised-button color="primary" routerLink="/admissions">
          {{ 'ACADEMICS.APPLY_NOW' | translate }}
        </a>
        <a mat-stroked-button color="primary" routerLink="/contact">
          {{ 'ACADEMICS.SCHEDULE_TOUR' | translate }}
        </a>
      </div>
    </div>
  </section>
</div>
