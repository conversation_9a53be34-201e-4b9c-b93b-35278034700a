<!-- Enhanced Hero Carousel Section -->
<app-enhanced-hero-carousel
  [slides]="enhancedHeroSlides"
  [autoPlay]="true"
  [interval]="5000"
  [showControls]="true"
  [showIndicators]="true"
  [theme]="'dark'"
  [size]="'large'">
  <div class="hero-buttons">
    <a mat-raised-button color="primary" routerLink="/admissions">Apply Now</a>
    <a mat-stroked-button color="primary" routerLink="/about">Discover SUMA</a>
  </div>
</app-enhanced-hero-carousel>

<!-- Key Features -->
<section class="features">
  <h2>Why Choose SUMA</h2>
  <div class="features-grid">
    <div class="feature-card">
      <mat-icon>school</mat-icon>
      <h3>Academic Excellence</h3>
      <p>Rigorous curriculum with outstanding academic achievements and dedicated faculty mentorship.</p>
    </div>
    <div class="feature-card">
      <mat-icon>psychology</mat-icon>
      <h3>Character Development</h3>
      <p>Focus on moral values, leadership skills, and personal growth through holistic education.</p>
    </div>
    <div class="feature-card">
      <mat-icon>diversity_3</mat-icon>
      <h3>Inclusive Community</h3>
      <p>Diverse and supportive environment fostering cultural awareness and mutual respect.</p>
    </div>
    <div class="feature-card">
      <mat-icon>emoji_events</mat-icon>
      <h3>Student Success</h3>
      <p>Track record of graduates excelling in prestigious universities and diverse career paths.</p>
    </div>
  </div>
</section>

<!-- Latest News -->
<section class="news">
  <h2>Latest News</h2>
  <div class="news-grid">
    <mat-card class="news-card" *ngFor="let news of latestNews">
      <img mat-card-image [src]="news.image" [alt]="news.title">
      <mat-card-content>
        <h3>{{news.title}}</h3>
        <p class="date">
          <mat-icon>event</mat-icon>
          {{news.date | date}}
        </p>
        <p>{{news.excerpt}}</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary">Read More</button>
      </mat-card-actions>
    </mat-card>
  </div>
</section>

<!-- Stats Section -->
<section class="stats">
  <div class="stats-grid">
    <div class="stat-item">
      <div class="stat-number">25+</div>
      <div class="stat-label">Years of Excellence</div>
    </div>
    <div class="stat-item">
      <div class="stat-number">95%</div>
      <div class="stat-label">University Acceptance</div>
    </div>
    <div class="stat-item">
      <div class="stat-number">50+</div>
      <div class="stat-label">Expert Faculty</div>
    </div>
    <div class="stat-item">
      <div class="stat-number">1000+</div>
      <div class="stat-label">Successful Alumni</div>
    </div>
  </div>
</section>

<!-- Call to Action -->
<section class="cta">
  <div class="cta-content">
    <h2>Begin Your Journey at SUMA</h2>
    <p>Join our community of learners and discover your potential in an environment that nurtures excellence</p>
    <div class="cta-buttons">
      <a mat-raised-button color="primary" routerLink="/admissions">Apply Now</a>
      <a mat-stroked-button color="primary" routerLink="/contact">Schedule a Visit</a>
    </div>
  </div>
</section>
