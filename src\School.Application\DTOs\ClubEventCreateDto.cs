using System;
using System.Collections.Generic;

namespace School.Application.DTOs;

public class ClubEventCreateDto
{
    public string Title { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public string Time { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public List<ClubEventTranslationCreateDto> Translations { get; set; } = new();
}

public class ClubEventTranslationCreateDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}
