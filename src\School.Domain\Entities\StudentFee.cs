using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class StudentFee : BaseEntity
{
    public Guid StudentId { get; set; }
    public FeeType Type { get; set; }
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal DueAmount { get; set; }
    public DateTime DueDate { get; set; }
    public DateTime? PaidDate { get; set; }
    public PaymentStatus Status { get; set; }
    public string TransactionId { get; set; } = string.Empty;
    public string PaymentMethod { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    
    // Navigation properties
    public Student Student { get; set; } = null!;
    public DateTime UpdatedAt { get; set; }
}
