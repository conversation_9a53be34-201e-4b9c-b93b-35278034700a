import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatFormFieldModule, MatError } from '@angular/material/form-field';

// Services and models
import { StudentService } from '../../../core/services/student.service';
import { AuthService } from '../../../core/services/auth.service';
import { Student, StudentAcademicHistory, TeachingMedium, ShiftType } from '../../../core/models/student.model';

@Component({
  selector: 'app-student-academic-history',
  templateUrl: './student-academic-history.component.html',
  styleUrls: ['./student-academic-history.component.scss'],
  standalone: true,
  imports: [
    CommonModule,

    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatSnackBarModule,
    MatFormFieldModule,
    MatError
  ]
})
export class StudentAcademicHistoryComponent implements OnInit {
  student: Student | null = null;
  academicHistory: StudentAcademicHistory[] = [];

  loading = {
    student: true,
    history: false
  };

  error = {
    student: false,
    history: false
  };

  constructor(
    private studentService: StudentService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadStudentData();
  }

  loadStudentData(): void {
    this.loading.student = true;

    // In a real application, you would fetch the student by user ID
    // For now, we'll use a mock student ID
    this.studentService.getStudentByStudentId('S2023-001')
      .subscribe({
        next: (student) => {
          this.student = student;
          this.loading.student = false;
          this.loadAcademicHistory();
        },
        error: (err) => {
          console.error('Error loading student data:', err);
          this.error.student = true;
          this.loading.student = false;
          this.snackBar.open('Failed to load student data', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  loadAcademicHistory(): void {
    if (!this.student) return;

    this.loading.history = true;
    this.error.history = false;

    this.studentService.getStudentAcademicHistory(this.student.id)
      .subscribe({
        next: (history) => {
          this.academicHistory = history.sort((a, b) => b.academicYear - a.academicYear);
          this.loading.history = false;
        },
        error: (err) => {
          console.error('Error loading academic history:', err);
          this.error.history = true;
          this.loading.history = false;
          this.snackBar.open('Failed to load academic history', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  getMediumLabel(medium: TeachingMedium): string {
    return medium === TeachingMedium.Bengali ? 'Bengali' : 'English';
  }

  getShiftLabel(shift: ShiftType): string {
    switch (shift) {
      case ShiftType.Morning: return 'Morning';
      case ShiftType.Day: return 'Day';
      case ShiftType.Evening: return 'Evening';
      default: return 'Unknown';
    }
  }

  getGradeColor(gpa: number | undefined): string {
    if (gpa === undefined) return '#757575';

    if (gpa >= 5.0) return '#2e7d32';
    if (gpa >= 4.5) return '#388e3c';
    if (gpa >= 4.0) return '#43a047';
    if (gpa >= 3.5) return '#689f38';
    if (gpa >= 3.0) return '#7cb342';
    if (gpa >= 2.5) return '#ffa000';
    if (gpa >= 2.0) return '#ffb300';
    if (gpa >= 1.0) return '#f57c00';
    return '#d32f2f';
  }

  getGradeLabel(gpa: number | undefined): string {
    if (gpa === undefined) return 'N/A';

    if (gpa >= 5.0) return 'A+';
    if (gpa >= 4.5) return 'A';
    if (gpa >= 4.0) return 'A-';
    if (gpa >= 3.5) return 'B+';
    if (gpa >= 3.0) return 'B';
    if (gpa >= 2.5) return 'C+';
    if (gpa >= 2.0) return 'C';
    if (gpa >= 1.0) return 'D';
    return 'F';
  }

  isCurrentYear(academicYear: number): boolean {
    return this.student?.academicYear === academicYear;
  }
}
