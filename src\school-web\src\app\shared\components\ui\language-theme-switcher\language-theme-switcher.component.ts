import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { LanguageService, LanguageOption } from '../../../../core/services/language.service';
import { AuthService } from '../../../../core/services/auth.service';
import { ThemeService } from '../../../../core/services/theme.service';

export type SwitcherStyle = 'compact' | 'full' | 'icon-only' | 'dropdown';
export type SwitcherTheme = 'light' | 'dark' | 'auto';

@Component({
  selector: 'app-language-theme-switcher',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatTooltipModule,
    MatDividerModule,
    MatSlideToggleModule,
    TranslateModule
  ],
  template: `
    <div class="language-theme-switcher" [class]="'style-' + style">
      <!-- Compact Style (Icon buttons side by side) -->
      <ng-container *ngIf="style === 'compact'">
        <div class="switcher-group">
          <!-- Language Switcher -->
          <button mat-icon-button 
                  [matMenuTriggerFor]="languageMenu"
                  [matTooltip]="'SWITCHER.LANGUAGE' | translate"
                  class="switcher-btn language-btn">
            <mat-icon>language</mat-icon>
            <span class="current-lang" *ngIf="showLabels">{{ getCurrentLanguage()?.flag }}</span>
          </button>
          
          <!-- Theme Switcher -->
          <button mat-icon-button 
                  (click)="toggleTheme()"
                  [matTooltip]="'SWITCHER.THEME' | translate"
                  class="switcher-btn theme-btn">
            <mat-icon>{{ currentTheme === 'dark' ? 'light_mode' : 'dark_mode' }}</mat-icon>
          </button>
        </div>
      </ng-container>

      <!-- Full Style (Buttons with text) -->
      <ng-container *ngIf="style === 'full'">
        <div class="switcher-group full-style">
          <!-- Language Switcher -->
          <button mat-button 
                  [matMenuTriggerFor]="languageMenu"
                  class="switcher-btn language-btn">
            <mat-icon>language</mat-icon>
            <span>{{ getCurrentLanguage()?.name }}</span>
            <mat-icon>arrow_drop_down</mat-icon>
          </button>
          
          <!-- Theme Toggle -->
          <div class="theme-toggle-container">
            <mat-icon>{{ currentTheme === 'dark' ? 'dark_mode' : 'light_mode' }}</mat-icon>
            <mat-slide-toggle 
              [checked]="currentTheme === 'dark'"
              (change)="toggleTheme()"
              [matTooltip]="'SWITCHER.TOGGLE_THEME' | translate">
            </mat-slide-toggle>
            <span class="theme-label">{{ getThemeLabel() }}</span>
          </div>
        </div>
      </ng-container>

      <!-- Icon Only Style -->
      <ng-container *ngIf="style === 'icon-only'">
        <div class="switcher-group icon-only">
          <button mat-icon-button 
                  [matMenuTriggerFor]="languageMenu"
                  [matTooltip]="'SWITCHER.CHANGE_LANGUAGE' | translate"
                  class="switcher-btn">
            <mat-icon>language</mat-icon>
          </button>
          
          <button mat-icon-button 
                  (click)="toggleTheme()"
                  [matTooltip]="'SWITCHER.TOGGLE_THEME' | translate"
                  class="switcher-btn">
            <mat-icon>{{ currentTheme === 'dark' ? 'light_mode' : 'dark_mode' }}</mat-icon>
          </button>
        </div>
      </ng-container>

      <!-- Dropdown Style (Combined menu) -->
      <ng-container *ngIf="style === 'dropdown'">
        <button mat-icon-button 
                [matMenuTriggerFor]="combinedMenu"
                [matTooltip]="'SWITCHER.SETTINGS' | translate"
                class="switcher-btn">
          <mat-icon>settings</mat-icon>
        </button>
      </ng-container>

      <!-- Language Menu -->
      <mat-menu #languageMenu="matMenu" class="language-menu">
        <div class="menu-header">
          <mat-icon>language</mat-icon>
          <span>{{ 'SWITCHER.SELECT_LANGUAGE' | translate }}</span>
        </div>
        <mat-divider></mat-divider>
        
        <button mat-menu-item 
                *ngFor="let language of availableLanguages"
                (click)="changeLanguage(language.code)"
                [class.active]="currentLanguage === language.code">
          <span class="language-flag">{{ language.flag }}</span>
          <span class="language-name">{{ language.name }}</span>
          <mat-icon *ngIf="currentLanguage === language.code" class="check-icon">check</mat-icon>
        </button>
      </mat-menu>

      <!-- Combined Menu (for dropdown style) -->
      <mat-menu #combinedMenu="matMenu" class="combined-menu">
        <div class="menu-header">
          <mat-icon>settings</mat-icon>
          <span>{{ 'SWITCHER.PREFERENCES' | translate }}</span>
        </div>
        <mat-divider></mat-divider>
        
        <!-- Language Section -->
        <div class="menu-section">
          <div class="section-header">
            <mat-icon>language</mat-icon>
            <span>{{ 'SWITCHER.LANGUAGE' | translate }}</span>
          </div>
          <button mat-menu-item 
                  *ngFor="let language of availableLanguages"
                  (click)="changeLanguage(language.code)"
                  [class.active]="currentLanguage === language.code">
            <span class="language-flag">{{ language.flag }}</span>
            <span class="language-name">{{ language.name }}</span>
            <mat-icon *ngIf="currentLanguage === language.code" class="check-icon">check</mat-icon>
          </button>
        </div>
        
        <mat-divider></mat-divider>
        
        <!-- Theme Section -->
        <div class="menu-section">
          <div class="section-header">
            <mat-icon>palette</mat-icon>
            <span>{{ 'SWITCHER.THEME' | translate }}</span>
          </div>
          <button mat-menu-item (click)="setTheme('light')" [class.active]="currentTheme === 'light'">
            <mat-icon>light_mode</mat-icon>
            <span>{{ 'SWITCHER.LIGHT_THEME' | translate }}</span>
            <mat-icon *ngIf="currentTheme === 'light'" class="check-icon">check</mat-icon>
          </button>
          <button mat-menu-item (click)="setTheme('dark')" [class.active]="currentTheme === 'dark'">
            <mat-icon>dark_mode</mat-icon>
            <span>{{ 'SWITCHER.DARK_THEME' | translate }}</span>
            <mat-icon *ngIf="currentTheme === 'dark'" class="check-icon">check</mat-icon>
          </button>
        </div>
      </mat-menu>
    </div>
  `,
  styleUrls: ['./language-theme-switcher.component.scss']
})
export class LanguageThemeSwitcherComponent implements OnInit {
  @Input() style: SwitcherStyle = 'compact';
  @Input() showLabels: boolean = false;
  @Input() theme: SwitcherTheme = 'auto';

  currentLanguage: string = 'en';
  currentTheme: string = 'light';
  availableLanguages: LanguageOption[] = [];

  constructor(
    private languageService: LanguageService,
    private authService: AuthService,
    private translate: TranslateService,
    private themeService: ThemeService
  ) {}

  ngOnInit(): void {
    // Initialize language
    this.currentLanguage = this.languageService.currentLanguage;
    this.availableLanguages = this.languageService.availableLanguages;

    // Subscribe to language changes
    this.languageService.currentLanguage$.subscribe(lang => {
      this.currentLanguage = lang;
    });

    // Initialize theme from ThemeService
    this.currentTheme = this.themeService.currentTheme;

    // Subscribe to theme changes from ThemeService
    this.themeService.currentTheme$.subscribe(theme => {
      this.currentTheme = theme;
    });
  }

  getCurrentLanguage(): LanguageOption | undefined {
    return this.availableLanguages.find(lang => lang.code === this.currentLanguage);
  }

  changeLanguage(languageCode: string): void {
    this.languageService.setLanguage(languageCode as any);
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
  }

  setTheme(theme: string): void {
    this.themeService.setTheme(theme as 'light' | 'dark');
  }

  getThemeLabel(): string {
    return this.currentTheme === 'dark' 
      ? this.translate.instant('SWITCHER.DARK_THEME')
      : this.translate.instant('SWITCHER.LIGHT_THEME');
  }
}
