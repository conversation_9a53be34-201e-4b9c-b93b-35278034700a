using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.User;
using School.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Identity
{
    public class UserService : IUserService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<UserService> _logger;

        public UserService(
            UserManager<ApplicationUser> userManager,
            ICurrentUserService currentUserService,
            ILogger<UserService> logger)
        {
            _userManager = userManager;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        public async Task<List<UserDto>> GetAllUsersAsync()
        {
            var users = await _userManager.Users
                .Where(u => !u.IsDeleted)
                .ToListAsync();

            return users.Select(u => new UserDto
            {
                Id = Guid.Parse(u.Id),
                Username = u.UserName ?? string.Empty,
                Email = u.Email ?? string.Empty,
                FirstName = u.FirstName,
                LastName = u.LastName,
                Role = u.Role,
                LastLogin = u.LastLogin,
                IsActive = u.IsActive,
                CreatedAt = u.CreatedAt,
                LastModifiedAt = u.LastModifiedAt
            }).ToList();
        }

        public async Task<UserDto?> GetUserByIdAsync(Guid id)
        {
            var user = await _userManager.FindByIdAsync(id.ToString());

            if (user == null || user.IsDeleted)
            {
                return null;
            }

            return new UserDto
            {
                Id = Guid.Parse(user.Id),
                Username = user.UserName ?? string.Empty,
                Email = user.Email ?? string.Empty,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Role = user.Role,
                LastLogin = user.LastLogin,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                LastModifiedAt = user.LastModifiedAt
            };
        }

        public async Task<UserDto?> UpdateUserAsync(Guid id, UserUpdateDto userDto)
        {
            var user = await _userManager.FindByIdAsync(id.ToString());

            if (user == null || user.IsDeleted)
            {
                return null;
            }

            // Update user properties
            if (userDto.Email != null)
            {
                user.Email = userDto.Email;
            }

            if (userDto.FirstName != null)
            {
                user.FirstName = userDto.FirstName;
            }

            if (userDto.LastName != null)
            {
                user.LastName = userDto.LastName;
            }

            if (userDto.Role.HasValue)
            {
                // Get current roles
                var currentRoles = await _userManager.GetRolesAsync(user);
                
                // Remove from current roles
                if (currentRoles.Any())
                {
                    await _userManager.RemoveFromRolesAsync(user, currentRoles);
                }
                
                // Add to new role
                user.Role = userDto.Role.Value;
                await _userManager.AddToRoleAsync(user, userDto.Role.Value.ToString());
            }

            if (userDto.IsActive.HasValue)
            {
                user.IsActive = userDto.IsActive.Value;
            }

            if (userDto.Password != null)
            {
                // Remove current password
                var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                var result = await _userManager.ResetPasswordAsync(user, token, userDto.Password);
                
                if (!result.Succeeded)
                {
                    _logger.LogError("Failed to update password for user {UserId}. Errors: {Errors}", 
                        id, string.Join(", ", result.Errors.Select(e => e.Description)));
                    return null;
                }
            }

            // Update audit fields
            user.LastModifiedAt = DateTime.UtcNow;
            user.LastModifiedBy = _currentUserService.UserId?.ToString();

            var updateResult = await _userManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                _logger.LogError("Failed to update user {UserId}. Errors: {Errors}", 
                    id, string.Join(", ", updateResult.Errors.Select(e => e.Description)));
                return null;
            }

            return new UserDto
            {
                Id = Guid.Parse(user.Id),
                Username = user.UserName ?? string.Empty,
                Email = user.Email ?? string.Empty,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Role = user.Role,
                LastLogin = user.LastLogin,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                LastModifiedAt = user.LastModifiedAt
            };
        }

        public async Task<bool> DeleteUserAsync(Guid id)
        {
            var user = await _userManager.FindByIdAsync(id.ToString());

            if (user == null)
            {
                return false;
            }

            // Soft delete
            user.IsDeleted = true;
            user.LastModifiedAt = DateTime.UtcNow;
            user.LastModifiedBy = _currentUserService.UserId?.ToString();

            var result = await _userManager.UpdateAsync(user);
            return result.Succeeded;
        }
    }
}
