import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { TermService } from '../../../../core/services/term.service';
import { AcademicYearService } from '../../../../core/services/academic-year.service';
import { Term, TermFilter, TermType, TermStatus, AcademicYear } from '../../../../core/models/academic-year.model';

@Component({
  selector: 'app-term-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatTooltipModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  templateUrl: './term-list.component.html',
  styleUrls: ['./term-list.component.scss']
})
export class TermListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = [
    'name',
    'code',
    'academicYear',
    'type',
    'startDate',
    'endDate',
    'status',
    'orderIndex',
    'actions'
  ];

  dataSource = new MatTableDataSource<Term>();
  loading = false;
  totalCount = 0;

  filterForm: FormGroup;
  academicYears: AcademicYear[] = [];
  
  typeOptions = [
    { value: TermType.Semester, label: 'Semester' },
    { value: TermType.Trimester, label: 'Trimester' },
    { value: TermType.Quarter, label: 'Quarter' },
    { value: TermType.Annual, label: 'Annual' },
    { value: TermType.Custom, label: 'Custom' }
  ];

  statusOptions = [
    { value: TermStatus.Planned, label: 'Planned' },
    { value: TermStatus.Active, label: 'Active' },
    { value: TermStatus.Completed, label: 'Completed' },
    { value: TermStatus.Cancelled, label: 'Cancelled' }
  ];

  currentFilter: TermFilter = {
    page: 1,
    pageSize: 10,
    sortDescending: false
  };

  constructor(
    private termService: TermService,
    private academicYearService: AcademicYearService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    this.filterForm = this.fb.group({
      academicYearId: [''],
      name: [''],
      code: [''],
      type: [''],
      status: ['']
    });
  }

  ngOnInit(): void {
    this.loadAcademicYears();
    this.loadTerms();
    this.setupFilterSubscription();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;

    this.paginator.page.subscribe(() => {
      this.currentFilter.page = this.paginator.pageIndex + 1;
      this.currentFilter.pageSize = this.paginator.pageSize;
      this.loadTerms();
    });

    this.sort.sortChange.subscribe(() => {
      this.currentFilter.sortBy = this.sort.active;
      this.currentFilter.sortDescending = this.sort.direction === 'desc';
      this.loadTerms();
    });
  }

  private loadAcademicYears(): void {
    this.academicYearService.getAcademicYears({
      page: 1,
      pageSize: 100,
      sortDescending: false
    }).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.academicYears = response.data.items;
        }
      },
      error: (error) => {
        console.error('Error loading academic years:', error);
      }
    });
  }

  private setupFilterSubscription(): void {
    this.filterForm.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe(() => {
        this.applyFilter();
      });
  }

  private applyFilter(): void {
    const formValue = this.filterForm.value;
    this.currentFilter = {
      ...this.currentFilter,
      page: 1,
      academicYearId: formValue.academicYearId || undefined,
      name: formValue.name || undefined,
      code: formValue.code || undefined,
      type: formValue.type !== '' ? formValue.type : undefined,
      status: formValue.status !== '' ? formValue.status : undefined
    };
    this.loadTerms();
  }

  loadTerms(): void {
    this.loading = true;
    this.termService.getTerms(this.currentFilter).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.dataSource.data = response.data.items;
          this.totalCount = response.data.totalCount;
          this.paginator.length = this.totalCount;
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading terms:', error);
        this.snackBar.open('Error loading terms', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  clearFilters(): void {
    this.filterForm.reset();
    this.currentFilter = {
      page: 1,
      pageSize: 10,
      sortDescending: false
    };
    this.loadTerms();
  }

  getTypeText(type: TermType): string {
    return this.termService.getTermTypeText(type);
  }

  getStatusText(status: TermStatus): string {
    return this.termService.getTermStatusText(status);
  }

  getStatusColor(status: TermStatus): string {
    return this.termService.getTermStatusColor(status);
  }

  activateTerm(term: Term): void {
    this.termService.activateTerm(term.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Term activated successfully', 'Close', { duration: 3000 });
          this.loadTerms();
        }
      },
      error: (error) => {
        console.error('Error activating term:', error);
        this.snackBar.open('Error activating term', 'Close', { duration: 3000 });
      }
    });
  }

  completeTerm(term: Term): void {
    this.termService.completeTerm(term.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Term completed successfully', 'Close', { duration: 3000 });
          this.loadTerms();
        }
      },
      error: (error) => {
        console.error('Error completing term:', error);
        this.snackBar.open('Error completing term', 'Close', { duration: 3000 });
      }
    });
  }

  deleteTerm(term: Term): void {
    if (confirm(`Are you sure you want to delete the term "${term.name}"?`)) {
      this.termService.deleteTerm(term.id).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Term deleted successfully', 'Close', { duration: 3000 });
            this.loadTerms();
          }
        },
        error: (error) => {
          console.error('Error deleting term:', error);
          this.snackBar.open('Error deleting term', 'Close', { duration: 3000 });
        }
      });
    }
  }
}
