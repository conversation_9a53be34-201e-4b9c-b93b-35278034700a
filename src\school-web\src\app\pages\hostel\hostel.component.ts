import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { HostelService } from '../../core/services/hostel.service';

@Component({
  selector: 'app-hostel',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatExpansionModule,
    TranslateModule
  ],
  templateUrl: './hostel.component.html',
  styleUrls: ['./hostel.component.scss']
})
export class HostelComponent implements OnInit {
  hostelFacilities: any[] = [];
  isLoading = true;
  error: string | null = null;
  currentLanguage: string;

  // Group facilities by gender
  facilitiesByGender: { [key: string]: any[] } = {};

  constructor(
    private hostelService: HostelService,
    private translateService: TranslateService
  ) {
    this.currentLanguage = this.translateService.currentLang || 'en';
  }

  ngOnInit(): void {
    this.loadHostelFacilities();

    this.translateService.onLangChange.subscribe(event => {
      this.currentLanguage = event.lang;
    });
  }

  loadHostelFacilities(): void {
    this.isLoading = true;
    this.error = null;

    this.hostelService.getHostelFacilities({ isActive: true }).subscribe({
      next: (facilities) => {
        this.hostelFacilities = facilities;
        this.groupFacilitiesByGender();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading hostel facilities:', error);
        this.error = 'Failed to load hostel facilities. Please try again later.';
        this.isLoading = false;
      }
    });
  }

  groupFacilitiesByGender(): void {
    this.facilitiesByGender = {};

    this.hostelFacilities.forEach(facility => {
      const genderKey = facility.gender.toString();

      if (!this.facilitiesByGender[genderKey]) {
        this.facilitiesByGender[genderKey] = [];
      }

      this.facilitiesByGender[genderKey].push(facility);
    });
  }

  getTranslation(item: any, field: string): string {
    if (!item) return '';

    // If current language is English, return the original content
    if (this.currentLanguage === 'en') {
      return item[field];
    }

    // Look for a translation in the current language
    const translation = item.translations?.find(
      (t: any) => t.languageCode === this.currentLanguage
    );

    // If translation exists and has the field, return it
    if (translation && translation[field]) {
      return translation[field];
    }

    // Fallback to original content
    return item[field];
  }

  getGenderLabel(gender: number): string {
    switch (gender) {
      case 0: return this.translateService.instant('HOSTEL.GENDER_TYPES.MALE');
      case 1: return this.translateService.instant('HOSTEL.GENDER_TYPES.FEMALE');
      case 2: return this.translateService.instant('HOSTEL.GENDER_TYPES.MIXED');
      default: return '';
    }
  }

  getHostelTypeLabel(type: number): string {
    switch (type) {
      case 0: return this.translateService.instant('HOSTEL.HOSTEL_TYPES.STANDARD');
      case 1: return this.translateService.instant('HOSTEL.HOSTEL_TYPES.DELUXE');
      case 2: return this.translateService.instant('HOSTEL.HOSTEL_TYPES.PREMIUM');
      default: return '';
    }
  }

  formatAmount(amount: number): string {
    return amount.toLocaleString('en-US', {
      style: 'currency',
      currency: 'BDT',
      minimumFractionDigits: 2
    });
  }
}
