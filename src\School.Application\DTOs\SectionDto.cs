using School.Domain.Enums;
using School.Domain.Entities;

namespace School.Application.DTOs;

/// <summary>
/// DTO for Section entity
/// </summary>
public class SectionDto
{
    public Guid Id { get; set; }
    public Guid GradeId { get; set; }
    public string GradeName { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public SectionType Type { get; set; }
    public TeachingMedium Medium { get; set; }
    public ShiftType Shift { get; set; }
    public int Capacity { get; set; }
    public int CurrentEnrollment { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public Guid AcademicYearId { get; set; }
    public string AcademicYearName { get; set; } = string.Empty;
    public Guid? ClassTeacherId { get; set; }
    public string? ClassTeacherName { get; set; }
    public string Classroom { get; set; } = string.Empty;
    public string RoomNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Requirements { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public decimal UtilizationPercentage => Capacity > 0 ? (decimal)CurrentEnrollment / Capacity * 100 : 0;
    public bool IsOverCapacity => CurrentEnrollment > Capacity;
    public int AvailableSlots => Math.Max(0, Capacity - CurrentEnrollment);
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }

    // Navigation properties
    public ClassTeacherDto? ClassTeacher { get; set; }
    public List<StudentDto> Students { get; set; } = new();
}

/// <summary>
/// DTO for creating a new Section
/// </summary>
public class CreateSectionDto
{
    public Guid GradeId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public SectionType Type { get; set; } = SectionType.Regular;
    public TeachingMedium Medium { get; set; } = TeachingMedium.Bengali;
    public ShiftType Shift { get; set; } = ShiftType.Morning;
    public int Capacity { get; set; } = 40;
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }
    public Guid AcademicYearId { get; set; }
    public Guid? ClassTeacherId { get; set; }
    public string Classroom { get; set; } = string.Empty;
    public string RoomNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Requirements { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;

    // Translation support
    public List<CreateSectionTranslationDto> Translations { get; set; } = new();
}

/// <summary>
/// DTO for updating a Section
/// </summary>
public class UpdateSectionDto
{
    public Guid GradeId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public SectionType Type { get; set; }
    public TeachingMedium Medium { get; set; }
    public ShiftType Shift { get; set; }
    public int Capacity { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public Guid AcademicYearId { get; set; }
    public Guid? ClassTeacherId { get; set; }
    public string Classroom { get; set; } = string.Empty;
    public string RoomNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Requirements { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;

    // Translation support
    public List<UpdateSectionTranslationDto> Translations { get; set; } = new();
}

/// <summary>
/// DTO for Section translation
/// </summary>
public class SectionTranslationDto
{
    public Guid Id { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Requirements { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

/// <summary>
/// DTO for creating Section translation
/// </summary>
public class CreateSectionTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Requirements { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating Section translation
/// </summary>
public class UpdateSectionTranslationDto
{
    public Guid Id { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Requirements { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

/// <summary>
/// DTO for Section filtering and pagination
/// </summary>
public class SectionFilterDto
{
    public string? SearchTerm { get; set; }
    public Guid? GradeId { get; set; }
    public Guid? AcademicYearId { get; set; }
    public SectionType? Type { get; set; }
    public TeachingMedium? Medium { get; set; }
    public ShiftType? Shift { get; set; }
    public bool? IsActive { get; set; }
    public bool? HasClassTeacher { get; set; }
    public bool? IsOverCapacity { get; set; }
    public int? MinCapacity { get; set; }
    public int? MaxCapacity { get; set; }
    public string? LanguageCode { get; set; } = "en";
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string SortBy { get; set; } = "DisplayOrder";
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// DTO for bulk section operations
/// </summary>
public class BulkSectionOperationDto
{
    public List<Guid> SectionIds { get; set; } = new();
    public string Operation { get; set; } = string.Empty; // "activate", "deactivate", "delete", "assign_teacher"
    public Guid? ClassTeacherId { get; set; } // For assign_teacher operation
    public string? Reason { get; set; }
}

/// <summary>
/// DTO for section capacity management
/// </summary>
public class SectionCapacityDto
{
    public Guid SectionId { get; set; }
    public string SectionName { get; set; } = string.Empty;
    public int CurrentCapacity { get; set; }
    public int NewCapacity { get; set; }
    public int CurrentEnrollment { get; set; }
    public string Reason { get; set; } = string.Empty;
}
