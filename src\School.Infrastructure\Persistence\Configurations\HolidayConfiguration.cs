using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;
using School.Domain.Enums;
using School.Domain.ValueObjects;

namespace School.Infrastructure.Persistence.Configurations;

public class HolidayConfiguration : IEntityTypeConfiguration<Holiday>
{
    public void Configure(EntityTypeBuilder<Holiday> builder)
    {
        builder.ToTable("Holidays");

        builder.<PERSON><PERSON>ey(h => h.Id);

        builder.Property(h => h.Id)
            .ValueGeneratedOnAdd();

        builder.Property(h => h.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(h => h.Description)
            .HasMaxLength(1000);

        builder.Property(h => h.StartDate)
            .IsRequired();

        builder.Property(h => h.EndDate)
            .IsRequired();

        builder.Property(h => h.Type)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(h => h.IsRecurring)
            .IsRequired()
            .HasDefaultValue(false);

        // Configure RecurrencePattern as owned entity (value object)
        builder.OwnsOne(h => h.RecurrencePattern, rp =>
        {
            rp.Property(p => p.Type)
                .HasConversion<string>()
                .HasColumnName("RecurrenceType")
                .HasMaxLength(50);

            rp.Property(p => p.Interval)
                .HasColumnName("RecurrenceInterval")
                .HasDefaultValue(1);

            rp.Property(p => p.EndDate)
                .HasColumnName("RecurrenceEndDate");

            rp.Property(p => p.MaxOccurrences)
                .HasColumnName("RecurrenceMaxOccurrences");

            rp.Property(p => p.DayOfMonth)
                .HasColumnName("RecurrenceDayOfMonth");

            rp.Property(p => p.MonthOfYear)
                .HasColumnName("RecurrenceMonthOfYear");

            rp.Property(p => p.CustomPattern)
                .HasColumnName("RecurrenceCustomPattern")
                .HasMaxLength(500);

            // Configure DaysOfWeek as JSON
            rp.Property(p => p.DaysOfWeek)
                .HasColumnName("RecurrenceDaysOfWeek")
                .HasConversion(
                    v => v != null ? string.Join(",", v.Select(d => (int)d)) : null,
                    v => !string.IsNullOrEmpty(v) ? v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(s => (DayOfWeek)int.Parse(s)).ToList() : null)
                .HasMaxLength(50);
        });

        builder.Property(h => h.Color)
            .HasMaxLength(7); // For hex color codes like #FF0000

        builder.Property(h => h.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(h => h.IsPublic)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(h => h.Remarks)
            .HasMaxLength(1000);

        // Foreign key relationships
        builder.Property(h => h.AcademicYearId);

        builder.Property(h => h.TermId);

        // Audit fields
        builder.Property(h => h.CreatedAt)
            .IsRequired();

        builder.Property(h => h.CreatedBy)
            .HasMaxLength(450);

        builder.Property(h => h.LastModifiedAt);

        builder.Property(h => h.LastModifiedBy)
            .HasMaxLength(450);

        // Relationships
        builder.HasOne(h => h.AcademicYear)
            .WithMany(ay => ay.Holidays)
            .HasForeignKey(h => h.AcademicYearId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(h => h.Term)
            .WithMany(t => t.Holidays)
            .HasForeignKey(h => h.TermId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasMany(h => h.Translations)
            .WithOne(ht => ht.Holiday)
            .HasForeignKey(ht => ht.HolidayId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(h => h.StartDate)
            .HasDatabaseName("IX_Holidays_StartDate");

        builder.HasIndex(h => h.EndDate)
            .HasDatabaseName("IX_Holidays_EndDate");

        builder.HasIndex(h => h.Type)
            .HasDatabaseName("IX_Holidays_Type");

        builder.HasIndex(h => h.IsActive)
            .HasDatabaseName("IX_Holidays_IsActive");

        builder.HasIndex(h => h.IsPublic)
            .HasDatabaseName("IX_Holidays_IsPublic");

        builder.HasIndex(h => h.AcademicYearId)
            .HasDatabaseName("IX_Holidays_AcademicYearId");

        builder.HasIndex(h => h.TermId)
            .HasDatabaseName("IX_Holidays_TermId");

        builder.HasIndex(h => new { h.StartDate, h.EndDate, h.AcademicYearId })
            .HasDatabaseName("IX_Holidays_DateRange_AcademicYear");

        builder.HasIndex(h => new { h.StartDate, h.EndDate, h.TermId })
            .HasDatabaseName("IX_Holidays_DateRange_Term");
    }
}
