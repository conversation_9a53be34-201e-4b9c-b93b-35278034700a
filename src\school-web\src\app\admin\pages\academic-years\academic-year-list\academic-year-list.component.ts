import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { AcademicYearService } from '../../../../core/services/academic-year.service';
import { AcademicYear, AcademicYearFilter, AcademicYearStatus } from '../../../../core/models/academic-year.model';

@Component({
  selector: 'app-academic-year-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatTooltipModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule
  ],
  templateUrl: './academic-year-list.component.html',
  styleUrls: ['./academic-year-list.component.scss']
})
export class AcademicYearListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = [
    'name',
    'code',
    'startDate',
    'endDate',
    'status',
    'isCurrentYear',
    'totalTerms',
    'actions'
  ];

  dataSource = new MatTableDataSource<AcademicYear>();
  loading = false;
  totalCount = 0;

  filterForm: FormGroup;
  statusOptions = [
    { value: AcademicYearStatus.Draft, label: 'Draft' },
    { value: AcademicYearStatus.Active, label: 'Active' },
    { value: AcademicYearStatus.Completed, label: 'Completed' },
    { value: AcademicYearStatus.Archived, label: 'Archived' },
    { value: AcademicYearStatus.Cancelled, label: 'Cancelled' }
  ];

  currentFilter: AcademicYearFilter = {
    page: 1,
    pageSize: 10,
    sortDescending: false
  };

  constructor(
    private academicYearService: AcademicYearService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.filterForm = this.fb.group({
      name: [''],
      code: [''],
      status: [''],
      isCurrentYear: [''],
      startDateFrom: [''],
      startDateTo: [''],
      endDateFrom: [''],
      endDateTo: ['']
    });
  }

  ngOnInit(): void {
    this.loadAcademicYears();
    this.setupFilterSubscription();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;

    this.paginator.page.subscribe(() => {
      this.currentFilter.page = this.paginator.pageIndex + 1;
      this.currentFilter.pageSize = this.paginator.pageSize;
      this.loadAcademicYears();
    });

    this.sort.sortChange.subscribe(() => {
      this.currentFilter.sortBy = this.sort.active;
      this.currentFilter.sortDescending = this.sort.direction === 'desc';
      this.loadAcademicYears();
    });
  }

  private setupFilterSubscription(): void {
    this.filterForm.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe(() => {
        this.applyFilter();
      });
  }

  private applyFilter(): void {
    const formValue = this.filterForm.value;
    this.currentFilter = {
      ...this.currentFilter,
      page: 1,
      name: formValue.name || undefined,
      code: formValue.code || undefined,
      status: formValue.status !== '' ? formValue.status : undefined,
      isCurrentYear: formValue.isCurrentYear !== '' ? formValue.isCurrentYear === 'true' : undefined,
      startDateFrom: formValue.startDateFrom || undefined,
      startDateTo: formValue.startDateTo || undefined,
      endDateFrom: formValue.endDateFrom || undefined,
      endDateTo: formValue.endDateTo || undefined
    };
    this.loadAcademicYears();
  }

  loadAcademicYears(): void {
    this.loading = true;
    this.academicYearService.getAcademicYears(this.currentFilter).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.dataSource.data = response.data.items;
          this.totalCount = response.data.totalCount;
          this.paginator.length = this.totalCount;
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading academic years:', error);
        this.snackBar.open('Error loading academic years', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  clearFilters(): void {
    this.filterForm.reset();
    this.currentFilter = {
      page: 1,
      pageSize: 10,
      sortDescending: false
    };
    this.loadAcademicYears();
  }

  getStatusText(status: AcademicYearStatus): string {
    return this.academicYearService.getAcademicYearStatusText(status);
  }

  getStatusColor(status: AcademicYearStatus): string {
    return this.academicYearService.getAcademicYearStatusColor(status);
  }

  setCurrentYear(academicYear: AcademicYear): void {
    if (academicYear.isCurrentYear) {
      this.snackBar.open('This is already the current academic year', 'Close', { duration: 3000 });
      return;
    }

    this.academicYearService.setCurrentAcademicYear(academicYear.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Academic year set as current successfully', 'Close', { duration: 3000 });
          this.loadAcademicYears();
        }
      },
      error: (error) => {
        console.error('Error setting current academic year:', error);
        this.snackBar.open('Error setting current academic year', 'Close', { duration: 3000 });
      }
    });
  }

  activateAcademicYear(academicYear: AcademicYear): void {
    this.academicYearService.activateAcademicYear(academicYear.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Academic year activated successfully', 'Close', { duration: 3000 });
          this.loadAcademicYears();
        }
      },
      error: (error) => {
        console.error('Error activating academic year:', error);
        this.snackBar.open('Error activating academic year', 'Close', { duration: 3000 });
      }
    });
  }

  completeAcademicYear(academicYear: AcademicYear): void {
    this.academicYearService.completeAcademicYear(academicYear.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Academic year completed successfully', 'Close', { duration: 3000 });
          this.loadAcademicYears();
        }
      },
      error: (error) => {
        console.error('Error completing academic year:', error);
        this.snackBar.open('Error completing academic year', 'Close', { duration: 3000 });
      }
    });
  }

  deleteAcademicYear(academicYear: AcademicYear): void {
    if (confirm(`Are you sure you want to delete the academic year "${academicYear.name}"?`)) {
      this.academicYearService.deleteAcademicYear(academicYear.id).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Academic year deleted successfully', 'Close', { duration: 3000 });
            this.loadAcademicYears();
          }
        },
        error: (error) => {
          console.error('Error deleting academic year:', error);
          this.snackBar.open('Error deleting academic year', 'Close', { duration: 3000 });
        }
      });
    }
  }
}
