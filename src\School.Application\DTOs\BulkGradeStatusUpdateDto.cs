using System.ComponentModel.DataAnnotations;

namespace School.Application.DTOs;

/// <summary>
/// DTO for bulk updating grade status
/// </summary>
public class BulkGradeStatusUpdateDto
{
    /// <summary>
    /// List of grade IDs to update
    /// </summary>
    [Required]
    public List<Guid> GradeIds { get; set; } = new();

    /// <summary>
    /// New status for the grades
    /// </summary>
    [Required]
    public bool IsActive { get; set; }
}
