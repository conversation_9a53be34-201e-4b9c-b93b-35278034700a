using System.Collections.Generic;

namespace School.Application.DTOs;

public class ClubActivityUpdateDto
{
    public int? Id { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
    public List<ClubActivityTranslationUpdateDto>? Translations { get; set; }
}

public class ClubActivityTranslationUpdateDto
{
    public int? Id { get; set; }
    public string? LanguageCode { get; set; }
    public string? Description { get; set; }
}
