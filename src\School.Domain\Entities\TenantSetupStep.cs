using School.Domain.Common;

namespace School.Domain.Entities;

/// <summary>
/// Tracks individual setup steps for tenant onboarding
/// </summary>
public class TenantSetupStep : BaseEntity
{
    /// <summary>
    /// The tenant/organization this step belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public Organization Tenant { get; set; } = null!;

    /// <summary>
    /// Unique identifier for the step (e.g., "school-profile", "academic-structure")
    /// </summary>
    public string StepId { get; set; } = string.Empty;

    /// <summary>
    /// Display name of the step
    /// </summary>
    public string StepName { get; set; } = string.Empty;

    /// <summary>
    /// Description of what this step involves
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Order in which this step should be completed
    /// </summary>
    public int OrderIndex { get; set; }

    /// <summary>
    /// Whether this step is required for setup completion
    /// </summary>
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// Whether this step has been completed
    /// </summary>
    public bool IsCompleted { get; set; } = false;

    /// <summary>
    /// Whether this step was skipped (for optional steps)
    /// </summary>
    public bool IsSkipped { get; set; } = false;

    /// <summary>
    /// When this step was completed
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// When this step was skipped
    /// </summary>
    public DateTime? SkippedAt { get; set; }

    /// <summary>
    /// Additional data/configuration for this step (JSON)
    /// </summary>
    public string? StepData { get; set; }

    /// <summary>
    /// Any notes or comments about this step
    /// </summary>
    public string? Notes { get; set; }
}
