<div class="register-container">
  <!-- Background Elements -->
  <div class="background-elements">
    <div class="bg-circle bg-circle-1"></div>
    <div class="bg-circle bg-circle-2"></div>
    <div class="bg-circle bg-circle-3"></div>
  </div>

  <div class="register-content">
    <!-- Header Section -->
    <div class="register-header">
      <div class="logo-section">
        <mat-icon class="logo-icon">school</mat-icon>
        <h1 class="main-title">{{ 'REGISTER.TITLE' | translate }}</h1>
      </div>
      <p class="subtitle">{{ 'REGISTER.SUBTITLE' | translate }}</p>
    </div>

    <!-- Registration Card -->
    <div class="register-card-wrapper">
      <mat-card class="register-card">
        <mat-card-content>

          <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">
            <!-- User Type Selection -->
            <div class="user-type-section">
              <h3 class="section-title">{{ 'REGISTER.SELECT_USER_TYPE' | translate }}</h3>
              <div class="user-type-grid">
                <div *ngFor="let type of userTypes"
                     class="user-type-card"
                     [class.selected]="selectedUserType === type.value"
                     (click)="selectUserType(type.value)">
                  <mat-icon class="type-icon">{{ type.icon }}</mat-icon>
                  <span class="type-label">{{ 'REGISTER.' + type.label | translate }}</span>
                  <mat-icon class="check-icon" *ngIf="selectedUserType === type.value">check_circle</mat-icon>
                </div>
              </div>
            </div>

            <!-- Personal Information -->
            <div class="form-section" *ngIf="selectedUserType">
              <h3 class="section-title">{{ 'REGISTER.PERSONAL_INFO' | translate }}</h3>

              <!-- Name Fields -->
              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>{{ 'REGISTER.FIRST_NAME' | translate }}</mat-label>
                  <input matInput formControlName="firstName" required>
                  <mat-icon matPrefix>person</mat-icon>
                  <mat-error *ngIf="registerForm.get('firstName')?.hasError('required')">
                    {{ 'REGISTER.FIRST_NAME_REQUIRED' | translate }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>{{ 'REGISTER.LAST_NAME' | translate }}</mat-label>
                  <input matInput formControlName="lastName" required>
                  <mat-icon matPrefix>person</mat-icon>
                  <mat-error *ngIf="registerForm.get('lastName')?.hasError('required')">
                    {{ 'REGISTER.LAST_NAME_REQUIRED' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Contact Fields -->
              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>{{ 'REGISTER.EMAIL' | translate }}</mat-label>
                  <input matInput formControlName="email" type="email" required>
                  <mat-icon matPrefix>email</mat-icon>
                  <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
                    {{ 'REGISTER.EMAIL_REQUIRED' | translate }}
                  </mat-error>
                  <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
                    {{ 'REGISTER.EMAIL_INVALID' | translate }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>{{ 'REGISTER.PHONE' | translate }}</mat-label>
                  <input matInput formControlName="phone" required>
                  <mat-icon matPrefix>phone</mat-icon>
                  <mat-error *ngIf="registerForm.get('phone')?.hasError('required')">
                    {{ 'REGISTER.PHONE_REQUIRED' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <!-- Account Information -->
            <div class="form-section" *ngIf="selectedUserType">
              <h3 class="section-title">{{ 'REGISTER.ACCOUNT_INFO' | translate }}</h3>

              <!-- Username Field -->
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>{{ 'REGISTER.USERNAME' | translate }}</mat-label>
                <input matInput formControlName="username" required>
                <mat-icon matPrefix>account_circle</mat-icon>
                <mat-hint>{{ 'REGISTER.USERNAME_HINT' | translate }}</mat-hint>
                <mat-error *ngIf="registerForm.get('username')?.hasError('required')">
                  {{ 'REGISTER.USERNAME_REQUIRED' | translate }}
                </mat-error>
              </mat-form-field>

              <!-- Password Fields -->
              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>{{ 'REGISTER.PASSWORD' | translate }}</mat-label>
                  <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" required>
                  <mat-icon matPrefix>lock</mat-icon>
                  <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                    <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                  </button>
                  <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
                    {{ 'REGISTER.PASSWORD_REQUIRED' | translate }}
                  </mat-error>
                  <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
                    {{ 'REGISTER.PASSWORD_LENGTH' | translate }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>{{ 'REGISTER.CONFIRM_PASSWORD' | translate }}</mat-label>
                  <input matInput [type]="hideConfirmPassword ? 'password' : 'text'" formControlName="confirmPassword" required>
                  <mat-icon matPrefix>lock</mat-icon>
                  <button mat-icon-button matSuffix (click)="hideConfirmPassword = !hideConfirmPassword" type="button">
                    <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                  </button>
                  <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required')">
                    {{ 'REGISTER.CONFIRM_PASSWORD_REQUIRED' | translate }}
                  </mat-error>
                  <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('passwordMismatch')">
                    {{ 'REGISTER.PASSWORD_MISMATCH' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Password Strength Indicator -->
              <div class="password-strength" *ngIf="registerForm.get('password')?.value">
                <div class="strength-bar">
                  <div class="strength-fill" [ngClass]="getPasswordStrength()"></div>
                </div>
                <span class="strength-text">{{ getPasswordStrengthText() | translate }}</span>
              </div>
            </div>

            <!-- Terms and Privacy -->
            <div class="terms-section" *ngIf="selectedUserType">
              <h3 class="section-title">{{ 'REGISTER.TERMS_TITLE' | translate }}</h3>

              <div class="terms-checkboxes">
                <mat-checkbox formControlName="termsAccepted" color="primary" required>
                  <span [innerHTML]="'REGISTER.TERMS' | translate"></span>
                </mat-checkbox>
                <mat-error *ngIf="registerForm.get('termsAccepted')?.hasError('required') && registerForm.get('termsAccepted')?.touched">
                  {{ 'REGISTER.TERMS_REQUIRED' | translate }}
                </mat-error>

                <mat-checkbox formControlName="privacyAccepted" color="primary" required>
                  <span [innerHTML]="'REGISTER.PRIVACY' | translate"></span>
                </mat-checkbox>
                <mat-error *ngIf="registerForm.get('privacyAccepted')?.hasError('required') && registerForm.get('privacyAccepted')?.touched">
                  {{ 'REGISTER.PRIVACY_REQUIRED' | translate }}
                </mat-error>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="submit-section" *ngIf="selectedUserType">
              <button
                mat-raised-button
                color="primary"
                type="submit"
                class="register-button"
                [disabled]="registerForm.invalid || isLoading || !selectedUserType">
                <mat-icon>{{ isLoading ? 'hourglass_empty' : 'how_to_reg' }}</mat-icon>
                {{ isLoading ? ('REGISTER.CREATING_ACCOUNT' | translate) : ('REGISTER.SUBMIT' | translate) }}
              </button>

              <!-- Loading Indicator -->
              <mat-progress-bar *ngIf="isLoading" mode="indeterminate" class="register-progress"></mat-progress-bar>
            </div>
          </form>
        </mat-card-content>

        <!-- Footer Actions -->
        <mat-card-actions class="card-actions">
          <div class="login-section">
            <span>{{ 'REGISTER.ALREADY_ACCOUNT' | translate }}</span>
            <button mat-button color="accent" routerLink="/login">
              <mat-icon>login</mat-icon>
              {{ 'REGISTER.LOGIN' | translate }}
            </button>
          </div>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</div>
