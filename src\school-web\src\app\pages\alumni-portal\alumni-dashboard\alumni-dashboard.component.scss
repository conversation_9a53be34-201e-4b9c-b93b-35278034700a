.dashboard-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 24px;

  .welcome-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    .welcome-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .welcome-text {
        h1 {
          margin: 0 0 8px 0;
          font-size: 2rem;
          font-weight: 500;
        }

        p {
          margin: 4px 0;
          opacity: 0.9;
        }
      }

      .welcome-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.2);

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        mat-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}

.stats-section {
  margin-bottom: 24px;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;

    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .stat-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;

          &.network { color: #4CAF50; }
          &.events { color: #2196F3; }
          &.donations { color: #FF5722; }
          &.jobs { color: #9C27B0; }
        }

        .stat-info {
          h3 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
          }

          p {
            margin: 4px 0 0 0;
            color: rgba(0, 0, 0, 0.6);
            font-size: 14px;
          }
        }
      }
    }
  }
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.quick-actions-section {
  .actions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;

    .action-button {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      text-align: left;
      height: auto;
      min-height: 80px;

      mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
      }

      .action-text {
        display: flex;
        flex-direction: column;

        .action-title {
          font-weight: 600;
          font-size: 14px;
        }

        .action-description {
          font-size: 12px;
          opacity: 0.8;
          margin-top: 4px;
        }
      }
    }
  }
}

.activities-section {
  .activities-list {
    .activity-item {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);

      &:last-child {
        border-bottom: none;
      }

      .activity-icon {
        color: #666;
        margin-top: 4px;
      }

      .activity-content {
        flex: 1;

        h4 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 500;
        }

        p {
          margin: 0 0 8px 0;
          color: rgba(0, 0, 0, 0.7);
          font-size: 14px;
        }

        .activity-date {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.5);
        }
      }
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}
