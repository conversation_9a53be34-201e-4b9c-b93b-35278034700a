using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Auth;
using School.Infrastructure.Identity;
using School.Infrastructure.Persistence;

namespace School.Infrastructure.Auth
{
    public class EnhancedAuthService : IAuthService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly EnhancedJwtService _jwtService;
        private readonly ApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<EnhancedAuthService> _logger;

        public EnhancedAuthService(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            EnhancedJwtService jwtService,
            ApplicationDbContext context,
            ICurrentUserService currentUserService,
            ILogger<EnhancedAuthService> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _jwtService = jwtService;
            _context = context;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        public async Task<LoginResponseDto?> LoginAsync(LoginDto loginDto)
        {
            try
            {
                var user = await _userManager.FindByNameAsync(loginDto.Username);
                if (user == null || !user.IsActive)
                {
                    _logger.LogWarning("Login failed: User {Username} not found or inactive", loginDto.Username);
                    return null;
                }

                // Check if account is locked
                if (user.AccountLockedUntil.HasValue && user.AccountLockedUntil > DateTime.UtcNow)
                {
                    _logger.LogWarning("Login failed: Account {UserId} is locked", user.Id);
                    return null;
                }

                // Verify password
                var result = await _signInManager.CheckPasswordSignInAsync(user, loginDto.Password, lockoutOnFailure: true);
                if (!result.Succeeded)
                {
                    _logger.LogWarning("Login failed: Invalid password for user {Username}", loginDto.Username);
                    return null;
                }

                // Generate tokens
                var (accessToken, accessExpiration) = await _jwtService.GenerateTokenAsync(user);
                var (refreshToken, refreshExpiration) = await _jwtService.GenerateRefreshTokenAsync(user.Id);

                // Update last login
                user.LastLogin = DateTime.UtcNow;
                await _userManager.UpdateAsync(user);

                // Log successful login
                await LogLoginAttempt(user.Id, true, "Login successful");

                return new LoginResponseDto
                {
                    Token = accessToken,
                    RefreshToken = refreshToken,
                    Expiration = accessExpiration,
                    RefreshTokenExpiration = refreshExpiration,
                    RequiresMfa = false, // TODO: Implement MFA check
                    User = CreateUserDto(user)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user {Username}", loginDto.Username);
                return null;
            }
        }

        public async Task<RefreshTokenResponseDto?> RefreshTokenAsync(RefreshTokenDto refreshTokenDto)
        {
            try
            {
                // Find the refresh token
                var refreshTokenEntity = await _context.UserRefreshTokens
                    .Include(rt => rt.User)
                    .FirstOrDefaultAsync(rt => rt.Token == refreshTokenDto.RefreshToken &&
                                             !rt.IsRevoked &&
                                             rt.ExpiryDate > DateTime.UtcNow);

                if (refreshTokenEntity == null)
                {
                    _logger.LogWarning("Refresh token validation failed: Token not found or expired");
                    return null;
                }

                var user = await _userManager.FindByIdAsync(refreshTokenEntity.UserId);
                if (user == null || !user.IsActive)
                {
                    _logger.LogWarning("Refresh token validation failed: User not found or inactive");
                    return null;
                }

                // Revoke the old refresh token
                await _jwtService.RevokeRefreshTokenAsync(refreshTokenDto.RefreshToken);

                // Generate new tokens
                var tokenResult = await _jwtService.GenerateTokenAsync(user);
                var refreshResult = await _jwtService.GenerateRefreshTokenAsync(user.Id);

                var accessToken = tokenResult.token;
                var accessExpiration = tokenResult.expiration;
                var newRefreshToken = refreshResult.refreshToken;
                var refreshExpiration = refreshResult.expiration;

                return new RefreshTokenResponseDto
                {
                    Token = accessToken,
                    RefreshToken = newRefreshToken,
                    Expiration = accessExpiration,
                    RefreshTokenExpiration = refreshExpiration
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token refresh");
                return null;
            }
        }

        public async Task LogoutAsync(string refreshToken)
        {
            try
            {
                await _jwtService.RevokeRefreshTokenAsync(refreshToken);
                _logger.LogInformation("User logged out successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
            }
        }

        public async Task<bool> RevokeAllTokensAsync(string userId)
        {
            try
            {
                await _jwtService.RevokeAllUserRefreshTokensAsync(userId);
                _logger.LogInformation("All tokens revoked for user {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error revoking all tokens for user {UserId}", userId);
                return false;
            }
        }

        public async Task<Guid> RegisterAsync(UserCreateDto userDto)
        {
            // Validate password confirmation
            if (userDto.Password != userDto.ConfirmPassword)
                throw new InvalidOperationException("Password and confirmation password do not match");

            // Check if username already exists
            var existingUser = await _userManager.FindByNameAsync(userDto.Username);
            if (existingUser != null)
                throw new InvalidOperationException($"Username '{userDto.Username}' is already taken");

            // Check if email already exists
            var existingEmail = await _userManager.FindByEmailAsync(userDto.Email);
            if (existingEmail != null)
                throw new InvalidOperationException($"Email '{userDto.Email}' is already registered");

            // Create new user
            var user = new ApplicationUser
            {
                UserName = userDto.Username,
                Email = userDto.Email,
                FirstName = userDto.FirstName,
                LastName = userDto.LastName,
                Role = userDto.Role,
                IsActive = true,
                EmailConfirmed = true, // For development
                CreatedAt = DateTime.UtcNow
            };

            var result = await _userManager.CreateAsync(user, userDto.Password);
            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                throw new InvalidOperationException($"User creation failed: {errors}");
            }

            // Add role
            await _userManager.AddToRoleAsync(user, userDto.Role.ToString());

            _logger.LogInformation("User {Username} registered successfully", userDto.Username);
            return Guid.Parse(user.Id);
        }

        private UserDto CreateUserDto(ApplicationUser user)
        {
            return new UserDto
            {
                Id = Guid.Parse(user.Id),
                Username = user.UserName ?? "",
                Email = user.Email ?? "",
                FirstName = user.FirstName ?? "",
                LastName = user.LastName ?? "",
                Role = user.Role,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                LastLogin = user.LastLogin,
                IsMfaEnabled = user.IsMfaEnabled
            };
        }

        private async Task LogLoginAttempt(string userId, bool success, string details)
        {
            try
            {
                var loginHistory = new UserLoginHistory
                {
                    UserId = userId,
                    LoginTime = DateTime.UtcNow,
                    IsSuccessful = success,
                    IpAddress = "Unknown", // TODO: Get from HttpContext
                    UserAgent = "Unknown", // TODO: Get from HttpContext
                    FailureReason = success ? null : details
                };

                _context.UserLoginHistory.Add(loginHistory);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log login attempt for user {UserId}", userId);
            }
        }
    }
}
