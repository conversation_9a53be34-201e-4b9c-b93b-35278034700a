.calendar-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;

  .calendar-header {
    margin-bottom: 16px;

    .header-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;

      .navigation-controls {
        display: flex;
        align-items: center;
        gap: 8px;

        .period-title {
          margin: 0 16px;
          font-weight: 500;
          color: var(--primary-color);
        }
      }

      .view-controls {
        mat-button-toggle-group {
          border: 1px solid var(--border-color);
          border-radius: 8px;
        }
      }
    }
  }

  .filters-card {
    margin-bottom: 16px;

    .filters-form {
      .filter-row {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
        flex-wrap: wrap;

        mat-form-field {
          min-width: 200px;
          flex: 1;
        }

        mat-checkbox {
          margin-right: 24px;
          align-self: center;
        }
      }
    }
  }

  .statistics-card {
    margin-bottom: 16px;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;

      .stat-item {
        text-align: center;
        padding: 16px;
        background: var(--surface-color);
        border-radius: 8px;
        border: 1px solid var(--border-color);

        .stat-value {
          display: block;
          font-size: 2rem;
          font-weight: 600;
          color: var(--primary-color);
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 0.875rem;
          color: var(--text-secondary);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  .calendar-card {
    .calendar-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px;
      color: var(--text-secondary);

      mat-spinner {
        margin-bottom: 16px;
      }
    }

    .calendar-content {
      min-height: 600px;

      // Month View Styles
      .month-view {
        .month-grid {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          gap: 1px;
          background: var(--border-color);
          border: 1px solid var(--border-color);
          border-radius: 8px;
          overflow: hidden;

          .day-header {
            background: var(--primary-color);
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            font-size: 0.875rem;
          }

          .day-cell {
            background: var(--surface-color);
            min-height: 120px;
            padding: 8px;
            position: relative;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
              background: var(--hover-color);
            }

            &.other-month {
              background: var(--disabled-color);
              color: var(--text-disabled);
            }

            &.today {
              background: var(--accent-light);
              
              .date-number {
                background: var(--primary-color);
                color: white;
                border-radius: 50%;
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
              }
            }

            &.selected {
              background: var(--primary-light);
              border: 2px solid var(--primary-color);
            }

            .date-number {
              font-weight: 500;
              margin-bottom: 4px;
            }

            .events-container {
              .event-item {
                background: var(--primary-color);
                color: white;
                padding: 2px 6px;
                margin: 2px 0;
                border-radius: 4px;
                font-size: 0.75rem;
                cursor: pointer;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                transition: opacity 0.2s ease;

                &:hover {
                  opacity: 0.8;
                }

                .event-title {
                  font-weight: 500;
                }
              }
            }
          }
        }
      }

      // Timeline View Styles
      .timeline-view {
        .timeline-header {
          display: flex;
          border-bottom: 2px solid var(--border-color);
          background: var(--surface-color);
          position: sticky;
          top: 0;
          z-index: 10;

          .time-column {
            width: 80px;
            flex-shrink: 0;
          }

          .date-column {
            flex: 1;
            padding: 16px 8px;
            text-align: center;
            border-left: 1px solid var(--border-color);

            .day-name {
              font-size: 0.875rem;
              color: var(--text-secondary);
              margin-bottom: 4px;
            }

            .date-number {
              font-size: 1.25rem;
              font-weight: 600;

              &.today {
                background: var(--primary-color);
                color: white;
                border-radius: 50%;
                width: 32px;
                height: 32px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
              }
            }
          }
        }

        .timeline-body {
          .time-slots {
            .time-slot {
              display: flex;
              border-bottom: 1px solid var(--border-light);
              min-height: 60px;

              &:nth-child(even) {
                background: var(--surface-alt);
              }

              .time-label {
                width: 80px;
                flex-shrink: 0;
                padding: 8px;
                font-size: 0.75rem;
                color: var(--text-secondary);
                text-align: right;
                border-right: 1px solid var(--border-color);
              }

              .slot-columns {
                display: flex;
                flex: 1;

                .slot-column {
                  flex: 1;
                  border-left: 1px solid var(--border-light);
                  padding: 4px;
                  position: relative;

                  .events-in-slot {
                    .timeline-event {
                      background: var(--primary-color);
                      color: white;
                      padding: 4px 8px;
                      margin: 2px 0;
                      border-radius: 4px;
                      font-size: 0.75rem;
                      cursor: pointer;
                      transition: opacity 0.2s ease;

                      &:hover {
                        opacity: 0.8;
                      }

                      .event-title {
                        font-weight: 500;
                        margin-bottom: 2px;
                      }

                      .event-time {
                        font-size: 0.6875rem;
                        opacity: 0.9;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .calendar-container {
    padding: 16px;

    .calendar-header .header-row {
      flex-direction: column;
      align-items: stretch;

      .navigation-controls {
        justify-content: center;
      }

      .view-controls {
        align-self: center;
      }
    }

    .filters-card .filters-form .filter-row {
      flex-direction: column;

      mat-form-field {
        min-width: unset;
      }
    }

    .statistics-card .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .calendar-content {
      .month-view .month-grid {
        .day-cell {
          min-height: 80px;
          padding: 4px;

          .events-container .event-item {
            font-size: 0.6875rem;
            padding: 1px 4px;
          }
        }
      }

      .timeline-view {
        .timeline-header .date-column {
          padding: 8px 4px;
        }

        .timeline-body .time-slots .time-slot {
          min-height: 40px;

          .time-label {
            width: 60px;
            padding: 4px;
          }
        }
      }
    }
  }
}

// Dark Theme Support
@media (prefers-color-scheme: dark) {
  .calendar-container {
    .calendar-card .calendar-content {
      .month-view .month-grid {
        .day-header {
          background: var(--primary-dark);
        }

        .day-cell {
          &.today .date-number {
            background: var(--primary-dark);
          }

          .events-container .event-item {
            background: var(--primary-dark);
          }
        }
      }

      .timeline-view .timeline-body .time-slots .time-slot .slot-columns .slot-column .events-in-slot .timeline-event {
        background: var(--primary-dark);
      }
    }
  }
}
