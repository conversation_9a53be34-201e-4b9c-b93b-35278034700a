using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using System;
using System.Text.RegularExpressions;

namespace School.Infrastructure.Middleware;

/// <summary>
/// Middleware for detecting and setting tenant context based on request
/// Supports subdomain-based and custom domain-based tenant identification
/// </summary>
public class TenantMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TenantMiddleware> _logger;

    public TenantMiddleware(RequestDelegate next, ILogger<TenantMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ITenantService tenantService, ITenantContext tenantContext)
    {
        try
        {
            // Skip tenant validation for authentication and system endpoints
            if (IsAdminOrSystemRoute(context.Request.Path))
            {
                _logger.LogInformation("Allowing admin/system route without tenant context: {Path}", context.Request.Path);
                await _next(context);
                return;
            }

            // Extract tenant identifier from request
            var tenantIdentifier = ExtractTenantIdentifier(context);

            if (!string.IsNullOrEmpty(tenantIdentifier))
            {
                // Set tenant context
                var tenantSet = await tenantService.SetTenantAsync(tenantIdentifier);

                if (tenantSet)
                {
                    var currentTenant = await tenantService.GetCurrentTenantAsync();
                    if (currentTenant != null)
                    {
                        // Ensure tenant context is set for global query filters
                        tenantContext.SetCurrentTenantId(currentTenant.Id);

                        _logger.LogInformation("Tenant context set: {TenantName} (ID: {TenantId})",
                            currentTenant.Name, currentTenant.Id);

                        // Add tenant info to response headers for debugging (remove in production)
                        context.Response.Headers["X-Tenant-Id"] = currentTenant.Id.ToString();
                        context.Response.Headers["X-Tenant-Name"] = currentTenant.Name;
                    }
                }
                else
                {
                    _logger.LogWarning("Tenant not found for identifier: {TenantIdentifier}", tenantIdentifier);

                    // For API requests, return structured error response
                    if (context.Request.Path.StartsWithSegments("/api"))
                    {
                        context.Response.StatusCode = 404;
                        context.Response.ContentType = "application/json";
                        await context.Response.WriteAsync("{\"error\":\"Tenant not found\",\"code\":\"TENANT_NOT_FOUND\",\"tenantIdentifier\":\"" + tenantIdentifier + "\"}");
                        return;
                    }

                    // For web requests, redirect to tenant selection
                    context.Response.Redirect("/tenant-setup/select?error=tenant-not-found");
                    return;
                }
            }
            else
            {
                _logger.LogWarning("No tenant identifier found in request: {Host}{Path}",
                    context.Request.Host, context.Request.Path);

                // For API requests that require tenant context, return error
                if (context.Request.Path.StartsWithSegments("/api"))
                {
                    context.Response.StatusCode = 400;
                    context.Response.ContentType = "application/json";
                    await context.Response.WriteAsync("{\"error\":\"Tenant identifier required\",\"code\":\"TENANT_REQUIRED\"}");
                    return;
                }

                // For web requests, redirect to tenant selection
                context.Response.Redirect("/tenant-setup/select");
                return;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in tenant middleware");

            // For API requests, return error response
            if (context.Request.Path.StartsWithSegments("/api"))
            {
                context.Response.StatusCode = 500;
                context.Response.ContentType = "application/json";
                await context.Response.WriteAsync("{\"error\":\"Internal server error\",\"code\":\"TENANT_MIDDLEWARE_ERROR\"}");
                return;
            }

            // For web requests, continue without tenant context
        }

        await _next(context);
    }

    /// <summary>
    /// Extracts tenant identifier from the HTTP request
    /// Supports subdomain-based and custom domain-based identification
    /// </summary>
    /// <param name="context">HTTP context</param>
    /// <returns>Tenant identifier or null if not found</returns>
    private string? ExtractTenantIdentifier(HttpContext context)
    {
        var host = context.Request.Host.Host;
        
        // Skip localhost and IP addresses during development
        if (host == "localhost" || IsIpAddress(host))
        {
            // For development, you can extract tenant from query parameter or header
            var tenantFromQuery = context.Request.Query["tenant"].FirstOrDefault();
            if (!string.IsNullOrEmpty(tenantFromQuery))
            {
                return tenantFromQuery;
            }
            
            var tenantFromHeader = context.Request.Headers["X-Tenant"].FirstOrDefault();
            if (!string.IsNullOrEmpty(tenantFromHeader))
            {
                return tenantFromHeader;
            }
            
            return null;
        }

        // Check if it's a subdomain pattern (e.g., school1.edumanage.com)
        var subdomainMatch = ExtractSubdomain(host);
        if (!string.IsNullOrEmpty(subdomainMatch))
        {
            return subdomainMatch;
        }

        // Check if it's a custom domain
        // For custom domains, we'll use the full domain as identifier
        return host;
    }

    /// <summary>
    /// Extracts subdomain from host
    /// Assumes pattern: {subdomain}.{maindomain}.{tld}
    /// </summary>
    /// <param name="host">Host name</param>
    /// <returns>Subdomain or null if not found</returns>
    private string? ExtractSubdomain(string host)
    {
        // Define your main domain pattern here
        // This should be configurable in production
        var mainDomainPatterns = new[]
        {
            @"^([a-zA-Z0-9-]+)\.edumanage\.com$",
            @"^([a-zA-Z0-9-]+)\.schoolmanage\.com$",
            @"^([a-zA-Z0-9-]+)\.localhost$" // For development
        };

        foreach (var pattern in mainDomainPatterns)
        {
            var match = Regex.Match(host, pattern, RegexOptions.IgnoreCase);
            if (match.Success && match.Groups.Count > 1)
            {
                var subdomain = match.Groups[1].Value;
                
                // Exclude common subdomains that are not tenants
                var excludedSubdomains = new[] { "www", "api", "admin", "app", "mail", "ftp" };
                if (!excludedSubdomains.Contains(subdomain.ToLower()))
                {
                    return subdomain;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// Checks if a string is an IP address
    /// </summary>
    /// <param name="host">Host string to check</param>
    /// <returns>True if it's an IP address, false otherwise</returns>
    private bool IsIpAddress(string host)
    {
        return System.Net.IPAddress.TryParse(host, out _);
    }

    /// <summary>
    /// Checks if the request path is an admin or system route that doesn't require tenant context
    /// </summary>
    /// <param name="path">Request path</param>
    /// <returns>True if the path is an admin or system route</returns>
    private static bool IsAdminOrSystemRoute(PathString path)
    {
        var pathValue = path.Value?.ToLowerInvariant();

        if (string.IsNullOrEmpty(pathValue))
            return false;

        // Check for tenant management endpoints with regex pattern
        if (IsTenantManagementRoute(pathValue))
            return true;

        // Admin authentication routes
        var adminRoutes = new[]
        {
            "/api/auth/admin/login",
            "/api/auth/admin/register",
            "/api/auth/admin/forgot-password",
            "/api/auth/admin/reset-password",
            "/api/auth/login", // Allow regular login for system admins
            "/api/auth/register",
            "/api/auth/forgot-password",
            "/api/auth/reset-password",
            "/api/auth/refresh-token",
            "/api/auth/logout",
            "/api/health",
            "/api/system/", // Any system-level endpoints
            "/api/admin/", // Any admin-specific endpoints
            "/swagger", // Swagger documentation
            "/health" // Health check endpoints
        };

        return adminRoutes.Any(pathValue.StartsWith);
    }

    /// <summary>
    /// Checks if the request path is a tenant management route
    /// Handles both simple paths like /api/tenant/list and parameterized paths like /api/tenant/{id}/users
    /// </summary>
    /// <param name="pathValue">Lowercase request path</param>
    /// <returns>True if it's a tenant management route</returns>
    private static bool IsTenantManagementRoute(string pathValue)
    {
        // Simple tenant routes
        if (pathValue.StartsWith("/api/tenant/"))
        {
            // Extract the part after /api/tenant/
            var tenantPath = pathValue.Substring("/api/tenant/".Length);

            // If it's empty or starts with known non-GUID routes, allow it
            if (string.IsNullOrEmpty(tenantPath) ||
                tenantPath.StartsWith("list") ||
                tenantPath.StartsWith("create") ||
                tenantPath.StartsWith("current") ||
                tenantPath.StartsWith("check-slug"))
            {
                return true;
            }

            // Check if it matches the pattern /api/tenant/{guid}/... or /api/tenant/{guid}
            var segments = tenantPath.Split('/');
            if (segments.Length >= 1)
            {
                var firstSegment = segments[0];

                // Check if the first segment is a GUID (tenant ID)
                if (Guid.TryParse(firstSegment, out _))
                {
                    // This is a tenant-specific endpoint like:
                    // /api/tenant/{id}
                    // /api/tenant/{id}/users
                    // /api/tenant/{id}/stats
                    // /api/tenant/{id}/grant-access
                    // etc.
                    return true;
                }
            }
        }

        return false;
    }
}
