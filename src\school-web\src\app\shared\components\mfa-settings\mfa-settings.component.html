<div class="mfa-settings-container">
  <!-- MFA Status Card -->
  <mat-card class="mfa-status-card" *ngIf="!showSetupFlow && !showDisableFlow">
    <mat-card-header>
      <div class="status-header">
        <mat-icon class="security-icon" [class.enabled]="isMfaEnabled">
          {{ isMfaEnabled ? 'verified_user' : 'security' }}
        </mat-icon>
        <div class="status-content">
          <h3 class="status-title">{{ 'MFA.TITLE' | translate }}</h3>
          <p class="status-description">
            {{ (isMfaEnabled ? 'MFA.ENABLED_DESCRIPTION' : 'MFA.DISABLED_DESCRIPTION') | translate }}
          </p>
        </div>
      </div>
    </mat-card-header>

    <mat-card-content>
      <div class="status-indicator" [class.enabled]="isMfaEnabled">
        <mat-icon>{{ isMfaEnabled ? 'check_circle' : 'warning' }}</mat-icon>
        <span class="status-text">
          {{ (isMfaEnabled ? 'MFA.STATUS_ENABLED' : 'MFA.STATUS_DISABLED') | translate }}
        </span>
      </div>

      <div class="mfa-info">
        <p>{{ 'MFA.INFO_TEXT' | translate }}</p>
        <ul class="benefits-list">
          <li>{{ 'MFA.BENEFIT_1' | translate }}</li>
          <li>{{ 'MFA.BENEFIT_2' | translate }}</li>
          <li>{{ 'MFA.BENEFIT_3' | translate }}</li>
        </ul>
      </div>
    </mat-card-content>

    <mat-card-actions>
      <button 
        *ngIf="!isMfaEnabled"
        mat-flat-button 
        color="primary" 
        (click)="showSetupFlow = true"
        class="action-button">
        <mat-icon>security</mat-icon>
        {{ 'MFA.ENABLE_MFA' | translate }}
      </button>

      <button 
        *ngIf="isMfaEnabled"
        mat-stroked-button 
        color="warn" 
        (click)="showDisableFlow = true"
        class="action-button">
        <mat-icon>no_encryption</mat-icon>
        {{ 'MFA.DISABLE_MFA' | translate }}
      </button>
    </mat-card-actions>
  </mat-card>

  <!-- MFA Setup Flow -->
  <mat-card class="setup-card" *ngIf="showSetupFlow">
    <mat-card-header>
      <div class="setup-header">
        <mat-icon class="setup-icon">security</mat-icon>
        <h3>{{ 'MFA.SETUP_TITLE' | translate }}</h3>
      </div>
    </mat-card-header>

    <mat-card-content>
      <mat-stepper [selectedIndex]="currentStep" orientation="vertical" #stepper>
        <!-- Step 1: Password Verification -->
        <mat-step [completed]="currentStep > 0">
          <ng-template matStepLabel>{{ 'MFA.STEP_1_TITLE' | translate }}</ng-template>
          <div class="step-content">
            <p>{{ 'MFA.STEP_1_DESCRIPTION' | translate }}</p>
            
            <form [formGroup]="setupForm" (ngSubmit)="startMfaSetup()">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>{{ 'MFA.PASSWORD' | translate }}</mat-label>
                <input matInput type="password" formControlName="password" required>
                <mat-icon matPrefix>lock</mat-icon>
                <mat-error>{{ getErrorMessage('setup', 'password') }}</mat-error>
              </mat-form-field>

              <div class="step-actions">
                <button mat-stroked-button type="button" (click)="cancelSetup()">
                  {{ 'COMMON.CANCEL' | translate }}
                </button>
                <button 
                  mat-flat-button 
                  color="primary" 
                  type="submit"
                  [disabled]="setupForm.invalid || isLoading">
                  <mat-icon *ngIf="isLoading" class="spinning">refresh</mat-icon>
                  <mat-icon *ngIf="!isLoading">arrow_forward</mat-icon>
                  {{ 'COMMON.CONTINUE' | translate }}
                </button>
              </div>
            </form>
          </div>
        </mat-step>

        <!-- Step 2: QR Code and Verification -->
        <mat-step [completed]="currentStep > 1">
          <ng-template matStepLabel>{{ 'MFA.STEP_2_TITLE' | translate }}</ng-template>
          <div class="step-content" *ngIf="mfaSetupData">
            <p>{{ 'MFA.STEP_2_DESCRIPTION' | translate }}</p>
            
            <div class="qr-section">
              <div class="qr-code">
                <img [src]="mfaSetupData.qrCodeUrl" alt="QR Code" class="qr-image">
              </div>
              <div class="manual-entry">
                <p>{{ 'MFA.MANUAL_ENTRY_TITLE' | translate }}</p>
                <mat-form-field appearance="outline" class="secret-field">
                  <mat-label>{{ 'MFA.SECRET_KEY' | translate }}</mat-label>
                  <input matInput [value]="mfaSetupData.secret" readonly>
                  <button mat-icon-button matSuffix type="button" 
                          (click)="copySecret()" 
                          [matTooltip]="'COMMON.COPY' | translate">
                    <mat-icon>content_copy</mat-icon>
                  </button>
                </mat-form-field>
              </div>
            </div>

            <form [formGroup]="verifyForm" (ngSubmit)="verifyAndEnableMfa()">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>{{ 'MFA.VERIFICATION_CODE' | translate }}</mat-label>
                <input matInput formControlName="verificationCode" maxlength="6" placeholder="000000">
                <mat-icon matPrefix>verified_user</mat-icon>
                <mat-hint>{{ 'MFA.VERIFICATION_HINT' | translate }}</mat-hint>
                <mat-error>{{ getErrorMessage('verify', 'verificationCode') }}</mat-error>
              </mat-form-field>

              <div class="step-actions">
                <button mat-stroked-button type="button" (click)="cancelSetup()">
                  {{ 'COMMON.CANCEL' | translate }}
                </button>
                <button 
                  mat-flat-button 
                  color="primary" 
                  type="submit"
                  [disabled]="verifyForm.invalid || isLoading">
                  <mat-icon *ngIf="isLoading" class="spinning">refresh</mat-icon>
                  <mat-icon *ngIf="!isLoading">verified</mat-icon>
                  {{ 'MFA.VERIFY_AND_ENABLE' | translate }}
                </button>
              </div>
            </form>
          </div>
        </mat-step>

        <!-- Step 3: Backup Codes -->
        <mat-step [completed]="currentStep > 2">
          <ng-template matStepLabel>{{ 'MFA.STEP_3_TITLE' | translate }}</ng-template>
          <div class="step-content">
            <div class="success-message">
              <mat-icon class="success-icon">check_circle</mat-icon>
              <h4>{{ 'MFA.SETUP_COMPLETE' | translate }}</h4>
              <p>{{ 'MFA.BACKUP_CODES_DESCRIPTION' | translate }}</p>
            </div>

            <div class="backup-codes-section">
              <h5>{{ 'MFA.BACKUP_CODES_TITLE' | translate }}</h5>
              <div class="backup-codes-grid">
                <mat-chip-set>
                  <mat-chip *ngFor="let code of backupCodes" class="backup-code">
                    {{ code }}
                  </mat-chip>
                </mat-chip-set>
              </div>

              <div class="backup-actions">
                <button mat-stroked-button (click)="downloadBackupCodes()">
                  <mat-icon>download</mat-icon>
                  {{ 'MFA.DOWNLOAD_CODES' | translate }}
                </button>
                <button mat-stroked-button (click)="copyBackupCodes()">
                  <mat-icon>content_copy</mat-icon>
                  {{ 'MFA.COPY_CODES' | translate }}
                </button>
              </div>

              <div class="warning-message">
                <mat-icon>warning</mat-icon>
                <span>{{ 'MFA.BACKUP_CODES_WARNING' | translate }}</span>
              </div>
            </div>

            <div class="step-actions">
              <button mat-flat-button color="primary" (click)="finishSetup()">
                <mat-icon>done</mat-icon>
                {{ 'COMMON.FINISH' | translate }}
              </button>
            </div>
          </div>
        </mat-step>
      </mat-stepper>
    </mat-card-content>
  </mat-card>

  <!-- MFA Disable Flow -->
  <mat-card class="disable-card" *ngIf="showDisableFlow">
    <mat-card-header>
      <div class="disable-header">
        <mat-icon class="disable-icon">no_encryption</mat-icon>
        <h3>{{ 'MFA.DISABLE_TITLE' | translate }}</h3>
      </div>
    </mat-card-header>

    <mat-card-content>
      <div class="warning-section">
        <mat-icon class="warning-icon">warning</mat-icon>
        <p>{{ 'MFA.DISABLE_WARNING' | translate }}</p>
      </div>

      <form [formGroup]="disableForm" (ngSubmit)="disableMfa()">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'MFA.PASSWORD' | translate }}</mat-label>
          <input matInput type="password" formControlName="password" required>
          <mat-icon matPrefix>lock</mat-icon>
          <mat-error>{{ getErrorMessage('disable', 'password') }}</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'MFA.CURRENT_MFA_CODE' | translate }}</mat-label>
          <input matInput formControlName="mfaCode" maxlength="6" placeholder="000000">
          <mat-icon matPrefix>verified_user</mat-icon>
          <mat-hint>{{ 'MFA.CURRENT_MFA_HINT' | translate }}</mat-hint>
          <mat-error>{{ getErrorMessage('disable', 'mfaCode') }}</mat-error>
        </mat-form-field>

        <div class="disable-actions">
          <button mat-stroked-button type="button" (click)="cancelDisable()">
            {{ 'COMMON.CANCEL' | translate }}
          </button>
          <button 
            mat-flat-button 
            color="warn" 
            type="submit"
            [disabled]="disableForm.invalid || isLoading">
            <mat-icon *ngIf="isLoading" class="spinning">refresh</mat-icon>
            <mat-icon *ngIf="!isLoading">no_encryption</mat-icon>
            {{ 'MFA.DISABLE_MFA' | translate }}
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
