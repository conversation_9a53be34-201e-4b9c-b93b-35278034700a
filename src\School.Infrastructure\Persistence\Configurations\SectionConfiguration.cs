using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class SectionConfiguration : IEntityTypeConfiguration<Section>
{
    public void Configure(EntityTypeBuilder<Section> builder)
    {
        builder.ToTable("Sections");

        builder.HasKey(s => s.Id);

        // Configure properties
        builder.Property(s => s.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(s => s.Code)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(s => s.Description)
            .HasMaxLength(500);

        builder.Property(s => s.Classroom)
            .HasMaxLength(100);

        builder.Property(s => s.RoomNumber)
            .HasMaxLength(50);

        builder.Property(s => s.Requirements)
            .HasMaxLength(1000);

        builder.Property(s => s.Remarks)
            .HasMaxLength(500);

        builder.Property(s => s.CreatedBy)
            .HasMaxLength(450);

        builder.Property(s => s.LastModifiedBy)
            .HasMaxLength(450);

        // Configure relationships
        builder.HasOne(s => s.Grade)
            .WithMany(g => g.Sections)
            .HasForeignKey(s => s.GradeId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(s => s.AcademicYear)
            .WithMany()
            .HasForeignKey(s => s.AcademicYearId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(s => s.Students)
            .WithOne()
            .HasForeignKey("CurrentSectionId")
            .OnDelete(DeleteBehavior.SetNull);



        builder.HasMany(s => s.Translations)
            .WithOne(t => t.Section)
            .HasForeignKey(t => t.SectionId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(s => new { s.Code, s.GradeId })
            .IsUnique();

        builder.HasIndex(s => new { s.Name, s.GradeId })
            .IsUnique();

        builder.HasIndex(s => s.GradeId);
        builder.HasIndex(s => s.AcademicYearId);
        builder.HasIndex(s => s.Type);
        builder.HasIndex(s => s.Medium);
        builder.HasIndex(s => s.Shift);
        builder.HasIndex(s => s.IsActive);

        // Global query filter for multi-tenancy
        builder.HasQueryFilter(s => EF.Property<string>(s, "TenantId") == null || 
                                   EF.Property<string>(s, "TenantId") == "");
    }
}
