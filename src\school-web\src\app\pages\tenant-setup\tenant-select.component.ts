import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { AuthService } from '../../core/services/auth.service';
import { TenantService } from '../../core/services/tenant.service';

@Component({
  selector: 'app-tenant-select',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    RouterModule,
    TranslateModule
  ],
  template: `
    <div class="tenant-select-container">
      <mat-card class="select-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>school</mat-icon>
            Select Your School
          </mat-card-title>
          <mat-card-subtitle>
            Choose which school you want to access
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <div *ngIf="userTenants.length > 0; else noTenants">
            <p>You have access to the following schools:</p>

            <mat-list>
              <mat-list-item *ngFor="let tenant of userTenants"
                           class="tenant-item"
                           (click)="selectTenant(tenant)">
                <mat-icon matListItemIcon>school</mat-icon>
                <div matListItemTitle>{{ tenant.tenantName || tenant.tenantSlug }}</div>
                <div matListItemLine>{{ tenant.role }} • {{ tenant.isActive ? 'Active' : 'Inactive' }}</div>
                <button mat-icon-button matListItemMeta>
                  <mat-icon>arrow_forward</mat-icon>
                </button>
              </mat-list-item>
            </mat-list>
          </div>

          <ng-template #noTenants>
            <div class="no-tenants">
              <mat-icon class="large-icon">school_off</mat-icon>
              <h3>No School Access</h3>
              <p>You don't have access to any schools yet.</p>
              <p>You can either:</p>
              <ul>
                <li>Create a new school if you're an administrator</li>
                <li>Contact your school administrator to get access</li>
              </ul>
            </div>
          </ng-template>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button routerLink="/login">
            <mat-icon>arrow_back</mat-icon>
            Back to Login
          </button>
          <div class="spacer"></div>
          <button mat-raised-button color="primary" routerLink="/tenant-setup/create">
            <mat-icon>add</mat-icon>
            Create New School
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .tenant-select-container {
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 20px;
      overflow-y: auto;
    }

    .select-card {
      max-width: 600px;
      width: 100%;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .tenant-item {
      cursor: pointer;
      border-radius: 8px;
      margin-bottom: 8px;
      transition: background-color 0.2s;
    }

    .tenant-item:hover {
      background-color: #f5f5f5;
    }

    .no-tenants {
      text-align: center;
      padding: 24px;
    }

    .large-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #ccc;
      margin-bottom: 16px;
    }

    .no-tenants h3 {
      margin: 0 0 16px 0;
      color: #333;
    }

    .no-tenants ul {
      text-align: left;
      max-width: 300px;
      margin: 16px auto;
    }

    mat-card-actions {
      display: flex;
      align-items: center;
      padding: 16px 24px;
    }

    .spacer {
      flex: 1;
    }
  `]
})
export class TenantSelectComponent implements OnInit, OnDestroy {
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private authService = inject(AuthService);
  private tenantService = inject(TenantService);

  userTenants: any[] = [];
  returnUrl = '/admin';

  ngOnInit() {
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/admin';
    this.loadUserTenants();
  }

  ngOnDestroy() {
    // Cleanup if needed
  }

  loadUserTenants() {
    const currentUser = this.authService.getCurrentUser();
    this.userTenants = currentUser?.tenants || [];
  }

  selectTenant(tenant: any) {
    if (!tenant.isActive) {
      // Handle inactive tenant
      this.router.navigate(['/tenant-setup/inactive']);
      return;
    }

    // Set the tenant and redirect
    const tenantSlug = tenant.tenantSlug || tenant.tenantId;
    this.tenantService.setDevelopmentTenant(tenantSlug);

    // Redirect to the return URL
    this.router.navigate([this.returnUrl]);
  }
}
