.classes-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.classes-content {
  max-width: 1000px;
  margin: 0 auto;
}

.class-selection-card,
.class-actions-card,
.students-card {
  margin-bottom: 24px;
}

.loading-indicator {
  margin: 16px 0;
}

.error-message {
  color: #c62828;
  text-align: center;
  padding: 16px 0;

  a {
    color: #3f51b5;
    cursor: pointer;
    text-decoration: underline;
  }
}

.no-subjects,
.no-students {
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
  padding: 24px 0;
}

.subject-selection {
  display: flex;
  justify-content: center;

  mat-form-field {
    width: 100%;
    max-width: 400px;
  }
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.actions-grid button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  height: auto;

  mat-icon {
    font-size: 32px;
    width: 32px;
    height: 32px;
    margin-bottom: 8px;
  }
}

.students-table-container {
  overflow-x: auto;
}

.students-table {
  width: 100%;
}

@media (max-width: 768px) {
  .actions-grid {
    grid-template-columns: 1fr;
  }
}
