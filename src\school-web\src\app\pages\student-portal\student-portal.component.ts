import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule, RouterOutlet } from '@angular/router';

// Angular Material imports
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { AuthService } from '../../core/services/auth.service';
import { StudentService } from '../../core/services/student.service';
import { Student } from '../../core/models/student.model';
import { LanguageThemeSwitcherComponent } from '../../shared/components/ui/language-theme-switcher/language-theme-switcher.component';

@Component({
  selector: 'app-student-portal',
  templateUrl: './student-portal.component.html',
  styleUrls: ['./student-portal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    RouterOutlet,
    MatToolbarModule,
    MatSidenavModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    MatMenuModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    LanguageThemeSwitcherComponent
  ]
})
export class StudentPortalComponent implements OnInit {
  student: Student | null = null;
  loading = true;
  error = false;

  navItems = [
    { label: 'Dashboard', icon: 'dashboard', route: '/student-portal/dashboard' },
    { label: 'Profile', icon: 'person', route: '/student-portal/profile' },
    { label: 'Attendance', icon: 'event_available', route: '/student-portal/attendance' },
    { label: 'Fees', icon: 'payment', route: '/student-portal/fees' },
    { label: 'Results', icon: 'assessment', route: '/student-portal/results' },
    { label: 'Leave Applications', icon: 'event_busy', route: '/student-portal/leaves' },
    { label: 'Academic History', icon: 'history', route: '/student-portal/academic-history' }
  ];

  constructor(
    private authService: AuthService,
    private studentService: StudentService,
    private router: Router,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadStudentData();
  }

  loadStudentData(): void {
    this.loading = true;
    const userId = this.authService.getCurrentUserId();

    if (!userId) {
      this.error = true;
      this.loading = false;
      this.router.navigate(['/login']);
      return;
    }

    // Check if the user has the correct role
    if (!this.authService.hasRole('Student')) {
      console.warn('User does not have Student role. Current role:', this.authService.getUserRole());

      // For testing purposes, we'll allow access but show a warning
      this.showErrorMessage('Warning: You are accessing the Student Portal but do not have the Student role. Some features may not work correctly.');

      // In a production environment, you would redirect to the appropriate portal:
      // this.error = true;
      // this.loading = false;
      // this.authService.navigateToUserPortal();
      // return;
    }

    // Fetch the student by user ID
    this.studentService.getStudentByUserId(userId)
      .subscribe({
        next: (student) => {
          this.student = student;
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading student data:', err);
          this.error = true;
          this.loading = false;

          // Check if this is a 401 error
          if (err.status === 401) {
            console.log('401 error when loading student data - token might be invalid');
            // Don't show error message or redirect to login, as the interceptor will handle it
          } else {
            this.showErrorMessage('Failed to load student data. Please try again later.');
          }
        }
      });
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
    this.showSuccessMessage('You have been successfully logged out.');
  }

  /**
   * Show a success message
   */
  private showSuccessMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  /**
   * Show an error message
   */
  private showErrorMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }
}
