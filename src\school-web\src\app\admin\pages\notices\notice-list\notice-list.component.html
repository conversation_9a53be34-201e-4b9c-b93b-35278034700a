<div class="notice-list-container">
  <div class="page-header">
    <h1>{{ 'admin.notices.title' | translate }}</h1>
    <a mat-raised-button color="primary" routerLink="/admin/notices/create">
      <mat-icon>add</mat-icon>
      {{ 'admin.notices.create' | translate }}
    </a>
  </div>

  <!-- Filter Section -->
  <mat-card class="filter-card">
    <mat-card-content>
      <div class="filter-form">
        <mat-form-field appearance="outline">
          <mat-label>{{ 'admin.notices.search' | translate }}</mat-label>
          <input matInput [(ngModel)]="filter.search" placeholder="{{ 'admin.notices.search_placeholder' | translate }}">
          <button *ngIf="filter.search" matSuffix mat-icon-button aria-label="Clear" (click)="filter.search = ''">
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'admin.notices.category' | translate }}</mat-label>
          <mat-select [(ngModel)]="filter.category">
            <mat-option *ngFor="let category of categories" [value]="category.value">
              {{ category.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'admin.notices.status' | translate }}</mat-label>
          <mat-select [(ngModel)]="filter.isActive">
            <mat-option *ngFor="let option of activeOptions" [value]="option.value">
              {{ option.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <div class="filter-actions">
          <button mat-raised-button color="primary" (click)="applyFilter()">
            <mat-icon>search</mat-icon>
            {{ 'admin.common.apply_filter' | translate }}
          </button>
          <button mat-button (click)="resetFilter()">
            <mat-icon>clear</mat-icon>
            {{ 'admin.common.reset' | translate }}
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Loading Spinner -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Error Message -->
  <div class="error-container" *ngIf="error">
    <mat-card class="error-card">
      <mat-card-content>
        <mat-icon color="warn">error</mat-icon>
        <p>{{ 'admin.common.error_loading' | translate }}</p>
        <button mat-raised-button color="primary" (click)="loadNotices()">
          <mat-icon>refresh</mat-icon>
          {{ 'admin.common.retry' | translate }}
        </button>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- No Results Message -->
  <div class="no-results-container" *ngIf="!isLoading && !error && notices.length === 0">
    <mat-card class="no-results-card">
      <mat-card-content>
        <mat-icon>info</mat-icon>
        <p>{{ 'admin.notices.no_notices' | translate }}</p>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Notices Table -->
  <div class="table-container" *ngIf="!isLoading && !error && notices.length > 0">
    <table mat-table [dataSource]="notices" matSort class="notices-table">
      <!-- Title Column -->
      <ng-container matColumnDef="title">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'admin.notices.title_column' | translate }}</th>
        <td mat-cell *matCellDef="let notice">{{ notice.title }}</td>
      </ng-container>

      <!-- Category Column -->
      <ng-container matColumnDef="category">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'admin.notices.category_column' | translate }}</th>
        <td mat-cell *matCellDef="let notice">{{ notice.category }}</td>
      </ng-container>

      <!-- Start Date Column -->
      <ng-container matColumnDef="startDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'admin.notices.start_date' | translate }}</th>
        <td mat-cell *matCellDef="let notice">{{ notice.startDate | date:'mediumDate' }}</td>
      </ng-container>

      <!-- End Date Column -->
      <ng-container matColumnDef="endDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'admin.notices.end_date' | translate }}</th>
        <td mat-cell *matCellDef="let notice">
          {{ notice.endDate ? (notice.endDate | date:'mediumDate') : ('admin.notices.no_end_date' | translate) }}
        </td>
      </ng-container>

      <!-- Priority Column -->
      <ng-container matColumnDef="priority">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'admin.notices.priority' | translate }}</th>
        <td mat-cell *matCellDef="let notice">
          <mat-chip [ngClass]="getPriorityClass(notice.priority)">
            {{ getPriorityLabel(notice.priority) }}
          </mat-chip>
        </td>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="isActive">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'admin.notices.status' | translate }}</th>
        <td mat-cell *matCellDef="let notice">
          <mat-chip [color]="notice.isActive ? 'primary' : 'warn'" selected>
            {{ notice.isActive ? ('admin.common.active' | translate) : ('admin.common.inactive' | translate) }}
          </mat-chip>
        </td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>{{ 'admin.common.actions' | translate }}</th>
        <td mat-cell *matCellDef="let notice">
          <button mat-icon-button [routerLink]="['/admin/notices', notice.id]" matTooltip="{{ 'admin.common.view' | translate }}">
            <mat-icon>visibility</mat-icon>
          </button>
          <button mat-icon-button [routerLink]="['/admin/notices/edit', notice.id]" matTooltip="{{ 'admin.common.edit' | translate }}">
            <mat-icon>edit</mat-icon>
          </button>
          <button mat-icon-button (click)="deleteNotice(notice)" matTooltip="{{ 'admin.common.delete' | translate }}">
            <mat-icon>delete</mat-icon>
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>

    <!-- Paginator -->
    <mat-paginator 
      [length]="totalCount"
      [pageSize]="filter.pageSize"
      [pageSizeOptions]="[5, 10, 25, 50]"
      (page)="onPageChange($event)"
      showFirstLastButtons>
    </mat-paginator>
  </div>
</div>
