using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class HostelFacility : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this hostel facility belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public HostelType Type { get; set; }
    public GenderType Gender { get; set; }
    public int Capacity { get; set; }
    public decimal MonthlyFee { get; set; }
    public string Location { get; set; } = string.Empty;
    public string ContactInfo { get; set; } = string.Empty;
    public bool HasWifi { get; set; }
    public bool HasAC { get; set; }
    public bool HasMeals { get; set; }
    public bool HasLaundry { get; set; }
    public bool HasStudyRoom { get; set; }
    public bool HasRecreationRoom { get; set; }
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }

    // Navigation properties
    public ICollection<HostelFacilityTranslation> Translations { get; set; } = new List<HostelFacilityTranslation>();
    public ICollection<HostelImage> Images { get; set; } = new List<HostelImage>();
}
