@use 'sass:color';
@use '../../../../styles/variables' as *;

// Dashboard Container
.dashboard-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// Welcome Section
.welcome-section {
  margin-bottom: 8px;

  .welcome-title {
    font-size: 24px;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: var(--sys-color-on-surface);
  }

  .welcome-subtitle {
    font-size: 16px;
    color: var(--sys-color-on-surface-variant);
    margin: 0;
  }
}

// Stats Grid
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;

  @media (max-width: 1200px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 600px) {
    grid-template-columns: 1fr;
  }
}

// Stats Card - Enhanced with theme support
.stats-card {
  .stats-card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
  }

  .stats-info {
    flex: 1;
  }

  .stats-trend {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #10b981;

    mat-icon {
      font-size: 16px;
      height: 16px;
      width: 16px;
      margin-right: 4px;
    }
  }
}

// Content Grid
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }
}

// Activity List - Using theme classes
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

// Task List - Using theme classes
.task-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

// Component uses theme classes from _admin-dashboard-theme.scss
// All styling is handled by the global theme system for consistency
