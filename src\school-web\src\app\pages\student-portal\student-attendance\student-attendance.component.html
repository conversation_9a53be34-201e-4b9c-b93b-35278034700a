<div class="attendance-container">
  <h1 class="page-title">Attendance Records</h1>

  <div *ngIf="loading.student" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error.student" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load student data. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadStudentData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading.student && !error.student && student" class="attendance-content">
    <!-- Filter Form -->
    <mat-card class="filter-card">
      <mat-card-content>
        <form [formGroup]="filterForm" (ngSubmit)="applyFilter()">
          <div class="filter-form">
            <mat-form-field appearance="outline">
              <mat-label>From Date</mat-label>
              <input matInput [matDatepicker]="fromPicker" formControlName="fromDate">
              <mat-datepicker-toggle matSuffix [for]="fromPicker"></mat-datepicker-toggle>
              <mat-datepicker #fromPicker></mat-datepicker>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>To Date</mat-label>
              <input matInput [matDatepicker]="toPicker" formControlName="toDate">
              <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
              <mat-datepicker #toPicker></mat-datepicker>
            </mat-form-field>

            <div class="filter-actions">
              <button mat-raised-button color="primary" type="submit">Apply Filter</button>
              <button mat-button type="button" (click)="resetFilter()">Reset</button>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>

    <!-- Attendance Stats -->
    <mat-card class="stats-card" *ngIf="!loading.attendance && !error.attendance">
      <mat-card-content>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ calculateAttendanceStats().total }}</div>
            <div class="stat-label">Total Days</div>
          </div>
          <div class="stat-item">
            <div class="stat-value present">{{ calculateAttendanceStats().present }}</div>
            <div class="stat-label">Present</div>
          </div>
          <div class="stat-item">
            <div class="stat-value absent">{{ calculateAttendanceStats().absent }}</div>
            <div class="stat-label">Absent</div>
          </div>
          <div class="stat-item">
            <div class="stat-value late">{{ calculateAttendanceStats().late }}</div>
            <div class="stat-label">Late</div>
          </div>
          <div class="stat-item">
            <div class="stat-value excused">{{ calculateAttendanceStats().excused }}</div>
            <div class="stat-label">Excused</div>
          </div>
          <div class="stat-item">
            <div class="stat-value on-leave">{{ calculateAttendanceStats().onLeave }}</div>
            <div class="stat-label">On Leave</div>
          </div>
          <div class="stat-item percentage">
            <div class="stat-value">{{ calculateAttendanceStats().presentPercentage.toFixed(2) }}%</div>
            <div class="stat-label">Attendance Rate</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Attendance Table -->
    <div class="table-container mat-elevation-z8">
      <div *ngIf="loading.attendance" class="table-loading-shade">
        <mat-spinner></mat-spinner>
      </div>

      <div *ngIf="error.attendance" class="table-error-shade">
        <div class="error-message">
          <mat-icon>error</mat-icon>
          <span>Failed to load attendance records</span>
          <button mat-button color="primary" (click)="loadAttendanceData()">Retry</button>
        </div>
      </div>

      <table mat-table [dataSource]="dataSource" matSort class="attendance-table">
        <!-- Date Column -->
        <ng-container matColumnDef="date">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
          <td mat-cell *matCellDef="let record">{{ record.date | date:'mediumDate' }}</td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
          <td mat-cell *matCellDef="let record">
            <span class="status-badge" [ngClass]="getStatusClass(record.status)">
              {{ getStatusText(record.status) }}
            </span>
          </td>
        </ng-container>

        <!-- Period Column -->
        <ng-container matColumnDef="period">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Period</th>
          <td mat-cell *matCellDef="let record">{{ record.period }}</td>
        </ng-container>

        <!-- Subject Column -->
        <ng-container matColumnDef="subjectCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Subject</th>
          <td mat-cell *matCellDef="let record">{{ record.subjectCode }}</td>
        </ng-container>

        <!-- Remarks Column -->
        <ng-container matColumnDef="remarks">
          <th mat-header-cell *matHeaderCellDef>Remarks</th>
          <td mat-cell *matCellDef="let record">{{ record.remarks }}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

        <!-- Row shown when there is no matching data. -->
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="5">No attendance records found</td>
        </tr>
      </table>

      <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
    </div>
  </div>
</div>
