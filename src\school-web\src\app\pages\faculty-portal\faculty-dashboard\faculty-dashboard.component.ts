import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { RouterModule } from '@angular/router';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { FacultyService } from '../../../core/services/faculty.service';
import { AuthService } from '../../../core/services/auth.service';
import { FacultyDetail, FacultySchedule, DayOfWeek } from '../../../core/models/faculty.model';
import { StudentLeave } from '../../../core/models/student.model';

@Component({
  selector: 'app-faculty-dashboard',
  templateUrl: './faculty-dashboard.component.html',
  styleUrls: ['./faculty-dashboard.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    DatePipe,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatExpansionModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatTableModule,
    MatSnackBarModule
  ]
})
export class FacultyDashboardComponent implements OnInit {
  faculty: FacultyDetail | null = null;
  todaySchedule: FacultySchedule[] = [];
  pendingLeaves: StudentLeave[] = [];

  loading = {
    faculty: true,
    schedule: false,
    leaves: false
  };

  error = {
    faculty: false,
    schedule: false,
    leaves: false
  };

  constructor(
    private facultyService: FacultyService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadFacultyData();
  }

  loadFacultyData(): void {
    this.loading.faculty = true;

    const userId = this.authService.getCurrentUserId();

    if (!userId) {
      this.error.faculty = true;
      this.loading.faculty = false;
      this.snackBar.open('User not authenticated', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    this.facultyService.getFacultyByUserId(userId)
      .subscribe({
        next: (faculty) => {
          this.faculty = faculty;
          this.loading.faculty = false;
          this.loadTodaySchedule();
          this.loadPendingLeaves();
        },
        error: (err) => {
          console.error('Error loading faculty data:', err);
          this.error.faculty = true;
          this.loading.faculty = false;
          this.snackBar.open('Failed to load faculty data', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  loadTodaySchedule(): void {
    if (!this.faculty) return;

    this.loading.schedule = true;
    this.error.schedule = false;

    const today = new Date().getDay();
    // Convert JavaScript day (0 = Sunday) to our DayOfWeek enum
    const dayOfWeek = today as DayOfWeek;

    this.facultyService.getFacultySchedule(this.faculty.id, dayOfWeek)
      .subscribe({
        next: (schedule) => {
          this.todaySchedule = schedule.sort((a, b) => {
            return a.periodNumber - b.periodNumber;
          });
          this.loading.schedule = false;
        },
        error: (err) => {
          console.error('Error loading schedule:', err);
          this.error.schedule = true;
          this.loading.schedule = false;
          this.snackBar.open('Failed to load today\'s schedule', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  loadPendingLeaves(): void {
    if (!this.faculty) return;

    this.loading.leaves = true;
    this.error.leaves = false;

    this.facultyService.getPendingLeaveApplications(this.faculty.id)
      .subscribe({
        next: (leaves) => {
          this.pendingLeaves = leaves;
          this.loading.leaves = false;
        },
        error: (err) => {
          console.error('Error loading pending leaves:', err);
          this.error.leaves = true;
          this.loading.leaves = false;
          this.snackBar.open('Failed to load pending leave applications', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  getDayName(day: DayOfWeek): string {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[day];
  }

  getCurrentDayName(): string {
    const today = new Date().getDay();
    return this.getDayName(today as DayOfWeek);
  }

  approveLeave(leaveId: number): void {
    if (!this.faculty) return;

    this.facultyService.approveLeaveApplication(this.faculty.id, leaveId, 'Approved by class teacher')
      .subscribe({
        next: () => {
          this.loadPendingLeaves();
          this.snackBar.open('Leave application approved successfully', 'Close', {
            duration: 3000
          });
        },
        error: (err) => {
          console.error('Error approving leave:', err);
          this.snackBar.open('Failed to approve leave application', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  rejectLeave(leaveId: number): void {
    if (!this.faculty) return;

    this.facultyService.rejectLeaveApplication(this.faculty.id, leaveId, 'Rejected by class teacher')
      .subscribe({
        next: () => {
          this.loadPendingLeaves();
          this.snackBar.open('Leave application rejected successfully', 'Close', {
            duration: 3000
          });
        },
        error: (err) => {
          console.error('Error rejecting leave:', err);
          this.snackBar.open('Failed to reject leave application', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  getLeaveTypeLabel(type: number): string {
    const types = ['Sick', 'Personal', 'Family', 'Religious', 'Other'];
    return types[type];
  }

  calculateLeaveDays(startDate: Date, endDate: Date): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  }
}
