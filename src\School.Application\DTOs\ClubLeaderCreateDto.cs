using System;
using System.ComponentModel.DataAnnotations;

namespace School.Application.DTOs
{
    public class ClubLeaderCreateDto
    {
        public int? StudentId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Role { get; set; }
        
        [StringLength(20)]
        public string Grade { get; set; }
        
        public int? ProfileImageId { get; set; }
        
        [Url]
        public string ProfileImageUrl { get; set; }
        
        public int DisplayOrder { get; set; }
    }
}
