<div class="tenant-list-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <mat-icon>business</mat-icon>
        {{ 'admin.tenants.title' | translate }}
      </h2>
      <p class="page-subtitle">{{ 'admin.tenants.subtitle' | translate }}</p>
    </div>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="createTenant()">
        <mat-icon>add</mat-icon>
        {{ 'admin.tenants.create' | translate }}
      </button>
    </div>
  </div>

  <!-- Filters Section -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-row">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>{{ 'admin.tenants.search' | translate }}</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup.enter)="onSearch()" placeholder="Search by name, slug, or domain">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>{{ 'admin.tenants.status_filter' | translate }}</mat-label>
          <mat-select [(value)]="statusFilter" (selectionChange)="onStatusFilterChange()">
            <mat-option value="all">All Status</mat-option>
            <mat-option value="active">Active</mat-option>
            <mat-option value="inactive">Inactive</mat-option>
            <mat-option value="trial">Trial</mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-raised-button color="primary" (click)="onSearch()">
          <mat-icon>search</mat-icon>
          {{ 'admin.common.search' | translate }}
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Tenants Table -->
  <mat-card class="table-card">
    <mat-card-content>
      <div class="table-container">
        <table mat-table [dataSource]="tenants" class="tenants-table" matSort>
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'admin.tenants.name' | translate }}
            </th>
            <td mat-cell *matCellDef="let tenant">
              <div class="tenant-info">
                <div class="tenant-icon">
                  <mat-icon>{{ getTypeIcon(tenant.type) }}</mat-icon>
                </div>
                <div class="tenant-details">
                  <div class="tenant-name">{{ tenant.displayName || tenant.name }}</div>
                  <div class="tenant-domain" *ngIf="tenant.customDomain">{{ tenant.customDomain }}</div>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Slug Column -->
          <ng-container matColumnDef="slug">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'admin.tenants.slug' | translate }}
            </th>
            <td mat-cell *matCellDef="let tenant">
              <code class="tenant-slug">{{ tenant.slug }}</code>
            </td>
          </ng-container>

          <!-- Type Column -->
          <ng-container matColumnDef="type">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'admin.tenants.type' | translate }}
            </th>
            <td mat-cell *matCellDef="let tenant">
              <mat-chip [color]="getStatusColor(tenant.type)">{{ tenant.type }}</mat-chip>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'admin.tenants.status' | translate }}
            </th>
            <td mat-cell *matCellDef="let tenant">
              <mat-chip [color]="getStatusColor(tenant.status)">
                {{ tenant.isActive ? 'Active' : 'Inactive' }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- User Count Column -->
          <ng-container matColumnDef="userCount">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'admin.tenants.users' | translate }}
            </th>
            <td mat-cell *matCellDef="let tenant">
              <span class="user-count">{{ tenant.userCount || 0 }}</span>
            </td>
          </ng-container>

          <!-- Trial Status Column -->
          <ng-container matColumnDef="trialStatus">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'admin.tenants.trial' | translate }}
            </th>
            <td mat-cell *matCellDef="let tenant">
              <div *ngIf="tenant.isTrialActive" class="trial-info">
                <mat-chip color="accent">Trial</mat-chip>
                <div class="trial-end" *ngIf="tenant.trialEndDate">
                  Ends: {{ tenant.trialEndDate | date:'short' }}
                </div>
              </div>
              <span *ngIf="!tenant.isTrialActive">-</span>
            </td>
          </ng-container>

          <!-- Created Date Column -->
          <ng-container matColumnDef="createdAt">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'admin.tenants.created' | translate }}
            </th>
            <td mat-cell *matCellDef="let tenant">
              {{ tenant.createdAt | date:'medium' }}
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'admin.common.actions' | translate }}
            </th>
            <td mat-cell *matCellDef="let tenant">
              <div class="action-buttons">
                <button mat-icon-button [matTooltip]="'admin.common.view' | translate" (click)="viewTenant(tenant)">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-icon-button [matTooltip]="'admin.common.edit' | translate" (click)="editTenant(tenant)">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button 
                        [matTooltip]="tenant.isActive ? ('admin.common.deactivate' | translate) : ('admin.common.activate' | translate)"
                        (click)="toggleTenantStatus(tenant)">
                  <mat-icon>{{ tenant.isActive ? 'block' : 'check_circle' }}</mat-icon>
                </button>
                <button mat-icon-button [matTooltip]="'admin.common.delete' | translate" (click)="deleteTenant(tenant)" color="warn">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- Loading State -->
        <div *ngIf="loading" class="loading-container">
          <mat-spinner></mat-spinner>
          <p>{{ 'admin.common.loading' | translate }}</p>
        </div>

        <!-- Empty State -->
        <div *ngIf="!loading && tenants.length === 0" class="empty-state">
          <mat-icon>business</mat-icon>
          <h3>{{ 'admin.tenants.no_tenants' | translate }}</h3>
          <p>{{ 'admin.tenants.no_tenants_message' | translate }}</p>
          <button mat-raised-button color="primary" (click)="createTenant()">
            <mat-icon>add</mat-icon>
            {{ 'admin.tenants.create_first' | translate }}
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
