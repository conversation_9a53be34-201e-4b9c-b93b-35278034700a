using School.Domain.Common;

using System;

namespace School.Domain.Entities
{
    public class ClubLeader : BaseEntity
    {
        public Guid ClubId { get; set; }
        public Guid? StudentId { get; set; }
        public string Name { get; set; }
        public string Role { get; set; }
        public string Grade { get; set; }
        public int? ProfileImageId { get; set; }
        public string ProfileImageUrl { get; set; }
        public int DisplayOrder { get; set; }
        
        // Navigation properties
        public Club Club { get; set; }
        public Student Student { get; set; }
    }
}
