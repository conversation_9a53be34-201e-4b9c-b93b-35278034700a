<div class="menu-item" (mouseenter)="openMenu(academicsMenuTrigger)" (mouseleave)="closeMenu()">
  <button mat-button [matMenuTriggerFor]="academicsMenu" #academicsMenuTrigger="matMenuTrigger"
          class="menu-trigger" [class.active]="activeRoute.startsWith('/academics')">
    {{ 'NAV.ACADEMICS' | translate }}
    <mat-icon>arrow_drop_down</mat-icon>
  </button>
  <mat-menu #academicsMenu="matMenu" class="mega-menu-panel" [overlapTrigger]="false" [hasBackdrop]="false">
    <div class="mega-menu-content" (mouseenter)="clearCloseTimeout()" (mouseleave)="closeMenu()">
      <div class="menu-column">
        <h3>{{ 'ACADEMICS.PROGRAMS' | translate }}</h3>
        <a mat-menu-item routerLink="/academics/elementary" [class.active]="activeRoute === '/academics/elementary'">{{ 'ACADEMICS.ELEMENTARY' | translate }}</a>
        <a mat-menu-item routerLink="/academics/middle" [class.active]="activeRoute === '/academics/middle'">{{ 'ACADEMICS.MIDDLE' | translate }}</a>
        <a mat-menu-item routerLink="/academics/high" [class.active]="activeRoute === '/academics/high'">{{ 'ACADEMICS.HIGH' | translate }}</a>
        <a mat-menu-item routerLink="/academics/curriculum" [class.active]="activeRoute === '/academics/curriculum'">{{ 'ACADEMICS.CURRICULUM' | translate }}</a>
      </div>
      <div class="menu-column">
        <h3>{{ 'ACADEMICS.SUBJECTS' | translate }}</h3>
        <a mat-menu-item routerLink="/academics/stem" [class.active]="activeRoute === '/academics/stem'">{{ 'ACADEMICS.STEM' | translate }}</a>
        <a mat-menu-item routerLink="/academics/languages" [class.active]="activeRoute === '/academics/languages'">{{ 'ACADEMICS.LANGUAGES' | translate }}</a>
        <a mat-menu-item routerLink="/academics/arts" [class.active]="activeRoute === '/academics/arts'">{{ 'ACADEMICS.ARTS' | translate }}</a>
        <a mat-menu-item routerLink="/academics/sports" [class.active]="activeRoute === '/academics/sports'">{{ 'ACADEMICS.SPORTS' | translate }}</a>
      </div>
      <div class="menu-column">
        <h3>{{ 'ACADEMICS.RESOURCES' | translate }}</h3>
        <a mat-menu-item routerLink="/academics/library" [class.active]="activeRoute === '/academics/library'">{{ 'ACADEMICS.LIBRARY' | translate }}</a>
        <a mat-menu-item routerLink="/academics/technology" [class.active]="activeRoute === '/academics/technology'">{{ 'ACADEMICS.TECHNOLOGY' | translate }}</a>
        <a mat-menu-item routerLink="/academics/counseling" [class.active]="activeRoute === '/academics/counseling'">{{ 'ACADEMICS.COUNSELING' | translate }}</a>
      </div>
    </div>
  </mat-menu>
</div>
