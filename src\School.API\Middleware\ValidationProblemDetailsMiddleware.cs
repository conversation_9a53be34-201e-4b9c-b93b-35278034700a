using Microsoft.AspNetCore.Http;
using School.API.Common;
using System.Threading.Tasks;

namespace School.API.Middleware
{
    public class ValidationProblemDetailsMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ValidationProblemDetailsMiddleware> _logger;

        public ValidationProblemDetailsMiddleware(RequestDelegate next, ILogger<ValidationProblemDetailsMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            await _next(context);

            // Check if we have a custom problem details response
            if (context.Items.TryGetValue("CustomProblemDetails", out var customResponse) && 
                customResponse is ApiResponse apiResponse)
            {
                // Only override the response if it's an error status code
                if (context.Response.StatusCode >= 400)
                {
                    context.Response.ContentType = "application/json";
                    await context.Response.WriteAsJsonAsync(apiResponse);
                }
            }
        }
    }

    // Extension method to add the middleware to the pipeline
    public static class ValidationProblemDetailsMiddlewareExtensions
    {
        public static IApplicationBuilder UseValidationProblemDetails(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<ValidationProblemDetailsMiddleware>();
        }
    }
}
