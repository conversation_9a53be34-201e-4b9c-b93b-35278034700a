using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Content;
using School.Domain.Entities;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Services
{
    public class ContentService : IContentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<ContentService> _logger;

        public ContentService(
            IUnitOfWork unitOfWork,
            ICurrentUserService currentUserService,
            ILogger<ContentService> logger)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        public async Task<(IEnumerable<ContentDto> Contents, int TotalCount)> GetAllContentAsync(ContentFilterDto filter)
        {
            _logger.LogInformation("Getting all content with filter: {Filter}", new { filter.Type, filter.Published, filter.Search, filter.Page, filter.PageSize });

            var repository = _unitOfWork.Repository<Content>();
            var query = repository.AsQueryable("Translations", "MediaItems");

            // Apply filters
            if (filter.Type.HasValue)
                query = query.Where(c => c.Type == filter.Type);

            if (filter.Published.HasValue)
                query = query.Where(c => c.IsPublished == filter.Published);

            if (!string.IsNullOrEmpty(filter.Search))
                query = query.Where(c => c.Title.Contains(filter.Search) || c.Body.Contains(filter.Search));

            // Get total count
            var totalCount = await query.CountAsync();

            // Get paginated results
            var contents = await query
                .OrderByDescending(c => c.CreatedAt)
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(c => new ContentDto
                {
                    Id = c.Id,
                    Title = c.Title,
                    Slug = c.Slug,
                    Body = c.Body,
                    Type = c.Type,
                    MetaDescription = c.MetaDescription,
                    IsPublished = c.IsPublished,
                    PublishedAt = c.PublishedAt,
                    CreatedAt = c.CreatedAt,
                    LastModifiedAt = c.LastModifiedAt
                })
                .ToListAsync();

            _logger.LogInformation($"Retrieved {contents.Count} contents out of {totalCount}");
            return (contents, totalCount);
        }

        public async Task<ContentDto?> GetContentByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting content by ID: {ContentId}", id);

            var repository = _unitOfWork.Repository<Content>();
            var content = await repository.GetByIdAsync(id, new[] { "Translations", "MediaItems" });

            if (content == null)
            {
                _logger.LogWarning("Content with ID {ContentId} not found", id);
                return null;
            }

            return new ContentDto
            {
                Id = content.Id,
                Title = content.Title,
                Slug = content.Slug,
                Body = content.Body,
                Type = content.Type,
                MetaDescription = content.MetaDescription,
                IsPublished = content.IsPublished,
                PublishedAt = content.PublishedAt,
                CreatedAt = content.CreatedAt,
                LastModifiedAt = content.LastModifiedAt
            };
        }

        public async Task<ContentDto?> GetContentBySlugAsync(string slug)
        {
            _logger.LogInformation("Getting content by slug: {Slug}", slug);

            var repository = _unitOfWork.Repository<Content>();
            var content = await repository.FindAsync(c => c.Slug == slug, new[] { "Translations", "MediaItems" });

            if (!content.Any())
            {
                _logger.LogWarning("Content with slug {Slug} not found", slug);
                return null;
            }

            var firstContent = content.First();
            return new ContentDto
            {
                Id = firstContent.Id,
                Title = firstContent.Title,
                Slug = firstContent.Slug,
                Body = firstContent.Body,
                Type = firstContent.Type,
                MetaDescription = firstContent.MetaDescription,
                IsPublished = firstContent.IsPublished,
                PublishedAt = firstContent.PublishedAt,
                CreatedAt = firstContent.CreatedAt,
                LastModifiedAt = firstContent.LastModifiedAt
            };
        }

        public async Task<Guid> CreateContentAsync(ContentCreateDto contentDto)
        {
            _logger.LogInformation("Creating new content with title: {Title}", contentDto.Title);

            try
            {
                var repository = _unitOfWork.Repository<Content>();

                // Check if slug already exists
                if (!string.IsNullOrEmpty(contentDto.Slug))
                {
                    var existingContent = await repository.FindAsync(c => c.Slug == contentDto.Slug);
                    if (existingContent.Any())
                    {
                        _logger.LogWarning("Content with slug {Slug} already exists", contentDto.Slug);
                        throw new InvalidOperationException($"Content with slug '{contentDto.Slug}' already exists");
                    }
                }

                var content = new Content
                {
                    Title = contentDto.Title,
                    Slug = contentDto.Slug,
                    Body = contentDto.Body,
                    Type = contentDto.Type,
                    MetaDescription = contentDto.MetaDescription,
                    IsPublished = contentDto.IsPublished,
                    PublishedAt = contentDto.IsPublished ? DateTime.UtcNow : null,
                    CreatedAt = DateTime.UtcNow,
                    CreatedById = _currentUserService.UserId ?? throw new InvalidOperationException("UserId cannot be null")
                };

                await repository.AddAsync(content);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Content created successfully with ID: {ContentId}", content.Id);
                return content.Id;
            }
            catch (Exception ex) when (ex is not InvalidOperationException)
            {
                _logger.LogError(ex, "Error creating content: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        public async Task<bool> UpdateContentAsync(Guid id, ContentUpdateDto contentDto)
        {
            _logger.LogInformation("Updating content with ID: {ContentId}", id);

            try
            {
                var repository = _unitOfWork.Repository<Content>();
                var content = await repository.GetByIdAsync(id);

                if (content == null)
                {
                    _logger.LogWarning("Content with ID {ContentId} not found for update", id);
                    return false;
                }

                // Check if slug is being changed and already exists
                if (contentDto.Slug != null && contentDto.Slug != content.Slug)
                {
                    var existingContent = await repository.FindAsync(c => c.Slug == contentDto.Slug && c.Id != id);
                    if (existingContent.Any())
                    {
                        _logger.LogWarning("Content with slug {Slug} already exists", contentDto.Slug);
                        throw new InvalidOperationException($"Content with slug '{contentDto.Slug}' already exists");
                    }
                }

                content.Title = contentDto.Title ?? content.Title;
                content.Slug = contentDto.Slug ?? content.Slug;
                content.Body = contentDto.Body ?? content.Body;
                content.Type = contentDto.Type ?? content.Type;
                content.MetaDescription = contentDto.MetaDescription ?? content.MetaDescription;
                content.IsPublished = contentDto.IsPublished ?? content.IsPublished;

                // Update published date if publishing for the first time
                if (content.IsPublished && !content.PublishedAt.HasValue)
                {
                    content.PublishedAt = DateTime.UtcNow;
                }

                // LastModifiedAt is set below
                content.LastModifiedById = _currentUserService.UserId ?? Guid.Parse("00000000-0000-0000-0000-000000000001");
                content.LastModifiedAt = DateTime.UtcNow;

                await repository.UpdateAsync(content);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Content with ID {ContentId} updated successfully", id);
                return true;
            }
            catch (Exception ex) when (ex is not InvalidOperationException)
            {
                _logger.LogError(ex, "Error updating content with ID {ContentId}: {ErrorMessage}", id, ex.Message);
                throw;
            }
        }

        public async Task<bool> DeleteContentAsync(Guid id)
        {
            _logger.LogInformation("Deleting content with ID: {ContentId}", id);

            try
            {
                var repository = _unitOfWork.Repository<Content>();

                // Check if content exists
                var content = await repository.GetByIdAsync(id);
                if (content == null)
                {
                    _logger.LogWarning("Content with ID {ContentId} not found for deletion", id);
                    return false;
                }

                // Use the repository's DeleteByIdAsync method which handles soft delete
                await repository.DeleteByIdAsync(id);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Content with ID {ContentId} deleted successfully", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting content with ID {ContentId}: {ErrorMessage}", id, ex.Message);
                throw;
            }
        }
    }
}
