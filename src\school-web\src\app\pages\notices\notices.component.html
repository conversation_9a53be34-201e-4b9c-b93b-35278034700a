<div class="notices-container">
  <div class="page-header">
    <h1>{{ 'NOTICE.ALL_NOTICES' | translate }}</h1>
    <p class="subtitle">{{ 'NOTICE.STAY_UPDATED' | translate }}</p>
  </div>

  <div class="notices-filter">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'NOTICE.FILTER_BY' | translate }}</mat-label>
      <mat-select [(value)]="selectedFilter" (selectionChange)="applyFilters()">
        <mat-option value="all">{{ 'NOTICE.ALL_CATEGORIES' | translate }}</mat-option>
        <mat-option value="admissions">{{ 'NOTICE.CATEGORY_ADMISSIONS' | translate }}</mat-option>
        <mat-option value="academics">{{ 'NOTICE.CATEGORY_ACADEMICS' | translate }}</mat-option>
        <mat-option value="events">{{ 'NOTICE.CATEGORY_EVENTS' | translate }}</mat-option>
        <mat-option value="sports">{{ 'NOTICE.CATEGORY_SPORTS' | translate }}</mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline" class="search-field">
      <mat-label>{{ 'NOTICE.SEARCH' | translate }}</mat-label>
      <input matInput [(ngModel)]="searchText" (ngModelChange)="applyFilters()" placeholder="{{ 'NOTICE.SEARCH_PLACEHOLDER' | translate }}">
      <button *ngIf="searchText" matSuffix mat-icon-button aria-label="Clear" (click)="searchText=''; applyFilters()">
        <mat-icon>close</mat-icon>
      </button>
    </mat-form-field>
  </div>

  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <div class="error-container" *ngIf="error">
    <p class="error-message">{{ error }}</p>
    <button mat-raised-button color="primary" (click)="loadNotices()">
      {{ 'NOTICE.TRY_AGAIN' | translate }}
    </button>
  </div>

  <div class="notices-list" *ngIf="!isLoading && !error">
    <div *ngIf="filteredNotices.length === 0" class="no-notices">
      <p>{{ 'NOTICE.NO_NOTICES' | translate }}</p>
    </div>

    <mat-card *ngFor="let notice of filteredNotices" class="notice-card" [ngClass]="getPriorityClass(notice.priority)">
      <mat-card-header>
        <div mat-card-avatar class="notice-icon">
          <mat-icon>{{getIconForCategory(notice.category)}}</mat-icon>
        </div>
        <mat-card-title>{{notice.title}}</mat-card-title>
        <mat-card-subtitle>{{notice.startDate | date:'mediumDate'}} • {{notice.category}}</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <p>{{notice.content}}</p>
        <mat-chip-listbox *ngIf="notice.endDate">
          <mat-chip color="accent" selected>{{ 'NOTICE.VALID_UNTIL' | translate }}: {{notice.endDate | date:'mediumDate'}}</mat-chip>
        </mat-chip-listbox>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" [routerLink]="['/notice', notice.id]">
          {{ 'NOTICE.READ_MORE' | translate }}
        </button>
      </mat-card-actions>
    </mat-card>

    <div *ngIf="filteredNotices.length === 0" class="no-notices">
      <mat-icon>info</mat-icon>
      <p>{{ 'NOTICE.NO_NOTICES_FOUND' | translate }}</p>
    </div>
  </div>

  <mat-paginator [length]="totalNotices"
                [pageSize]="pageSize"
                [pageSizeOptions]="[5, 10, 25, 50]"
                (page)="onPageChange($event)">
  </mat-paginator>
</div>
