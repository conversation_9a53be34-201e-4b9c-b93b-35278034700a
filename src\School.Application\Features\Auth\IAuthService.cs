using School.Application.DTOs;
using System.Threading.Tasks;

namespace School.Application.Features.Auth
{
    public interface IAuthService
    {
        /// <summary>
        /// Authenticate user with username/password and optional MFA
        /// </summary>
        Task<LoginResponseDto?> LoginAsync(LoginDto loginDto);

        /// <summary>
        /// Register a new user
        /// </summary>
        Task<Guid> RegisterAsync(UserCreateDto userDto);

        /// <summary>
        /// Refresh access token using refresh token
        /// </summary>
        Task<RefreshTokenResponseDto?> RefreshTokenAsync(RefreshTokenDto refreshTokenDto);

        /// <summary>
        /// Logout user and revoke refresh token
        /// </summary>
        Task LogoutAsync(string refreshToken);

        /// <summary>
        /// Revoke all refresh tokens for a user
        /// </summary>
        Task<bool> RevokeAllTokensAsync(string userId);
    }
}