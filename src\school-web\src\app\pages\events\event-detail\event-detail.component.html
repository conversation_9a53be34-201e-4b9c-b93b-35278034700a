<!-- Loading State -->
<div class="loading-container" *ngIf="loading">
  <mat-icon class="loading-icon">hourglass_empty</mat-icon>
  <p>{{ 'EVENTS.LOADING_EVENT' | translate }}</p>
</div>

<!-- Error State -->
<div class="error-container" *ngIf="error">
  <mat-icon class="error-icon">error_outline</mat-icon>
  <h2>{{ 'EVENTS.EVENT_NOT_FOUND' | translate }}</h2>
  <p>{{ 'EVENTS.EVENT_NOT_FOUND_MESSAGE' | translate }}</p>
  <button mat-raised-button color="primary" (click)="goBack()">{{ 'EVENTS.BACK_TO_EVENTS' | translate }}</button>
</div>

<!-- Event Detail Content -->
<div class="event-detail-container" *ngIf="!loading && !error && event">
  <!-- Event Header -->
  <section class="event-header">
    <div class="container">
      <button mat-button class="back-button" (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> {{ 'EVENTS.BACK_TO_EVENTS' | translate }}
      </button>
      
      <div class="event-image">
        <img [src]="event.image" [alt]="event.title">
        <div class="event-category">{{event.category}}</div>
        <div class="event-status" *ngIf="isEventPast()">{{ 'EVENTS.PAST_EVENT' | translate }}</div>
      </div>
      
      <div class="event-header-content">
        <h1 class="event-title">{{event.title}}</h1>
        
        <div class="event-meta">
          <div class="meta-item">
            <mat-icon>event</mat-icon>
            <span>{{formatDateRange()}}</span>
          </div>
          <div class="meta-item">
            <mat-icon>schedule</mat-icon>
            <span>{{event.time}}</span>
          </div>
          <div class="meta-item">
            <mat-icon>location_on</mat-icon>
            <span>{{event.location}}</span>
          </div>
          <div class="meta-item">
            <mat-icon>person</mat-icon>
            <span>{{ 'EVENTS.ORGANIZED_BY' | translate }}: {{event.organizer}}</span>
          </div>
        </div>
        
        <div class="event-tags" *ngIf="event.tags && event.tags.length > 0">
          <mat-chip-set>
            <mat-chip *ngFor="let tag of event.tags">{{tag}}</mat-chip>
          </mat-chip-set>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Event Content -->
  <section class="event-content">
    <div class="container">
      <div class="content-grid">
        <!-- Main Content -->
        <div class="main-content">
          <div class="description-section">
            <h2>{{ 'EVENTS.ABOUT_EVENT' | translate }}</h2>
            <p>{{event.description}}</p>
          </div>
          
          <div class="schedule-section" *ngIf="event.schedule && event.schedule.length > 0">
            <h2>{{ 'EVENTS.EVENT_SCHEDULE' | translate }}</h2>
            <div class="schedule-list">
              <div class="schedule-item" *ngFor="let item of event.schedule">
                <div class="schedule-time">{{item.time}}</div>
                <div class="schedule-activity">{{item.activity}}</div>
              </div>
            </div>
          </div>
          
          <div class="speakers-section" *ngIf="event.speakers && event.speakers.length > 0">
            <h2>{{ 'EVENTS.SPEAKERS' | translate }}</h2>
            <div class="speakers-grid">
              <div class="speaker-card" *ngFor="let speaker of event.speakers">
                <div class="speaker-image" *ngIf="speaker.image">
                  <img [src]="speaker.image" [alt]="speaker.name">
                </div>
                <div class="speaker-info">
                  <h3 class="speaker-name">{{speaker.name}}</h3>
                  <p class="speaker-title">{{speaker.title}}</p>
                  <p class="speaker-bio" *ngIf="speaker.bio">{{speaker.bio}}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Sidebar -->
        <div class="sidebar">
          <div class="registration-card" *ngIf="event.registrationRequired">
            <h3>{{ 'EVENTS.REGISTRATION' | translate }}</h3>
            
            <div class="registration-closed" *ngIf="isRegistrationClosed() || isEventPast()">
              <mat-icon>event_busy</mat-icon>
              <p>{{ isEventPast() ? ('EVENTS.EVENT_PASSED' | translate) : ('EVENTS.REGISTRATION_CLOSED' | translate) }}</p>
            </div>
            
            <div class="registration-open" *ngIf="!isRegistrationClosed() && !isEventPast()">
              <p>{{ 'EVENTS.REGISTRATION_REQUIRED' | translate }}</p>
              
              <div class="deadline" *ngIf="event.registrationDeadline">
                <strong>{{ 'EVENTS.REGISTRATION_DEADLINE' | translate }}:</strong>
                <span>{{event.registrationDeadline | date:'mediumDate'}}</span>
              </div>
              
              <a mat-raised-button color="primary" [href]="event.registrationLink" target="_blank" class="register-button">
                {{ 'EVENTS.REGISTER_NOW' | translate }}
              </a>
            </div>
          </div>
          
          <div class="contact-card">
            <h3>{{ 'EVENTS.CONTACT_INFO' | translate }}</h3>
            <div class="contact-info">
              <div class="contact-item" *ngIf="event.contactEmail">
                <mat-icon>email</mat-icon>
                <a [href]="'mailto:' + event.contactEmail">{{event.contactEmail}}</a>
              </div>
              <div class="contact-item" *ngIf="event.contactPhone">
                <mat-icon>phone</mat-icon>
                <a [href]="'tel:' + event.contactPhone">{{event.contactPhone}}</a>
              </div>
            </div>
          </div>
          
          <div class="share-card">
            <h3>{{ 'EVENTS.SHARE_EVENT' | translate }}</h3>
            <div class="share-buttons">
              <a mat-mini-fab color="primary" href="javascript:void(0)" aria-label="Share on Facebook">
                <mat-icon>facebook</mat-icon>
              </a>
              <a mat-mini-fab color="primary" href="javascript:void(0)" aria-label="Share on Twitter">
                <mat-icon>twitter</mat-icon>
              </a>
              <a mat-mini-fab color="primary" href="javascript:void(0)" aria-label="Share by Email">
                <mat-icon>email</mat-icon>
              </a>
            </div>
          </div>
          
          <div class="calendar-card">
            <h3>{{ 'EVENTS.ADD_TO_CALENDAR' | translate }}</h3>
            <div class="calendar-buttons">
              <a mat-stroked-button color="primary" href="javascript:void(0)">
                <mat-icon>event</mat-icon>
                {{ 'EVENTS.GOOGLE_CALENDAR' | translate }}
              </a>
              <a mat-stroked-button color="primary" href="javascript:void(0)">
                <mat-icon>calendar_today</mat-icon>
                {{ 'EVENTS.ICAL' | translate }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Call to Action -->
  <section class="cta-section" *ngIf="!isEventPast()">
    <div class="container">
      <h2>{{ 'EVENTS.JOIN_US' | translate }}</h2>
      <p>{{ 'EVENTS.JOIN_US_MESSAGE' | translate }}</p>
      
      <div class="cta-buttons">
        <a mat-raised-button color="primary" [href]="event.registrationLink" target="_blank" *ngIf="event.registrationRequired && !isRegistrationClosed()">
          {{ 'EVENTS.REGISTER_NOW' | translate }}
        </a>
        <button mat-stroked-button color="primary" (click)="goBack()">
          {{ 'EVENTS.EXPLORE_MORE_EVENTS' | translate }}
        </button>
      </div>
    </div>
  </section>
</div>
