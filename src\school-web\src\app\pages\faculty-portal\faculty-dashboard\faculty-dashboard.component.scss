.dashboard-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.dashboard-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.info-card {
  grid-column: span 2;
}

.schedule-card,
.leaves-card {
  grid-column: span 1;
}

.actions-card {
  grid-column: span 2;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
}

.social-links {
  display: flex;
  gap: 12px;
}

.social-link {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: #3f51b5;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: #3f51b5;
    color: white;
  }

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

.loading-indicator {
  margin: 16px 0;
}

.error-message {
  color: #c62828;
  text-align: center;
  padding: 16px 0;

  a {
    color: #3f51b5;
    cursor: pointer;
    text-decoration: underline;
  }
}

.no-schedule,
.no-leaves {
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
  padding: 24px 0;
}

.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.schedule-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.period-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3f51b5;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: 500;
  margin-right: 16px;
}

.period-details {
  flex: 1;
}

.period-subject {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.period-class,
.period-time,
.period-room {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 2px;
}

.leaves-list {
  margin-top: 16px;
}

.leave-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.leave-dates {
  display: flex;
  gap: 24px;
}

.leave-date,
.leave-reason,
.leave-attachment,
.leave-applied {
  display: flex;
  flex-direction: column;
}

.leave-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.leave-value {
  font-size: 16px;
  font-weight: 500;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.actions-grid button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  height: auto;

  mat-icon {
    font-size: 32px;
    width: 32px;
    height: 32px;
    margin-bottom: 8px;
  }
}

mat-card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px 16px;
}

@media (max-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }

  .info-card,
  .schedule-card,
  .leaves-card,
  .actions-card {
    grid-column: span 1;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .leave-dates {
    flex-direction: column;
    gap: 8px;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }
}
