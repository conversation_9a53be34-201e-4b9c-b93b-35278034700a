<h2 mat-dialog-title>Record Attendance</h2>
<h3 class="dialog-subtitle">Class {{ data.grade }}-{{ data.section }} | {{ data.date | date:'mediumDate' }}</h3>

<div *ngIf="loading" class="loading-container">
  <mat-spinner diameter="40"></mat-spinner>
</div>

<form [formGroup]="attendanceForm" (ngSubmit)="onSubmit()">
  <mat-dialog-content>
    <div class="quick-actions">
      <button type="button" mat-stroked-button color="primary" (click)="markAllPresent()">
        <mat-icon>done_all</mat-icon> Mark All Present
      </button>
      <button type="button" mat-stroked-button color="warn" (click)="markAllAbsent()">
        <mat-icon>clear_all</mat-icon> Mark All Absent
      </button>
    </div>

    <div class="students-table-container">
      <table class="students-table">
        <thead>
          <tr>
            <th>Roll No.</th>
            <th>Student Name</th>
            <th>Status</th>
            <th>Remarks</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let studentControl of students.controls; let i = index" [formGroup]="$any(studentControl)">
            <td>{{ studentControl.get('rollNumber')?.value }}</td>
            <td>{{ studentControl.get('firstName')?.value }} {{ studentControl.get('lastName')?.value }}</td>
            <td>
              <mat-form-field appearance="outline">
                <mat-select formControlName="status">
                  <mat-option *ngFor="let option of statusOptions" [value]="option.value">
                    <span [ngClass]="getStatusClass(option.value)">{{ option.label }}</span>
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </td>
            <td>
              <mat-form-field appearance="outline">
                <input matInput formControlName="remarks" placeholder="Optional">
              </mat-form-field>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" [disabled]="loading" (click)="onCancel()">Cancel</button>
    <button mat-raised-button color="primary" type="submit" [disabled]="attendanceForm.invalid || loading">
      <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
      <span *ngIf="!loading">Save Attendance</span>
    </button>
  </mat-dialog-actions>
</form>
