using School.Application.DTOs;
using School.Domain.Enums;

using System.Collections.Generic;
using System.Threading.Tasks;

namespace School.Application.Features.Student
{
    public interface IStudentService
    {
        Task<(IEnumerable<StudentDto> Students, int TotalCount)> GetAllStudentsAsync(StudentFilterDto filter);
        Task<StudentDetailDto?> GetStudentByIdAsync(Guid id);
        Task<StudentDto?> GetStudentByStudentIdAsync(string studentId);
        Task<StudentDto?> GetStudentByUserIdAsync(string userId);
        Task<Guid> CreateStudentAsync(CreateStudentDto studentDto);
        Task<bool> UpdateStudentAsync(Guid id, UpdateStudentDto studentDto);
        Task<bool> DeleteStudentAsync(Guid id);

        // Attendance methods
        Task<IEnumerable<StudentAttendanceDto>> GetStudentAttendanceAsync(Guid studentId, string? fromDate = null, string? toDate = null);
        Task<Guid> CreateAttendanceAsync(CreateStudentAttendanceDto attendanceDto);
        Task<bool> UpdateAttendanceAsync(Guid id, UpdateStudentAttendanceDto attendanceDto);
        Task<bool> DeleteAttendanceAsync(Guid id);

        // Fee methods
        Task<IEnumerable<StudentFeeDto>> GetStudentFeesAsync(Guid studentId, int? academicYear = null);
        Task<Guid> CreateFeeAsync(CreateStudentFeeDto feeDto);
        Task<bool> UpdateFeeAsync(Guid id, UpdateStudentFeeDto feeDto);
        Task<bool> DeleteFeeAsync(Guid id);

        // Result methods
        Task<IEnumerable<StudentResultDto>> GetStudentResultsAsync(Guid studentId, string? academicYear = null, string? examType = null);
        Task<decimal?> CalculateGPAAsync(Guid studentId, int academicYear, ExamType examType);
        Task<Guid> CreateResultAsync(CreateStudentResultDto resultDto);
        Task<bool> UpdateResultAsync(Guid id, UpdateStudentResultDto resultDto);
        Task<bool> DeleteResultAsync(Guid id);

        // Leave methods
        Task<IEnumerable<StudentLeaveDto>> GetStudentLeavesAsync(Guid studentId, string? fromDate = null, string? toDate = null, LeaveStatus? status = null);
        Task<StudentLeaveDto?> GetLeaveByIdAsync(Guid id);
        Task<Guid> CreateLeaveAsync(CreateStudentLeaveDto leaveDto);
        Task<bool> UpdateLeaveStatusAsync(Guid id, UpdateStudentLeaveDto leaveDto);
        Task<bool> DeleteLeaveAsync(Guid id);

        // Academic History methods
        Task<IEnumerable<StudentAcademicHistoryDto>> GetStudentAcademicHistoryAsync(Guid studentId);
        Task<StudentAcademicHistoryDto?> GetAcademicHistoryByYearAsync(Guid studentId, int academicYear);
        Task<Guid> CreateAcademicHistoryAsync(CreateStudentAcademicHistoryDto academicHistoryDto);
        Task<bool> UpdateAcademicHistoryAsync(Guid id, UpdateStudentAcademicHistoryDto academicHistoryDto);
        Task<bool> PromoteStudentAsync(Guid studentId, int fromAcademicYear, int toAcademicYear, int toGrade, string toSection, int toRollNumber, string toStudentId);

        // Parent association methods
        Task<bool> AddParentAsync(Guid studentId, CreateStudentParentDto parentDto);
        Task<bool> UpdateParentRelationAsync(Guid studentId, Guid parentId, ParentRelationType relationType, bool isPrimaryContact);
        Task<bool> RemoveParentAsync(Guid studentId, Guid parentId);
    }
}
