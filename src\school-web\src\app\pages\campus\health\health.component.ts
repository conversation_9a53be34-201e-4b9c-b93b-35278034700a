import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../../shared/components/default-hero/default-hero.component';

interface Service {
  title: string;
  description: string;
  icon: string;
}

interface Staff {
  name: string;
  title: string;
  credentials: string;
  bio: string;
  image: string;
}

@Component({
  selector: 'app-health',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatExpansionModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './health.component.html',
  styleUrls: ['./health.component.scss']
})
export class HealthComponent {
  // Health center information
  healthCenter = {
    hours: 'Monday-Friday: 8:00 AM - 4:30 PM',
    location: 'Health Services Building, First Floor',
    phone: '(*************',
    email: 'healthcenter&#64;school.edu',
    emergencyInfo: 'For after-hours emergencies, please call Campus Security at (************* or dial 911.'
  };

  // Health services
  services: Service[] = [
    {
      title: 'Primary Care',
      description: 'Our healthcare providers offer diagnosis and treatment for common illnesses and injuries, as well as management of chronic conditions.',
      icon: 'medical_services'
    },
    {
      title: 'Immunizations',
      description: 'We provide required and recommended vaccinations, including seasonal flu shots, to protect our school community from preventable diseases.',
      icon: 'vaccines'
    },
    {
      title: 'Mental Health Services',
      description: 'Our counselors offer individual and group therapy, crisis intervention, and referrals for specialized mental health care when needed.',
      icon: 'psychology'
    },
    {
      title: 'Health Education',
      description: 'We provide resources and workshops on various health topics to promote wellness and prevention among our students.',
      icon: 'school'
    },
    {
      title: 'Medication Management',
      description: 'Our staff can administer prescribed medications during school hours and provide guidance on medication management.',
      icon: 'medication'
    },
    {
      title: 'First Aid & Emergency Care',
      description: 'We provide immediate care for injuries and medical emergencies that occur during school hours.',
      icon: 'emergency'
    }
  ];

  // Health center staff
  staff: Staff[] = [
    {
      name: 'Dr. Sarah Johnson',
      title: 'Medical Director',
      credentials: 'MD, Board Certified in Pediatrics',
      bio: 'Dr. Johnson oversees all medical services at our Health Center. With over 15 years of experience in pediatric and adolescent medicine, she is dedicated to providing comprehensive healthcare for our students.',
      image: 'assets/images/campus/health/doctor1.jpg'
    },
    {
      name: 'Jennifer Williams',
      title: 'School Nurse',
      credentials: 'RN, BSN, Certified School Nurse',
      bio: 'Jennifer has been a school nurse for 10 years and specializes in adolescent health. She coordinates daily healthcare services and manages chronic condition care plans for students.',
      image: 'assets/images/campus/health/nurse1.jpg'
    },
    {
      name: 'Michael Chen',
      title: 'School Nurse',
      credentials: 'RN, MSN',
      bio: 'Michael brings experience from pediatric hospital settings to our school health center. He focuses on health education and preventive care for our students.',
      image: 'assets/images/campus/health/nurse2.jpg'
    },
    {
      name: 'Dr. Lisa Rodriguez',
      title: 'School Psychologist',
      credentials: 'PhD in Clinical Psychology',
      bio: 'Dr. Rodriguez provides mental health assessments, individual and group counseling, and crisis intervention services. She specializes in adolescent development and emotional well-being.',
      image: 'assets/images/campus/health/psychologist.jpg'
    },
    {
      name: 'David Thompson',
      title: 'Mental Health Counselor',
      credentials: 'LMHC, MA in Counseling Psychology',
      bio: 'David works with students to address social-emotional concerns, stress management, and adjustment issues. He facilitates support groups and provides individual counseling.',
      image: 'assets/images/campus/health/counselor.jpg'
    },
    {
      name: 'Amanda Garcia',
      title: 'Health Services Coordinator',
      credentials: 'MPH in Health Education',
      bio: 'Amanda coordinates health education programs, manages health records, and serves as a liaison between the health center, families, and community health resources.',
      image: 'assets/images/campus/health/coordinator.jpg'
    }
  ];

  // Wellness programs
  wellnessPrograms = [
    {
      title: 'Physical Fitness Program',
      description: 'Our comprehensive physical fitness program includes regular physical education classes, intramural sports, and fitness challenges designed to promote active lifestyles and physical well-being.',
      image: 'assets/images/campus/health/fitness.jpg'
    },
    {
      title: 'Nutrition Education',
      description: 'Through classroom lessons, cooking demonstrations, and cafeteria initiatives, we teach students about healthy eating habits and the importance of balanced nutrition for overall health.',
      image: 'assets/images/campus/health/nutrition.jpg'
    },
    {
      title: 'Stress Management',
      description: 'We offer workshops, mindfulness training, and relaxation techniques to help students develop effective strategies for managing academic and personal stress.',
      image: 'assets/images/campus/health/stress.jpg'
    },
    {
      title: 'Sleep Health',
      description: 'Our sleep education program teaches students about the importance of quality sleep for learning, emotional regulation, and physical health, along with strategies for improving sleep habits.',
      image: 'assets/images/campus/health/sleep.jpg'
    }
  ];

  // Health resources
  healthResources = [
    {
      title: 'Health Forms',
      description: 'Required health forms for enrollment, including immunization records, physical examination forms, and medication authorization forms.',
      link: 'assets/documents/health-forms.pdf'
    },
    {
      title: 'Immunization Requirements',
      description: 'Information about required and recommended vaccinations for students at different grade levels.',
      link: 'assets/documents/immunization-requirements.pdf'
    },
    {
      title: 'Medication Policy',
      description: 'Guidelines for medication administration at school, including prescription and over-the-counter medications.',
      link: 'assets/documents/medication-policy.pdf'
    },
    {
      title: 'Allergy & Asthma Action Plans',
      description: 'Forms and guidelines for managing allergies and asthma in the school setting.',
      link: 'assets/documents/allergy-asthma-plans.pdf'
    },
    {
      title: 'Mental Health Resources',
      description: 'Information about school-based and community mental health services and support resources.',
      link: 'assets/documents/mental-health-resources.pdf'
    },
    {
      title: 'Wellness Tips',
      description: 'Practical advice for maintaining physical and mental well-being throughout the school year.',
      link: 'assets/documents/wellness-tips.pdf'
    }
  ];

  // FAQs
  faqs = [
    {
      question: 'What should I do if my child is sick?',
      answer: 'If your child has a fever (100.4°F or higher), vomiting, diarrhea, or a contagious condition such as strep throat or pink eye, please keep them home until they are symptom-free for 24 hours without medication. Notify the school attendance office and the health center about the absence and the nature of the illness.'
    },
    {
      question: 'How do I submit required health forms?',
      answer: 'All health forms can be submitted electronically through our secure parent portal or delivered in person to the Health Center. Forms must be completed and signed by a healthcare provider where indicated. Please submit all required forms before the start of the school year or within 30 days of enrollment.'
    },
    {
      question: 'What is the procedure for medication administration at school?',
      answer: 'All medications (prescription and over-the-counter) require a completed Medication Authorization Form signed by both a parent/guardian and a healthcare provider. Medications must be delivered to the Health Center by a parent/guardian in the original, properly labeled container. Students are not permitted to carry medications except for emergency medications such as inhalers or epinephrine auto-injectors with proper authorization.'
    },
    {
      question: 'How are food allergies managed at school?',
      answer: 'Students with food allergies should have an Allergy Action Plan on file at the Health Center. Our cafeteria provides allergen information for all menu items and offers allergen-free alternatives. We maintain allergen-aware tables in the cafeteria and train staff in recognizing and responding to allergic reactions. Please contact the Health Center to develop an individualized plan for your child.'
    },
    {
      question: 'What mental health services are available to students?',
      answer: 'Our school provides a range of mental health services, including individual counseling, group support, crisis intervention, and referrals to community resources. Students can access these services by visiting the Health Center, speaking with their school counselor, or being referred by a teacher or parent. All services are confidential within the limits of safety and legal requirements.'
    },
    {
      question: 'How do I request accommodations for a medical condition?',
      answer: 'If your child requires accommodations for a medical condition, please contact the Health Center to initiate the process. You will need to provide documentation from a healthcare provider detailing the condition and recommended accommodations. Our team will work with you to develop an appropriate plan, which may include a 504 Plan or Health Care Plan depending on the nature of the condition.'
    }
  ];
}
