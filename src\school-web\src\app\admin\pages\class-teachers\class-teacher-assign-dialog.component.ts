import { Component, OnInit, inject, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { TranslateModule } from '@ngx-translate/core';
import { ClassTeacherService, ClassTeacher, CreateClassTeacherDto } from '../../../core/services/class-teacher.service';
import { GradeService } from '../../../core/services/grade.service';
import { SectionService } from '../../../core/services/section.service';
import { AcademicYearService } from '../../../core/services/academic-year.service';

export interface DialogData {
  mode: 'assign' | 'reassign';
  classTeacher?: ClassTeacher;
}

@Component({
  selector: 'app-class-teacher-assign-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatSlideToggleModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDatepickerModule,
    MatNativeDateModule,
    TranslateModule
  ],
  templateUrl: './class-teacher-assign-dialog.component.html',
  styleUrls: ['./class-teacher-assign-dialog.component.scss']
})
export class ClassTeacherAssignDialogComponent implements OnInit {
  private fb = inject(FormBuilder);
  private classTeacherService = inject(ClassTeacherService);
  private gradeService = inject(GradeService);
  private sectionService = inject(SectionService);
  private academicYearService = inject(AcademicYearService);
  private dialogRef = inject(MatDialogRef<ClassTeacherAssignDialogComponent>);

  assignmentForm: FormGroup;
  loading = false;
  grades: any[] = [];
  sections: any[] = [];
  academicYears: any[] = [];
  availableFaculty: any[] = [];
  unassignedSections: any[] = [];

  constructor(@Inject(MAT_DIALOG_DATA) public data: DialogData) {
    this.assignmentForm = this.fb.group({
      facultyId: ['', Validators.required],
      gradeId: ['', Validators.required],
      sectionId: ['', Validators.required],
      academicYearId: ['', Validators.required],
      startDate: [new Date(), Validators.required],
      isPrimary: [true],
      responsibilities: ['', Validators.maxLength(1000)],
      specialDuties: ['', Validators.maxLength(1000)],
      contactSchedule: ['', Validators.maxLength(500)],
      officeHours: ['', Validators.maxLength(500)]
    });
  }

  ngOnInit() {
    this.loadAcademicYears();
    this.loadGrades();
    
    if (this.data.mode === 'reassign' && this.data.classTeacher) {
      this.populateForm(this.data.classTeacher);
    }

    // Watch for grade changes to load sections
    this.assignmentForm.get('gradeId')?.valueChanges.subscribe(gradeId => {
      if (gradeId) {
        this.loadSectionsByGrade(gradeId);
      } else {
        this.sections = [];
      }
      this.assignmentForm.get('sectionId')?.setValue('');
    });

    // Watch for academic year changes to load available faculty
    this.assignmentForm.get('academicYearId')?.valueChanges.subscribe(academicYearId => {
      if (academicYearId) {
        this.loadAvailableFaculty(academicYearId);
        this.loadUnassignedSections(academicYearId);
      }
    });
  }

  loadAcademicYears() {
    this.academicYearService.getActiveAcademicYears().subscribe({
      next: (years) => {
        this.academicYears = years;
        // Set current academic year as default
        if (years.length > 0 && this.data.mode === 'assign') {
          this.assignmentForm.get('academicYearId')?.setValue(years[0].id);
        }
      },
      error: (error) => {
        console.error('Error loading academic years:', error);
      }
    });
  }

  loadGrades() {
    this.gradeService.getActiveGrades('').subscribe({
      next: (grades) => {
        this.grades = grades;
      },
      error: (error) => {
        console.error('Error loading grades:', error);
      }
    });
  }

  loadSectionsByGrade(gradeId: string) {
    this.sectionService.getActiveSections(gradeId).subscribe({
      next: (sections) => {
        this.sections = sections;
      },
      error: (error) => {
        console.error('Error loading sections:', error);
      }
    });
  }

  loadAvailableFaculty(academicYearId: string) {
    this.classTeacherService.getAvailableFacultyForAssignment(academicYearId).subscribe({
      next: (faculty) => {
        this.availableFaculty = faculty;
      },
      error: (error) => {
        console.error('Error loading available faculty:', error);
      }
    });
  }

  loadUnassignedSections(academicYearId: string) {
    this.classTeacherService.getUnassignedSections(academicYearId).subscribe({
      next: (sections) => {
        this.unassignedSections = sections;
      },
      error: (error) => {
        console.error('Error loading unassigned sections:', error);
      }
    });
  }

  populateForm(classTeacher: ClassTeacher) {
    this.assignmentForm.patchValue({
      facultyId: classTeacher.facultyId,
      gradeId: classTeacher.gradeId,
      sectionId: classTeacher.sectionId,
      academicYearId: classTeacher.academicYearId,
      startDate: new Date(classTeacher.startDate),
      isPrimary: classTeacher.isPrimary,
      responsibilities: classTeacher.responsibilities,
      specialDuties: classTeacher.specialDuties,
      contactSchedule: classTeacher.contactSchedule,
      officeHours: classTeacher.officeHours
    });
  }

  onSubmit() {
    if (this.assignmentForm.valid) {
      this.loading = true;
      const formValue = this.assignmentForm.value;

      if (this.data.mode === 'assign') {
        const createDto: CreateClassTeacherDto = {
          facultyId: formValue.facultyId,
          sectionId: formValue.sectionId,
          academicYearId: formValue.academicYearId,
          startDate: formValue.startDate,
          isPrimary: formValue.isPrimary,
          responsibilities: formValue.responsibilities,
          specialDuties: formValue.specialDuties,
          contactSchedule: formValue.contactSchedule,
          officeHours: formValue.officeHours
        };

        this.classTeacherService.createClassTeacher(createDto).subscribe({
          next: () => {
            this.loading = false;
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('Error assigning class teacher:', error);
            this.loading = false;
          }
        });
      } else {
        // Reassign logic
        this.classTeacherService.reassignClassTeacher(
          this.data.classTeacher!.sectionId,
          formValue.facultyId,
          'Reassigned through admin panel'
        ).subscribe({
          next: () => {
            this.loading = false;
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('Error reassigning class teacher:', error);
            this.loading = false;
          }
        });
      }
    }
  }

  onCancel() {
    this.dialogRef.close(false);
  }

  getTitle(): string {
    return this.data.mode === 'assign' ? 'CLASS_TEACHERS.ASSIGN_TEACHER' : 'CLASS_TEACHERS.REASSIGN_TEACHER';
  }

  getSubmitButtonText(): string {
    return this.data.mode === 'assign' ? 'CLASS_TEACHERS.ASSIGN' : 'CLASS_TEACHERS.REASSIGN';
  }

  isSectionAvailable(sectionId: string): boolean {
    if (this.data.mode === 'reassign') {
      return true; // Allow reassigning to any section
    }
    return this.unassignedSections.some(section => section.id === sectionId);
  }
}
