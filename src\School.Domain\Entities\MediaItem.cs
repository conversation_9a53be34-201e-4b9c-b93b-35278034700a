using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class MediaItem : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this media item belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string OriginalFileName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string MimeType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public MediaType Type { get; set; }
    public string AltText { get; set; } = string.Empty;
    public string Caption { get; set; } = string.Empty;

    // Relationships
    public Guid? ContentId { get; set; }
    public Content? Content { get; set; }

    public Guid UploadedById { get; set; }
    public User? UploadedBy { get; set; }
    public DateTime UpdatedAt { get; set; }
}
