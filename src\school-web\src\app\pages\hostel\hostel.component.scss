.hostel-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
  }
  
  .page-description {
    font-size: 1.1rem;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
  }
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  text-align: center;
}

.error-message {
  color: #f44336;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.hostel-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.intro-section {
  display: flex;
  padding: 2rem;
  background-color: #f5f5f5;
  
  .intro-text {
    flex: 1;
    padding-right: 2rem;
    
    h2 {
      font-size: 1.8rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 1rem;
    }
    
    p {
      font-size: 1.1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1.5rem;
    }
  }
  
  .intro-image {
    flex: 1;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px;
    }
  }
}

.feature-list {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  
  li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    
    mat-icon {
      color: #3f51b5;
    }
    
    span {
      font-size: 1rem;
      color: #555;
    }
  }
}

.facilities-container {
  padding: 2rem;
  
  h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.5rem;
  }
}

.facilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.facility-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: transform 0.3s, box-shadow 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
  
  mat-card-header {
    padding-bottom: 1rem;
  }
  
  mat-card-title {
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.facility-images {
  height: 200px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.facility-description {
  font-size: 1rem;
  line-height: 1.5;
  color: #555;
  margin: 1rem 0;
}

.facility-details {
  margin: 1rem 0;
  
  .detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    
    mat-icon {
      color: #3f51b5;
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }
    
    span {
      font-size: 0.95rem;
      color: #555;
    }
  }
}

.facility-amenities {
  margin-top: 1.5rem;
  
  h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
  }
}

.amenities-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  
  .amenity-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    
    mat-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
      color: #f44336;
    }
    
    &.available mat-icon {
      color: #4caf50;
    }
    
    span {
      font-size: 0.9rem;
      color: #555;
    }
  }
}

.application-section {
  padding: 2rem;
  background-color: #f5f5f5;
  
  h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    text-align: center;
  }
  
  p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #555;
    margin-bottom: 1.5rem;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
  
  mat-accordion {
    max-width: 800px;
    margin: 0 auto 2rem;
  }
}

.application-cta {
  text-align: center;
  margin-top: 2rem;
  
  button {
    padding: 0.5rem 2rem;
    font-size: 1.1rem;
  }
}

.contact-section {
  padding: 2rem;
  
  h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    text-align: center;
  }
  
  p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #555;
    margin-bottom: 1.5rem;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

.contact-info {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
  
  .contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    
    mat-icon {
      color: #3f51b5;
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }
    
    h4 {
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0 0 0.5rem;
      color: #333;
    }
    
    p {
      font-size: 1rem;
      color: #555;
      margin: 0;
      text-align: left;
    }
  }
}

@media (max-width: 768px) {
  .intro-section {
    flex-direction: column;
    
    .intro-text {
      padding-right: 0;
      padding-bottom: 1.5rem;
    }
  }
  
  .facilities-grid {
    grid-template-columns: 1fr;
  }
  
  .contact-info {
    flex-direction: column;
    align-items: center;
  }
}
