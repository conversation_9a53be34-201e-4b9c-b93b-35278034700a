.academic-year-form-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    gap: 16px;

    .header-content {
      flex: 1;

      .page-title {
        margin: 0 0 8px 0;
        font-size: 2rem;
        font-weight: 500;
        color: var(--mat-sys-on-surface);
      }

      .page-subtitle {
        margin: 0;
        color: var(--mat-sys-on-surface-variant);
        font-size: 1rem;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
      flex-shrink: 0;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 24px;
    text-align: center;
    color: var(--mat-sys-on-surface-variant);

    mat-spinner {
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      font-size: 1rem;
    }
  }

  .form-card {
    .step-form {
      padding: 24px;

      .form-section {
        margin-bottom: 32px;

        h3 {
          margin: 0 0 16px 0;
          font-size: 1.25rem;
          font-weight: 500;
          color: var(--mat-sys-on-surface);
        }

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          h3 {
            margin: 0;
          }
        }

        .form-row {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 16px;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .full-width {
            grid-column: 1 / -1;
          }
        }
      }

      .step-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 32px;
        padding-top: 16px;
        border-top: 1px solid var(--mat-sys-outline-variant);

        button {
          min-width: 120px;

          mat-icon {
            margin: 0 4px;
          }
        }
      }
    }

    .terms-container {
      .term-card {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        mat-card {
          transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
          }

          mat-card-header {
            .drag-handle {
              cursor: grab;
              color: var(--mat-sys-on-surface-variant);
              margin-right: 8px;

              &:active {
                cursor: grabbing;
              }
            }

            .card-actions {
              margin-left: auto;
            }
          }

          .term-form {
            .form-row {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 16px;
              margin-bottom: 16px;

              &:last-child {
                margin-bottom: 0;
              }

              .full-width {
                grid-column: 1 / -1;
              }
            }
          }
        }
      }

      // CDK Drag & Drop styles
      .cdk-drag-preview {
        box-sizing: border-box;
        border-radius: 4px;
        box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
                    0 8px 10px 1px rgba(0, 0, 0, 0.14),
                    0 3px 14px 2px rgba(0, 0, 0, 0.12);
      }

      .cdk-drag-placeholder {
        opacity: 0;
      }

      .cdk-drag-animating {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
      }

      .cdk-drop-list-dragging .cdk-drag:not(.cdk-drag-placeholder) {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
      }
    }

    .review-section {
      padding: 24px;

      h3 {
        margin: 0 0 24px 0;
        font-size: 1.5rem;
        font-weight: 500;
        color: var(--mat-sys-on-surface);
      }

      .review-card {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 24px;
        }

        .review-item {
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          gap: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          strong {
            min-width: 120px;
            font-weight: 500;
            color: var(--mat-sys-on-surface);
          }
        }

        .term-review {
          margin-bottom: 16px;
          padding: 12px;
          border: 1px solid var(--mat-sys-outline-variant);
          border-radius: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .term-review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            strong {
              font-weight: 500;
              color: var(--mat-sys-on-surface);
            }
          }

          .term-review-details {
            color: var(--mat-sys-on-surface-variant);
            font-size: 0.875rem;
          }
        }
      }

      mat-checkbox {
        margin-bottom: 24px;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .academic-year-form-container {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: stretch;

      .header-actions {
        justify-content: stretch;

        button {
          flex: 1;
        }
      }
    }

    .form-card {
      .step-form {
        padding: 16px;

        .form-section {
          .form-row {
            grid-template-columns: 1fr;
          }

          .section-header {
            flex-direction: column;
            align-items: stretch;
            gap: 12px;

            button {
              align-self: flex-start;
            }
          }
        }

        .step-actions {
          flex-direction: column;
          gap: 12px;

          button {
            width: 100%;
          }
        }
      }

      .terms-container {
        .term-card {
          mat-card {
            .term-form {
              .form-row {
                grid-template-columns: 1fr;
              }
            }
          }
        }
      }

      .review-section {
        padding: 16px;

        .review-card {
          .review-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;

            strong {
              min-width: auto;
            }
          }

          .term-review {
            .term-review-header {
              flex-direction: column;
              align-items: flex-start;
              gap: 8px;
            }
          }
        }
      }
    }
  }
}

// Dark Theme Support
@media (prefers-color-scheme: dark) {
  .academic-year-form-container {
    .page-header {
      .page-title {
        color: var(--mat-sys-on-surface);
      }

      .page-subtitle {
        color: var(--mat-sys-on-surface-variant);
      }
    }

    .form-card {
      .step-form {
        .form-section {
          h3 {
            color: var(--mat-sys-on-surface);
          }
        }
      }

      .terms-container {
        .term-card {
          mat-card {
            &:hover {
              box-shadow: 0 4px 8px rgba(255, 255, 255, 0.12);
            }

            mat-card-header {
              .drag-handle {
                color: var(--mat-sys-on-surface-variant);
              }
            }
          }
        }
      }

      .review-section {
        h3 {
          color: var(--mat-sys-on-surface);
        }

        .review-card {
          .review-item {
            strong {
              color: var(--mat-sys-on-surface);
            }
          }

          .term-review {
            border-color: var(--mat-sys-outline-variant);

            .term-review-header {
              strong {
                color: var(--mat-sys-on-surface);
              }
            }

            .term-review-details {
              color: var(--mat-sys-on-surface-variant);
            }
          }
        }
      }
    }
  }
}

// Animation Classes
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
