import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-term-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="detail-container">
      <div class="page-header">
        <h1>Term Details</h1>
        <div class="header-actions">
          <button mat-raised-button routerLink="/admin/terms">
            <mat-icon>arrow_back</mat-icon>
            Back to List
          </button>
        </div>
      </div>
      
      <mat-card>
        <mat-card-content>
          <p>Term Detail component - Coming Soon!</p>
          <p>Term ID: {{termId}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .detail-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }
    
    h1 {
      margin: 0;
      font-size: 2rem;
      font-weight: 500;
    }
  `]
})
export class TermDetailComponent implements OnInit {
  termId?: string;

  constructor(private route: ActivatedRoute) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.termId = params['id'];
    });
  }
}
