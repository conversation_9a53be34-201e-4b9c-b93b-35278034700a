.class-teachers-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;

  mat-card {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
  }

  mat-toolbar {
    border-radius: 8px 8px 0 0;
    
    .spacer {
      flex: 1 1 auto;
    }
  }
}

.filter-section {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
  flex-wrap: wrap;

  .search-field {
    flex: 1;
    min-width: 250px;
  }

  .filter-field {
    min-width: 150px;
  }

  button {
    height: 56px;
  }
}

.table-container {
  overflow-x: auto;
  margin-bottom: 16px;

  .class-teachers-table {
    width: 100%;
    
    th {
      font-weight: 600;
      color: #333;
      background-color: #f5f5f5;
    }

    td {
      padding: 12px 8px;
    }
  }
}

.faculty-info {
  .faculty-name {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }

  .faculty-email {
    display: block;
    color: #666;
    font-size: 0.75rem;
  }
}

.section-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .section-name {
    font-weight: 500;
    color: #333;
  }

  .section-code {
    background-color: #e3f2fd;
    color: #1976d2;
    font-size: 0.75rem;
    height: 20px;
    line-height: 20px;
    align-self: flex-start;
  }
}

.grade-name {
  font-weight: 500;
  color: #4caf50;
  background-color: #e8f5e8;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
}

.workload-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;

  mat-chip {
    font-weight: 500;
    font-size: 0.75rem;
    height: 24px;
    line-height: 24px;
  }

  .student-count {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666;
    font-size: 0.75rem;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;

  mat-chip {
    font-size: 0.75rem;
    height: 24px;
    line-height: 24px;
  }

  mat-slide-toggle {
    transform: scale(0.8);
  }
}

.start-date {
  color: #607d8b;
  font-weight: 500;
}

.academic-year {
  color: #607d8b;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 4px;

  button {
    width: 36px;
    height: 36px;
    
    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;

  mat-spinner {
    margin-bottom: 16px;
  }
}

.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #666;

  .no-data-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #ccc;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    color: #333;
  }

  p {
    margin: 0 0 24px 0;
    max-width: 400px;
    line-height: 1.5;
  }
}

// Responsive design
@media (max-width: 1200px) {
  .class-teachers-container {
    padding: 16px;
  }

  .filter-section {
    .filter-field {
      min-width: 120px;
    }
  }
}

@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;

    .search-field,
    .filter-field {
      min-width: auto;
    }
  }

  .table-container {
    .class-teachers-table {
      font-size: 0.875rem;

      th, td {
        padding: 8px 4px;
      }
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .workload-info,
  .status-info {
    align-items: center;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .class-teachers-container {
    mat-card {
      background-color: #424242;
      color: #fff;
    }
  }

  .table-container {
    .class-teachers-table {
      th {
        background-color: #616161;
        color: #fff;
      }
    }
  }

  .faculty-info {
    .faculty-name {
      color: #fff;
    }

    .faculty-email {
      color: #ccc;
    }
  }

  .section-info {
    .section-name {
      color: #fff;
    }
  }

  .no-data-container {
    color: #ccc;

    h3 {
      color: #fff;
    }
  }
}

// Chip animations
mat-chip {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

// Table row hover effect
.class-teachers-table {
  tr.mat-mdc-row:hover {
    background-color: #f5f5f5;
  }
}

@media (prefers-color-scheme: dark) {
  .class-teachers-table {
    tr.mat-mdc-row:hover {
      background-color: #616161;
    }
  }
}

// Workload color indicators
mat-chip {
  &[color="primary"] {
    background-color: #4caf50;
    color: white;
  }

  &[color="accent"] {
    background-color: #ff9800;
    color: white;
  }

  &[color="warn"] {
    background-color: #f44336;
    color: white;
  }
}

// Status indicators
.status-info mat-chip {
  &[color="primary"] {
    background-color: #4caf50;
    color: white;
  }

  &[color="warn"] {
    background-color: #f44336;
    color: white;
  }

  &[color="accent"] {
    background-color: #ff9800;
    color: white;
  }
}

// Slide toggle customization
mat-slide-toggle {
  .mat-mdc-slide-toggle-bar {
    height: 16px;
    border-radius: 8px;
  }

  .mat-mdc-slide-toggle-thumb {
    width: 12px;
    height: 12px;
  }
}
