.media-list-container {
  padding: 16px;
  display: grid;
  grid-template-columns: minmax(0, 1fr) 350px;
  grid-gap: 16px;
  width: 100%;
}

.page-header {
  grid-column: 1 / -1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-card {
  grid-column: 1 / -1;
  margin-bottom: 16px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.search-field {
  flex: 1;
  min-width: 200px;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.loading-container,
.error-container {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  text-align: center;
}

.error-message {
  color: #f44336;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.media-grid-container {
  grid-column: 1;
  width: 100%;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
  width: 100%;
}

.no-media {
  width: 100%;
  text-align: center;
  padding: 48px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;

  .empty-icon {
    font-size: 64px;
    height: 64px;
    width: 64px;
    color: #bdbdbd;
  }

  p {
    font-size: 18px;
    color: #757575;
    margin-bottom: 16px;
  }
}

.media-item {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  background-color: #fff;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &.selected {
    border: 2px solid #3f51b5;
  }
}

.media-preview {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.media-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #757575;
  }
}

.media-info {
  padding: 8px;
}

.media-title {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.media-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: #757575;
}

.media-details {
  grid-column: 2;

  mat-card {
    position: sticky;
    top: 16px;
  }
}

.media-preview-large {
  margin: 16px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
  min-height: 200px;

  img {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
  }
}

.media-icon-large {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;

  mat-icon {
    font-size: 96px;
    width: 96px;
    height: 96px;
    color: #757575;
  }
}

.media-details-info {
  margin-top: 16px;
}

.detail-row {
  margin-bottom: 8px;
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-weight: 500;
  color: #757575;
  font-size: 12px;
}

.detail-value {
  font-size: 14px;
}

.url-container {
  display: flex;
  align-items: center;
}

.url-value {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@media (max-width: 1200px) {
  .media-list-container {
    grid-template-columns: minmax(0, 1fr) 300px;
  }
}

@media (max-width: 1024px) {
  .media-list-container {
    grid-template-columns: minmax(0, 1fr);
  }

  .media-details {
    grid-column: 1;
  }
}

@media (max-width: 768px) {
  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-field {
    width: 100%;
  }

  .filter-actions {
    margin-top: 8px;
  }

  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  }
}

@media (max-width: 480px) {
  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }

  .media-preview {
    height: 100px;
  }
}
