.setup-wizard-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow-y: auto;

  .setup-card {
    max-width: 900px;
    width: 100%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    overflow: hidden;

    mat-card-header {
      background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
      color: white;
      padding: 24px;

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 1.5rem;
        font-weight: 500;
        margin: 0;

        mat-icon {
          font-size: 28px;
          width: 28px;
          height: 28px;
        }
      }

      mat-card-subtitle {
        color: rgba(255, 255, 255, 0.8);
        margin-top: 8px;
        font-size: 1rem;
      }
    }

    mat-card-content {
      padding: 32px;
    }
  }
}

// No Tenant Access Message
.no-tenant-message {
  text-align: center;
  padding: 40px 20px;

  .message-content {
    max-width: 500px;
    margin: 0 auto;

    .large-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: #2196f3;
      margin-bottom: 16px;
    }

    h3 {
      color: #1976d2;
      margin-bottom: 16px;
      font-size: 1.5rem;
    }

    p {
      margin-bottom: 16px;
      color: #666;
      line-height: 1.6;

      &:last-of-type {
        margin-bottom: 24px;
      }
    }

    button {
      margin-top: 16px;
    }
  }
}

.progress-section {
  margin-bottom: 32px;

  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .progress-text {
      font-weight: 500;
      color: #333;
    }

    .progress-percentage {
      font-weight: 600;
      color: #1976d2;
      font-size: 1.1rem;
    }
  }

  mat-progress-bar {
    height: 8px;
    border-radius: 4px;
  }
}

.step-form {
  .form-section {
    margin-bottom: 24px;

    h3 {
      color: #333;
      font-size: 1.2rem;
      font-weight: 500;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #e0e0e0;
    }

    .section-description {
      color: #666;
      margin-bottom: 16px;
      line-height: 1.5;
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      .form-field {
        flex: 1;
      }
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .checkbox-section {
      margin-bottom: 16px;

      mat-checkbox {
        .mat-mdc-checkbox-label {
          font-size: 1rem;
          color: #333;
        }
      }
    }
  }

  .step-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e0e0e0;

    button {
      min-width: 120px;
    }
  }
}

.finalize-section {
  margin-top: 32px;

  .finalize-card {
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    color: white;
    text-align: center;

    .finalize-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .success-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #fff;
      }

      h3 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 500;
      }

      p {
        margin: 0;
        font-size: 1rem;
        opacity: 0.9;
        max-width: 400px;
      }
    }

    mat-card-actions {
      padding: 24px;
      justify-content: center;

      button {
        font-size: 1.1rem;
        padding: 12px 24px;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);

        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }

        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }
}

// Stepper customization
mat-stepper {
  .mat-stepper-vertical-line::before {
    border-left-color: #e0e0e0;
  }

  .mat-step-header {
    .mat-step-icon {
      background-color: #e0e0e0;
      color: #666;

      &.mat-step-icon-state-done {
        background-color: #4caf50;
        color: white;
      }

      &.mat-step-icon-state-edit {
        background-color: #1976d2;
        color: white;
      }
    }

    .mat-step-label {
      font-weight: 500;
      color: #333;

      &.mat-step-label-active {
        color: #1976d2;
      }
    }
  }

  .mat-step-content {
    margin-top: 16px;
    margin-left: 36px;
    padding-left: 24px;
    border-left: 1px solid #e0e0e0;
  }
}

// Form field customization
mat-form-field {
  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
  }

  &.mat-focused {
    .mat-mdc-form-field-outline-thick {
      color: #1976d2;
    }
  }

  &.mat-form-field-invalid {
    .mat-mdc-form-field-outline-thick {
      color: #f44336;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .setup-wizard-container {
    padding: 10px;

    .setup-card {
      mat-card-header {
        padding: 16px;

        mat-card-title {
          font-size: 1.3rem;

          mat-icon {
            font-size: 24px;
            width: 24px;
            height: 24px;
          }
        }

        mat-card-subtitle {
          font-size: 0.9rem;
        }
      }

      mat-card-content {
        padding: 20px;
      }
    }
  }

  .step-form {
    .form-section {
      .form-row {
        flex-direction: column;
        gap: 8px;
      }
    }

    .step-actions {
      flex-direction: column;

      button {
        width: 100%;
      }
    }
  }

  mat-stepper {
    .mat-step-content {
      margin-left: 24px;
      padding-left: 16px;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .setup-wizard-container {
    .setup-card {
      background-color: #424242;
      color: #fff;

      mat-card-content {
        background-color: #424242;
      }
    }
  }

  .step-form {
    .form-section {
      h3 {
        color: #fff;
        border-bottom-color: #616161;
      }

      .section-description {
        color: #ccc;
      }
    }

    .step-actions {
      border-top-color: #616161;
    }
  }

  .progress-section {
    .progress-info {
      .progress-text {
        color: #fff;
      }
    }
  }

  mat-stepper {
    .mat-step-header {
      .mat-step-label {
        color: #fff;
      }
    }

    .mat-step-content {
      border-left-color: #616161;
    }
  }
}

// Animation for step completion
.step-form {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Loading state
.step-actions button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

// Success animations
.finalize-section {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
