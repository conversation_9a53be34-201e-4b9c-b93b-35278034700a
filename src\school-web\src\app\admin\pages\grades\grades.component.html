<div class="grades-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>
        <mat-toolbar color="primary">
          <span>{{ 'GRADES.TITLE' | translate }}</span>
          <span class="spacer"></span>
          <button mat-raised-button color="accent" (click)="openCreateDialog()">
            <mat-icon>add</mat-icon>
            {{ 'GRADES.ADD_GRADE' | translate }}
          </button>
        </mat-toolbar>
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <!-- Search and Filter Section -->
      <div class="filter-section">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>{{ 'COMMON.SEARCH' | translate }}</mat-label>
          <input matInput 
                 [(ngModel)]="searchTerm" 
                 (keyup.enter)="onSearch()"
                 placeholder="{{ 'GRADES.SEARCH_PLACEHOLDER' | translate }}">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>{{ 'GRADES.ACADEMIC_YEAR' | translate }}</mat-label>
          <mat-select [(ngModel)]="selectedAcademicYear" (selectionChange)="onSearch()">
            <mat-option value="">{{ 'COMMON.ALL' | translate }}</mat-option>
            <!-- Academic years will be loaded dynamically -->
          </mat-select>
        </mat-form-field>

        <button mat-raised-button color="primary" (click)="onSearch()">
          <mat-icon>search</mat-icon>
          {{ 'COMMON.SEARCH' | translate }}
        </button>
      </div>

      <!-- Data Table -->
      <div class="table-container">
        <table mat-table [dataSource]="grades" class="grades-table" matSort>
          <!-- Code Column -->
          <ng-container matColumnDef="code">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'GRADES.CODE' | translate }}
            </th>
            <td mat-cell *matCellDef="let grade">
              <span class="grade-code">{{ grade.code }}</span>
            </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'GRADES.NAME' | translate }}
            </th>
            <td mat-cell *matCellDef="let grade">
              <div class="grade-info">
                <span class="grade-name">{{ grade.name }}</span>
                <small class="grade-description">{{ grade.description }}</small>
              </div>
            </td>
          </ng-container>

          <!-- Level Column -->
          <ng-container matColumnDef="level">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'GRADES.LEVEL' | translate }}
            </th>
            <td mat-cell *matCellDef="let grade">
              <span class="grade-level">{{ grade.level }}</span>
            </td>
          </ng-container>

          <!-- Education Level Column -->
          <ng-container matColumnDef="educationLevel">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'GRADES.EDUCATION_LEVEL' | translate }}
            </th>
            <td mat-cell *matCellDef="let grade">
              <span class="education-level">{{ getEducationLevelDisplay(grade.educationLevel) }}</span>
            </td>
          </ng-container>

          <!-- Max Students Column -->
          <ng-container matColumnDef="maxStudents">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'GRADES.MAX_STUDENTS' | translate }}
            </th>
            <td mat-cell *matCellDef="let grade">
              <span class="max-students">{{ grade.maxStudents }}</span>
            </td>
          </ng-container>

          <!-- Academic Year Column -->
          <ng-container matColumnDef="academicYear">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'GRADES.ACADEMIC_YEAR' | translate }}
            </th>
            <td mat-cell *matCellDef="let grade">
              <span class="academic-year">{{ grade.academicYearName }}</span>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="isActive">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'COMMON.STATUS' | translate }}
            </th>
            <td mat-cell *matCellDef="let grade">
              <mat-slide-toggle 
                [checked]="grade.isActive"
                (change)="toggleGradeStatus(grade)"
                [color]="'primary'">
              </mat-slide-toggle>
              <span class="status-text" [class.active]="grade.isActive" [class.inactive]="!grade.isActive">
                {{ grade.isActive ? ('COMMON.ACTIVE' | translate) : ('COMMON.INACTIVE' | translate) }}
              </span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'COMMON.ACTIONS' | translate }}
            </th>
            <td mat-cell *matCellDef="let grade">
              <div class="action-buttons">
                <button mat-icon-button 
                        color="primary" 
                        (click)="openEditDialog(grade)"
                        matTooltip="{{ 'COMMON.EDIT' | translate }}">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button 
                        color="warn" 
                        (click)="deleteGrade(grade)"
                        matTooltip="{{ 'COMMON.DELETE' | translate }}">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- Loading Indicator -->
        <div *ngIf="loading" class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
          <p>{{ 'COMMON.LOADING' | translate }}</p>
        </div>

        <!-- No Data Message -->
        <div *ngIf="!loading && grades.length === 0" class="no-data-container">
          <mat-icon class="no-data-icon">school</mat-icon>
          <h3>{{ 'GRADES.NO_GRADES_FOUND' | translate }}</h3>
          <p>{{ 'GRADES.NO_GRADES_MESSAGE' | translate }}</p>
          <button mat-raised-button color="primary" (click)="openCreateDialog()">
            <mat-icon>add</mat-icon>
            {{ 'GRADES.CREATE_FIRST_GRADE' | translate }}
          </button>
        </div>
      </div>

      <!-- Pagination -->
      <mat-paginator 
        [length]="totalCount"
        [pageSize]="pageSize"
        [pageSizeOptions]="[5, 10, 25, 50]"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
