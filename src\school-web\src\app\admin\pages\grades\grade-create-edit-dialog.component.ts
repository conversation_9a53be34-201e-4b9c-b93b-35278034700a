import { Component, OnInit, inject, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { GradeService, Grade, CreateGradeDto, UpdateGradeDto } from '../../../core/services/grade.service';
import { AcademicYearService } from '../../../core/services/academic-year.service';

export interface DialogData {
  mode: 'create' | 'edit';
  grade?: Grade;
}

@Component({
  selector: 'app-grade-create-edit-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatSlideToggleModule,
    MatIconModule,
    MatProgressSpinnerModule,
    TranslateModule
  ],
  templateUrl: './grade-create-edit-dialog.component.html',
  styleUrls: ['./grade-create-edit-dialog.component.scss']
})
export class GradeCreateEditDialogComponent implements OnInit {
  private fb = inject(FormBuilder);
  private gradeService = inject(GradeService);
  private academicYearService = inject(AcademicYearService);
  private dialogRef = inject(MatDialogRef<GradeCreateEditDialogComponent>);

  gradeForm: FormGroup;
  loading = false;
  academicYears: any[] = [];
  educationLevels: { value: string; label: string }[] = [];

  constructor(@Inject(MAT_DIALOG_DATA) public data: DialogData) {
    this.gradeForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      code: ['', [Validators.required, Validators.maxLength(20)]],
      level: [1, [Validators.required, Validators.min(1), Validators.max(20)]],
      educationLevel: ['', Validators.required],
      description: ['', Validators.maxLength(500)],
      minAge: [3, [Validators.required, Validators.min(0), Validators.max(25)]],
      maxAge: [25, [Validators.required, Validators.min(0), Validators.max(30)]],
      maxStudents: [30, [Validators.required, Validators.min(1), Validators.max(200)]],
      isActive: [true],
      displayOrder: [1, [Validators.required, Validators.min(1)]],
      academicYearId: ['', Validators.required],
      promotionCriteria: ['', Validators.maxLength(1000)],
      minPassingGrade: [40, [Validators.required, Validators.min(0), Validators.max(100)]],
      remarks: ['', Validators.maxLength(500)]
    });
  }

  ngOnInit() {
    this.loadAcademicYears();
    this.loadEducationLevels();

    if (this.data.mode === 'edit' && this.data.grade) {
      this.populateForm(this.data.grade);
    }

    // Add custom validator for age range
    this.gradeForm.get('maxAge')?.valueChanges.subscribe(() => {
      this.validateAgeRange();
    });
    this.gradeForm.get('minAge')?.valueChanges.subscribe(() => {
      this.validateAgeRange();
    });
  }

  loadAcademicYears() {
    this.academicYearService.getActiveAcademicYears().subscribe({
      next: (years) => {
        this.academicYears = years;
      },
      error: (error) => {
        console.error('Error loading academic years:', error);
      }
    });
  }

  loadEducationLevels() {
    this.gradeService.getEducationLevels().subscribe({
      next: (levels) => {
        this.educationLevels = levels;
      },
      error: (error) => {
        console.error('Error loading education levels:', error);
        // Fallback to default levels
        this.educationLevels = [
          { value: 'PreSchool', label: 'Pre-School' },
          { value: 'Primary', label: 'Primary' },
          { value: 'Secondary', label: 'Secondary' },
          { value: 'HigherSecondary', label: 'Higher Secondary' },
          { value: 'Undergraduate', label: 'Undergraduate' },
          { value: 'Postgraduate', label: 'Postgraduate' }
        ];
      }
    });
  }

  populateForm(grade: Grade) {
    this.gradeForm.patchValue({
      name: grade.name,
      code: grade.code,
      level: grade.level,
      educationLevel: grade.educationLevel,
      description: grade.description,
      minAge: grade.minAge,
      maxAge: grade.maxAge,
      maxStudents: grade.maxStudents,
      isActive: grade.isActive,
      displayOrder: grade.displayOrder,
      academicYearId: grade.academicYearId,
      promotionCriteria: grade.promotionCriteria,
      minPassingGrade: grade.minPassingGrade,
      remarks: grade.remarks
    });
  }

  validateAgeRange() {
    const minAge = this.gradeForm.get('minAge')?.value;
    const maxAge = this.gradeForm.get('maxAge')?.value;

    if (minAge && maxAge && minAge >= maxAge) {
      this.gradeForm.get('maxAge')?.setErrors({ ageRange: true });
    } else {
      const maxAgeControl = this.gradeForm.get('maxAge');
      if (maxAgeControl?.errors?.['ageRange']) {
        delete maxAgeControl.errors['ageRange'];
        if (Object.keys(maxAgeControl.errors).length === 0) {
          maxAgeControl.setErrors(null);
        }
      }
    }
  }

  onSubmit() {
    if (this.gradeForm.valid) {
      this.loading = true;
      const formValue = this.gradeForm.value;

      if (this.data.mode === 'create') {
        const createDto: CreateGradeDto = formValue;
        this.gradeService.createGrade(createDto).subscribe({
          next: () => {
            this.loading = false;
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('Error creating grade:', error);
            this.loading = false;
          }
        });
      } else {
        const updateDto: UpdateGradeDto = {
          name: formValue.name,
          code: formValue.code,
          level: formValue.level,
          educationLevel: formValue.educationLevel,
          description: formValue.description,
          minAge: formValue.minAge,
          maxAge: formValue.maxAge,
          maxStudents: formValue.maxStudents,
          isActive: formValue.isActive,
          displayOrder: formValue.displayOrder,
          promotionCriteria: formValue.promotionCriteria,
          minPassingGrade: formValue.minPassingGrade,
          remarks: formValue.remarks
        };

        this.gradeService.updateGrade(this.data.grade!.id, updateDto).subscribe({
          next: () => {
            this.loading = false;
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('Error updating grade:', error);
            this.loading = false;
          }
        });
      }
    }
  }

  onCancel() {
    this.dialogRef.close(false);
  }

  getTitle(): string {
    return this.data.mode === 'create' ? 'GRADES.CREATE_GRADE' : 'GRADES.EDIT_GRADE';
  }

  getSubmitButtonText(): string {
    return this.data.mode === 'create' ? 'COMMON.CREATE' : 'COMMON.UPDATE';
  }
}
