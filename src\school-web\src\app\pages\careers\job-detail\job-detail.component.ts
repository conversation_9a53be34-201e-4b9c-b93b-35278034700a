import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatStepperModule } from '@angular/material/stepper';
import { TranslateModule } from '@ngx-translate/core';

interface JobOpening {
  id: string;
  title: string;
  department: string;
  type: 'full-time' | 'part-time' | 'contract';
  location: string;
  description: string;
  responsibilities: string[];
  qualifications: string[];
  postedDate: Date;
  salary?: string;
  benefits?: string[];
  applicationDeadline?: Date;
  startDate?: string;
  contactEmail?: string;
  applicationUrl?: string;
  applicationProcess?: string[];
  requiredDocuments?: string[];
}

@Component({
  selector: 'app-job-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatDividerModule,
    MatStepperModule,
    TranslateModule
  ],
  templateUrl: './job-detail.component.html',
  styleUrls: ['./job-detail.component.scss']
})
export class JobDetailComponent implements OnInit {
  // Job data
  job: JobOpening | null = null;

  // Loading state
  loading = true;

  // Error state
  error = false;

  // Mock job data (in a real app, this would come from a service)
  private jobsData: JobOpening[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadJobsData();

    // Get job ID from route params
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.loadJob(id);
      } else {
        this.error = true;
        this.loading = false;
      }
    });
  }

  /**
   * Load jobs data
   */
  private loadJobsData(): void {
    // In a real app, this would be a service call
    this.jobsData = [
      {
        id: 'JOB001',
        title: 'Mathematics Teacher',
        department: 'Academic',
        type: 'full-time',
        location: 'Main Campus',
        description: 'We are seeking an experienced Mathematics teacher to join our secondary school faculty. The ideal candidate will have a passion for teaching mathematics and the ability to inspire students to achieve their full potential.',
        responsibilities: [
          'Teach mathematics to students in grades 9-12',
          'Develop and implement engaging lesson plans aligned with curriculum standards',
          'Assess student progress and provide constructive feedback',
          'Collaborate with other faculty members to enhance the mathematics program',
          'Participate in parent-teacher conferences and school events',
          'Serve as an advisor to a group of students'
        ],
        qualifications: [
          'Bachelor\'s degree in Mathematics or related field (Master\'s preferred)',
          'Teaching certification in Mathematics',
          'Minimum 3 years of teaching experience at the secondary level',
          'Strong knowledge of mathematics curriculum and teaching methodologies',
          'Excellent communication and interpersonal skills',
          'Ability to integrate technology into classroom instruction'
        ],
        postedDate: new Date('2023-09-15'),
        salary: 'Competitive salary based on qualifications and experience',
        benefits: [
          'Health insurance',
          'Retirement plan with employer matching',
          'Paid time off',
          'Professional development opportunities',
          'Tuition reimbursement for advanced degrees'
        ],
        applicationDeadline: new Date('2023-11-15'),
        startDate: 'January 2024',
        contactEmail: '<EMAIL>',
        applicationUrl: 'https://school.edu/careers/apply/JOB001',
        applicationProcess: [
          'Submit online application',
          'Initial screening interview',
          'Teaching demonstration',
          'Panel interview with department members',
          'Final interview with administration'
        ],
        requiredDocuments: [
          'Resume/CV',
          'Cover letter',
          'Teaching certification',
          'Transcripts',
          'Three professional references'
        ]
      },
      {
        id: 'JOB002',
        title: 'School Counselor',
        department: 'Student Support Services',
        type: 'full-time',
        location: 'Main Campus',
        description: 'We are looking for a dedicated School Counselor to provide academic, social, and emotional support to our students. The successful candidate will work collaboratively with teachers, administrators, and parents to ensure student success.',
        responsibilities: [
          'Provide individual and group counseling to students',
          'Develop and implement comprehensive school counseling programs',
          'Assist students with academic planning and college/career readiness',
          'Collaborate with teachers and parents to address student needs',
          'Coordinate with external resources and agencies when necessary',
          'Maintain accurate and confidential student records'
        ],
        qualifications: [
          'Master\'s degree in School Counseling or related field',
          'Valid counseling certification/license',
          'Experience working with school-aged children',
          'Knowledge of counseling theories and techniques',
          'Strong communication and interpersonal skills',
          'Ability to work effectively in a team environment'
        ],
        postedDate: new Date('2023-09-20'),
        salary: 'Competitive salary based on qualifications and experience',
        benefits: [
          'Health insurance',
          'Retirement plan with employer matching',
          'Paid time off',
          'Professional development opportunities'
        ],
        applicationDeadline: new Date('2023-11-20'),
        startDate: 'January 2024',
        contactEmail: '<EMAIL>',
        applicationUrl: 'https://school.edu/careers/apply/JOB002',
        applicationProcess: [
          'Submit online application',
          'Initial screening interview',
          'Case study presentation',
          'Panel interview with student support team',
          'Final interview with administration'
        ],
        requiredDocuments: [
          'Resume/CV',
          'Cover letter',
          'Counseling certification/license',
          'Transcripts',
          'Three professional references'
        ]
      },
      {
        id: 'JOB003',
        title: 'IT Support Specialist',
        department: 'Information Technology',
        type: 'full-time',
        location: 'Main Campus',
        description: 'We are seeking an IT Support Specialist to maintain and support our school\'s technology infrastructure. The ideal candidate will have strong technical skills and a commitment to providing excellent service to faculty, staff, and students.',
        responsibilities: [
          'Provide technical support to faculty, staff, and students',
          'Install, configure, and maintain computer hardware and software',
          'Troubleshoot and resolve technology issues in a timely manner',
          'Assist with the implementation of new technologies',
          'Maintain network security and data backup systems',
          'Train users on new software and hardware'
        ],
        qualifications: [
          'Bachelor\'s degree in Computer Science, Information Technology, or related field',
          'Minimum 2 years of experience in IT support',
          'Knowledge of Windows and Mac operating systems',
          'Experience with network administration and security',
          'Strong problem-solving and communication skills',
          'Ability to work independently and as part of a team'
        ],
        postedDate: new Date('2023-09-25'),
        salary: 'Competitive salary based on qualifications and experience',
        benefits: [
          'Health insurance',
          'Retirement plan',
          'Paid time off',
          'Professional development opportunities'
        ],
        applicationDeadline: new Date('2023-11-25'),
        startDate: 'January 2024',
        contactEmail: '<EMAIL>',
        applicationUrl: 'https://school.edu/careers/apply/JOB003',
        applicationProcess: [
          'Submit online application',
          'Technical assessment',
          'Initial interview',
          'Panel interview with IT team',
          'Final interview with administration'
        ],
        requiredDocuments: [
          'Resume/CV',
          'Cover letter',
          'Relevant certifications',
          'Three professional references'
        ]
      },
      {
        id: 'JOB004',
        title: 'Science Laboratory Assistant',
        department: 'Academic',
        type: 'part-time',
        location: 'Main Campus',
        description: 'We are looking for a Science Laboratory Assistant to support our science department. The successful candidate will assist science teachers with laboratory preparation, maintenance, and safety procedures.',
        responsibilities: [
          'Prepare laboratory materials and equipment for science classes',
          'Assist teachers during laboratory sessions',
          'Maintain laboratory equipment and supplies',
          'Ensure compliance with safety regulations and procedures',
          'Order and inventory laboratory supplies',
          'Assist with the development of laboratory activities'
        ],
        qualifications: [
          'Bachelor\'s degree in a science discipline',
          'Experience working in a laboratory setting',
          'Knowledge of laboratory safety procedures',
          'Ability to work effectively with teachers and students',
          'Strong organizational and time management skills',
          'Attention to detail and commitment to safety'
        ],
        postedDate: new Date('2023-10-01'),
        salary: 'Hourly rate based on qualifications and experience',
        benefits: [
          'Flexible schedule',
          'Professional development opportunities'
        ],
        applicationDeadline: new Date('2023-11-01'),
        startDate: 'January 2024',
        contactEmail: '<EMAIL>',
        applicationUrl: 'https://school.edu/careers/apply/JOB004',
        applicationProcess: [
          'Submit online application',
          'Initial interview',
          'Practical assessment',
          'Final interview with science department'
        ],
        requiredDocuments: [
          'Resume/CV',
          'Cover letter',
          'Two professional references'
        ]
      },
      {
        id: 'JOB005',
        title: 'Administrative Assistant',
        department: 'Administration',
        type: 'full-time',
        location: 'Main Campus',
        description: 'We are seeking an Administrative Assistant to provide administrative support to our school office. The ideal candidate will be organized, detail-oriented, and have excellent communication skills.',
        responsibilities: [
          'Greet visitors and answer phone calls',
          'Manage correspondence and filing systems',
          'Schedule appointments and meetings',
          'Prepare and distribute documents and reports',
          'Assist with event planning and coordination',
          'Provide general administrative support to school staff'
        ],
        qualifications: [
          'High school diploma required; Associate\'s or Bachelor\'s degree preferred',
          'Minimum 2 years of administrative experience',
          'Proficiency in Microsoft Office applications',
          'Excellent organizational and time management skills',
          'Strong written and verbal communication skills',
          'Ability to maintain confidentiality and work independently'
        ],
        postedDate: new Date('2023-10-05'),
        salary: 'Competitive salary based on qualifications and experience',
        benefits: [
          'Health insurance',
          'Retirement plan',
          'Paid time off',
          'Professional development opportunities'
        ],
        applicationDeadline: new Date('2023-11-05'),
        startDate: 'January 2024',
        contactEmail: '<EMAIL>',
        applicationUrl: 'https://school.edu/careers/apply/JOB005',
        applicationProcess: [
          'Submit online application',
          'Skills assessment',
          'Initial interview',
          'Final interview with administration'
        ],
        requiredDocuments: [
          'Resume/CV',
          'Cover letter',
          'Three professional references'
        ]
      }
    ];
  }

  /**
   * Load job by ID
   */
  private loadJob(id: string): void {
    const job = this.jobsData.find(job => job.id === id);

    if (job) {
      this.job = job;
      this.loading = false;
    } else {
      this.error = true;
      this.loading = false;
    }
  }

  /**
   * Navigate back to careers page
   */
  goBack(): void {
    this.router.navigate(['/careers']);
  }

  /**
   * Format date for display
   */
  formatDate(date: Date): string {
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    
    return date.toLocaleDateString('en-US', options);
  }

  /**
   * Check if application deadline has passed
   */
  isDeadlinePassed(): boolean {
    if (!this.job || !this.job.applicationDeadline) return false;
    
    const today = new Date();
    return today > this.job.applicationDeadline;
  }

  /**
   * Calculate days until deadline
   */
  getDaysUntilDeadline(): number {
    if (!this.job || !this.job.applicationDeadline) return 0;
    
    const today = new Date();
    const deadline = new Date(this.job.applicationDeadline);
    const diffTime = Math.abs(deadline.getTime() - today.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  }

  /**
   * Calculate days since posting
   */
  getDaysSincePosting(): number {
    if (!this.job) return 0;
    
    const today = new Date();
    const postedDate = new Date(this.job.postedDate);
    const diffTime = Math.abs(today.getTime() - postedDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  }
}
