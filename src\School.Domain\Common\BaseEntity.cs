namespace School.Domain.Common;

/// <summary>
/// Interface for entities that belong to a specific tenant/organization
/// </summary>
public interface ITenantEntity
{
    /// <summary>
    /// The ID of the tenant/organization this entity belongs to
    /// </summary>
    Guid TenantId { get; set; }
}

/// <summary>
/// Base class for all entities with common properties
/// </summary>
public abstract class BaseEntity
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedBy { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public string? LastModifiedBy { get; set; }
    public bool IsDeleted { get; set; } = false;
}
