import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatTableModule } from '@angular/material/table';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatDividerModule,
    MatTableModule,
    MatProgressBarModule,
    TranslateModule
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  // Stats data
  statsCards = [
    {
      title: 'Total Students',
      count: 1250,
      icon: 'school',
      color: '#3f51b5',
      increase: 5.2,
      period: 'since last month'
    },
    {
      title: 'Total Faculty',
      count: 85,
      icon: 'people',
      color: '#f44336',
      increase: 2.1,
      period: 'since last month'
    },
    {
      title: 'Active Notices',
      count: 24,
      icon: 'announcement',
      color: '#ff9800',
      increase: 12.5,
      period: 'since last month'
    },
    {
      title: 'Media Files',
      count: 342,
      icon: 'perm_media',
      color: '#4caf50',
      increase: 8.3,
      period: 'since last month'
    }
  ];

  // Recent activities
  recentActivities = [
    {
      action: 'New student registered',
      user: 'John Smith',
      time: '10 minutes ago',
      icon: 'person_add',
      color: '#3f51b5'
    },
    {
      action: 'Notice published',
      user: 'Sarah Johnson',
      time: '1 hour ago',
      icon: 'announcement',
      color: '#ff9800'
    },
    {
      action: 'Content updated',
      user: 'Michael Brown',
      time: '2 hours ago',
      icon: 'edit',
      color: '#4caf50'
    },
    {
      action: 'Media uploaded',
      user: 'Emily Davis',
      time: '3 hours ago',
      icon: 'cloud_upload',
      color: '#2196f3'
    },
    {
      action: 'User role changed',
      user: 'Robert Wilson',
      time: '5 hours ago',
      icon: 'security',
      color: '#f44336'
    }
  ];

  // Tasks data
  pendingTasks = [
    {
      task: 'Review admission applications',
      priority: 'High',
      deadline: 'Today',
      progress: 75
    },
    {
      task: 'Update course content',
      priority: 'Medium',
      deadline: 'Tomorrow',
      progress: 45
    },
    {
      task: 'Prepare monthly report',
      priority: 'High',
      deadline: 'In 2 days',
      progress: 30
    },
    {
      task: 'Schedule faculty meeting',
      priority: 'Low',
      deadline: 'Next week',
      progress: 10
    }
  ];

  // Table columns
  displayedColumns: string[] = ['task', 'priority', 'deadline', 'progress', 'actions'];

  constructor() { }

  ngOnInit(): void {
  }

  getPriorityClass(priority: string): string {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'priority-high';
      case 'medium':
        return 'priority-medium';
      case 'low':
        return 'priority-low';
      default:
        return '';
    }
  }

  getProgressColor(progress: number): string {
    if (progress < 30) {
      return 'warn';
    } else if (progress < 70) {
      return 'accent';
    } else {
      return 'primary';
    }
  }
}
