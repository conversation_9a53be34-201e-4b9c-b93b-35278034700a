<div class="setup-wizard-container">
  <mat-card class="setup-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>school</mat-icon>
        School Setup Wizard
      </mat-card-title>
      <mat-card-subtitle>
        Complete the setup process to start using your school management system
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <!-- No Tenant Access Message -->
      <div *ngIf="!currentTenant" class="no-tenant-message">
        <div class="message-content">
          <mat-icon class="large-icon">info</mat-icon>
          <h3>School Setup Required</h3>
          <p>You need a school to be created for you before you can proceed with setup.</p>
          <p><strong>Please contact your System Administrator to create a school and grant you access.</strong></p>
          <button mat-raised-button color="primary" routerLink="/login">
            <mat-icon>arrow_back</mat-icon>
            Back to Login
          </button>
        </div>
      </div>

      <!-- Main Wizard Content (only show when tenant is available) -->
      <div *ngIf="currentTenant">
        <!-- Progress Bar -->
        <div class="progress-section">
        <div class="progress-info">
          <span class="progress-text">Setup Progress</span>
          <span class="progress-percentage">{{ getProgressPercentage() | number:'1.0-0' }}%</span>
        </div>
        <mat-progress-bar mode="determinate" [value]="getProgressPercentage()"></mat-progress-bar>
      </div>

      <!-- Setup Stepper -->
      <mat-stepper #stepper orientation="vertical" [selectedIndex]="currentStepIndex" (selectionChange)="onStepChange($event)">
        
        <!-- Step 1: School Profile -->
        <mat-step [completed]="setupSteps[0] && setupSteps[0].isCompleted">
          <ng-template matStepLabel>School Profile</ng-template>
          <form [formGroup]="schoolProfileForm" class="step-form">
            <div class="form-section">
              <h3>Basic Information</h3>
              <div class="form-row">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>School Name</mat-label>
                  <input matInput formControlName="schoolName" placeholder="Enter school name">
                  <mat-error *ngIf="schoolProfileForm.get('schoolName')?.hasError('required')">
                    School name is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>School Type</mat-label>
                  <mat-select formControlName="schoolType">
                    <mat-option *ngFor="let type of schoolTypes" [value]="type">
                      {{ type }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Established Year</mat-label>
                  <input matInput type="number" formControlName="establishedYear">
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Affiliation Board</mat-label>
                  <mat-select formControlName="affiliationBoard">
                    <mat-option *ngFor="let board of affiliationBoards" [value]="board">
                      {{ board }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>

            <div class="form-section">
              <h3>Address</h3>
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Street Address</mat-label>
                <input matInput formControlName="street">
              </mat-form-field>

              <div class="form-row">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>City</mat-label>
                  <input matInput formControlName="city">
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>State</mat-label>
                  <input matInput formControlName="state">
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Postal Code</mat-label>
                  <input matInput formControlName="postalCode">
                </mat-form-field>
              </div>
            </div>

            <div class="form-section">
              <h3>Contact Information</h3>
              <div class="form-row">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Phone</mat-label>
                  <input matInput formControlName="phone">
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Email</mat-label>
                  <input matInput type="email" formControlName="email">
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Website (Optional)</mat-label>
                  <input matInput formControlName="website">
                </mat-form-field>
              </div>
            </div>

            <div class="step-actions">
              <button mat-raised-button color="primary" 
                      [disabled]="!schoolProfileForm.valid || isLoading"
                      (click)="completeStep(0)">
                Complete School Profile
              </button>
            </div>
          </form>
        </mat-step>

        <!-- Step 2: Academic Structure -->
        <mat-step [completed]="setupSteps[1] && setupSteps[1].isCompleted">
          <ng-template matStepLabel>Academic Structure</ng-template>
          <form [formGroup]="academicStructureForm" class="step-form">
            <div class="form-section">
              <h3>Academic Year Configuration</h3>
              <div class="form-row">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Academic Year Start</mat-label>
                  <mat-select formControlName="academicYearStart">
                    <mat-option value="01-01">January 1st</mat-option>
                    <mat-option value="04-01">April 1st</mat-option>
                    <mat-option value="06-01">June 1st</mat-option>
                    <mat-option value="09-01">September 1st</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Grade Structure</mat-label>
                  <mat-select formControlName="gradeStructure">
                    <mat-option value="K12">K-12 (Kindergarten to Grade 12)</mat-option>
                    <mat-option value="1-10">Grades 1-10</mat-option>
                    <mat-option value="1-12">Grades 1-12</mat-option>
                    <mat-option value="custom">Custom</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <div class="checkbox-section">
                <mat-checkbox formControlName="hasTerms">
                  Divide academic year into terms/semesters
                </mat-checkbox>
              </div>

              <div class="form-row" *ngIf="academicStructureForm.get('hasTerms')?.value">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Number of Terms</mat-label>
                  <mat-select formControlName="termCount">
                    <mat-option value="2">2 Terms (Semesters)</mat-option>
                    <mat-option value="3">3 Terms (Trimesters)</mat-option>
                    <mat-option value="4">4 Terms (Quarters)</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>

            <div class="step-actions">
              <button mat-raised-button color="primary" 
                      [disabled]="!academicStructureForm.valid || isLoading"
                      (click)="completeStep(1)">
                Complete Academic Structure
              </button>
            </div>
          </form>
        </mat-step>

        <!-- Step 3: User Roles -->
        <mat-step [completed]="setupSteps[2] && setupSteps[2].isCompleted">
          <ng-template matStepLabel>User Roles & Permissions</ng-template>
          <form [formGroup]="userRolesForm" class="step-form">
            <div class="form-section">
              <h3>Role Configuration</h3>
              <p class="section-description">
                Configure how user roles and permissions will be managed in your school.
              </p>

              <div class="checkbox-section">
                <mat-checkbox formControlName="defaultRoles">
                  Use default school roles (Principal, Vice Principal, Teacher, Student, Parent)
                </mat-checkbox>
              </div>

              <div class="checkbox-section">
                <mat-checkbox formControlName="enableCustomRoles">
                  Enable custom role creation
                </mat-checkbox>
              </div>
            </div>

            <div class="step-actions">
              <button mat-raised-button color="primary" 
                      [disabled]="isLoading"
                      (click)="completeStep(2)">
                Complete User Roles
              </button>
            </div>
          </form>
        </mat-step>

        <!-- Step 4: Initial Users (Optional) -->
        <mat-step [completed]="setupSteps[3] && setupSteps[3].isCompleted">
          <ng-template matStepLabel>Initial Users (Optional)</ng-template>
          <form [formGroup]="initialUsersForm" class="step-form">
            <div class="form-section">
              <h3>Create Initial User Accounts</h3>
              <p class="section-description">
                You can create initial user accounts now or skip this step and add users later.
              </p>

              <div class="checkbox-section">
                <mat-checkbox formControlName="createInitialUsers">
                  Create initial user accounts
                </mat-checkbox>
              </div>

              <div *ngIf="initialUsersForm.get('createInitialUsers')?.value" class="form-row">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Principal Email</mat-label>
                  <input matInput type="email" formControlName="principalEmail">
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Vice Principal Email</mat-label>
                  <input matInput type="email" formControlName="vicePrincipalEmail">
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Admin Email</mat-label>
                  <input matInput type="email" formControlName="adminEmail">
                </mat-form-field>
              </div>
            </div>

            <div class="step-actions">
              <button mat-raised-button color="primary" 
                      [disabled]="isLoading"
                      (click)="completeStep(3)">
                Complete Initial Users
              </button>
              <button mat-button (click)="skipStep(3)" [disabled]="isLoading">
                Skip This Step
              </button>
            </div>
          </form>
        </mat-step>

        <!-- Step 5: System Settings (Optional) -->
        <mat-step [completed]="setupSteps[4] && setupSteps[4].isCompleted">
          <ng-template matStepLabel>System Settings (Optional)</ng-template>
          <form [formGroup]="systemSettingsForm" class="step-form">
            <div class="form-section">
              <h3>System Preferences</h3>
              <div class="form-row">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Time Zone</mat-label>
                  <mat-select formControlName="timeZone">
                    <mat-option *ngFor="let tz of timeZones" [value]="tz.value">
                      {{ tz.label }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Currency</mat-label>
                  <mat-select formControlName="currency">
                    <mat-option *ngFor="let curr of currencies" [value]="curr.value">
                      {{ curr.symbol }} {{ curr.label }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Language</mat-label>
                  <mat-select formControlName="language">
                    <mat-option *ngFor="let lang of languages" [value]="lang.value">
                      {{ lang.label }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <h3>Notification Settings</h3>
              <div class="checkbox-section">
                <mat-checkbox formControlName="enableNotifications">
                  Enable system notifications
                </mat-checkbox>
              </div>
              <div class="checkbox-section">
                <mat-checkbox formControlName="enableEmail">
                  Enable email notifications
                </mat-checkbox>
              </div>
              <div class="checkbox-section">
                <mat-checkbox formControlName="enableSMS">
                  Enable SMS notifications
                </mat-checkbox>
              </div>
            </div>

            <div class="step-actions">
              <button mat-raised-button color="primary" 
                      [disabled]="!systemSettingsForm.valid || isLoading"
                      (click)="completeStep(4)">
                Complete System Settings
              </button>
              <button mat-button (click)="skipStep(4)" [disabled]="isLoading">
                Skip This Step
              </button>
            </div>
          </form>
        </mat-step>
      </mat-stepper>

      <!-- Finalize Setup -->
      <div class="finalize-section" *ngIf="canFinalize()">
        <mat-card class="finalize-card">
          <mat-card-content>
            <div class="finalize-content">
              <mat-icon class="success-icon">check_circle</mat-icon>
              <h3>Setup Complete!</h3>
              <p>All required steps have been completed. Click the button below to finalize your school setup.</p>
            </div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-raised-button color="primary" size="large" 
                    [disabled]="isLoading"
                    (click)="finalizeSetup()">
              <mat-icon>rocket_launch</mat-icon>
              Finalize Setup & Start Using System
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
      </div> <!-- End of main wizard content conditional -->
    </mat-card-content>
  </mat-card>
</div>
