using System.Collections.Generic;

namespace School.Application.DTOs;

public class ClubAchievementCreateDto
{
    public string Description { get; set; } = string.Empty;
    public int Year { get; set; }
    public bool IsActive { get; set; } = true;
    public List<ClubAchievementTranslationCreateDto> Translations { get; set; } = new();
}

public class ClubAchievementTranslationCreateDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}
