// Material Design 3 Theme Setup for School Management System
@use '@angular/material' as mat;
@use 'styles/components/hero' as hero;
@use 'styles/_sass-utils' as sass-utils;

// Import material variables for Angular Material
@use 'styles/_material-variables' as *;

// External font imports (these still use @import as they're URLs)
// Material Icons are loaded in index.html for better performance
@import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

// Include the common styles for Angular Material
@include mat.core();

// Define custom color palettes for School Management System
$school-primary-palette: (
  0: #000000,
  10: #001d36,
  20: #003258,
  25: #003f6b,
  30: #004c7f,
  35: #005a94,
  40: #0069aa,
  50: #0086d7,
  60: #00a5ff,
  70: #4fc3ff,
  80: #7dd5ff,
  90: #c2e8ff,
  95: #e1f4ff,
  98: #f1f9ff,
  99: #f8fcff,
  100: #ffffff,
  // Primary container colors
  primary: #0069aa,
  on-primary: #ffffff,
  primary-container: #c2e8ff,
  on-primary-container: #001d36,
  // Surface colors
  surface: #f8fcff,
  on-surface: #191c20,
  surface-variant: #dde3ea,
  on-surface-variant: #41474d,
  // Outline colors
  outline: #71787e,
  outline-variant: #c1c7ce,
  // Other colors
  shadow: #000000,
  scrim: #000000,
  inverse-surface: #2e3135,
  inverse-on-surface: #eff1f6,
  inverse-primary: #7dd5ff,
);

$school-secondary-palette: (
  0: #000000,
  10: #0f1419,
  20: #24292e,
  25: #2f353a,
  30: #3b4146,
  35: #474d53,
  40: #535a60,
  50: #6c737a,
  60: #858d94,
  70: #a0a7af,
  80: #bbc2ca,
  90: #d7dee6,
  95: #e5ecf4,
  98: #f1f9ff,
  99: #f8fcff,
  100: #ffffff,
);

// Create light theme using M3 approach
$light-theme: mat.define-theme((
  color: (
    theme-type: light,
    primary: mat.$azure-palette,
    tertiary: mat.$blue-palette,
  ),
));

// Create dark theme using M3 approach
$dark-theme: mat.define-theme((
  color: (
    theme-type: dark,
    primary: mat.$azure-palette,
    tertiary: mat.$blue-palette,
  ),
));

// Base styles that apply to both themes
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', 'Roboto', "Helvetica Neue", sans-serif;
  margin: 0;
  transition: all 0.3s ease;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &.has-fixed-nav {
    padding-top: 64px; // Same as navbar height
  }
}

// Apply Bengali font for Bengali language
:lang(bn), .bengali-text {
  font-family: 'Hind Siliguri', 'Inter', sans-serif !important;
}

// Default font for English
:lang(en) {
  font-family: 'Inter', 'Roboto', sans-serif;
}

// Light theme application
.light-theme {
  @include mat.all-component-themes($light-theme);

  // Custom CSS variables for consistent theming
  --sys-color-primary: #{mat.get-theme-color($light-theme, primary)};
  --sys-color-on-primary: #{mat.get-theme-color($light-theme, on-primary)};
  --sys-color-primary-container: #{mat.get-theme-color($light-theme, primary-container)};
  --sys-color-on-primary-container: #{mat.get-theme-color($light-theme, on-primary-container)};
  --sys-color-surface: #{mat.get-theme-color($light-theme, surface)};
  --sys-color-on-surface: #{mat.get-theme-color($light-theme, on-surface)};
  --sys-color-surface-variant: #{mat.get-theme-color($light-theme, surface-variant)};
  --sys-color-on-surface-variant: #{mat.get-theme-color($light-theme, on-surface-variant)};
  --sys-color-outline: #{mat.get-theme-color($light-theme, outline)};
  --sys-color-outline-variant: #{mat.get-theme-color($light-theme, outline-variant)};
  --sys-color-background: #{mat.get-theme-color($light-theme, surface)};
  --sys-color-on-background: #{mat.get-theme-color($light-theme, on-surface)};
  --sys-color-error: #{mat.get-theme-color($light-theme, error)};
  --sys-color-on-error: #{mat.get-theme-color($light-theme, on-error)};
  --sys-color-shadow: #{mat.get-theme-color($light-theme, shadow)};
}

// Dark theme application
.dark-theme {
  @include mat.all-component-themes($dark-theme);

  // Custom CSS variables for consistent theming
  --sys-color-primary: #{mat.get-theme-color($dark-theme, primary)};
  --sys-color-on-primary: #{mat.get-theme-color($dark-theme, on-primary)};
  --sys-color-primary-container: #{mat.get-theme-color($dark-theme, primary-container)};
  --sys-color-on-primary-container: #{mat.get-theme-color($dark-theme, on-primary-container)};
  --sys-color-surface: #{mat.get-theme-color($dark-theme, surface)};
  --sys-color-on-surface: #{mat.get-theme-color($dark-theme, on-surface)};
  --sys-color-surface-variant: #{mat.get-theme-color($dark-theme, surface-variant)};
  --sys-color-on-surface-variant: #{mat.get-theme-color($dark-theme, on-surface-variant)};
  --sys-color-outline: #{mat.get-theme-color($dark-theme, outline)};
  --sys-color-outline-variant: #{mat.get-theme-color($dark-theme, outline-variant)};
  --sys-color-background: #{mat.get-theme-color($dark-theme, surface)};
  --sys-color-on-background: #{mat.get-theme-color($dark-theme, on-surface)};
  --sys-color-error: #{mat.get-theme-color($dark-theme, error)};
  --sys-color-on-error: #{mat.get-theme-color($dark-theme, on-error)};
  --sys-color-shadow: #{mat.get-theme-color($dark-theme, shadow)};
}

// Material Icons styles - Enhanced for Material Design 3
mat-icon {
  &.mat-icon {
    // Ensure proper sizing and alignment
    height: 24px;
    width: 24px;
    overflow: hidden;

    // Force hardware acceleration for smoother rendering
    transform: translateZ(0);
    will-change: transform;

    // Fix alignment issues
    vertical-align: middle;
    display: inline-flex;
    justify-content: center;
    align-items: center;

    // Ensure proper font loading
    font-display: block;

    // Prevent icon flashing during load
    &::before {
      content: '';
      display: inline-block;
      width: 1em;
      height: 1em;
    }
  }
}

// Specific styles for each icon variant to ensure proper font-family
.material-icons {
  font-family: 'Material Icons', sans-serif !important;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined', sans-serif !important;
}

.material-icons-round {
  font-family: 'Material Icons Round', sans-serif !important;
}

.material-icons-sharp {
  font-family: 'Material Icons Sharp', sans-serif !important;
}

.material-icons-two-tone {
  font-family: 'Material Icons Two Tone', sans-serif !important;
}

// Enhanced Snackbar styles using Material Design 3 colors
.success-snackbar {
  --mdc-snackbar-container-color: var(--sys-color-primary);
  --mdc-snackbar-supporting-text-color: var(--sys-color-on-primary);
  --mdc-snackbar-action-color: var(--sys-color-on-primary);
}

.error-snackbar {
  --mdc-snackbar-container-color: var(--sys-color-error);
  --mdc-snackbar-supporting-text-color: var(--sys-color-on-error);
  --mdc-snackbar-action-color: var(--sys-color-on-error);
}

// Global utility classes for Material Design 3
.surface-container {
  background-color: var(--sys-color-surface-variant);
  color: var(--sys-color-on-surface-variant);
}

.surface-container-low {
  background-color: var(--sys-color-surface);
  color: var(--sys-color-on-surface);
}

.surface-container-high {
  background-color: var(--sys-color-primary-container);
  color: var(--sys-color-on-primary-container);
}

// Elevation styles following Material Design 3
.elevation-1 {
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
}

.elevation-2 {
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
}

.elevation-3 {
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
}

.elevation-4 {
  box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
}

.elevation-5 {
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);
}

// Responsive design utilities
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  @media (min-width: 768px) {
    padding: 0 24px;
  }

  @media (min-width: 1024px) {
    padding: 0 32px;
  }
}

// Import portal theme enhancements
@import 'styles/portal-theme';

// Import admin dashboard theme enhancements
@import 'styles/admin-dashboard-theme';

// Tenant Setup Standalone Styles
// Ensure tenant setup pages are completely outside any layout
.tenant-setup-page {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 10000 !important;
  overflow-y: auto !important;

  // Override any parent container styles
  margin: 0 !important;
  padding: 0 !important;

  // Ensure it's above everything else
  background: inherit;

  // Hide any parent scrollbars
  body & {
    overflow: hidden;
  }
}

// Hide main app content when tenant setup is active
body.tenant-setup-active {
  overflow: hidden !important;

  app-root > *:not(router-outlet):not(.tenant-setup-page) {
    display: none !important;
  }

  // Hide navigation, headers, footers, etc.
  nav, header, footer, .main-content, .sidebar {
    display: none !important;
  }
}
