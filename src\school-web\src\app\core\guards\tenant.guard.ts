import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, take, filter, timeout, catchError } from 'rxjs/operators';
import { TenantService } from '../services/tenant.service';

@Injectable({
  providedIn: 'root'
})
export class TenantGuard implements CanActivate, CanActivateChild {

  constructor(
    private tenantService: TenantService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkTenantAccess(state.url);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkTenantAccess(state.url);
  }

  /**
   * Check if tenant is loaded and valid
   */
  private checkTenantAccess(url: string): Observable<boolean> {
    // Wait for tenant to be loaded (with timeout)
    return this.tenantService.tenantLoaded$.pipe(
      filter(loaded => loaded), // Wait until tenant loading is complete
      take(1), // Take only the first emission
      timeout(10000), // 10 second timeout
      map(() => {
        const currentTenant = this.tenantService.getCurrentTenantSync();
        
        // Check if tenant is loaded and valid
        if (currentTenant && currentTenant.isActive) {
          return true;
        }
        
        // If no valid tenant, handle accordingly
        this.handleInvalidTenant(url);
        return false;
      }),
      catchError(error => {
        console.error('Tenant guard error:', error);
        this.handleTenantError(url);
        return of(false);
      })
    );
  }

  /**
   * Handle case when tenant is invalid or not found
   */
  private handleInvalidTenant(attemptedUrl: string): void {
    console.warn('Invalid or inactive tenant detected');
    
    // You can customize this behavior based on your requirements
    // Options:
    // 1. Redirect to tenant selection page
    // 2. Show error page
    // 3. Redirect to main domain
    
    // For now, we'll redirect to a tenant error page
    this.router.navigate(['/tenant-error'], { 
      queryParams: { 
        returnUrl: attemptedUrl,
        reason: 'invalid-tenant'
      } 
    });
  }

  /**
   * Handle tenant loading errors
   */
  private handleTenantError(attemptedUrl: string): void {
    console.error('Tenant loading error in guard');
    
    // Redirect to error page with appropriate message
    this.router.navigate(['/tenant-error'], { 
      queryParams: { 
        returnUrl: attemptedUrl,
        reason: 'loading-error'
      } 
    });
  }
}
