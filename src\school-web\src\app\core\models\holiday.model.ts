import { BaseModel } from './base.model';

export enum HolidayType {
  National = 0,
  Religious = 1,
  Cultural = 2,
  Academic = 3,
  Administrative = 4,
  Seasonal = 5,
  Custom = 6
}

export enum RecurrenceType {
  None = 0,
  Daily = 1,
  Weekly = 2,
  Monthly = 3,
  Yearly = 4,
  Custom = 5
}

export interface RecurrencePattern {
  type: RecurrenceType;
  interval: number;
  endDate?: Date;
  maxOccurrences?: number;
  daysOfWeek?: number[];
  dayOfMonth?: number;
  monthOfYear?: number;
  customPattern?: string;
}

export interface HolidayTranslation {
  id: string;
  holidayId: string;
  languageCode: string;
  name: string;
  description: string;
  remarks: string;
}

export interface CreateHolidayTranslation {
  languageCode: string;
  name: string;
  description: string;
  remarks: string;
}

export interface UpdateHolidayTranslation {
  name: string;
  description: string;
  remarks: string;
}

export interface Holiday extends BaseModel {
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  type: HolidayType;
  isRecurring: boolean;
  recurrencePattern?: RecurrencePattern;
  color: string;
  isActive: boolean;
  isPublic: boolean;
  remarks: string;
  academicYearId?: string;
  termId?: string;
  academicYearName: string;
  termName: string;
  translations: HolidayTranslation[];
}

export interface CreateHoliday {
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  type: HolidayType;
  isRecurring: boolean;
  recurrencePattern?: RecurrencePattern;
  color: string;
  isActive: boolean;
  isPublic: boolean;
  remarks: string;
  academicYearId?: string;
  termId?: string;
  translations?: CreateHolidayTranslation[];
}

export interface UpdateHoliday {
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  type: HolidayType;
  isRecurring: boolean;
  recurrencePattern?: RecurrencePattern;
  color: string;
  isActive: boolean;
  isPublic: boolean;
  remarks: string;
  academicYearId?: string;
  termId?: string;
}

export interface HolidayFilter {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: string;
  name?: string;
  type?: HolidayType;
  academicYearId?: string;
  termId?: string;
  startDate?: Date;
  endDate?: Date;
  isActive?: boolean;
  isPublic?: boolean;
  isRecurring?: boolean;
}

export interface HolidayEvent {
  id: string;
  name: string;
  start: Date;
  end: Date;
  allDay: boolean;
  type: HolidayType;
  color: string;
  isRecurring: boolean;
}

export interface HolidayStatistics {
  total: number;
  national: number;
  religious: number;
  cultural: number;
  academic: number;
  administrative: number;
  seasonal: number;
  custom: number;
  public: number;
  private: number;
  recurring: number;
  oneTime: number;
  currentMonth: number;
  upcoming30Days: number;
}

export interface ValidateHolidayDatesRequest {
  academicYearId?: string;
  termId?: string;
  startDate: Date;
  endDate: Date;
  excludeId?: string;
}

export interface GenerateRecurringHolidaysRequest {
  fromDate: Date;
  toDate: Date;
}

export interface UpdateRecurringHolidayRequest {
  holidayDto: UpdateHoliday;
  updateSeries: boolean;
}

// Helper functions for Holiday types
export function getHolidayTypeLabel(type: HolidayType): string {
  switch (type) {
    case HolidayType.National: return 'National';
    case HolidayType.Religious: return 'Religious';
    case HolidayType.Cultural: return 'Cultural';
    case HolidayType.Academic: return 'Academic';
    case HolidayType.Administrative: return 'Administrative';
    case HolidayType.Seasonal: return 'Seasonal';
    case HolidayType.Custom: return 'Custom';
    default: return 'Unknown';
  }
}

export function getHolidayTypeColor(type: HolidayType): string {
  switch (type) {
    case HolidayType.National: return '#FF5722';
    case HolidayType.Religious: return '#9C27B0';
    case HolidayType.Cultural: return '#FF9800';
    case HolidayType.Academic: return '#2196F3';
    case HolidayType.Administrative: return '#607D8B';
    case HolidayType.Seasonal: return '#4CAF50';
    case HolidayType.Custom: return '#795548';
    default: return '#9E9E9E';
  }
}

export function getRecurrenceTypeLabel(type: RecurrenceType): string {
  switch (type) {
    case RecurrenceType.None: return 'None';
    case RecurrenceType.Daily: return 'Daily';
    case RecurrenceType.Weekly: return 'Weekly';
    case RecurrenceType.Monthly: return 'Monthly';
    case RecurrenceType.Yearly: return 'Yearly';
    case RecurrenceType.Custom: return 'Custom';
    default: return 'Unknown';
  }
}

// Constants
export const HOLIDAY_COLORS = [
  '#FF5722', '#E91E63', '#9C27B0', '#673AB7',
  '#3F51B5', '#2196F3', '#03A9F4', '#00BCD4',
  '#009688', '#4CAF50', '#8BC34A', '#CDDC39',
  '#FFEB3B', '#FFC107', '#FF9800', '#FF5722',
  '#795548', '#9E9E9E', '#607D8B'
];

export const DEFAULT_HOLIDAY_FILTER: HolidayFilter = {
  page: 1,
  pageSize: 10,
  sortBy: 'startDate',
  sortDirection: 'asc'
};
