<div class="quick-links" (mouseenter)="openMenu(quickLinksMenuTrigger)" (mouseleave)="closeMenu()">
  <button mat-icon-button [matMenuTriggerFor]="quickLinksMenu" #quickLinksMenuTrigger="matMenuTrigger" class="quick-links-button">
    <mat-icon>more_vert</mat-icon>
  </button>
  <mat-menu #quickLinksMenu="matMenu" [overlapTrigger]="false" [hasBackdrop]="false">
    <div class="quick-links-content" (mouseenter)="clearCloseTimeout()" (mouseleave)="closeMenu()">
      <a mat-menu-item routerLink="/careers">
        <mat-icon>work</mat-icon>
        {{ 'QUICK_LINKS.CAREERS' | translate }}
      </a>
      <a mat-menu-item routerLink="/calendar">
        <mat-icon>event</mat-icon>
        {{ 'QUICK_LINKS.CALENDAR' | translate }}
      </a>
      <a mat-menu-item routerLink="/alumni">
        <mat-icon>school</mat-icon>
        {{ 'QUICK_LINKS.ALUMNI' | translate }}
      </a>
      <a mat-menu-item routerLink="/donate">
        <mat-icon>volunteer_activism</mat-icon>
        {{ 'QUICK_LINKS.DONATE' | translate }}
      </a>
      <a mat-menu-item routerLink="/contact">
        <mat-icon>contact_support</mat-icon>
        {{ 'QUICK_LINKS.CONTACT' | translate }}
      </a>
    </div>
  </mat-menu>
</div>
