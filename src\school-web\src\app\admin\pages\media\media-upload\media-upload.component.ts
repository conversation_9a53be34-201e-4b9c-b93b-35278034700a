import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { MediaService } from '../../../../core/services/media.service';

@Component({
  selector: 'app-media-upload',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    TranslateModule
  ],
  templateUrl: './media-upload.component.html',
  styleUrls: ['./media-upload.component.scss']
})
export class MediaUploadComponent implements OnInit {
  uploadForm!: FormGroup;
  isUploading = false;
  uploadProgress = 0;
  selectedFiles: File[] = [];
  previewUrls: string[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private mediaService: MediaService,
    private snackBar: MatSnackBar,
    private translate: TranslateService
  ) { }

  ngOnInit(): void {
    this.uploadForm = this.formBuilder.group({
      type: [0, Validators.required],
      altText: [''],
      caption: ['']
    });
  }

  onFileSelected(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      this.selectedFiles = Array.from(files);
      this.previewUrls = [];

      // Create previews for images
      this.selectedFiles.forEach(file => {
        if (file.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = (e: any) => {
            this.previewUrls.push(e.target.result);
          };
          reader.readAsDataURL(file);
        } else {
          // For non-image files, use a placeholder
          this.previewUrls.push('');
        }
      });
    }
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
    this.previewUrls.splice(index, 1);
  }

  uploadFiles(): void {
    if (this.uploadForm.invalid || this.selectedFiles.length === 0) {
      return;
    }

    this.isUploading = true;
    this.uploadProgress = 0;

    const formData = new FormData();
    const formValues = this.uploadForm.value;

    // Add form values
    formData.append('type', formValues.type);
    formData.append('altText', formValues.altText || '');
    formData.append('caption', formValues.caption || '');

    // Add files
    this.selectedFiles.forEach((file, index) => {
      formData.append('files', file, file.name);
    });

    // Simulate progress (in a real app, you would use HttpClient's progress events)
    const progressInterval = setInterval(() => {
      if (this.uploadProgress < 90) {
        this.uploadProgress += 10;
      }
    }, 300);

    this.mediaService.uploadMedia(formData).subscribe({
      next: (response) => {
        clearInterval(progressInterval);
        this.uploadProgress = 100;

        setTimeout(() => {
          this.isUploading = false;
          this.snackBar.open(
            this.translate.instant('admin.media.uploadSuccess'),
            this.translate.instant('common.close'),
            { duration: 3000 }
          );
          this.router.navigate(['/admin/media']);
        }, 500);
      },
      error: (error) => {
        clearInterval(progressInterval);
        console.error('Error uploading files:', error);
        this.isUploading = false;
        this.uploadProgress = 0;

        this.snackBar.open(
          this.translate.instant('admin.media.uploadError'),
          this.translate.instant('common.close'),
          { duration: 5000 }
        );
      }
    });
  }

  cancel(): void {
    this.router.navigate(['/admin/media']);
  }
}
