.notice-form-container {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;

      h1 {
        margin: 0 0 0 8px;
        font-size: 24px;
        font-weight: 500;
      }
    }
  }

  .loading-container, .error-container {
    display: flex;
    justify-content: center;
    padding: 40px 0;
  }

  .error-card {
    max-width: 500px;
    width: 100%;

    mat-card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      mat-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        margin-bottom: 16px;
      }

      p {
        margin-bottom: 16px;
        font-size: 16px;
      }
    }
  }

  .form-container {
    mat-card {
      margin-bottom: 20px;
    }

    .notice-form {
      padding: 20px 0;

      .form-row {
        margin-bottom: 16px;

        &.two-columns {
          display: flex;
          gap: 16px;

          mat-form-field {
            flex: 1;
          }
        }

        .full-width {
          width: 100%;
        }
      }
    }

    .action-card {
      margin-top: 20px;
    }

    .form-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .action-message {
        display: flex;
        align-items: center;
        gap: 8px;

        mat-icon {
          font-size: 20px;
          height: 20px;
          width: 20px;
        }

        span {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.6);
        }
      }

      .action-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }

  .spinner-button {
    display: inline-block;
    margin-left: 8px;
    vertical-align: middle;
  }

  .translation-indicator {
    font-size: 16px;
    height: 16px;
    width: 16px;
    margin-left: 8px;
    vertical-align: middle;
  }

  .tab-label {
    font-weight: 500;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .form-row.two-columns {
      flex-direction: column;
      gap: 0;
    }

    .form-container {
      .form-actions {
        flex-direction: column;
        gap: 16px;

        .action-message, .action-buttons {
          width: 100%;
        }

        .action-buttons {
          justify-content: space-between;
        }
      }
    }
  }
}
