using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

/// <summary>
/// Represents a section within a grade (e.g., "A", "B", "Science", "Commerce")
/// </summary>
public class Section : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this section belongs to
    /// </summary>
    public Guid TenantId { get; set; }

    /// <summary>
    /// Grade this section belongs to
    /// </summary>
    public Guid GradeId { get; set; }

    /// <summary>
    /// Section name (e.g., "A", "B", "Science Section", "Commerce Section")
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Section code (e.g., "A", "B", "SCI", "COM")
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Section type (Regular, Science, Commerce, Arts, etc.)
    /// </summary>
    public SectionType Type { get; set; } = SectionType.Regular;

    /// <summary>
    /// Teaching medium for this section
    /// </summary>
    public TeachingMedium Medium { get; set; } = TeachingMedium.Bengali;

    /// <summary>
    /// Shift timing for this section
    /// </summary>
    public ShiftType Shift { get; set; } = ShiftType.Morning;

    /// <summary>
    /// Maximum number of students allowed in this section
    /// </summary>
    public int Capacity { get; set; } = 40;

    /// <summary>
    /// Current number of enrolled students
    /// </summary>
    public int CurrentEnrollment { get; set; } = 0;

    /// <summary>
    /// Whether this section is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Display order for sorting
    /// </summary>
    public int DisplayOrder { get; set; }

    /// <summary>
    /// Academic year this section is for
    /// </summary>
    public Guid AcademicYearId { get; set; }

    /// <summary>
    /// Class teacher assigned to this section
    /// </summary>
    public Guid? ClassTeacherId { get; set; }

    /// <summary>
    /// Classroom assigned to this section
    /// </summary>
    public string Classroom { get; set; } = string.Empty;

    /// <summary>
    /// Room number or location
    /// </summary>
    public string RoomNumber { get; set; } = string.Empty;

    /// <summary>
    /// Description of the section
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Special requirements or notes
    /// </summary>
    public string Requirements { get; set; } = string.Empty;

    /// <summary>
    /// Remarks or additional notes
    /// </summary>
    public string Remarks { get; set; } = string.Empty;

    // Navigation properties
    /// <summary>
    /// Grade this section belongs to
    /// </summary>
    public Grade Grade { get; set; } = null!;

    /// <summary>
    /// Academic year this section is for
    /// </summary>
    public AcademicYear AcademicYear { get; set; } = null!;

    /// <summary>
    /// Class teacher assigned to this section
    /// </summary>
    public ClassTeacher? ClassTeacher { get; set; }

    /// <summary>
    /// Students enrolled in this section
    /// </summary>
    public ICollection<Student> Students { get; set; } = new List<Student>();

    /// <summary>
    /// Translation support
    /// </summary>
    public ICollection<SectionTranslation> Translations { get; set; } = new List<SectionTranslation>();
}

/// <summary>
/// Translation entity for Section
/// </summary>
public class SectionTranslation : BaseEntity
{
    public Guid SectionId { get; set; }
    public string LanguageCode { get; set; } = string.Empty; // "en", "bn"
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Requirements { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;

    // Navigation property
    public Section Section { get; set; } = null!;
}
