# Translation Management

This directory contains all translation files for the school website. The application supports both English and Bengali languages.

## Directory Structure

- `/en/` - English translation files (organized by module)
- `/bn/` - Bengali translation files (organized by module)
- `modules.json` - List of all modules and supported languages
- `mega-menu-translations.json` - Consolidated list of all mega menu translations (English)
- `bn/mega-menu-translations.json` - Consolidated list of all mega menu translations (Bengali)
- `merge-mega-menu-translations.js` - <PERSON><PERSON>t to merge mega menu translations into module files
- `check-unused-translations.js` - Script to identify unused translation keys

## Translation Files

Each module has its own translation file in both English and Bengali. For example:
- `en/core.json` - Core English translations
- `bn/core.json` - Core Bengali translations

## Mega Menu Translations

The mega menu translations are consolidated in the `mega-menu-translations.json` files. These files contain all the translation keys used in the navigation mega menus.

The keys are organized by section:
- `NAV` - Main navigation items
- `ABOUT` - About menu items
- `ACADEMICS` - Academics menu items
- `ADMISSIONS` - Admissions menu items
- `CAMPUS_LIFE` - Campus Life menu items
- `NEWS` - News menu items
- `EVENTS` - Events menu items
- `PORTALS` - Portals menu items
- `QUICK_LINKS` - Quick links items
- `FACULTY` - Faculty-related items
- `CONTACT` - Contact-related items

## Module Mapping

The mega menu translations are mapped to the following modules:
- `NAV` → core
- `QUICK_LINKS` → core
- `ABOUT` → about
- `ACADEMICS` → academics
- `ADMISSIONS` → admissions
- `CAMPUS_LIFE` → campus-life
- `NEWS` → news
- `EVENTS` → events
- `PORTALS` → core
- `FACULTY` → faculty
- `CONTACT` → contact

## Merging Translations

To merge the mega menu translations into the appropriate module files, run:

```bash
node merge-mega-menu-translations.js
```

This script will:
1. Read the mega menu translation files
2. Determine which module each key belongs to
3. Read the corresponding module file
4. Merge the translations
5. Write the updated module file

## Adding New Translations

1. Add new keys to the appropriate module file
2. If the key is used in a mega menu, also add it to the `mega-menu-translations.json` files
3. Run the merge script to ensure consistency

## Translation Usage in Components

In Angular components, translations are used with the `translate` pipe:

```html
{{ 'NAV.ABOUT' | translate }}
```

This will display "About Us" in English or "আমাদের সম্পর্কে" in Bengali, depending on the selected language.

## Cleaning Up Translations

To maintain a clean and efficient translation system:

1. Use the `check-unused-translations.js` script to identify unused translation keys:

```bash
node check-unused-translations.js
```

2. Remove any identified unused keys from the translation files

3. Keep all translations in the appropriate module files in the `/en/` and `/bn/` directories

4. Avoid creating translation files in the root directory

5. Update the `translationModules` array in the `TranslationValidatorService` when adding or removing modules
