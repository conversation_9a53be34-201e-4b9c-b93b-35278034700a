using System.ComponentModel.DataAnnotations;

namespace School.Application.DTOs;

public class CreateTenantRequest
{
    [Required]
    [StringLength(100, MinimumLength = 2)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(50, MinimumLength = 2)]
    [RegularExpression(@"^[a-z0-9-]+$", ErrorMessage = "Slug can only contain lowercase letters, numbers, and hyphens")]
    public string Slug { get; set; } = string.Empty;

    [Required]
    [StringLength(100, MinimumLength = 2)]
    public string DisplayName { get; set; } = string.Empty;

    [Required]
    public string Type { get; set; } = "School";

    [RegularExpression(@"^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$", ErrorMessage = "Invalid domain format")]
    public string? CustomDomain { get; set; }

    [Required]
    public string DefaultLanguage { get; set; } = "en-US";

    [Required]
    public string TimeZone { get; set; } = "UTC";

    [Required]
    public string Currency { get; set; } = "USD";

    public bool IsTrialActive { get; set; } = true;

    public string? TrialEndDate { get; set; }

    public CreateTenantAdminUser? AdminUser { get; set; }
}

public class CreateTenantAdminUser
{
    [Required]
    [StringLength(50, MinimumLength = 2)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [StringLength(50, MinimumLength = 2)]
    public string LastName { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [Required]
    [StringLength(50, MinimumLength = 3)]
    [RegularExpression(@"^[a-zA-Z0-9_]+$", ErrorMessage = "Username can only contain letters, numbers, and underscores")]
    public string Username { get; set; } = string.Empty;

    [Required]
    [StringLength(100, MinimumLength = 8)]
    public string Password { get; set; } = string.Empty;
}

public class UpdateTenantRequest
{
    [StringLength(100, MinimumLength = 2)]
    public string? Name { get; set; }

    [StringLength(100, MinimumLength = 2)]
    public string? DisplayName { get; set; }

    public string? Type { get; set; }

    [RegularExpression(@"^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$", ErrorMessage = "Invalid domain format")]
    public string? CustomDomain { get; set; }

    public string? DefaultLanguage { get; set; }

    public string? TimeZone { get; set; }

    public string? Currency { get; set; }

    public bool? IsTrialActive { get; set; }

    public string? TrialEndDate { get; set; }
}

public class UpdateTenantStatusRequest
{
    [Required]
    public bool IsActive { get; set; }
}

public class GrantTenantAccessRequest
{
    [Required]
    [EmailAddress]
    public string UserEmail { get; set; } = string.Empty;

    [Required]
    public string Role { get; set; } = "User";

    public bool SendInvitation { get; set; } = true;
}

public class TenantUserAccessDto
{
    public Guid UserId { get; set; }
    public string UserEmail { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime GrantedAt { get; set; }
    public string GrantedBy { get; set; } = string.Empty;
}

public class TenantStatsDto
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int TotalStudents { get; set; }
    public int TotalFaculty { get; set; }
    public int TotalClasses { get; set; }
    public DateTime LastActivity { get; set; }
    public long StorageUsed { get; set; }
    public string SubscriptionStatus { get; set; } = string.Empty;
}

public class TenantSearchRequest
{
    public string? Search { get; set; }
    public string? Status { get; set; }
    public string? Type { get; set; }
    public int Page { get; set; } = 1;
    public int Limit { get; set; } = 20;
}

public class TenantSearchResponse
{
    public List<TenantSummary> Tenants { get; set; } = new();
    public int Total { get; set; }
    public int Page { get; set; }
    public int Limit { get; set; }
    public int TotalPages { get; set; }
}

public class CheckSlugAvailabilityRequest
{
    [Required]
    public string Slug { get; set; } = string.Empty;
    public Guid? ExcludeTenantId { get; set; }
}

public class CheckSlugAvailabilityResponse
{
    public bool Available { get; set; }
    public string? Message { get; set; }
}

public class TenantSummary
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? CustomDomain { get; set; }
    public DateTime JoinedDate { get; set; }
    public bool IsTrialActive { get; set; }
    public DateTime? TrialEndDate { get; set; }
}
