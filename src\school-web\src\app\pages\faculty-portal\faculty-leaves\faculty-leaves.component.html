<div class="leaves-container">
  <h1 class="page-title">Leave Applications</h1>

  <div *ngIf="loading.faculty" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error.faculty" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load faculty data. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadFacultyData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading.faculty && !error.faculty && faculty" class="leaves-content">
    <!-- Filter Form -->
    <mat-card class="filter-card">
      <mat-card-header>
        <mat-card-title>Filter Leave Applications</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <form [formGroup]="filterForm" (ngSubmit)="applyFilter()">
          <div class="filter-form">
            <mat-form-field appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select formControlName="status">
                <mat-option *ngFor="let status of leaveStatuses" [value]="status.value">
                  {{ status.label }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <div class="filter-actions">
              <button mat-raised-button color="primary" type="submit">
                Apply Filter
              </button>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>

    <!-- Loading Indicator -->
    <div *ngIf="loading.leaves" class="leaves-loading">
      <mat-progress-bar mode="indeterminate"></mat-progress-bar>
    </div>

    <!-- Error Message -->
    <div *ngIf="error.leaves" class="leaves-error">
      <mat-error>
        <mat-icon>error</mat-icon>
        <span>Failed to load leave applications. Please try again.</span>
        <button mat-button color="warn" (click)="loadLeaves()">Retry</button>
      </mat-error>
    </div>

    <!-- No Leaves Message -->
    <div *ngIf="!loading.leaves && !error.leaves && leaves.length === 0" class="no-leaves">
      <mat-card>
        <mat-card-content>
          <p>No leave applications found with the selected status.</p>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Leaves List -->
    <div *ngIf="!loading.leaves && !error.leaves && leaves.length > 0" class="leaves-list">
      <mat-card *ngFor="let leave of leaves" class="leave-card">
        <mat-card-header>
          <div mat-card-avatar class="student-avatar">
            <mat-icon>account_circle</mat-icon>
          </div>
          <mat-card-title>Student Name</mat-card-title>
          <mat-card-subtitle>
            <!-- Student info will be populated from API in real implementation -->
            Class 10-A |
            Roll: 101 |
            ID: STU2023101
          </mat-card-subtitle>
          <div class="leave-status">
            <span class="status-badge" [ngClass]="getStatusClass(leave.status)">
              {{ getStatusLabel(leave.status) }}
            </span>
          </div>
        </mat-card-header>

        <mat-card-content>
          <div class="leave-details">
            <div class="leave-type">
              <span class="leave-label">Leave Type:</span>
              <span class="leave-value">{{ getLeaveTypeLabel(leave.type) }}</span>
            </div>

            <div class="leave-duration">
              <span class="leave-label">Duration:</span>
              <span class="leave-value">{{ calculateLeaveDays(leave.startDate, leave.endDate) }} days</span>
            </div>

            <div class="leave-dates">
              <div class="leave-date">
                <span class="leave-label">From:</span>
                <span class="leave-value">{{ leave.startDate | date:'mediumDate' }}</span>
              </div>
              <div class="leave-date">
                <span class="leave-label">To:</span>
                <span class="leave-value">{{ leave.endDate | date:'mediumDate' }}</span>
              </div>
            </div>

            <div class="leave-reason">
              <span class="leave-label">Reason:</span>
              <span class="leave-value">{{ leave.reason }}</span>
            </div>

            <div class="leave-attachment" *ngIf="leave.attachmentPath">
              <span class="leave-label">Attachment:</span>
              <a class="leave-value" [href]="leave.attachmentPath" target="_blank">View Attachment</a>
            </div>

            <div class="leave-applied">
              <span class="leave-label">Applied On:</span>
              <span class="leave-value">{{ leave.createdAt | date:'medium' }}</span>
            </div>
          </div>
        </mat-card-content>

        <mat-card-actions *ngIf="leave.status === 0">
          <button mat-button color="warn" (click)="rejectLeave(leave.id)">Reject</button>
          <button mat-raised-button color="primary" (click)="approveLeave(leave.id)">Approve</button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</div>
