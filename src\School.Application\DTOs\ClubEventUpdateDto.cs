using System;
using System.Collections.Generic;

namespace School.Application.DTOs;

public class ClubEventUpdateDto
{
    public int? Id { get; set; }
    public string? Title { get; set; }
    public DateTime? Date { get; set; }
    public string? Time { get; set; }
    public string? Location { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
    public List<ClubEventTranslationUpdateDto>? Translations { get; set; }
}

public class ClubEventTranslationUpdateDto
{
    public int? Id { get; set; }
    public string? LanguageCode { get; set; }
    public string? Title { get; set; }
    public string? Location { get; set; }
    public string? Description { get; set; }
}
