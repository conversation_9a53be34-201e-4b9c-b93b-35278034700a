<div class="profile-container">
  <h1 class="page-title">Parent Profile</h1>

  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load parent profile. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadParentData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading && !error && parent" class="profile-content">
    <mat-card class="profile-card">
      <mat-card-header>
        <div mat-card-avatar class="profile-avatar">
          <img *ngIf="parent.profileImage" [src]="parent.profileImage.filePath" alt="Parent Photo">
          <mat-icon *ngIf="!parent.profileImage">account_circle</mat-icon>
        </div>
        <mat-card-title>{{ parent.firstName }} {{ parent.lastName }}</mat-card-title>
        <mat-card-subtitle>{{ parent.email }}</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">Full Name</span>
            <span class="info-value">{{ parent.firstName }} {{ parent.lastName }}</span>
          </div>
          
          <div class="info-item">
            <span class="info-label">Gender</span>
            <span class="info-value">{{ getGenderLabel(parent.gender) }}</span>
          </div>
          
          <div class="info-item">
            <span class="info-label">Email</span>
            <span class="info-value">{{ parent.email }}</span>
          </div>
          
          <div class="info-item">
            <span class="info-label">Phone</span>
            <span class="info-value">{{ parent.phone }}</span>
          </div>
          
          <div class="info-item">
            <span class="info-label">Alternate Phone</span>
            <span class="info-value">{{ parent.alternatePhone || 'N/A' }}</span>
          </div>
          
          <div class="info-item">
            <span class="info-label">Occupation</span>
            <span class="info-value">{{ parent.occupation || 'N/A' }}</span>
          </div>
          
          <div class="info-item full-width">
            <span class="info-label">Address</span>
            <span class="info-value">{{ parent.address || 'N/A' }}</span>
          </div>
        </div>
        
        <div class="associated-students">
          <h3>Associated Students</h3>
          
          <div *ngIf="parent.students && parent.students.length > 0" class="students-list">
            <mat-card *ngFor="let studentParent of parent.students" class="student-card">
              <mat-card-header>
                <div mat-card-avatar class="student-avatar">
                  <img *ngIf="studentParent.student.profileImage" [src]="studentParent.student.profileImage.filePath" alt="Student Photo">
                  <mat-icon *ngIf="!studentParent.student.profileImage">account_circle</mat-icon>
                </div>
                <mat-card-title>{{ studentParent.student.firstName }} {{ studentParent.student.lastName }}</mat-card-title>
                <mat-card-subtitle>
                  Class {{ studentParent.student.currentGrade }}-{{ studentParent.student.section }} | 
                  ID: {{ studentParent.student.studentId }}
                </mat-card-subtitle>
              </mat-card-header>
              
              <mat-card-content>
                <div class="relation-info">
                  <div class="relation-type">
                    <span class="relation-label">Relation:</span>
                    <span class="relation-value">{{ 
                      studentParent.relationType === 0 ? 'Father' : 
                      studentParent.relationType === 1 ? 'Mother' : 
                      studentParent.relationType === 2 ? 'Guardian' : 'Other' 
                    }}</span>
                  </div>
                  
                  <div class="primary-contact">
                    <span class="relation-label">Primary Contact:</span>
                    <span class="relation-value">{{ studentParent.isPrimaryContact ? 'Yes' : 'No' }}</span>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
          
          <div *ngIf="!parent.students || parent.students.length === 0" class="no-students">
            <p>No students associated with this parent account.</p>
          </div>
        </div>
      </mat-card-content>
      
      <mat-card-actions>
        <button mat-raised-button color="primary">Edit Profile</button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
