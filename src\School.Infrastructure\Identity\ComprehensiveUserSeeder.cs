using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using School.Domain.Entities;
using School.Domain.Enums;
using School.Infrastructure.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Identity
{
    public static class ComprehensiveUserSeeder
    {
        public static async Task SeedComprehensiveUsersAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<ComprehensiveUserSeeder>>();

            logger.LogInformation("Starting comprehensive user seeding...");

            // Seed Faculty with related data
            await SeedFacultyUsersAsync(userManager, context, logger);

            // Seed Students with related data
            await SeedStudentUsersAsync(userManager, context, logger);

            // Seed Parents with related data
            await SeedParentUsersAsync(userManager, context, logger);

            // Seed Alumni with related data
            await SeedAlumniUsersAsync(userManager, context, logger);

            // Save all changes
            await context.SaveChangesAsync();

            logger.LogInformation("Comprehensive user seeding completed");
        }

        private static async Task SeedFacultyUsersAsync(UserManager<ApplicationUser> userManager, ApplicationDbContext context, ILogger logger)
        {
            logger.LogInformation("Seeding faculty users and related data...");

            var facultyData = new[]
            {
                new { Username = "dr.smith", Email = "<EMAIL>", FirstName = "Dr. John", LastName = "Smith", Department = "Mathematics", Designation = "Professor", Specialization = "Advanced Calculus" },
                new { Username = "prof.johnson", Email = "<EMAIL>", FirstName = "Prof. Sarah", LastName = "Johnson", Department = "Physics", Designation = "Associate Professor", Specialization = "Quantum Physics" },
                new { Username = "dr.rahman", Email = "<EMAIL>", FirstName = "Dr. Ahmed", LastName = "Rahman", Department = "Chemistry", Designation = "Professor", Specialization = "Organic Chemistry" },
                new { Username = "ms.khan", Email = "<EMAIL>", FirstName = "Ms. Fatima", LastName = "Khan", Department = "English", Designation = "Assistant Professor", Specialization = "Literature" },
                new { Username = "mr.uddin", Email = "<EMAIL>", FirstName = "Mr. Karim", LastName = "Uddin", Department = "Computer Science", Designation = "Lecturer", Specialization = "Software Engineering" }
            };

            foreach (var faculty in facultyData)
            {
                var existingUser = await userManager.FindByNameAsync(faculty.Username);
                if (existingUser == null)
                {
                    var user = new ApplicationUser
                    {
                        Id = Guid.NewGuid().ToString(),
                        UserName = faculty.Username,
                        Email = faculty.Email,
                        EmailConfirmed = true,
                        FirstName = faculty.FirstName,
                        LastName = faculty.LastName,
                        Role = UserRole.Faculty,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        PhoneNumber = $"+***********{Random.Shared.Next(10, 99)}",
                        DateOfBirth = DateTime.Now.AddYears(-Random.Shared.Next(30, 50)),
                        Address = $"{Random.Shared.Next(100, 999)} Faculty Lane, Dhaka",
                        PreferredLanguage = "en",
                        PreferredTheme = "light"
                    };

                    var result = await userManager.CreateAsync(user, "Faculty@123");
                    if (result.Succeeded)
                    {
                        await userManager.AddToRoleAsync(user, UserRole.Faculty.ToString());

                        // Create Faculty entity
                        var facultyEntity = new Faculty
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id,
                            EmployeeId = $"FAC{Random.Shared.Next(1000, 9999)}",
                            Department = faculty.Department,
                            Designation = faculty.Designation,
                            JoiningDate = DateTime.Now.AddYears(-Random.Shared.Next(1, 10)),
                            Salary = Random.Shared.Next(50000, 150000),
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        };

                        context.Faculty.Add(facultyEntity);

                        // Add specialization
                        var specialization = new FacultySpecialization
                        {
                            Id = Guid.NewGuid(),
                            FacultyId = facultyEntity.Id,
                            Specialization = faculty.Specialization,
                            CreatedAt = DateTime.UtcNow
                        };

                        context.FacultySpecializations.Add(specialization);

                        logger.LogInformation("Created faculty user: {Username}", faculty.Username);
                    }
                }
            }
        }

        private static async Task SeedStudentUsersAsync(UserManager<ApplicationUser> userManager, ApplicationDbContext context, ILogger logger)
        {
            logger.LogInformation("Seeding student users and related data...");

            var studentData = new[]
            {
                new { Username = "student.michael", Email = "<EMAIL>", FirstName = "Michael", LastName = "Brown", Class = "Grade 10", Section = "A", RollNumber = "2024001" },
                new { Username = "student.emily", Email = "<EMAIL>", FirstName = "Emily", LastName = "Davis", Class = "Grade 11", Section = "B", RollNumber = "2024002" },
                new { Username = "student.david", Email = "<EMAIL>", FirstName = "David", LastName = "Wilson", Class = "Grade 9", Section = "A", RollNumber = "2024003" },
                new { Username = "student.rashida", Email = "<EMAIL>", FirstName = "Rashida", LastName = "Begum", Class = "Grade 10", Section = "C", RollNumber = "2024004" },
                new { Username = "student.arif", Email = "<EMAIL>", FirstName = "Arif", LastName = "Hasan", Class = "Grade 11", Section = "A", RollNumber = "2024005" },
                new { Username = "student.nadia", Email = "<EMAIL>", FirstName = "Nadia", LastName = "Islam", Class = "Grade 9", Section = "B", RollNumber = "2024006" }
            };

            foreach (var student in studentData)
            {
                var existingUser = await userManager.FindByNameAsync(student.Username);
                if (existingUser == null)
                {
                    var user = new ApplicationUser
                    {
                        Id = Guid.NewGuid().ToString(),
                        UserName = student.Username,
                        Email = student.Email,
                        EmailConfirmed = true,
                        FirstName = student.FirstName,
                        LastName = student.LastName,
                        Role = UserRole.Student,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        PhoneNumber = $"+***********{Random.Shared.Next(10, 99)}",
                        DateOfBirth = DateTime.Now.AddYears(-Random.Shared.Next(14, 18)),
                        Address = $"{Random.Shared.Next(100, 999)} Student Hostel, Dhaka",
                        PreferredLanguage = Random.Shared.Next(0, 2) == 0 ? "en" : "bn",
                        PreferredTheme = Random.Shared.Next(0, 2) == 0 ? "light" : "dark"
                    };

                    var result = await userManager.CreateAsync(user, "Student@123");
                    if (result.Succeeded)
                    {
                        await userManager.AddToRoleAsync(user, UserRole.Student.ToString());

                        // Create Student entity
                        var studentEntity = new Student
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id,
                            StudentId = student.RollNumber,
                            Class = student.Class,
                            Section = student.Section,
                            RollNumber = student.RollNumber,
                            AdmissionDate = DateTime.Now.AddYears(-Random.Shared.Next(1, 5)),
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        };

                        context.Students.Add(studentEntity);

                        logger.LogInformation("Created student user: {Username}", student.Username);
                    }
                }
            }
        }

        private static async Task SeedParentUsersAsync(UserManager<ApplicationUser> userManager, ApplicationDbContext context, ILogger logger)
        {
            logger.LogInformation("Seeding parent users and related data...");

            var parentData = new[]
            {
                new { Username = "parent.taylor", Email = "<EMAIL>", FirstName = "Robert", LastName = "Taylor", Occupation = "Engineer", Relation = ParentRelationType.Father },
                new { Username = "parent.anderson", Email = "<EMAIL>", FirstName = "Jennifer", LastName = "Anderson", Occupation = "Doctor", Relation = ParentRelationType.Mother },
                new { Username = "parent.karim", Email = "<EMAIL>", FirstName = "Abdul", LastName = "Karim", Occupation = "Business Owner", Relation = ParentRelationType.Father },
                new { Username = "parent.salma", Email = "<EMAIL>", FirstName = "Salma", LastName = "Khatun", Occupation = "Teacher", Relation = ParentRelationType.Mother }
            };

            foreach (var parent in parentData)
            {
                var existingUser = await userManager.FindByNameAsync(parent.Username);
                if (existingUser == null)
                {
                    var user = new ApplicationUser
                    {
                        Id = Guid.NewGuid().ToString(),
                        UserName = parent.Username,
                        Email = parent.Email,
                        EmailConfirmed = true,
                        FirstName = parent.FirstName,
                        LastName = parent.LastName,
                        Role = UserRole.Parent,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        PhoneNumber = $"+***********{Random.Shared.Next(10, 99)}",
                        DateOfBirth = DateTime.Now.AddYears(-Random.Shared.Next(35, 55)),
                        Address = $"{Random.Shared.Next(100, 999)} Parent Residence, Dhaka",
                        PreferredLanguage = Random.Shared.Next(0, 2) == 0 ? "en" : "bn",
                        PreferredTheme = "light"
                    };

                    var result = await userManager.CreateAsync(user, "Parent@123");
                    if (result.Succeeded)
                    {
                        await userManager.AddToRoleAsync(user, UserRole.Parent.ToString());

                        // Create Parent entity
                        var parentEntity = new Parent
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id,
                            Occupation = parent.Occupation,
                            RelationType = parent.Relation,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        };

                        context.Parents.Add(parentEntity);

                        logger.LogInformation("Created parent user: {Username}", parent.Username);
                    }
                }
            }
        }

        private static async Task SeedAlumniUsersAsync(UserManager<ApplicationUser> userManager, ApplicationDbContext context, ILogger logger)
        {
            logger.LogInformation("Seeding alumni users and related data...");

            var alumniData = new[]
            {
                new { Username = "alumni.james", Email = "<EMAIL>", FirstName = "James", LastName = "Thomas", GraduationYear = 2018, CurrentPosition = "Software Engineer", Company = "Tech Corp" },
                new { Username = "alumni.jessica", Email = "<EMAIL>", FirstName = "Jessica", LastName = "Martinez", GraduationYear = 2019, CurrentPosition = "Marketing Manager", Company = "Global Inc" },
                new { Username = "alumni.mohammad", Email = "<EMAIL>", FirstName = "Mohammad", LastName = "Ali", GraduationYear = 2017, CurrentPosition = "Doctor", Company = "City Hospital" },
                new { Username = "alumni.ayesha", Email = "<EMAIL>", FirstName = "Ayesha", LastName = "Siddique", GraduationYear = 2020, CurrentPosition = "Entrepreneur", Company = "Own Business" }
            };

            foreach (var alumni in alumniData)
            {
                var existingUser = await userManager.FindByNameAsync(alumni.Username);
                if (existingUser == null)
                {
                    var user = new ApplicationUser
                    {
                        Id = Guid.NewGuid().ToString(),
                        UserName = alumni.Username,
                        Email = alumni.Email,
                        EmailConfirmed = true,
                        FirstName = alumni.FirstName,
                        LastName = alumni.LastName,
                        Role = UserRole.Alumni,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        PhoneNumber = $"+***********{Random.Shared.Next(10, 99)}",
                        DateOfBirth = DateTime.Now.AddYears(-Random.Shared.Next(22, 35)),
                        Address = $"{Random.Shared.Next(100, 999)} Alumni Street, Dhaka",
                        PreferredLanguage = Random.Shared.Next(0, 2) == 0 ? "en" : "bn",
                        PreferredTheme = Random.Shared.Next(0, 2) == 0 ? "light" : "dark"
                    };

                    var result = await userManager.CreateAsync(user, "Alumni@123");
                    if (result.Succeeded)
                    {
                        await userManager.AddToRoleAsync(user, UserRole.Alumni.ToString());

                        // Create Alumni entity
                        var alumniEntity = new Alumni
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id,
                            GraduationYear = alumni.GraduationYear,
                            CurrentPosition = alumni.CurrentPosition,
                            Company = alumni.Company,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        };

                        context.Alumni.Add(alumniEntity);

                        logger.LogInformation("Created alumni user: {Username}", alumni.Username);
                    }
                }
            }
        }
    }
}
