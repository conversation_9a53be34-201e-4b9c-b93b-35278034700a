using Microsoft.EntityFrameworkCore;
using School.Application.Common.Interfaces;
using School.Domain.Common;
using System.Linq.Expressions;

namespace School.Infrastructure.Persistence.Repositories;

/// <summary>
/// Generic repository implementation for CRUD operations
/// </summary>
/// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
public class Repository<T> : IRepository<T> where T : BaseEntity
{
    protected readonly ApplicationDbContext _dbContext;
    protected readonly DbSet<T> _dbSet;

    public Repository(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<T>();
    }

    // Query methods
    public virtual async Task<T?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FindAsync(new object[] { id }, cancellationToken);
    }

    public virtual async Task<T?> GetByIdAsync(Guid id, string[] includes, CancellationToken cancellationToken = default)
    {
        IQueryable<T> query = _dbSet;

        foreach (var include in includes)
        {
            query = query.Include(include);
        }

        return await query.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(e => !e.IsDeleted).ToListAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<T>> GetAllAsync(string[] includes, CancellationToken cancellationToken = default)
    {
        IQueryable<T> query = _dbSet.Where(e => !e.IsDeleted);

        foreach (var include in includes)
        {
            query = query.Include(include);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(e => !e.IsDeleted).Where(predicate).ToListAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<T>> FindAsync(Expression<Func<T, bool>> predicate, string[] includes, CancellationToken cancellationToken = default)
    {
        IQueryable<T> query = _dbSet.Where(e => !e.IsDeleted).Where(predicate);

        foreach (var include in includes)
        {
            query = query.Include(include);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<T>> FindAsync(Expression<Func<T, bool>> predicate, int skip, int take, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(e => !e.IsDeleted)
            .Where(predicate)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<T>> FindAsync(Expression<Func<T, bool>> predicate, int skip, int take, string[] includes, CancellationToken cancellationToken = default)
    {
        IQueryable<T> query = _dbSet.Where(e => !e.IsDeleted).Where(predicate);

        foreach (var include in includes)
        {
            query = query.Include(include);
        }

        return await query
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        if (predicate == null)
        {
            return await _dbSet.Where(e => !e.IsDeleted).CountAsync(cancellationToken);
        }

        return await _dbSet.Where(e => !e.IsDeleted).Where(predicate).CountAsync(cancellationToken);
    }

    public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(e => !e.IsDeleted).AnyAsync(predicate, cancellationToken);
    }

    // Command methods
    public virtual async Task<T> AddAsync(T entity, string? userId = null, CancellationToken cancellationToken = default)
    {
        // Set audit fields
        entity.CreatedAt = DateTime.UtcNow;
        entity.CreatedBy = userId;

        await _dbSet.AddAsync(entity, cancellationToken);
        return entity;
    }

    public virtual async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, string? userId = null, CancellationToken cancellationToken = default)
    {
        var entityList = entities.ToList();

        // Set audit fields for all entities
        foreach (var entity in entityList)
        {
            entity.CreatedAt = DateTime.UtcNow;
            entity.CreatedBy = userId;
        }

        await _dbSet.AddRangeAsync(entityList, cancellationToken);
        return entityList;
    }

    public virtual Task UpdateAsync(T entity, string? userId = null, CancellationToken cancellationToken = default)
    {
        // Set audit fields
        entity.LastModifiedAt = DateTime.UtcNow;
        entity.LastModifiedBy = userId;

        _dbContext.Entry(entity).State = EntityState.Modified;
        return Task.CompletedTask;
    }

    public virtual Task UpdateRangeAsync(IEnumerable<T> entities, string? userId = null, CancellationToken cancellationToken = default)
    {
        foreach (var entity in entities)
        {
            // Set audit fields
            entity.LastModifiedAt = DateTime.UtcNow;
            entity.LastModifiedBy = userId;

            _dbContext.Entry(entity).State = EntityState.Modified;
        }

        return Task.CompletedTask;
    }

    public virtual async Task DeleteAsync(T entity, string? userId = null, bool softDelete = true, CancellationToken cancellationToken = default)
    {
        if (softDelete)
        {
            // Soft delete with audit trail
            entity.IsDeleted = true;
            entity.LastModifiedAt = DateTime.UtcNow;
            entity.LastModifiedBy = userId;

            _dbContext.Entry(entity).State = EntityState.Modified;
        }
        else
        {
            _dbSet.Remove(entity);
        }
    }

    public virtual async Task DeleteRangeAsync(IEnumerable<T> entities, string? userId = null, bool softDelete = true, CancellationToken cancellationToken = default)
    {
        if (softDelete)
        {
            foreach (var entity in entities)
            {
                // Soft delete with audit trail
                entity.IsDeleted = true;
                entity.LastModifiedAt = DateTime.UtcNow;
                entity.LastModifiedBy = userId;

                _dbContext.Entry(entity).State = EntityState.Modified;
            }
        }
        else
        {
            _dbSet.RemoveRange(entities);
        }
    }

    public virtual async Task DeleteByIdAsync(Guid id, string? userId = null, bool softDelete = true, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);

        if (entity != null)
        {
            await DeleteAsync(entity, userId, softDelete, cancellationToken);
        }
    }

    // Advanced query methods
    public virtual IQueryable<T> AsQueryable()
    {
        return _dbSet.Where(e => !e.IsDeleted);
    }

    public virtual IQueryable<T> AsQueryable(params string[] includes)
    {
        IQueryable<T> query = _dbSet.Where(e => !e.IsDeleted);

        foreach (var include in includes)
        {
            query = query.Include(include);
        }

        return query;
    }
}
