import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Router } from '@angular/router';
import { Observable, catchError, throwError, switchMap } from 'rxjs';
import { AuthService } from '../services/auth.service';

/**
 * HTTP Interceptor
 * Handles headers, authentication tokens, and token refresh
 */
@Injectable()
export class HttpRequestInterceptor implements HttpInterceptor {

  private readonly publicEndpoints = [
    '/assets/i18n/',
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/refresh'
  ];

  constructor(private authService: AuthService, private router: Router) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    // Add basic headers
    let modifiedRequest = this.addHeaders(request);

    // Skip auth for public endpoints
    if (this.isPublicEndpoint(request.url)) {
      return next.handle(modifiedRequest);
    }

    // Add auth token if available
    const token = this.authService.getToken();
    if (token && this.authService.isLoggedIn()) {
      modifiedRequest = modifiedRequest.clone({
        setHeaders: { 'Authorization': `Bearer ${token}` }
      });
    }

    // Handle request with error handling
    return next.handle(modifiedRequest).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          // Try token refresh, otherwise logout
          const refreshToken = this.authService.getRefreshToken();
          if (refreshToken && !request.url.includes('/api/auth/refresh')) {
            return this.authService.refreshToken().pipe(
              switchMap((refreshResponse) => {
                if (refreshResponse?.token) {
                  // Retry with new token
                  const retryRequest = modifiedRequest.clone({
                    setHeaders: { 'Authorization': `Bearer ${refreshResponse.token}` }
                  });
                  return next.handle(retryRequest);
                } else {
                  this.handleAuthError();
                  return throwError(() => error);
                }
              }),
              catchError(() => {
                this.handleAuthError();
                return throwError(() => error);
              })
            );
          } else {
            this.handleAuthError();
          }
        }
        return throwError(() => error);
      })
    );
  }

  private isPublicEndpoint(url: string): boolean {
    return this.publicEndpoints.some(endpoint => url.includes(endpoint));
  }

  private addHeaders(request: HttpRequest<unknown>): HttpRequest<unknown> {
    // Skip multipart/form-data requests
    if (request.headers.get('Content-Type')?.includes('multipart/form-data')) {
      return request;
    }

    const headers: Record<string, string> = { 'Accept': 'application/json' };

    if (!request.headers.has('Content-Type')) {
      headers['Content-Type'] = 'application/json';
    }

    return request.clone({ setHeaders: headers });
  }

  private handleAuthError(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }
}
