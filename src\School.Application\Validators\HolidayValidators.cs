using FluentValidation;
using School.Application.DTOs;
using School.Domain.Enums;

namespace School.Application.Validators;

public class CreateHolidayDtoValidator : AbstractValidator<CreateHolidayDto>
{
    public CreateHolidayDtoValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Holiday name is required.")
            .MaximumLength(200).WithMessage("Holiday name cannot exceed 200 characters.")
            .MinimumLength(2).WithMessage("Holiday name must be at least 2 characters long.");

        RuleFor(x => x.Description)
            .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters.");

        RuleFor(x => x.StartDate)
            .NotEmpty().WithMessage("Start date is required.")
            .Must(BeValidDate).WithMessage("Start date must be a valid date.");

        RuleFor(x => x.EndDate)
            .NotEmpty().WithMessage("End date is required.")
            .Must(BeValidDate).WithMessage("End date must be a valid date.")
            .GreaterThanOrEqualTo(x => x.StartDate).WithMessage("End date must be greater than or equal to start date.");

        RuleFor(x => x.Type)
            .IsInEnum().WithMessage("Holiday type must be a valid value.");

        RuleFor(x => x.Color)
            .Matches(@"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$")
            .When(x => !string.IsNullOrEmpty(x.Color))
            .WithMessage("Color must be a valid hex color code (e.g., #FF0000).");

        RuleFor(x => x.Remarks)
            .MaximumLength(1000).WithMessage("Remarks cannot exceed 1000 characters.");

        RuleFor(x => x.RecurrencePattern)
            .SetValidator(new RecurrencePatternValidator()!)
            .When(x => x.IsRecurring && x.RecurrencePattern != null);

        RuleFor(x => x.IsRecurring)
            .Must((dto, isRecurring) => !isRecurring || dto.RecurrencePattern != null)
            .WithMessage("Recurrence pattern is required when holiday is recurring.");

        RuleFor(x => x.Translations)
            .Must(HaveUniqueLanguageCodes)
            .When(x => x.Translations != null && x.Translations.Any())
            .WithMessage("Each language code can only appear once in translations.");

        RuleForEach(x => x.Translations)
            .SetValidator(new CreateHolidayTranslationDtoValidator())
            .When(x => x.Translations != null);

        // Business rule: Holiday duration validation
        RuleFor(x => x)
            .Must(HaveReasonableDuration)
            .WithMessage("Holiday duration cannot exceed 365 days.");

        // Business rule: Future date validation for recurring holidays
        RuleFor(x => x.StartDate)
            .GreaterThanOrEqualTo(DateTime.Today.AddDays(-1))
            .When(x => x.IsRecurring)
            .WithMessage("Recurring holidays should start from today or a future date.");
    }

    private static bool BeValidDate(DateTime date)
    {
        return date != default && date.Year >= 1900 && date.Year <= 2100;
    }

    private static bool HaveUniqueLanguageCodes(IEnumerable<CreateHolidayTranslationDto>? translations)
    {
        if (translations == null) return true;
        
        var languageCodes = translations.Select(t => t.LanguageCode.ToLowerInvariant()).ToList();
        return languageCodes.Count == languageCodes.Distinct().Count();
    }

    private static bool HaveReasonableDuration(CreateHolidayDto dto)
    {
        return (dto.EndDate - dto.StartDate).TotalDays <= 365;
    }
}

public class UpdateHolidayDtoValidator : AbstractValidator<UpdateHolidayDto>
{
    public UpdateHolidayDtoValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Holiday name is required.")
            .MaximumLength(200).WithMessage("Holiday name cannot exceed 200 characters.")
            .MinimumLength(2).WithMessage("Holiday name must be at least 2 characters long.");

        RuleFor(x => x.Description)
            .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters.");

        RuleFor(x => x.StartDate)
            .NotEmpty().WithMessage("Start date is required.")
            .Must(BeValidDate).WithMessage("Start date must be a valid date.");

        RuleFor(x => x.EndDate)
            .NotEmpty().WithMessage("End date is required.")
            .Must(BeValidDate).WithMessage("End date must be a valid date.")
            .GreaterThanOrEqualTo(x => x.StartDate).WithMessage("End date must be greater than or equal to start date.");

        RuleFor(x => x.Type)
            .IsInEnum().WithMessage("Holiday type must be a valid value.");

        RuleFor(x => x.Color)
            .Matches(@"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$")
            .When(x => !string.IsNullOrEmpty(x.Color))
            .WithMessage("Color must be a valid hex color code (e.g., #FF0000).");

        RuleFor(x => x.Remarks)
            .MaximumLength(1000).WithMessage("Remarks cannot exceed 1000 characters.");

        RuleFor(x => x.RecurrencePattern)
            .SetValidator(new RecurrencePatternValidator()!)
            .When(x => x.IsRecurring && x.RecurrencePattern != null);

        RuleFor(x => x.IsRecurring)
            .Must((dto, isRecurring) => !isRecurring || dto.RecurrencePattern != null)
            .WithMessage("Recurrence pattern is required when holiday is recurring.");

        // Business rule: Holiday duration validation
        RuleFor(x => x)
            .Must(HaveReasonableDuration)
            .WithMessage("Holiday duration cannot exceed 365 days.");
    }

    private static bool BeValidDate(DateTime date)
    {
        return date != default && date.Year >= 1900 && date.Year <= 2100;
    }

    private static bool HaveReasonableDuration(UpdateHolidayDto dto)
    {
        return (dto.EndDate - dto.StartDate).TotalDays <= 365;
    }
}

public class HolidayFilterDtoValidator : AbstractValidator<HolidayFilterDto>
{
    public HolidayFilterDtoValidator()
    {
        RuleFor(x => x.Page)
            .GreaterThan(0).WithMessage("Page must be greater than 0.");

        RuleFor(x => x.PageSize)
            .GreaterThan(0).WithMessage("Page size must be greater than 0.")
            .LessThanOrEqualTo(100).WithMessage("Page size cannot exceed 100.");

        RuleFor(x => x.Name)
            .MaximumLength(200).WithMessage("Name filter cannot exceed 200 characters.");

        RuleFor(x => x.Type)
            .IsInEnum().When(x => x.Type.HasValue)
            .WithMessage("Holiday type must be a valid value.");

        RuleFor(x => x.StartDate)
            .Must(BeValidDate).When(x => x.StartDate.HasValue)
            .WithMessage("Start date must be a valid date.");

        RuleFor(x => x.EndDate)
            .Must(BeValidDate).When(x => x.EndDate.HasValue)
            .WithMessage("End date must be a valid date.")
            .GreaterThanOrEqualTo(x => x.StartDate)
            .When(x => x.StartDate.HasValue && x.EndDate.HasValue)
            .WithMessage("End date must be greater than or equal to start date.");

        RuleFor(x => x.SortBy)
            .Must(BeValidSortField)
            .When(x => !string.IsNullOrEmpty(x.SortBy))
            .WithMessage("Sort field must be one of: name, startdate, enddate, type, createdat.");

        RuleFor(x => x.SortDirection)
            .Must(BeValidSortDirection)
            .When(x => !string.IsNullOrEmpty(x.SortDirection))
            .WithMessage("Sort direction must be 'asc' or 'desc'.");
    }

    private static bool BeValidDate(DateTime? date)
    {
        return !date.HasValue || (date.Value != default && date.Value.Year >= 1900 && date.Value.Year <= 2100);
    }

    private static bool BeValidSortField(string? sortBy)
    {
        if (string.IsNullOrEmpty(sortBy)) return true;
        
        var validFields = new[] { "name", "startdate", "enddate", "type", "createdat" };
        return validFields.Contains(sortBy.ToLowerInvariant());
    }

    private static bool BeValidSortDirection(string? sortDirection)
    {
        if (string.IsNullOrEmpty(sortDirection)) return true;
        
        var validDirections = new[] { "asc", "desc" };
        return validDirections.Contains(sortDirection.ToLowerInvariant());
    }
}

public class CreateHolidayTranslationDtoValidator : AbstractValidator<CreateHolidayTranslationDto>
{
    public CreateHolidayTranslationDtoValidator()
    {
        RuleFor(x => x.LanguageCode)
            .NotEmpty().WithMessage("Language code is required.")
            .MaximumLength(10).WithMessage("Language code cannot exceed 10 characters.")
            .Matches(@"^[a-z]{2}(-[A-Z]{2})?$").WithMessage("Language code must be in format 'en' or 'en-US'.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Translated name is required.")
            .MaximumLength(200).WithMessage("Translated name cannot exceed 200 characters.")
            .MinimumLength(2).WithMessage("Translated name must be at least 2 characters long.");

        RuleFor(x => x.Description)
            .MaximumLength(1000).WithMessage("Translated description cannot exceed 1000 characters.");

        RuleFor(x => x.Remarks)
            .MaximumLength(1000).WithMessage("Translated remarks cannot exceed 1000 characters.");
    }
}

public class UpdateHolidayTranslationDtoValidator : AbstractValidator<UpdateHolidayTranslationDto>
{
    public UpdateHolidayTranslationDtoValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Translated name is required.")
            .MaximumLength(200).WithMessage("Translated name cannot exceed 200 characters.")
            .MinimumLength(2).WithMessage("Translated name must be at least 2 characters long.");

        RuleFor(x => x.Description)
            .MaximumLength(1000).WithMessage("Translated description cannot exceed 1000 characters.");

        RuleFor(x => x.Remarks)
            .MaximumLength(1000).WithMessage("Translated remarks cannot exceed 1000 characters.");
    }
}

public class RecurrencePatternValidator : AbstractValidator<School.Domain.ValueObjects.RecurrencePattern>
{
    public RecurrencePatternValidator()
    {
        RuleFor(x => x.Type)
            .IsInEnum().WithMessage("Recurrence type must be a valid value.");

        RuleFor(x => x.Interval)
            .GreaterThan(0).WithMessage("Recurrence interval must be greater than 0.")
            .LessThanOrEqualTo(100).WithMessage("Recurrence interval cannot exceed 100.");

        RuleFor(x => x.EndDate)
            .GreaterThan(DateTime.Today)
            .When(x => x.EndDate.HasValue)
            .WithMessage("Recurrence end date must be in the future.");

        RuleFor(x => x.MaxOccurrences)
            .GreaterThan(0)
            .When(x => x.MaxOccurrences.HasValue)
            .WithMessage("Maximum occurrences must be greater than 0.")
            .LessThanOrEqualTo(1000)
            .When(x => x.MaxOccurrences.HasValue)
            .WithMessage("Maximum occurrences cannot exceed 1000.");

        RuleFor(x => x.DaysOfWeek)
            .Must(HaveValidDaysOfWeek)
            .When(x => x.Type == RecurrenceType.Weekly && x.DaysOfWeek != null)
            .WithMessage("Days of week must contain valid values for weekly recurrence.");

        RuleFor(x => x.DayOfMonth)
            .InclusiveBetween(1, 31)
            .When(x => x.Type == RecurrenceType.Monthly && x.DayOfMonth.HasValue)
            .WithMessage("Day of month must be between 1 and 31 for monthly recurrence.");

        RuleFor(x => x.MonthOfYear)
            .InclusiveBetween(1, 12)
            .When(x => x.Type == RecurrenceType.Yearly && x.MonthOfYear.HasValue)
            .WithMessage("Month of year must be between 1 and 12 for yearly recurrence.");

        RuleFor(x => x.CustomPattern)
            .MaximumLength(500).WithMessage("Custom pattern cannot exceed 500 characters.");

        // Business rules for recurrence patterns
        RuleFor(x => x)
            .Must(HaveValidWeeklyPattern)
            .When(x => x.Type == RecurrenceType.Weekly)
            .WithMessage("Weekly recurrence must specify days of week.");

        RuleFor(x => x)
            .Must(HaveValidMonthlyPattern)
            .When(x => x.Type == RecurrenceType.Monthly)
            .WithMessage("Monthly recurrence must specify day of month.");

        RuleFor(x => x)
            .Must(HaveValidYearlyPattern)
            .When(x => x.Type == RecurrenceType.Yearly)
            .WithMessage("Yearly recurrence must specify month of year.");
    }

    private static bool HaveValidDaysOfWeek(List<DayOfWeek>? daysOfWeek)
    {
        return daysOfWeek != null && daysOfWeek.Any() && daysOfWeek.All(d => Enum.IsDefined(typeof(DayOfWeek), d));
    }

    private static bool HaveValidWeeklyPattern(School.Domain.ValueObjects.RecurrencePattern pattern)
    {
        return pattern.DaysOfWeek != null && pattern.DaysOfWeek.Any();
    }

    private static bool HaveValidMonthlyPattern(School.Domain.ValueObjects.RecurrencePattern pattern)
    {
        return pattern.DayOfMonth.HasValue;
    }

    private static bool HaveValidYearlyPattern(School.Domain.ValueObjects.RecurrencePattern pattern)
    {
        return pattern.MonthOfYear.HasValue;
    }
}
