// Hero Section
.hero {
  // Use global hero styles from _hero.scss
  position: relative;

  // Style for hero buttons inside the carousel
  ::ng-deep .hero-buttons {
    gap: 0.5rem;
    display: flex;
    justify-content: center;
    margin-top: 0.5rem;

    a {
      padding: 0.3rem 1rem;
      font-weight: 500;
      transition: transform 0.2s ease;
      font-size: 0.8rem;

      &:hover {
        transform: translateY(-2px);
      }
    }
  }
}
// Features Section
.features {
  padding: 4rem 2rem;
  background-color: #f5f5f5;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50px;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(to bottom right, transparent 49%, #f5f5f5 50%);
  }

  h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    position: relative;

    &::after {
      content: '';
      display: block;
      width: 60px;
      height: 3px;
      background-color: var(--mat-primary-color);
      margin: 1rem auto 0;
    }
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;

    .feature-card {
      text-align: center;
      padding: 2rem;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
      }

      mat-icon {
        font-size: 3rem;
        height: 3rem;
        width: 3rem;
        color: var(--mat-primary-color);
        margin-bottom: 1.5rem;
        transition: transform 0.3s ease;
      }

      h3 {
        margin-bottom: 1rem;
        font-weight: 500;
      }
    }
  }
}
// News Section
.news {
  padding: 4rem 2rem;

  h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    position: relative;

    &::after {
      content: '';
      display: block;
      width: 60px;
      height: 3px;
      background-color: var(--mat-primary-color);
      margin: 1rem auto 0;
    }
  }

  .news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;

    .news-card {
      border-radius: 12px;
      overflow: hidden;
      transition: transform 0.3s ease;
      background: white;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);

      &:hover {
        transform: translateY(-5px);

        img {
          transform: scale(1.05);
        }
      }

      img {
        height: 200px;
        width: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
      }

      .date {
        color: rgba(0, 0, 0, 0.6);
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        mat-icon {
          font-size: 1rem;
          height: 1rem;
          width: 1rem;
        }
      }
    }
  }
}
// Events Section
.events {
  padding: 4rem 2rem;
  background-color: #f5f5f5;

  h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    position: relative;

    &::after {
      content: '';
      display: block;
      width: 60px;
      height: 3px;
      background-color: var(--mat-primary-color);
      margin: 1rem auto 0;
    }
  }

  .events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;

    .event-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
      }

      mat-card-content {
        display: flex;
        gap: 1.5rem;
        padding: 1.5rem;
      }

      .event-date {
        text-align: center;
        min-width: 80px;
        padding: 0.75rem;
        background-color: var(--mat-primary-color);
        color: white;
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

        .day {
          font-size: 1.75rem;
          font-weight: bold;
          display: block;
          line-height: 1;
        }

        .month {
          font-size: 0.9rem;
          text-transform: uppercase;
          margin-top: 0.25rem;
        }
      }

      .event-details {
        flex: 1;

        h3 {
          margin-bottom: 0.75rem;
          font-weight: 500;
          color: var(--mat-primary-color);
        }

        p {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          margin-bottom: 0.5rem;
          color: rgba(0, 0, 0, 0.7);

          mat-icon {
            font-size: 1.1rem;
            height: 1.1rem;
            width: 1.1rem;
            color: var(--mat-primary-color);
          }
        }
      }
    }
  }
}
// Stats Section
.stats {
  background: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)),
              url('/assets/images/patterns/dots.png');
  padding: 4rem 2rem;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;

    .stat-item {
      padding: 2rem;

      .stat-number {
        font-size: 3rem;
        font-weight: 700;
        color: var(--mat-primary-color);
        margin-bottom: 0.5rem;
      }

      .stat-label {
        font-size: 1.1rem;
        color: rgba(0, 0, 0, 0.7);
      }
    }
  }
}
// Call to Action Section
.cta {
  padding: 6rem 2rem;
  background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)),
              url('/assets/images/cta-bg.jpg') no-repeat center center;
  background-size: cover;
  background-attachment: fixed;
  text-align: center;
  color: white;

  .cta-content {
    max-width: 800px;
    margin: 0 auto;

    h2 {
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
      font-weight: 700;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
      line-height: 1.6;
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-top: 2rem;

      a {
        min-width: 150px;
      }
    }
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .hero {
    // Height is controlled by global hero styles

    .hero-content {
      h1 {
        font-size: 1.8rem;
      }

      p {
        font-size: 0.95rem;
      }
    }
  }

  .features, .news, .events {
    padding: 3rem 1rem;

    h2 {
      font-size: 2rem;
    }
  }

  .cta {
    padding: 4rem 1rem;

    .cta-content {
      h2 {
        font-size: 2rem;
      }
    }
  }

  .stats {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  .cta {
    .cta-buttons {
      flex-direction: column;
      align-items: center;
    }
  }
}

@media (max-width: 480px) {
  .hero-buttons {
    flex-direction: column;
    gap: 1rem !important;
  }

  .event-card mat-card-content {
    flex-direction: column;
    text-align: center;

    .event-date {
      margin: 0 auto;
    }
  }

  .stats {
    .stats-grid {
      grid-template-columns: 1fr;
    }

    .stat-item {
      .stat-number {
        font-size: 2.5rem;
      }
    }
  }
}

