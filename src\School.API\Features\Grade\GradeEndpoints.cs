using Carter;
using Microsoft.AspNetCore.Mvc;
using School.Application.DTOs;
using School.Application.Features.Grade;

namespace School.API.Features.Grade;

/// <summary>
/// Grade management API endpoints
/// </summary>
public class GradeEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/grades")
            .WithTags("Grades")
            .RequireAuthorization();

        // Grade CRUD operations
        group.MapGet("/", GetAllGrades)
            .WithName("GetAllGrades")
            .WithSummary("Get all grades with filtering and pagination")
            .Produces<(IEnumerable<GradeDto> Grades, int TotalCount)>();

        group.MapGet("/{id:guid}", GetGradeById)
            .WithName("GetGradeById")
            .WithSummary("Get grade by ID")
            .Produces<GradeDto>()
            .Produces(404);

        group.MapPost("/", CreateGrade)
            .WithName("CreateGrade")
            .WithSummary("Create a new grade")
            .Produces<Guid>(201)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        group.MapPut("/{id:guid}", UpdateGrade)
            .WithName("UpdateGrade")
            .WithSummary("Update an existing grade")
            .Produces(204)
            .Produces(400)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapDelete("/{id:guid}", DeleteGrade)
            .WithName("DeleteGrade")
            .WithSummary("Delete a grade")
            .Produces(204)
            .Produces(400)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapPatch("/{id:guid}/activate", ActivateGrade)
            .WithName("ActivateGrade")
            .WithSummary("Activate a grade")
            .Produces(204)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapPatch("/{id:guid}/deactivate", DeactivateGrade)
            .WithName("DeactivateGrade")
            .WithSummary("Deactivate a grade")
            .Produces(204)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        // Grade validation
        group.MapGet("/validate/name/{name}", ValidateGradeName)
            .WithName("ValidateGradeName")
            .WithSummary("Check if grade name is unique")
            .Produces<bool>();

        // Grade analytics
        group.MapGet("/statistics", GetGradeStatistics)
            .WithName("GetGradeStatistics")
            .WithSummary("Get grade statistics")
            .Produces<object>();

        // Grade-specific operations
        group.MapGet("/{id:guid}/sections", GetGradeSections)
            .WithName("GetGradeSections")
            .WithSummary("Get sections for a grade")
            .Produces<IEnumerable<object>>();

        group.MapGet("/{id:guid}/students", GetGradeStudents)
            .WithName("GetGradeStudents")
            .WithSummary("Get students in a grade")
            .Produces<IEnumerable<object>>();

        group.MapGet("/{id:guid}/subjects", GetGradeSubjects)
            .WithName("GetGradeSubjects")
            .WithSummary("Get subjects for a grade")
            .Produces<IEnumerable<object>>();
    }

    #region Endpoint Implementations

    private static async Task<IResult> GetAllGrades(
        IGradeService gradeService,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] Guid? academicYearId = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string sortBy = "Name",
        [FromQuery] bool sortDescending = false)
    {
        try
        {
            var filter = new GradeFilterDto
            {
                SearchTerm = searchTerm,
                IsActive = isActive,
                AcademicYearId = academicYearId,
                Page = page,
                PageSize = pageSize,
                SortBy = sortBy,
                SortDescending = sortDescending
            };

            var (grades, totalCount) = await gradeService.GetAllGradesAsync(filter);

            var response = new
            {
                data = grades,
                totalCount,
                page = filter.Page,
                pageSize = filter.PageSize,
                totalPages = (int)Math.Ceiling((double)totalCount / filter.PageSize)
            };

            return Results.Ok(response);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving grades: {ex.Message}");
        }
    }

    private static async Task<IResult> GetGradeById(Guid id, IGradeService gradeService)
    {
        try
        {
            var grade = await gradeService.GetGradeByIdAsync(id);
            return grade != null ? Results.Ok(grade) : Results.NotFound($"Grade with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving grade: {ex.Message}");
        }
    }

    private static async Task<IResult> CreateGrade(CreateGradeDto gradeDto, IGradeService gradeService)
    {
        try
        {
            var gradeId = await gradeService.CreateGradeAsync(gradeDto);
            return Results.Created($"/api/grades/{gradeId}", gradeId);
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error creating grade: {ex.Message}");
        }
    }

    private static async Task<IResult> UpdateGrade(Guid id, UpdateGradeDto gradeDto, IGradeService gradeService)
    {
        try
        {
            var success = await gradeService.UpdateGradeAsync(id, gradeDto);
            return success ? Results.NoContent() : Results.NotFound($"Grade with ID {id} not found");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error updating grade: {ex.Message}");
        }
    }

    private static async Task<IResult> DeleteGrade(Guid id, IGradeService gradeService)
    {
        try
        {
            var success = await gradeService.DeleteGradeAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Grade with ID {id} not found");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error deleting grade: {ex.Message}");
        }
    }

    private static async Task<IResult> ActivateGrade(Guid id, IGradeService gradeService)
    {
        try
        {
            var success = await gradeService.ActivateGradeAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Grade with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error activating grade: {ex.Message}");
        }
    }

    private static async Task<IResult> DeactivateGrade(Guid id, IGradeService gradeService)
    {
        try
        {
            var success = await gradeService.DeactivateGradeAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Grade with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error deactivating grade: {ex.Message}");
        }
    }

    private static async Task<IResult> ValidateGradeName(string name, [FromQuery] Guid? excludeId, IGradeService gradeService)
    {
        try
        {
            // TODO: Implement when service method is available
            // var isUnique = await gradeService.IsGradeNameUniqueAsync(name, excludeId);
            return Results.Ok(true); // Temporary implementation
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error validating grade name: {ex.Message}");
        }
    }

    private static async Task<IResult> GetGradeStatistics(IGradeService gradeService)
    {
        try
        {
            // TODO: Implement when service method is available
            // var statistics = await gradeService.GetGradeStatisticsAsync();
            return Results.Ok(new { message = "Statistics endpoint not yet implemented" });
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving grade statistics: {ex.Message}");
        }
    }

    private static async Task<IResult> GetGradeSections(Guid id, IGradeService gradeService)
    {
        try
        {
            var sections = await gradeService.GetGradeSectionsAsync(id);
            return Results.Ok(sections);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving grade sections: {ex.Message}");
        }
    }

    private static async Task<IResult> GetGradeStudents(Guid id, IGradeService gradeService)
    {
        try
        {
            var students = await gradeService.GetGradeStudentsAsync(id);
            return Results.Ok(students);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving grade students: {ex.Message}");
        }
    }

    private static async Task<IResult> GetGradeSubjects(Guid id, IGradeService gradeService)
    {
        try
        {
            var subjects = await gradeService.GetGradeSubjectsAsync(id);
            return Results.Ok(subjects);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving grade subjects: {ex.Message}");
        }
    }

    #endregion
}
