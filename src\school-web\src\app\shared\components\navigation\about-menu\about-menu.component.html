<div class="menu-item" (mouseenter)="openMenu(aboutMenuTrigger)" (mouseleave)="closeMenu()">
  <button mat-button [matMenuTriggerFor]="aboutMenu" #aboutMenuTrigger="matMenuTrigger"
          class="menu-trigger" [class.active]="activeRoute.startsWith('/about')">
    {{ 'NAV.ABOUT' | translate }}
    <mat-icon>arrow_drop_down</mat-icon>
  </button>
  <mat-menu #aboutMenu="matMenu" class="mega-menu-panel" [overlapTrigger]="false" [hasBackdrop]="false">
    <div class="mega-menu-content" (mouseenter)="clearCloseTimeout()" (mouseleave)="closeMenu()">
      <div class="menu-column">
        <h3>{{ 'ABOUT.TITLE' | translate }}</h3>
        <a mat-menu-item routerLink="/about" [class.active]="activeRoute === '/about'">{{ 'ABOUT.OVERVIEW' | translate }}</a>
        <a mat-menu-item routerLink="/about/history" [class.active]="activeRoute === '/about/history'">{{ 'ABOUT.HISTORY' | translate }}</a>
        <a mat-menu-item routerLink="/about/mission" [class.active]="activeRoute === '/about/mission'">{{ 'ABOUT.MISSION' | translate }}</a>
        <a mat-menu-item routerLink="/about/leadership" [class.active]="activeRoute === '/about/leadership'">{{ 'ABOUT.LEADERSHIP' | translate }}</a>
      </div>
      <div class="menu-column">
        <h3>{{ 'ABOUT.FACILITIES' | translate }}</h3>
        <a mat-menu-item routerLink="/facilities" [class.active]="activeRoute === '/facilities'">{{ 'ABOUT.CAMPUS' | translate }}</a>
        <a mat-menu-item routerLink="/faculty" [class.active]="activeRoute === '/faculty'">{{ 'FACULTY.TITLE' | translate }}</a>
        <a mat-menu-item routerLink="/about/alumni" [class.active]="activeRoute === '/about/alumni'">{{ 'ABOUT.ALUMNI' | translate }}</a>
      </div>
      <div class="menu-column">
        <h3>{{ 'ABOUT.OPPORTUNITIES' | translate }}</h3>
        <a mat-menu-item routerLink="/careers" [class.active]="activeRoute === '/careers'">{{ 'QUICK_LINKS.CAREERS' | translate }}</a>
        <a mat-menu-item routerLink="/about/parents" [class.active]="activeRoute === '/about/parents'">{{ 'ABOUT.PARENTS' | translate }}</a>
      </div>
    </div>
  </mat-menu>
</div>
