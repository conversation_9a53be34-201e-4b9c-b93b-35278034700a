.results-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.results-content {
  max-width: 1000px;
  margin: 0 auto;
}

.filter-card {
  margin-bottom: 24px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.gpa-card {
  margin-bottom: 24px;
  text-align: center;
}

.gpa-display {
  padding: 24px;
}

.gpa-value {
  font-size: 48px;
  font-weight: 500;
  color: #3f51b5;
}

.gpa-label {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.6);
  margin-top: 8px;
}

.results-loading,
.results-error {
  margin-bottom: 24px;
}

.results-error {
  text-align: center;
  padding: 16px;

  mat-icon {
    vertical-align: middle;
    margin-right: 8px;
  }
}

.no-results {
  text-align: center;
  padding: 32px;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.6);
}

.results-table-container {
  margin-bottom: 24px;
  overflow-x: auto;
}

.results-table {
  width: 100%;

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  }

  th {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.87);
    background-color: #f5f5f5;
  }

  tr:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.optional-subject {
  background-color: rgba(0, 0, 0, 0.02);
}

.optional-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  background-color: #e0e0e0;
  color: rgba(0, 0, 0, 0.6);
  margin-left: 8px;
}

.grade-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  color: white;
}

.summary-card {
  margin-bottom: 24px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
}

.summary-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.summary-value {
  font-size: 16px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }

  .results-table {
    th, td {
      padding: 8px;
    }
  }
}
