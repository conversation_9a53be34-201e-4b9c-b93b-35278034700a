import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';
import { EnhancedHeroComponent } from '../../../shared/components/enhanced-hero/enhanced-hero.component';

@Component({
  selector: 'app-history',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    TranslateModule,
    EnhancedHeroComponent
  ],
  templateUrl: './history.component.html',
  styleUrls: ['./history.component.scss']
})
export class HistoryComponent {
  // Timeline events for the school history
  timelineEvents = [
    {
      year: '1985',
      title: 'Foundation',
      description: 'Our school was founded with a vision to provide quality education that nurtures both academic excellence and character development.',
      image: 'assets/images/history/foundation.jpg'
    },
    {
      year: '1990',
      title: 'First Graduating Class',
      description: 'Our first batch of students graduated with remarkable achievements, setting a high standard for future generations.',
      image: 'assets/images/history/first-graduation.jpg'
    },
    {
      year: '1995',
      title: 'Campus Expansion',
      description: 'The school expanded its campus to include new facilities for science, arts, and sports to provide a more comprehensive education.',
      image: 'assets/images/history/campus-expansion.jpg'
    },
    {
      year: '2000',
      title: 'Technology Integration',
      description: 'We embraced the digital age by integrating technology into our curriculum and establishing computer labs for all students.',
      image: 'assets/images/history/technology.jpg'
    },
    {
      year: '2005',
      title: 'International Recognition',
      description: 'Our school received international accreditation, recognizing our commitment to educational excellence and global standards.',
      image: 'assets/images/history/international.jpg'
    },
    {
      year: '2010',
      title: 'STEM Program Launch',
      description: 'We launched our comprehensive STEM program to prepare students for the challenges and opportunities of the 21st century.',
      image: 'assets/images/history/stem.jpg'
    },
    {
      year: '2015',
      title: 'Arts and Culture Center',
      description: 'The opening of our Arts and Culture Center provided students with new opportunities to explore and develop their creative talents.',
      image: 'assets/images/history/arts.jpg'
    },
    {
      year: '2020',
      title: 'Digital Transformation',
      description: 'We successfully navigated the challenges of remote learning and accelerated our digital transformation initiatives.',
      image: 'assets/images/history/digital.jpg'
    },
    {
      year: 'Today',
      title: 'Continuing Excellence',
      description: 'Today, we continue our tradition of excellence, preparing students to be thoughtful, responsible, and successful global citizens.',
      image: 'assets/images/history/today.jpg'
    }
  ];

  // Founder information
  founders = [
    {
      name: 'Dr. James Wilson',
      title: 'Founding Principal',
      bio: 'Dr. Wilson was a visionary educator who believed in the power of holistic education. His leadership laid the foundation for our school\'s philosophy and values.',
      image: 'assets/images/history/founder1.jpg'
    },
    {
      name: 'Mrs. Sarah Johnson',
      title: 'Co-Founder',
      bio: 'Mrs. Johnson was instrumental in developing our innovative curriculum and establishing our commitment to academic excellence and character development.',
      image: 'assets/images/history/founder2.jpg'
    }
  ];
}
