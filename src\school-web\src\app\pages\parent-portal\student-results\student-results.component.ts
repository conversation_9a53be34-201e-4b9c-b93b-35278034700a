import { Component, OnInit } from '@angular/core';
import { CommonModule, DecimalPipe } from '@angular/common';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { ParentService } from '../../../core/services/parent.service';
import { AuthService } from '../../../core/services/auth.service';
import { StudentResult, ExamType } from '../../../core/models/student.model';

@Component({
  selector: 'app-student-results',
  templateUrl: './student-results.component.html',
  styleUrls: ['./student-results.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    DecimalPipe,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatTableModule,
    MatSnackBarModule
  ]
})
export class StudentResultsComponent implements OnInit {
  studentId: number = 0;
  resultRecords: StudentResult[] = [];
  filterForm: FormGroup;

  loading = {
    results: true
  };

  error = {
    results: false
  };

  displayedColumns: string[] = ['subjectCode', 'subjectName', 'marksObtained', 'totalMarks', 'percentage', 'grade'];

  examTypes = [
    { value: ExamType.HalfYearly, label: 'Half Yearly' },
    { value: ExamType.Annual, label: 'Annual' },
    { value: ExamType.ClassTest, label: 'Class Test' },
    { value: ExamType.SSC, label: 'SSC' }
  ];

  constructor(
    private route: ActivatedRoute,
    private parentService: ParentService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    const currentYear = new Date().getFullYear();

    this.filterForm = this.formBuilder.group({
      examType: [ExamType.Annual],
      academicYear: [currentYear]
    });
  }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.studentId = +params['id'];
      this.loadResults();
    });
  }

  loadResults(): void {
    if (!this.studentId) return;

    this.loading.results = true;
    this.error.results = false;

    const parentId = 1; // In a real app, get from auth service
    const formValues = this.filterForm.value;

    this.parentService.getStudentResultsForParent(
      parentId,
      this.studentId,
      formValues.examType,
      formValues.academicYear
    ).subscribe({
      next: (records) => {
        this.resultRecords = records;
        this.loading.results = false;
      },
      error: (err) => {
        console.error('Error loading result records:', err);
        this.error.results = true;
        this.loading.results = false;
        this.snackBar.open('Failed to load result records', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  getExamTypeLabel(examType: ExamType): string {
    return this.examTypes.find(type => type.value === examType)?.label || 'Unknown';
  }

  getGradeColor(grade: string): string {
    switch (grade) {
      case 'A+': return '#2e7d32';
      case 'A': return '#388e3c';
      case 'A-': return '#43a047';
      case 'B+': return '#689f38';
      case 'B': return '#7cb342';
      case 'C+': return '#ffa000';
      case 'C': return '#ffb300';
      case 'D': return '#f57c00';
      case 'F': return '#d32f2f';
      default: return '#757575';
    }
  }

  calculateTotalGPA(): number {
    if (this.resultRecords.length === 0) return 0;

    const totalGPA = this.resultRecords.reduce((sum, record) => sum + record.gradePoint, 0);
    return totalGPA / this.resultRecords.length;
  }

  downloadResultSheet(): void {
    // In a real app, this would download a result sheet
    this.snackBar.open('Downloading result sheet...', 'Close', {
      duration: 3000
    });
  }
}
