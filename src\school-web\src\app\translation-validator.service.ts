import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class TranslationValidatorService {
  // Define the translation modules to check
  private readonly translationModules = [
    'core',
    'about',
    'academics',
    'admissions',
    'campus-life',
    'events',
    'careers',
    'contact',
    'news',
    'login',
    'register',
    'reset-password',
    'terms-and-conditions',
    'privacy-policy',
    'faq',
    'alumni',
    'faculty',
    'student-portal',
    'parent-portal',
    'home'
  ];

  constructor(private http: HttpClient) {}

  /**
   * Validates translation files for missing keys
   * @param targetLang Target language to validate against English (default: 'bn')
   */
  validateTranslations(targetLang: string = 'bn'): Observable<any> {
    console.log(`Validating translation files for ${targetLang}...`);

    // Load all English and target language translation files
    const enRequests = this.translationModules.map(module =>
      this.http.get(`./assets/i18n/en/${module}.json`).pipe(
        catchError(error => {
          console.warn(`Could not load English module '${module}':`, error.message);
          return of({});
        })
      )
    );

    const targetRequests = this.translationModules.map(module =>
      this.http.get(`./assets/i18n/${targetLang}/${module}.json`).pipe(
        catchError(error => {
          console.warn(`Could not load ${targetLang} module '${module}':`, error.message);
          return of({});
        })
      )
    );

    // Combine all requests
    return forkJoin([...enRequests, ...targetRequests]).pipe(
      map(results => {
        const enResults = results.slice(0, this.translationModules.length);
        const targetResults = results.slice(this.translationModules.length);

        const validationResults: any = {};

        // Check each module
        this.translationModules.forEach((module, index) => {
          const enModule = enResults[index];
          const targetModule = targetResults[index];

          if (Object.keys(enModule).length === 0) {
            console.warn(`English module '${module}' is empty or not found`);
            validationResults[module] = {
              status: 'source_missing',
              message: `English module '${module}' is empty or not found`
            };
            return;
          }

          if (Object.keys(targetModule).length === 0) {
            console.warn(`${targetLang} module '${module}' is empty or not found`);
            validationResults[module] = {
              status: 'missing',
              message: `${targetLang} module '${module}' is empty or not found`
            };
            return;
          }

          // Find missing keys in target language translation
          const missingKeys = this.findMissingKeys(enModule, targetModule);

          if (missingKeys.length > 0) {
            console.warn(`Module '${module}' has ${missingKeys.length} missing keys in ${targetLang} translation`);
            validationResults[module] = {
              status: 'incomplete',
              missingKeys,
              count: missingKeys.length
            };
          } else {
            console.log(`\u2713 Module '${module}' is complete in both languages`);
            validationResults[module] = {
              status: 'complete'
            };
          }
        });

        return validationResults;
      })
    );
  }

  /**
   * Generate a template for missing translations
   * @param module Module name to generate template for
   * @param targetLang Target language (default: 'bn')
   */
  generateTranslationTemplate(module: string, targetLang: string = 'bn'): Observable<any> {
    if (!this.translationModules.includes(module)) {
      return of({
        error: `Invalid module name: ${module}. Available modules: ${this.translationModules.join(', ')}`
      });
    }

    // Load the English module as source
    return this.http.get(`./assets/i18n/en/${module}.json`).pipe(
      map(enModule => {
        // Try to load the target language module if it exists
        return this.http.get(`./assets/i18n/${targetLang}/${module}.json`).pipe(
          map(targetModule => {
            // Find missing keys
            const missingKeys = this.findMissingKeys(enModule, targetModule);

            if (missingKeys.length === 0) {
              return {
                status: 'complete',
                message: `No missing keys found in module '${module}' for ${targetLang}`
              };
            }

            // Generate template with missing keys
            const template = this.createTemplateFromMissingKeys(enModule, targetModule, missingKeys);

            return {
              status: 'incomplete',
              missingKeys,
              count: missingKeys.length,
              template
            };
          }),
          catchError(() => {
            // If target module doesn't exist, create a complete template
            return of({
              status: 'missing',
              message: `${targetLang} module '${module}' not found, generating complete template`,
              template: this.createCompleteTemplate(enModule)
            });
          })
        );
      }),
      catchError(error => {
        return of({
          status: 'error',
          error: `Could not load English module '${module}': ${error.message}`
        });
      }),
      // Flatten the nested observable
      map(result => result)
    );
  }

  /**
   * Recursively finds missing keys in target object compared to source object
   */
  private findMissingKeys(source: any, target: any, prefix = ''): string[] {
    const missingKeys: string[] = [];

    Object.keys(source).forEach(key => {
      const currentKey = prefix ? `${prefix}.${key}` : key;

      if (!(key in target)) {
        missingKeys.push(currentKey);
      } else if (
        typeof source[key] === 'object' &&
        source[key] !== null &&
        !Array.isArray(source[key]) &&
        typeof target[key] === 'object' &&
        target[key] !== null &&
        !Array.isArray(target[key])
      ) {
        // Recursively check nested objects
        const nestedMissingKeys = this.findMissingKeys(source[key], target[key], currentKey);
        missingKeys.push(...nestedMissingKeys);
      }
    });

    return missingKeys;
  }

  /**
   * Creates a template object with missing keys from the source
   */
  private createTemplateFromMissingKeys(source: any, _target: any, missingKeys: string[]): any {
    const template: any = {};

    missingKeys.forEach(key => {
      const parts = key.split('.');
      let current = template;
      let sourceCurrent = source;

      // Navigate to the correct nesting level
      for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];
        sourceCurrent = sourceCurrent[part];

        if (!current[part]) {
          current[part] = {};
        }
        current = current[part];
      }

      // Add the missing key with empty string or placeholder
      const lastPart = parts[parts.length - 1];
      const sourceValue = sourceCurrent[lastPart];

      // Use empty string for simple values, or recursively copy objects
      if (typeof sourceValue === 'object' && sourceValue !== null && !Array.isArray(sourceValue)) {
        current[lastPart] = this.createEmptyTemplate(sourceValue);
      } else {
        // Use the English value as a placeholder
        current[lastPart] = sourceValue;
      }
    });

    return template;
  }

  /**
   * Creates a complete template from the source object
   */
  private createCompleteTemplate(source: any): any {
    if (typeof source !== 'object' || source === null || Array.isArray(source)) {
      return source;
    }

    const result: any = {};

    Object.keys(source).forEach(key => {
      const value = source[key];

      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        result[key] = this.createCompleteTemplate(value);
      } else {
        result[key] = value;
      }
    });

    return result;
  }

  /**
   * Creates an empty template with the same structure as the source
   */
  private createEmptyTemplate(source: any): any {
    if (typeof source !== 'object' || source === null || Array.isArray(source)) {
      return '';
    }

    const result: any = {};

    Object.keys(source).forEach(key => {
      const value = source[key];

      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        result[key] = this.createEmptyTemplate(value);
      } else {
        result[key] = value;
      }
    });

    return result;
  }
}
