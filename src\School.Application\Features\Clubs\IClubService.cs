using School.Application.Common.Models;
using School.Application.DTOs;
using System.Threading;
using System.Threading.Tasks;

namespace School.Application.Features.Clubs;

public interface IClubService
{
    Task<ApiResult<PagedList<ClubDto>>> GetClubsAsync(ClubFilterDto filter, CancellationToken cancellationToken = default);
    Task<ApiResult<ClubDto>> GetClubByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<ApiResult<ClubDto>> CreateClubAsync(ClubCreateDto clubDto, CancellationToken cancellationToken = default);
    Task<ApiResult<ClubDto>> UpdateClubAsync(Guid id, ClubUpdateDto clubDto, CancellationToken cancellationToken = default);
    Task<ApiResult<bool>> DeleteClubAsync(Guid id, CancellationToken cancellationToken = default);
    Task<ApiResult<PagedList<ClubDto>>> GetFeaturedClubsAsync(int page = 1, int pageSize = 10, CancellationToken cancellationToken = default);
    Task<ApiResult<List<string>>> GetClubCategoriesAsync(CancellationToken cancellationToken = default);
}
