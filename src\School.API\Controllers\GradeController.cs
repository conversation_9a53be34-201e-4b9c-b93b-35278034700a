using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.Application.DTOs;
using School.Application.Features.Grade;
using School.Domain.Enums;

namespace School.API.Controllers;

/// <summary>
/// API controller for Grade management operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class GradeController : ControllerBase
{
    private readonly IGradeService _gradeService;
    private readonly ILogger<GradeController> _logger;

    public GradeController(IGradeService gradeService, ILogger<GradeController> logger)
    {
        _gradeService = gradeService;
        _logger = logger;
    }

    /// <summary>
    /// Get all grades with filtering and pagination
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<object>> GetGrades([FromQuery] GradeFilterDto filter)
    {
        try
        {
            var (grades, totalCount) = await _gradeService.GetAllGradesAsync(filter);
            
            return Ok(new
            {
                data = grades,
                totalCount,
                page = filter.Page,
                pageSize = filter.PageSize,
                totalPages = (int)Math.Ceiling((double)totalCount / filter.PageSize)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving grades");
            return StatusCode(500, "An error occurred while retrieving grades");
        }
    }

    /// <summary>
    /// Get grade by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<GradeDto>> GetGrade(Guid id)
    {
        try
        {
            var grade = await _gradeService.GetGradeByIdAsync(id);
            
            if (grade == null)
            {
                return NotFound($"Grade with ID {id} not found");
            }

            return Ok(grade);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving grade {GradeId}", id);
            return StatusCode(500, "An error occurred while retrieving the grade");
        }
    }

    /// <summary>
    /// Get grades by academic year
    /// </summary>
    [HttpGet("academic-year/{academicYearId}")]
    public async Task<ActionResult<IEnumerable<GradeDto>>> GetGradesByAcademicYear(Guid academicYearId)
    {
        try
        {
            var grades = await _gradeService.GetGradesByAcademicYearAsync(academicYearId);
            return Ok(grades);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving grades for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while retrieving grades");
        }
    }

    /// <summary>
    /// Get active grades by academic year
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/active")]
    public async Task<ActionResult<IEnumerable<GradeDto>>> GetActiveGrades(Guid academicYearId)
    {
        try
        {
            var grades = await _gradeService.GetActiveGradesAsync(academicYearId);
            return Ok(grades);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active grades for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while retrieving active grades");
        }
    }

    /// <summary>
    /// Create a new grade
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<object>> CreateGrade([FromBody] CreateGradeDto gradeDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Validate grade code uniqueness
            var isCodeValid = await _gradeService.ValidateGradeCodeAsync(gradeDto.Code, gradeDto.AcademicYearId);
            if (!isCodeValid)
            {
                return BadRequest($"Grade code '{gradeDto.Code}' already exists for this academic year");
            }

            // Validate grade level uniqueness
            var isLevelValid = await _gradeService.ValidateGradeLevelAsync(gradeDto.Level, gradeDto.AcademicYearId);
            if (!isLevelValid)
            {
                return BadRequest($"Grade level '{gradeDto.Level}' already exists for this academic year");
            }

            var gradeId = await _gradeService.CreateGradeAsync(gradeDto);
            
            return CreatedAtAction(nameof(GetGrade), new { id = gradeId }, new { id = gradeId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating grade");
            return StatusCode(500, "An error occurred while creating the grade");
        }
    }

    /// <summary>
    /// Update an existing grade
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult> UpdateGrade(Guid id, [FromBody] UpdateGradeDto gradeDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Validate grade code uniqueness (excluding current grade)
            var isCodeValid = await _gradeService.ValidateGradeCodeAsync(gradeDto.Code, gradeDto.AcademicYearId, id);
            if (!isCodeValid)
            {
                return BadRequest($"Grade code '{gradeDto.Code}' already exists for this academic year");
            }

            // Validate grade level uniqueness (excluding current grade)
            var isLevelValid = await _gradeService.ValidateGradeLevelAsync(gradeDto.Level, gradeDto.AcademicYearId, id);
            if (!isLevelValid)
            {
                return BadRequest($"Grade level '{gradeDto.Level}' already exists for this academic year");
            }

            var success = await _gradeService.UpdateGradeAsync(id, gradeDto);
            
            if (!success)
            {
                return NotFound($"Grade with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating grade {GradeId}", id);
            return StatusCode(500, "An error occurred while updating the grade");
        }
    }

    /// <summary>
    /// Delete a grade
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteGrade(Guid id)
    {
        try
        {
            // Check if grade can be deleted
            var canDelete = await _gradeService.CanDeleteGradeAsync(id);
            if (!canDelete)
            {
                return BadRequest("Cannot delete grade that has associated sections or students");
            }

            var success = await _gradeService.DeleteGradeAsync(id);
            
            if (!success)
            {
                return NotFound($"Grade with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting grade {GradeId}", id);
            return StatusCode(500, "An error occurred while deleting the grade");
        }
    }

    /// <summary>
    /// Activate a grade
    /// </summary>
    [HttpPatch("{id}/activate")]
    public async Task<ActionResult> ActivateGrade(Guid id)
    {
        try
        {
            var success = await _gradeService.ActivateGradeAsync(id);
            
            if (!success)
            {
                return NotFound($"Grade with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating grade {GradeId}", id);
            return StatusCode(500, "An error occurred while activating the grade");
        }
    }

    /// <summary>
    /// Deactivate a grade
    /// </summary>
    [HttpPatch("{id}/deactivate")]
    public async Task<ActionResult> DeactivateGrade(Guid id)
    {
        try
        {
            var success = await _gradeService.DeactivateGradeAsync(id);
            
            if (!success)
            {
                return NotFound($"Grade with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating grade {GradeId}", id);
            return StatusCode(500, "An error occurred while deactivating the grade");
        }
    }

    /// <summary>
    /// Get grade statistics
    /// </summary>
    [HttpGet("{id}/statistics")]
    public async Task<ActionResult<GradeStatisticsDto>> GetGradeStatistics(Guid id)
    {
        try
        {
            var statistics = await _gradeService.GetGradeStatisticsAsync(id);
            return Ok(statistics);
        }
        catch (ArgumentException)
        {
            return NotFound($"Grade with ID {id} not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving grade statistics for {GradeId}", id);
            return StatusCode(500, "An error occurred while retrieving grade statistics");
        }
    }

    /// <summary>
    /// Get all grade statistics for an academic year
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/statistics")]
    public async Task<ActionResult<IEnumerable<GradeStatisticsDto>>> GetAllGradeStatistics(Guid academicYearId)
    {
        try
        {
            var statistics = await _gradeService.GetAllGradeStatisticsAsync(academicYearId);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving grade statistics for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while retrieving grade statistics");
        }
    }

    /// <summary>
    /// Update grade display order
    /// </summary>
    [HttpPatch("{id}/display-order")]
    public async Task<ActionResult> UpdateDisplayOrder(Guid id, [FromBody] int newOrder)
    {
        try
        {
            var success = await _gradeService.UpdateDisplayOrderAsync(id, newOrder);

            if (!success)
            {
                return NotFound($"Grade with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating display order for grade {GradeId}", id);
            return StatusCode(500, "An error occurred while updating the display order");
        }
    }

    /// <summary>
    /// Get grades by education level
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/education-level/{educationLevel}")]
    public async Task<ActionResult<IEnumerable<GradeDto>>> GetGradesByEducationLevel(
        Guid academicYearId,
        EducationLevel educationLevel)
    {
        try
        {
            var grades = await _gradeService.GetGradesByEducationLevelAsync(academicYearId, educationLevel);
            return Ok(grades);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving grades by education level {EducationLevel} for academic year {AcademicYearId}",
                educationLevel, academicYearId);
            return StatusCode(500, "An error occurred while retrieving grades");
        }
    }

    /// <summary>
    /// Get grade sections
    /// </summary>
    [HttpGet("{id}/sections")]
    public async Task<ActionResult<IEnumerable<SectionDto>>> GetGradeSections(Guid id)
    {
        try
        {
            var sections = await _gradeService.GetGradeSectionsAsync(id);
            return Ok(sections);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving sections for grade {GradeId}", id);
            return StatusCode(500, "An error occurred while retrieving grade sections");
        }
    }

    /// <summary>
    /// Get grade students
    /// </summary>
    [HttpGet("{id}/students")]
    public async Task<ActionResult<IEnumerable<StudentDto>>> GetGradeStudents(Guid id)
    {
        try
        {
            var students = await _gradeService.GetGradeStudentsAsync(id);
            return Ok(students);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving students for grade {GradeId}", id);
            return StatusCode(500, "An error occurred while retrieving grade students");
        }
    }

    /// <summary>
    /// Get available capacity for a grade
    /// </summary>
    [HttpGet("{id}/available-capacity")]
    public async Task<ActionResult<int>> GetAvailableCapacity(Guid id)
    {
        try
        {
            var capacity = await _gradeService.GetAvailableCapacityAsync(id);
            return Ok(capacity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving available capacity for grade {GradeId}", id);
            return StatusCode(500, "An error occurred while retrieving available capacity");
        }
    }

    /// <summary>
    /// Bulk update grade status
    /// </summary>
    [HttpPatch("bulk/status")]
    public async Task<ActionResult> BulkUpdateStatus([FromBody] BulkGradeStatusUpdateDto request)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var success = await _gradeService.BulkUpdateGradeStatusAsync(request.GradeIds, request.IsActive);
            if (!success)
                return BadRequest("Failed to update grade status");

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk updating grade status");
            return StatusCode(500, "An error occurred while updating grade status");
        }
    }

    /// <summary>
    /// Export grades to CSV
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/export")]
    public async Task<ActionResult> ExportGrades(Guid academicYearId)
    {
        try
        {
            var csvStream = await _gradeService.ExportGradesToCsvAsync(academicYearId);
            return File(csvStream, "text/csv", $"grades_{academicYearId}_{DateTime.Now:yyyyMMdd}.csv");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting grades for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while exporting grades");
        }
    }
}
