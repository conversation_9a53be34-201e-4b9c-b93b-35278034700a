import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { ApiResponseHandlerService } from './api-response-handler.service';
import { 
  Club, 
  ClubFilter, 
  CreateClub, 
  UpdateClub 
} from '../models/club.model';
import { PagedResult } from '../models/paged-result.model';
import { ApiResponse } from '../models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class ClubService {
  private apiUrl = `${environment.apiUrl}/clubs`;

  constructor(
    private http: HttpClient,
    private apiResponseHandler: ApiResponseHandlerService
  ) { }

  /**
   * Get all clubs with filtering and pagination
   */
  getClubs(filter: ClubFilter = {}): Observable<PagedResult<Club>> {
    let params = new HttpParams();
    
    if (filter.name) params = params.set('name', filter.name);
    if (filter.category) params = params.set('category', filter.category);
    if (filter.isFeatured !== undefined) params = params.set('isFeatured', filter.isFeatured.toString());
    if (filter.isActive !== undefined) params = params.set('isActive', filter.isActive.toString());
    if (filter.page) params = params.set('page', filter.page.toString());
    if (filter.pageSize) params = params.set('pageSize', filter.pageSize.toString());
    if (filter.sortBy) params = params.set('sortBy', filter.sortBy);
    if (filter.sortDirection) params = params.set('sortDirection', filter.sortDirection);
    
    return this.http.get<ApiResponse<PagedResult<Club>>>(this.apiUrl, { params })
      .pipe(
        map(response => this.apiResponseHandler.handleResponse(response))
      );
  }

  /**
   * Get a club by ID
   */
  getClub(id: number): Observable<Club> {
    return this.http.get<ApiResponse<Club>>(`${this.apiUrl}/${id}`)
      .pipe(
        map(response => this.apiResponseHandler.handleResponse(response))
      );
  }

  /**
   * Create a new club
   */
  createClub(club: CreateClub): Observable<Club> {
    return this.http.post<ApiResponse<Club>>(this.apiUrl, club)
      .pipe(
        map(response => this.apiResponseHandler.handleResponse(response))
      );
  }

  /**
   * Update an existing club
   */
  updateClub(id: number, club: UpdateClub): Observable<Club> {
    return this.http.put<ApiResponse<Club>>(`${this.apiUrl}/${id}`, club)
      .pipe(
        map(response => this.apiResponseHandler.handleResponse(response))
      );
  }

  /**
   * Delete a club
   */
  deleteClub(id: number): Observable<void> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`)
      .pipe(
        map(response => this.apiResponseHandler.handleResponse(response))
      );
  }

  /**
   * Get featured clubs
   */
  getFeaturedClubs(page: number = 1, pageSize: number = 10): Observable<PagedResult<Club>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());
    
    return this.http.get<ApiResponse<PagedResult<Club>>>(`${this.apiUrl}/featured`, { params })
      .pipe(
        map(response => this.apiResponseHandler.handleResponse(response))
      );
  }

  /**
   * Get all club categories
   */
  getClubCategories(): Observable<string[]> {
    return this.http.get<ApiResponse<string[]>>(`${this.apiUrl}/categories`)
      .pipe(
        map(response => this.apiResponseHandler.handleResponse(response))
      );
  }
}
