using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using School.Application.Common.Interfaces;
using School.Domain.Common;
using School.Infrastructure.Identity;

namespace School.Infrastructure.Persistence.Interceptors;

public class AuditableEntitySaveChangesInterceptor : SaveChangesInterceptor
{
    private readonly ICurrentUserService _currentUserService;
    private readonly IDateTime _dateTime;

    public AuditableEntitySaveChangesInterceptor(
        ICurrentUserService currentUserService,
        IDateTime dateTime)
    {
        _currentUserService = currentUserService;
        _dateTime = dateTime;
    }

    public override InterceptionResult<int> SavingChanges(DbContextEventData eventData, InterceptionResult<int> result)
    {
        UpdateEntities(eventData.Context);
        return base.SavingChanges(eventData, result);
    }

    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(DbContextEventData eventData, InterceptionResult<int> result, CancellationToken cancellationToken = default)
    {
        UpdateEntities(eventData.Context);
        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    private void UpdateEntities(DbContext? context)
    {
        if (context == null) return;

        string? userId = _currentUserService.UserId?.ToString();

        foreach (var entry in context.ChangeTracker.Entries<BaseEntity>())
        {
            // Only set audit fields if they haven't been set already (e.g., by Repository methods)
            if (entry.State == EntityState.Added)
            {
                if (entry.Entity.CreatedBy == null)
                {
                    entry.Entity.CreatedAt = _dateTime.Now;
                    entry.Entity.CreatedBy = userId;
                }
            }
            else if (entry.State == EntityState.Modified || entry.HasChangedOwnedEntities())
            {
                if (entry.Entity.LastModifiedBy == null)
                {
                    entry.Entity.LastModifiedAt = _dateTime.Now;
                    entry.Entity.LastModifiedBy = userId;
                }
            }
            else if (entry.State == EntityState.Deleted)
            {
                // Implement soft delete
                entry.State = EntityState.Modified;
                entry.Entity.IsDeleted = true;
                entry.Entity.LastModifiedAt = _dateTime.Now;
                entry.Entity.LastModifiedBy = userId;
            }
        }

        // Handle ApplicationUser entities
        foreach (var entry in context.ChangeTracker.Entries<ApplicationUser>())
        {
            if (entry.State == EntityState.Added)
            {
                if (entry.Entity.CreatedBy == null)
                {
                    entry.Entity.CreatedAt = _dateTime.Now;
                    entry.Entity.CreatedBy = userId;
                }
            }
            else if (entry.State == EntityState.Modified)
            {
                if (entry.Entity.LastModifiedBy == null)
                {
                    entry.Entity.LastModifiedAt = _dateTime.Now;
                    entry.Entity.LastModifiedBy = userId;
                }
            }
            else if (entry.State == EntityState.Deleted)
            {
                // Implement soft delete
                entry.State = EntityState.Modified;
                entry.Entity.IsDeleted = true;
                entry.Entity.LastModifiedAt = _dateTime.Now;
                entry.Entity.LastModifiedBy = userId;
            }
        }
    }
}

public static class Extensions
{
    public static bool HasChangedOwnedEntities(this EntityEntry entry) =>
        entry.References.Any(r =>
            r.TargetEntry != null &&
            r.TargetEntry.Metadata.IsOwned() &&
            (r.TargetEntry.State == EntityState.Added || r.TargetEntry.State == EntityState.Modified));
}
