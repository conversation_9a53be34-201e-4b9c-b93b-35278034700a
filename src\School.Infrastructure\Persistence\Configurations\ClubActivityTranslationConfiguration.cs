using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ClubActivityTranslationConfiguration : IEntityTypeConfiguration<ClubActivityTranslation>
{
    public void Configure(EntityTypeBuilder<ClubActivityTranslation> builder)
    {
        builder.HasKey(t => t.Id);
        
        builder.Property(t => t.LanguageCode)
            .IsRequired()
            .HasMaxLength(5);
            
        builder.Property(t => t.Description)
            .IsRequired();
            
        // Create a unique constraint for ClubActivityId and LanguageCode
        builder.HasIndex(t => new { t.ClubActivityId, t.LanguageCode })
            .IsUnique();
    }
}
