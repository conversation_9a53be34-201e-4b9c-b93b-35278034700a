using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.DTOs;
using School.Infrastructure.Services;

namespace School.API.Endpoints;

public class HolidayEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/holidays").WithTags("Holidays");

        // Get all holidays with filtering and pagination
        group.MapGet("/", async ([AsParameters] HolidayFilterDto filter, [FromServices] IHolidayService holidayService) =>
        {
            var holidays = await holidayService.GetHolidaysAsync(filter);
            return ApiResults.ApiOk(holidays, "Holidays retrieved successfully");
        }).WithName("GetAllHolidays").WithOpenApi();

        // Get holiday by ID
        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IHolidayService holidayService) =>
        {
            var holiday = await holidayService.GetHolidayByIdAsync(id);
            if (holiday == null)
            {
                return ApiResults.ApiNotFound("Holiday not found");
            }
            return ApiResults.ApiOk(holiday, "Holiday retrieved successfully");
        }).WithName("GetHolidayById").WithOpenApi();

        // Get holidays by academic year
        group.MapGet("/academic-year/{academicYearId}", async ([FromRoute] Guid academicYearId, [FromServices] IHolidayService holidayService) =>
        {
            var holidays = await holidayService.GetHolidaysByAcademicYearAsync(academicYearId);
            return ApiResults.ApiOk(holidays, "Holidays retrieved successfully");
        }).WithName("GetHolidaysByAcademicYear").WithOpenApi();

        // Get holidays by term
        group.MapGet("/term/{termId}", async ([FromRoute] Guid termId, [FromServices] IHolidayService holidayService) =>
        {
            var holidays = await holidayService.GetHolidaysByTermAsync(termId);
            return ApiResults.ApiOk(holidays, "Holidays retrieved successfully");
        }).WithName("GetHolidaysByTerm").WithOpenApi();

        // Get holidays by date range
        group.MapGet("/date-range", async ([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromServices] IHolidayService holidayService) =>
        {
            var holidays = await holidayService.GetHolidaysByDateRangeAsync(startDate, endDate);
            return ApiResults.ApiOk(holidays, "Holidays retrieved successfully");
        }).WithName("GetHolidaysByDateRange").WithOpenApi();

        // Get holiday events for calendar
        group.MapGet("/events", async ([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, 
            [FromQuery] Guid? academicYearId, [FromQuery] Guid? termId, [FromServices] IHolidayService holidayService) =>
        {
            var events = await holidayService.GetHolidayEventsAsync(startDate, endDate, academicYearId, termId);
            return ApiResults.ApiOk(events, "Holiday events retrieved successfully");
        }).WithName("GetHolidayEvents").WithOpenApi();

        // Get overlapping holidays
        group.MapGet("/overlapping", async ([FromQuery] Guid? academicYearId, [FromQuery] Guid? termId,
            [FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] Guid? excludeId,
            [FromServices] IHolidayService holidayService) =>
        {
            var holidays = await holidayService.GetOverlappingHolidaysAsync(academicYearId, termId, startDate, endDate, excludeId);
            return ApiResults.ApiOk(holidays, "Overlapping holidays retrieved successfully");
        }).WithName("GetOverlappingHolidays").WithOpenApi();

        // Get holiday statistics
        group.MapGet("/statistics", async ([FromQuery] Guid? academicYearId, [FromQuery] Guid? termId, [FromServices] IHolidayService holidayService) =>
        {
            var statistics = await holidayService.GetHolidayStatisticsAsync(academicYearId, termId);
            return ApiResults.ApiOk(statistics, "Holiday statistics retrieved successfully");
        }).WithName("GetHolidayStatistics").WithOpenApi();

        // Get total holidays in period
        group.MapGet("/count", async ([FromQuery] DateTime startDate, [FromQuery] DateTime endDate,
            [FromQuery] Guid? academicYearId, [FromQuery] Guid? termId, [FromServices] IHolidayService holidayService) =>
        {
            var count = await holidayService.GetTotalHolidaysInPeriodAsync(startDate, endDate, academicYearId, termId);
            return ApiResults.ApiOk(new { Count = count }, "Holiday count retrieved successfully");
        }).WithName("GetHolidayCount").WithOpenApi();

        // Create new holiday
        group.MapPost("/", async ([FromBody] CreateHolidayDto holidayDto, [FromServices] IHolidayService holidayService) =>
        {
            try
            {
                var holidayId = await holidayService.CreateHolidayAsync(holidayDto);
                return ApiResults.ApiCreated($"/api/holidays/{holidayId}", holidayId.ToString(), "Holiday created successfully");
            }
            catch (InvalidOperationException ex)
            {
                return ApiResults.ApiBadRequest(ex.Message);
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("CreateHoliday")
        .WithOpenApi();

        // Update holiday
        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateHolidayDto holidayDto, [FromServices] IHolidayService holidayService) =>
        {
            try
            {
                var success = await holidayService.UpdateHolidayAsync(id, holidayDto);
                if (!success)
                {
                    return ApiResults.ApiNotFound("Holiday not found");
                }
                return ApiResults.ApiOk("Holiday updated successfully");
            }
            catch (InvalidOperationException ex)
            {
                return ApiResults.ApiBadRequest(ex.Message);
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateHoliday")
        .WithOpenApi();

        // Delete holiday
        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IHolidayService holidayService) =>
        {
            var success = await holidayService.DeleteHolidayAsync(id);
            if (!success)
            {
                return ApiResults.ApiNotFound("Holiday not found");
            }
            return ApiResults.ApiOk("Holiday deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteHoliday")
        .WithOpenApi();

        // Activate holiday
        group.MapPatch("/{id}/activate", async ([FromRoute] Guid id, [FromServices] IHolidayService holidayService) =>
        {
            var success = await holidayService.ActivateHolidayAsync(id);
            if (!success)
            {
                return ApiResults.ApiNotFound("Holiday not found");
            }
            return ApiResults.ApiOk("Holiday activated successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("ActivateHoliday")
        .WithOpenApi();

        // Deactivate holiday
        group.MapPatch("/{id}/deactivate", async ([FromRoute] Guid id, [FromServices] IHolidayService holidayService) =>
        {
            var success = await holidayService.DeactivateHolidayAsync(id);
            if (!success)
            {
                return ApiResults.ApiNotFound("Holiday not found");
            }
            return ApiResults.ApiOk("Holiday deactivated successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("DeactivateHoliday")
        .WithOpenApi();

        // Validate holiday dates
        group.MapPost("/validate", async ([FromBody] ValidateHolidayDatesRequest request, [FromServices] IHolidayService holidayService) =>
        {
            var isValid = await holidayService.ValidateHolidayDatesAsync(request.AcademicYearId, request.TermId, 
                request.StartDate, request.EndDate, request.ExcludeId);
            return ApiResults.ApiOk(new { IsValid = isValid }, "Holiday dates validated");
        }).WithName("ValidateHolidayDates").WithOpenApi();

        // Generate recurring holidays
        group.MapPost("/{id}/generate-recurring", async ([FromRoute] Guid id, [FromBody] GenerateRecurringHolidaysRequest request, 
            [FromServices] IHolidayService holidayService) =>
        {
            var holidays = await holidayService.GenerateRecurringHolidaysAsync(id, request.FromDate, request.ToDate);
            return ApiResults.ApiOk(holidays, "Recurring holidays generated successfully");
        }).WithName("GenerateRecurringHolidays").WithOpenApi();

        // Update recurring holiday series
        group.MapPut("/{id}/recurring-series", async ([FromRoute] Guid id, [FromBody] UpdateRecurringHolidayRequest request, 
            [FromServices] IHolidayService holidayService) =>
        {
            try
            {
                var success = await holidayService.UpdateRecurringHolidaySeriesAsync(id, request.HolidayDto, request.UpdateSeries);
                if (!success)
                {
                    return ApiResults.ApiNotFound("Holiday not found");
                }
                return ApiResults.ApiOk("Recurring holiday series updated successfully");
            }
            catch (InvalidOperationException ex)
            {
                return ApiResults.ApiBadRequest(ex.Message);
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateRecurringHolidaySeries")
        .WithOpenApi();

        // Translation management endpoints
        var translationGroup = group.MapGroup("/{id}/translations").WithTags("Holiday Translations");

        // Get all translations for a holiday
        translationGroup.MapGet("/", async ([FromRoute] Guid id, [FromServices] IHolidayService holidayService) =>
        {
            var translations = await holidayService.GetTranslationsAsync(id);
            return ApiResults.ApiOk(translations, "Holiday translations retrieved successfully");
        }).WithName("GetHolidayTranslations").WithOpenApi();

        // Add translation
        translationGroup.MapPost("/", async ([FromRoute] Guid id, [FromBody] CreateHolidayTranslationDto translationDto, 
            [FromServices] IHolidayService holidayService) =>
        {
            var success = await holidayService.AddTranslationAsync(id, translationDto);
            if (!success)
            {
                return ApiResults.ApiBadRequest("Translation already exists or holiday not found");
            }
            return ApiResults.ApiCreated($"/api/holidays/{id}/translations/{translationDto.LanguageCode}",
                translationDto.LanguageCode, "Holiday translation added successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("AddHolidayTranslation")
        .WithOpenApi();

        // Update translation
        translationGroup.MapPut("/{languageCode}", async ([FromRoute] Guid id, [FromRoute] string languageCode, 
            [FromBody] UpdateHolidayTranslationDto translationDto, [FromServices] IHolidayService holidayService) =>
        {
            var success = await holidayService.UpdateTranslationAsync(id, languageCode, translationDto);
            if (!success)
            {
                return ApiResults.ApiNotFound("Translation not found");
            }
            return ApiResults.ApiOk("Holiday translation updated successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateHolidayTranslation")
        .WithOpenApi();

        // Delete translation
        translationGroup.MapDelete("/{languageCode}", async ([FromRoute] Guid id, [FromRoute] string languageCode, 
            [FromServices] IHolidayService holidayService) =>
        {
            var success = await holidayService.DeleteTranslationAsync(id, languageCode);
            if (!success)
            {
                return ApiResults.ApiNotFound("Translation not found");
            }
            return ApiResults.ApiOk("Holiday translation deleted successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("DeleteHolidayTranslation")
        .WithOpenApi();
    }
}

// Request DTOs for specific endpoints
public record ValidateHolidayDatesRequest(Guid? AcademicYearId, Guid? TermId, DateTime StartDate, DateTime EndDate, Guid? ExcludeId);
public record GenerateRecurringHolidaysRequest(DateTime FromDate, DateTime ToDate);
public record UpdateRecurringHolidayRequest(UpdateHolidayDto HolidayDto, bool UpdateSeries);
