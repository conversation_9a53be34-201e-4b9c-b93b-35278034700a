<div class="language-selector">
  <button mat-button [matMenuTriggerFor]="languageMenu" class="language-button">
    <span class="flag">{{ getCurrentLanguage()?.flag }}</span>
    <span class="lang-name">{{ getCurrentLanguage()?.name }}</span>
    <mat-icon>arrow_drop_down</mat-icon>
  </button>
  <mat-menu #languageMenu="matMenu">
    <button mat-menu-item *ngFor="let lang of languages" (click)="changeLanguage(lang.code)" [disabled]="currentLang === lang.code">
      <span class="flag">{{ lang.flag }}</span>
      <span>{{ lang.name }}</span>
    </button>
  </mat-menu>
</div>
