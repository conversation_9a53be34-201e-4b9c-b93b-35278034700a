{"name": "school-web", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:dev": "ng serve --configuration=development", "build": "ng build", "build:dev": "ng build --configuration=development", "build:prod": "ng build --configuration=production", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.5", "@angular/cdk": "^19.2.8", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/localize": "^19.2.5", "@angular/material": "^19.2.8", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@ckeditor/ckeditor5-angular": "^9.1.0", "@ckeditor/ckeditor5-build-classic": "^44.3.0", "@fullcalendar/angular": "^6.1.17", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "lodash": "^4.17.21", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.6", "@angular/cli": "^19.2.6", "@angular/compiler-cli": "^19.2.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}