using System;

namespace School.Application.DTOs
{
    public class ClubLeaderDto
    {
        public Guid id { get; set; }
        public int ClubId { get; set; }
        public int? StudentId { get; set; }
        public string Name { get; set; }
        public string Role { get; set; }
        public string Grade { get; set; }
        public int? ProfileImageId { get; set; }
        public string ProfileImageUrl { get; set; }
        public int DisplayOrder { get; set; }
        
        // Related student (if available)
        public StudentDto Student { get; set; }
    }
}
