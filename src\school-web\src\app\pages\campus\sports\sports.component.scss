@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section styles are now handled by the DefaultHeroComponent

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Section Styles
section {
  padding: $section-padding;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: $dark-gray;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Introduction Section
.intro-section {
  background-color: $white;

  .intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Philosophy Section
.philosophy-section {
  background-color: $light-gray;

  .philosophy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .philosophy-item {
      background-color: $white;
      border-radius: $border-radius;
      padding: 30px;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        color: $text-color;
        padding-bottom: 10px;
        border-bottom: 2px solid $primary-color;
      }

      p {
        color: $dark-gray;
        line-height: 1.6;
        margin-bottom: 0;
      }
    }
  }
}

// Sports Section
.sports-section {
  background-color: $white;

  ::ng-deep .mat-mdc-tab-header {
    margin-bottom: 20px;
  }

  .level-content {
    padding: 20px 0;
  }

  .sports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    padding: 20px;

    .sport-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .sport-image {
        height: 200px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 10px;
          color: $text-color;
        }

        .sport-description {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 20px;
          flex-grow: 1;
        }

        .sport-details {
          .detail-item {
            display: flex;
            align-items: center;

            mat-icon {
              color: $primary-color;
              margin-right: 10px;
              font-size: 20px;
              height: 20px;
              width: 20px;
            }

            span {
              color: $dark-gray;
            }
          }
        }
      }
    }
  }
}

// Facilities Section
.facilities-section {
  background-color: $light-gray;

  .facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .facility-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .facility-image {
        height: 200px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 15px;
          color: $text-color;
        }

        .facility-features {
          padding-left: 20px;
          margin-bottom: 0;

          li {
            color: $dark-gray;
            line-height: 1.6;
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

// Achievements Section
.achievements-section {
  background-color: $white;

  .achievements-container {
    max-width: 800px;
    margin: 40px auto 0;

    .achievement-year {
      margin-bottom: 40px;

      &:last-child {
        margin-bottom: 0;
      }

      h3 {
        font-size: 1.8rem;
        margin-bottom: 20px;
        color: $text-color;
        text-align: center;
        position: relative;
        display: inline-block;
        padding: 0 20px 10px;

        &:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 3px;
          background-color: $primary-color;
        }
      }

      .achievement-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;

        .achievement-item {
          background-color: $light-gray;
          border-radius: $border-radius;
          padding: 20px;
          transition: transform 0.3s, box-shadow 0.3s;

          &:hover {
            transform: translateY(-3px);
            box-shadow: $box-shadow;
          }

          .achievement-sport {
            font-weight: 500;
            color: $primary-color;
            margin-bottom: 5px;
          }

          .achievement-title {
            color: $text-color;
            font-size: 1.1rem;
          }
        }
      }
    }
  }
}

// Requirements Section
.requirements-section {
  background-color: $light-gray;

  .requirements-container {
    max-width: 800px;
    margin: 40px auto 30px;

    .requirement-item {
      display: flex;
      margin-bottom: 30px;
      background-color: $white;
      border-radius: $border-radius;
      padding: 20px;
      box-shadow: $box-shadow;
      transition: transform 0.3s;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        transform: translateY(-5px);
      }

      .requirement-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background-color: $primary-color;
        border-radius: 50%;
        margin-right: 20px;
        flex-shrink: 0;

        mat-icon {
          font-size: 30px;
          height: 30px;
          width: 30px;
          color: $white;
        }
      }

      .requirement-content {
        flex-grow: 1;

        h3 {
          font-size: 1.3rem;
          margin-bottom: 10px;
          color: $text-color;
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }

  .requirements-cta {
    text-align: center;
    margin-top: 40px;

    a {
      padding: 10px 30px;
      font-size: 1.1rem;
    }
  }
}

// Contact Section
.contact-section {
  background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -15%));
  color: $white;

  .contact-content {
    max-width: 800px;
    margin: 0 auto;

    h2 {
      color: $white;

      &:after {
        background-color: $white;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
      text-align: center;
    }

    .contact-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;

      .contact-item {
        display: flex;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: $border-radius;
        padding: 20px;

        mat-icon {
          font-size: 30px;
          height: 30px;
          width: 30px;
          margin-right: 20px;
          margin-top: 5px;
        }

        .contact-details {
          h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
          }

          p {
            font-size: 1rem;
            margin-bottom: 5px;
            text-align: left;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .hero-section {
    height: 350px;

    .hero-content h1 {
      font-size: 2.5rem;
    }
  }

  section {
    padding: 60px 0;

    h2 {
      font-size: 2rem;
    }
  }

  .philosophy-grid, .sports-grid, .facilities-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .achievement-list {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 768px) {
  // Hero section styles removed

  .requirement-item {
    flex-direction: column;

    .requirement-icon {
      margin-right: 0;
      margin-bottom: 15px;
    }
  }

  .contact-info {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  // Hero section styles removed

  section h2 {
    font-size: 1.8rem;
  }

  .philosophy-grid, .sports-grid, .facilities-grid {
    grid-template-columns: 1fr;
  }
}
