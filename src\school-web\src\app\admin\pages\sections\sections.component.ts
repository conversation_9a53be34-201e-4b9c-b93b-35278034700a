import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SectionService } from '../../../core/services/section.service';
import { GradeService } from '../../../core/services/grade.service';
import { AcademicYearService } from '../../../core/services/academic-year.service';
import { SectionCreateEditDialogComponent } from './section-create-edit-dialog.component';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';

export interface Section {
  id: string;
  name: string;
  code: string;
  gradeId: string;
  gradeName: string;
  type: string;
  medium: string;
  shift: string;
  capacity: number;
  currentEnrollment: number;
  isActive: boolean;
  displayOrder: number;
  academicYearId: string;
  academicYearName: string;
  classroom: string;
  roomNumber: string;
  description: string;
  requirements: string;
  remarks: string;
  createdAt: Date;
  lastModifiedAt?: Date;
}

@Component({
  selector: 'app-sections',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatSnackBarModule,
    MatCardModule,
    MatToolbarModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatChipsModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './sections.component.html',
  styleUrls: ['./sections.component.scss']
})
export class SectionsComponent implements OnInit {
  private dialog = inject(MatDialog);
  private snackBar = inject(MatSnackBar);
  private sectionService = inject(SectionService);
  private gradeService = inject(GradeService);
  private academicYearService = inject(AcademicYearService);

  displayedColumns: string[] = [
    'code',
    'name',
    'grade',
    'type',
    'medium',
    'capacity',
    'classroom',
    'academicYear',
    'isActive',
    'actions'
  ];

  sections: Section[] = [];
  grades: any[] = [];
  academicYears: any[] = [];
  loading = false;
  totalCount = 0;
  pageSize = 10;
  pageIndex = 0;
  searchTerm = '';
  selectedGrade = '';
  selectedAcademicYear = '';
  selectedType = '';
  selectedMedium = '';

  sectionTypes = [
    { value: 'Regular', label: 'Regular' },
    { value: 'Science', label: 'Science' },
    { value: 'Commerce', label: 'Commerce' },
    { value: 'Arts', label: 'Arts' },
    { value: 'Vocational', label: 'Vocational' }
  ];

  mediums = [
    { value: 'Bengali', label: 'Bengali' },
    { value: 'English', label: 'English' },
    { value: 'Hindi', label: 'Hindi' },
    { value: 'Urdu', label: 'Urdu' }
  ];

  ngOnInit() {
    this.loadSections();
    this.loadGrades();
    this.loadAcademicYears();
  }

  loadSections() {
    this.loading = true;
    const filter = {
      page: this.pageIndex + 1,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm,
      gradeId: this.selectedGrade || undefined,
      academicYearId: this.selectedAcademicYear || undefined,
      type: this.selectedType || undefined,
      medium: this.selectedMedium || undefined,
      isActive: undefined
    };

    this.sectionService.getSections(filter).subscribe({
      next: (response) => {
        this.sections = response.data;
        this.totalCount = response.totalCount;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading sections:', error);
        this.snackBar.open('Error loading sections', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  loadGrades() {
    this.gradeService.getActiveGrades('').subscribe({
      next: (grades) => {
        this.grades = grades;
      },
      error: (error) => {
        console.error('Error loading grades:', error);
      }
    });
  }

  loadAcademicYears() {
    this.academicYearService.getActiveAcademicYears().subscribe({
      next: (years) => {
        this.academicYears = years;
      },
      error: (error) => {
        console.error('Error loading academic years:', error);
      }
    });
  }

  onSearch() {
    this.pageIndex = 0;
    this.loadSections();
  }

  onPageChange(event: any) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadSections();
  }

  openCreateDialog() {
    const dialogRef = this.dialog.open(SectionCreateEditDialogComponent, {
      width: '700px',
      data: { mode: 'create' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadSections();
        this.snackBar.open('Section created successfully', 'Close', { duration: 3000 });
      }
    });
  }

  openEditDialog(section: Section) {
    const dialogRef = this.dialog.open(SectionCreateEditDialogComponent, {
      width: '700px',
      data: { mode: 'edit', section: { ...section } }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadSections();
        this.snackBar.open('Section updated successfully', 'Close', { duration: 3000 });
      }
    });
  }

  deleteSection(section: Section) {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Section',
        message: `Are you sure you want to delete the section "${section.name}"? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.sectionService.deleteSection(section.id).subscribe({
          next: () => {
            this.loadSections();
            this.snackBar.open('Section deleted successfully', 'Close', { duration: 3000 });
          },
          error: (error) => {
            console.error('Error deleting section:', error);
            this.snackBar.open('Error deleting section', 'Close', { duration: 3000 });
          }
        });
      }
    });
  }

  toggleSectionStatus(section: Section) {
    const newStatus = !section.isActive;
    this.sectionService.updateSectionStatus(section.id, newStatus).subscribe({
      next: () => {
        section.isActive = newStatus;
        this.snackBar.open(
          `Section ${newStatus ? 'activated' : 'deactivated'} successfully`, 
          'Close', 
          { duration: 3000 }
        );
      },
      error: (error) => {
        console.error('Error updating section status:', error);
        this.snackBar.open('Error updating section status', 'Close', { duration: 3000 });
      }
    });
  }

  getCapacityPercentage(section: Section): number {
    return section.capacity > 0 ? (section.currentEnrollment / section.capacity) * 100 : 0;
  }

  getCapacityColor(percentage: number): string {
    if (percentage >= 90) return 'warn';
    if (percentage >= 75) return 'accent';
    return 'primary';
  }

  getTypeDisplay(type: string): string {
    const typeObj = this.sectionTypes.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  }

  getMediumDisplay(medium: string): string {
    const mediumObj = this.mediums.find(m => m.value === medium);
    return mediumObj ? mediumObj.label : medium;
  }

  clearFilters() {
    this.searchTerm = '';
    this.selectedGrade = '';
    this.selectedAcademicYear = '';
    this.selectedType = '';
    this.selectedMedium = '';
    this.pageIndex = 0;
    this.loadSections();
  }
}
