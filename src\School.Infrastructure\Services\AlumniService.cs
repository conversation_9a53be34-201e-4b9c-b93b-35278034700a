using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Alumni;
using School.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Services
{
    public class AlumniService : IAlumniService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<AlumniService> _logger;

        public AlumniService(
            IUnitOfWork unitOfWork,
            ICurrentUserService currentUserService,
            ILogger<AlumniService> logger)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        #region Alumni Methods

        public async Task<(IEnumerable<AlumniDto> Alumni, int TotalCount)> GetAllAlumniAsync(AlumniFilterDto filter)
        {
            var repository = _unitOfWork.Repository<Alumni>();
            var query = repository.AsQueryable("Translations", "ProfileImage");

            // Apply filters
            if (!string.IsNullOrEmpty(filter.Name))
            {
                query = query.Where(a => a.Name.Contains(filter.Name));
            }

            if (filter.GraduationYear.HasValue)
            {
                query = query.Where(a => a.GraduationYear == filter.GraduationYear.Value);
            }

            if (!string.IsNullOrEmpty(filter.Profession))
            {
                query = query.Where(a => a.Profession.Contains(filter.Profession));
            }

            if (!string.IsNullOrEmpty(filter.Organization))
            {
                query = query.Where(a => a.Organization.Contains(filter.Organization));
            }

            if (filter.IsFeatured.HasValue)
            {
                query = query.Where(a => a.IsFeatured == filter.IsFeatured.Value);
            }

            if (filter.IsActive.HasValue)
            {
                query = query.Where(a => a.IsActive == filter.IsActive.Value);
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Get paginated results
            var alumni = await query
                .OrderByDescending(a => a.GraduationYear)
                .ThenBy(a => a.DisplayOrder)
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(a => new AlumniDto
                {
                    Id = a.Id,
                    Name = a.Name,
                    Email = a.Email,
                    Phone = a.Phone,
                    GraduationYear = a.GraduationYear,
                    Profession = a.Profession,
                    Organization = a.Organization,
                    Designation = a.Designation,
                    Biography = a.Biography,
                    Achievements = a.Achievements,
                    LinkedInProfile = a.LinkedInProfile,
                    FacebookProfile = a.FacebookProfile,
                    TwitterProfile = a.TwitterProfile,
                    IsFeatured = a.IsFeatured,
                    IsActive = a.IsActive,
                    DisplayOrder = a.DisplayOrder,
                    ProfileImageId = a.ProfileImageId,
                    ProfileImage = a.ProfileImage != null ? new MediaItemDto
                    {
                        Id = a.ProfileImage.Id,
                        FileName = a.ProfileImage.FileName,
                        FilePath = a.ProfileImage.FilePath,
                        MimeType = a.ProfileImage.MimeType
                    } : null,
                    CreatedAt = a.CreatedAt,
                    LastModifiedAt = a.LastModifiedAt,
                    Translations = a.Translations.Select(t => new AlumniTranslationDto
                    {
                        Id = t.Id,
                        AlumniId = t.AlumniId,
                        LanguageCode = t.LanguageCode,
                        Name = t.Name,
                        Profession = t.Profession,
                        Organization = t.Organization,
                        Designation = t.Designation,
                        Biography = t.Biography,
                        Achievements = t.Achievements
                    }).ToList()
                })
                .ToListAsync();

            return (alumni, totalCount);
        }

        public async Task<AlumniDto?> GetAlumniByIdAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<Alumni>();
            var alumni = await repository.GetByIdAsync(id, new[] { "Translations", "ProfileImage", "Testimonials", "Testimonials.Translations" });

            if (alumni == null) return null;

            return new AlumniDto
            {
                Id = alumni.Id,
                Name = alumni.Name,
                Email = alumni.Email,
                Phone = alumni.Phone,
                GraduationYear = alumni.GraduationYear,
                Profession = alumni.Profession,
                Organization = alumni.Organization,
                Designation = alumni.Designation,
                Biography = alumni.Biography,
                Achievements = alumni.Achievements,
                LinkedInProfile = alumni.LinkedInProfile,
                FacebookProfile = alumni.FacebookProfile,
                TwitterProfile = alumni.TwitterProfile,
                IsFeatured = alumni.IsFeatured,
                IsActive = alumni.IsActive,
                DisplayOrder = alumni.DisplayOrder,
                ProfileImageId = alumni.ProfileImageId,
                ProfileImage = alumni.ProfileImage != null ? new MediaItemDto
                {
                    Id = alumni.ProfileImage.Id,
                    FileName = alumni.ProfileImage.FileName,
                    FilePath = alumni.ProfileImage.FilePath,
                    MimeType = alumni.ProfileImage.MimeType
                } : null,
                CreatedAt = alumni.CreatedAt,
                LastModifiedAt = alumni.LastModifiedAt,
                Translations = alumni.Translations.Select(t => new AlumniTranslationDto
                {
                    Id = t.Id,
                    AlumniId = t.AlumniId,
                    LanguageCode = t.LanguageCode,
                    Name = t.Name,
                    Profession = t.Profession,
                    Organization = t.Organization,
                    Designation = t.Designation,
                    Biography = t.Biography,
                    Achievements = t.Achievements
                }).ToList(),
                Testimonials = alumni.Testimonials.Where(t => t.IsActive && !t.IsDeleted).Select(t => new AlumniTestimonialDto
                {
                    Id = t.Id,
                    AlumniId = t.AlumniId,
                    Content = t.Content,
                    IsApproved = t.IsApproved,
                    IsActive = t.IsActive,
                    DisplayOrder = t.DisplayOrder,
                    CreatedAt = t.CreatedAt,
                    LastModifiedAt = t.LastModifiedAt,
                    Translations = t.Translations.Select(tt => new AlumniTestimonialTranslationDto
                    {
                        Id = tt.Id,
                        AlumniTestimonialId = tt.AlumniTestimonialId,
                        LanguageCode = tt.LanguageCode,
                        Content = tt.Content
                    }).ToList()
                }).ToList()
            };
        }

        public async Task<Guid> CreateAlumniAsync(CreateAlumniDto alumniDto)
        {
            var repository = _unitOfWork.Repository<Alumni>();

            var alumni = new Alumni
            {
                Name = alumniDto.Name,
                Email = alumniDto.Email,
                Phone = alumniDto.Phone,
                GraduationYear = alumniDto.GraduationYear,
                Profession = alumniDto.Profession,
                Organization = alumniDto.Organization,
                Designation = alumniDto.Designation,
                Biography = alumniDto.Biography,
                Achievements = alumniDto.Achievements,
                LinkedInProfile = alumniDto.LinkedInProfile,
                FacebookProfile = alumniDto.FacebookProfile,
                TwitterProfile = alumniDto.TwitterProfile,
                IsFeatured = alumniDto.IsFeatured,
                IsActive = alumniDto.IsActive,
                DisplayOrder = alumniDto.DisplayOrder,
                ProfileImageId = alumniDto.ProfileImageId,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = _currentUserService.UserId?.ToString()
            };

            await repository.AddAsync(alumni);
            await _unitOfWork.SaveChangesAsync();

            // Add translations if provided
            if (alumniDto.Translations != null && alumniDto.Translations.Any())
            {
                var translationRepository = _unitOfWork.Repository<AlumniTranslation>();

                foreach (var translationDto in alumniDto.Translations)
                {
                    var translation = new AlumniTranslation
                    {
                        AlumniId = alumni.Id,
                        LanguageCode = translationDto.LanguageCode,
                        Name = translationDto.Name,
                        Profession = translationDto.Profession,
                        Organization = translationDto.Organization,
                        Designation = translationDto.Designation,
                        Biography = translationDto.Biography,
                        Achievements = translationDto.Achievements,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = _currentUserService.UserId?.ToString()
                    };

                    await translationRepository.AddAsync(translation);
                }

                await _unitOfWork.SaveChangesAsync();
            }

            _logger.LogInformation("Alumni created with ID {AlumniId}", alumni.Id);
            return alumni.Id;
        }

        public async Task<bool> UpdateAlumniAsync(Guid id, UpdateAlumniDto alumniDto)
        {
            var repository = _unitOfWork.Repository<Alumni>();
            var alumni = await repository.GetByIdAsync(id);

            if (alumni == null) return false;

            // Update properties
            alumni.Name = alumniDto.Name;
            alumni.Email = alumniDto.Email;
            alumni.Phone = alumniDto.Phone;
            alumni.GraduationYear = alumniDto.GraduationYear;
            alumni.Profession = alumniDto.Profession;
            alumni.Organization = alumniDto.Organization;
            alumni.Designation = alumniDto.Designation;
            alumni.Biography = alumniDto.Biography;
            alumni.Achievements = alumniDto.Achievements;
            alumni.LinkedInProfile = alumniDto.LinkedInProfile;
            alumni.FacebookProfile = alumniDto.FacebookProfile;
            alumni.TwitterProfile = alumniDto.TwitterProfile;
            alumni.IsFeatured = alumniDto.IsFeatured;
            alumni.IsActive = alumniDto.IsActive;
            alumni.DisplayOrder = alumniDto.DisplayOrder;
            alumni.ProfileImageId = alumniDto.ProfileImageId;
            // LastModifiedAt is set below
            alumni.LastModifiedBy = _currentUserService.UserId?.ToString();
            alumni.LastModifiedAt = DateTime.UtcNow;

            await repository.UpdateAsync(alumni);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Alumni updated with ID {AlumniId}", alumni.Id);
            return true;
        }

        public async Task<bool> DeleteAlumniAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<Alumni>();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Alumni deleted with ID {AlumniId}", id);
            return true;
        }

        #endregion

        #region Testimonial Methods

        public async Task<AlumniTestimonialDto?> GetTestimonialByIdAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<AlumniTestimonial>();
            var testimonial = await repository.GetByIdAsync(id, new[] { "Translations" });

            if (testimonial == null) return null;

            return new AlumniTestimonialDto
            {
                Id = testimonial.Id,
                AlumniId = testimonial.AlumniId,
                Content = testimonial.Content,
                IsApproved = testimonial.IsApproved,
                IsActive = testimonial.IsActive,
                DisplayOrder = testimonial.DisplayOrder,
                CreatedAt = testimonial.CreatedAt,
                LastModifiedAt = testimonial.LastModifiedAt,
                Translations = testimonial.Translations.Select(t => new AlumniTestimonialTranslationDto
                {
                    Id = t.Id,
                    AlumniTestimonialId = t.AlumniTestimonialId,
                    LanguageCode = t.LanguageCode,
                    Content = t.Content
                }).ToList()
            };
        }

        public async Task<Guid> CreateTestimonialAsync(CreateAlumniTestimonialDto testimonialDto)
        {
            // Verify alumni exists
            var alumniRepository = _unitOfWork.Repository<Alumni>();
            var alumni = await alumniRepository.GetByIdAsync(testimonialDto.AlumniId);

            if (alumni == null)
            {
                _logger.LogWarning("Testimonial creation failed: Alumni with ID {AlumniId} not found", testimonialDto.AlumniId);
                throw new InvalidOperationException($"Alumni with ID {testimonialDto.AlumniId} not found");
            }

            var testimonialRepository = _unitOfWork.Repository<AlumniTestimonial>();

            var testimonial = new AlumniTestimonial
            {
                AlumniId = testimonialDto.AlumniId,
                Content = testimonialDto.Content,
                IsApproved = testimonialDto.IsApproved,
                IsActive = testimonialDto.IsActive,
                DisplayOrder = testimonialDto.DisplayOrder,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = _currentUserService.UserId?.ToString()
            };

            await testimonialRepository.AddAsync(testimonial);
            await _unitOfWork.SaveChangesAsync();

            // Add translations if provided
            if (testimonialDto.Translations != null && testimonialDto.Translations.Any())
            {
                var translationRepository = _unitOfWork.Repository<AlumniTestimonialTranslation>();

                foreach (var translationDto in testimonialDto.Translations)
                {
                    var translation = new AlumniTestimonialTranslation
                    {
                        AlumniTestimonialId = testimonial.Id,
                        LanguageCode = translationDto.LanguageCode,
                        Content = translationDto.Content,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = _currentUserService.UserId?.ToString()
                    };

                    await translationRepository.AddAsync(translation);
                }

                await _unitOfWork.SaveChangesAsync();
            }

            _logger.LogInformation("Testimonial created with ID {TestimonialId} for alumni {AlumniId}",
                testimonial.Id, testimonialDto.AlumniId);
            return testimonial.Id;
        }

        public async Task<bool> UpdateTestimonialAsync(Guid id, UpdateAlumniTestimonialDto testimonialDto)
        {
            var repository = _unitOfWork.Repository<AlumniTestimonial>();
            var testimonial = await repository.GetByIdAsync(id);

            if (testimonial == null) return false;

            // Update properties
            testimonial.Content = testimonialDto.Content;
            testimonial.IsApproved = testimonialDto.IsApproved;
            testimonial.IsActive = testimonialDto.IsActive;
            testimonial.DisplayOrder = testimonialDto.DisplayOrder;
            // LastModifiedAt is set below
            testimonial.LastModifiedBy = _currentUserService.UserId?.ToString();
            testimonial.LastModifiedAt = DateTime.UtcNow;

            await repository.UpdateAsync(testimonial);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Testimonial updated with ID {TestimonialId}", testimonial.Id);
            return true;
        }

        public async Task<bool> DeleteTestimonialAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<AlumniTestimonial>();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Testimonial deleted with ID {TestimonialId}", id);
            return true;
        }

        #endregion

        #region Translation Methods

        public async Task<bool> AddTranslationAsync(Guid alumniId, CreateAlumniTranslationDto translationDto)
        {
            var alumniRepository = _unitOfWork.Repository<Alumni>();
            var alumni = await alumniRepository.GetByIdAsync(alumniId, new[] { "Translations" });

            if (alumni == null) return false;

            // Check if translation for this language already exists
            var existingTranslation = alumni.Translations
                .FirstOrDefault(t => t.LanguageCode == translationDto.LanguageCode);

            if (existingTranslation != null)
            {
                _logger.LogWarning("Translation for language {LanguageCode} already exists for alumni {AlumniId}",
                    translationDto.LanguageCode, alumniId);
                return false;
            }

            var translationRepository = _unitOfWork.Repository<AlumniTranslation>();

            var translation = new AlumniTranslation
            {
                AlumniId = alumniId,
                LanguageCode = translationDto.LanguageCode,
                Name = translationDto.Name,
                Profession = translationDto.Profession,
                Organization = translationDto.Organization,
                Designation = translationDto.Designation,
                Biography = translationDto.Biography,
                Achievements = translationDto.Achievements,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = _currentUserService.UserId?.ToString()
            };

            await translationRepository.AddAsync(translation);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Translation added for alumni {AlumniId} in language {LanguageCode}",
                alumniId, translationDto.LanguageCode);
            return true;
        }

        public async Task<bool> UpdateTranslationAsync(Guid alumniId, string languageCode, UpdateAlumniTranslationDto translationDto)
        {
            var translationRepository = _unitOfWork.Repository<AlumniTranslation>();

            var translations = await translationRepository.FindAsync(
                t => t.AlumniId == alumniId && t.LanguageCode == languageCode);

            if (!translations.Any()) return false;

            var translation = translations.First();

            // Update properties
            translation.Name = translationDto.Name;
            translation.Profession = translationDto.Profession;
            translation.Organization = translationDto.Organization;
            translation.Designation = translationDto.Designation;
            translation.Biography = translationDto.Biography;
            translation.Achievements = translationDto.Achievements;
            // LastModifiedAt is set below
            translation.LastModifiedBy = _currentUserService.UserId?.ToString();
            translation.LastModifiedAt = DateTime.UtcNow;

            await translationRepository.UpdateAsync(translation);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Translation updated for alumni {AlumniId} in language {LanguageCode}",
                alumniId, languageCode);
            return true;
        }

        public async Task<bool> DeleteTranslationAsync(Guid alumniId, string languageCode)
        {
            var translationRepository = _unitOfWork.Repository<AlumniTranslation>();

            var translations = await translationRepository.FindAsync(
                t => t.AlumniId == alumniId && t.LanguageCode == languageCode);

            if (!translations.Any()) return false;

            var translation = translations.First();

            await translationRepository.DeleteAsync(translation);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Translation deleted for alumni {AlumniId} in language {LanguageCode}",
                alumniId, languageCode);
            return true;
        }

        #endregion
    }
}
