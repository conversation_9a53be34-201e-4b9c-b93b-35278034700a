using School.Domain.Common;

using System;
using System.Collections.Generic;

namespace School.Domain.Entities
{
    public class ClubEvent : BaseEntity
    {
        public Guid ClubId { get; set; }
        public string Title { get; set; }
        public DateTime Date { get; set; }
        public string Time { get; set; }
        public string Location { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        
        // Navigation properties
        public Club Club { get; set; }
        public ICollection<ClubEventTranslation> Translations { get; set; }
    }
}
