import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-error-display',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div class="error-container" [ngStyle]="{'height': height}">
      <mat-card>
        <mat-card-content>
          <div class="error-content">
            <mat-icon color="warn" class="error-icon">error</mat-icon>
            <p class="error-message">{{ message }}</p>
          </div>
        </mat-card-content>
        <mat-card-actions align="end">
          <button mat-button color="primary" (click)="retry.emit()" *ngIf="showRetry">Retry</button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .error-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
    }
    
    .error-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
    
    .error-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }
    
    .error-message {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.87);
    }
  `]
})
export class ErrorDisplayComponent {
  @Input() message: string = 'An error occurred. Please try again later.';
  @Input() showRetry: boolean = true;
  @Input() height: string = '300px';
  @Output() retry = new EventEmitter<void>();
}
