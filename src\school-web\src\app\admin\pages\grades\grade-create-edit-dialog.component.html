<div class="dialog-container">
  <h2 mat-dialog-title>
    <mat-icon>school</mat-icon>
    {{ getTitle() | translate }}
  </h2>

  <mat-dialog-content>
    <form [formGroup]="gradeForm" class="grade-form">
      <!-- Basic Information Section -->
      <div class="form-section">
        <h3 class="section-title">{{ 'GRADES.BASIC_INFORMATION' | translate }}</h3>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'GRADES.NAME' | translate }}</mat-label>
            <input matInput formControlName="name" placeholder="{{ 'GRADES.NAME_PLACEHOLDER' | translate }}">
            <mat-error *ngIf="gradeForm.get('name')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
            <mat-error *ngIf="gradeForm.get('name')?.hasError('maxlength')">
              {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 100} }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'GRADES.CODE' | translate }}</mat-label>
            <input matInput formControlName="code" placeholder="{{ 'GRADES.CODE_PLACEHOLDER' | translate }}">
            <mat-error *ngIf="gradeForm.get('code')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
            <mat-error *ngIf="gradeForm.get('code')?.hasError('maxlength')">
              {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 20} }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'GRADES.LEVEL' | translate }}</mat-label>
            <input matInput type="number" formControlName="level" min="1" max="20">
            <mat-error *ngIf="gradeForm.get('level')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
            <mat-error *ngIf="gradeForm.get('level')?.hasError('min') || gradeForm.get('level')?.hasError('max')">
              {{ 'VALIDATION.RANGE' | translate: {min: 1, max: 20} }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'GRADES.EDUCATION_LEVEL' | translate }}</mat-label>
            <mat-select formControlName="educationLevel">
              <mat-option *ngFor="let level of educationLevels" [value]="level.value">
                {{ level.label }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="gradeForm.get('educationLevel')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'GRADES.DESCRIPTION' | translate }}</mat-label>
          <textarea matInput formControlName="description" rows="3" 
                    placeholder="{{ 'GRADES.DESCRIPTION_PLACEHOLDER' | translate }}"></textarea>
          <mat-error *ngIf="gradeForm.get('description')?.hasError('maxlength')">
            {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 500} }}
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Age and Capacity Section -->
      <div class="form-section">
        <h3 class="section-title">{{ 'GRADES.AGE_AND_CAPACITY' | translate }}</h3>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'GRADES.MIN_AGE' | translate }}</mat-label>
            <input matInput type="number" formControlName="minAge" min="0" max="25">
            <mat-error *ngIf="gradeForm.get('minAge')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
            <mat-error *ngIf="gradeForm.get('minAge')?.hasError('min') || gradeForm.get('minAge')?.hasError('max')">
              {{ 'VALIDATION.RANGE' | translate: {min: 0, max: 25} }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'GRADES.MAX_AGE' | translate }}</mat-label>
            <input matInput type="number" formControlName="maxAge" min="0" max="30">
            <mat-error *ngIf="gradeForm.get('maxAge')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
            <mat-error *ngIf="gradeForm.get('maxAge')?.hasError('min') || gradeForm.get('maxAge')?.hasError('max')">
              {{ 'VALIDATION.RANGE' | translate: {min: 0, max: 30} }}
            </mat-error>
            <mat-error *ngIf="gradeForm.get('maxAge')?.hasError('ageRange')">
              {{ 'GRADES.AGE_RANGE_ERROR' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'GRADES.MAX_STUDENTS' | translate }}</mat-label>
            <input matInput type="number" formControlName="maxStudents" min="1" max="200">
            <mat-error *ngIf="gradeForm.get('maxStudents')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
            <mat-error *ngIf="gradeForm.get('maxStudents')?.hasError('min') || gradeForm.get('maxStudents')?.hasError('max')">
              {{ 'VALIDATION.RANGE' | translate: {min: 1, max: 200} }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'GRADES.DISPLAY_ORDER' | translate }}</mat-label>
            <input matInput type="number" formControlName="displayOrder" min="1">
            <mat-error *ngIf="gradeForm.get('displayOrder')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
            <mat-error *ngIf="gradeForm.get('displayOrder')?.hasError('min')">
              {{ 'VALIDATION.MIN_VALUE' | translate: {min: 1} }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      <!-- Academic Information Section -->
      <div class="form-section">
        <h3 class="section-title">{{ 'GRADES.ACADEMIC_INFORMATION' | translate }}</h3>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'GRADES.ACADEMIC_YEAR' | translate }}</mat-label>
            <mat-select formControlName="academicYearId">
              <mat-option *ngFor="let year of academicYears" [value]="year.id">
                {{ year.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="gradeForm.get('academicYearId')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'GRADES.MIN_PASSING_GRADE' | translate }}</mat-label>
            <input matInput type="number" formControlName="minPassingGrade" min="0" max="100" step="0.01">
            <mat-hint>{{ 'GRADES.MIN_PASSING_GRADE_HINT' | translate }}</mat-hint>
            <mat-error *ngIf="gradeForm.get('minPassingGrade')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
            <mat-error *ngIf="gradeForm.get('minPassingGrade')?.hasError('min') || gradeForm.get('minPassingGrade')?.hasError('max')">
              {{ 'VALIDATION.RANGE' | translate: {min: 0, max: 100} }}
            </mat-error>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'GRADES.PROMOTION_CRITERIA' | translate }}</mat-label>
          <textarea matInput formControlName="promotionCriteria" rows="3" 
                    placeholder="{{ 'GRADES.PROMOTION_CRITERIA_PLACEHOLDER' | translate }}"></textarea>
          <mat-error *ngIf="gradeForm.get('promotionCriteria')?.hasError('maxlength')">
            {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 1000} }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'GRADES.REMARKS' | translate }}</mat-label>
          <textarea matInput formControlName="remarks" rows="2" 
                    placeholder="{{ 'GRADES.REMARKS_PLACEHOLDER' | translate }}"></textarea>
          <mat-error *ngIf="gradeForm.get('remarks')?.hasError('maxlength')">
            {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 500} }}
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Status Section -->
      <div class="form-section">
        <div class="status-toggle">
          <mat-slide-toggle formControlName="isActive" color="primary">
            {{ 'GRADES.IS_ACTIVE' | translate }}
          </mat-slide-toggle>
          <small class="status-hint">{{ 'GRADES.IS_ACTIVE_HINT' | translate }}</small>
        </div>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()" [disabled]="loading">
      {{ 'COMMON.CANCEL' | translate }}
    </button>
    <button mat-raised-button color="primary" (click)="onSubmit()" 
            [disabled]="!gradeForm.valid || loading">
      <mat-spinner *ngIf="loading" diameter="20" class="button-spinner"></mat-spinner>
      <span *ngIf="!loading">{{ getSubmitButtonText() | translate }}</span>
    </button>
  </mat-dialog-actions>
</div>
