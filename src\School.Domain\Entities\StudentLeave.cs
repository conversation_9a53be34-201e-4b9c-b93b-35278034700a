using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class StudentLeave : BaseEntity
{
    public Guid StudentId { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public LeaveType Type { get; set; }
    public string Reason { get; set; } = string.Empty;
    public LeaveStatus Status { get; set; } = LeaveStatus.Pending;
    public string ApprovedBy { get; set; } = string.Empty;
    public DateTime? ApprovedAt { get; set; }
    public string Comments { get; set; } = string.Empty;
    public string AttachmentPath { get; set; } = string.Empty;

    // Navigation properties
    public Student Student { get; set; } = null!;
    public DateTime UpdatedAt { get; set; }
    public string ApprovalRemarks { get; set; } = string.Empty;
}
