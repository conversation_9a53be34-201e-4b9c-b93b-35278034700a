<!-- Hero Section -->
<app-default-hero
  translationPrefix="CAMPUS_LIFE"
  title="CAMPUS_LIFE.DINING"
  subtitle="CAMPUS_LIFE.DINING_SUBTITLE"
  theme="dark"
  size="large"
  alignment="center"
  backgroundImage="assets/images/campus/dining-hero.jpg">
</app-default-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'CAMPUS_LIFE.DINING_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.DINING_INTRO_P1' | translate }}</p>
      <p>{{ 'CAMPUS_LIFE.DINING_INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Dining Locations Section -->
  <section class="locations-section">
    <h2>{{ 'CAMPUS_LIFE.DINING_LOCATIONS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.LOCATIONS_INTRO' | translate }}</p>

    <div class="locations-grid">
      <mat-card class="location-card" *ngFor="let location of diningLocations">
        <div class="location-image">
          <img [src]="location.image" [alt]="location.name">
        </div>
        <mat-card-content>
          <h3>{{location.name}}</h3>
          <p class="location-description">{{location.description}}</p>
          <div class="location-details">
            <div class="detail-item">
              <mat-icon>schedule</mat-icon>
              <span>{{location.hours}}</span>
            </div>
            <div class="detail-item">
              <mat-icon>location_on</mat-icon>
              <span>{{location.location}}</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Sample Menu Section -->
  <section class="menu-section">
    <h2>{{ 'CAMPUS_LIFE.SAMPLE_MENU' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.MENU_INTRO' | translate }}</p>

    <mat-tab-group animationDuration="300ms">
      <mat-tab *ngFor="let category of menuCategories" [label]="category">
        <div class="menu-grid">
          <mat-card class="menu-card" *ngFor="let item of getMenuItemsByCategory(category)">
            <div class="menu-image">
              <img [src]="item.image" [alt]="item.name">
              <div class="menu-badge" *ngIf="item.vegetarian">
                <mat-icon>spa</mat-icon>
              </div>
            </div>
            <mat-card-content>
              <div class="menu-header">
                <h3>{{item.name}}</h3>
                <span class="menu-price">{{item.price}}</span>
              </div>
              <p class="menu-description">{{item.description}}</p>
              <div class="menu-allergens" *ngIf="item.allergens && item.allergens.length > 0">
                <span class="allergen-label">{{ 'CAMPUS_LIFE.ALLERGENS' | translate }}:</span>
                <span class="allergen-list">{{item.allergens.join(', ')}}</span>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>
    </mat-tab-group>

    <div class="menu-legend">
      <div class="legend-item">
        <mat-icon>spa</mat-icon>
        <span>{{ 'CAMPUS_LIFE.VEGETARIAN' | translate }}</span>
      </div>
    </div>
  </section>

  <!-- Meal Plans Section -->
  <section class="meal-plans-section">
    <h2>{{ 'CAMPUS_LIFE.MEAL_PLANS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.MEAL_PLANS_INTRO' | translate }}</p>

    <div class="meal-plans-grid">
      <mat-card class="plan-card" *ngFor="let plan of mealPlans">
        <mat-card-header>
          <mat-card-title>{{plan.name}}</mat-card-title>
          <mat-card-subtitle>{{plan.price}}</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p class="plan-description">{{plan.description}}</p>
          <ul class="plan-features">
            <li *ngFor="let feature of plan.features">{{feature}}</li>
          </ul>
        </mat-card-content>
        <mat-card-actions>
          <a mat-button color="primary" routerLink="/admissions">
            {{ 'CAMPUS_LIFE.LEARN_MORE' | translate }}
          </a>
        </mat-card-actions>
      </mat-card>
    </div>
  </section>

  <!-- Nutrition & Sustainability Section -->
  <section class="initiatives-section">
    <h2>{{ 'CAMPUS_LIFE.NUTRITION_SUSTAINABILITY' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.INITIATIVES_INTRO' | translate }}</p>

    <div class="initiatives-grid">
      <div class="initiative-item" *ngFor="let initiative of initiatives">
        <div class="initiative-icon">
          <mat-icon>{{initiative.icon}}</mat-icon>
        </div>
        <h3>{{initiative.title}}</h3>
        <p>{{initiative.description}}</p>
      </div>
    </div>
  </section>

  <!-- Special Dietary Needs Section -->
  <section class="dietary-needs-section">
    <h2>{{ 'CAMPUS_LIFE.SPECIAL_DIETARY_NEEDS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.DIETARY_NEEDS_INTRO' | translate }}</p>

    <div class="dietary-content">
      <div class="dietary-image">
        <img src="assets/images/campus/dining/dietary-needs.jpg" alt="Special Dietary Needs">
      </div>
      <div class="dietary-text">
        <p>{{ 'CAMPUS_LIFE.DIETARY_NEEDS_P1' | translate }}</p>
        <p>{{ 'CAMPUS_LIFE.DIETARY_NEEDS_P2' | translate }}</p>
        <p>{{ 'CAMPUS_LIFE.DIETARY_NEEDS_P3' | translate }}</p>

        <div class="dietary-cta">
          <a mat-raised-button color="primary" href="mailto:dining&#64;school.edu">
            {{ 'CAMPUS_LIFE.CONTACT_DINING_SERVICES' | translate }}
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="contact-section">
    <div class="contact-content">
      <h2>{{ 'CAMPUS_LIFE.DINING_SERVICES' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.DINING_SERVICES_TEXT' | translate }}</p>
      <div class="contact-info">
        <div class="contact-item">
          <mat-icon>person</mat-icon>
          <div class="contact-details">
            <h3>{{ 'CAMPUS_LIFE.DINING_DIRECTOR' | translate }}</h3>
            <p>Chef Robert Anderson</p>
            <p>randerson&#64;school.edu</p>
            <p>(123) 456-7890 ext. 456</p>
          </div>
        </div>

        <div class="contact-item">
          <mat-icon>location_on</mat-icon>
          <div class="contact-details">
            <h3>{{ 'CAMPUS_LIFE.DINING_OFFICE' | translate }}</h3>
            <p>Student Center, Room 105</p>
            <p>Monday-Friday, 8:00 AM - 4:00 PM</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
