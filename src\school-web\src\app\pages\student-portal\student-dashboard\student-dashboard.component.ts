import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { RouterModule } from '@angular/router';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatBadgeModule } from '@angular/material/badge';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { StudentService } from '../../../core/services/student.service';
import { AuthService } from '../../../core/services/auth.service';
import {
  Student, StudentAttendance, StudentFee,
  StudentResult, StudentLeave, ExamType
} from '../../../core/models/student.model';

@Component({
  selector: 'app-student-dashboard',
  templateUrl: './student-dashboard.component.html',
  styleUrls: ['./student-dashboard.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    DatePipe,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatBadgeModule,
    MatChipsModule,
    MatSnackBarModule
  ]
})
export class StudentDashboardComponent implements OnInit {
  student: Student | null = null;
  recentAttendance: StudentAttendance[] = [];
  upcomingFees: StudentFee[] = [];
  recentResults: StudentResult[] = [];
  pendingLeaves: StudentLeave[] = [];
  currentGPA: number | null = null;

  loading = {
    student: true,
    attendance: true,
    fees: true,
    results: true,
    leaves: true,
    gpa: true
  };

  error = {
    student: false,
    attendance: false,
    fees: false,
    results: false,
    leaves: false,
    gpa: false
  };

  constructor(
    private studentService: StudentService,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    this.loadStudentData();
  }

  loadStudentData(): void {
    // Get the current user ID from the auth service
    const userId = this.authService.getCurrentUserId();
    if (!userId) {
      this.error.student = true;
      this.loading.student = false;
      return;
    }

    // Get the student by user ID
    this.studentService.getStudentByUserId(userId)
      .subscribe({
        next: (student) => {
          this.student = student;
          this.loading.student = false;

          // Load related data
          this.loadAttendance();
          this.loadFees();
          this.loadResults();
          this.loadLeaves();
          this.loadGPA();
        },
        error: (err) => {
          console.error('Error loading student data:', err);
          this.error.student = true;
          this.loading.student = false;
        }
      });
  }

  loadAttendance(): void {
    if (!this.student) return;

    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    this.studentService.getStudentAttendance(this.student.id, oneMonthAgo, today)
      .subscribe({
        next: (attendance) => {
          this.recentAttendance = attendance.slice(0, 5); // Get last 5 attendance records
          this.loading.attendance = false;
        },
        error: (err) => {
          console.error('Error loading attendance data:', err);
          this.error.attendance = true;
          this.loading.attendance = false;
        }
      });
  }

  loadFees(): void {
    if (!this.student) return;

    this.studentService.getStudentFees(this.student.id, this.student.academicYear)
      .subscribe({
        next: (fees) => {
          // Get upcoming fees (due date in the future and not fully paid)
          this.upcomingFees = fees
            .filter(fee => new Date(fee.dueDate) > new Date() && fee.dueAmount > 0)
            .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
            .slice(0, 3); // Get next 3 upcoming fees

          this.loading.fees = false;
        },
        error: (err) => {
          console.error('Error loading fee data:', err);
          this.error.fees = true;
          this.loading.fees = false;
        }
      });
  }

  loadResults(): void {
    if (!this.student) return;

    this.studentService.getStudentResults(this.student.id, this.student.academicYear)
      .subscribe({
        next: (results) => {
          this.recentResults = results.slice(0, 5); // Get last 5 results
          this.loading.results = false;
        },
        error: (err) => {
          console.error('Error loading result data:', err);
          this.error.results = true;
          this.loading.results = false;
        }
      });
  }

  loadLeaves(): void {
    if (!this.student) return;

    this.studentService.getStudentLeaves(this.student.id)
      .subscribe({
        next: (leaves) => {
          // Get pending leave applications
          this.pendingLeaves = leaves.filter(leave => leave.status === 0); // 0 = Pending
          this.loading.leaves = false;
        },
        error: (err) => {
          console.error('Error loading leave data:', err);
          this.error.leaves = true;
          this.loading.leaves = false;
        }
      });
  }

  loadGPA(): void {
    if (!this.student) return;

    this.studentService.calculateGPA(this.student.id, this.student.academicYear, ExamType.Annual)
      .subscribe({
        next: (response) => {
          this.currentGPA = response.gpa;
          this.loading.gpa = false;
        },
        error: (err) => {
          console.error('Error loading GPA data:', err);
          this.error.gpa = true;
          this.loading.gpa = false;
        }
      });
  }
}
