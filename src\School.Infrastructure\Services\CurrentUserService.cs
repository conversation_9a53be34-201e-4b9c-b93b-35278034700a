using Microsoft.AspNetCore.Http;
using School.Application.Common.Interfaces;
using System.Security.Claims;

namespace School.Infrastructure.Services;

public class CurrentUserService : ICurrentUserService
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CurrentUserService(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public Guid? UserId
    {
        get
        {
            // Try to get the user ID from the standard claim first
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.NameIdentifier);

            // If not found, try the custom claim
            if (string.IsNullOrEmpty(userId))
            {
                userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("userId");
            }

            // Log the claims for debugging
            if (userId == null)
            {
                var claims = _httpContextAccessor.HttpContext?.User?.Claims.Select(c => $"{c.Type}: {c.Value}").ToList();
                if (claims != null && claims.Any())
                {
                    Console.WriteLine($"Available claims: {string.Join(", ", claims)}");
                }
            }

            return userId != null && Guid.TryParse(userId, out var guid) ? guid : null;
        }
    }

    public string? Username => _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.Name);

    public bool IsAuthenticated => _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;

    public Guid TenantId
    {
        get
        {
            var tenantId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("tenantId");

            if (string.IsNullOrEmpty(tenantId))
            {
                // Fallback to organization claim if tenantId is not found
                tenantId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("organizationId");
            }

            return tenantId != null && Guid.TryParse(tenantId, out var guid) ? guid : Guid.Empty;
        }
    }
}
