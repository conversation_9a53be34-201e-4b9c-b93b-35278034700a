<!-- Enhanced Hero Section -->
<app-enhanced-hero
  [title]="'ACADEMICS.TITLE' | translate"
  [subtitle]="'ACADEMICS.SUBTITLE' | translate"
  [description]="'ACADEMICS.DESCRIPTION' | translate"
  [backgroundImage]="'assets/images/academics-hero.jpg'"
  [overlayImage]="'assets/images/school-emblem.png'"
  [breadcrumbs]="['Academics']"
  [buttons]="[
    {label: 'ACADEMICS.EXPLORE_PROGRAMS' | translate, link: '#programs', isPrimary: true, icon: 'school'},
    {label: 'ACADEMICS.VIEW_CURRICULUM' | translate, link: '#curriculum', isPrimary: false}
  ]"
  [theme]="'light'"
  [size]="'large'"
  [alignment]="'left'">
</app-enhanced-hero>

<!-- Floating Navigation Sidebar -->
<div class="floating-nav" [class.visible]="showFloatingNav">
  <div class="floating-nav-content">
    <h3>{{ 'ACADEMICS.QUICK_NAV' | translate }}</h3>
    <ul class="floating-nav-links">
      <li>
        <a (click)="scrollToSection('programs')" [class.active]="activeSection === 'programs'">
          <mat-icon>school</mat-icon>
          <span>{{ 'ACADEMICS.PROGRAMS' | translate }}</span>
        </a>
      </li>
      <li>
        <a (click)="scrollToSection('special-programs')" [class.active]="activeSection === 'special-programs'">
          <mat-icon>stars</mat-icon>
          <span>{{ 'ACADEMICS.SPECIAL_PROGRAMS' | translate }}</span>
        </a>
      </li>
      <li>
        <a (click)="scrollToSection('curriculum')" [class.active]="activeSection === 'curriculum'">
          <mat-icon>menu_book</mat-icon>
          <span>{{ 'ACADEMICS.CURRICULUM' | translate }}</span>
        </a>
      </li>
      <li>
        <a (click)="scrollToSection('resources')" [class.active]="activeSection === 'resources'">
          <mat-icon>library_books</mat-icon>
          <span>{{ 'ACADEMICS.RESOURCES' | translate }}</span>
        </a>
      </li>
    </ul>
  </div>
</div>

<!-- Main Content -->
<div class="container">
  <!-- Academic Programs Section -->
  <section id="programs" class="content-section">
    <h2>{{ 'ACADEMICS.PROGRAMS' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.PROGRAMS_INTRO' | translate }}</p>

    <mat-tab-group [selectedIndex]="selectedTabIndex" (selectedIndexChange)="setSelectedTab($event)" animationDuration="300ms" class="program-tabs">
      <mat-tab *ngFor="let program of programs" [label]="program.title">
        <div class="program-content">
          <div class="program-image">
            <img [src]="program.image" [alt]="program.title">
          </div>
          <div class="program-details">
            <h3>{{program.title}}</h3>
            <p class="program-grades"><strong>{{ 'ACADEMICS.GRADES' | translate }}:</strong> {{program.grades}}</p>
            <p class="program-description">{{program.description}}</p>

            <h4>{{ 'ACADEMICS.KEY_FEATURES' | translate }}</h4>
            <ul class="feature-list">
              <li *ngFor="let feature of program.features">
                <mat-icon>check_circle</mat-icon>
                <span>{{feature}}</span>
              </li>
            </ul>

            <button mat-raised-button color="primary" class="learn-more-btn">
              {{ 'ACADEMICS.LEARN_MORE' | translate }}
            </button>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>

    <div class="program-nav-buttons">
      <button mat-button [disabled]="selectedTabIndex === 0" (click)="setSelectedTab(selectedTabIndex - 1)">
        <mat-icon>arrow_back</mat-icon> {{ 'ACADEMICS.PREVIOUS' | translate }}
      </button>
      <button mat-button [disabled]="selectedTabIndex === programs.length - 1" (click)="setSelectedTab(selectedTabIndex + 1)">
        {{ 'ACADEMICS.NEXT' | translate }} <mat-icon>arrow_forward</mat-icon>
      </button>
    </div>
  </section>

  <!-- Special Programs Section -->
  <section id="special-programs" class="content-section">
    <h2>{{ 'ACADEMICS.SPECIAL_PROGRAMS' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.SPECIAL_PROGRAMS_INTRO' | translate }}</p>

    <div class="special-programs-grid">
      <div class="special-program-card" *ngFor="let program of specialPrograms">
        <mat-icon class="program-icon">{{program.icon}}</mat-icon>
        <h3>{{program.title}}</h3>
        <p>{{program.description}}</p>
      </div>
    </div>
  </section>

  <!-- Curriculum Section -->
  <section id="curriculum" class="content-section">
    <h2>{{ 'ACADEMICS.CURRICULUM' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.CURRICULUM_INTRO' | translate }}</p>

    <div class="curriculum-container">
      <h3>{{ 'ACADEMICS.SAMPLE_COURSES' | translate }}</h3>

      <mat-accordion class="courses-accordion">
        <mat-expansion-panel *ngFor="let course of courses">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{course.title}}
            </mat-panel-title>
            <mat-panel-description>
              {{course.department}} | {{course.grade}}
            </mat-panel-description>
          </mat-expansion-panel-header>

          <p>{{course.description}}</p>
          <div class="course-details">
            <span class="course-id">{{ 'ACADEMICS.COURSE_ID' | translate }}: {{course.id}}</span>
            <button mat-button color="primary">{{ 'ACADEMICS.VIEW_SYLLABUS' | translate }}</button>
          </div>
        </mat-expansion-panel>
      </mat-accordion>
    </div>
  </section>

  <!-- Academic Resources Section -->
  <section id="resources" class="content-section">
    <h2>{{ 'ACADEMICS.RESOURCES' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.RESOURCES_INTRO' | translate }}</p>

    <div class="resources-grid">
      <mat-card class="resource-card">
        <mat-icon>local_library</mat-icon>
        <h3>{{ 'ACADEMICS.LIBRARY' | translate }}</h3>
        <p>{{ 'ACADEMICS.LIBRARY_DESC' | translate }}</p>
        <a mat-button color="primary" [routerLink]="['/library']">{{ 'ACADEMICS.EXPLORE' | translate }}</a>
      </mat-card>

      <mat-card class="resource-card">
        <mat-icon>computer</mat-icon>
        <h3>{{ 'ACADEMICS.TECHNOLOGY' | translate }}</h3>
        <p>{{ 'ACADEMICS.TECHNOLOGY_DESC' | translate }}</p>
        <a mat-button color="primary" href="#">{{ 'ACADEMICS.LEARN_MORE' | translate }}</a>
      </mat-card>

      <mat-card class="resource-card">
        <mat-icon>psychology</mat-icon>
        <h3>{{ 'ACADEMICS.COUNSELING' | translate }}</h3>
        <p>{{ 'ACADEMICS.COUNSELING_DESC' | translate }}</p>
        <a mat-button color="primary" href="#">{{ 'ACADEMICS.CONTACT' | translate }}</a>
      </mat-card>

      <mat-card class="resource-card">
        <mat-icon>accessibility_new</mat-icon>
        <h3>{{ 'ACADEMICS.SUPPORT_SERVICES' | translate }}</h3>
        <p>{{ 'ACADEMICS.SUPPORT_SERVICES_DESC' | translate }}</p>
        <a mat-button color="primary" href="#">{{ 'ACADEMICS.LEARN_MORE' | translate }}</a>
      </mat-card>
    </div>
  </section>
</div>
