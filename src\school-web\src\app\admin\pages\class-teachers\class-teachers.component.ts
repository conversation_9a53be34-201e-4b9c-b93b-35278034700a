import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatBadgeModule } from '@angular/material/badge';
import { TranslateModule } from '@ngx-translate/core';
import { ClassTeacherService } from '../../../core/services/class-teacher.service';
import { GradeService } from '../../../core/services/grade.service';
import { SectionService } from '../../../core/services/section.service';
import { AcademicYearService } from '../../../core/services/academic-year.service';
import { ClassTeacherAssignDialogComponent } from './class-teacher-assign-dialog.component';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';

export interface ClassTeacher {
  id: string;
  facultyId: string;
  facultyName: string;
  facultyEmail: string;
  sectionId: string;
  sectionName: string;
  sectionCode: string;
  gradeId: string;
  gradeName: string;
  academicYearId: string;
  academicYearName: string;
  startDate: Date;
  endDate?: Date;
  isPrimary: boolean;
  isActive: boolean;
  status: string;
  responsibilities: string;
  specialDuties: string;
  contactSchedule: string;
  officeHours: string;
  studentCount: number;
  workloadPercentage: number;
  createdAt: Date;
  lastModifiedAt?: Date;
}

@Component({
  selector: 'app-class-teachers',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatSnackBarModule,
    MatCardModule,
    MatToolbarModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatBadgeModule,
    TranslateModule
  ],
  templateUrl: './class-teachers.component.html',
  styleUrls: ['./class-teachers.component.scss']
})
export class ClassTeachersComponent implements OnInit {
  private dialog = inject(MatDialog);
  private snackBar = inject(MatSnackBar);
  private classTeacherService = inject(ClassTeacherService);
  private gradeService = inject(GradeService);
  private sectionService = inject(SectionService);
  private academicYearService = inject(AcademicYearService);

  displayedColumns: string[] = [
    'faculty',
    'section',
    'grade',
    'workload',
    'status',
    'startDate',
    'academicYear',
    'actions'
  ];

  classTeachers: ClassTeacher[] = [];
  grades: any[] = [];
  sections: any[] = [];
  academicYears: any[] = [];
  loading = false;
  totalCount = 0;
  pageSize = 10;
  pageIndex = 0;
  searchTerm = '';
  selectedGrade = '';
  selectedSection = '';
  selectedAcademicYear = '';
  selectedStatus = '';

  statusOptions = [
    { value: 'Active', label: 'Active' },
    { value: 'Inactive', label: 'Inactive' },
    { value: 'Suspended', label: 'Suspended' },
    { value: 'Completed', label: 'Completed' }
  ];

  ngOnInit() {
    this.loadClassTeachers();
    this.loadGrades();
    this.loadSections();
    this.loadAcademicYears();
  }

  loadClassTeachers() {
    this.loading = true;
    const filter = {
      page: this.pageIndex + 1,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm,
      gradeId: this.selectedGrade || undefined,
      sectionId: this.selectedSection || undefined,
      academicYearId: this.selectedAcademicYear || undefined,
      status: this.selectedStatus || undefined
    };

    this.classTeacherService.getClassTeachers(filter).subscribe({
      next: (response: any) => {
        this.classTeachers = response.data;
        this.totalCount = response.totalCount;
        this.loading = false;
      },
      error: (error: any) => {
        console.error('Error loading class teachers:', error);
        this.snackBar.open('Error loading class teachers', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  loadGrades() {
    this.gradeService.getActiveGrades('').subscribe({
      next: (grades: any) => {
        this.grades = grades;
      },
      error: (error: any) => {
        console.error('Error loading grades:', error);
      }
    });
  }

  loadSections() {
    if (this.selectedGrade) {
      this.sectionService.getSectionsByGrade(this.selectedGrade).subscribe({
        next: (sections: any) => {
          this.sections = sections;
        },
        error: (error: any) => {
          console.error('Error loading sections:', error);
        }
      });
    } else {
      this.sections = [];
    }
  }

  loadAcademicYears() {
    this.academicYearService.getActiveAcademicYears().subscribe({
      next: (years: any) => {
        this.academicYears = years;
      },
      error: (error: any) => {
        console.error('Error loading academic years:', error);
      }
    });
  }

  onGradeChange() {
    this.selectedSection = '';
    this.loadSections();
    this.onSearch();
  }

  onSearch() {
    this.pageIndex = 0;
    this.loadClassTeachers();
  }

  onPageChange(event: any) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadClassTeachers();
  }

  openAssignDialog() {
    const dialogRef = this.dialog.open(ClassTeacherAssignDialogComponent, {
      width: '600px',
      data: { mode: 'assign' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadClassTeachers();
        this.snackBar.open('Class teacher assigned successfully', 'Close', { duration: 3000 });
      }
    });
  }

  openReassignDialog(classTeacher: ClassTeacher) {
    const dialogRef = this.dialog.open(ClassTeacherAssignDialogComponent, {
      width: '600px',
      data: { mode: 'reassign', classTeacher: { ...classTeacher } }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadClassTeachers();
        this.snackBar.open('Class teacher reassigned successfully', 'Close', { duration: 3000 });
      }
    });
  }

  removeClassTeacher(classTeacher: ClassTeacher) {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Remove Class Teacher',
        message: `Are you sure you want to remove ${classTeacher.facultyName} from ${classTeacher.sectionName}?`,
        confirmText: 'Remove',
        cancelText: 'Cancel',
        type: 'warning'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.classTeacherService.removeClassTeacher(classTeacher.sectionId).subscribe({
          next: () => {
            this.loadClassTeachers();
            this.snackBar.open('Class teacher removed successfully', 'Close', { duration: 3000 });
          },
          error: (error: any) => {
            console.error('Error removing class teacher:', error);
            this.snackBar.open('Error removing class teacher', 'Close', { duration: 3000 });
          }
        });
      }
    });
  }

  toggleStatus(classTeacher: ClassTeacher) {
    const newStatus = classTeacher.isActive ? 'Inactive' : 'Active';
    this.classTeacherService.updateClassTeacherStatus(classTeacher.id, newStatus).subscribe({
      next: () => {
        classTeacher.isActive = !classTeacher.isActive;
        classTeacher.status = newStatus;
        this.snackBar.open(
          `Class teacher ${newStatus.toLowerCase()} successfully`, 
          'Close', 
          { duration: 3000 }
        );
      },
      error: (error: any) => {
        console.error('Error updating status:', error);
        this.snackBar.open('Error updating status', 'Close', { duration: 3000 });
      }
    });
  }

  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'active':
        return 'primary';
      case 'inactive':
        return 'warn';
      case 'suspended':
        return 'accent';
      case 'completed':
        return '';
      default:
        return '';
    }
  }

  getWorkloadColor(percentage: number): string {
    if (percentage >= 100) return 'warn';
    if (percentage >= 80) return 'accent';
    return 'primary';
  }

  clearFilters() {
    this.searchTerm = '';
    this.selectedGrade = '';
    this.selectedSection = '';
    this.selectedAcademicYear = '';
    this.selectedStatus = '';
    this.sections = [];
    this.pageIndex = 0;
    this.loadClassTeachers();
  }
}
