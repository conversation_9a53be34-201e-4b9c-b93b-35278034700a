using School.Application.DTOs;
using School.Domain.Enums;
using School.Domain.Entities;

namespace School.Application.Features.Section;

/// <summary>
/// Service interface for Section management operations
/// </summary>
public interface ISectionService
{
    // Section CRUD operations
    Task<(IEnumerable<SectionDto> Sections, int TotalCount)> GetAllSectionsAsync(SectionFilterDto filter);
    Task<SectionDto?> GetSectionByIdAsync(Guid id);
    Task<IEnumerable<SectionDto>> GetSectionsByGradeAsync(Guid gradeId);
    Task<IEnumerable<SectionDto>> GetSectionsByAcademicYearAsync(Guid academicYearId);
    Task<IEnumerable<SectionDto>> GetActiveSectionsAsync(Guid gradeId);
    Task<Guid> CreateSectionAsync(CreateSectionDto sectionDto);
    Task<bool> UpdateSectionAsync(Guid id, UpdateSectionDto sectionDto);
    Task<bool> DeleteSectionAsync(Guid id);

    // Section status management
    Task<bool> ActivateSectionAsync(Guid id);
    Task<bool> DeactivateSectionAsync(Guid id);
    Task<bool> UpdateDisplayOrderAsync(Guid id, int newOrder);

    // Section validation
    Task<bool> ValidateSectionCodeAsync(string code, Guid gradeId, Guid? excludeId = null);
    Task<bool> ValidateSectionNameAsync(string name, Guid gradeId, Guid? excludeId = null);
    Task<bool> CanDeleteSectionAsync(Guid id);

    // Section capacity management
    Task<bool> UpdateSectionCapacityAsync(Guid id, int newCapacity);
    Task<bool> CheckSectionCapacityAsync(Guid sectionId);
    Task<IEnumerable<SectionDto>> GetOverCapacitySectionsAsync(Guid academicYearId);
    Task<IEnumerable<SectionDto>> GetUnderUtilizedSectionsAsync(Guid academicYearId, decimal threshold = 50.0m);
    Task<bool> BulkUpdateCapacityAsync(List<SectionCapacityDto> capacityUpdates);

    // Student enrollment management
    Task<bool> EnrollStudentAsync(Guid sectionId, Guid studentId);
    Task<bool> TransferStudentAsync(Guid studentId, Guid fromSectionId, Guid toSectionId);
    Task<bool> UnenrollStudentAsync(Guid sectionId, Guid studentId);
    Task<bool> BulkEnrollStudentsAsync(Guid sectionId, List<Guid> studentIds);
    Task<int> GetAvailableSlotsAsync(Guid sectionId);
    Task<bool> HasAvailableCapacityAsync(Guid sectionId, int requiredSlots = 1);

    // Class teacher management
    Task<bool> AssignClassTeacherAsync(Guid sectionId, Guid facultyId);
    Task<bool> RemoveClassTeacherAsync(Guid sectionId);
    Task<bool> UpdateClassTeacherAsync(Guid sectionId, Guid newFacultyId);
    Task<IEnumerable<SectionDto>> GetSectionsWithoutClassTeacherAsync(Guid academicYearId);
    Task<IEnumerable<SectionDto>> GetSectionsByClassTeacherAsync(Guid facultyId);

    // Section statistics and analytics
    Task<SectionStatisticsDto> GetSectionStatisticsAsync(Guid id);
    Task<IEnumerable<SectionStatisticsDto>> GetGradeSectionStatisticsAsync(Guid gradeId);
    Task<IEnumerable<SectionStatisticsDto>> GetAcademicYearSectionStatisticsAsync(Guid academicYearId);
    Task<decimal> GetSectionUtilizationAsync(Guid sectionId);
    Task<int> GetTotalStudentsBySectionAsync(Guid sectionId);

    // Translation management
    Task<bool> AddTranslationAsync(Guid sectionId, CreateSectionTranslationDto translationDto);
    Task<bool> UpdateTranslationAsync(Guid sectionId, UpdateSectionTranslationDto translationDto);
    Task<bool> DeleteTranslationAsync(Guid sectionId, string languageCode);
    Task<IEnumerable<SectionTranslationDto>> GetTranslationsAsync(Guid sectionId);

    // Bulk operations
    Task<bool> BulkUpdateSectionStatusAsync(List<Guid> sectionIds, bool isActive);
    Task<bool> BulkDeleteSectionsAsync(List<Guid> sectionIds);
    Task<bool> BulkUpdateDisplayOrderAsync(Dictionary<Guid, int> sectionOrders);
    Task<bool> BulkAssignClassTeachersAsync(Dictionary<Guid, Guid> sectionTeacherMappings);
    Task<bool> ProcessBulkOperationAsync(BulkSectionOperationDto operationDto);

    // Section templates and automation
    Task<bool> CreateSectionsFromTemplateAsync(Guid gradeId, int numberOfSections, string namePattern = "Section {0}");
    Task<bool> AutoAssignStudentsToSectionsAsync(Guid gradeId, string assignmentStrategy = "balanced");
    Task<IEnumerable<SectionDto>> GetSectionTemplatesAsync();

    // Import/Export operations
    Task<bool> ImportSectionsFromCsvAsync(Stream csvStream, Guid gradeId);
    Task<Stream> ExportSectionsToCsvAsync(Guid gradeId);
    Task<bool> DuplicateSectionsToNewAcademicYearAsync(Guid sourceGradeId, Guid targetGradeId);

    // Section scheduling and resources
    Task<bool> UpdateSectionClassroomAsync(Guid sectionId, string classroom, string roomNumber);
    Task<bool> UpdateSectionScheduleAsync(Guid sectionId, ShiftType shift);
    Task<IEnumerable<SectionDto>> GetSectionsByShiftAsync(Guid academicYearId, ShiftType shift);
    Task<IEnumerable<SectionDto>> GetSectionsByMediumAsync(Guid academicYearId, TeachingMedium medium);

    // Section relationships
    Task<IEnumerable<StudentDto>> GetSectionStudentsAsync(Guid sectionId);
    Task<ClassTeacherDto?> GetSectionClassTeacherAsync(Guid sectionId);
    Task<GradeDto?> GetSectionGradeAsync(Guid sectionId);

    // Academic year transitions
    Task<bool> ArchiveSectionAsync(Guid sectionId);
    Task<bool> RestoreSectionAsync(Guid sectionId);
    Task<IEnumerable<SectionDto>> GetArchivedSectionsAsync(Guid gradeId);
    Task<bool> PromoteSectionStudentsAsync(Guid sectionId, Guid targetSectionId);

    // Section optimization
    Task<IEnumerable<string>> GetSectionOptimizationSuggestionsAsync(Guid gradeId);
    Task<bool> OptimizeSectionDistributionAsync(Guid gradeId);
    Task<decimal> CalculateOptimalSectionCountAsync(Guid gradeId, int totalStudents, int idealCapacity);
}
