using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class StudentAttendance : BaseEntity
{
    public Guid StudentId { get; set; }
    public DateTime Date { get; set; }
    public AttendanceStatus Status { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public string RecordedBy { get; set; } = string.Empty;
    public int AcademicYear { get; set; }
    public int Grade { get; set; }
    public string Section { get; set; } = string.Empty;
    public string Period { get; set; } = string.Empty; // For period-wise attendance
    public string SubjectCode { get; set; } = string.Empty; // For subject-wise attendance
    public bool IsLeaveApproved { get; set; } = false; // If student is on approved leave
    public Guid? LeaveId { get; set; } // Reference to leave application if applicable

    // Navigation properties
    public Student Student { get; set; } = null!;
    public StudentLeave? Leave { get; set; }
    public DateTime UpdatedAt { get; set; }
}
