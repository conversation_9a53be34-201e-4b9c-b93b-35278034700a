using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ClubEventConfiguration : IEntityTypeConfiguration<ClubEvent>
{
    public void Configure(EntityTypeBuilder<ClubEvent> builder)
    {
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.Title)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(e => e.Date)
            .IsRequired();
            
        builder.Property(e => e.Time)
            .HasMaxLength(50);
            
        builder.Property(e => e.Location)
            .HasMaxLength(100);
            
        builder.Property(e => e.Description)
            .IsRequired();
            
        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);
            
        // Relationships
        builder.HasMany(e => e.Translations)
            .WithOne(t => t.ClubEvent)
            .HasForeignKey(t => t.ClubEventId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
