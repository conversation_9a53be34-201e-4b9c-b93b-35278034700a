import { MediaItem } from './media.model';
import { Faculty } from './faculty.model';
import { Parent, CreateParent } from './parent.model';

export interface Student {
  id: number;
  studentId: string;
  firstName: string;
  lastName: string;
  fullName?: string;
  dateOfBirth: Date;
  gender: GenderType;
  email: string;
  phone: string;
  address: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelation: string;
  bloodGroup: string;
  medicalConditions: string;
  allergies: string;
  currentGrade: number;
  section: string;
  medium: TeachingMedium;
  shift: ShiftType;
  academicYear: number;
  rollNumber: number;
  admissionYear: number;
  graduationDate?: Date;
  isActive: boolean;
  isHosteler: boolean;
  userId: string;
  classTeacherId?: number;
  classTeacher?: Faculty;
  profileImageId?: number;
  profileImage?: MediaItem;
  createdAt: Date;
  updatedAt?: Date;
  parents: StudentParent[];
}

export interface StudentDetail extends Student {
  recentAttendance: StudentAttendance[];
  recentFees: StudentFee[];
  recentResults: StudentResult[];
  recentLeaves: StudentLeave[];
  academicHistory: StudentAcademicHistory[];
  currentYearGPA?: number;
}

export interface StudentParent {
  id: number;
  studentId: number;
  parentId: number;
  relationType: ParentRelationType;
  isPrimaryContact: boolean;
  parent: Parent;
}

export interface StudentAttendance {
  id: number;
  studentId: number;
  date: Date;
  status: AttendanceStatus;
  remarks: string;
  recordedBy: string;
  academicYear: number;
  grade: number;
  section: string;
  period: string;
  subjectCode: string;
  isLeaveApproved: boolean;
  leaveId?: number;
  createdAt: Date;
  updatedAt?: Date;
}

export interface StudentFee {
  id: number;
  studentId: number;
  type: FeeType;
  description: string;
  amount: number;
  paidAmount: number;
  dueAmount: number;
  dueDate: Date;
  paidDate?: Date;
  status: PaymentStatus;
  transactionId: string;
  paymentMethod: string;
  remarks: string;
  createdAt: Date;
  updatedAt?: Date;
}

export interface StudentResult {
  id: number;
  studentId: number;
  examType: ExamType;
  subjectCode: string;
  subjectName: string;
  marksObtained: number;
  totalMarks: number;
  percentage: number;
  letterGrade: string;
  gradePoint: number;
  remarks: string;
  academicYear: number;
  isOptional: boolean;
  createdAt: Date;
  updatedAt?: Date;
  marks: number;
  maxMarks: any;
  grade: string;
  credits?: number;
}

export interface StudentLeave {
  id: number;
  studentId: number;
  startDate: Date;
  endDate: Date;
  type: LeaveType;
  reason: string;
  status: LeaveStatus;
  approvedBy: string;
  approvedAt?: Date;
  comments: string;
  attachmentPath: string;
  createdAt: Date;
  updatedAt?: Date;
  approvalRemarks?: any;
}

export interface StudentAcademicHistory {
  id: number;
  studentId: number;
  academicYear: number;
  grade: number;
  section: string;
  medium: TeachingMedium;
  shift: ShiftType;
  rollNumber: number;
  studentIdForYear: string;
  finalGPA?: number;
  remarks: string;
  isPromoted: boolean;
  classTeacherId?: number;
  classTeacher?: Faculty;
  createdAt: Date;
  updatedAt?: Date;
  studentId_Year?: string;
}

export interface CreateStudent {
  studentId: string;
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  gender: GenderType;
  email: string;
  phone: string;
  address: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelation: string;
  bloodGroup: string;
  medicalConditions: string;
  allergies: string;
  currentGrade: number;
  section: string;
  medium: TeachingMedium;
  shift: ShiftType;
  academicYear: number;
  rollNumber: number;
  admissionYear: number;
  graduationDate?: Date;
  isActive: boolean;
  isHosteler: boolean;
  userId: string;
  classTeacherId?: number;
  profileImageId?: number;
  parents?: CreateStudentParent[];
}

export interface UpdateStudent {
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  gender: GenderType;
  email: string;
  phone: string;
  address: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelation: string;
  bloodGroup: string;
  medicalConditions: string;
  allergies: string;
  currentGrade: number;
  section: string;
  medium: TeachingMedium;
  shift: ShiftType;
  academicYear: number;
  rollNumber: number;
  admissionYear: number;
  graduationDate?: Date;
  isActive: boolean;
  isHosteler: boolean;
  classTeacherId?: number;
  profileImageId?: number;
}

export interface CreateStudentParent {
  parentId?: number;
  relationType: ParentRelationType;
  isPrimaryContact: boolean;
  parent?: CreateParent;
}

export interface CreateStudentAttendance {
  studentId: number;
  date: Date;
  status: AttendanceStatus;
  remarks: string;
  recordedBy: string;
  period: string;
  subjectCode: string;
}

export interface UpdateStudentAttendance {
  status: AttendanceStatus;
  remarks: string;
  recordedBy: string;
}

export interface CreateStudentFee {
  studentId: number;
  type: FeeType;
  description: string;
  amount: number;
  paidAmount: number;
  dueAmount: number;
  dueDate: Date;
  paidDate?: Date;
  status: PaymentStatus;
  transactionId: string;
  paymentMethod: string;
  remarks: string;
}

export interface UpdateStudentFee {
  paidAmount: number;
  dueAmount: number;
  paidDate?: Date;
  status: PaymentStatus;
  transactionId: string;
  paymentMethod: string;
  remarks: string;
}

export interface CreateStudentResult {
  studentId: number;
  examType: ExamType;
  subjectCode: string;
  subjectName: string;
  marksObtained: number;
  totalMarks: number;
  remarks: string;
  academicYear: number;
  isOptional: boolean;
  examName?: string;
  subject?: string;
  semester?: string;
  marks?: number;
  maxMarks?: any;
  grade?: string;
  credits?: number;
}

export interface UpdateStudentResult {
  marksObtained: number;
  totalMarks: number;
  remarks: string;
  letterGrade?: string;
  gradePoint?: number;
  marks?: number;
  maxMarks?: any;
  grade?: string;
  credits?: number;
}

export interface CreateStudentLeave {
  studentId: number;
  startDate: Date;
  endDate: Date;
  type: LeaveType;
  reason: string;
  attachmentPath: string;
}

export interface UpdateStudentLeave {
  status: LeaveStatus;
  approvedBy: string;
  comments: string;
  approvalRemarks?: any;
}

export interface CreateStudentAcademicHistory {
  studentId: number;
  academicYear: number;
  grade: number;
  section: string;
  medium: TeachingMedium;
  shift: ShiftType;
  rollNumber: number;
  studentIdForYear: string;
  finalGPA?: number;
  remarks: string;
  isPromoted: boolean;
  classTeacherId?: number;
}

export interface UpdateStudentAcademicHistory {
  finalGPA?: number;
  remarks: string;
  isPromoted: boolean;
  grade?: number;
  section?: string;
  rollNumber?: number;
  studentId_Year?: string;
  classTeacherId?: number;
}

export interface StudentFilter {
  studentId?: string;
  name?: string;
  grade?: number;
  section?: string;
  medium?: TeachingMedium;
  shift?: ShiftType;
  academicYear?: number;
  rollNumber?: number;
  admissionYear?: number;
  classTeacherId?: number;
  isActive?: boolean;
  isHosteler?: boolean;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: string;
}

export enum GenderType {
  Male = 0,
  Female = 1,
  Other = 2
}

export enum TeachingMedium {
  Bengali = 0,
  English = 1
}

export enum ShiftType {
  Morning = 0,
  Day = 1,
  Evening = 2
}

export enum AttendanceStatus {
  Present = 0,
  Absent = 1,
  Late = 2,
  Excused = 3,
  OnLeave = 4
}

export enum FeeType {
  Tuition = 0,
  Admission = 1,
  Examination = 2,
  Library = 3,
  Laboratory = 4,
  Transport = 5,
  Hostel = 6,
  Other = 7
}

export enum PaymentStatus {
  Pending = 0,
  Partial = 1,
  Paid = 2,
  Overdue = 3,
  Waived = 4
}

export enum ExamType {
  HalfYearly = 0,
  Annual = 1,
  ClassTest = 2,
  SSC = 3
}

export enum LeaveType {
  Sick = 0,
  Personal = 1,
  Family = 2,
  Religious = 3,
  Other = 4
}

export enum LeaveStatus {
  Pending = 0,
  Approved = 1,
  Rejected = 2,
  Cancelled = 3
}

export enum ParentRelationType {
  Father = 0,
  Mother = 1,
  Guardian = 2,
  Other = 3
}

