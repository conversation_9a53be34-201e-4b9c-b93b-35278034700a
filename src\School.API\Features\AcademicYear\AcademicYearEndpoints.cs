using Carter;
using Microsoft.AspNetCore.Mvc;
using School.Application.Features.AcademicYear;
using School.Application.Features.AcademicYear.DTOs;

namespace School.API.Features.AcademicYear;

/// <summary>
/// Academic Year management API endpoints
/// </summary>
public class AcademicYearEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/academic-years")
            .WithTags("Academic Years")
            .RequireAuthorization();

        // Academic Year CRUD operations
        group.MapGet("/", GetAllAcademicYears)
            .WithName("GetAllAcademicYears")
            .WithSummary("Get all academic years with filtering and pagination")
            .Produces<(IEnumerable<AcademicYearDto> AcademicYears, int TotalCount)>();

        group.MapGet("/{id:guid}", GetAcademicYearById)
            .WithName("GetAcademicYearById")
            .WithSummary("Get academic year by ID")
            .Produces<AcademicYearDto>()
            .Produces(404);

        group.MapGet("/current", GetCurrentAcademicYear)
            .WithName("GetCurrentAcademicYear")
            .WithSummary("Get current active academic year")
            .Produces<AcademicYearDto>()
            .Produces(404);

        group.MapPost("/", CreateAcademicYear)
            .WithName("CreateAcademicYear")
            .WithSummary("Create a new academic year")
            .Produces<Guid>(201)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        group.MapPut("/{id:guid}", UpdateAcademicYear)
            .WithName("UpdateAcademicYear")
            .WithSummary("Update an existing academic year")
            .Produces(204)
            .Produces(400)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapDelete("/{id:guid}", DeleteAcademicYear)
            .WithName("DeleteAcademicYear")
            .WithSummary("Delete an academic year")
            .Produces(204)
            .Produces(400)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapPatch("/{id:guid}/activate", ActivateAcademicYear)
            .WithName("ActivateAcademicYear")
            .WithSummary("Activate an academic year")
            .Produces(204)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapPatch("/{id:guid}/deactivate", DeactivateAcademicYear)
            .WithName("DeactivateAcademicYear")
            .WithSummary("Deactivate an academic year")
            .Produces(204)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapPatch("/{id:guid}/set-current", SetCurrentAcademicYear)
            .WithName("SetCurrentAcademicYear")
            .WithSummary("Set as current academic year")
            .Produces(204)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        // Academic Year validation
        group.MapGet("/validate/name/{name}", ValidateAcademicYearName)
            .WithName("ValidateAcademicYearName")
            .WithSummary("Check if academic year name is unique")
            .Produces<bool>();

        // Academic Year analytics
        group.MapGet("/statistics", GetAcademicYearStatistics)
            .WithName("GetAcademicYearStatistics")
            .WithSummary("Get academic year statistics")
            .Produces<object>();
    }

    #region Endpoint Implementations

    private static async Task<IResult> GetAllAcademicYears(
        IAcademicYearService academicYearService,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] bool? isCurrent = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string sortBy = "Name",
        [FromQuery] bool sortDescending = false)
    {
        try
        {
            var filter = new AcademicYearFilterDto
            {
                SearchTerm = searchTerm,
                IsActive = isActive,
                IsCurrent = isCurrent,
                Page = page,
                PageSize = pageSize,
                SortBy = sortBy,
                SortDescending = sortDescending
            };

            var result = await academicYearService.GetAllAcademicYearsAsync(filter);
            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving academic years: {ex.Message}");
        }
    }

    private static async Task<IResult> GetAcademicYearById(Guid id, IAcademicYearService academicYearService)
    {
        try
        {
            var academicYear = await academicYearService.GetAcademicYearByIdAsync(id);
            return academicYear != null ? Results.Ok(academicYear) : Results.NotFound($"Academic year with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving academic year: {ex.Message}");
        }
    }

    private static async Task<IResult> GetCurrentAcademicYear(IAcademicYearService academicYearService)
    {
        try
        {
            var academicYear = await academicYearService.GetCurrentAcademicYearAsync();
            return academicYear != null ? Results.Ok(academicYear) : Results.NotFound("No current academic year found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving current academic year: {ex.Message}");
        }
    }

    private static async Task<IResult> CreateAcademicYear(CreateAcademicYearDto academicYearDto, IAcademicYearService academicYearService)
    {
        try
        {
            var academicYearId = await academicYearService.CreateAcademicYearAsync(academicYearDto);
            return Results.Created($"/api/academic-years/{academicYearId}", academicYearId);
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error creating academic year: {ex.Message}");
        }
    }

    private static async Task<IResult> UpdateAcademicYear(Guid id, UpdateAcademicYearDto academicYearDto, IAcademicYearService academicYearService)
    {
        try
        {
            var success = await academicYearService.UpdateAcademicYearAsync(id, academicYearDto);
            return success ? Results.NoContent() : Results.NotFound($"Academic year with ID {id} not found");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error updating academic year: {ex.Message}");
        }
    }

    private static async Task<IResult> DeleteAcademicYear(Guid id, IAcademicYearService academicYearService)
    {
        try
        {
            var success = await academicYearService.DeleteAcademicYearAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Academic year with ID {id} not found");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error deleting academic year: {ex.Message}");
        }
    }

    private static async Task<IResult> ActivateAcademicYear(Guid id, IAcademicYearService academicYearService)
    {
        try
        {
            var success = await academicYearService.ActivateAcademicYearAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Academic year with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error activating academic year: {ex.Message}");
        }
    }

    private static async Task<IResult> DeactivateAcademicYear(Guid id, IAcademicYearService academicYearService)
    {
        try
        {
            var success = await academicYearService.DeactivateAcademicYearAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Academic year with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error deactivating academic year: {ex.Message}");
        }
    }

    private static async Task<IResult> SetCurrentAcademicYear(Guid id, IAcademicYearService academicYearService)
    {
        try
        {
            var success = await academicYearService.SetCurrentAcademicYearAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Academic year with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error setting current academic year: {ex.Message}");
        }
    }

    private static async Task<IResult> ValidateAcademicYearName(string name, [FromQuery] Guid? excludeId, IAcademicYearService academicYearService)
    {
        try
        {
            var isUnique = await academicYearService.IsAcademicYearNameUniqueAsync(name, excludeId);
            return Results.Ok(isUnique);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error validating academic year name: {ex.Message}");
        }
    }

    private static async Task<IResult> GetAcademicYearStatistics(IAcademicYearService academicYearService)
    {
        try
        {
            var statistics = await academicYearService.GetAcademicYearStatisticsAsync();
            return Results.Ok(statistics);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving academic year statistics: {ex.Message}");
        }
    }

    #endregion
}
