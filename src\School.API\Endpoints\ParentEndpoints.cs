using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.DTOs;
using School.Application.Features.Parent;
using School.Domain.Enums;

namespace School.API.Endpoints;

public class ParentEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/parents").WithTags("Parents");

        // Get all parents with filtering and pagination
        group.MapGet("/", async ([AsParameters] ParentFilterDto filter, [FromServices] IParentService parentService) =>
        {
            var (parents, totalCount) = await parentService.GetAllParentsAsync(filter);
            var response = new { TotalCount = totalCount, Items = parents };
            return ApiResults.ApiOk(response, "Parents retrieved successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("GetAllParents")
        .WithOpenApi();

        // Get parent by ID
        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IParentService parentService) =>
        {
            var parent = await parentService.GetParentByIdAsync(id);
            if (parent == null)
            {
                return ApiResults.ApiNotFound("Parent not found");
            }
            return ApiResults.ApiOk(parent, "Parent retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetParentById")
        .WithOpenApi();

        // Get parent by user ID
        group.MapGet("/user/{userId}", async ([FromRoute] string userId, [FromServices] IParentService parentService) =>
        {
            var parent = await parentService.GetParentByUserIdAsync(userId);
            if (parent == null)
            {
                return ApiResults.ApiNotFound("Parent not found");
            }
            return ApiResults.ApiOk(parent, "Parent retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetParentByUserId")
        .WithOpenApi();

        // Create new parent
        group.MapPost("/", async ([FromBody] CreateParentDto parentDto, [FromServices] IParentService parentService) =>
        {
            var parentId = await parentService.CreateParentAsync(parentDto);
            return ApiResults.ApiCreated($"/api/parents/{parentId}", parentId.ToString(), "Parent created successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("CreateParent")
        .WithOpenApi();

        // Update parent
        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateParentDto parentDto, [FromServices] IParentService parentService) =>
        {
            var result = await parentService.UpdateParentAsync(id, parentDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Parent not found");
            }
            return ApiResults.ApiOk("Parent updated successfully");
        })
        .RequireAuthorization()
        .WithName("UpdateParent")
        .WithOpenApi();

        // Delete parent
        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IParentService parentService) =>
        {
            var result = await parentService.DeleteParentAsync(id);
            if (!result)
            {
                return ApiResults.ApiNotFound("Parent not found");
            }
            return ApiResults.ApiOk("Parent deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteParent")
        .WithOpenApi();

        // Student association endpoints
        var studentGroup = group.MapGroup("/{parentId}/students");

        // Get parent's students
        studentGroup.MapGet("/", async ([FromRoute] Guid parentId, [FromServices] IParentService parentService) =>
        {
            var students = await parentService.GetParentStudentsAsync(parentId);
            return ApiResults.ApiOk(students, "Students retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetParentStudents")
        .WithOpenApi();

        // Add student to parent
        studentGroup.MapPost("/{studentId}", async ([FromRoute] Guid parentId, [FromRoute] Guid studentId, [FromQuery] ParentRelationType relationType, [FromQuery] bool isPrimaryContact, [FromServices] IParentService parentService) =>
        {
            var result = await parentService.AddStudentAsync(parentId, studentId, relationType, isPrimaryContact);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Student already associated with this parent or parent/student not found");
            }
            return ApiResults.ApiOk("Student added to parent successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("AddStudentToParent")
        .WithOpenApi();

        // Update student-parent relationship
        studentGroup.MapPut("/{studentId}", async ([FromRoute] Guid parentId, [FromRoute] Guid studentId, [FromQuery] ParentRelationType relationType, [FromQuery] bool isPrimaryContact, [FromServices] IParentService parentService) =>
        {
            var result = await parentService.UpdateStudentRelationAsync(parentId, studentId, relationType, isPrimaryContact);
            if (!result)
            {
                return ApiResults.ApiNotFound("Student-parent relationship not found");
            }
            return ApiResults.ApiOk("Student-parent relationship updated successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("UpdateStudentParentRelationFromParent")
        .WithOpenApi();

        // Remove student from parent
        studentGroup.MapDelete("/{studentId}", async ([FromRoute] Guid parentId, [FromRoute] Guid studentId, [FromServices] IParentService parentService) =>
        {
            var result = await parentService.RemoveStudentAsync(parentId, studentId);
            if (!result)
            {
                return ApiResults.ApiNotFound("Student-parent relationship not found");
            }
            return ApiResults.ApiOk("Student removed from parent successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("RemoveStudentFromParent")
        .WithOpenApi();

        // Parent Portal endpoints
        var portalGroup = group.MapGroup("/{parentId}/portal");

        // Get student attendance
        portalGroup.MapGet("/{studentId}/attendance", async ([FromRoute] Guid parentId, [FromRoute] Guid studentId, [AsParameters] ParentAttendanceFilterDto filter, [FromServices] IParentService parentService) =>
        {
            var attendance = await parentService.GetStudentAttendanceAsync(parentId, studentId, filter.FromDate, filter.ToDate);
            return ApiResults.ApiOk(attendance, "Student attendance retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetStudentAttendanceForParent")
        .WithOpenApi();

        // Get student fees
        portalGroup.MapGet("/{studentId}/fees", async ([FromRoute] Guid parentId, [FromRoute] Guid studentId, [AsParameters] StudentFeeFilterDto filter, [FromServices] IParentService parentService) =>
        {
            var fees = await parentService.GetStudentFeesAsync(parentId, studentId, !string.IsNullOrEmpty(filter.AcademicYear) && int.TryParse(filter.AcademicYear, out var academicYear) ? academicYear : null);
            return ApiResults.ApiOk(fees, "Student fees retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetStudentFeesForParent")
        .WithOpenApi();

        // Get student results
        portalGroup.MapGet("/{studentId}/results", async ([FromRoute] Guid parentId, [FromRoute] Guid studentId, [AsParameters] StudentResultFilterDto2 filter, [FromServices] IParentService parentService) =>
        {
            var results = await parentService.GetStudentResultsAsync(parentId, studentId, filter.AcademicYear, filter.ExamType);
            return ApiResults.ApiOk(results, "Student results retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetStudentResultsForParent")
        .WithOpenApi();

        // Get student leaves
        portalGroup.MapGet("/{studentId}/leaves", async ([FromRoute] Guid parentId, [FromRoute] Guid studentId, [AsParameters] StudentLeaveFilterDto filter, [FromServices] IParentService parentService) =>
        {
            var leaves = await parentService.GetStudentLeavesAsync(parentId, studentId, filter.FromDate, filter.ToDate, filter.Status);
            return ApiResults.ApiOk(leaves, "Student leaves retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetStudentLeavesForParent")
        .WithOpenApi();

        // Create student leave
        portalGroup.MapPost("/{studentId}/leaves", async ([FromRoute] Guid parentId, [FromBody] CreateStudentLeaveDto leaveDto, [FromServices] IParentService parentService) =>
        {
            try
            {
                var leaveId = await parentService.CreateStudentLeaveAsync(parentId, leaveDto);
                return ApiResults.ApiCreated($"/api/parents/{parentId}/portal/{leaveDto.StudentId}/leaves/{leaveId}", leaveId.ToString(), "Leave application submitted successfully");
            }
            catch (UnauthorizedAccessException)
            {
                return ApiResults.ApiForbidden("You do not have permission to create leave for this student");
            }
        })
        .RequireAuthorization()
        .WithName("CreateStudentLeaveByParent")
        .WithOpenApi();
    }
}
