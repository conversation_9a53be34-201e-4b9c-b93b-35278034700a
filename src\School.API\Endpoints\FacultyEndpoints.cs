using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using School.API.Common;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Domain.Entities;
using System.Text.Json;
using School.Application.Features.Faculty;

namespace School.API.Endpoints;

public class FacultyEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/faculty").WithTags("Faculty");

        group.MapGet("/", async ([AsParameters] FacultyFilterDto filter, [FromServices] IFacultyService facultyService) =>
        {
            var (faculties, totalCount) = await facultyService.GetAllFacultiesAsync(filter);
            var response = new { TotalCount = totalCount, Items = faculties };
            return ApiResults.ApiOk(response, "Faculties retrieved successfully");
        }).WithName("GetAllFaculties").WithOpenApi();

        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IFacultyService facultyService) =>
        {
            var faculty = await facultyService.GetFacultyByIdAsync(id);
            if (faculty == null)
            {
                return ApiResults.ApiNotFound("Faculty not found");
            }
            return ApiResults.ApiOk(faculty, "Faculty retrieved successfully");
        }).WithName("GetFacultyById").WithOpenApi();

        group.MapPost("/", async ([FromBody] FacultyCreateDto facultyDto, [FromServices] IFacultyService facultyService) =>
        {
            var facultyId = await facultyService.CreateFacultyAsync(facultyDto);
            return ApiResults.ApiCreated(new { id = facultyId }, $"/api/faculty/{facultyId}", "Faculty created successfully");
        }).WithName("CreateFaculty").WithOpenApi();

        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] FacultyUpdateDto facultyDto, [FromServices] IFacultyService facultyService) =>
        {
            var updated = await facultyService.UpdateFacultyAsync(id, facultyDto);
            if (!updated)
            {
                return ApiResults.ApiNotFound("Faculty not found");
            }
            return ApiResults.ApiOk(new { id }, "Faculty updated successfully");
        }).WithName("UpdateFaculty").WithOpenApi();

        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IFacultyService facultyService) =>
        {
            var deleted = await facultyService.DeleteFacultyAsync(id);
            if (!deleted)
            {
                return ApiResults.ApiNotFound("Faculty not found");
            }
            return ApiResults.ApiOk("Faculty deleted successfully");
        }).WithName("DeleteFaculty").WithOpenApi();
    }
}
