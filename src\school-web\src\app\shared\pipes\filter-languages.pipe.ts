import { Pipe, PipeTransform } from '@angular/core';

interface Language {
  code: string;
  name: string;
}

@Pipe({
  name: 'filterLanguages',
  standalone: true
})
export class FilterLanguagesPipe implements PipeTransform {
  transform(languages: Language[], excludeCode: string): Language[] {
    if (!languages || !excludeCode) {
      return languages;
    }
    
    return languages.filter(lang => lang.code !== excludeCode);
  }
}
