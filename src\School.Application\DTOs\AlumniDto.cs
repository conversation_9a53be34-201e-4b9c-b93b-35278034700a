using School.Application.DTOs.Common;

namespace School.Application.DTOs;

public class AlumniDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public int GraduationYear { get; set; }
    public string Profession { get; set; } = string.Empty;
    public string Organization { get; set; } = string.Empty;
    public string Designation { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string Achievements { get; set; } = string.Empty;
    public string LinkedInProfile { get; set; } = string.Empty;
    public string FacebookProfile { get; set; } = string.Empty;
    public string TwitterProfile { get; set; } = string.Empty;
    public bool IsFeatured { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public Guid? ProfileImageId { get; set; }
    public MediaItemDto? ProfileImage { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public List<AlumniTranslationDto> Translations { get; set; } = new List<AlumniTranslationDto>();
    public List<AlumniTestimonialDto> Testimonials { get; set; } = new List<AlumniTestimonialDto>();
}

public class AlumniTranslationDto
{
    public Guid Id { get; set; }
    public Guid AlumniId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Profession { get; set; } = string.Empty;
    public string Organization { get; set; } = string.Empty;
    public string Designation { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string Achievements { get; set; } = string.Empty;
}

public class AlumniTestimonialDto
{
    public Guid Id { get; set; }
    public Guid AlumniId { get; set; }
    public string Content { get; set; } = string.Empty;
    public bool IsApproved { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public List<AlumniTestimonialTranslationDto> Translations { get; set; } = new List<AlumniTestimonialTranslationDto>();
}

public class AlumniTestimonialTranslationDto
{
    public Guid Id { get; set; }
    public Guid AlumniTestimonialId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
}

public class CreateAlumniDto
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public int GraduationYear { get; set; }
    public string Profession { get; set; } = string.Empty;
    public string Organization { get; set; } = string.Empty;
    public string Designation { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string Achievements { get; set; } = string.Empty;
    public string LinkedInProfile { get; set; } = string.Empty;
    public string FacebookProfile { get; set; } = string.Empty;
    public string TwitterProfile { get; set; } = string.Empty;
    public bool IsFeatured { get; set; }
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }
    public Guid? ProfileImageId { get; set; }
    public List<CreateAlumniTranslationDto>? Translations { get; set; }
}

public class UpdateAlumniDto
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public int GraduationYear { get; set; }
    public string Profession { get; set; } = string.Empty;
    public string Organization { get; set; } = string.Empty;
    public string Designation { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string Achievements { get; set; } = string.Empty;
    public string LinkedInProfile { get; set; } = string.Empty;
    public string FacebookProfile { get; set; } = string.Empty;
    public string TwitterProfile { get; set; } = string.Empty;
    public bool IsFeatured { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public Guid? ProfileImageId { get; set; }
}

public class CreateAlumniTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Profession { get; set; } = string.Empty;
    public string Organization { get; set; } = string.Empty;
    public string Designation { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string Achievements { get; set; } = string.Empty;
}

public class UpdateAlumniTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Profession { get; set; } = string.Empty;
    public string Organization { get; set; } = string.Empty;
    public string Designation { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string Achievements { get; set; } = string.Empty;
}

public class CreateAlumniTestimonialDto
{
    public Guid AlumniId { get; set; }
    public string Content { get; set; } = string.Empty;
    public bool IsApproved { get; set; } = false;
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }
    public List<CreateAlumniTestimonialTranslationDto>? Translations { get; set; }
}

public class UpdateAlumniTestimonialDto
{
    public string Content { get; set; } = string.Empty;
    public bool IsApproved { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
}

public class CreateAlumniTestimonialTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
}

public class UpdateAlumniTestimonialTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
}

/// <summary>
/// Filter DTO for alumni queries
/// </summary>
public class AlumniFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filter by alumni name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Filter by graduation year
    /// </summary>
    public int? GraduationYear { get; set; }

    /// <summary>
    /// Filter by profession
    /// </summary>
    public string? Profession { get; set; }

    /// <summary>
    /// Filter by organization
    /// </summary>
    public string? Organization { get; set; }

    /// <summary>
    /// Filter by featured status
    /// </summary>
    public bool? IsFeatured { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }
}
