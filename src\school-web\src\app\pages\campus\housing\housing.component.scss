@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section styles are now handled by the DefaultHeroComponent

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Section Styles
section {
  padding: $section-padding;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: $dark-gray;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Introduction Section
.intro-section {
  background-color: $white;

  .intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Residence Halls Section
.residence-section {
  background-color: $light-gray;

  ::ng-deep .mat-mdc-tab-header {
    margin-bottom: 30px;
  }

  .residences-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
    gap: 30px;
    padding: 20px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    .residence-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .residence-image {
        height: 250px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;

        h3 {
          font-size: 1.8rem;
          margin-bottom: 5px;
          color: $text-color;
        }

        .residence-capacity {
          font-weight: 500;
          color: $primary-color;
          margin-bottom: 15px;
        }

        .residence-description {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 20px;
        }

        .residence-details {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;

          @media (max-width: 576px) {
            grid-template-columns: 1fr;
          }

          .detail-section {
            h4 {
              font-size: 1.2rem;
              margin-bottom: 10px;
              color: $text-color;
              padding-bottom: 5px;
              border-bottom: 2px solid $medium-gray;
            }

            ul {
              padding-left: 20px;
              margin-bottom: 0;

              li {
                color: $dark-gray;
                margin-bottom: 5px;

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }
    }
  }
}

// Programs Section
.programs-section {
  background-color: $white;

  .programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .program-card {
      background-color: $light-gray;
      border-radius: $border-radius;
      padding: 30px;
      text-align: center;
      transition: transform 0.3s, box-shadow 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: $box-shadow;
      }

      .program-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 70px;
        height: 70px;
        background-color: $primary-color;
        border-radius: 50%;
        margin: 0 auto 20px;

        mat-icon {
          font-size: 35px;
          height: 35px;
          width: 35px;
          color: $white;
        }
      }

      h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        color: $text-color;
      }

      p {
        color: $dark-gray;
        line-height: 1.6;
        margin-bottom: 0;
      }
    }
  }
}

// Staff Section
.staff-section {
  background-color: $light-gray;

  .staff-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .staff-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .staff-image {
        height: 250px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 5px;
          color: $text-color;
        }

        .staff-title {
          font-weight: 500;
          color: $primary-color;
          margin-bottom: 15px;
        }

        .staff-description {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }
}

// Daily Life Section
.daily-life-section {
  background-color: $white;

  .daily-life-container {
    display: flex;
    align-items: center;
    gap: 40px;
    margin-top: 40px;

    @media (max-width: 992px) {
      flex-direction: column;
    }

    .daily-life-image {
      flex: 1;
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;

      img {
        width: 100%;
        height: auto;
        display: block;
      }
    }

    .daily-life-content {
      flex: 1;

      .schedule-item {
        display: flex;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid $medium-gray;

        &:last-child {
          margin-bottom: 0;
          padding-bottom: 0;
          border-bottom: none;
        }

        .schedule-time {
          width: 150px;
          font-weight: 500;
          color: $primary-color;
          flex-shrink: 0;
        }

        .schedule-activity {
          color: $text-color;
        }
      }
    }
  }

  .daily-life-note {
    display: flex;
    align-items: flex-start;
    background-color: $light-gray;
    border-radius: $border-radius;
    padding: 20px;
    max-width: 800px;
    margin: 30px auto 0;

    mat-icon {
      color: $primary-color;
      margin-right: 15px;
      margin-top: 3px;
    }

    p {
      margin: 0;
      color: $text-color;
      line-height: 1.6;
      font-size: 0.95rem;
    }
  }
}

// FAQs Section
.faqs-section {
  background-color: $light-gray;

  .faqs-container {
    max-width: 800px;
    margin: 40px auto 0;

    ::ng-deep .mat-expansion-panel {
      margin-bottom: 15px;
      border-radius: $border-radius;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ::ng-deep .mat-expansion-panel-header {
      padding: 20px;
    }

    ::ng-deep .mat-expansion-panel-header-title {
      color: $text-color;
      font-weight: 500;
      font-size: 1.1rem;
    }

    p {
      color: $dark-gray;
      line-height: 1.6;
      margin-bottom: 0;
    }
  }
}

// Application Section
.application-section {
  background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -15%));
  color: $white;

  .application-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h2 {
      color: $white;

      &:after {
        background-color: $white;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
    }

    .application-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;

      a {
        padding: 10px 30px;
        font-size: 1.1rem;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  section {
    padding: 60px 0;

    h2 {
      font-size: 2rem;
    }
  }

  .programs-grid, .staff-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .application-buttons {
    flex-direction: column;
    align-items: center;

    a {
      width: 100%;
      max-width: 300px;
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

@media (max-width: 576px) {
  section h2 {
    font-size: 1.8rem;
  }

  .programs-grid, .staff-grid {
    grid-template-columns: 1fr;
  }

  .schedule-item {
    flex-direction: column;

    .schedule-time {
      width: 100% !important;
      margin-bottom: 5px;
    }
  }
}
