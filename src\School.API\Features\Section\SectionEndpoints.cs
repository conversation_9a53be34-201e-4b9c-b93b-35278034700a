using <PERSON>;
using Microsoft.AspNetCore.Mvc;
using School.Application.Features.Section;
using School.Application.DTOs;

namespace School.API.Features.Section;

/// <summary>
/// Section management API endpoints
/// </summary>
public class SectionEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/sections")
            .WithTags("Sections")
            .RequireAuthorization();

        // Section CRUD operations
        group.MapGet("/", GetAllSections)
            .WithName("GetAllSections")
            .WithSummary("Get all sections with filtering and pagination")
            .Produces<object>();

        group.MapGet("/{id:guid}", GetSectionById)
            .WithName("GetSectionById")
            .WithSummary("Get section by ID")
            .Produces<object>()
            .Produces(404);

        group.MapPost("/", CreateSection)
            .WithName("CreateSection")
            .WithSummary("Create a new section")
            .Produces<Guid>(201)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        group.MapPut("/{id:guid}", UpdateSection)
            .WithName("UpdateSection")
            .WithSummary("Update an existing section")
            .Produces(204)
            .Produces(400)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapDelete("/{id:guid}", DeleteSection)
            .WithName("DeleteSection")
            .WithSummary("Delete a section")
            .Produces(204)
            .Produces(400)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapPatch("/{id:guid}/activate", ActivateSection)
            .WithName("ActivateSection")
            .WithSummary("Activate a section")
            .Produces(204)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapPatch("/{id:guid}/deactivate", DeactivateSection)
            .WithName("DeactivateSection")
            .WithSummary("Deactivate a section")
            .Produces(204)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        // Section validation
        group.MapGet("/validate/name/{name}", ValidateSectionName)
            .WithName("ValidateSectionName")
            .WithSummary("Check if section name is unique within grade")
            .Produces<bool>();

        // Section analytics
        group.MapGet("/statistics", GetSectionStatistics)
            .WithName("GetSectionStatistics")
            .WithSummary("Get section statistics")
            .Produces<object>();

        // Section-specific operations
        group.MapGet("/{id:guid}/students", GetSectionStudents)
            .WithName("GetSectionStudents")
            .WithSummary("Get students in a section")
            .Produces<object>();

        group.MapGet("/grade/{gradeId:guid}", GetSectionsByGrade)
            .WithName("GetSectionsByGrade")
            .WithSummary("Get sections for a grade")
            .Produces<object>();

        // Bulk operations
        group.MapPost("/bulk/create", BulkCreateSections)
            .WithName("BulkCreateSections")
            .WithSummary("Create multiple sections")
            .Produces(201)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        group.MapPost("/bulk/assign-students", BulkAssignStudents)
            .WithName("BulkAssignStudents")
            .WithSummary("Assign multiple students to sections")
            .Produces(200)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");
    }

    #region Endpoint Implementations

    private static async Task<IResult> GetAllSections(
        ISectionService sectionService,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] Guid? gradeId = null,
        [FromQuery] Guid? academicYearId = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string sortBy = "Name",
        [FromQuery] bool sortDescending = false)
    {
        try
        {
            var filter = new SectionFilterDto
            {
                SearchTerm = searchTerm,
                IsActive = isActive,
                GradeId = gradeId,
                AcademicYearId = academicYearId,
                Page = page,
                PageSize = pageSize,
                SortBy = sortBy,
                SortDescending = sortDescending
            };

            var (sections, totalCount) = await sectionService.GetAllSectionsAsync(filter);
            
            var response = new
            {
                data = sections,
                totalCount,
                page = filter.Page,
                pageSize = filter.PageSize,
                totalPages = (int)Math.Ceiling((double)totalCount / filter.PageSize)
            };
            
            return Results.Ok(response);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving sections: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSectionById(Guid id, ISectionService sectionService)
    {
        try
        {
            var section = await sectionService.GetSectionByIdAsync(id);
            return section != null ? Results.Ok(section) : Results.NotFound($"Section with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving section: {ex.Message}");
        }
    }

    private static async Task<IResult> CreateSection(CreateSectionDto sectionDto, ISectionService sectionService)
    {
        try
        {
            var sectionId = await sectionService.CreateSectionAsync(sectionDto);
            return Results.Created($"/api/sections/{sectionId}", sectionId);
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error creating section: {ex.Message}");
        }
    }

    private static async Task<IResult> UpdateSection(Guid id, UpdateSectionDto sectionDto, ISectionService sectionService)
    {
        try
        {
            var success = await sectionService.UpdateSectionAsync(id, sectionDto);
            return success ? Results.NoContent() : Results.NotFound($"Section with ID {id} not found");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error updating section: {ex.Message}");
        }
    }

    private static async Task<IResult> DeleteSection(Guid id, ISectionService sectionService)
    {
        try
        {
            var success = await sectionService.DeleteSectionAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Section with ID {id} not found");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error deleting section: {ex.Message}");
        }
    }

    private static async Task<IResult> ActivateSection(Guid id, ISectionService sectionService)
    {
        try
        {
            var success = await sectionService.ActivateSectionAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Section with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error activating section: {ex.Message}");
        }
    }

    private static async Task<IResult> DeactivateSection(Guid id, ISectionService sectionService)
    {
        try
        {
            var success = await sectionService.DeactivateSectionAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Section with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error deactivating section: {ex.Message}");
        }
    }

    private static async Task<IResult> ValidateSectionName(string name, [FromQuery] Guid? gradeId, [FromQuery] Guid? excludeId, ISectionService sectionService)
    {
        try
        {
            var isUnique = await sectionService.IsSectionNameUniqueAsync(name, gradeId, excludeId);
            return Results.Ok(isUnique);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error validating section name: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSectionStatistics(ISectionService sectionService)
    {
        try
        {
            var statistics = await sectionService.GetSectionStatisticsAsync();
            return Results.Ok(statistics);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving section statistics: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSectionStudents(Guid id, ISectionService sectionService)
    {
        try
        {
            var students = await sectionService.GetSectionStudentsAsync(id);
            return Results.Ok(students);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving section students: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSectionsByGrade(Guid gradeId, ISectionService sectionService)
    {
        try
        {
            var sections = await sectionService.GetSectionsByGradeAsync(gradeId);
            return Results.Ok(sections);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving sections by grade: {ex.Message}");
        }
    }

    private static async Task<IResult> BulkCreateSections(IEnumerable<CreateSectionDto> sections, ISectionService sectionService)
    {
        try
        {
            var success = await sectionService.BulkCreateSectionsAsync(sections);
            return success ? Results.Created("", null) : Results.BadRequest("Failed to create sections");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error bulk creating sections: {ex.Message}");
        }
    }

    private static async Task<IResult> BulkAssignStudents(object assignmentData, ISectionService sectionService)
    {
        try
        {
            // Implementation would depend on the specific DTO structure
            // var success = await sectionService.BulkAssignStudentsAsync(assignmentData);
            return Results.Ok("Students assigned successfully");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error bulk assigning students: {ex.Message}");
        }
    }

    #endregion
}
