using School.Domain.Common;

using System;

namespace School.Domain.Entities
{
    public class ClubGalleryItem : BaseEntity
    {
        public Guid ClubId { get; set; }
        public Guid? MediaId { get; set; }
        public string ImageUrl { get; set; }
        public string Caption { get; set; }
        public int DisplayOrder { get; set; }
        
        // Navigation properties
        public Club Club { get; set; }
    }
}
