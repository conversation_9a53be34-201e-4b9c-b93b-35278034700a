using Microsoft.AspNetCore.Http;

namespace School.API.Common;

/// <summary>
/// Static methods for creating standardized API responses
/// </summary>
public static class ApiResults
{
    /// <summary>
    /// Creates a successful response with data
    /// </summary>
    public static IResult ApiOk<T>(T data, string? message = null)
    {
        return Microsoft.AspNetCore.Http.TypedResults.Ok(ApiResponse<T>.SuccessResponse(data, message));
    }

    /// <summary>
    /// Creates a successful response without data
    /// </summary>
    public static IResult ApiOk(string? message = null)
    {
        return Microsoft.AspNetCore.Http.TypedResults.Ok(ApiResponse.SuccessResponse(message));
    }

    /// <summary>
    /// Creates a created response with data
    /// </summary>
    public static IResult ApiCreated<T>(T data, string? location = null, string? message = null)
    {
        if (string.IsNullOrEmpty(location))
        {
            return Microsoft.AspNetCore.Http.TypedResults.Created(string.Empty, ApiResponse<T>.SuccessResponse(data, message));
        }

        return Microsoft.AspNetCore.Http.TypedResults.Created(location, ApiResponse<T>.SuccessResponse(data, message));
    }

    /// <summary>
    /// Creates a not found response
    /// </summary>
    public static IResult ApiNotFound(string message = "Resource not found")
    {
        return Microsoft.AspNetCore.Http.TypedResults.NotFound(ApiResponse.ErrorResponse(message, StatusCodes.Status404NotFound));
    }

    /// <summary>
    /// Creates a bad request response
    /// </summary>
    public static IResult ApiBadRequest(string message)
    {
        return Microsoft.AspNetCore.Http.TypedResults.BadRequest(ApiResponse.ErrorResponse(message, StatusCodes.Status400BadRequest));
    }

    /// <summary>
    /// Creates an unauthorized response
    /// </summary>
    public static IResult ApiUnauthorized(string message = "Authentication required. Please log in to access this resource.")
    {
        var response = ApiResponse.ErrorResponse(message, StatusCodes.Status401Unauthorized);
        return Microsoft.AspNetCore.Http.Results.Json(response, statusCode: StatusCodes.Status401Unauthorized);
    }

    /// <summary>
    /// Creates a forbidden response
    /// </summary>
    public static IResult ApiForbidden(string message = "Forbidden")
    {
        return Microsoft.AspNetCore.Http.Results.Json(ApiResponse.ErrorResponse(message, StatusCodes.Status403Forbidden), statusCode: StatusCodes.Status403Forbidden);
    }

    /// <summary>
    /// Creates a server error response
    /// </summary>
    public static IResult ApiServerError(string message = "Internal server error")
    {
        return Microsoft.AspNetCore.Http.Results.Json(ApiResponse.ErrorResponse(message, StatusCodes.Status500InternalServerError), statusCode: StatusCodes.Status500InternalServerError);
    }

    /// <summary>
    /// Creates a no content response
    /// </summary>
    public static IResult ApiNoContent()
    {
        return Microsoft.AspNetCore.Http.Results.NoContent();
    }
}
