.notice-detail-container {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      display: flex;
      align-items: center;
      
      h1 {
        margin: 0 0 0 8px;
        font-size: 24px;
        font-weight: 500;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .loading-container, .error-container {
    display: flex;
    justify-content: center;
    padding: 40px 0;
  }
  
  .error-card {
    max-width: 500px;
    width: 100%;
    
    mat-card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      
      mat-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        margin-bottom: 16px;
      }
      
      p {
        margin-bottom: 16px;
        font-size: 16px;
      }
    }
  }
  
  .detail-container {
    .notice-card {
      mat-card-header {
        margin-bottom: 16px;
        
        mat-card-title {
          font-size: 20px;
          margin-bottom: 8px;
        }
        
        .notice-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          align-items: center;
          
          .notice-category, .notice-date {
            margin-right: 16px;
          }
        }
      }
      
      .notice-content {
        padding: 16px;
        
        h3 {
          margin-top: 0;
          margin-bottom: 16px;
          font-size: 18px;
          font-weight: 500;
        }
        
        p {
          margin-bottom: 24px;
          white-space: pre-line;
        }
        
        .notice-details {
          margin-top: 24px;
          border-top: 1px solid rgba(0, 0, 0, 0.12);
          padding-top: 16px;
          
          .detail-item {
            margin-bottom: 8px;
            display: flex;
            
            .detail-label {
              font-weight: 500;
              min-width: 120px;
              margin-right: 16px;
            }
          }
        }
      }
    }
  }
  
  // Priority chip styles
  .priority-low {
    background-color: #8bc34a;
    color: white;
  }
  
  .priority-medium {
    background-color: #ffc107;
    color: rgba(0, 0, 0, 0.87);
  }
  
  .priority-high {
    background-color: #ff9800;
    color: white;
  }
  
  .priority-urgent {
    background-color: #f44336;
    color: white;
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      
      .header-actions {
        margin-top: 16px;
      }
    }
  }
}
