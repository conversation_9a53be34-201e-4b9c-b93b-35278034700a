<!-- Hero Section -->
<app-default-hero
  translationPrefix="CAMPUS_LIFE"
  title="CAMPUS_LIFE.HOUSING"
  subtitle="CAMPUS_LIFE.HOUSING_SUBTITLE"
  theme="dark"
  size="large"
  alignment="center">
</app-default-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'CAMPUS_LIFE.HOUSING_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.HOUSING_INTRO_P1' | translate }}</p>
      <p>{{ 'CAMPUS_LIFE.HOUSING_INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Residence Halls Section -->
  <section class="residence-section">
    <h2>{{ 'CAMPUS_LIFE.RESIDENCE_HALLS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.RESIDENCE_INTRO' | translate }}</p>

    <mat-tab-group animationDuration="300ms">
      <mat-tab *ngFor="let level of gradeLevels" [label]="level">
        <div class="residences-grid">
          <mat-card class="residence-card" *ngFor="let residence of getResidencesByGradeLevel(level)">
            <div class="residence-image">
              <img [src]="residence.image" [alt]="residence.name">
            </div>
            <mat-card-content>
              <h3>{{residence.name}}</h3>
              <div class="residence-capacity">{{ 'CAMPUS_LIFE.CAPACITY' | translate }}: {{residence.capacity}}</div>
              <p class="residence-description">{{residence.description}}</p>

              <div class="residence-details">
                <div class="detail-section">
                  <h4>{{ 'CAMPUS_LIFE.ROOM_TYPES' | translate }}</h4>
                  <ul>
                    <li *ngFor="let roomType of residence.roomTypes">{{roomType}}</li>
                  </ul>
                </div>

                <div class="detail-section">
                  <h4>{{ 'CAMPUS_LIFE.AMENITIES' | translate }}</h4>
                  <ul>
                    <li *ngFor="let amenity of residence.amenities">{{amenity}}</li>
                  </ul>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>
    </mat-tab-group>
  </section>

  <!-- Residential Life Programs Section -->
  <section class="programs-section">
    <h2>{{ 'CAMPUS_LIFE.RESIDENTIAL_PROGRAMS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.PROGRAMS_INTRO' | translate }}</p>

    <div class="programs-grid">
      <div class="program-card" *ngFor="let program of residentialPrograms">
        <div class="program-icon">
          <mat-icon>{{program.icon}}</mat-icon>
        </div>
        <h3>{{program.title}}</h3>
        <p>{{program.description}}</p>
      </div>
    </div>
  </section>

  <!-- Residential Life Staff Section -->
  <section class="staff-section">
    <h2>{{ 'CAMPUS_LIFE.RESIDENTIAL_STAFF' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.STAFF_INTRO' | translate }}</p>

    <div class="staff-grid">
      <mat-card class="staff-card" *ngFor="let staff of residentialStaff">
        <div class="staff-image">
          <img [src]="staff.image" [alt]="staff.name">
        </div>
        <mat-card-content>
          <h3>{{staff.name}}</h3>
          <div class="staff-title">{{staff.title}}</div>
          <p class="staff-description">{{staff.description}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Daily Life Section -->
  <section class="daily-life-section">
    <h2>{{ 'CAMPUS_LIFE.DAILY_LIFE' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.DAILY_LIFE_INTRO' | translate }}</p>

    <div class="daily-life-container">
      <div class="daily-life-image">
        <img src="assets/images/campus/housing/daily-life.jpg" alt="Daily Life in Residence Halls">
      </div>
      <div class="daily-life-content">
        <div class="schedule-item">
          <div class="schedule-time">6:30 - 7:30 AM</div>
          <div class="schedule-activity">{{ 'CAMPUS_LIFE.WAKE_UP' | translate }}</div>
        </div>
        <div class="schedule-item">
          <div class="schedule-time">7:00 - 8:00 AM</div>
          <div class="schedule-activity">{{ 'CAMPUS_LIFE.BREAKFAST' | translate }}</div>
        </div>
        <div class="schedule-item">
          <div class="schedule-time">8:00 AM - 3:30 PM</div>
          <div class="schedule-activity">{{ 'CAMPUS_LIFE.CLASSES' | translate }}</div>
        </div>
        <div class="schedule-item">
          <div class="schedule-time">3:30 - 5:30 PM</div>
          <div class="schedule-activity">{{ 'CAMPUS_LIFE.ACTIVITIES' | translate }}</div>
        </div>
        <div class="schedule-item">
          <div class="schedule-time">5:30 - 6:30 PM</div>
          <div class="schedule-activity">{{ 'CAMPUS_LIFE.DINNER' | translate }}</div>
        </div>
        <div class="schedule-item">
          <div class="schedule-time">7:00 - 9:00 PM</div>
          <div class="schedule-activity">{{ 'CAMPUS_LIFE.STUDY_HALL' | translate }}</div>
        </div>
        <div class="schedule-item">
          <div class="schedule-time">9:00 - 10:00 PM</div>
          <div class="schedule-activity">{{ 'CAMPUS_LIFE.FREE_TIME' | translate }}</div>
        </div>
        <div class="schedule-item">
          <div class="schedule-time">10:00 - 10:30 PM</div>
          <div class="schedule-activity">{{ 'CAMPUS_LIFE.ROOM_CHECK' | translate }}</div>
        </div>
        <div class="schedule-item">
          <div class="schedule-time">10:30 PM</div>
          <div class="schedule-activity">{{ 'CAMPUS_LIFE.LIGHTS_OUT' | translate }}</div>
        </div>
      </div>
    </div>

    <div class="daily-life-note">
      <mat-icon>info</mat-icon>
      <p>{{ 'CAMPUS_LIFE.SCHEDULE_NOTE' | translate }}</p>
    </div>
  </section>

  <!-- FAQs Section -->
  <section class="faqs-section">
    <h2>{{ 'CAMPUS_LIFE.HOUSING_FAQS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.FAQS_INTRO' | translate }}</p>

    <div class="faqs-container">
      <mat-accordion>
        <mat-expansion-panel *ngFor="let faq of faqs">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{faq.question}}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <p>{{faq.answer}}</p>
        </mat-expansion-panel>
      </mat-accordion>
    </div>
  </section>

  <!-- Application Section -->
  <section class="application-section">
    <div class="application-content">
      <h2>{{ 'CAMPUS_LIFE.INTERESTED_IN_BOARDING' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.APPLICATION_TEXT' | translate }}</p>
      <div class="application-buttons">
        <a mat-raised-button color="primary" routerLink="/admissions">
          {{ 'CAMPUS_LIFE.APPLY_NOW' | translate }}
        </a>
        <a mat-stroked-button color="primary" routerLink="/contact">
          {{ 'CAMPUS_LIFE.CONTACT_RESIDENTIAL_LIFE' | translate }}
        </a>
      </div>
    </div>
  </section>
</div>
