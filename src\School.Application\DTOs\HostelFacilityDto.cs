using School.Application.DTOs.Common;
using School.Domain.Enums;

namespace School.Application.DTOs;

public class HostelFacilityDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public HostelType Type { get; set; }
    public GenderType Gender { get; set; }
    public int Capacity { get; set; }
    public decimal MonthlyFee { get; set; }
    public string Location { get; set; } = string.Empty;
    public string ContactInfo { get; set; } = string.Empty;
    public bool HasWifi { get; set; }
    public bool HasAC { get; set; }
    public bool HasMeals { get; set; }
    public bool HasLaundry { get; set; }
    public bool HasStudyRoom { get; set; }
    public bool HasRecreationRoom { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public List<HostelFacilityTranslationDto> Translations { get; set; } = new List<HostelFacilityTranslationDto>();
    public List<HostelImageDto> Images { get; set; } = new List<HostelImageDto>();
}

public class HostelFacilityTranslationDto
{
    public Guid Id { get; set; }
    public Guid HostelFacilityId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public class HostelImageDto
{
    public Guid Id { get; set; }
    public Guid HostelFacilityId { get; set; }
    public Guid MediaItemId { get; set; }
    public int DisplayOrder { get; set; }
    public MediaItemDto? MediaItem { get; set; }
}

public class CreateHostelFacilityDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public HostelType Type { get; set; }
    public GenderType Gender { get; set; }
    public int Capacity { get; set; }
    public decimal MonthlyFee { get; set; }
    public string Location { get; set; } = string.Empty;
    public string ContactInfo { get; set; } = string.Empty;
    public bool HasWifi { get; set; }
    public bool HasAC { get; set; }
    public bool HasMeals { get; set; }
    public bool HasLaundry { get; set; }
    public bool HasStudyRoom { get; set; }
    public bool HasRecreationRoom { get; set; }
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }
    public List<Guid> ImageIds { get; set; } = new List<Guid>();
}

public class UpdateHostelFacilityDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public HostelType Type { get; set; }
    public GenderType Gender { get; set; }
    public int Capacity { get; set; }
    public decimal MonthlyFee { get; set; }
    public string Location { get; set; } = string.Empty;
    public string ContactInfo { get; set; } = string.Empty;
    public bool HasWifi { get; set; }
    public bool HasAC { get; set; }
    public bool HasMeals { get; set; }
    public bool HasLaundry { get; set; }
    public bool HasStudyRoom { get; set; }
    public bool HasRecreationRoom { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public List<Guid> ImageIds { get; set; } = new List<Guid>();
}

public class CreateHostelFacilityTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public class UpdateHostelFacilityTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public class AddHostelImageDto
{
    public Guid MediaItemId { get; set; }
    public int DisplayOrder { get; set; }
}

/// <summary>
/// Filter DTO for hostel facility queries
/// </summary>
public class HostelFacilityFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filter by facility name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Filter by hostel type
    /// </summary>
    public HostelType? Type { get; set; }

    /// <summary>
    /// Filter by gender type
    /// </summary>
    public GenderType? Gender { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }
}
