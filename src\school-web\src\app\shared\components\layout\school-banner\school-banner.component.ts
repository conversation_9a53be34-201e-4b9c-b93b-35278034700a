import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
// Language selector is no longer used in this component

@Component({
  selector: 'app-school-banner',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    RouterModule,
    TranslateModule
  ],
  templateUrl: './school-banner.component.html',
  styleUrls: ['./school-banner.component.scss']
})
export class SchoolBannerComponent implements OnInit {
  isMobile: boolean = false;
  isVerySmallScreen: boolean = false;

  constructor() {}

  ngOnInit(): void {
    this.checkScreenSize();
  }

  @HostListener('window:resize', [])
  checkScreenSize(): void {
    const wasMobile = this.isMobile;
    const wasVerySmall = this.isVerySmallScreen;

    this.isMobile = window.innerWidth < 992; // Breakpoint for mobile view
    this.isVerySmallScreen = window.innerWidth < 480; // Breakpoint for very small screens

    // If screen size category changed, force a layout update
    if (wasMobile !== this.isMobile || wasVerySmall !== this.isVerySmallScreen) {
      this.updateLayout();
    }
  }

  /**
   * Update layout after screen size changes
   */
  private updateLayout(): void {
    // Force a change detection cycle
    setTimeout(() => {
      // Any additional layout updates can go here
    }, 0);
  }

  // No longer needed as the mobile menu toggle is now in the main-nav component
}
