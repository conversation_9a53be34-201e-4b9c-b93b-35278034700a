import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface Grade {
  id: string;
  name: string;
  code: string;
  level: number;
  educationLevel: string;
  description: string;
  minAge: number;
  maxAge: number;
  maxStudents: number;
  isActive: boolean;
  displayOrder: number;
  academicYearId: string;
  academicYearName: string;
  promotionCriteria: string;
  minPassingGrade: number;
  remarks: string;
  createdAt: Date;
  lastModifiedAt?: Date;
}

export interface CreateGradeDto {
  name: string;
  code: string;
  level: number;
  educationLevel: string;
  description: string;
  minAge: number;
  maxAge: number;
  maxStudents: number;
  isActive: boolean;
  displayOrder: number;
  academicYearId: string;
  promotionCriteria: string;
  minPassingGrade: number;
  remarks: string;
}

export interface UpdateGradeDto {
  name: string;
  code: string;
  level: number;
  educationLevel: string;
  description: string;
  minAge: number;
  maxAge: number;
  maxStudents: number;
  isActive: boolean;
  displayOrder: number;
  promotionCriteria: string;
  minPassingGrade: number;
  remarks: string;
}

export interface GradeFilterDto {
  page: number;
  pageSize: number;
  searchTerm?: string;
  academicYearId?: string;
  educationLevel?: string;
  isActive?: boolean;
  sortBy?: string;
  sortDirection?: string;
}

export interface GradeListResponse {
  data: Grade[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

@Injectable({
  providedIn: 'root'
})
export class GradeService {
  private http = inject(HttpClient);
  private apiUrl = `${environment.apiUrl}/api/Grade`;

  getGrades(filter: GradeFilterDto): Observable<GradeListResponse> {
    let params = new HttpParams()
      .set('page', filter.page.toString())
      .set('pageSize', filter.pageSize.toString());

    if (filter.searchTerm) {
      params = params.set('searchTerm', filter.searchTerm);
    }
    if (filter.academicYearId) {
      params = params.set('academicYearId', filter.academicYearId);
    }
    if (filter.educationLevel) {
      params = params.set('educationLevel', filter.educationLevel);
    }
    if (filter.isActive !== undefined) {
      params = params.set('isActive', filter.isActive.toString());
    }
    if (filter.sortBy) {
      params = params.set('sortBy', filter.sortBy);
    }
    if (filter.sortDirection) {
      params = params.set('sortDirection', filter.sortDirection);
    }

    return this.http.get<GradeListResponse>(this.apiUrl, { params });
  }

  getGrade(id: string): Observable<Grade> {
    return this.http.get<Grade>(`${this.apiUrl}/${id}`);
  }

  getGradesByAcademicYear(academicYearId: string): Observable<Grade[]> {
    return this.http.get<Grade[]>(`${this.apiUrl}/academic-year/${academicYearId}`);
  }

  getActiveGrades(academicYearId: string): Observable<Grade[]> {
    return this.http.get<Grade[]>(`${this.apiUrl}/academic-year/${academicYearId}/active`);
  }

  createGrade(grade: CreateGradeDto): Observable<{ id: string }> {
    return this.http.post<{ id: string }>(this.apiUrl, grade);
  }

  updateGrade(id: string, grade: UpdateGradeDto): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, grade);
  }

  deleteGrade(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  updateGradeStatus(id: string, isActive: boolean): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/status`, { isActive });
  }

  reorderGrades(gradeOrders: { id: string; displayOrder: number }[]): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/reorder`, { gradeOrders });
  }

  bulkUpdateGradeStatus(gradeIds: string[], isActive: boolean): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/bulk-status`, { gradeIds, isActive });
  }

  exportGrades(academicYearId: string): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/academic-year/${academicYearId}/export`, {
      responseType: 'blob'
    });
  }

  getGradeStatistics(academicYearId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/academic-year/${academicYearId}/statistics`);
  }

  validateGradeCode(code: string, academicYearId: string, excludeId?: string): Observable<{ isValid: boolean }> {
    let params = new HttpParams()
      .set('code', code)
      .set('academicYearId', academicYearId);

    if (excludeId) {
      params = params.set('excludeId', excludeId);
    }

    return this.http.get<{ isValid: boolean }>(`${this.apiUrl}/validate-code`, { params });
  }

  getEducationLevels(): Observable<{ value: string; label: string }[]> {
    return this.http.get<{ value: string; label: string }[]>(`${this.apiUrl}/education-levels`);
  }
}
