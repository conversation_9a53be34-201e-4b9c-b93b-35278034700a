using School.Domain.Entities;

namespace School.Application.Common.Interfaces;

/// <summary>
/// Service for managing tenant context and operations
/// </summary>
public interface ITenantService
{
    /// <summary>
    /// Gets the current tenant ID from the request context
    /// </summary>
    /// <returns>Current tenant ID or null if not set</returns>
    Guid? GetCurrentTenantId();

    /// <summary>
    /// Gets the current tenant organization
    /// </summary>
    /// <returns>Current organization or null if not found</returns>
    Task<Organization?> GetCurrentTenantAsync();

    /// <summary>
    /// Sets the current tenant context based on identifier (subdomain, custom domain, etc.)
    /// </summary>
    /// <param name="identifier">Tenant identifier (slug, domain, etc.)</param>
    /// <returns>True if tenant was found and set, false otherwise</returns>
    Task<bool> SetTenantAsync(string identifier);

    /// <summary>
    /// Checks if a user has access to a specific tenant
    /// </summary>
    /// <param name="userId">User ID to check</param>
    /// <param name="tenantId">Tenant ID to check access for</param>
    /// <returns>True if user has access, false otherwise</returns>
    Task<bool> UserHasAccessToTenantAsync(string userId, Guid tenantId);

    /// <summary>
    /// Gets all tenants that a user has access to
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>List of organizations the user has access to</returns>
    Task<IEnumerable<Organization>> GetUserTenantsAsync(string userId);

    /// <summary>
    /// Gets tenant by slug (subdomain identifier)
    /// </summary>
    /// <param name="slug">Tenant slug</param>
    /// <returns>Organization or null if not found</returns>
    Task<Organization?> GetTenantBySlugAsync(string slug);

    /// <summary>
    /// Gets tenant by ID
    /// </summary>
    /// <param name="tenantId">Tenant ID</param>
    /// <returns>Organization or null if not found</returns>
    Task<Organization?> GetTenantByIdAsync(Guid tenantId);

    /// <summary>
    /// Gets tenant by custom domain
    /// </summary>
    /// <param name="domain">Custom domain</param>
    /// <returns>Organization or null if not found</returns>
    Task<Organization?> GetTenantByDomainAsync(string domain);

    /// <summary>
    /// Validates if a tenant slug is available
    /// </summary>
    /// <param name="slug">Slug to validate</param>
    /// <param name="excludeTenantId">Tenant ID to exclude from validation (for updates)</param>
    /// <returns>True if slug is available, false otherwise</returns>
    Task<bool> IsSlugAvailableAsync(string slug, Guid? excludeTenantId = null);

    /// <summary>
    /// Validates if a custom domain is available
    /// </summary>
    /// <param name="domain">Domain to validate</param>
    /// <param name="excludeTenantId">Tenant ID to exclude from validation (for updates)</param>
    /// <returns>True if domain is available, false otherwise</returns>
    Task<bool> IsDomainAvailableAsync(string domain, Guid? excludeTenantId = null);

    /// <summary>
    /// Creates a new tenant organization
    /// </summary>
    /// <param name="organization">Organization to create</param>
    /// <returns>Created organization</returns>
    Task<Organization> CreateTenantAsync(Organization organization);

    /// <summary>
    /// Updates an existing tenant organization
    /// </summary>
    /// <param name="organization">Organization to update</param>
    /// <returns>Updated organization</returns>
    Task<Organization> UpdateTenantAsync(Organization organization);

    /// <summary>
    /// Deactivates a tenant (soft delete)
    /// </summary>
    /// <param name="tenantId">Tenant ID to deactivate</param>
    /// <returns>True if successful, false otherwise</returns>
    Task<bool> DeactivateTenantAsync(Guid tenantId);

    /// <summary>
    /// Adds a user to a tenant with specified role
    /// </summary>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="role">Role to assign</param>
    /// <returns>True if successful, false otherwise</returns>
    Task<bool> AddUserToTenantAsync(Guid tenantId, string userId, Domain.Enums.OrganizationRole role);

    /// <summary>
    /// Removes a user from a tenant
    /// </summary>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="userId">User ID</param>
    /// <returns>True if successful, false otherwise</returns>
    Task<bool> RemoveUserFromTenantAsync(Guid tenantId, string userId);

    /// <summary>
    /// Updates a user's role in a tenant
    /// </summary>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="role">New role</param>
    /// <returns>True if successful, false otherwise</returns>
    Task<bool> UpdateUserRoleInTenantAsync(Guid tenantId, string userId, Domain.Enums.OrganizationRole role);
}
