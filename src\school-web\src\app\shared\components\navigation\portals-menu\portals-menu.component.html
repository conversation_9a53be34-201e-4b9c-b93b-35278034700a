<div class="menu-item" (mouseenter)="openMenu(portalsMenuTrigger)" (mouseleave)="closeMenu()">
  <button mat-button [matMenuTriggerFor]="portalsMenu" #portalsMenuTrigger="matMenuTrigger"
          class="menu-trigger" [class.active]="activeRoute.startsWith('/student-portal') || activeRoute.startsWith('/parent-portal') || activeRoute.startsWith('/faculty-portal')">
    {{ 'NAV.PORTALS' | translate }}
    <mat-icon>arrow_drop_down</mat-icon>
  </button>
  <mat-menu #portalsMenu="matMenu" class="mega-menu-panel" [overlapTrigger]="false" [hasBackdrop]="false">
    <div class="mega-menu-content" (mouseenter)="clearCloseTimeout()" (mouseleave)="closeMenu()">
      <div class="menu-column">
        <h3>{{ 'PORTALS.TITLE' | translate }}</h3>
        <a mat-menu-item routerLink="/student-portal" [class.active]="activeRoute.startsWith('/student-portal')">
          <mat-icon>school</mat-icon>
          {{ 'PORTALS.STUDENT' | translate }}
        </a>
        <a mat-menu-item routerLink="/parent-portal" [class.active]="activeRoute.startsWith('/parent-portal')">
          <mat-icon>people</mat-icon>
          {{ 'PORTALS.PARENT' | translate }}
        </a>
        <a mat-menu-item routerLink="/faculty-portal" [class.active]="activeRoute.startsWith('/faculty-portal')">
          <mat-icon>person</mat-icon>
          {{ 'PORTALS.FACULTY' | translate }}
        </a>
      </div>
      <div class="menu-column">
        <h3>{{ 'PORTALS.RESOURCES' | translate }}</h3>
        <a mat-menu-item routerLink="/portal-help" [class.active]="activeRoute.startsWith('/portal-help')">
          <mat-icon>help</mat-icon>
          {{ 'PORTALS.HELP' | translate }}
        </a>
      </div>
    </div>
  </mat-menu>
</div>
