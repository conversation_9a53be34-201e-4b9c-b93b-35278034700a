<div class="notice-bar" [class.closed]="isClosed">
  <div class="notice-content">
    <div class="notice-icon">
      <mat-icon>campaign</mat-icon>
    </div>
    <div class="notice-scroll-container">
      <div class="notice-scroll" #noticeScroll>
        <div class="notice-items">
          <span class="notice-item" (click)="viewNotice('admission')"><mat-icon>event</mat-icon> {{ 'NOTICE.ADMISSION_OPEN' | translate }}</span>
          <span class="notice-separator">•</span>
          <span class="notice-item" (click)="viewNotice('exam')"><mat-icon>school</mat-icon> {{ 'NOTICE.EXAM_SCHEDULE' | translate }}</span>
          <span class="notice-separator">•</span>
          <span class="notice-item" (click)="viewNotice('annual-day')"><mat-icon>celebration</mat-icon> {{ 'NOTICE.ANNUAL_DAY' | translate }}</span>
          <span class="notice-separator">•</span>
          <span class="notice-item" (click)="viewNotice('sports-day')"><mat-icon>sports_soccer</mat-icon> {{ 'NOTICE.SPORTS_DAY' | translate }}</span>
        </div>
        <!-- Duplicate for seamless scrolling -->
        <div class="notice-items">
          <span class="notice-item" (click)="viewNotice('admission')"><mat-icon>event</mat-icon> {{ 'NOTICE.ADMISSION_OPEN' | translate }}</span>
          <span class="notice-separator">•</span>
          <span class="notice-item" (click)="viewNotice('exam')"><mat-icon>school</mat-icon> {{ 'NOTICE.EXAM_SCHEDULE' | translate }}</span>
          <span class="notice-separator">•</span>
          <span class="notice-item" (click)="viewNotice('annual-day')"><mat-icon>celebration</mat-icon> {{ 'NOTICE.ANNUAL_DAY' | translate }}</span>
          <span class="notice-separator">•</span>
          <span class="notice-item" (click)="viewNotice('sports-day')"><mat-icon>sports_soccer</mat-icon> {{ 'NOTICE.SPORTS_DAY' | translate }}</span>
        </div>
      </div>
    </div>
    <div class="notice-actions">
      <button mat-button class="view-all-btn" (click)="viewAllNotices()">
        {{ 'NOTICE.VIEW_ALL' | translate }}
      </button>
      <button mat-icon-button class="close-btn" (click)="closeNoticeBar()" aria-label="Close notice bar">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>
</div>
