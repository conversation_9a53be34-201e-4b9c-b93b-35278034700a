using School.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace School.Application.Features.Alumni
{
    public interface IAlumniService
    {
        Task<(IEnumerable<AlumniDto> Alumni, int TotalCount)> GetAllAlumniAsync(AlumniFilterDto filter);
        Task<AlumniDto?> GetAlumniByIdAsync(Guid id);
        Task<Guid> CreateAlumniAsync(CreateAlumniDto alumniDto);
        Task<bool> UpdateAlumniAsync(Guid id, UpdateAlumniDto alumniDto);
        Task<bool> DeleteAlumniAsync(Guid id);

        // Testimonial methods
        Task<AlumniTestimonialDto?> GetTestimonialByIdAsync(Guid id);
        Task<Guid> CreateTestimonialAsync(CreateAlumniTestimonialDto testimonialDto);
        Task<bool> UpdateTestimonialAsync(Guid id, UpdateAlumniTestimonialDto testimonialDto);
        Task<bool> DeleteTestimonialAsync(Guid id);

        // Translation methods
        Task<bool> AddTranslationAsync(Guid alumniId, CreateAlumniTranslationDto translationDto);
        Task<bool> UpdateTranslationAsync(Guid alumniId, string languageCode, UpdateAlumniTranslationDto translationDto);
        Task<bool> DeleteTranslationAsync(Guid alumniId, string languageCode);
    }
}
