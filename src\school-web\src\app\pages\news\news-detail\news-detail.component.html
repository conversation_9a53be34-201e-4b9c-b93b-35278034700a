<!-- Article Not Found -->
<div class="container not-found" *ngIf="!article">
  <mat-card>
    <mat-card-content>
      <h2>{{ 'NEWS.ARTICLE_NOT_FOUND' | translate }}</h2>
      <p>{{ 'NEWS.ARTICLE_NOT_FOUND_MESSAGE' | translate }}</p>
      <a mat-raised-button color="primary" routerLink="/news">
        {{ 'NEWS.BACK_TO_NEWS' | translate }}
      </a>
    </mat-card-content>
  </mat-card>
</div>

<!-- Article Content -->
<div class="container" *ngIf="article">
  <!-- Article Header -->
  <div class="article-header">
    <div class="article-category">{{article.category}}</div>
    <h1 class="article-title">{{article.title}}</h1>
    <div class="article-meta">
      <div class="meta-item">
        <mat-icon>calendar_today</mat-icon>
        <span>{{article.date | date:'mediumDate'}}</span>
      </div>
      <div class="meta-item">
        <mat-icon>person</mat-icon>
        <span>{{article.author}}</span>
      </div>
    </div>
  </div>
  
  <!-- Article Image -->
  <div class="article-image">
    <img [src]="article.image" [alt]="article.title">
  </div>
  
  <!-- Article Content -->
  <div class="article-content">
    <p *ngFor="let paragraph of article.content">{{paragraph}}</p>
  </div>
  
  <!-- Article Tags -->
  <div class="article-tags">
    <h3>{{ 'NEWS.TAGS' | translate }}</h3>
    <div class="tags-list">
      <mat-chip-set>
        <mat-chip *ngFor="let tag of article.tags">{{tag}}</mat-chip>
      </mat-chip-set>
    </div>
  </div>
  
  <!-- Share Article -->
  <div class="article-share">
    <h3>{{ 'NEWS.SHARE_ARTICLE' | translate }}</h3>
    <div class="share-buttons">
      <button mat-mini-fab color="primary" (click)="shareOnFacebook()" aria-label="Share on Facebook">
        <mat-icon>facebook</mat-icon>
      </button>
      <button mat-mini-fab color="primary" (click)="shareOnTwitter()" aria-label="Share on Twitter">
        <mat-icon>twitter</mat-icon>
      </button>
      <button mat-mini-fab color="primary" (click)="shareOnLinkedIn()" aria-label="Share on LinkedIn">
        <mat-icon>linkedin</mat-icon>
      </button>
      <button mat-mini-fab color="primary" (click)="shareByEmail()" aria-label="Share by Email">
        <mat-icon>email</mat-icon>
      </button>
    </div>
  </div>
  
  <mat-divider></mat-divider>
  
  <!-- Related Articles -->
  <div class="related-articles" *ngIf="relatedArticles.length > 0">
    <h2>{{ 'NEWS.RELATED_ARTICLES' | translate }}</h2>
    <div class="related-grid">
      <mat-card class="related-card" *ngFor="let related of relatedArticles">
        <div class="related-image">
          <img [src]="related.image" [alt]="related.title">
        </div>
        <mat-card-content>
          <div class="related-date">{{related.date | date:'mediumDate'}}</div>
          <h3 class="related-title">{{related.title}}</h3>
        </mat-card-content>
        <mat-card-actions>
          <a mat-button color="primary" [routerLink]="['/news', related.id]">
            {{ 'NEWS.READ_MORE' | translate }}
          </a>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
  
  <!-- Back to News -->
  <div class="back-to-news">
    <a mat-raised-button color="primary" routerLink="/news">
      <mat-icon>arrow_back</mat-icon>
      {{ 'NEWS.BACK_TO_NEWS' | translate }}
    </a>
  </div>
</div>
