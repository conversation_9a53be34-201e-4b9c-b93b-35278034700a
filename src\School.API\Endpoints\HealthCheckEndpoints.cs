using Carter;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using School.API.Common;
using System.Text.Json;

namespace School.API.Endpoints;

public class HealthCheckEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var healthCheckGroup = app.MapGroup("/api/health").WithTags("Health");

        // Basic health check endpoint
        healthCheckGroup.MapGet("/", () =>
        {
            return ApiResults.ApiOk(new { Status = "Healthy", Timestamp = DateTime.UtcNow }, "API is running");
        })
        .WithName("GetHealth")
        .WithOpenApi();

        // Detailed health check endpoint
        healthCheckGroup.MapHealthChecks("/detailed", new HealthCheckOptions
        {
            ResponseWriter = async (context, report) =>
            {
                context.Response.ContentType = "application/json";

                var response = new
                {
                    Status = report.Status.ToString(),
                    Checks = report.Entries.Select(e => new
                    {
                        Component = e.Key,
                        Status = e.Value.Status.ToString(),
                        Description = e.Value.Description,
                        Duration = e.Value.Duration.TotalMilliseconds
                    }),
                    TotalDuration = report.TotalDuration.TotalMilliseconds
                };

                await context.Response.WriteAsync(JsonSerializer.Serialize(response));
            }
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("GetDetailedHealth")
        .WithOpenApi();
    }
}
