using School.Domain.Common;

namespace School.Domain.Entities;

public class Department : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this department belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string HeadOfDepartment { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }

    // Navigation properties
    public Guid? ImageId { get; set; }
    public MediaItem? Image { get; set; }
    public ICollection<DepartmentTranslation> Translations { get; set; } = new List<DepartmentTranslation>();

    // Faculty relationship - will be implemented when we add proper relationships
    // public ICollection<Faculty> Faculty { get; set; } = new List<Faculty>();
}
