namespace School.Domain.Enums;

/// <summary>
/// Available subscription plans for organizations
/// </summary>
public enum SubscriptionPlan
{
    /// <summary>
    /// Free trial period
    /// </summary>
    Trial = 1,

    /// <summary>
    /// Basic plan with limited features
    /// </summary>
    Basic = 2,

    /// <summary>
    /// Standard plan with most features
    /// </summary>
    Standard = 3,

    /// <summary>
    /// Professional plan with advanced features
    /// </summary>
    Professional = 4,

    /// <summary>
    /// Premium plan with all features
    /// </summary>
    Premium = 5,

    /// <summary>
    /// Enterprise plan with custom features and support
    /// </summary>
    Enterprise = 6,

    /// <summary>
    /// Custom plan with negotiated features
    /// </summary>
    Custom = 7
}
