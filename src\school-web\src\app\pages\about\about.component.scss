// Hero Section
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('/assets/images/about-hero.jpg');
  background-size: cover;
  background-position: center;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  margin-bottom: 2rem;

  .hero-content {
    max-width: 800px;
    padding: 0 20px;

    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    .subtitle {
      font-size: 1.5rem;
      font-weight: 300;
    }
  }
}

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Content Sections
.content-section {
  margin-bottom: 4rem;
  padding-top: 2rem;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: #333;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100px;
      height: 4px;
      background-color: #3f51b5;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #555;
  }
}

// Floating Navigation
.floating-nav {
  position: fixed;
  top: 50%;
  right: -200px;
  transform: translateY(-50%);
  background-color: white;
  border-radius: 8px 0 0 8px;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  z-index: 100;

  &.visible {
    right: 0;
  }

  .floating-nav-content {
    padding: 20px;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 1.2rem;
      color: #333;
    }

    .floating-nav-links {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 10px;

        a {
          display: flex;
          align-items: center;
          color: #666;
          text-decoration: none;
          padding: 8px 10px;
          border-radius: 4px;
          transition: background-color 0.2s;
          cursor: pointer;

          &:hover {
            background-color: #f5f5f5;
            color: #3f51b5;
          }

          &.active {
            background-color: #e8eaf6;
            color: #3f51b5;
            font-weight: 500;
          }

          mat-icon {
            margin-right: 10px;
          }
        }
      }
    }
  }
}

// History Timeline
.history-timeline {
  position: relative;
  margin: 40px 0;

  &:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 120px;
    width: 4px;
    background-color: #e0e0e0;
  }

  .timeline-item {
    position: relative;
    display: flex;
    margin-bottom: 30px;

    .timeline-year {
      width: 100px;
      text-align: right;
      padding-right: 30px;
      font-weight: bold;
      font-size: 1.2rem;
      color: #3f51b5;
      flex-shrink: 0;
    }

    .timeline-content {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-left: 40px;
      flex-grow: 1;

      &:before {
        content: '';
        position: absolute;
        top: 20px;
        left: 118px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #3f51b5;
        border: 4px solid white;
      }

      h3 {
        margin-top: 0;
        color: #333;
      }

      p {
        margin-bottom: 0;
        color: #666;
        line-height: 1.6;
      }
    }
  }
}

// Mission & Vision
.mission-vision-container {
  display: flex;
  gap: 30px;
  margin-bottom: 40px;

  @media (max-width: 768px) {
    flex-direction: column;
  }

  .mission-box, .vision-box {
    flex: 1;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

    h3 {
      margin-top: 0;
      font-size: 1.5rem;
      margin-bottom: 15px;
      color: white;
    }

    p {
      color: white;
      line-height: 1.7;
      margin-bottom: 0;
    }
  }

  .mission-box {
    background: linear-gradient(135deg, #3f51b5, #1a237e);
  }

  .vision-box {
    background: linear-gradient(135deg, #673ab7, #311b92);
  }
}

// Values
.values-container {
  h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #333;
  }

  .values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;

    .value-item {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 25px;
      text-align: center;
      transition: transform 0.3s, box-shadow 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }

      mat-icon {
        font-size: 40px;
        height: 40px;
        width: 40px;
        color: #3f51b5;
        margin-bottom: 15px;
      }

      h4 {
        font-size: 1.2rem;
        margin-bottom: 10px;
        color: #333;
      }

      p {
        color: #666;
        line-height: 1.5;
      }
    }
  }
}

// Leadership Team
.leadership-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;

  .leader-card {
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    img {
      width: 100%;
      height: 300px;
      object-fit: cover;
    }

    mat-card-content {
      padding: 20px;

      h3 {
        margin-top: 0;
        font-size: 1.4rem;
        margin-bottom: 5px;
        color: #333;
      }

      h4 {
        color: #3f51b5;
        font-size: 1rem;
        margin-top: 0;
        margin-bottom: 15px;
      }

      p {
        color: #666;
        line-height: 1.6;
        margin-bottom: 0;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .hero-section {
    height: 350px;

    .hero-content h1 {
      font-size: 2.5rem;
    }
  }

  .floating-nav {
    display: none;
  }

  .history-timeline:before {
    left: 80px;
  }

  .history-timeline .timeline-item .timeline-year {
    width: 60px;
  }

  .history-timeline .timeline-item .timeline-content:before {
    left: 78px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 300px;

    .hero-content {
      h1 {
        font-size: 2rem;
      }

      .subtitle {
        font-size: 1.2rem;
      }
    }
  }

  .content-section h2 {
    font-size: 2rem;
  }

  .history-timeline:before {
    left: 60px;
  }

  .history-timeline .timeline-item .timeline-year {
    width: 40px;
    font-size: 1rem;
  }

  .history-timeline .timeline-item .timeline-content {
    margin-left: 30px;
  }

  .history-timeline .timeline-item .timeline-content:before {
    left: 58px;
  }
}

@media (max-width: 576px) {
  .hero-section {
    height: 250px;

    .hero-content h1 {
      font-size: 1.8rem;
    }
  }

  .history-timeline {
    &:before {
      display: none;
    }

    .timeline-item {
      flex-direction: column;

      .timeline-year {
        width: 100%;
        text-align: left;
        padding-right: 0;
        margin-bottom: 10px;
      }

      .timeline-content {
        margin-left: 0;

        &:before {
          display: none;
        }
      }
    }
  }
}
