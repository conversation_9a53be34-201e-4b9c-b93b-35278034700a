import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { ErrorHandlerService } from './error-handler.service';

@Injectable({
  providedIn: 'root'
})
export class ContentService extends BaseApiService {
  constructor(
    protected override http: HttpClient,
    protected override errorHandler: ErrorHandlerService
  ) {
    super(http, errorHandler);
  }

  getContents(params: any = {}): Observable<any> {
    let httpParams = new HttpParams();

    if (params.type) httpParams = httpParams.set('type', params.type.toString());
    if (params.published !== undefined) httpParams = httpParams.set('published', params.published.toString());
    if (params.search) httpParams = httpParams.set('search', params.search);
    if (params.page) httpParams = httpParams.set('page', params.page.toString());
    if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());

    return this.http.get(`${this.apiUrl}/content`, { params: httpParams });
  }

  getContent(id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/content/${id}`);
  }

  getContentBySlug(slug: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/content/slug/${slug}`);
  }

  createContent(content: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/content`, content);
  }

  updateContent(id: number, content: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/content/${id}`, content);
  }

  deleteContent(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/content/${id}`);
  }

  addContentTranslation(contentId: number, translation: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/content/${contentId}/translations`, translation);
  }

  updateContentTranslation(contentId: number, translationId: number, translation: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/content/${contentId}/translations/${translationId}`, translation);
  }

  deleteContentTranslation(contentId: number, translationId: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/content/${contentId}/translations/${translationId}`);
  }
}
