.media-upload-container {
  padding: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.upload-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.full-width {
  width: 100%;
}

.file-upload-container {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 16px 0;
}

.file-hint {
  color: rgba(0, 0, 0, 0.6);
  font-size: 12px;
}

.selected-files {
  margin-top: 24px;
  
  h3 {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
  }
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.file-preview {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  
  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.file-icon {
  mat-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: #757575;
  }
}

.file-info {
  flex: 1;
  overflow: hidden;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 12px;
  color: #757575;
}

.upload-progress {
  margin: 24px 0;
  
  .progress-text {
    text-align: center;
    margin-top: 8px;
    font-size: 14px;
    color: #757575;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

.spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@media (max-width: 768px) {
  .file-upload-container {
    flex-direction: column;
    align-items: flex-start;
  }
}
