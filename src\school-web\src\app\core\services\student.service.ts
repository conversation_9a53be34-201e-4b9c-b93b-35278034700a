import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { ErrorHandlerService } from './error-handler.service';
import { ApiResponseHandlerService } from './api-response-handler.service';
import { ApiResponse } from '../models/api-response.model';
import {
  Student, StudentDetail, StudentFilter, CreateStudent, UpdateStudent,
  CreateStudentAttendance, UpdateStudentAttendance, CreateStudentFee, UpdateStudentFee,
  CreateStudentResult, UpdateStudentResult, CreateStudentLeave, UpdateStudentLeave,
  CreateStudentAcademicHistory, UpdateStudentAcademicHistory, CreateStudentParent,
  ExamType, LeaveStatus, ParentRelationType, StudentAttendance, StudentFee, StudentResult,
  StudentLeave, StudentAcademicHistory
} from '../models/student.model';

@Injectable({
  providedIn: 'root'
})
export class StudentService extends BaseApiService {
  constructor(
    protected override http: HttpClient,
    protected override errorHandler: ErrorHandlerService,
    private apiResponseHandler: ApiResponseHandlerService
  ) {
    super(http, errorHandler);
  }

  // Student CRUD operations
  getStudents(filter: StudentFilter = {}): Observable<{ totalCount: number, items: Student[] }> {
    let httpParams = new HttpParams();

    if (filter.studentId) httpParams = httpParams.set('studentId', filter.studentId);
    if (filter.name) httpParams = httpParams.set('name', filter.name);
    if (filter.grade !== undefined) httpParams = httpParams.set('grade', filter.grade.toString());
    if (filter.section) httpParams = httpParams.set('section', filter.section);
    if (filter.medium !== undefined) httpParams = httpParams.set('medium', filter.medium.toString());
    if (filter.shift !== undefined) httpParams = httpParams.set('shift', filter.shift.toString());
    if (filter.academicYear !== undefined) httpParams = httpParams.set('academicYear', filter.academicYear.toString());
    if (filter.rollNumber !== undefined) httpParams = httpParams.set('rollNumber', filter.rollNumber.toString());
    if (filter.admissionYear !== undefined) httpParams = httpParams.set('admissionYear', filter.admissionYear.toString());
    if (filter.classTeacherId !== undefined) httpParams = httpParams.set('classTeacherId', filter.classTeacherId.toString());
    if (filter.isActive !== undefined) httpParams = httpParams.set('isActive', filter.isActive.toString());
    if (filter.isHosteler !== undefined) httpParams = httpParams.set('isHosteler', filter.isHosteler.toString());
    if (filter.page) httpParams = httpParams.set('page', filter.page.toString());
    if (filter.pageSize) httpParams = httpParams.set('pageSize', filter.pageSize.toString());
    if (filter.sortBy) httpParams = httpParams.set('sortBy', filter.sortBy);
    if (filter.sortDirection) httpParams = httpParams.set('sortDirection', filter.sortDirection);

    return this.apiResponseHandler.processResponse<{ totalCount: number, items: Student[] }>(
      this.http.get<ApiResponse<{ totalCount: number, items: Student[] }>>(`${this.apiUrl}/students`, { params: httpParams }),
      false,
      'Failed to retrieve students'
    );
  }

  getStudent(id: number): Observable<StudentDetail> {
    return this.apiResponseHandler.processResponse<StudentDetail>(
      this.http.get<ApiResponse<StudentDetail>>(`${this.apiUrl}/students/${id}`),
      false,
      `Failed to retrieve student with ID ${id}`
    );
  }

  getStudentByStudentId(studentId: string): Observable<Student> {
    return this.apiResponseHandler.processResponse<Student>(
      this.http.get<ApiResponse<Student>>(`${this.apiUrl}/students/by-student-id/${studentId}`),
      false,
      `Failed to retrieve student with student ID ${studentId}`
    );
  }

  getStudentByUserId(userId: string): Observable<Student> {
    return this.apiResponseHandler.processResponse<Student>(
      this.http.get<ApiResponse<Student>>(`${this.apiUrl}/students/by-user-id/${userId}`),
      false,
      `Failed to retrieve student with user ID ${userId}`
    );
  }

  createStudent(student: CreateStudent): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.post<ApiResponse<{ id: number }>>(`${this.apiUrl}/students`, student),
      true,
      'Failed to create student'
    );
  }

  updateStudent(id: number, student: UpdateStudent): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/students/${id}`, student),
      true,
      `Failed to update student with ID ${id}`
    );
  }

  deleteStudent(id: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/students/${id}`),
      true,
      `Failed to delete student with ID ${id}`
    );
  }

  // Attendance operations
  getStudentAttendance(studentId: number, fromDate?: Date, toDate?: Date): Observable<StudentAttendance[]> {
    let httpParams = new HttpParams();

    if (fromDate) httpParams = httpParams.set('fromDate', fromDate.toISOString());
    if (toDate) httpParams = httpParams.set('toDate', toDate.toISOString());

    return this.apiResponseHandler.processResponse<StudentAttendance[]>(
      this.http.get<ApiResponse<StudentAttendance[]>>(`${this.apiUrl}/students/${studentId}/attendance`, { params: httpParams }),
      false,
      'Failed to retrieve student attendance'
    );
  }

  createAttendance(attendance: CreateStudentAttendance): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.post<ApiResponse<{ id: number }>>(`${this.apiUrl}/students/${attendance.studentId}/attendance`, attendance),
      true,
      'Failed to create attendance record'
    );
  }

  updateAttendance(studentId: number, attendanceId: number, attendance: UpdateStudentAttendance): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/attendance/${attendanceId}`, attendance),
      true,
      'Failed to update attendance record'
    );
  }

  deleteAttendance(studentId: number, attendanceId: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/attendance/${attendanceId}`),
      true,
      'Failed to delete attendance record'
    );
  }

  // Fee operations
  getStudentFees(studentId: number, academicYear?: number): Observable<StudentFee[]> {
    let httpParams = new HttpParams();

    if (academicYear !== undefined) httpParams = httpParams.set('academicYear', academicYear.toString());

    return this.apiResponseHandler.processResponse<StudentFee[]>(
      this.http.get<ApiResponse<StudentFee[]>>(`${this.apiUrl}/students/${studentId}/fees`, { params: httpParams }),
      false,
      'Failed to retrieve student fees'
    );
  }

  createFee(fee: CreateStudentFee): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.post<ApiResponse<{ id: number }>>(`${this.apiUrl}/students/${fee.studentId}/fees`, fee),
      true,
      'Failed to create fee record'
    );
  }

  updateFee(studentId: number, feeId: number, fee: UpdateStudentFee): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/fees/${feeId}`, fee),
      true,
      'Failed to update fee record'
    );
  }

  deleteFee(studentId: number, feeId: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/fees/${feeId}`),
      true,
      'Failed to delete fee record'
    );
  }

  // Result operations
  getStudentResults(studentId: number, academicYear?: number, examType?: ExamType): Observable<StudentResult[]> {
    let httpParams = new HttpParams();

    if (academicYear !== undefined) httpParams = httpParams.set('academicYear', academicYear.toString());
    if (examType !== undefined) httpParams = httpParams.set('examType', examType.toString());

    return this.apiResponseHandler.processResponse<StudentResult[]>(
      this.http.get<ApiResponse<StudentResult[]>>(`${this.apiUrl}/students/${studentId}/results`, { params: httpParams }),
      false,
      'Failed to retrieve student results'
    );
  }

  calculateGPA(studentId: number, academicYear: number, examType: ExamType): Observable<{ gpa: number }> {
    let httpParams = new HttpParams()
      .set('academicYear', academicYear.toString())
      .set('examType', examType.toString());

    return this.apiResponseHandler.processResponse<{ gpa: number }>(
      this.http.get<ApiResponse<{ gpa: number }>>(`${this.apiUrl}/students/${studentId}/results/gpa`, { params: httpParams }),
      false,
      'Failed to calculate GPA'
    );
  }

  createResult(result: CreateStudentResult): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.post<ApiResponse<{ id: number }>>(`${this.apiUrl}/students/${result.studentId}/results`, result),
      true,
      'Failed to create result record'
    );
  }

  updateResult(studentId: number, resultId: number, result: UpdateStudentResult): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/results/${resultId}`, result),
      true,
      'Failed to update result record'
    );
  }

  deleteResult(studentId: number, resultId: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/results/${resultId}`),
      true,
      'Failed to delete result record'
    );
  }

  // Leave operations
  getStudentLeaves(studentId: number, fromDate?: Date, toDate?: Date, status?: LeaveStatus): Observable<StudentLeave[]> {
    let httpParams = new HttpParams();

    if (fromDate) httpParams = httpParams.set('fromDate', fromDate.toISOString());
    if (toDate) httpParams = httpParams.set('toDate', toDate.toISOString());
    if (status !== undefined) httpParams = httpParams.set('status', status.toString());

    return this.apiResponseHandler.processResponse<StudentLeave[]>(
      this.http.get<ApiResponse<StudentLeave[]>>(`${this.apiUrl}/students/${studentId}/leaves`, { params: httpParams }),
      false,
      'Failed to retrieve student leaves'
    );
  }

  getLeaveById(studentId: number, leaveId: number): Observable<StudentLeave> {
    return this.apiResponseHandler.processResponse<StudentLeave>(
      this.http.get<ApiResponse<StudentLeave>>(`${this.apiUrl}/students/${studentId}/leaves/${leaveId}`),
      false,
      'Failed to retrieve leave record'
    );
  }

  createLeave(leave: CreateStudentLeave): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.post<ApiResponse<{ id: number }>>(`${this.apiUrl}/students/${leave.studentId}/leaves`, leave),
      true,
      'Leave application submitted successfully'
    );
  }

  updateLeaveStatus(studentId: number, leaveId: number, leave: UpdateStudentLeave): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/leaves/${leaveId}/status`, leave),
      true,
      'Leave status updated successfully'
    );
  }

  deleteLeave(studentId: number, leaveId: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/leaves/${leaveId}`),
      true,
      'Leave application deleted successfully'
    );
  }

  // Academic History operations
  getStudentAcademicHistory(studentId: number): Observable<StudentAcademicHistory[]> {
    return this.apiResponseHandler.processResponse<StudentAcademicHistory[]>(
      this.http.get<ApiResponse<StudentAcademicHistory[]>>(`${this.apiUrl}/students/${studentId}/academic-history`),
      false,
      'Failed to retrieve academic history'
    );
  }

  getAcademicHistoryByYear(studentId: number, academicYear: number): Observable<StudentAcademicHistory> {
    return this.apiResponseHandler.processResponse<StudentAcademicHistory>(
      this.http.get<ApiResponse<StudentAcademicHistory>>(`${this.apiUrl}/students/${studentId}/academic-history/${academicYear}`),
      false,
      `Failed to retrieve academic history for year ${academicYear}`
    );
  }

  createAcademicHistory(academicHistory: CreateStudentAcademicHistory): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.post<ApiResponse<{ id: number }>>(`${this.apiUrl}/students/${academicHistory.studentId}/academic-history`, academicHistory),
      true,
      'Academic history record created successfully'
    );
  }

  updateAcademicHistory(studentId: number, academicHistoryId: number, academicHistory: UpdateStudentAcademicHistory): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/academic-history/${academicHistoryId}`, academicHistory),
      true,
      'Academic history record updated successfully'
    );
  }

  promoteStudent(studentId: number, fromAcademicYear: number, toAcademicYear: number, toGrade: number, toSection: string, toRollNumber: number, toStudentId: string): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.post<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/promote`, {
        fromAcademicYear,
        toAcademicYear,
        toGrade,
        toSection,
        toRollNumber,
        toStudentId
      }),
      true,
      'Student promoted successfully'
    );
  }

  // Parent Association operations
  addParentToStudent(studentId: number, parentDto: CreateStudentParent): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.post<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/parents`, parentDto),
      true,
      'Parent added to student successfully'
    );
  }

  updateParentRelation(studentId: number, parentId: number, relationType: ParentRelationType, isPrimaryContact: boolean): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/parents/${parentId}`, {
        relationType,
        isPrimaryContact
      }),
      true,
      'Parent relationship updated successfully'
    );
  }

  removeParentFromStudent(studentId: number, parentId: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/students/${studentId}/parents/${parentId}`),
      true,
      'Parent removed from student successfully'
    );
  }
}
