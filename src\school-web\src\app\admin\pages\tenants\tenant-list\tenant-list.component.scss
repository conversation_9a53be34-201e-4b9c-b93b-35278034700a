.tenant-list-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  gap: 16px;

  .header-content {
    flex: 1;

    .page-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 500;
      color: var(--primary-color);

      mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
      }
    }

    .page-subtitle {
      margin: 0;
      color: var(--text-secondary);
      font-size: 16px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

.filters-card {
  margin-bottom: 24px;

  .filters-row {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;

    .search-field {
      flex: 1;
      min-width: 300px;
    }

    .filter-field {
      min-width: 150px;
    }
  }
}

.table-card {
  .table-container {
    overflow-x: auto;

    .tenants-table {
      width: 100%;
      min-width: 800px;

      .tenant-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .tenant-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 8px;
          background-color: var(--primary-color);
          color: white;

          mat-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
          }
        }

        .tenant-details {
          .tenant-name {
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 2px;
          }

          .tenant-domain {
            font-size: 12px;
            color: var(--text-secondary);
            font-family: 'Courier New', monospace;
          }
        }
      }

      .tenant-slug {
        background-color: var(--background-secondary);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-family: 'Courier New', monospace;
      }

      .user-count {
        font-weight: 500;
        color: var(--primary-color);
      }

      .trial-info {
        .trial-end {
          font-size: 11px;
          color: var(--text-secondary);
          margin-top: 2px;
        }
      }

      .action-buttons {
        display: flex;
        gap: 4px;

        button {
          width: 36px;
          height: 36px;

          mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  gap: 16px;

  p {
    margin: 0;
    color: var(--text-secondary);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
  gap: 16px;

  mat-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: var(--text-secondary);
    opacity: 0.5;
  }

  h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
  }

  p {
    margin: 0;
    color: var(--text-secondary);
    max-width: 400px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .tenant-list-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;

    .header-actions {
      justify-content: flex-end;
    }
  }

  .filters-row {
    flex-direction: column;
    align-items: stretch;

    .search-field,
    .filter-field {
      min-width: unset;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .tenant-slug {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
