using School.Domain.Common;

namespace School.Domain.Entities;

public class FacultyAward : BaseEntity
{
    public Guid FacultyId { get; set; }
    public Faculty? Faculty { get; set; }
    
    public string Name { get; set; } = string.Empty;
    public int? Year { get; set; }
    public string? Organization { get; set; }
    public int DisplayOrder { get; set; }
    public string Description { get; set; }
}
