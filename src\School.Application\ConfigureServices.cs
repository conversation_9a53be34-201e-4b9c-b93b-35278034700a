using System.Reflection;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using School.Application.Common.Behaviors;
using School.Application.Features.AcademicCalendar;
using School.Application.Features.AcademicYear;
using School.Application.Features.Alumni;
using School.Application.Features.Term;
using School.Application.Features.Auth;
using School.Application.Features.Career;
using School.Application.Features.Clubs;
using School.Application.Features.Content;
using School.Application.Features.Department;
using School.Application.Features.Event;
using School.Application.Features.Faculty;
using School.Application.Features.HostelFacility;
using School.Application.Features.Media;
using School.Application.Features.Notice;
using School.Application.Features.Parent;
using School.Application.Features.Student;
using School.Application.Features.TuitionFee;
using School.Application.Features.User;

namespace School.Application;

public static class ConfigureServices
{
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        services.AddAutoMapper(Assembly.GetExecutingAssembly());
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
        services.AddMediatR(cfg => {
            cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
            cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
        });

        // Register application behaviors and validators
        // Service implementations are registered in the Infrastructure layer

        // Register application service interfaces
        // Note: The implementations are in the Infrastructure layer

        return services;
    }
}
