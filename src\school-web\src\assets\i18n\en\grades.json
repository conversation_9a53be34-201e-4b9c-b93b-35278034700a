{"GRADES": {"TITLE": "Grade Management", "ADD_GRADE": "Add Grade", "CREATE_GRADE": "Create Grade", "EDIT_GRADE": "Edit Grade", "DELETE_GRADE": "Delete Grade", "GRADE_DETAILS": "Grade Details", "SEARCH_PLACEHOLDER": "Search by name, code, or description...", "NO_GRADES_FOUND": "No Grades Found", "NO_GRADES_MESSAGE": "No grades have been created yet. Create your first grade to get started.", "CREATE_FIRST_GRADE": "Create First Grade", "BASIC_INFORMATION": "Basic Information", "AGE_AND_CAPACITY": "Age and Capacity", "ACADEMIC_INFORMATION": "Academic Information", "NAME": "Grade Name", "NAME_PLACEHOLDER": "Enter grade name (e.g., Grade 1, Kindergarten)", "CODE": "Grade Code", "CODE_PLACEHOLDER": "Enter unique code (e.g., G1, KG)", "LEVEL": "Grade Level", "EDUCATION_LEVEL": "Education Level", "DESCRIPTION": "Description", "DESCRIPTION_PLACEHOLDER": "Enter grade description and objectives", "MIN_AGE": "Minimum Age", "MAX_AGE": "Maximum Age", "MAX_STUDENTS": "Maximum Students", "DISPLAY_ORDER": "Display Order", "ACADEMIC_YEAR": "Academic Year", "PROMOTION_CRITERIA": "Promotion Criteria", "PROMOTION_CRITERIA_PLACEHOLDER": "Enter criteria for promotion to next grade", "MIN_PASSING_GRADE": "Minimum Passing Grade (%)", "MIN_PASSING_GRADE_HINT": "Minimum percentage required to pass this grade", "REMARKS": "Remarks", "REMARKS_PLACEHOLDER": "Additional notes or comments", "IS_ACTIVE": "Active Grade", "IS_ACTIVE_HINT": "Only active grades will be available for student enrollment", "AGE_RANGE_ERROR": "Maximum age must be greater than minimum age", "STATISTICS": {"TOTAL_GRADES": "Total Grades", "ACTIVE_GRADES": "Active Grades", "TOTAL_STUDENTS": "Total Students", "AVERAGE_CLASS_SIZE": "Average Class Size"}, "ACTIONS": {"ACTIVATE": "Activate", "DEACTIVATE": "Deactivate", "DUPLICATE": "Duplicate", "EXPORT": "Export", "IMPORT": "Import", "REORDER": "Reorder"}, "MESSAGES": {"CREATED_SUCCESS": "Grade created successfully", "UPDATED_SUCCESS": "Grade updated successfully", "DELETED_SUCCESS": "Grade deleted successfully", "ACTIVATED_SUCCESS": "Grade activated successfully", "DEACTIVATED_SUCCESS": "Grade deactivated successfully", "REORDERED_SUCCESS": "Grades reordered successfully", "EXPORTED_SUCCESS": "Grades exported successfully", "CREATE_ERROR": "Error creating grade", "UPDATE_ERROR": "Error updating grade", "DELETE_ERROR": "Error deleting grade", "LOAD_ERROR": "Error loading grades", "STATUS_UPDATE_ERROR": "Error updating grade status", "DELETE_CONFIRMATION": "Are you sure you want to delete this grade?", "DELETE_WARNING": "This action cannot be undone and will affect all related data.", "DEACTIVATE_CONFIRMATION": "Are you sure you want to deactivate this grade?", "DEACTIVATE_WARNING": "Students will no longer be able to enroll in this grade."}, "FILTERS": {"ALL_EDUCATION_LEVELS": "All Education Levels", "ALL_ACADEMIC_YEARS": "All Academic Years", "ACTIVE_ONLY": "Active Only", "INACTIVE_ONLY": "Inactive Only"}, "SORT": {"NAME_ASC": "Name (A-Z)", "NAME_DESC": "Name (Z-A)", "LEVEL_ASC": "Level (Low to High)", "LEVEL_DESC": "Level (High to Low)", "CREATED_ASC": "Oldest First", "CREATED_DESC": "Newest First"}, "EDUCATION_LEVELS": {"PRE_SCHOOL": "Pre-School", "PRIMARY": "Primary", "SECONDARY": "Secondary", "HIGHER_SECONDARY": "Higher Secondary", "UNDERGRADUATE": "Undergraduate", "POSTGRADUATE": "Postgraduate"}, "VALIDATION": {"NAME_REQUIRED": "Grade name is required", "CODE_REQUIRED": "Grade code is required", "CODE_UNIQUE": "Grade code must be unique", "LEVEL_REQUIRED": "Grade level is required", "LEVEL_RANGE": "Grade level must be between 1 and 20", "EDUCATION_LEVEL_REQUIRED": "Education level is required", "MIN_AGE_REQUIRED": "Minimum age is required", "MAX_AGE_REQUIRED": "Maximum age is required", "AGE_RANGE_INVALID": "Maximum age must be greater than minimum age", "MAX_STUDENTS_REQUIRED": "Maximum students is required", "MAX_STUDENTS_RANGE": "Maximum students must be between 1 and 200", "ACADEMIC_YEAR_REQUIRED": "Academic year is required", "MIN_PASSING_GRADE_REQUIRED": "Minimum passing grade is required", "MIN_PASSING_GRADE_RANGE": "Minimum passing grade must be between 0 and 100", "DISPLAY_ORDER_REQUIRED": "Display order is required"}}}