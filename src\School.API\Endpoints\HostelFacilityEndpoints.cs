using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using School.API.Common;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Domain.Entities;
using School.Domain.Enums;
using School.Application.Features.HostelFacility;

namespace School.API.Endpoints;

public class HostelFacilityEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/hostel-facilities").WithTags("Hostel Facilities");

        group.MapGet("/", async ([AsParameters] HostelFacilityFilterDto filter, [FromServices] IHostelFacilityService facilityService) =>
        {
            var (facilities, totalCount) = await facilityService.GetAllFacilitiesAsync(filter);
            var response = new { TotalCount = totalCount, Items = facilities };
            return ApiResults.ApiOk(response, "Hostel facilities retrieved successfully");
        }).WithName("GetAllHostelFacilities").WithOpenApi();

        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IHostelFacilityService facilityService) =>
        {
            var facility = await facilityService.GetFacilityByIdAsync(id);
            if (facility == null)
            {
                return ApiResults.ApiNotFound("Facility not found");
            }
            return ApiResults.ApiOk(facility, "Hostel facility retrieved successfully");
        }).WithName("GetHostelFacilityById").WithOpenApi();

        group.MapPost("/", async ([FromBody] CreateHostelFacilityDto facilityDto, [FromServices] IHostelFacilityService facilityService) =>
        {
            var facilityId = await facilityService.CreateFacilityAsync(facilityDto);
            return ApiResults.ApiCreated(new { id = facilityId }, $"/api/hostel-facilities/{facilityId}", "Hostel facility created successfully");
        }).WithName("CreateHostelFacility").WithOpenApi();

        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateHostelFacilityDto facilityDto, [FromServices] IHostelFacilityService facilityService) =>
        {
            var updated = await facilityService.UpdateFacilityAsync(id, facilityDto);
            if (!updated)
            {
                return ApiResults.ApiNotFound("Facility not found");
            }
            return ApiResults.ApiOk(new { id }, "Hostel facility updated successfully");
        }).WithName("UpdateHostelFacility").WithOpenApi();

        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IHostelFacilityService facilityService) =>
        {
            var deleted = await facilityService.DeleteFacilityAsync(id);
            if (!deleted)
            {
                return ApiResults.ApiNotFound("Facility not found");
            }
            return ApiResults.ApiOk("Hostel facility deleted successfully");
        }).WithName("DeleteHostelFacility").WithOpenApi();
    }
}
