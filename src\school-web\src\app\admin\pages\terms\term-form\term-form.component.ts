import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-term-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="form-container">
      <div class="page-header">
        <h1>{{isEditMode ? 'Edit Term' : 'Create Term'}}</h1>
        <div class="header-actions">
          <button mat-button (click)="cancel()">
            <mat-icon>close</mat-icon>
            Cancel
          </button>
        </div>
      </div>
      
      <mat-card>
        <mat-card-content>
          <p>Term Form component - Coming Soon!</p>
          <p *ngIf="isEditMode">Term ID: {{termId}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .form-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }
    
    h1 {
      margin: 0;
      font-size: 2rem;
      font-weight: 500;
    }
  `]
})
export class TermFormComponent implements OnInit {
  isEditMode = false;
  termId?: string;

  constructor(
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.termId = params['id'];
      }
    });
  }

  cancel(): void {
    this.router.navigate(['/admin/terms']);
  }
}
