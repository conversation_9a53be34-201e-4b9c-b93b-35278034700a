import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../../shared/components/default-hero/default-hero.component';

interface MenuItem {
  name: string;
  description: string;
  price: string;
  allergens?: string[];
  vegetarian: boolean;
  image: string;
  category: string;
}

interface MealPlan {
  name: string;
  description: string;
  price: string;
  features: string[];
}

@Component({
  selector: 'app-dining',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatExpansionModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './dining.component.html',
  styleUrls: ['./dining.component.scss']
})
export class DiningComponent {
  // Dining locations
  diningLocations = [
    {
      name: 'Main Cafeteria',
      description: 'Our spacious main dining hall serves breakfast, lunch, and dinner with a variety of options including a hot food station, salad bar, sandwich station, and international cuisine.',
      hours: 'Monday-Friday: 7:00 AM - 7:00 PM, Saturday-Sunday: 8:00 AM - 6:00 PM',
      location: 'Student Center, First Floor',
      image: 'assets/images/campus/dining/cafeteria.jpg'
    },
    {
      name: 'Café Express',
      description: 'A quick-service café offering coffee, tea, smoothies, pastries, and grab-and-go sandwiches and salads for students and faculty on the move.',
      hours: 'Monday-Friday: 7:30 AM - 4:00 PM, Closed weekends',
      location: 'Academic Building, Ground Floor',
      image: 'assets/images/campus/dining/cafe.jpg'
    },
    {
      name: 'Snack Bar',
      description: 'Located near the athletic facilities, the Snack Bar offers healthy snacks, sports drinks, smoothies, and light meals for students before or after physical activities.',
      hours: 'Monday-Friday: 10:00 AM - 6:00 PM, Saturday: 10:00 AM - 2:00 PM, Closed Sunday',
      location: 'Athletic Center, Main Lobby',
      image: 'assets/images/campus/dining/snack-bar.jpg'
    },
    {
      name: 'Faculty Dining Room',
      description: 'A dedicated dining space for faculty and staff with a more upscale menu, quieter atmosphere, and table service. Also available for special events and meetings.',
      hours: 'Monday-Friday: 11:00 AM - 2:00 PM, Closed weekends',
      location: 'Administration Building, Second Floor',
      image: 'assets/images/campus/dining/faculty-dining.jpg'
    }
  ];

  // Menu categories
  menuCategories = [
    'Breakfast',
    'Lunch',
    'Dinner',
    'Snacks & Beverages'
  ];

  // Sample menu items
  menuItems: MenuItem[] = [
    {
      name: 'Breakfast Burrito',
      description: 'Scrambled eggs, cheese, potatoes, and choice of bacon or sausage wrapped in a whole wheat tortilla.',
      price: '$4.50',
      allergens: ['Eggs', 'Dairy', 'Wheat'],
      vegetarian: false,
      image: 'assets/images/campus/dining/breakfast-burrito.jpg',
      category: 'Breakfast'
    },
    {
      name: 'Oatmeal Bar',
      description: 'Steel-cut oatmeal with choice of toppings including fresh fruit, nuts, brown sugar, and cinnamon.',
      price: '$3.75',
      allergens: ['Tree Nuts'],
      vegetarian: true,
      image: 'assets/images/campus/dining/oatmeal.jpg',
      category: 'Breakfast'
    },
    {
      name: 'Yogurt Parfait',
      description: 'Greek yogurt layered with granola and seasonal berries.',
      price: '$3.50',
      allergens: ['Dairy'],
      vegetarian: true,
      image: 'assets/images/campus/dining/yogurt-parfait.jpg',
      category: 'Breakfast'
    },
    {
      name: 'Avocado Toast',
      description: 'Whole grain toast topped with mashed avocado, cherry tomatoes, and microgreens.',
      price: '$4.25',
      allergens: ['Wheat'],
      vegetarian: true,
      image: 'assets/images/campus/dining/avocado-toast.jpg',
      category: 'Breakfast'
    },
    {
      name: 'Grilled Chicken Sandwich',
      description: 'Grilled chicken breast with lettuce, tomato, and herb aioli on a ciabatta roll. Served with side salad or chips.',
      price: '$6.50',
      allergens: ['Wheat', 'Eggs'],
      vegetarian: false,
      image: 'assets/images/campus/dining/chicken-sandwich.jpg',
      category: 'Lunch'
    },
    {
      name: 'Harvest Salad',
      description: 'Mixed greens with roasted vegetables, quinoa, dried cranberries, goat cheese, and balsamic vinaigrette.',
      price: '$5.75',
      allergens: ['Dairy'],
      vegetarian: true,
      image: 'assets/images/campus/dining/harvest-salad.jpg',
      category: 'Lunch'
    },
    {
      name: 'Vegetable Stir Fry',
      description: 'Seasonal vegetables stir-fried with tofu or chicken in a ginger-soy sauce, served over brown rice.',
      price: '$6.25',
      allergens: ['Soy'],
      vegetarian: true,
      image: 'assets/images/campus/dining/stir-fry.jpg',
      category: 'Lunch'
    },
    {
      name: 'Turkey Chili',
      description: 'Hearty turkey chili with beans, vegetables, and spices. Served with cornbread.',
      price: '$5.50',
      allergens: [],
      vegetarian: false,
      image: 'assets/images/campus/dining/turkey-chili.jpg',
      category: 'Lunch'
    },
    {
      name: 'Herb Roasted Chicken',
      description: 'Herb-roasted chicken breast served with roasted potatoes and seasonal vegetables.',
      price: '$7.50',
      allergens: [],
      vegetarian: false,
      image: 'assets/images/campus/dining/roasted-chicken.jpg',
      category: 'Dinner'
    },
    {
      name: 'Pasta Primavera',
      description: 'Whole wheat pasta tossed with fresh vegetables, olive oil, garlic, and Parmesan cheese.',
      price: '$6.75',
      allergens: ['Wheat', 'Dairy'],
      vegetarian: true,
      image: 'assets/images/campus/dining/pasta-primavera.jpg',
      category: 'Dinner'
    },
    {
      name: 'Salmon with Quinoa',
      description: 'Grilled salmon fillet served over quinoa pilaf with roasted vegetables and lemon-dill sauce.',
      price: '$8.50',
      allergens: ['Fish'],
      vegetarian: false,
      image: 'assets/images/campus/dining/salmon-quinoa.jpg',
      category: 'Dinner'
    },
    {
      name: 'Vegetable Curry',
      description: 'Chickpea and vegetable curry served with basmati rice and naan bread.',
      price: '$6.50',
      allergens: ['Wheat'],
      vegetarian: true,
      image: 'assets/images/campus/dining/vegetable-curry.jpg',
      category: 'Dinner'
    },
    {
      name: 'Fresh Fruit Cup',
      description: 'Seasonal fresh fruit cut into bite-sized pieces.',
      price: '$3.25',
      allergens: [],
      vegetarian: true,
      image: 'assets/images/campus/dining/fruit-cup.jpg',
      category: 'Snacks & Beverages'
    },
    {
      name: 'Hummus & Veggie Plate',
      description: 'House-made hummus served with carrot sticks, cucumber slices, bell peppers, and pita triangles.',
      price: '$4.50',
      allergens: ['Wheat'],
      vegetarian: true,
      image: 'assets/images/campus/dining/hummus-plate.jpg',
      category: 'Snacks & Beverages'
    },
    {
      name: 'Smoothie',
      description: 'Blended fruit smoothie with choice of strawberry-banana, mixed berry, or green machine (spinach, kale, pineapple, and banana).',
      price: '$4.00',
      allergens: [],
      vegetarian: true,
      image: 'assets/images/campus/dining/smoothie.jpg',
      category: 'Snacks & Beverages'
    },
    {
      name: 'Trail Mix',
      description: 'House-made trail mix with nuts, dried fruit, and dark chocolate chips.',
      price: '$2.75',
      allergens: ['Tree Nuts', 'Peanuts'],
      vegetarian: true,
      image: 'assets/images/campus/dining/trail-mix.jpg',
      category: 'Snacks & Beverages'
    }
  ];

  // Meal plans
  mealPlans: MealPlan[] = [
    {
      name: 'Full Board Plan',
      description: 'Unlimited access to the main cafeteria for breakfast, lunch, and dinner, seven days a week, plus $200 in Dining Dollars per semester for use at other campus dining locations.',
      price: '$2,500 per semester',
      features: [
        'Unlimited meals at Main Cafeteria',
        '$200 Dining Dollars per semester',
        'Guest passes (5 per semester)',
        'Special event meals included'
      ]
    },
    {
      name: '15-Meal Plan',
      description: '15 meals per week at the main cafeteria, plus $300 in Dining Dollars per semester for use at other campus dining locations.',
      price: '$2,200 per semester',
      features: [
        '15 meals per week at Main Cafeteria',
        '$300 Dining Dollars per semester',
        'Guest passes (3 per semester)',
        'Special event meals included'
      ]
    },
    {
      name: '10-Meal Plan',
      description: '10 meals per week at the main cafeteria, plus $400 in Dining Dollars per semester for use at other campus dining locations.',
      price: '$1,900 per semester',
      features: [
        '10 meals per week at Main Cafeteria',
        '$400 Dining Dollars per semester',
        'Guest passes (2 per semester)',
        'Special event meals included'
      ]
    },
    {
      name: 'Commuter Plan',
      description: '5 meals per week at the main cafeteria, plus $500 in Dining Dollars per semester for use at other campus dining locations.',
      price: '$1,500 per semester',
      features: [
        '5 meals per week at Main Cafeteria',
        '$500 Dining Dollars per semester',
        'Guest passes (1 per semester)',
        'Special event meals included'
      ]
    }
  ];

  // Nutrition and sustainability initiatives
  initiatives = [
    {
      title: 'Farm-to-Table Program',
      description: 'We partner with local farms to source fresh, seasonal produce, reducing our carbon footprint and supporting the local economy while providing the freshest ingredients for our meals.',
      icon: 'eco'
    },
    {
      title: 'Allergen Awareness',
      description: 'All menu items are clearly labeled with common allergens, and our staff is trained to handle special dietary needs and prevent cross-contamination.',
      icon: 'health_and_safety'
    },
    {
      title: 'Waste Reduction',
      description: 'Our composting and recycling programs divert food waste and packaging from landfills, and we use compostable or recyclable serving materials whenever possible.',
      icon: 'recycling'
    },
    {
      title: 'Balanced Nutrition',
      description: 'Our menus are designed by registered dietitians to ensure balanced nutrition, with a focus on whole foods, lean proteins, whole grains, and plenty of fruits and vegetables.',
      icon: 'restaurant'
    },
    {
      title: 'Plant-Forward Options',
      description: 'We offer a variety of delicious plant-based and vegetarian options at every meal to reduce our environmental impact and accommodate diverse dietary preferences.',
      icon: 'spa'
    },
    {
      title: 'Food Education',
      description: 'Through cooking demonstrations, nutrition workshops, and informational displays, we help students develop healthy eating habits and food literacy that will serve them throughout life.',
      icon: 'school'
    }
  ];

  // Filter menu items by category
  getMenuItemsByCategory(category: string): MenuItem[] {
    return this.menuItems.filter(item => item.category === category);
  }
}
