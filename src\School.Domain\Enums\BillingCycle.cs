namespace School.Domain.Enums;

/// <summary>
/// Billing cycle options for subscriptions
/// </summary>
public enum BillingCycle
{
    /// <summary>
    /// Billed monthly
    /// </summary>
    Monthly = 1,

    /// <summary>
    /// Billed quarterly (every 3 months)
    /// </summary>
    Quarterly = 2,

    /// <summary>
    /// Billed semi-annually (every 6 months)
    /// </summary>
    SemiAnnually = 3,

    /// <summary>
    /// Billed annually (yearly)
    /// </summary>
    Annually = 4,

    /// <summary>
    /// One-time payment
    /// </summary>
    OneTime = 5,

    /// <summary>
    /// Custom billing cycle
    /// </summary>
    Custom = 6
}
