﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace School.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddAcademicYearAndTermEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AcademicYear",
                table: "StudentAcademicHistories");

            migrationBuilder.DropColumn(
                name: "StudentId_Year",
                table: "StudentAcademicHistories");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "StudentAcademicHistories");

            migrationBuilder.DropColumn(
                name: "AcademicYear",
                table: "AcademicCalendars");

            migrationBuilder.DropColumn(
                name: "Semester",
                table: "AcademicCalendars");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "AcademicCalendars");

            migrationBuilder.AddColumn<Guid>(
                name: "AcademicYearId",
                table: "StudentAcademicHistories",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TermId",
                table: "StudentAcademicHistories",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "RelationType",
                table: "Parents",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<Guid>(
                name: "AcademicYearId",
                table: "AcademicCalendars",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TermId",
                table: "AcademicCalendars",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AcademicYears",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    DisplayName = table.Column<string>(type: "text", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    IsCurrentYear = table.Column<bool>(type: "boolean", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Remarks = table.Column<string>(type: "text", nullable: false),
                    TotalWorkingDays = table.Column<int>(type: "integer", nullable: false),
                    TotalHolidays = table.Column<int>(type: "integer", nullable: false),
                    RegistrationStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RegistrationEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AdmissionStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AdmissionEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AcademicYears", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AcademicYearTranslations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AcademicYearId = table.Column<Guid>(type: "uuid", nullable: false),
                    LanguageCode = table.Column<string>(type: "text", nullable: false),
                    DisplayName = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Remarks = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AcademicYearTranslations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AcademicYearTranslations_AcademicYears_AcademicYearId",
                        column: x => x.AcademicYearId,
                        principalTable: "AcademicYears",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Terms",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AcademicYearId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    OrderIndex = table.Column<int>(type: "integer", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Remarks = table.Column<string>(type: "text", nullable: false),
                    TotalWorkingDays = table.Column<int>(type: "integer", nullable: false),
                    TotalHolidays = table.Column<int>(type: "integer", nullable: false),
                    ExamStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExamEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ResultPublishDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RegistrationDeadline = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    FeePaymentDeadline = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    PassingGrade = table.Column<decimal>(type: "numeric", nullable: true),
                    MaximumGrade = table.Column<decimal>(type: "numeric", nullable: true),
                    GradingScale = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Terms", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Terms_AcademicYears_AcademicYearId",
                        column: x => x.AcademicYearId,
                        principalTable: "AcademicYears",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TermTranslations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TermId = table.Column<Guid>(type: "uuid", nullable: false),
                    LanguageCode = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Remarks = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TermTranslations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TermTranslations_Terms_TermId",
                        column: x => x.TermId,
                        principalTable: "Terms",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_StudentAcademicHistories_AcademicYearId",
                table: "StudentAcademicHistories",
                column: "AcademicYearId");

            migrationBuilder.CreateIndex(
                name: "IX_StudentAcademicHistories_TermId",
                table: "StudentAcademicHistories",
                column: "TermId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicCalendars_AcademicYearId",
                table: "AcademicCalendars",
                column: "AcademicYearId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicCalendars_TermId",
                table: "AcademicCalendars",
                column: "TermId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicYearTranslations_AcademicYearId",
                table: "AcademicYearTranslations",
                column: "AcademicYearId");

            migrationBuilder.CreateIndex(
                name: "IX_Terms_AcademicYearId",
                table: "Terms",
                column: "AcademicYearId");

            migrationBuilder.CreateIndex(
                name: "IX_TermTranslations_TermId",
                table: "TermTranslations",
                column: "TermId");

            migrationBuilder.AddForeignKey(
                name: "FK_AcademicCalendars_AcademicYears_AcademicYearId",
                table: "AcademicCalendars",
                column: "AcademicYearId",
                principalTable: "AcademicYears",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AcademicCalendars_Terms_TermId",
                table: "AcademicCalendars",
                column: "TermId",
                principalTable: "Terms",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_StudentAcademicHistories_AcademicYears_AcademicYearId",
                table: "StudentAcademicHistories",
                column: "AcademicYearId",
                principalTable: "AcademicYears",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_StudentAcademicHistories_Terms_TermId",
                table: "StudentAcademicHistories",
                column: "TermId",
                principalTable: "Terms",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AcademicCalendars_AcademicYears_AcademicYearId",
                table: "AcademicCalendars");

            migrationBuilder.DropForeignKey(
                name: "FK_AcademicCalendars_Terms_TermId",
                table: "AcademicCalendars");

            migrationBuilder.DropForeignKey(
                name: "FK_StudentAcademicHistories_AcademicYears_AcademicYearId",
                table: "StudentAcademicHistories");

            migrationBuilder.DropForeignKey(
                name: "FK_StudentAcademicHistories_Terms_TermId",
                table: "StudentAcademicHistories");

            migrationBuilder.DropTable(
                name: "AcademicYearTranslations");

            migrationBuilder.DropTable(
                name: "TermTranslations");

            migrationBuilder.DropTable(
                name: "Terms");

            migrationBuilder.DropTable(
                name: "AcademicYears");

            migrationBuilder.DropIndex(
                name: "IX_StudentAcademicHistories_AcademicYearId",
                table: "StudentAcademicHistories");

            migrationBuilder.DropIndex(
                name: "IX_StudentAcademicHistories_TermId",
                table: "StudentAcademicHistories");

            migrationBuilder.DropIndex(
                name: "IX_AcademicCalendars_AcademicYearId",
                table: "AcademicCalendars");

            migrationBuilder.DropIndex(
                name: "IX_AcademicCalendars_TermId",
                table: "AcademicCalendars");

            migrationBuilder.DropColumn(
                name: "AcademicYearId",
                table: "StudentAcademicHistories");

            migrationBuilder.DropColumn(
                name: "TermId",
                table: "StudentAcademicHistories");

            migrationBuilder.DropColumn(
                name: "RelationType",
                table: "Parents");

            migrationBuilder.DropColumn(
                name: "AcademicYearId",
                table: "AcademicCalendars");

            migrationBuilder.DropColumn(
                name: "TermId",
                table: "AcademicCalendars");

            migrationBuilder.AddColumn<int>(
                name: "AcademicYear",
                table: "StudentAcademicHistories",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "StudentId_Year",
                table: "StudentAcademicHistories",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "StudentAcademicHistories",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "AcademicYear",
                table: "AcademicCalendars",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Semester",
                table: "AcademicCalendars",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "AcademicCalendars",
                type: "timestamp with time zone",
                nullable: true);
        }
    }
}
