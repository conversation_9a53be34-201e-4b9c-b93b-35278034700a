<!-- Hero Section -->
<app-default-hero
  translationPrefix="CAMPUS_LIFE"
  title="CAMPUS_LIFE.SPORTS"
  subtitle="CAMPUS_LIFE.SPORTS_SUBTITLE"
  theme="dark"
  size="large"
  alignment="center"
  backgroundImage="assets/images/campus/sports-hero.jpg">
</app-default-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'CAMPUS_LIFE.SPORTS_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.SPORTS_INTRO_P1' | translate }}</p>
      <p>{{ 'CAMPUS_LIFE.SPORTS_INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Athletic Philosophy Section -->
  <section class="philosophy-section">
    <h2>{{ 'CAMPUS_LIFE.ATHLETIC_PHILOSOPHY' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.PHILOSOPHY_INTRO' | translate }}</p>

    <div class="philosophy-grid">
      <div class="philosophy-item" *ngFor="let point of philosophyPoints">
        <h3>{{point.title}}</h3>
        <p>{{point.description}}</p>
      </div>
    </div>
  </section>

  <!-- Sports Programs Section -->
  <section class="sports-section">
    <h2>{{ 'CAMPUS_LIFE.SPORTS_PROGRAMS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.SPORTS_PROGRAMS_INTRO' | translate }}</p>

    <mat-tab-group animationDuration="300ms">
      <mat-tab *ngFor="let level of levels" [label]="level">
        <div class="level-content">
          <mat-tab-group animationDuration="300ms">
            <mat-tab *ngFor="let season of seasons" [label]="season">
              <div class="sports-grid">
                <mat-card class="sport-card" *ngFor="let sport of getSportsBySeasonAndLevel(season, level)">
                  <div class="sport-image">
                    <img [src]="sport.image" [alt]="sport.name">
                  </div>
                  <mat-card-content>
                    <h3>{{sport.name}}</h3>
                    <p class="sport-description">{{sport.description}}</p>
                    <div class="sport-details">
                      <div class="detail-item">
                        <mat-icon>person</mat-icon>
                        <span>{{sport.coach}}</span>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>
              </div>
            </mat-tab>
          </mat-tab-group>
        </div>
      </mat-tab>
    </mat-tab-group>
  </section>

  <!-- Athletic Facilities Section -->
  <section class="facilities-section">
    <h2>{{ 'CAMPUS_LIFE.ATHLETIC_FACILITIES' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.FACILITIES_INTRO' | translate }}</p>

    <div class="facilities-grid">
      <mat-card class="facility-card" *ngFor="let facility of facilities">
        <div class="facility-image">
          <img [src]="facility.image" [alt]="facility.name">
        </div>
        <mat-card-content>
          <h3>{{facility.name}}</h3>
          <ul class="facility-features">
            <li *ngFor="let feature of facility.features">{{feature}}</li>
          </ul>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Athletic Achievements Section -->
  <section class="achievements-section">
    <h2>{{ 'CAMPUS_LIFE.ATHLETIC_ACHIEVEMENTS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.ACHIEVEMENTS_INTRO' | translate }}</p>

    <div class="achievements-container">
      <div class="achievement-year" *ngFor="let year of getUniqueYears()">
        <h3>{{year}}</h3>
        <div class="achievement-list">
          <div class="achievement-item" *ngFor="let achievement of getAchievementsByYear(year)">
            <div class="achievement-sport">{{achievement.sport}}</div>
            <div class="achievement-title">{{achievement.achievement}}</div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Athletic Requirements Section -->
  <section class="requirements-section">
    <h2>{{ 'CAMPUS_LIFE.ATHLETIC_REQUIREMENTS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.REQUIREMENTS_INTRO' | translate }}</p>

    <div class="requirements-container">
      <div class="requirement-item">
        <div class="requirement-icon">
          <mat-icon>assignment</mat-icon>
        </div>
        <div class="requirement-content">
          <h3>{{ 'CAMPUS_LIFE.PHYSICAL_EXAMINATION' | translate }}</h3>
          <p>{{ 'CAMPUS_LIFE.PHYSICAL_EXAMINATION_DESC' | translate }}</p>
        </div>
      </div>

      <div class="requirement-item">
        <div class="requirement-icon">
          <mat-icon>school</mat-icon>
        </div>
        <div class="requirement-content">
          <h3>{{ 'CAMPUS_LIFE.ACADEMIC_ELIGIBILITY' | translate }}</h3>
          <p>{{ 'CAMPUS_LIFE.ACADEMIC_ELIGIBILITY_DESC' | translate }}</p>
        </div>
      </div>

      <div class="requirement-item">
        <div class="requirement-icon">
          <mat-icon>description</mat-icon>
        </div>
        <div class="requirement-content">
          <h3>{{ 'CAMPUS_LIFE.FORMS_WAIVERS' | translate }}</h3>
          <p>{{ 'CAMPUS_LIFE.FORMS_WAIVERS_DESC' | translate }}</p>
        </div>
      </div>

      <div class="requirement-item">
        <div class="requirement-icon">
          <mat-icon>paid</mat-icon>
        </div>
        <div class="requirement-content">
          <h3>{{ 'CAMPUS_LIFE.PARTICIPATION_FEE' | translate }}</h3>
          <p>{{ 'CAMPUS_LIFE.PARTICIPATION_FEE_DESC' | translate }}</p>
        </div>
      </div>
    </div>

    <div class="requirements-cta">
      <a mat-raised-button color="primary" href="assets/documents/athletic-forms.pdf" target="_blank">
        {{ 'CAMPUS_LIFE.DOWNLOAD_FORMS' | translate }}
      </a>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="contact-section">
    <div class="contact-content">
      <h2>{{ 'CAMPUS_LIFE.ATHLETIC_DEPARTMENT' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.ATHLETIC_DEPARTMENT_TEXT' | translate }}</p>
      <div class="contact-info">
        <div class="contact-item">
          <mat-icon>person</mat-icon>
          <div class="contact-details">
            <h3>{{ 'CAMPUS_LIFE.ATHLETIC_DIRECTOR' | translate }}</h3>
            <p>Mr. Michael Thompson</p>
            <p>mthompson&#64;school.edu</p>
            <p>(123) 456-7890 ext. 234</p>
          </div>
        </div>

        <div class="contact-item">
          <mat-icon>location_on</mat-icon>
          <div class="contact-details">
            <h3>{{ 'CAMPUS_LIFE.ATHLETIC_OFFICE' | translate }}</h3>
            <p>Main Gymnasium, Room 101</p>
            <p>Monday-Friday, 8:00 AM - 4:00 PM</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
