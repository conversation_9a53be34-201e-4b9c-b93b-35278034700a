namespace School.Application.DTOs;

/// <summary>
/// Admin login request DTO
/// </summary>
public class AdminLoginDto
{
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string? MfaCode { get; set; }
    public bool RememberMe { get; set; } = false;
}

/// <summary>
/// Admin-specific login response with tenant access information
/// </summary>
public class AdminLoginResponseDto : LoginResponseDto
{
    public List<string> UserRoles { get; set; } = new();
    public List<TenantAccessDto> AccessibleTenants { get; set; } = new();
}

/// <summary>
/// Tenant access information for admin users
/// </summary>
public class TenantAccessDto
{
    public Guid TenantId { get; set; }
    public string TenantName { get; set; } = string.Empty;
    public string TenantSlug { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public bool CanManage { get; set; }
}
