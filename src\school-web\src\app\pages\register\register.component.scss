.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
}

.register-card-wrapper {
  width: 100%;
  max-width: 600px;
}

.register-card {
  width: 100%;
  padding: 1.5rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: white;
}

.register-header {
  width: 100%;
  text-align: center;
  margin-bottom: 1.5rem;

  h1 {
    margin-bottom: 0.5rem;
    color: #3f51b5;
    font-size: 2rem;
  }

  p {
    color: #666;
    margin-bottom: 0;
  }
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  width: 100%;

  @media (max-width: 600px) {
    flex-direction: column;
    gap: 0;
  }
}

.full-width {
  width: 100%;
}

.half-width {
  width: 100%;

  @media (min-width: 601px) {
    width: calc(50% - 0.5rem);
  }
}

.terms-section {
  margin-bottom: 1rem;
  
  mat-error {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    margin-left: 1.5rem;
  }
}

.register-button {
  margin-top: 1rem;
  height: 48px;
  font-size: 1rem;
  
  mat-icon {
    margin-right: 0.5rem;
  }
}

.register-progress {
  margin-top: 1rem;
}

// Enhanced styles for improved register UI
.register-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  background: linear-gradient(135deg, var(--sys-color-primary) 0%, var(--sys-color-secondary) 100%);
  overflow-x: hidden;

  // Background Elements
  .background-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;

    .bg-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;

      &.bg-circle-1 {
        width: 200px;
        height: 200px;
        top: 10%;
        left: -5%;
        animation-delay: 0s;
      }

      &.bg-circle-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: -5%;
        animation-delay: 2s;
      }

      &.bg-circle-3 {
        width: 100px;
        height: 100px;
        top: 30%;
        right: 20%;
        animation-delay: 4s;
      }
    }
  }

  .register-content {
    position: relative;
    z-index: 2;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;

    .register-header {
      text-align: center;
      margin-bottom: 2rem;
      color: white;

      .logo-section {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        margin-bottom: 1rem;

        .logo-icon {
          font-size: 3rem;
          width: 3rem;
          height: 3rem;
        }

        .main-title {
          font-size: 2.5rem;
          font-weight: 700;
          margin: 0;
          letter-spacing: -0.025em;
        }
      }

      .subtitle {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
        font-weight: 400;
      }
    }
  }
}

// User type selection styles
.user-type-section {
  margin-bottom: 2rem;

  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--sys-color-on-surface);
    margin-bottom: 1rem;
    text-align: center;
  }

  .user-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;

    .user-type-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 1.5rem 1rem;
      border: 2px solid var(--sys-color-outline-variant);
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      background: var(--sys-color-surface);

      &:hover {
        border-color: var(--sys-color-primary);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      &.selected {
        border-color: var(--sys-color-primary);
        background: var(--sys-color-primary-container);
        color: var(--sys-color-on-primary-container);

        .type-icon {
          color: var(--sys-color-primary);
        }
      }

      .type-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
        margin-bottom: 0.5rem;
        color: var(--sys-color-on-surface-variant);
        transition: color 0.3s ease;
      }

      .type-label {
        font-size: 0.875rem;
        font-weight: 500;
        text-align: center;
      }

      .check-icon {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        color: var(--sys-color-primary);
        font-size: 1.25rem;
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .register-card {
    padding: 1rem;
  }
  
  .register-header h1 {
    font-size: 1.5rem;
  }
}
