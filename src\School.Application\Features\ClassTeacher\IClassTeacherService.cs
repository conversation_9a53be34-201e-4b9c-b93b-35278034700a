using School.Application.DTOs;
using School.Domain.Enums;
using School.Domain.Entities;

namespace School.Application.Features.ClassTeacher;

/// <summary>
/// Service interface for ClassTeacher management operations
/// </summary>
public interface IClassTeacherService
{
    // ClassTeacher CRUD operations
    Task<(IEnumerable<ClassTeacherDto> ClassTeachers, int TotalCount)> GetAllClassTeachersAsync(ClassTeacherFilterDto filter);
    Task<ClassTeacherDto?> GetClassTeacherByIdAsync(Guid id);
    Task<ClassTeacherDto?> GetClassTeacherBySectionAsync(Guid sectionId);
    Task<IEnumerable<ClassTeacherDto>> GetClassTeachersByFacultyAsync(Guid facultyId);
    Task<IEnumerable<ClassTeacherDto>> GetClassTeachersByAcademicYearAsync(Guid academicYearId);
    Task<IEnumerable<ClassTeacherDto>> GetActiveClassTeachersAsync(Guid academicYearId);
    Task<Guid> CreateClassTeacherAsync(CreateClassTeacherDto classTeacherDto);
    Task<bool> UpdateClassTeacherAsync(Guid id, UpdateClassTeacherDto classTeacherDto);
    Task<bool> DeleteClassTeacherAsync(Guid id);

    // ClassTeacher assignment management
    Task<bool> AssignClassTeacherAsync(Guid facultyId, Guid sectionId, Guid academicYearId, DateTime? startDate = null);
    Task<bool> ReassignClassTeacherAsync(Guid sectionId, Guid newFacultyId, string reason = "");
    Task<bool> RemoveClassTeacherAsync(Guid sectionId, string reason = "");
    Task<bool> TransferClassTeacherAsync(Guid facultyId, Guid fromSectionId, Guid toSectionId);

    // ClassTeacher status management
    Task<bool> ActivateClassTeacherAsync(Guid id);
    Task<bool> SuspendClassTeacherAsync(Guid id, string reason = "");
    Task<bool> CompleteClassTeacherAssignmentAsync(Guid id, DateTime? endDate = null);
    Task<bool> CancelClassTeacherAssignmentAsync(Guid id, string reason = "");
    Task<bool> UpdateClassTeacherStatusAsync(Guid id, ClassTeacherStatus status, string reason = "");

    // ClassTeacher validation
    Task<bool> ValidateClassTeacherAssignmentAsync(Guid facultyId, Guid sectionId, Guid academicYearId);
    Task<bool> CanAssignClassTeacherAsync(Guid facultyId, Guid sectionId);
    Task<bool> HasConflictingAssignmentAsync(Guid facultyId, Guid sectionId, Guid academicYearId);
    Task<bool> CanDeleteClassTeacherAsync(Guid id);

    // ClassTeacher workload management
    Task<ClassTeacherWorkloadDto> GetFacultyWorkloadAsync(Guid facultyId, Guid academicYearId);
    Task<IEnumerable<ClassTeacherWorkloadDto>> GetAllFacultyWorkloadsAsync(Guid academicYearId);
    Task<IEnumerable<ClassTeacherWorkloadDto>> GetOverloadedFacultyAsync(Guid academicYearId, decimal threshold = 100.0m);
    Task<bool> OptimizeWorkloadDistributionAsync(Guid academicYearId);
    Task<decimal> CalculateFacultyWorkloadScoreAsync(Guid facultyId, Guid academicYearId);

    // ClassTeacher assignment wizard
    Task<ClassTeacherAssignmentWizardDto> GetAssignmentWizardDataAsync(Guid academicYearId);
    Task<bool> ProcessAssignmentWizardAsync(ClassTeacherAssignmentWizardDto wizardDto);
    Task<IEnumerable<FacultyDto>> GetAvailableFacultyForAssignmentAsync(Guid academicYearId);
    Task<IEnumerable<SectionDto>> GetUnassignedSectionsAsync(Guid academicYearId);

    // ClassTeacher performance and analytics
    Task<ClassTeacherPerformanceDto> GetClassTeacherPerformanceAsync(Guid id);
    Task<IEnumerable<ClassTeacherPerformanceDto>> GetAllClassTeacherPerformanceAsync(Guid academicYearId);
    Task<bool> UpdatePerformanceNotesAsync(Guid id, string notes);
    Task<decimal> CalculateClassTeacherEffectivenessAsync(Guid id);

    // Bulk operations
    Task<bool> BulkAssignClassTeachersAsync(Dictionary<Guid, Guid> sectionFacultyMappings, Guid academicYearId);
    Task<bool> BulkUpdateClassTeacherStatusAsync(List<Guid> classTeacherIds, ClassTeacherStatus status, string reason = "");
    Task<bool> BulkTransferClassTeachersAsync(Dictionary<Guid, Guid> facultySectionMappings);
    Task<bool> ProcessBulkOperationAsync(BulkClassTeacherOperationDto operationDto);

    // ClassTeacher scheduling and communication
    Task<bool> UpdateContactScheduleAsync(Guid id, string contactSchedule);
    Task<bool> UpdateOfficeHoursAsync(Guid id, string officeHours);
    Task<bool> UpdateResponsibilitiesAsync(Guid id, string responsibilities);
    Task<bool> UpdateSpecialDutiesAsync(Guid id, string specialDuties);

    // Import/Export operations
    Task<bool> ImportClassTeachersFromCsvAsync(Stream csvStream, Guid academicYearId);
    Task<Stream> ExportClassTeachersToCsvAsync(Guid academicYearId);
    Task<bool> DuplicateAssignmentsToNewAcademicYearAsync(Guid sourceAcademicYearId, Guid targetAcademicYearId);

    // ClassTeacher history and tracking
    Task<IEnumerable<ClassTeacherDto>> GetClassTeacherHistoryAsync(Guid facultyId);
    Task<IEnumerable<ClassTeacherDto>> GetSectionHistoryAsync(Guid sectionId);
    Task<bool> ArchiveClassTeacherAsync(Guid id);
    Task<bool> RestoreClassTeacherAsync(Guid id);

    // Academic year transitions
    Task<bool> RolloverClassTeachersToNewYearAsync(Guid sourceAcademicYearId, Guid targetAcademicYearId);
    Task<IEnumerable<ClassTeacherDto>> GetExpiredAssignmentsAsync(Guid academicYearId);
    Task<bool> RenewClassTeacherAssignmentAsync(Guid id, Guid newAcademicYearId);

    // ClassTeacher recommendations and suggestions
    Task<IEnumerable<FacultyDto>> GetRecommendedFacultyForSectionAsync(Guid sectionId);
    Task<IEnumerable<SectionDto>> GetRecommendedSectionsForFacultyAsync(Guid facultyId, Guid academicYearId);
    Task<IEnumerable<string>> GetAssignmentOptimizationSuggestionsAsync(Guid academicYearId);

    // ClassTeacher reporting
    Task<Stream> GenerateClassTeacherReportAsync(Guid academicYearId, string reportType = "summary");
    Task<Stream> GenerateWorkloadAnalysisReportAsync(Guid academicYearId);
    Task<Stream> GeneratePerformanceReportAsync(Guid academicYearId);

    // ClassTeacher notifications and alerts
    Task<IEnumerable<string>> GetClassTeacherAlertsAsync(Guid facultyId);
    Task<bool> SendAssignmentNotificationAsync(Guid classTeacherId);
    Task<bool> SendWorkloadWarningAsync(Guid facultyId);
}
