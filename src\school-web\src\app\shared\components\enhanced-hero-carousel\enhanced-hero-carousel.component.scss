@use 'sass:color';

// Variables
$primary-color: #3f51b5;
$primary-light: #757de8;
$primary-dark: #002984;
$accent-color: #ff4081;
$light-text: #ffffff;
$dark-text: #212121;
$pattern-color: rgba(255, 255, 255, 0.05);

// Enhanced Hero Carousel Base Styles
.enhanced-hero-carousel {
  position: relative;
  width: 100%;
  overflow: hidden;

  // Fixed height of 40% viewport height
  height: 40vh;
  min-height: 250px; // Minimum height for very small screens
  max-height: 500px; // Maximum height to prevent excessive space on large screens

  // Size variations only affect content scaling
  &.size-small {
    .carousel-content {
      transform: scale(0.9);
    }
  }

  &.size-medium {
    .carousel-content {
      transform: scale(1);
    }
  }

  &.size-large {
    .carousel-content {
      transform: scale(1.1);
    }
  }

  // Theme variations
  &.theme-light {
    color: $dark-text;

    .slide-overlay {
      background-color: rgba(255, 255, 255, 0.7);
    }

    .carousel-content {
      .slide-title, .slide-subtitle {
        color: $primary-dark;
      }

      .slide-description {
        color: rgba(0, 0, 0, 0.8);
      }
    }

    .carousel-arrow {
      background-color: rgba(255, 255, 255, 0.7);
      color: $primary-dark;

      &:hover {
        background-color: rgba(255, 255, 255, 0.9);
      }
    }

    .carousel-indicators button {
      border-color: $primary-dark;

      &.active, &:hover {
        background-color: $primary-dark;
      }
    }
  }

  &.theme-dark {
    color: $light-text;

    .slide-overlay {
      background-color: rgba(0, 0, 0, 0.5);
    }

    .carousel-content {
      .slide-title, .slide-subtitle {
        color: $light-text;
      }

      .slide-description {
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .carousel-arrow {
      background-color: rgba(0, 0, 0, 0.3);
      color: $light-text;

      &:hover {
        background-color: rgba(0, 0, 0, 0.5);
      }
    }

    .carousel-indicators button {
      border-color: $light-text;

      &.active, &:hover {
        background-color: $light-text;
      }
    }
  }
}

// Carousel slides
.carousel-slides {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  .carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-color: $primary-color; // Default background if no image
    opacity: 0;
    transition: opacity 1s ease-in-out;

    &.active {
      opacity: 1;
      z-index: 2;

      .slide-overlay {
        animation: fadeIn 1s ease-in-out;
      }
    }
  }
}

// Slide overlay with pattern
.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(45deg, $pattern-color 25%, transparent 25%, transparent 75%, $pattern-color 75%, $pattern-color),
    linear-gradient(45deg, $pattern-color 25%, transparent 25%, transparent 75%, $pattern-color 75%, $pattern-color);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
  z-index: 1;
}

// Decorative elements
.slide-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  overflow: hidden;

  .circle-decoration {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);

    &.circle-1 {
      width: 400px;
      height: 400px;
      top: -150px;
      right: 10%;
      animation: float 15s infinite ease-in-out;
    }

    &.circle-2 {
      width: 300px;
      height: 300px;
      bottom: -100px;
      left: 5%;
      animation: float 20s infinite ease-in-out reverse;
    }

    &.circle-3 {
      width: 200px;
      height: 200px;
      top: 30%;
      left: 20%;
      animation: float 12s infinite ease-in-out 2s;
    }
  }
}

// Content container
.carousel-container {
  position: relative;
  z-index: 5;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-sizing: border-box;
}

// Carousel content
.carousel-content {
  max-width: 800px;
  padding: 0 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: auto;
  width: 100%;

  .slide-title {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.2;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 0.8s ease-out;

    &::after {
      content: '';
      display: block;
      width: 80px;
      height: 4px;
      background-color: $accent-color;
      margin: 10px auto;
    }

    // Handle long titles
    &.long-text {
      font-size: 2.2rem;
      line-height: 1.1;
    }

    &.very-long-text {
      font-size: 1.8rem;
      line-height: 1.1;
    }
  }

  .slide-subtitle {
    font-size: 1.6rem;
    font-weight: 500;
    margin-bottom: 0.7rem;
    opacity: 0.9;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 0.8s ease-out 0.2s both;

    // Handle long subtitles
    &.long-text {
      font-size: 1.3rem;
      line-height: 1.2;
    }

    &.very-long-text {
      font-size: 1.1rem;
      line-height: 1.2;
    }
  }

  .slide-description {
    font-size: 1.2rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    animation: fadeInUp 0.8s ease-out 0.4s both;

    // Handle long descriptions
    &.long-text {
      font-size: 1rem;
      line-height: 1.4;
      max-height: 5.6em; // 4 lines of text
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      line-clamp: 4;
      -webkit-box-orient: vertical;
    }

    &.very-long-text {
      font-size: 0.9rem;
      line-height: 1.3;
      max-height: 3.9em; // 3 lines of text
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
    }
  }

  .slide-buttons, .custom-content {
    display: flex;
    gap: 12px;
    margin-top: 15px;
    justify-content: center;
    animation: fadeInUp 0.8s ease-out 0.6s both;

    a {
      padding: 8px 20px;
      font-weight: 500;
      border-radius: 30px;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      font-size: 1rem;

      mat-icon {
        margin-right: 8px;
        font-size: 20px;
        height: 20px;
        width: 20px;
      }

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }
    }
  }
}

// Navigation controls
.carousel-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: 6;
  display: flex;
  justify-content: space-between;
  padding: 0 10px;

  .carousel-arrow {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;

    mat-icon {
      font-size: 24px;
      height: 24px;
      width: 24px;
    }

    &:focus {
      outline: none;
    }

    &:hover {
      transform: scale(1.1);
    }
  }
}

// Indicators
.carousel-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 6;
  display: flex;
  gap: 10px;

  button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid;
    background: transparent;
    padding: 0;
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      transform: scale(1.3);
    }

    &:hover {
      transform: scale(1.3);
    }

    &:focus {
      outline: none;
    }
  }
}

// Wave shape at bottom
.carousel-wave {
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  z-index: 7;
  line-height: 0;

  svg {
    width: 100%;
    height: 50px;
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

// Responsive adjustments
@media (max-width: 992px) {
  .enhanced-hero-carousel {
    .carousel-content {
      padding: 0 30px;

      .slide-title {
        font-size: 2.5rem;
      }

      .slide-subtitle {
        font-size: 1.6rem;
      }

      .slide-description {
        font-size: 1.1rem;
      }

      .slide-buttons, .custom-content {
        a {
          padding: 10px 24px;
          font-size: 1rem;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .enhanced-hero-carousel {
    .carousel-container {
      padding: 30px 0;
    }

    .carousel-content {
      padding: 20px 20px;

      .slide-title {
        font-size: 2.2rem;

        &::after {
          width: 70px;
          margin: 12px auto;
        }
      }

      .slide-subtitle {
        font-size: 1.4rem;
      }

      .slide-description {
        font-size: 1rem;
        margin-bottom: 1rem;
      }

      .slide-buttons, .custom-content {
        margin-top: 20px;

        a {
          padding: 8px 20px;
          font-size: 1rem;

          mat-icon {
            font-size: 20px;
            height: 20px;
            width: 20px;
            margin-right: 8px;
          }
        }
      }
    }

    .carousel-controls {
      .carousel-arrow {
        width: 40px;
        height: 40px;

        mat-icon {
          font-size: 28px;
          height: 28px;
          width: 28px;
        }
      }
    }

    .carousel-indicators {
      bottom: 40px;
    }
  }
}

@media (max-width: 576px) {
  .enhanced-hero-carousel {
    .carousel-container {
      padding: 20px 0;
    }

    .carousel-content {
      padding: 15px 15px;

      .slide-title {
        font-size: 1.8rem;

        &::after {
          width: 60px;
          height: 3px;
          margin: 8px auto;
        }
      }

      .slide-subtitle {
        font-size: 1.2rem;
        margin-bottom: 0.7rem;
      }

      .slide-description {
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 0.8rem;
      }

      .slide-buttons, .custom-content {
        flex-direction: row; // Keep buttons side by side
        gap: 10px;
        margin-top: 15px;

        a {
          padding: 7px 18px;
          font-size: 0.9rem;

          mat-icon {
            font-size: 18px;
            height: 18px;
            width: 18px;
            margin-right: 6px;
          }
        }
      }
    }

    .carousel-controls {
      .carousel-arrow {
        width: 36px;
        height: 36px;

        mat-icon {
          font-size: 24px;
          height: 24px;
          width: 24px;
        }
      }
    }

    .carousel-indicators {
      bottom: 30px;

      button {
        width: 10px;
        height: 10px;
      }
    }

    .carousel-wave svg {
      height: 35px;
    }
  }
}
