using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace School.Application.DTOs
{
    public class ClubUpdateDto
    {
        [StringLength(100)]
        public string Name { get; set; }
        
        public string Description { get; set; }
        
        [StringLength(250)]
        public string ShortDescription { get; set; }
        
        [StringLength(50)]
        public string Category { get; set; }
        
        [StringLength(100)]
        public string MeetingSchedule { get; set; }
        
        [StringLength(100)]
        public string Location { get; set; }
        
        public string Requirements { get; set; }
        
        public string JoinProcess { get; set; }
        
        [EmailAddress]
        public string ContactEmail { get; set; }
        
        [Url]
        public string Website { get; set; }
        
        [Url]
        public string Instagram { get; set; }
        
        [Url]
        public string Facebook { get; set; }
        
        public bool? IsFeatured { get; set; }
        
        public int? DisplayOrder { get; set; }
        
        public bool? IsActive { get; set; }
        
        public int? ProfileImageId { get; set; }
        
        [Url]
        public string ProfileImageUrl { get; set; }
        
        // Related collections
        public ICollection<ClubTranslationUpdateDto> Translations { get; set; }
        public ICollection<ClubAdvisorUpdateDto> Advisors { get; set; }
        public ICollection<ClubLeaderUpdateDto> Leaders { get; set; }
        public ICollection<ClubActivityUpdateDto> Activities { get; set; }
        public ICollection<ClubAchievementUpdateDto> Achievements { get; set; }
        public ICollection<ClubEventUpdateDto> Events { get; set; }
        public ICollection<ClubGalleryItemUpdateDto> GalleryItems { get; set; }
    }
}
