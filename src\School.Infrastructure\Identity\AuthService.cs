using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Auth;
using School.Domain.Enums;
using School.Infrastructure.Persistence;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace School.Infrastructure.Identity
{
    public class AuthService : IAuthService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly ITokenGenerator _tokenGenerator;
        private readonly IMfaService _mfaService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            ITokenGenerator tokenGenerator,
            IMfaService mfaService,
            ICurrentUserService currentUserService,
            ApplicationDbContext context,
            ILogger<AuthService> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _tokenGenerator = tokenGenerator;
            _mfaService = mfaService;
            _currentUserService = currentUserService;
            _context = context;
            _logger = logger;
        }

        public async Task<LoginResponseDto?> LoginAsync(LoginDto loginDto)
        {
            var user = await _userManager.FindByNameAsync(loginDto.Username);

            if (user == null || user.IsDeleted || !user.IsActive)
            {
                await LogLoginAttempt(loginDto.Username, false, "User not found or inactive");
                _logger.LogWarning("Login failed: User {Username} not found or inactive", loginDto.Username);
                return null;
            }

            // Check if account is locked
            if (user.AccountLockedUntil.HasValue && user.AccountLockedUntil > DateTime.UtcNow)
            {
                await LogLoginAttempt(user.Id, false, "Account locked");
                _logger.LogWarning("Login failed: Account {UserId} is locked until {LockUntil}", user.Id, user.AccountLockedUntil);
                return null;
            }

            var result = await _signInManager.CheckPasswordSignInAsync(user, loginDto.Password, false);

            if (!result.Succeeded)
            {
                await HandleFailedLogin(user);
                await LogLoginAttempt(user.Id, false, "Invalid password");
                _logger.LogWarning("Login failed: Invalid password for user {Username}", loginDto.Username);
                return null;
            }

            // Reset failed login attempts on successful password verification
            if (user.FailedLoginAttempts > 0)
            {
                user.FailedLoginAttempts = 0;
                user.LastFailedLogin = null;
                await _userManager.UpdateAsync(user);
            }

            // Check if MFA is required
            if (user.IsMfaEnabled)
            {
                // If MFA code is provided, verify it
                if (!string.IsNullOrEmpty(loginDto.MfaCode))
                {
                    var mfaValid = await _mfaService.VerifyMfaCodeAsync(user.Id, loginDto.MfaCode) ||
                                   await _mfaService.VerifyBackupCodeAsync(user.Id, loginDto.MfaCode);

                    if (!mfaValid)
                    {
                        await LogLoginAttempt(user.Id, false, "Invalid MFA code");
                        _logger.LogWarning("Login failed: Invalid MFA code for user {Username}", loginDto.Username);
                        return null;
                    }
                }
                else
                {
                    // Return response indicating MFA is required
                    var mfaToken = _tokenGenerator.GenerateMfaToken(user.Id);
                    await LogLoginAttempt(user.Id, false, "MFA required");

                    return new LoginResponseDto
                    {
                        Token = string.Empty,
                        Expiration = DateTime.UtcNow,
                        RequiresMfa = true,
                        MfaToken = mfaToken,
                        User = CreateUserDto(user)
                    };
                }
            }

            // Generate tokens
            var (token, expiration) = await _tokenGenerator.GenerateTokenAsync(user);
            var refreshToken = _tokenGenerator.GenerateRefreshToken();
            var refreshTokenExpiration = DateTime.UtcNow.AddDays(7); // 7 days for refresh token

            // Save refresh token
            await SaveRefreshTokenAsync(user.Id, refreshToken, refreshTokenExpiration);

            // Update last login time
            user.LastLogin = DateTime.UtcNow;
            await _userManager.UpdateAsync(user);

            await LogLoginAttempt(user.Id, true, "Login successful", user.IsMfaEnabled);
            _logger.LogInformation("User {Username} logged in successfully", user.UserName);

            return new LoginResponseDto
            {
                Token = token,
                RefreshToken = refreshToken,
                Expiration = expiration,
                RefreshTokenExpiration = refreshTokenExpiration,
                RequiresMfa = false,
                User = CreateUserDto(user)
            };
        }

        public async Task<Guid> RegisterAsync(UserCreateDto userDto)
        {
            // Validate password confirmation
            if (userDto.Password != userDto.ConfirmPassword)
            {
                throw new InvalidOperationException("Password and confirmation password do not match");
            }

            // Check if username already exists
            var existingUser = await _userManager.FindByNameAsync(userDto.Username);
            if (existingUser != null)
            {
                _logger.LogWarning("Registration failed: Username {Username} already exists", userDto.Username);
                throw new InvalidOperationException($"Username '{userDto.Username}' is already taken");
            }

            // Check if email already exists
            if (!string.IsNullOrEmpty(userDto.Email))
            {
                existingUser = await _userManager.FindByEmailAsync(userDto.Email);
                if (existingUser != null)
                {
                    _logger.LogWarning("Registration failed: Email {Email} already exists", userDto.Email);
                    throw new InvalidOperationException($"Email '{userDto.Email}' is already registered");
                }
            }

            var user = new ApplicationUser
            {
                UserName = userDto.Username,
                Email = userDto.Email ?? string.Empty,
                FirstName = userDto.FirstName ?? string.Empty,
                LastName = userDto.LastName ?? string.Empty,
                Role = userDto.Role,
                PhoneNumber = userDto.PhoneNumber,
                DateOfBirth = userDto.DateOfBirth,
                Address = userDto.Address,
                PreferredLanguage = userDto.PreferredLanguage,
                PreferredTheme = userDto.PreferredTheme,
                TimeZone = userDto.TimeZone,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = _currentUserService.UserId?.ToString(),
                LastPasswordChange = DateTime.UtcNow
            };

            var result = await _userManager.CreateAsync(user, userDto.Password);

            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                _logger.LogError("Registration failed: {Errors}", errors);
                throw new InvalidOperationException($"Failed to create user: {errors}");
            }

            // Add user to role based on UserRole enum
            await _userManager.AddToRoleAsync(user, userDto.Role.ToString());

            _logger.LogInformation("User {Username} registered successfully with ID {UserId}", user.UserName, user.Id);

            return Guid.Parse(user.Id);
        }

        // Additional methods for enhanced authentication
        public async Task<RefreshTokenResponseDto?> RefreshTokenAsync(RefreshTokenDto refreshTokenDto)
        {
            var refreshToken = await _context.Set<UserRefreshToken>()
                .Include(rt => rt.User)
                .FirstOrDefaultAsync(rt => rt.Token == refreshTokenDto.RefreshToken && !rt.IsRevoked);

            if (refreshToken == null || refreshToken.ExpiryDate <= DateTime.UtcNow)
            {
                _logger.LogWarning("Invalid or expired refresh token");
                return null;
            }

            var user = refreshToken.User;
            if (user.IsDeleted || !user.IsActive)
            {
                _logger.LogWarning("Refresh token belongs to inactive user {UserId}", user.Id);
                return null;
            }

            // Generate new tokens
            var (newToken, expiration) = await _tokenGenerator.GenerateTokenAsync(user);
            var newRefreshToken = _tokenGenerator.GenerateRefreshToken();
            var newRefreshTokenExpiration = DateTime.UtcNow.AddDays(7);

            // Revoke old refresh token
            refreshToken.IsRevoked = true;
            refreshToken.RevokedAt = DateTime.UtcNow;

            // Save new refresh token
            await SaveRefreshTokenAsync(user.Id, newRefreshToken, newRefreshTokenExpiration);

            await _context.SaveChangesAsync();

            _logger.LogInformation("Token refreshed for user {UserId}", user.Id);

            return new RefreshTokenResponseDto
            {
                Token = newToken,
                RefreshToken = newRefreshToken,
                Expiration = expiration,
                RefreshTokenExpiration = newRefreshTokenExpiration
            };
        }
        // Helper methods
        private async Task HandleFailedLogin(ApplicationUser user)
        {
            user.FailedLoginAttempts++;
            user.LastFailedLogin = DateTime.UtcNow;

            // Lock account after 5 failed attempts for 30 minutes
            if (user.FailedLoginAttempts >= 5)
            {
                user.AccountLockedUntil = DateTime.UtcNow.AddMinutes(30);
                _logger.LogWarning("Account {UserId} locked due to too many failed login attempts", user.Id);
            }

            await _userManager.UpdateAsync(user);
        }

        private async Task LogLoginAttempt(string userIdOrUsername, bool isSuccessful, string? failureReason = null, bool isMfaUsed = false)
        {
            var loginHistory = new UserLoginHistory
            {
                UserId = userIdOrUsername,
                LoginTime = DateTime.UtcNow,
                IsSuccessful = isSuccessful,
                FailureReason = failureReason,
                IsMfaUsed = isMfaUsed
                // IpAddress and UserAgent would be set from HTTP context in a real implementation
            };

            _context.Set<UserLoginHistory>().Add(loginHistory);
            await _context.SaveChangesAsync();
        }

        private async Task SaveRefreshTokenAsync(string userId, string refreshToken, DateTime expiration)
        {
            var tokenEntity = new UserRefreshToken
            {
                UserId = userId,
                Token = refreshToken,
                ExpiryDate = expiration,
                CreatedAt = DateTime.UtcNow
                // CreatedByIp would be set from HTTP context in a real implementation
            };

            _context.Set<UserRefreshToken>().Add(tokenEntity);
            await _context.SaveChangesAsync();
        }

        private UserDto CreateUserDto(ApplicationUser user)
        {
            return new UserDto
            {
                Id = Guid.Parse(user.Id),
                Username = user.UserName ?? string.Empty,
                Email = user.Email ?? string.Empty,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Role = user.Role,
                LastLogin = user.LastLogin,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                LastModifiedAt = user.LastModifiedAt,
                IsMfaEnabled = user.IsMfaEnabled,
                LastMfaSetup = user.LastMfaSetup,
                PreferredLanguage = user.PreferredLanguage,
                PreferredTheme = user.PreferredTheme,
                TimeZone = user.TimeZone,
                ProfileImageUrl = user.ProfileImageUrl,
                PhoneNumber = user.PhoneNumber,
                PhoneNumber2 = user.PhoneNumber2,
                DateOfBirth = user.DateOfBirth,
                Address = user.Address,
                FailedLoginAttempts = user.FailedLoginAttempts,
                LastFailedLogin = user.LastFailedLogin,
                AccountLockedUntil = user.AccountLockedUntil,
                LastPasswordChange = user.LastPasswordChange
            };
        }

        public async Task LogoutAsync(string refreshToken)
        {
            var token = await _context.Set<UserRefreshToken>()
                .FirstOrDefaultAsync(rt => rt.Token == refreshToken);

            if (token != null)
            {
                token.IsRevoked = true;
                token.RevokedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> RevokeAllTokensAsync(string userId)
        {
            var tokens = await _context.Set<UserRefreshToken>()
                .Where(rt => rt.UserId == userId && !rt.IsRevoked)
                .ToListAsync();

            foreach (var token in tokens)
            {
                token.IsRevoked = true;
                token.RevokedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("All refresh tokens revoked for user {UserId}", userId);

            return true;
        }
    }
}
