<div class="attendance-container">
  <h1 class="page-title">Attendance Management</h1>

  <div *ngIf="loading.faculty" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error.faculty" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load faculty data. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadFacultyData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading.faculty && !error.faculty && faculty" class="attendance-content">
    <!-- Filter Form -->
    <mat-card class="filter-card">
      <mat-card-header>
        <mat-card-title>Select Class</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <form [formGroup]="filterForm" (ngSubmit)="loadAttendance()">
          <div class="filter-form">
            <mat-form-field appearance="outline">
              <mat-label>Grade</mat-label>
              <mat-select formControlName="grade">
                <mat-option *ngFor="let grade of getUniqueGrades()" [value]="grade">
                  {{ grade }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="filterForm.get('grade')?.hasError('required')">
                Grade is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Section</mat-label>
              <mat-select formControlName="section" [disabled]="!filterForm.get('grade')?.value">
                <mat-option *ngFor="let section of getSectionsForGrade(filterForm.get('grade')?.value)" [value]="section">
                  {{ section }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="filterForm.get('section')?.hasError('required')">
                Section is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="date">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <mat-error *ngIf="filterForm.get('date')?.hasError('required')">
                Date is required
              </mat-error>
            </mat-form-field>

            <div class="filter-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="filterForm.invalid">
                View Attendance
              </button>
              <button mat-raised-button color="accent" type="button" (click)="openRecordAttendanceDialog()" [disabled]="filterForm.invalid">
                Record Attendance
              </button>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>

    <!-- Loading Indicator -->
    <div *ngIf="loading.attendance" class="attendance-loading">
      <mat-progress-bar mode="indeterminate"></mat-progress-bar>
    </div>

    <!-- Error Message -->
    <div *ngIf="error.attendance" class="attendance-error">
      <mat-error>
        <mat-icon>error</mat-icon>
        <span>Failed to load attendance data. Please try again.</span>
        <button mat-button color="warn" (click)="loadAttendance()">Retry</button>
      </mat-error>
    </div>

    <!-- Attendance Table -->
    <div *ngIf="!loading.attendance && !error.attendance && attendanceData.length > 0" class="attendance-table-container mat-elevation-z8">
      <table mat-table [dataSource]="attendanceData" class="attendance-table">
        <!-- Roll Number Column -->
        <ng-container matColumnDef="rollNumber">
          <th mat-header-cell *matHeaderCellDef>Roll No.</th>
          <td mat-cell *matCellDef="let student">{{ student.rollNumber }}</td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Student Name</th>
          <td mat-cell *matCellDef="let student">{{ student.firstName }} {{ student.lastName }}</td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let student">
            <span class="status-badge" [ngClass]="getStatusClass(student.status)">
              {{ getStatusText(student.status) }}
            </span>
          </td>
        </ng-container>

        <!-- Remarks Column -->
        <ng-container matColumnDef="remarks">
          <th mat-header-cell *matHeaderCellDef>Remarks</th>
          <td mat-cell *matCellDef="let student">{{ student.remarks }}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>

    <!-- No Data Message -->
    <div *ngIf="!loading.attendance && !error.attendance && attendanceData.length === 0 && filterForm.valid && filterForm.touched" class="no-data">
      <mat-card>
        <mat-card-content>
          <p>No attendance records found for the selected criteria.</p>
          <p>Click "Record Attendance" to create new records.</p>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
