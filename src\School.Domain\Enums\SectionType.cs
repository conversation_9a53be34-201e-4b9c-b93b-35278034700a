namespace School.Domain.Enums;

/// <summary>
/// Type of section for categorization
/// </summary>
public enum SectionType
{
    /// <summary>
    /// Regular academic section
    /// </summary>
    Regular = 1,

    /// <summary>
    /// Science stream section
    /// </summary>
    Science = 2,

    /// <summary>
    /// Commerce stream section
    /// </summary>
    Commerce = 3,

    /// <summary>
    /// Arts/Humanities stream section
    /// </summary>
    Arts = 4,

    /// <summary>
    /// Vocational training section
    /// </summary>
    Vocational = 5,

    /// <summary>
    /// Special needs section
    /// </summary>
    Special = 6,

    /// <summary>
    /// Honors or advanced section
    /// </summary>
    Honors = 7
}
