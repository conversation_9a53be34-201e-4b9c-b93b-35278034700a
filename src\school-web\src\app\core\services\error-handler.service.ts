import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlerService {
  constructor(private snackBar: MatSnackBar) { }

  /**
   * Handle HTTP errors and display appropriate messages
   * @param error The HTTP error response
   * @param context Optional context information for the error
   * @returns An observable that errors with the formatted error
   */
  handleError(error: HttpErrorResponse, context?: string): Observable<never> {
    let errorMessage = 'An error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = this.getServerErrorMessage(error);
    }
    
    // Add context if provided
    if (context) {
      errorMessage = `${context}: ${errorMessage}`;
    }
    
    // Log the error
    console.error(errorMessage, error);
    
    // Show a snackbar with the error message
    this.showErrorSnackbar(errorMessage);
    
    // Return an observable with a user-facing error message
    return throwError(() => new Error(errorMessage));
  }

  /**
   * Show an error message in a snackbar
   * @param message The error message to display
   */
  showErrorSnackbar(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  /**
   * Show a success message in a snackbar
   * @param message The success message to display
   */
  showSuccessSnackbar(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  /**
   * Get a user-friendly error message from a server error response
   * @param error The HTTP error response
   * @returns A user-friendly error message
   */
  private getServerErrorMessage(error: HttpErrorResponse): string {
    switch (error.status) {
      case 400:
        return this.getBadRequestMessage(error);
      case 401:
        return 'You are not authorized to access this resource';
      case 403:
        return 'You do not have permission to access this resource';
      case 404:
        return 'The requested resource was not found';
      case 500:
        return 'Internal server error';
      default:
        return `Unknown server error: ${error.message}`;
    }
  }

  /**
   * Get a user-friendly message for 400 Bad Request errors
   * @param error The HTTP error response
   * @returns A user-friendly error message
   */
  private getBadRequestMessage(error: HttpErrorResponse): string {
    if (error.error && typeof error.error === 'object') {
      // Check for validation errors
      if (error.error.errors) {
        const validationErrors = Object.values(error.error.errors).flat();
        if (validationErrors.length > 0) {
          return validationErrors.join(', ');
        }
      }
      
      // Check for error message
      if (error.error.message) {
        return error.error.message;
      }
    }
    
    return 'Invalid request';
  }
}
