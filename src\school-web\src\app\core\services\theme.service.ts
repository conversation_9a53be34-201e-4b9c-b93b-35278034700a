import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export type Theme = 'light' | 'dark';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_STORAGE_KEY = 'schoolWebTheme';
  private currentThemeSubject = new BehaviorSubject<Theme>('light');
  
  constructor() {
    this.initializeTheme();
  }

  /**
   * Get current theme as observable
   */
  get currentTheme$(): Observable<Theme> {
    return this.currentThemeSubject.asObservable();
  }

  /**
   * Get current theme value
   */
  get currentTheme(): Theme {
    return this.currentThemeSubject.value;
  }

  /**
   * Initialize theme from local storage or system preference
   */
  private initializeTheme(): void {
    try {
      // Try to get theme from local storage first
      const storedTheme = localStorage.getItem(this.THEME_STORAGE_KEY);
      
      if (storedTheme && this.isValidTheme(storedTheme)) {
        this.setTheme(storedTheme as Theme, false);
        console.log('Using theme from local storage:', storedTheme);
      } else {
        // Check system preference
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        const systemTheme: Theme = prefersDark ? 'dark' : 'light';
        this.setTheme(systemTheme, true);
        console.log('Using system/default theme:', systemTheme);
      }
    } catch (error) {
      console.error('Error initializing theme:', error);
      this.setTheme('light', true);
    }
  }

  /**
   * Set theme
   */
  setTheme(theme: Theme, saveToStorage: boolean = true): void {
    if (!this.isValidTheme(theme)) {
      console.error(`Invalid theme: ${theme}`);
      return;
    }

    // Update the subject
    this.currentThemeSubject.next(theme);

    // Apply theme to document
    this.applyTheme(theme);

    // Save to storage if requested
    if (saveToStorage) {
      this.saveThemePreference(theme);
    }

    console.log('Theme set to:', theme);
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme(): void {
    const newTheme: Theme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  /**
   * Apply theme to document body
   */
  private applyTheme(theme: Theme): void {
    try {
      // Remove existing theme classes
      document.body.classList.remove('light-theme', 'dark-theme');
      
      // Add new theme class
      document.body.classList.add(`${theme}-theme`);
      
      // Set data attribute for CSS targeting
      document.body.setAttribute('data-theme', theme);
      
      console.log('Applied theme:', theme);
    } catch (error) {
      console.error('Error applying theme:', error);
    }
  }

  /**
   * Save theme preference to local storage
   */
  private saveThemePreference(theme: Theme): void {
    try {
      localStorage.setItem(this.THEME_STORAGE_KEY, theme);
      console.log('Theme preference saved:', theme);
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  }

  /**
   * Check if theme is valid
   */
  private isValidTheme(theme: string): boolean {
    return ['light', 'dark'].includes(theme);
  }

  /**
   * Listen to system theme changes
   */
  listenToSystemThemeChanges(): void {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      mediaQuery.addEventListener('change', (e) => {
        // Only update if user hasn't manually set a preference
        const storedTheme = localStorage.getItem(this.THEME_STORAGE_KEY);
        if (!storedTheme) {
          const systemTheme: Theme = e.matches ? 'dark' : 'light';
          this.setTheme(systemTheme, false);
          console.log('System theme changed to:', systemTheme);
        }
      });
    }
  }
}
