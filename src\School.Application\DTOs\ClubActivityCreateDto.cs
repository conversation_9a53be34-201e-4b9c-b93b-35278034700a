using System.Collections.Generic;

namespace School.Application.DTOs;

public class ClubActivityCreateDto
{
    public string Description { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public List<ClubActivityTranslationCreateDto> Translations { get; set; } = new();
}

public class ClubActivityTranslationCreateDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}
