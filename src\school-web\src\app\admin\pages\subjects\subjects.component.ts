import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { SubjectService } from '../../../core/services/subject.service';
import { GradeService } from '../../../core/services/grade.service';
import { AcademicYearService } from '../../../core/services/academic-year.service';
import { Subject, SubjectFilter, SubjectType, DifficultyLevel } from '../../../core/models/subject.model';
// import { SubjectCreateEditDialogComponent } from './subject-create-edit-dialog.component';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-subjects',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatSnackBarModule,
    MatCardModule,
    MatToolbarModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatChipsModule,
    MatBadgeModule,
    MatMenuModule,
    TranslateModule
  ],
  templateUrl: './subjects.component.html',
  styleUrls: ['./subjects.component.scss']
})
export class SubjectsComponent implements OnInit {
  private dialog = inject(MatDialog);
  private snackBar = inject(MatSnackBar);
  private subjectService = inject(SubjectService);
  private gradeService = inject(GradeService);
  private academicYearService = inject(AcademicYearService);

  displayedColumns: string[] = [
    'code',
    'name',
    'subjectType',
    'credits',
    'hoursPerWeek',
    'gradeName',
    'departmentName',
    'isCore',
    'isActive',
    'actions'
  ];

  subjects: Subject[] = [];
  loading = false;
  totalCount = 0;
  pageSize = 10;
  pageIndex = 0;
  
  // Filter properties
  searchTerm = '';
  selectedGrade = '';
  selectedAcademicYear = '';
  selectedSubjectType = '';
  selectedDifficulty = '';
  isActiveFilter: boolean | null = null;
  isCoreFilter: boolean | null = null;
  isElectiveFilter: boolean | null = null;

  // Dropdown data
  grades: any[] = [];
  academicYears: any[] = [];
  subjectTypes = Object.values(SubjectType);
  difficultyLevels = Object.values(DifficultyLevel);

  ngOnInit() {
    this.loadDropdownData();
    this.loadSubjects();
  }

  loadDropdownData() {
    // Load grades
    this.gradeService.getGrades({ pageSize: 1000 }).subscribe({
      next: (response) => {
        this.grades = response.data;
      },
      error: (error) => {
        console.error('Error loading grades:', error);
      }
    });

    // Load academic years
    this.academicYearService.getAcademicYears({ pageSize: 1000 }).subscribe({
      next: (response) => {
        this.academicYears = response.data;
      },
      error: (error) => {
        console.error('Error loading academic years:', error);
      }
    });
  }

  loadSubjects() {
    this.loading = true;
    const filter: SubjectFilter = {
      page: this.pageIndex + 1,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm || undefined,
      gradeId: this.selectedGrade || undefined,
      academicYearId: this.selectedAcademicYear || undefined,
      subjectType: this.selectedSubjectType as SubjectType || undefined,
      difficulty: this.selectedDifficulty as DifficultyLevel || undefined,
      isActive: this.isActiveFilter ?? undefined,
      isCore: this.isCoreFilter ?? undefined,
      isElective: this.isElectiveFilter ?? undefined
    };

    this.subjectService.getSubjects(filter).subscribe({
      next: (response) => {
        this.subjects = response.data;
        this.totalCount = response.totalCount;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading subjects:', error);
        this.snackBar.open('Error loading subjects', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadSubjects();
  }

  onSearch() {
    this.pageIndex = 0;
    this.loadSubjects();
  }

  onFilterChange() {
    this.pageIndex = 0;
    this.loadSubjects();
  }

  clearFilters() {
    this.searchTerm = '';
    this.selectedGrade = '';
    this.selectedAcademicYear = '';
    this.selectedSubjectType = '';
    this.selectedDifficulty = '';
    this.isActiveFilter = null;
    this.isCoreFilter = null;
    this.isElectiveFilter = null;
    this.pageIndex = 0;
    this.loadSubjects();
  }

  openCreateDialog() {
    // TODO: Implement SubjectCreateEditDialogComponent
    this.snackBar.open('Create dialog not yet implemented', 'Close', { duration: 3000 });
  }

  openEditDialog(subject: Subject) {
    // TODO: Implement SubjectCreateEditDialogComponent
    this.snackBar.open('Edit dialog not yet implemented', 'Close', { duration: 3000 });
  }

  toggleSubjectStatus(subject: Subject) {
    const action = subject.isActive ? 'deactivate' : 'activate';
    const actionText = subject.isActive ? 'deactivated' : 'activated';
    
    const operation = subject.isActive 
      ? this.subjectService.deactivateSubject(subject.id)
      : this.subjectService.activateSubject(subject.id);

    operation.subscribe({
      next: () => {
        subject.isActive = !subject.isActive;
        this.snackBar.open(`Subject ${actionText} successfully`, 'Close', { duration: 3000 });
      },
      error: (error) => {
        console.error(`Error ${action} subject:`, error);
        this.snackBar.open(`Error ${action} subject`, 'Close', { duration: 3000 });
      }
    });
  }

  deleteSubject(subject: Subject) {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Subject',
        message: `Are you sure you want to delete "${subject.name}"? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.subjectService.deleteSubject(subject.id).subscribe({
          next: () => {
            this.loadSubjects();
            this.snackBar.open('Subject deleted successfully', 'Close', { duration: 3000 });
          },
          error: (error) => {
            console.error('Error deleting subject:', error);
            this.snackBar.open('Error deleting subject', 'Close', { duration: 3000 });
          }
        });
      }
    });
  }

  getSubjectTypeColor(type: SubjectType): string {
    switch (type) {
      case SubjectType.Core: return 'primary';
      case SubjectType.Elective: return 'accent';
      case SubjectType.Optional: return 'warn';
      case SubjectType.Practical: return 'primary';
      case SubjectType.Lab: return 'accent';
      default: return 'primary';
    }
  }

  getDifficultyColor(difficulty: DifficultyLevel): string {
    switch (difficulty) {
      case DifficultyLevel.Beginner: return 'primary';
      case DifficultyLevel.Intermediate: return 'accent';
      case DifficultyLevel.Advanced: return 'warn';
      case DifficultyLevel.Expert: return 'warn';
      default: return 'primary';
    }
  }
}
