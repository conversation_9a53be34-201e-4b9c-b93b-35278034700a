<div class="alumni-portal-container">
  <mat-toolbar color="primary" class="portal-header">
    <button mat-icon-button (click)="sidenav.toggle()">
      <mat-icon>menu</mat-icon>
    </button>
    <span>Alumni Portal</span>
    <span class="spacer"></span>
    <button mat-icon-button [matMenuTriggerFor]="userMenu">
      <mat-icon>account_circle</mat-icon>
    </button>
    <mat-menu #userMenu="matMenu">
      <button mat-menu-item routerLink="/alumni-portal/profile">
        <mat-icon>person</mat-icon>
        <span>Profile</span>
      </button>
      <button mat-menu-item (click)="logout()">
        <mat-icon>exit_to_app</mat-icon>
        <span>Logout</span>
      </button>
    </mat-menu>
  </mat-toolbar>

  <mat-sidenav-container class="sidenav-container" #sidenav>
    <mat-sidenav mode="side" opened class="sidenav">
      <div *ngIf="alumni && !loading" class="alumni-info">
        <div class="alumni-avatar">
          <img *ngIf="alumni.profileImage?.filePath" [src]="alumni.profileImage.filePath" [alt]="alumni.name">
          <mat-icon *ngIf="!alumni.profileImage?.filePath">person</mat-icon>
        </div>
        <div class="alumni-details">
          <h3>{{ alumni.name }}</h3>
          <p>{{ alumni.profession }}</p>
          <p>Class of {{ alumni.graduationYear }}</p>
          <p *ngIf="alumni.currentCompany">{{ alumni.currentCompany }}</p>
        </div>
      </div>

      <mat-nav-list>
        <a mat-list-item 
           *ngFor="let item of navItems" 
           [routerLink]="item.route"
           routerLinkActive="active-link">
          <mat-icon matListItemIcon>{{ item.icon }}</mat-icon>
          <span matListItemTitle>{{ item.label }}</span>
        </a>
      </mat-nav-list>
    </mat-sidenav>

    <mat-sidenav-content class="content">
      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
      </div>

      <div *ngIf="error" class="error-container">
        <mat-card>
          <mat-card-content>
            <p>Unable to load alumni data. Please try again later.</p>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button color="primary" (click)="loadAlumniData()">Retry</button>
          </mat-card-actions>
        </mat-card>
      </div>

      <div *ngIf="!loading && !error" class="router-container">
        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>
