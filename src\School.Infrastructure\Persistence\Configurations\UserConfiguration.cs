using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.Property(t => t.Username)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(t => t.Email)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(t => t.PasswordHash)
            .IsRequired();

        builder.Property(t => t.FirstName)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(t => t.LastName)
            .HasMaxLength(50)
            .IsRequired();

        builder.HasIndex(t => t.Username)
            .IsUnique();

        builder.HasIndex(t => t.Email)
            .IsUnique();

        builder.HasQueryFilter(u => !u.IsDeleted);
    }
}
