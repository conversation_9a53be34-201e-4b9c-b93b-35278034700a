using School.Application.DTOs;
using School.Application.Common.Models;
using School.Domain.Entities;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace School.Application.Common.Interfaces;

public interface IClubRepository : IRepository<Club>
{
    Task<PagedList<Club>> GetClubsAsync(ClubFilterDto filter, CancellationToken cancellationToken = default);
    Task<Club> GetClubByIdWithDetailsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<PagedList<Club>> GetFeaturedClubsAsync(int page = 1, int pageSize = 10, CancellationToken cancellationToken = default);
    Task<List<string>> GetClubCategoriesAsync(CancellationToken cancellationToken = default);
}
