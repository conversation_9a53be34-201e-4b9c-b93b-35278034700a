// Mega Menu Styles
// This file contains reusable styles for the mega menu components

@use 'sass:color';
@use '../../../styles/_variables' as *;

// Menu Item Styles
@mixin menu-item {
  position: relative;
  display: flex;
  align-items: center;
  white-space: nowrap;
  margin: 0 5px; // Compact spacing between menu items
}

// Menu Trigger/Link Styles
@mixin menu-trigger {
  height: 60px;
  line-height: 60px;
  padding: 0 15px;
  font-weight: 500;
  letter-spacing: 0.5px;
  color: #333;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  font-size: 16px;
  transition: all 0.3s ease;
  z-index: 1000; // Ensure the trigger is above other elements

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background-color: $primary-color;
    transition: all 0.3s ease;
    transform: translateX(-50%);
  }

  &.active {
    color: $primary-color;
    font-weight: 600;
    background-color: rgba($primary-color, 0.05);

    &::after {
      width: 80%;
      height: 4px;
    }

    mat-icon {
      transform: rotate(180deg);
      color: $primary-color;
    }
  }

  &:hover {
    background-color: rgba($primary-color, 0.08);
    color: $primary-color;
    transform: translateY(-2px);
    transition: all 0.2s ease;

    &::after {
      width: 50%;
    }
  }

  mat-icon {
    transition: transform 0.3s ease;
    margin-left: 4px;
    font-size: 18px;
    height: 18px;
    width: 18px;
  }
}

// Mega Menu Panel Styles
@mixin mega-menu-panel {
  max-width: 1200px !important; // Maximum width for very large menus
  min-width: 280px; // Minimum width for single column
  width: auto; // Auto width to adjust to content
  margin-top: 8px;
  border-radius: 8px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden; // Contain the content
  position: fixed; // Fix position to prevent scrolling issues
  z-index: 1001; // Higher than the navbar

  .mat-mdc-menu-content {
    padding: 0 !important;
    overflow: hidden; // Prevent scrollbars
  }
}

// Smaller Panel for Portals Menu
@mixin mega-menu-panel-small {
  @include mega-menu-panel;
  min-width: 400px; // Minimum width for portals menu
  max-width: 500px; // Maximum width for portals menu
  // No need to redefine position and z-index as they're inherited from mega-menu-panel
}

// Mega Menu Content Styles
@mixin mega-menu-content {
  display: flex;
  padding: 24px 24px;
  background-color: white;
  width: 100%; // Full width of the panel
  box-sizing: border-box;
  flex-wrap: wrap; // Allow wrapping for responsive behavior
  justify-content: flex-start; // Start from the left
  gap: 10px; // Add gap between columns

  // Individual Menu Column
  .menu-column {
    flex: 1 1 0;
    padding: 0 15px;
    box-sizing: border-box;
    min-width: 200px; // Minimum width for readability
    max-width: 300px; // Maximum width to prevent excessive stretching

    // Responsive behavior for different column counts
    // For 1 column layout
    &:only-child {
      min-width: 250px;
      max-width: 100%;
      padding: 0;
    }

    // For 2 column layout
    &:first-child:nth-last-child(2),
    &:last-child:nth-of-type(2) {
      min-width: 180px;
      max-width: 50%;
    }

    // For 3 column layout
    &:first-child:nth-last-child(3),
    &:nth-child(2):nth-last-child(2),
    &:last-child:nth-of-type(3) {
      min-width: 180px;
      max-width: 33.33%;
    }

    // For 4+ column layout
    &:first-child:nth-last-child(n+4),
    &:nth-child(n+2):nth-last-child(n+3) {
      min-width: 160px;
      max-width: 25%;
    }

    // First column
    &:first-child {
      padding-left: 0;
    }

    // Last column
    &:last-child {
      padding-right: 0;
    }

    h3 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 15px;
      color: $primary-color;
      position: relative;
      padding-bottom: 8px;
      letter-spacing: 0.3px;
      text-transform: uppercase;
      white-space: normal;
      word-wrap: break-word;
      line-height: 1.3;

      // Adjust font size based on column count
      .menu-column:only-child & {
        font-size: 18px;
      }

      // Smaller font for many columns
      .menu-column:first-child:nth-last-child(n+4) &,
      .menu-column:nth-child(n+2):nth-last-child(n+3) & {
        font-size: 14px;
        margin-bottom: 12px;
        padding-bottom: 6px;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 40px;
        height: 2px;
        background-color: $primary-color;
        opacity: 0.8;
      }
    }

    a.mat-mdc-menu-item {
      min-height: 40px;
      height: auto;
      line-height: 1.4;
      padding: 10px 12px;
      border-radius: 4px;
      transition: all 0.2s ease;
      margin-bottom: 8px;
      font-size: 14px;
      white-space: normal; // Allow text to wrap
      overflow: visible;
      width: 100%;
      box-sizing: border-box;
      display: block;
      word-wrap: break-word; // Break long words if needed
      hyphens: auto; // Add hyphens for better text wrapping

      // Adjust padding based on column count
      .menu-column:only-child & {
        padding: 12px 16px; // More padding for single column
      }

      // Smaller padding for many columns
      .menu-column:first-child:nth-last-child(n+4) &,
      .menu-column:nth-child(n+2):nth-last-child(n+3) & {
        padding: 8px 10px;
        font-size: 13px;
      }

      &.active {
        background-color: rgba($primary-color, 0.1);
        font-weight: 500;
        color: $primary-color;
      }

      &:hover {
        background-color: rgba($primary-color, 0.05);
        color: $primary-color;
      }

      mat-icon {
        margin-right: 8px;
        color: $primary-color;
        vertical-align: middle;
        font-size: 18px;
        height: 18px;
        width: 18px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

// Main Mega Menu Container
@mixin mega-menu-container {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  margin: 0 16px;
  min-width: 0;
  width: 100%;
  position: relative; // Ensure proper positioning context for mega menus
  z-index: 999; // Lower than the mega menu panels but higher than other elements
}
