using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Domain.Entities;
using School.Domain.Enums;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace School.Infrastructure.Persistence;

public static class ApplicationDbContextSeed
{
    public static async Task SeedDefaultDataAsync(ApplicationDbContext context, ILogger logger)
    {
        try
        {
            // Seed default notices if none exist
            if (!await context.Notices.AnyAsync())
            {
                logger.LogInformation("Seeding default notices");

                var adminUser = await context.Users.FirstAsync(u => u.UserName == "admin");

                var notices = new List<Notice>
                {
                    new Notice
                    {
                        Title = "Admissions Open for 2023-24 Academic Year",
                        Content = "Applications are now being accepted for the upcoming academic year. Visit our admissions page for more details.",
                        StartDate = DateTime.UtcNow.AddDays(-10),
                        EndDate = DateTime.UtcNow.AddDays(30),
                        Priority = NoticePriority.High,
                        Category = "Admissions",
                        IsActive = true,
                        CreatedById = Guid.Parse(adminUser.Id),
                        CreatedAt = DateTime.UtcNow,
                        Translations = new List<NoticeTranslation>
                        {
                            new NoticeTranslation
                            {
                                LanguageCode = "bn",
                                Title = "২০২৩-২৪ শিক্ষাবর্ষের জন্য ভর্তি খোলা আছে",
                                Content = "আগামী শিক্ষাবর্ষের জন্য আবেদন গ্রহণ করা হচ্ছে। আরও বিবরণের জন্য আমাদের ভর্তি পৃষ্ঠা দেখুন।",
                                CreatedAt = DateTime.UtcNow
                            }
                        }
                    },
                    new Notice
                    {
                        Title = "Final Exam Schedule Released",
                        Content = "The schedule for final examinations has been published. Please check your student portal for details.",
                        StartDate = DateTime.UtcNow.AddDays(-5),
                        EndDate = DateTime.UtcNow.AddDays(15),
                        Priority = NoticePriority.Medium,
                        Category = "Academics",
                        IsActive = true,
                        CreatedById = Guid.Parse(adminUser.Id),
                        CreatedAt = DateTime.UtcNow,
                        Translations = new List<NoticeTranslation>
                        {
                            new NoticeTranslation
                            {
                                LanguageCode = "bn",
                                Title = "চূড়ান্ত পরীক্ষার সময়সূচী প্রকাশিত হয়েছে",
                                Content = "চূড়ান্ত পরীক্ষার সময়সূচী প্রকাশিত হয়েছে। বিস্তারিত জানতে আপনার ছাত্র পোর্টাল দেখুন।",
                                CreatedAt = DateTime.UtcNow
                            }
                        }
                    },
                    new Notice
                    {
                        Title = "Annual Day Celebration on December 15",
                        Content = "Join us for our annual day celebration featuring performances by students from all grades.",
                        StartDate = DateTime.UtcNow.AddDays(-2),
                        EndDate = DateTime.UtcNow.AddDays(20),
                        Priority = NoticePriority.Medium,
                        Category = "Events",
                        IsActive = true,
                        CreatedById = Guid.Parse(adminUser.Id),
                        CreatedAt = DateTime.UtcNow,
                        Translations = new List<NoticeTranslation>
                        {
                            new NoticeTranslation
                            {
                                LanguageCode = "bn",
                                Title = "ডিসেম্বর ১৫ তারিখে বার্ষিক দিবস উদযাপন",
                                Content = "সকল শ্রেণীর শিক্ষার্থীদের পরিবেশনা সহ আমাদের বার্ষিক দিবস উদযাপনে যোগ দিন।",
                                CreatedAt = DateTime.UtcNow
                            }
                        }
                    },
                    new Notice
                    {
                        Title = "Inter-School Sports Competition Next Week",
                        Content = "Our school will be participating in the inter-school sports competition starting next Monday.",
                        StartDate = DateTime.UtcNow,
                        EndDate = DateTime.UtcNow.AddDays(10),
                        Priority = NoticePriority.Low,
                        Category = "Sports",
                        IsActive = true,
                        CreatedById = Guid.Parse(adminUser.Id),
                        CreatedAt = DateTime.UtcNow,
                        Translations = new List<NoticeTranslation>
                        {
                            new NoticeTranslation
                            {
                                LanguageCode = "bn",
                                Title = "আগামী সপ্তাহে আন্তঃস্কুল ক্রীড়া প্রতিযোগিতা",
                                Content = "আগামী সোমবার থেকে আমাদের স্কুল আন্তঃস্কুল ক্রীড়া প্রতিযোগিতায় অংশগ্রহণ করবে।",
                                CreatedAt = DateTime.UtcNow
                            }
                        }
                    }
                };

                context.Notices.AddRange(notices);
                await context.SaveChangesAsync();
            }

            // Seed default faculty members if none exist
            if (!await context.Faculty.AnyAsync())
            {
                logger.LogInformation("Seeding default faculty members");

                var faculty = new List<Faculty>
                {
                    new Faculty
                    {
                        Name = "Dr. Sarah Johnson",
                        Title = "Principal",
                        Email = "<EMAIL>",
                        Phone = "(*************",
                        Office = "Admin Building, Room 101",
                        Biography = "Dr. Sarah Johnson has over 20 years of experience in education, with a focus on curriculum development and educational leadership. She has implemented several innovative programs that have significantly improved student outcomes.",
                        ShortBio = "Educational leader with 20+ years of experience in curriculum development and school administration.",
                        JoinedYear = 2015,
                        IsFeatured = true,
                        IsActive = true,
                        DisplayOrder = 1,
                        Department = "administration",
                        Website = "https://www.sarahjohnson-education.com",
                        LinkedIn = "https://linkedin.com/in/sarahjohnson",
                        CreatedAt = DateTime.UtcNow,
                        Translations = new List<FacultyTranslation>
                        {
                            new FacultyTranslation
                            {
                                LanguageCode = "bn",
                                Name = "ড. সারা জনসন",
                                Title = "অধ্যক্ষ",
                                Biography = "ড. সারা জনসন শিক্ষা ক্ষেত্রে ২০ বছরেরও বেশি অভিজ্ঞতা রয়েছে, পাঠ্যক্রম উন্নয়ন এবং শিক্ষা নেতৃত্বের উপর ফোকাস সহ। তিনি বেশ কয়েকটি উদ্ভাবনী প্রোগ্রাম বাস্তবায়ন করেছেন যা শিক্ষার্থীদের ফলাফল উল্লেখযোগ্যভাবে উন্নত করেছে।",
                                ShortBio = "পাঠ্যক্রম উন্নয়ন এবং স্কুল প্রশাসনে ২০+ বছরের অভিজ্ঞতা সহ শিক্ষা নেতা।",
                                CreatedAt = DateTime.UtcNow
                            }
                        },
                        Education = new List<FacultyEducation>
                        {
                            new FacultyEducation
                            {
                                Degree = "Ph.D. in Educational Leadership",
                                Institution = "Harvard University",
                                Year = 2005,
                                DisplayOrder = 1,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultyEducation
                            {
                                Degree = "M.Ed. in Curriculum Development",
                                Institution = "Columbia University",
                                Year = 2000,
                                DisplayOrder = 2,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultyEducation
                            {
                                Degree = "B.A. in Education",
                                Institution = "University of Michigan",
                                Year = 1998,
                                DisplayOrder = 3,
                                CreatedAt = DateTime.UtcNow
                            }
                        },
                        Specializations = new List<FacultySpecialization>
                        {
                            new FacultySpecialization
                            {
                                Name = "Educational Leadership",
                                DisplayOrder = 1,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultySpecialization
                            {
                                Name = "Curriculum Development",
                                DisplayOrder = 2,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultySpecialization
                            {
                                Name = "School Administration",
                                DisplayOrder = 3,
                                CreatedAt = DateTime.UtcNow
                            }
                        },
                        Courses = new List<FacultyCourse>
                        {
                            new FacultyCourse
                            {
                                Name = "Educational Leadership Seminar",
                                DisplayOrder = 1,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultyCourse
                            {
                                Name = "Principles of School Administration",
                                DisplayOrder = 2,
                                CreatedAt = DateTime.UtcNow
                            }
                        },
                        Publications = new List<FacultyPublication>
                        {
                            new FacultyPublication
                            {
                                Title = "Transforming Education Through Innovative Leadership",
                                Journal = "Journal of Educational Leadership",
                                Year = 2022,
                                DisplayOrder = 1,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultyPublication
                            {
                                Title = "Curriculum Development for the 21st Century",
                                Journal = "Educational Review",
                                Year = 2020,
                                DisplayOrder = 2,
                                CreatedAt = DateTime.UtcNow
                            }
                        },
                        Awards = new List<FacultyAward>
                        {
                            new FacultyAward
                            {
                                Name = "National Principal of the Year",
                                Year = 2021,
                                Organization = "National Association of School Principals",
                                DisplayOrder = 1,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultyAward
                            {
                                Name = "Educational Leadership Excellence Award",
                                Year = 2019,
                                Organization = "Education Leadership Foundation",
                                DisplayOrder = 2,
                                CreatedAt = DateTime.UtcNow
                            }
                        }
                    },
                    new Faculty
                    {
                        Name = "Prof. Michael Chen",
                        Title = "Head of Science Department",
                        Email = "<EMAIL>",
                        Phone = "(*************",
                        Office = "Science Building, Room 203",
                        Biography = "Professor Michael Chen is an accomplished physicist with a passion for science education. He has developed innovative teaching methods that make complex scientific concepts accessible to students of all levels.",
                        ShortBio = "Physicist and educator dedicated to making science accessible and engaging for all students.",
                        JoinedYear = 2016,
                        IsFeatured = true,
                        IsActive = true,
                        DisplayOrder = 2,
                        Department = "science",
                        LinkedIn = "https://linkedin.com/in/michaelchen",
                        ResearchGate = "https://researchgate.net/profile/Michael_Chen",
                        CreatedAt = DateTime.UtcNow,
                        Education = new List<FacultyEducation>
                        {
                            new FacultyEducation
                            {
                                Degree = "Ph.D. in Physics",
                                Institution = "MIT",
                                Year = 2010,
                                DisplayOrder = 1,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultyEducation
                            {
                                Degree = "M.S. in Physics",
                                Institution = "Stanford University",
                                Year = 2007,
                                DisplayOrder = 2,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultyEducation
                            {
                                Degree = "B.S. in Physics",
                                Institution = "California Institute of Technology",
                                Year = 2005,
                                DisplayOrder = 3,
                                CreatedAt = DateTime.UtcNow
                            }
                        },
                        Specializations = new List<FacultySpecialization>
                        {
                            new FacultySpecialization
                            {
                                Name = "Physics Education",
                                DisplayOrder = 1,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultySpecialization
                            {
                                Name = "Quantum Mechanics",
                                DisplayOrder = 2,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultySpecialization
                            {
                                Name = "STEM Curriculum Development",
                                DisplayOrder = 3,
                                CreatedAt = DateTime.UtcNow
                            }
                        },
                        Courses = new List<FacultyCourse>
                        {
                            new FacultyCourse
                            {
                                Name = "Advanced Physics",
                                DisplayOrder = 1,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultyCourse
                            {
                                Name = "Introduction to Quantum Mechanics",
                                DisplayOrder = 2,
                                CreatedAt = DateTime.UtcNow
                            },
                            new FacultyCourse
                            {
                                Name = "Science Research Methods",
                                DisplayOrder = 3,
                                CreatedAt = DateTime.UtcNow
                            }
                        }
                    }
                };

                context.Faculty.AddRange(faculty);
                await context.SaveChangesAsync();
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while seeding the database");
            throw;
        }
    }

    private static string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
        return Convert.ToBase64String(hashedBytes);
    }
}
