using School.Application.DTOs.Common;
using School.Domain.Enums;

namespace School.Application.DTOs;

public class StudentDto
{
    // Basic Information
    public Guid Id { get; set; }
    public string StudentId { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName => $"{FirstName} {LastName}";
    public DateTime DateOfBirth { get; set; }
    public GenderType Gender { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string EmergencyContactName { get; set; } = string.Empty;
    public string EmergencyContactPhone { get; set; } = string.Empty;
    public string EmergencyContactRelation { get; set; } = string.Empty;

    // Medical Information
    public string BloodGroup { get; set; } = string.Empty;
    public string MedicalConditions { get; set; } = string.Empty;
    public string Allergies { get; set; } = string.Empty;

    // Academic Information
    public int CurrentGrade { get; set; }
    public string Section { get; set; } = string.Empty;
    public TeachingMedium Medium { get; set; }
    public ShiftType Shift { get; set; }
    public int AcademicYear { get; set; }
    public int RollNumber { get; set; }
    public int AdmissionYear { get; set; }
    public DateTime? GraduationDate { get; set; }

    // Status Information
    public bool IsActive { get; set; }
    public bool IsHosteler { get; set; }
    public string UserId { get; set; } = string.Empty;

    // Class Teacher Information
    public Guid? ClassTeacherId { get; set; }
    public FacultyDto? ClassTeacher { get; set; }

    // Media
    public Guid? ProfileImageId { get; set; }
    public MediaItemDto? ProfileImage { get; set; }

    // Timestamps
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Relationships
    public List<StudentParentDto> Parents { get; set; } = new List<StudentParentDto>();
}

public class StudentDetailDto : StudentDto
{
    public List<StudentAttendanceDto> RecentAttendance { get; set; } = new List<StudentAttendanceDto>();
    public List<StudentFeeDto> RecentFees { get; set; } = new List<StudentFeeDto>();
    public List<StudentResultDto> RecentResults { get; set; } = new List<StudentResultDto>();
    public List<StudentLeaveDto> RecentLeaves { get; set; } = new List<StudentLeaveDto>();
    public List<StudentAcademicHistoryDto> AcademicHistory { get; set; } = new List<StudentAcademicHistoryDto>();
    public decimal? CurrentYearGPA { get; set; }
}

public class StudentParentDto
{
    public Guid Id { get; set; }
    public Guid StudentId { get; set; }
    public Guid ParentId { get; set; }
    public ParentRelationType RelationType { get; set; }
    public bool IsPrimaryContact { get; set; }
    public ParentDto Parent { get; set; } = null!;
}

public class StudentAttendanceDto
{
    public Guid Id { get; set; }
    public Guid StudentId { get; set; }
    public DateTime Date { get; set; }
    public AttendanceStatus Status { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public string RecordedBy { get; set; } = string.Empty;
    public int AcademicYear { get; set; }
    public int Grade { get; set; }
    public string Section { get; set; } = string.Empty;
    public string Period { get; set; } = string.Empty;
    public string SubjectCode { get; set; } = string.Empty;
    public bool IsLeaveApproved { get; set; }
    public Guid? LeaveId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class StudentFeeDto
{
    public Guid Id { get; set; }
    public Guid StudentId { get; set; }
    public FeeType Type { get; set; }
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal DueAmount { get; set; }
    public DateTime DueDate { get; set; }
    public DateTime? PaidDate { get; set; }
    public PaymentStatus Status { get; set; }
    public string TransactionId { get; set; } = string.Empty;
    public string PaymentMethod { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class StudentResultDto
{
    public Guid Id { get; set; }
    public Guid StudentId { get; set; }
    public ExamType ExamType { get; set; }
    public string SubjectCode { get; set; } = string.Empty;
    public string SubjectName { get; set; } = string.Empty;
    public decimal MarksObtained { get; set; }
    public decimal TotalMarks { get; set; }
    public decimal Percentage { get; set; }
    public string LetterGrade { get; set; } = string.Empty;
    public decimal GradePoint { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public int AcademicYear { get; set; }
    public bool IsOptional { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public int Marks { get; set; }
    public string MaxMarks { get; set; }
    public string Grade { get; set; } = string.Empty;
    public decimal? Credits { get; set; }
}

public class CreateStudentDto
{
    // Basic Information
    public string StudentId { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public DateTime DateOfBirth { get; set; }
    public GenderType Gender { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string EmergencyContactName { get; set; } = string.Empty;
    public string EmergencyContactPhone { get; set; } = string.Empty;
    public string EmergencyContactRelation { get; set; } = string.Empty;

    // Medical Information
    public string BloodGroup { get; set; } = string.Empty;
    public string MedicalConditions { get; set; } = string.Empty;
    public string Allergies { get; set; } = string.Empty;

    // Academic Information
    public int CurrentGrade { get; set; }
    public string Section { get; set; } = string.Empty;
    public TeachingMedium Medium { get; set; } = TeachingMedium.Bengali;
    public ShiftType Shift { get; set; } = ShiftType.Morning;
    public int AcademicYear { get; set; }
    public int RollNumber { get; set; }
    public int AdmissionYear { get; set; }
    public DateTime? GraduationDate { get; set; }

    // Status Information
    public bool IsActive { get; set; } = true;
    public bool IsHosteler { get; set; } = false;
    public string UserId { get; set; } = string.Empty;

    // Class Teacher Information
    public Guid? ClassTeacherId { get; set; }

    // Media
    public Guid? ProfileImageId { get; set; }

    // Relationships
    public List<CreateStudentParentDto>? Parents { get; set; }
}

public class UpdateStudentDto
{
    // Basic Information
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public DateTime DateOfBirth { get; set; }
    public GenderType Gender { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string EmergencyContactName { get; set; } = string.Empty;
    public string EmergencyContactPhone { get; set; } = string.Empty;
    public string EmergencyContactRelation { get; set; } = string.Empty;

    // Medical Information
    public string BloodGroup { get; set; } = string.Empty;
    public string MedicalConditions { get; set; } = string.Empty;
    public string Allergies { get; set; } = string.Empty;

    // Academic Information
    public int CurrentGrade { get; set; }
    public string Section { get; set; } = string.Empty;
    public TeachingMedium Medium { get; set; }
    public ShiftType Shift { get; set; }
    public int AcademicYear { get; set; }
    public int RollNumber { get; set; }
    public int AdmissionYear { get; set; }
    public DateTime? GraduationDate { get; set; }

    // Status Information
    public bool IsActive { get; set; }
    public bool IsHosteler { get; set; }

    // Class Teacher Information
    public Guid? ClassTeacherId { get; set; }

    // Media
    public Guid? ProfileImageId { get; set; }
}

public class CreateStudentParentDto
{
    public Guid? ParentId { get; set; }
    public ParentRelationType RelationType { get; set; }
    public bool IsPrimaryContact { get; set; }
    public CreateParentDto? Parent { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public GenderType Gender { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string AlternatePhone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string Occupation { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public Guid? ProfileImageId { get; set; }
}

public class CreateStudentAttendanceDto
{
    public Guid StudentId { get; set; }
    public DateTime Date { get; set; }
    public AttendanceStatus Status { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public string RecordedBy { get; set; } = string.Empty;
    public string Period { get; set; } = string.Empty;
    public string SubjectCode { get; set; } = string.Empty;
}

public class UpdateStudentAttendanceDto
{
    public AttendanceStatus Status { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public string RecordedBy { get; set; } = string.Empty;
}

public class CreateStudentFeeDto
{
    public Guid StudentId { get; set; }
    public FeeType Type { get; set; }
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal DueAmount { get; set; }
    public DateTime DueDate { get; set; }
    public DateTime? PaidDate { get; set; }
    public PaymentStatus Status { get; set; }
    public string TransactionId { get; set; } = string.Empty;
    public string PaymentMethod { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

public class UpdateStudentFeeDto
{
    public decimal PaidAmount { get; set; }
    public decimal DueAmount { get; set; }
    public DateTime? PaidDate { get; set; }
    public PaymentStatus Status { get; set; }
    public string TransactionId { get; set; } = string.Empty;
    public string PaymentMethod { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

public class CreateStudentResultDto
{
    public Guid StudentId { get; set; }
    public string ExamName { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public decimal MarksObtained { get; set; }
    public decimal TotalMarks { get; set; }
    public string Grade { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public int AcademicYear { get; set; }
    public string Semester { get; set; } = string.Empty;
    public ExamType ExamType { get; set; }
    public string SubjectCode { get; set; } = string.Empty;
    public int Marks { get; set; }
    public decimal MaxMarks { get; set; }
    public decimal? Credits { get; set; }
    public string SubjectName { get; set; } = string.Empty;
}

public class UpdateStudentResultDto
{
    public decimal MarksObtained { get; set; }
    public decimal TotalMarks { get; set; }
    public string LetterGrade { get; set; } = string.Empty;
    public decimal GradePoint { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public int Marks { get; set; }
    public decimal MaxMarks { get; set; }
    public string Grade { get; set; } = string.Empty;
    public decimal? Credits { get; set; }
}

public class StudentLeaveDto
{
    public Guid Id { get; set; }
    public Guid StudentId { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public LeaveType Type { get; set; }
    public string Reason { get; set; } = string.Empty;
    public LeaveStatus Status { get; set; }
    public string ApprovedBy { get; set; } = string.Empty;
    public DateTime? ApprovedAt { get; set; }
    public string Comments { get; set; } = string.Empty;
    public string AttachmentPath { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string ApprovalRemarks { get; set; } = string.Empty;
}

public class CreateStudentLeaveDto
{
    public Guid StudentId { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public LeaveType Type { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string AttachmentPath { get; set; } = string.Empty;
}

public class UpdateStudentLeaveDto
{
    public LeaveStatus Status { get; set; }
    public string ApprovedBy { get; set; } = string.Empty;
    public string Comments { get; set; } = string.Empty;
    public string ApprovalRemarks { get; set; } = string.Empty;
}

public class StudentAcademicHistoryDto
{
    public Guid Id { get; set; }
    public Guid StudentId { get; set; }
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }
    public int Grade { get; set; }
    public string Section { get; set; } = string.Empty;
    public TeachingMedium Medium { get; set; }
    public ShiftType Shift { get; set; }
    public int RollNumber { get; set; }
    public string StudentIdForYear { get; set; } = string.Empty;
    public decimal? FinalGPA { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public bool IsPromoted { get; set; }
    public Guid? ClassTeacherId { get; set; }
    public FacultyDto? ClassTeacher { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public string AcademicYearName { get; set; } = string.Empty;
    public string TermName { get; set; } = string.Empty;
}

public class CreateStudentAcademicHistoryDto
{
    public Guid StudentId { get; set; }
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }
    public int Grade { get; set; }
    public string Section { get; set; } = string.Empty;
    public TeachingMedium Medium { get; set; }
    public ShiftType Shift { get; set; }
    public int RollNumber { get; set; }
    public string StudentIdForYear { get; set; } = string.Empty;
    public decimal? FinalGPA { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public bool IsPromoted { get; set; }
    public Guid? ClassTeacherId { get; set; }
}

public class UpdateStudentAcademicHistoryDto
{
    public decimal? FinalGPA { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public bool IsPromoted { get; set; }
    public int Grade { get; set; }
    public string Section { get; set; } = string.Empty;
    public int RollNumber { get; set; }
    public Guid? ClassTeacherId { get; set; }
}

/// <summary>
/// Filter DTO for student queries
/// </summary>
public class StudentFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filter by student ID
    /// </summary>
    public string? StudentId { get; set; }

    /// <summary>
    /// Filter by student name (first or last)
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Filter by grade/class
    /// </summary>
    public int? Grade { get; set; }

    /// <summary>
    /// Filter by section
    /// </summary>
    public string? Section { get; set; }

    /// <summary>
    /// Filter by teaching medium
    /// </summary>
    public TeachingMedium? Medium { get; set; }

    /// <summary>
    /// Filter by shift
    /// </summary>
    public ShiftType? Shift { get; set; }

    /// <summary>
    /// Filter by academic year
    /// </summary>
    public int? AcademicYear { get; set; }

    /// <summary>
    /// Filter by roll number
    /// </summary>
    public int? RollNumber { get; set; }

    /// <summary>
    /// Filter by admission year
    /// </summary>
    public int? AdmissionYear { get; set; }

    /// <summary>
    /// Filter by class teacher
    /// </summary>
    public Guid? ClassTeacherId { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter by hosteler status
    /// </summary>
    public bool? IsHosteler { get; set; }
}
