import { Component, OnInit, inject, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { SectionService, Section, CreateSectionDto, UpdateSectionDto } from '../../../core/services/section.service';
import { GradeService } from '../../../core/services/grade.service';
import { AcademicYearService } from '../../../core/services/academic-year.service';

export interface DialogData {
  mode: 'create' | 'edit';
  section?: Section;
}

@Component({
  selector: 'app-section-create-edit-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatSlideToggleModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatTabsModule,
    TranslateModule
  ],
  templateUrl: './section-create-edit-dialog.component.html',
  styleUrls: ['./section-create-edit-dialog.component.scss']
})
export class SectionCreateEditDialogComponent implements OnInit {
  private fb = inject(FormBuilder);
  private sectionService = inject(SectionService);
  private gradeService = inject(GradeService);
  private academicYearService = inject(AcademicYearService);
  private dialogRef = inject(MatDialogRef<SectionCreateEditDialogComponent>);

  sectionForm: FormGroup;
  loading = false;
  grades: any[] = [];
  academicYears: any[] = [];
  sectionTypes: { value: string; label: string }[] = [];
  mediums: { value: string; label: string }[] = [];
  shifts: { value: string; label: string }[] = [];

  constructor(@Inject(MAT_DIALOG_DATA) public data: DialogData) {
    this.sectionForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      code: ['', [Validators.required, Validators.maxLength(20)]],
      gradeId: ['', Validators.required],
      type: ['Regular', Validators.required],
      medium: ['Bengali', Validators.required],
      shift: ['Morning', Validators.required],
      capacity: [30, [Validators.required, Validators.min(1), Validators.max(200)]],
      isActive: [true],
      displayOrder: [1, [Validators.required, Validators.min(1)]],
      academicYearId: ['', Validators.required],
      classroom: ['', Validators.maxLength(100)],
      roomNumber: ['', Validators.maxLength(50)],
      description: ['', Validators.maxLength(500)],
      requirements: ['', Validators.maxLength(1000)],
      remarks: ['', Validators.maxLength(500)]
    });
  }

  ngOnInit() {
    this.loadGrades();
    this.loadAcademicYears();
    this.loadSectionTypes();
    this.loadMediums();
    this.loadShifts();

    if (this.data.mode === 'edit' && this.data.section) {
      this.populateForm(this.data.section);
    }
  }

  loadGrades() {
    this.gradeService.getActiveGrades('').subscribe({
      next: (grades) => {
        this.grades = grades;
      },
      error: (error) => {
        console.error('Error loading grades:', error);
      }
    });
  }

  loadAcademicYears() {
    this.academicYearService.getActiveAcademicYears().subscribe({
      next: (years) => {
        this.academicYears = years;
      },
      error: (error) => {
        console.error('Error loading academic years:', error);
      }
    });
  }

  loadSectionTypes() {
    this.sectionService.getSectionTypes().subscribe({
      next: (types) => {
        this.sectionTypes = types;
      },
      error: (error) => {
        console.error('Error loading section types:', error);
        // Fallback to default types
        this.sectionTypes = [
          { value: 'Regular', label: 'Regular' },
          { value: 'Science', label: 'Science' },
          { value: 'Commerce', label: 'Commerce' },
          { value: 'Arts', label: 'Arts' },
          { value: 'Vocational', label: 'Vocational' }
        ];
      }
    });
  }

  loadMediums() {
    this.sectionService.getMediums().subscribe({
      next: (mediums) => {
        this.mediums = mediums;
      },
      error: (error) => {
        console.error('Error loading mediums:', error);
        // Fallback to default mediums
        this.mediums = [
          { value: 'Bengali', label: 'Bengali' },
          { value: 'English', label: 'English' },
          { value: 'Hindi', label: 'Hindi' },
          { value: 'Urdu', label: 'Urdu' }
        ];
      }
    });
  }

  loadShifts() {
    this.sectionService.getShifts().subscribe({
      next: (shifts) => {
        this.shifts = shifts;
      },
      error: (error) => {
        console.error('Error loading shifts:', error);
        // Fallback to default shifts
        this.shifts = [
          { value: 'Morning', label: 'Morning' },
          { value: 'Day', label: 'Day' },
          { value: 'Evening', label: 'Evening' }
        ];
      }
    });
  }

  populateForm(section: Section) {
    this.sectionForm.patchValue({
      name: section.name,
      code: section.code,
      gradeId: section.gradeId,
      type: section.type,
      medium: section.medium,
      shift: section.shift,
      capacity: section.capacity,
      isActive: section.isActive,
      displayOrder: section.displayOrder,
      academicYearId: section.academicYearId,
      classroom: section.classroom,
      roomNumber: section.roomNumber,
      description: section.description,
      requirements: section.requirements,
      remarks: section.remarks
    });
  }

  onSubmit() {
    if (this.sectionForm.valid) {
      this.loading = true;
      const formValue = this.sectionForm.value;

      if (this.data.mode === 'create') {
        const createDto: CreateSectionDto = formValue;
        this.sectionService.createSection(createDto).subscribe({
          next: () => {
            this.loading = false;
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('Error creating section:', error);
            this.loading = false;
          }
        });
      } else {
        const updateDto: UpdateSectionDto = {
          name: formValue.name,
          code: formValue.code,
          gradeId: formValue.gradeId,
          type: formValue.type,
          medium: formValue.medium,
          shift: formValue.shift,
          capacity: formValue.capacity,
          isActive: formValue.isActive,
          displayOrder: formValue.displayOrder,
          classroom: formValue.classroom,
          roomNumber: formValue.roomNumber,
          description: formValue.description,
          requirements: formValue.requirements,
          remarks: formValue.remarks
        };

        this.sectionService.updateSection(this.data.section!.id, updateDto).subscribe({
          next: () => {
            this.loading = false;
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('Error updating section:', error);
            this.loading = false;
          }
        });
      }
    }
  }

  onCancel() {
    this.dialogRef.close(false);
  }

  getTitle(): string {
    return this.data.mode === 'create' ? 'SECTIONS.CREATE_SECTION' : 'SECTIONS.EDIT_SECTION';
  }

  getSubmitButtonText(): string {
    return this.data.mode === 'create' ? 'COMMON.CREATE' : 'COMMON.UPDATE';
  }
}
