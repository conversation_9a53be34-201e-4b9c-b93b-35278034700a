<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Authentication Messages -->
  <data name="Auth.LoginSuccess" xml:space="preserve">
    <value>Login successful</value>
  </data>
  <data name="Auth.LoginFailed" xml:space="preserve">
    <value>Invalid credentials</value>
  </data>
  <data name="Auth.MfaRequired" xml:space="preserve">
    <value>MFA verification required</value>
  </data>
  <data name="Auth.TokenExpired" xml:space="preserve">
    <value>Token has expired</value>
  </data>
  <data name="Auth.Unauthorized" xml:space="preserve">
    <value>Unauthorized access</value>
  </data>
  <data name="Auth.RegistrationSuccess" xml:space="preserve">
    <value>User registered successfully</value>
  </data>
  <data name="Auth.RegistrationFailed" xml:space="preserve">
    <value>Registration failed</value>
  </data>
  
  <!-- Validation Messages -->
  <data name="Validation.Required" xml:space="preserve">
    <value>{0} is required</value>
  </data>
  <data name="Validation.EmailInvalid" xml:space="preserve">
    <value>Please enter a valid email address</value>
  </data>
  <data name="Validation.PasswordTooShort" xml:space="preserve">
    <value>Password must be at least {0} characters long</value>
  </data>
  <data name="Validation.PasswordComplexity" xml:space="preserve">
    <value>Password must contain uppercase, lowercase, number and special character</value>
  </data>
  
  <!-- Academic Year Messages -->
  <data name="AcademicYear.Created" xml:space="preserve">
    <value>Academic year created successfully</value>
  </data>
  <data name="AcademicYear.Updated" xml:space="preserve">
    <value>Academic year updated successfully</value>
  </data>
  <data name="AcademicYear.Deleted" xml:space="preserve">
    <value>Academic year deleted successfully</value>
  </data>
  <data name="AcademicYear.NotFound" xml:space="preserve">
    <value>Academic year not found</value>
  </data>
  <data name="AcademicYear.AlreadyExists" xml:space="preserve">
    <value>Academic year already exists for this period</value>
  </data>
  
  <!-- Holiday Messages -->
  <data name="Holiday.Created" xml:space="preserve">
    <value>Holiday created successfully</value>
  </data>
  <data name="Holiday.Updated" xml:space="preserve">
    <value>Holiday updated successfully</value>
  </data>
  <data name="Holiday.Deleted" xml:space="preserve">
    <value>Holiday deleted successfully</value>
  </data>
  <data name="Holiday.NotFound" xml:space="preserve">
    <value>Holiday not found</value>
  </data>
  <data name="Holiday.DateConflict" xml:space="preserve">
    <value>Holiday dates conflict with existing holiday</value>
  </data>
  
  <!-- Calendar Messages -->
  <data name="Calendar.EventCreated" xml:space="preserve">
    <value>Calendar event created successfully</value>
  </data>
  <data name="Calendar.EventUpdated" xml:space="preserve">
    <value>Calendar event updated successfully</value>
  </data>
  <data name="Calendar.EventDeleted" xml:space="preserve">
    <value>Calendar event deleted successfully</value>
  </data>
  <data name="Calendar.EventNotFound" xml:space="preserve">
    <value>Calendar event not found</value>
  </data>
  
  <!-- General Messages -->
  <data name="General.Success" xml:space="preserve">
    <value>Operation completed successfully</value>
  </data>
  <data name="General.Error" xml:space="preserve">
    <value>An error occurred while processing your request</value>
  </data>
  <data name="General.NotFound" xml:space="preserve">
    <value>The requested resource was not found</value>
  </data>
  <data name="General.BadRequest" xml:space="preserve">
    <value>Invalid request</value>
  </data>
  <data name="General.InternalError" xml:space="preserve">
    <value>Internal server error</value>
  </data>
</root>
