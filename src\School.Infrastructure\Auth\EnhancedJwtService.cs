using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using School.Application.Common.Interfaces;
using School.Infrastructure.Identity;
using School.Infrastructure.Persistence;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.EntityFrameworkCore;

namespace School.Infrastructure.Auth
{
    public class EnhancedJwtService : ITokenGenerator
    {
        private readonly IConfiguration _configuration;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;

        public EnhancedJwtService(
            IConfiguration configuration, 
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context)
        {
            _configuration = configuration;
            _userManager = userManager;
            _context = context;
        }

        public async Task<(string token, DateTime expiration)> GenerateTokenAsync(object applicationUser)
        {
            if (applicationUser is not ApplicationUser user)
                throw new ArgumentException("Invalid user type", nameof(applicationUser));

            var jwtKey = _configuration["Jwt:Key"];
            if (string.IsNullOrEmpty(jwtKey))
                throw new InvalidOperationException("JWT Key not configured");

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            // Get user roles
            var roles = await _userManager.GetRolesAsync(user);

            // Create claims
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id),
                new(ClaimTypes.Name, user.UserName ?? ""),
                new(ClaimTypes.Email, user.Email ?? ""),
                new("userId", user.Id),
                new("role", user.Role.ToString()),
                new(ClaimTypes.Role, user.Role.ToString()),
                new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new(JwtRegisteredClaimNames.Sub, user.Id),
            };

            // Add role claims from Identity
            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            // Add permission based on role
            var permission = GetPermissionForRole(user.Role);
            claims.Add(new Claim("permission", permission));

            // Short-lived access token (15 minutes)
            var expiration = DateTime.UtcNow.AddMinutes(15);

            var token = new JwtSecurityToken(
                issuer: _configuration["Jwt:Issuer"],
                audience: _configuration["Jwt:Audience"],
                claims: claims,
                expires: expiration,
                signingCredentials: credentials
            );

            var tokenString = new JwtSecurityTokenHandler().WriteToken(token);
            return (tokenString, expiration);
        }

        public async Task<(string token, DateTime expiration)> GenerateAdminTokenAsync(object applicationUser, IList<string> roles)
        {
            if (applicationUser is not ApplicationUser user)
                throw new ArgumentException("Invalid user type", nameof(applicationUser));

            var jwtKey = _configuration["Jwt:Key"];
            if (string.IsNullOrEmpty(jwtKey))
                throw new InvalidOperationException("JWT Key not configured");

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            // Create admin-specific claims
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id),
                new(ClaimTypes.Name, user.UserName ?? ""),
                new(ClaimTypes.Email, user.Email ?? ""),
                new("userId", user.Id),
                new("role", user.Role.ToString()),
                new(ClaimTypes.Role, user.Role.ToString()),
                new("isAdmin", "true"), // Special admin claim
                new("adminType", roles.Contains("SystemAdmin") ? "SystemAdmin" : "TenantAdmin"),
                new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new(JwtRegisteredClaimNames.Sub, user.Id),
            };

            // Add all role claims from Identity
            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            // Add admin permissions
            claims.Add(new Claim("permission", "admin"));
            claims.Add(new Claim("canAccessAllTenants", roles.Contains("SystemAdmin").ToString().ToLower()));
            claims.Add(new Claim("isSystemAdmin", roles.Contains("SystemAdmin").ToString().ToLower()));
            claims.Add(new Claim("isTenantAdmin", roles.Contains("Admin").ToString().ToLower()));

            // Longer expiration for admin tokens (1 hour)
            var expiration = DateTime.UtcNow.AddHours(1);

            var token = new JwtSecurityToken(
                issuer: _configuration["Jwt:Issuer"],
                audience: _configuration["Jwt:Audience"],
                claims: claims,
                expires: expiration,
                signingCredentials: credentials
            );

            var tokenString = new JwtSecurityTokenHandler().WriteToken(token);
            return (tokenString, expiration);
        }

        public async Task<(string refreshToken, DateTime expiration)> GenerateRefreshTokenAsync(string userId)
        {
            // Generate cryptographically secure refresh token
            var randomBytes = new byte[64];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomBytes);
            var refreshToken = System.Convert.ToBase64String(randomBytes);

            // Long-lived refresh token (7 days)
            var expiration = DateTime.UtcNow.AddDays(7);

            // Store refresh token in database
            var userRefreshToken = new UserRefreshToken
            {
                UserId = userId,
                Token = refreshToken,
                ExpiryDate = expiration,
                CreatedAt = DateTime.UtcNow,
                IsRevoked = false
            };

            _context.UserRefreshTokens.Add(userRefreshToken);
            await _context.SaveChangesAsync();

            return (refreshToken, expiration);
        }

        public async Task<bool> ValidateRefreshTokenAsync(string refreshToken, string userId)
        {
            var token = await _context.UserRefreshTokens
                .FirstOrDefaultAsync(rt => rt.Token == refreshToken &&
                                         rt.UserId == userId &&
                                         !rt.IsRevoked &&
                                         rt.ExpiryDate > DateTime.UtcNow);

            return token != null;
        }

        public async Task RevokeRefreshTokenAsync(string refreshToken)
        {
            var token = await _context.UserRefreshTokens
                .FirstOrDefaultAsync(rt => rt.Token == refreshToken);

            if (token != null)
            {
                token.IsRevoked = true;
                token.RevokedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
        }

        public async Task RevokeAllUserRefreshTokensAsync(string userId)
        {
            var tokens = await _context.UserRefreshTokens
                .Where(rt => rt.UserId == userId && !rt.IsRevoked)
                .ToListAsync();

            foreach (var token in tokens)
            {
                token.IsRevoked = true;
                token.RevokedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
        }

        private string GetPermissionForRole(Domain.Enums.UserRole role)
        {
            return role switch
            {
                Domain.Enums.UserRole.Admin => "admin",
                Domain.Enums.UserRole.Student => "student",
                Domain.Enums.UserRole.Faculty => "teacher",
                Domain.Enums.UserRole.Parent => "parent",
                Domain.Enums.UserRole.Alumni => "alumni",
                _ => "user"
            };
        }

        // Interface implementation methods
        public (string token, DateTime expiration) GenerateToken(Domain.Entities.User user)
        {
            // Legacy method for User entity - simplified implementation
            var jwtKey = _configuration["Jwt:Key"];
            if (string.IsNullOrEmpty(jwtKey))
                throw new InvalidOperationException("JWT Key not configured");

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new(ClaimTypes.Name, user.Username),
                new(ClaimTypes.Email, user.Email),
                new("userId", user.Id.ToString()),
                new("role", user.Role.ToString()),
                new(ClaimTypes.Role, user.Role.ToString()),
                new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new(JwtRegisteredClaimNames.Sub, user.Id.ToString())
            };

            var expiration = DateTime.UtcNow.AddMinutes(15);

            var token = new JwtSecurityToken(
                issuer: _configuration["Jwt:Issuer"],
                audience: _configuration["Jwt:Audience"],
                claims: claims,
                expires: expiration,
                signingCredentials: credentials
            );

            return (new JwtSecurityTokenHandler().WriteToken(token), expiration);
        }

        public string GenerateRefreshToken()
        {
            var randomBytes = new byte[64];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomBytes);
            return System.Convert.ToBase64String(randomBytes);
        }

        public string GenerateMfaToken(string userId)
        {
            var jwtKey = _configuration["Jwt:Key"];
            if (string.IsNullOrEmpty(jwtKey))
                throw new InvalidOperationException("JWT Key not configured");

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = new List<Claim>
            {
                new("userId", userId),
                new("tokenType", "mfa"),
                new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            };

            var expiration = DateTime.UtcNow.AddMinutes(5);

            var token = new JwtSecurityToken(
                issuer: _configuration["Jwt:Issuer"],
                audience: _configuration["Jwt:Audience"],
                claims: claims,
                expires: expiration,
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        public ClaimsPrincipal? GetPrincipalFromExpiredToken(string token)
        {
            var jwtKey = _configuration["Jwt:Key"];
            if (string.IsNullOrEmpty(jwtKey))
                throw new InvalidOperationException("JWT Key not configured");

            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateAudience = false,
                ValidateIssuer = false,
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey)),
                ValidateLifetime = false // Don't validate lifetime for refresh
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out SecurityToken securityToken);

            if (securityToken is not JwtSecurityToken jwtSecurityToken ||
                !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
                throw new SecurityTokenException("Invalid token");

            return principal;
        }
    }
}
