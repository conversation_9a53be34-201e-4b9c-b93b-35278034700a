<!-- Hero Section -->
<app-default-hero
  translationPrefix="CAREERS"
  title="CAREERS.TITLE"
  subtitle="CAREERS.SUBTITLE"
  theme="dark"
  size="large"
  alignment="center"
  backgroundImage="assets/images/careers-hero.jpg">
</app-default-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'CAREERS.JOIN_OUR_TEAM' | translate }}</h2>
      <p>{{ 'CAREERS.INTRO_P1' | translate }}</p>
      <p>{{ 'CAREERS.INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Current Openings Section -->
  <section class="openings-section">
    <h2>{{ 'CAREERS.CURRENT_OPENINGS' | translate }}</h2>
    <p class="section-intro">{{ 'CAREERS.OPENINGS_INTRO' | translate }}</p>

    <div class="department-filter">
      <span class="filter-label">{{ 'CAREERS.FILTER_BY_DEPARTMENT' | translate }}:</span>
      <div class="filter-buttons">
        <button
          *ngFor="let department of departments"
          [class.active]="selectedDepartment === department"
          (click)="selectedDepartment = department">
          {{department}}
        </button>
      </div>
    </div>

    <div class="job-listings">
      <mat-card class="job-card" *ngFor="let job of getFilteredJobs()">
        <mat-card-header>
          <mat-card-title>{{job.title}}</mat-card-title>
          <mat-card-subtitle>
            <span class="job-department">{{job.department}}</span>
            <span class="job-type" [ngClass]="job.type">{{job.type}}</span>
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="job-meta">
            <div class="meta-item">
              <mat-icon>location_on</mat-icon>
              <span>{{job.location}}</span>
            </div>
            <div class="meta-item">
              <mat-icon>event</mat-icon>
              <span>{{ 'CAREERS.POSTED' | translate }} {{getDaysSincePosting(job.postedDate)}} {{ 'CAREERS.DAYS_AGO' | translate }}</span>
            </div>
          </div>
          <p class="job-description">{{job.description}}</p>
        </mat-card-content>
        <mat-card-actions>
          <a mat-raised-button color="primary" [routerLink]="['/careers', job.id]">
            {{ 'CAREERS.VIEW_DETAILS' | translate }}
          </a>
          <a mat-stroked-button color="primary" [routerLink]="['/careers/apply', job.id]">
            {{ 'CAREERS.APPLY_NOW' | translate }}
          </a>
        </mat-card-actions>
      </mat-card>
    </div>
  </section>

  <!-- Benefits Section -->
  <section class="benefits-section">
    <h2>{{ 'CAREERS.BENEFITS' | translate }}</h2>
    <p class="section-intro">{{ 'CAREERS.BENEFITS_INTRO' | translate }}</p>

    <div class="benefits-grid">
      <div class="benefit-card" *ngFor="let benefit of benefits">
        <div class="benefit-icon">
          <mat-icon>{{benefit.icon}}</mat-icon>
        </div>
        <h3>{{benefit.title}}</h3>
        <p>{{benefit.description}}</p>
      </div>
    </div>
  </section>

  <!-- Testimonials Section -->
  <section class="testimonials-section">
    <h2>{{ 'CAREERS.EMPLOYEE_TESTIMONIALS' | translate }}</h2>
    <p class="section-intro">{{ 'CAREERS.TESTIMONIALS_INTRO' | translate }}</p>

    <div class="testimonials-container">
      <div class="testimonial-card" *ngFor="let testimonial of testimonials">
        <div class="testimonial-image">
          <img [src]="testimonial.image" [alt]="testimonial.name">
        </div>
        <div class="testimonial-content">
          <div class="quote-icon">
            <mat-icon>format_quote</mat-icon>
          </div>
          <p class="testimonial-quote">{{testimonial.quote}}</p>
          <div class="testimonial-author">
            <p class="author-name">{{testimonial.name}}</p>
            <p class="author-position">{{testimonial.position}}</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Application Process Section -->
  <section class="process-section">
    <h2>{{ 'CAREERS.APPLICATION_PROCESS' | translate }}</h2>
    <p class="section-intro">{{ 'CAREERS.PROCESS_INTRO' | translate }}</p>

    <div class="process-steps">
      <div class="step" *ngFor="let step of applicationSteps; let i = index">
        <div class="step-number">{{i + 1}}</div>
        <div class="step-content">
          <h3>{{step.title}}</h3>
          <p>{{step.description}}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQs Section -->
  <section class="faqs-section">
    <h2>{{ 'CAREERS.FAQS' | translate }}</h2>
    <p class="section-intro">{{ 'CAREERS.FAQS_INTRO' | translate }}</p>

    <div class="faqs-container">
      <mat-accordion>
        <mat-expansion-panel *ngFor="let faq of faqs">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{faq.question}}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <p>{{faq.answer}}</p>
        </mat-expansion-panel>
      </mat-accordion>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="contact-section">
    <div class="contact-content">
      <h2>{{ 'CAREERS.QUESTIONS' | translate }}</h2>
      <p>{{ 'CAREERS.CONTACT_TEXT' | translate }}</p>
      <div class="contact-buttons">
        <a mat-raised-button color="primary" href="mailto:careers&#64;school.edu">
          <mat-icon>email</mat-icon>
          {{ 'CAREERS.EMAIL_HR' | translate }}
        </a>
        <a mat-stroked-button color="primary" routerLink="/contact">
          <mat-icon>contact_support</mat-icon>
          {{ 'CAREERS.CONTACT_US' | translate }}
        </a>
      </div>
    </div>
  </section>
</div>