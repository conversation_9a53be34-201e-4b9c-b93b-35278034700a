using School.Domain.Enums;

namespace School.Application.DTOs;

public class TuitionFeeDto
{
    public Guid Id { get; set; }
    public EducationLevel Level { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public FeeType Type { get; set; }
    public FeeFrequency Frequency { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public List<TuitionFeeTranslationDto> Translations { get; set; } = new List<TuitionFeeTranslationDto>();
}

public class TuitionFeeTranslationDto
{
    public Guid Id { get; set; }
    public Guid TuitionFeeId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public class CreateTuitionFeeDto
{
    public EducationLevel Level { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public FeeType Type { get; set; }
    public FeeFrequency Frequency { get; set; }
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }
}

public class UpdateTuitionFeeDto
{
    public EducationLevel Level { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public FeeType Type { get; set; }
    public FeeFrequency Frequency { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
}

public class CreateTuitionFeeTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public class UpdateTuitionFeeTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}
