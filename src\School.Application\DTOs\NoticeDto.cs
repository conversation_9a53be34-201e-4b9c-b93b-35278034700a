using School.Application.Common.Mappings;
using School.Application.DTOs.Common;
using School.Domain.Entities;
using School.Domain.Enums;
using System;
using System.Collections.Generic;

namespace School.Application.DTOs;

public class NoticeDto : IMapFrom<Notice>
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public NoticePriority Priority { get; set; }
    public string Category { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public Guid CreatedById { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public List<NoticeTranslationDto> Translations { get; set; } = new();
}

public class NoticeCreateDto
{
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public NoticePriority Priority { get; set; } = NoticePriority.Medium;
    public string Category { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public List<NoticeTranslationCreateDto>? Translations { get; set; }
}

public class NoticeUpdateDto
{
    public string? Title { get; set; }
    public string? Content { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public NoticePriority? Priority { get; set; }
    public string? Category { get; set; }
    public bool? IsActive { get; set; }
}

public class NoticeTranslationDto : IMapFrom<NoticeTranslation>
{
    public Guid Id { get; set; }
    public Guid NoticeId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
}

public class NoticeTranslationCreateDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
}

public class NoticeTranslationUpdateDto
{
    public string? Title { get; set; }
    public string? Content { get; set; }
}

/// <summary>
/// Filter DTO for notice queries
/// </summary>
public class NoticeFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filter by category
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Search term for title or content
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// Filter by priority
    /// </summary>
    public NoticePriority? Priority { get; set; }

    /// <summary>
    /// Filter by start date (from)
    /// </summary>
    public DateTime? StartDateFrom { get; set; }

    /// <summary>
    /// Filter by start date (to)
    /// </summary>
    public DateTime? StartDateTo { get; set; }
}
