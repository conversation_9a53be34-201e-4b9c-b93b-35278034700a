import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule, MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-icon-test',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule],
  template: `
    <div class="icon-test-container">
      <h2>Material Icons Test</h2>

      <div class="test-section">
        <h3>Standard Icons</h3>
        <div class="icon-row">
          <div class="icon-item">
            <mat-icon>home</mat-icon>
            <p>home</p>
          </div>

          <div class="icon-item">
            <mat-icon>person</mat-icon>
            <p>person</p>
          </div>

          <div class="icon-item">
            <mat-icon>settings</mat-icon>
            <p>settings</p>
          </div>

          <div class="icon-item">
            <mat-icon>favorite</mat-icon>
            <p>favorite</p>
          </div>

          <div class="icon-item">
            <mat-icon>menu</mat-icon>
            <p>menu</p>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>Icon Variants</h3>
        <div class="icon-row">
          <div class="icon-item">
            <mat-icon>home</mat-icon>
            <p>Standard</p>
          </div>

          <div class="icon-item">
            <mat-icon fontSet="material-icons-outlined">home</mat-icon>
            <p>Outlined</p>
          </div>

          <div class="icon-item">
            <mat-icon fontSet="material-icons-round">home</mat-icon>
            <p>Round</p>
          </div>

          <div class="icon-item">
            <mat-icon fontSet="material-icons-sharp">home</mat-icon>
            <p>Sharp</p>
          </div>

          <div class="icon-item">
            <mat-icon fontSet="material-icons-two-tone">home</mat-icon>
            <p>Two Tone</p>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>Icon Sizes</h3>
        <div class="icon-row">
          <div class="icon-item">
            <mat-icon style="font-size: 18px; height: 18px; width: 18px;">star</mat-icon>
            <p>Small (18px)</p>
          </div>

          <div class="icon-item">
            <mat-icon>star</mat-icon>
            <p>Default (24px)</p>
          </div>

          <div class="icon-item">
            <mat-icon style="font-size: 36px; height: 36px; width: 36px;">star</mat-icon>
            <p>Medium (36px)</p>
          </div>

          <div class="icon-item">
            <mat-icon style="font-size: 48px; height: 48px; width: 48px;">star</mat-icon>
            <p>Large (48px)</p>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>Icons in Buttons</h3>
        <div class="button-row">
          <button mat-button color="primary">
            <mat-icon>favorite</mat-icon> Like
          </button>

          <button mat-raised-button color="primary">
            <mat-icon>save</mat-icon> Save
          </button>

          <button mat-stroked-button color="primary">
            <mat-icon>delete</mat-icon> Delete
          </button>

          <button mat-icon-button color="primary">
            <mat-icon>search</mat-icon>
          </button>
        </div>
      </div>

      <div class="test-section">
        <h3>Colored Icons</h3>
        <div class="icon-row">
          <div class="icon-item">
            <mat-icon style="color: #f44336;">warning</mat-icon>
            <p>Red</p>
          </div>

          <div class="icon-item">
            <mat-icon style="color: #4caf50;">check_circle</mat-icon>
            <p>Green</p>
          </div>

          <div class="icon-item">
            <mat-icon style="color: #2196f3;">info</mat-icon>
            <p>Blue</p>
          </div>

          <div class="icon-item">
            <mat-icon style="color: #ff9800;">star</mat-icon>
            <p>Orange</p>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .icon-test-container {
      padding: 20px;
      background-color: #f5f5f5;
      border-radius: 8px;
      margin: 20px 0;
    }

    h2 {
      margin-bottom: 30px;
      color: #333;
      text-align: center;
      font-size: 28px;
    }

    .test-section {
      background-color: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 30px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    h3 {
      margin-bottom: 20px;
      color: #333;
      font-size: 20px;
      border-bottom: 2px solid #eee;
      padding-bottom: 10px;
    }

    .icon-row {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
    }

    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      width: 100px;
      transition: transform 0.2s, box-shadow 0.2s;
    }

    .icon-item:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    mat-icon {
      font-size: 24px;
      height: 24px;
      width: 24px;
      margin-bottom: 10px;
    }

    p {
      margin: 0;
      font-size: 12px;
      color: #666;
      text-align: center;
    }

    .button-row {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      align-items: center;
    }

    button mat-icon {
      margin-bottom: 0;
      margin-right: 5px;
    }
  `]
})
export class IconTestComponent implements OnInit {
  constructor(
    private matIconRegistry: MatIconRegistry,
    private domSanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    // Log available icon sets for debugging
    console.log('IconTestComponent initialized');

    // Register a test SVG icon
    try {
      this.matIconRegistry.addSvgIcon(
        'custom_icon',
        this.domSanitizer.bypassSecurityTrustResourceUrl('./assets/icons/custom-icon.svg')
      );
    } catch (error) {
      console.warn('Could not register custom SVG icon:', error);
    }
  }
}
