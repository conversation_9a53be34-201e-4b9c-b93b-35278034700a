using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class StudentAcademicHistory : BaseEntity
{
    public Guid StudentId { get; set; }

    // Foreign key relationships
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }

    public int Grade { get; set; }
    public string Section { get; set; } = string.Empty;
    public TeachingMedium Medium { get; set; }
    public ShiftType Shift { get; set; }
    public int RollNumber { get; set; }
    public string StudentIdForYear { get; set; } = string.Empty;
    public decimal? FinalGPA { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public bool IsPromoted { get; set; }
    public Guid? ClassTeacherId { get; set; }

    // Navigation properties
    public Student Student { get; set; } = null!;
    public AcademicYear? AcademicYear { get; set; }
    public Term? Term { get; set; }
    public Faculty? ClassTeacher { get; set; }
}
