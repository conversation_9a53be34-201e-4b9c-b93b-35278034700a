using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Domain.Entities;
using School.Domain.Enums;

namespace School.Infrastructure.Services;

/// <summary>
/// Service for managing tenant context and operations
/// </summary>
public class TenantService : ITenantService
{
    private readonly IApplicationDbContext _context;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ITenantContext _tenantContext;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<TenantService> _logger;
    private Guid? _currentTenantId;
    private Organization? _currentTenant;

    public TenantService(
        IApplicationDbContext context,
        IHttpContextAccessor httpContextAccessor,
        ITenantContext tenantContext,
        ICurrentUserService currentUserService,
        ILogger<TenantService> logger)
    {
        _context = context;
        _httpContextAccessor = httpContextAccessor;
        _tenantContext = tenantContext;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public Guid? GetCurrentTenantId()
    {
        // Use tenant context as the primary source
        var tenantId = _tenantContext.GetCurrentTenantId();
        if (tenantId.HasValue)
        {
            return tenantId;
        }

        // Fallback to instance variable
        return _currentTenantId;
    }

    public async Task<Organization?> GetCurrentTenantAsync()
    {
        if (_currentTenant != null)
        {
            return _currentTenant;
        }

        if (_currentTenantId.HasValue)
        {
            _currentTenant = await _context.Organizations
                .FirstOrDefaultAsync(o => o.Id == _currentTenantId.Value && o.IsActive);
            return _currentTenant;
        }

        // For admin users, don't auto-detect tenant - they must explicitly set tenant context
        // This ensures proper tenant admin flow where admins need to be granted access
        _logger.LogInformation("No tenant context found for user {UserId}", _currentUserService.UserId);

        // Check if user has any tenant access for debugging
        if (_currentUserService.IsAuthenticated && _currentUserService.UserId.HasValue)
        {
            var userTenants = await GetUserTenantsAsync(_currentUserService.UserId.Value.ToString());
            _logger.LogInformation("User {UserId} has access to {TenantCount} tenants",
                _currentUserService.UserId, userTenants.Count());

            foreach (var tenant in userTenants)
            {
                _logger.LogInformation("Available tenant: {TenantName} (ID: {TenantId})",
                    tenant.Name, tenant.Id);
            }
        }

        return null;
    }

    public async Task<bool> SetTenantAsync(string identifier)
    {
        try
        {
            Organization? tenant = null;

            // First try to find by slug (subdomain)
            tenant = await GetTenantBySlugAsync(identifier);

            // If not found, try custom domain
            if (tenant == null)
            {
                tenant = await GetTenantByDomainAsync(identifier);
            }

            if (tenant != null && tenant.IsActive)
            {
                _currentTenantId = tenant.Id;
                _currentTenant = tenant;

                // Set in tenant context
                _tenantContext.SetCurrentTenantId(tenant.Id);

                // Store in HTTP context for this request
                if (_httpContextAccessor.HttpContext != null)
                {
                    _httpContextAccessor.HttpContext.Items["TenantId"] = tenant.Id;
                    _httpContextAccessor.HttpContext.Items["Tenant"] = tenant;
                }

                _logger.LogInformation("Tenant context set to: {TenantName} (ID: {TenantId})",
                    tenant.Name, tenant.Id);

                return true;
            }

            _logger.LogWarning("Tenant not found or inactive for identifier: {Identifier}", identifier);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting tenant context for identifier: {Identifier}", identifier);
            return false;
        }
    }

    public async Task<bool> UserHasAccessToTenantAsync(string userId, Guid tenantId)
    {
        try
        {
            var organizationUser = await _context.OrganizationUsers
                .FirstOrDefaultAsync(ou => ou.UserId == userId && 
                                          ou.OrganizationId == tenantId && 
                                          ou.IsActive);

            return organizationUser != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking user access to tenant. UserId: {UserId}, TenantId: {TenantId}", 
                userId, tenantId);
            return false;
        }
    }

    public async Task<IEnumerable<Organization>> GetUserTenantsAsync(string userId)
    {
        try
        {
            var organizations = await _context.OrganizationUsers
                .Where(ou => ou.UserId == userId && ou.IsActive)
                .Include(ou => ou.Organization)
                .Select(ou => ou.Organization)
                .Where(o => o.IsActive)
                .ToListAsync();

            return organizations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user tenants for UserId: {UserId}", userId);
            return Enumerable.Empty<Organization>();
        }
    }

    public async Task<Organization?> GetTenantBySlugAsync(string slug)
    {
        try
        {
            return await _context.Organizations
                .FirstOrDefaultAsync(o => o.Slug.ToLower() == slug.ToLower() && o.IsActive);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant by slug: {Slug}", slug);
            return null;
        }
    }

    public async Task<Organization?> GetTenantByIdAsync(Guid tenantId)
    {
        try
        {
            return await _context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId && o.IsActive);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant by ID: {TenantId}", tenantId);
            return null;
        }
    }

    public async Task<Organization?> GetTenantByDomainAsync(string domain)
    {
        try
        {
            return await _context.Organizations
                .FirstOrDefaultAsync(o => o.CustomDomain != null && 
                                         o.CustomDomain.ToLower() == domain.ToLower() && 
                                         o.IsActive);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant by domain: {Domain}", domain);
            return null;
        }
    }

    public async Task<bool> IsSlugAvailableAsync(string slug, Guid? excludeTenantId = null)
    {
        try
        {
            var query = _context.Organizations.Where(o => o.Slug.ToLower() == slug.ToLower());
            
            if (excludeTenantId.HasValue)
            {
                query = query.Where(o => o.Id != excludeTenantId.Value);
            }

            return !await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking slug availability: {Slug}", slug);
            return false;
        }
    }

    public async Task<bool> IsDomainAvailableAsync(string domain, Guid? excludeTenantId = null)
    {
        try
        {
            var query = _context.Organizations.Where(o => o.CustomDomain != null && 
                                                         o.CustomDomain.ToLower() == domain.ToLower());
            
            if (excludeTenantId.HasValue)
            {
                query = query.Where(o => o.Id != excludeTenantId.Value);
            }

            return !await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking domain availability: {Domain}", domain);
            return false;
        }
    }

    public async Task<Organization> CreateTenantAsync(Organization organization)
    {
        try
        {
            _context.Organizations.Add(organization);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Created new tenant: {TenantName} (ID: {TenantId})", 
                organization.Name, organization.Id);
            
            return organization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tenant: {TenantName}", organization.Name);
            throw;
        }
    }

    public async Task<Organization> UpdateTenantAsync(Organization organization)
    {
        try
        {
            _context.Organizations.Update(organization);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Updated tenant: {TenantName} (ID: {TenantId})", 
                organization.Name, organization.Id);
            
            return organization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant: {TenantId}", organization.Id);
            throw;
        }
    }

    public async Task<bool> DeactivateTenantAsync(Guid tenantId)
    {
        try
        {
            var tenant = await _context.Organizations.FindAsync(tenantId);
            if (tenant != null)
            {
                tenant.IsActive = false;
                tenant.Status = OrganizationStatus.Inactive;
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Deactivated tenant: {TenantId}", tenantId);
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating tenant: {TenantId}", tenantId);
            return false;
        }
    }

    public async Task<bool> AddUserToTenantAsync(Guid tenantId, string userId, OrganizationRole role)
    {
        try
        {
            var existingUser = await _context.OrganizationUsers
                .FirstOrDefaultAsync(ou => ou.OrganizationId == tenantId && ou.UserId == userId);

            if (existingUser != null)
            {
                // User already exists, update role and activate if needed
                existingUser.Role = role;
                existingUser.IsActive = true;
                existingUser.AcceptedDate = DateTime.UtcNow;
            }
            else
            {
                // Add new user to organization
                var organizationUser = new OrganizationUser
                {
                    OrganizationId = tenantId,
                    UserId = userId,
                    Role = role,
                    IsActive = true,
                    JoinedDate = DateTime.UtcNow,
                    AcceptedDate = DateTime.UtcNow
                };

                _context.OrganizationUsers.Add(organizationUser);
            }

            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Added user {UserId} to tenant {TenantId} with role {Role}", 
                userId, tenantId, role);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding user to tenant. UserId: {UserId}, TenantId: {TenantId}", 
                userId, tenantId);
            return false;
        }
    }

    public async Task<bool> RemoveUserFromTenantAsync(Guid tenantId, string userId)
    {
        try
        {
            var organizationUser = await _context.OrganizationUsers
                .FirstOrDefaultAsync(ou => ou.OrganizationId == tenantId && ou.UserId == userId);

            if (organizationUser != null)
            {
                organizationUser.IsActive = false;
                organizationUser.LeftDate = DateTime.UtcNow;
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Removed user {UserId} from tenant {TenantId}", userId, tenantId);
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing user from tenant. UserId: {UserId}, TenantId: {TenantId}", 
                userId, tenantId);
            return false;
        }
    }

    public async Task<bool> UpdateUserRoleInTenantAsync(Guid tenantId, string userId, OrganizationRole role)
    {
        try
        {
            var organizationUser = await _context.OrganizationUsers
                .FirstOrDefaultAsync(ou => ou.OrganizationId == tenantId && ou.UserId == userId && ou.IsActive);

            if (organizationUser != null)
            {
                organizationUser.Role = role;
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Updated user {UserId} role in tenant {TenantId} to {Role}", 
                    userId, tenantId, role);
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user role in tenant. UserId: {UserId}, TenantId: {TenantId}", 
                userId, tenantId);
            return false;
        }
    }
}
