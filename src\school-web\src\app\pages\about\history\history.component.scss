// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section
.hero-section {
  width: 100%;
  margin-bottom: 2rem;
}

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Introduction Section
.intro-section {
  padding: $section-padding;

  .intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h2 {
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
      color: $text-color;
      position: relative;
      padding-bottom: 0.5rem;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background-color: $primary-color;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Timeline Section
.timeline-section {
  padding: $section-padding;
  background-color: $light-gray;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .timeline {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      left: 50%;
      width: 4px;
      background-color: $primary-color;
      transform: translateX(-50%);
    }

    .timeline-item {
      position: relative;
      margin-bottom: 60px;
      width: 50%;
      padding-right: 40px;

      &:last-child {
        margin-bottom: 0;
      }

      &:before {
        content: '';
        position: absolute;
        top: 20px;
        right: -8px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: $primary-color;
        z-index: 1;
      }

      &.right {
        left: 50%;
        padding-right: 0;
        padding-left: 40px;

        &:before {
          left: -8px;
          right: auto;
        }

        .timeline-content {
          &:before {
            left: -10px;
            border-width: 10px 10px 10px 0;
            border-color: transparent $white transparent transparent;
          }
        }
      }

      .timeline-content {
        background-color: $white;
        border-radius: $border-radius;
        padding: 20px;
        box-shadow: $box-shadow;
        position: relative;

        &:before {
          content: '';
          position: absolute;
          top: 20px;
          right: -10px;
          border-width: 10px 0 10px 10px;
          border-style: solid;
          border-color: transparent transparent transparent $white;
        }

        .timeline-year {
          display: inline-block;
          background-color: $primary-color;
          color: $white;
          padding: 5px 15px;
          border-radius: 20px;
          font-weight: bold;
          margin-bottom: 15px;
        }

        .timeline-image {
          margin-bottom: 15px;
          border-radius: $border-radius;
          overflow: hidden;

          img {
            width: 100%;
            height: auto;
            display: block;
          }
        }

        h3 {
          font-size: 1.5rem;
          margin-bottom: 10px;
          color: $text-color;
        }

        p {
          margin-bottom: 0;
          color: $dark-gray;
          line-height: 1.6;
        }
      }
    }
  }
}

// Founders Section
.founders-section {
  padding: $section-padding;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .founders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;

    .founder-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .founder-image {
        height: 250px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }

      mat-card-content {
        padding: 20px;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 5px;
          color: $text-color;
        }

        h4 {
          font-size: 1.1rem;
          margin-bottom: 15px;
          color: $primary-color;
          font-weight: 500;
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }
}

// Legacy Section
.legacy-section {
  padding: $section-padding;
  background-color: $light-gray;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .legacy-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;
    }

    .legacy-cta {
      margin-top: 30px;

      a {
        padding: 10px 30px;
        font-size: 1.1rem;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  // No hero-specific styles needed here

  .intro-section, .timeline-section, .founders-section, .legacy-section {
    padding: 60px 0;

    h2 {
      font-size: 2rem;
    }
  }
}

@media (max-width: 768px) {
  .timeline {
    &:before {
      left: 30px;
    }

    .timeline-item {
      width: 100%;
      padding-right: 0;
      padding-left: 70px;

      &:before {
        left: 22px;
        right: auto;
      }

      &.right {
        left: 0;
        padding-left: 70px;

        &:before {
          left: 22px;
        }

        .timeline-content {
          &:before {
            left: -10px;
            border-width: 10px 10px 10px 0;
            border-color: transparent $white transparent transparent;
          }
        }
      }

      .timeline-content {
        &:before {
          left: -10px;
          right: auto;
          border-width: 10px 10px 10px 0;
          border-color: transparent $white transparent transparent;
        }
      }
    }
  }

  // No hero-specific styles needed here
}

@media (max-width: 576px) {
  // No hero-specific styles needed here

  .intro-section, .timeline-section, .founders-section, .legacy-section {
    h2 {
      font-size: 1.8rem;
    }
  }

  .founders-grid {
    grid-template-columns: 1fr;
  }
}
