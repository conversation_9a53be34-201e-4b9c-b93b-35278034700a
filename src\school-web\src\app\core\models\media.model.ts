import { BaseFilter } from './base.model';

export interface MediaItem {
  id: number;
  fileName: string;
  originalFileName?: string;
  filePath: string;
  mimeType: string;
  fileSize?: number;
  type?: MediaType;
  altText?: string;
  caption?: string;
  contentId?: number;
  uploadedById?: string;
  uploadedByName?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export enum MediaType {
  Image = 0,
  Document = 1,
  Video = 2,
  Audio = 3,
  Other = 4
}

export interface MediaFilter extends BaseFilter {
  search?: string;
  type?: MediaType;
  contentId?: number;
}

export interface MediaUpload {
  file: File;
  type: MediaType;
  altText?: string;
  caption?: string;
  contentId?: number;
}
