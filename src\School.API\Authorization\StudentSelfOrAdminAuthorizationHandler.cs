using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;

namespace School.API.Authorization
{
    public class StudentSelfOrAdminRequirement : IAuthorizationRequirement
    {
    }

    public class StudentSelfOrAdminAuthorizationHandler : AuthorizationHandler<StudentSelfOrAdminRequirement>
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<StudentSelfOrAdminAuthorizationHandler> _logger;

        public StudentSelfOrAdminAuthorizationHandler(
            IHttpContextAccessor httpContextAccessor,
            ILogger<StudentSelfOrAdminAuthorizationHandler> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        protected override Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            StudentSelfOrAdminRequirement requirement)
        {
            // Check if user is an admin (Admin or SystemAdmin)
            if (context.User.IsInRole("Admin") || context.User.IsInRole("SystemAdmin"))
            {
                _logger.LogInformation("User is an admin, granting access");
                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            // Check if user is a student
            if (context.User.IsInRole("Student"))
            {
                // Get the user ID from the route
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext == null)
                {
                    _logger.LogWarning("HttpContext is null, denying access");
                    return Task.CompletedTask;
                }

                // Try to get the userId from the route
                if (!httpContext.Request.RouteValues.TryGetValue("userId", out var routeUserIdObj))
                {
                    _logger.LogWarning("No userId found in route values, denying access");
                    return Task.CompletedTask;
                }

                var routeUserId = routeUserIdObj?.ToString();
                if (string.IsNullOrEmpty(routeUserId))
                {
                    _logger.LogWarning("Route userId is null or empty, denying access");
                    return Task.CompletedTask;
                }

                // Get the user ID from the claims
                var userIdClaim = context.User.FindFirst("userId")?.Value ??
                                 context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                if (string.IsNullOrEmpty(userIdClaim))
                {
                    _logger.LogWarning("User ID claim not found, denying access");
                    return Task.CompletedTask;
                }

                // Allow access if the user ID in the route matches the user ID in the claims
                if (routeUserId == userIdClaim)
                {
                    _logger.LogInformation("User is accessing their own data, granting access");
                    context.Succeed(requirement);
                    return Task.CompletedTask;
                }

                _logger.LogWarning("User is trying to access another user's data, denying access. Route userId: {RouteUserId}, User's userId: {UserIdClaim}", 
                    routeUserId, userIdClaim);
            }

            return Task.CompletedTask;
        }
    }
}
