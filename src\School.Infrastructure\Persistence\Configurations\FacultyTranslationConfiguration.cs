using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class FacultyTranslationConfiguration : IEntityTypeConfiguration<FacultyTranslation>
{
    public void Configure(EntityTypeBuilder<FacultyTranslation> builder)
    {
        builder.Property(t => t.LanguageCode)
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(t => t.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(t => t.Title)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(t => t.ShortBio)
            .HasMaxLength(500);

        builder.HasOne(t => t.Faculty)
            .WithMany(t => t.Translations)
            .HasForeignKey(t => t.FacultyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(t => new { t.FacultyId, t.LanguageCode })
            .IsUnique();

        builder.HasQueryFilter(ft => !ft.IsDeleted);
    }
}
