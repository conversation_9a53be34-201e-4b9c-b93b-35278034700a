using Carter;
using School.API.Features.AcademicYear;
using School.API.Features.Grade;
using School.API.Features.Student;
using School.API.Features.Subject;

namespace School.API.Features;

/// <summary>
/// Extension methods for registering all feature-based endpoints
/// </summary>
public static class FeatureEndpointsExtensions
{
    /// <summary>
    /// Registers all feature-based minimal API endpoints
    /// </summary>
    /// <param name="app">The web application</param>
    /// <returns>The web application for chaining</returns>
    public static WebApplication MapFeatureEndpoints(this WebApplication app)
    {
        // Register all Carter modules (feature endpoints)
        app.MapCarter();

        return app;
    }

    /// <summary>
    /// Adds all feature-based services to the service collection
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddFeatureEndpoints(this IServiceCollection services)
    {
        // Add Carter for minimal API endpoints
        services.AddCarter();

        return services;
    }
}

/// <summary>
/// Base interface for all feature endpoint modules
/// </summary>
public interface IFeatureEndpoints : ICarterModule
{
    /// <summary>
    /// The feature name for grouping and documentation
    /// </summary>
    string FeatureName { get; }

    /// <summary>
    /// The base route prefix for this feature
    /// </summary>
    string RoutePrefix { get; }

    /// <summary>
    /// Tags for OpenAPI documentation
    /// </summary>
    string[] Tags { get; }
}

/// <summary>
/// Base class for feature endpoints with common functionality
/// </summary>
public abstract class FeatureEndpointsBase : IFeatureEndpoints
{
    public abstract string FeatureName { get; }
    public abstract string RoutePrefix { get; }
    public abstract string[] Tags { get; }

    public abstract void AddRoutes(IEndpointRouteBuilder app);

    /// <summary>
    /// Creates a route group with common configuration
    /// </summary>
    /// <param name="app">The endpoint route builder</param>
    /// <returns>Configured route group</returns>
    protected RouteGroupBuilder CreateFeatureGroup(IEndpointRouteBuilder app)
    {
        return app.MapGroup(RoutePrefix)
            .WithTags(Tags)
            .RequireAuthorization();
    }

    /// <summary>
    /// Handles common error responses
    /// </summary>
    /// <param name="ex">The exception</param>
    /// <param name="operation">The operation name</param>
    /// <returns>Problem result</returns>
    protected static IResult HandleError(Exception ex, string operation)
    {
        return ex switch
        {
            InvalidOperationException => Results.BadRequest(ex.Message),
            ArgumentException => Results.BadRequest(ex.Message),
            UnauthorizedAccessException => Results.Forbid(),
            KeyNotFoundException => Results.NotFound(ex.Message),
            _ => Results.Problem($"Error in {operation}: {ex.Message}")
        };
    }

    /// <summary>
    /// Creates a standardized success response for creation operations
    /// </summary>
    /// <param name="id">The created entity ID</param>
    /// <param name="routeName">The route name for the created resource</param>
    /// <returns>Created result</returns>
    protected IResult CreatedResponse(Guid id, string? routeName = null)
    {
        var location = routeName != null ? $"{RoutePrefix}/{id}" : null;
        return Results.Created(location, id);
    }

    /// <summary>
    /// Creates a standardized response for update operations
    /// </summary>
    /// <param name="success">Whether the operation was successful</param>
    /// <param name="entityName">The entity name for error messages</param>
    /// <param name="id">The entity ID</param>
    /// <returns>No content or not found result</returns>
    protected static IResult UpdateResponse(bool success, string entityName, Guid id)
    {
        return success ? Results.NoContent() : Results.NotFound($"{entityName} with ID {id} not found");
    }

    /// <summary>
    /// Creates a standardized response for delete operations
    /// </summary>
    /// <param name="success">Whether the operation was successful</param>
    /// <param name="entityName">The entity name for error messages</param>
    /// <param name="id">The entity ID</param>
    /// <returns>No content or not found result</returns>
    protected static IResult DeleteResponse(bool success, string entityName, Guid id)
    {
        return success ? Results.NoContent() : Results.NotFound($"{entityName} with ID {id} not found");
    }

    /// <summary>
    /// Creates a standardized response for get operations
    /// </summary>
    /// <param name="entity">The retrieved entity</param>
    /// <param name="entityName">The entity name for error messages</param>
    /// <param name="id">The entity ID</param>
    /// <returns>OK or not found result</returns>
    protected static IResult GetResponse<T>(T? entity, string entityName, Guid id) where T : class
    {
        return entity != null ? Results.Ok(entity) : Results.NotFound($"{entityName} with ID {id} not found");
    }

    /// <summary>
    /// Creates a standardized response for validation operations
    /// </summary>
    /// <param name="isValid">Whether the validation passed</param>
    /// <returns>OK result with validation status</returns>
    protected static IResult ValidationResponse(bool isValid)
    {
        return Results.Ok(isValid);
    }

    /// <summary>
    /// Creates a standardized response for list operations
    /// </summary>
    /// <param name="items">The list of items</param>
    /// <param name="totalCount">Total count for pagination</param>
    /// <returns>OK result with items and count</returns>
    protected static IResult ListResponse<T>(IEnumerable<T> items, int? totalCount = null)
    {
        if (totalCount.HasValue)
        {
            return Results.Ok(new { Items = items, TotalCount = totalCount.Value });
        }
        return Results.Ok(items);
    }

    /// <summary>
    /// Creates a standardized response for file operations
    /// </summary>
    /// <param name="stream">The file stream</param>
    /// <param name="contentType">The content type</param>
    /// <param name="fileName">The file name</param>
    /// <returns>File result</returns>
    protected static IResult FileResponse(Stream stream, string contentType, string fileName)
    {
        return Results.File(stream, contentType, fileName);
    }
}
