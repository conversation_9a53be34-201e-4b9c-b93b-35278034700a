<div class="tenant-form-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <mat-icon>{{ isEditMode ? 'edit' : 'add' }}</mat-icon>
        {{ isEditMode ? ('admin.tenants.edit' | translate) : ('admin.tenants.create' | translate) }}
      </h2>
      <p class="page-subtitle">
        {{ isEditMode ? ('admin.tenants.edit_subtitle' | translate) : ('admin.tenants.create_subtitle' | translate) }}
      </p>
    </div>
    <div class="header-actions">
      <button mat-stroked-button (click)="onCancel()">
        <mat-icon>close</mat-icon>
        {{ 'admin.common.cancel' | translate }}
      </button>
      <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="loading">
        <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
        <mat-icon *ngIf="!loading">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
        {{ isEditMode ? ('admin.common.save' | translate) : ('admin.common.create' | translate) }}
      </button>
    </div>
  </div>

  <form [formGroup]="tenantForm" (ngSubmit)="onSubmit()">
    <!-- Basic Information -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>info</mat-icon>
          {{ 'admin.tenants.basic_info' | translate }}
        </mat-card-title>
        <mat-card-subtitle>{{ 'admin.tenants.basic_info_subtitle' | translate }}</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>{{ 'admin.tenants.name' | translate }}</mat-label>
            <input matInput formControlName="name" placeholder="Enter organization name">
            <mat-error *ngIf="tenantForm.get('name')?.hasError('required')">
              Name is required
            </mat-error>
            <mat-error *ngIf="tenantForm.get('name')?.hasError('minlength')">
              Name must be at least 2 characters
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>{{ 'admin.tenants.slug' | translate }}</mat-label>
            <input matInput formControlName="slug" placeholder="organization-slug" [readonly]="isEditMode">
            <mat-hint>Used in URLs and API calls</mat-hint>
            <mat-error *ngIf="tenantForm.get('slug')?.hasError('required')">
              Slug is required
            </mat-error>
            <mat-error *ngIf="tenantForm.get('slug')?.hasError('pattern')">
              Slug can only contain lowercase letters, numbers, and hyphens
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>{{ 'admin.tenants.display_name' | translate }}</mat-label>
            <input matInput formControlName="displayName" placeholder="Display Name">
            <mat-error *ngIf="tenantForm.get('displayName')?.hasError('required')">
              Display name is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>{{ 'admin.tenants.type' | translate }}</mat-label>
            <mat-select formControlName="type">
              <mat-option *ngFor="let type of organizationTypes" [value]="type.value">
                {{ type.label }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>{{ 'admin.tenants.custom_domain' | translate }}</mat-label>
            <input matInput formControlName="customDomain" placeholder="school.example.com">
            <mat-hint>Optional custom domain</mat-hint>
            <mat-error *ngIf="tenantForm.get('customDomain')?.hasError('pattern')">
              Invalid domain format
            </mat-error>
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Localization Settings -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>language</mat-icon>
          {{ 'admin.tenants.localization' | translate }}
        </mat-card-title>
        <mat-card-subtitle>{{ 'admin.tenants.localization_subtitle' | translate }}</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="third-width">
            <mat-label>{{ 'admin.tenants.language' | translate }}</mat-label>
            <mat-select formControlName="defaultLanguage">
              <mat-option *ngFor="let lang of languages" [value]="lang.value">
                {{ lang.label }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="third-width">
            <mat-label>{{ 'admin.tenants.timezone' | translate }}</mat-label>
            <mat-select formControlName="timeZone">
              <mat-option *ngFor="let tz of timeZones" [value]="tz.value">
                {{ tz.label }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="third-width">
            <mat-label>{{ 'admin.tenants.currency' | translate }}</mat-label>
            <mat-select formControlName="currency">
              <mat-option *ngFor="let curr of currencies" [value]="curr.value">
                {{ curr.label }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Trial Settings -->
    <mat-card class="form-section">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>schedule</mat-icon>
          {{ 'admin.tenants.trial_settings' | translate }}
        </mat-card-title>
        <mat-card-subtitle>{{ 'admin.tenants.trial_subtitle' | translate }}</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="form-row">
          <mat-checkbox formControlName="isTrialActive" class="trial-checkbox">
            {{ 'admin.tenants.enable_trial' | translate }}
          </mat-checkbox>
        </div>

        <div class="form-row" *ngIf="isTrialActive">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>{{ 'admin.tenants.trial_end_date' | translate }}</mat-label>
            <input matInput [matDatepicker]="trialPicker" formControlName="trialEndDate">
            <mat-datepicker-toggle matSuffix [for]="trialPicker"></mat-datepicker-toggle>
            <mat-datepicker #trialPicker></mat-datepicker>
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Admin User (Create Mode Only) -->
    <mat-card class="form-section" *ngIf="!isEditMode" [formGroup]="adminForm">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>admin_panel_settings</mat-icon>
          {{ 'admin.tenants.admin_user' | translate }}
        </mat-card-title>
        <mat-card-subtitle>{{ 'admin.tenants.admin_user_subtitle' | translate }}</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>{{ 'admin.tenants.first_name' | translate }}</mat-label>
            <input matInput formControlName="firstName" placeholder="First Name">
            <mat-error *ngIf="adminForm.get('firstName')?.hasError('required')">
              First name is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>{{ 'admin.tenants.last_name' | translate }}</mat-label>
            <input matInput formControlName="lastName" placeholder="Last Name">
            <mat-error *ngIf="adminForm.get('lastName')?.hasError('required')">
              Last name is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>{{ 'admin.tenants.email' | translate }}</mat-label>
            <input matInput formControlName="email" type="email" placeholder="<EMAIL>">
            <mat-error *ngIf="adminForm.get('email')?.hasError('required')">
              Email is required
            </mat-error>
            <mat-error *ngIf="adminForm.get('email')?.hasError('email')">
              Invalid email format
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>{{ 'admin.tenants.username' | translate }}</mat-label>
            <input matInput formControlName="username" placeholder="admin_user">
            <mat-error *ngIf="adminForm.get('username')?.hasError('required')">
              Username is required
            </mat-error>
            <mat-error *ngIf="adminForm.get('username')?.hasError('pattern')">
              Username can only contain letters, numbers, and underscores
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>{{ 'admin.tenants.password' | translate }}</mat-label>
            <input matInput formControlName="password" type="password" placeholder="Password">
            <mat-hint>Minimum 8 characters</mat-hint>
            <mat-error *ngIf="adminForm.get('password')?.hasError('required')">
              Password is required
            </mat-error>
            <mat-error *ngIf="adminForm.get('password')?.hasError('minlength')">
              Password must be at least 8 characters
            </mat-error>
          </mat-form-field>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Form Actions -->
    <div class="form-actions">
      <button type="button" mat-stroked-button (click)="onCancel()">
        <mat-icon>close</mat-icon>
        {{ 'admin.common.cancel' | translate }}
      </button>
      <button type="submit" mat-raised-button color="primary" [disabled]="loading">
        <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
        <mat-icon *ngIf="!loading">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
        {{ isEditMode ? ('admin.common.save' | translate) : ('admin.common.create' | translate) }}
      </button>
    </div>
  </form>
</div>
