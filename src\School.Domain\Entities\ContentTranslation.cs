using School.Domain.Common;

namespace School.Domain.Entities;

public class ContentTranslation : BaseEntity
{
    public Guid ContentId { get; set; }
    public Content? Content { get; set; }

    public string LanguageCode { get; set; } = string.Empty; // en, bn
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string MetaDescription { get; set; } = string.Empty;
}
