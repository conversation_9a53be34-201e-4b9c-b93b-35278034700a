namespace School.Application.Common.Interfaces;

/// <summary>
/// Interface for accessing current tenant context
/// </summary>
public interface ITenantContext
{
    /// <summary>
    /// Gets the current tenant ID
    /// </summary>
    /// <returns>Current tenant ID or null if no tenant context is available</returns>
    Guid? GetCurrentTenantId();

    /// <summary>
    /// Sets the current tenant ID
    /// </summary>
    /// <param name="tenantId">Tenant ID to set</param>
    void SetCurrentTenantId(Guid? tenantId);

    /// <summary>
    /// Checks if tenant context is available
    /// </summary>
    /// <returns>True if tenant context is available, false otherwise</returns>
    bool HasTenantContext();

    /// <summary>
    /// Clears the current tenant context
    /// </summary>
    void ClearTenantContext();
}
