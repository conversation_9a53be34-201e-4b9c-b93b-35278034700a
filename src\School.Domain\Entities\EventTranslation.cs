using School.Domain.Common;

namespace School.Domain.Entities;

public class EventTranslation : BaseEntity
{
    public Guid EventId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;

    // Navigation properties
    public Event Event { get; set; } = null!;
    public string Organizer { get; set; }
}
