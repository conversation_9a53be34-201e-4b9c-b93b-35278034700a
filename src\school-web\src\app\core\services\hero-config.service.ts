import { Injectable } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { HeroImageService } from './hero-image.service';
import { HeroButton } from '../../shared/components/enhanced-hero/enhanced-hero.component';

export interface HeroConfig {
  title: string;
  subtitle: string;
  description: string;
  backgroundImage: string;
  overlayImage: string;
  breadcrumbs: string[];
  buttons: HeroButton[];
  theme: 'light' | 'dark';
  size: 'small' | 'medium' | 'large';
  alignment: 'left' | 'center' | 'right';
}

@Injectable({
  providedIn: 'root'
})
export class HeroConfigService {
  private currentRoute: string = '';

  constructor(
    private router: Router,
    private heroImageService: HeroImageService
  ) {
    // Track current route
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      this.currentRoute = event.urlAfterRedirects.split('?')[0].split('#')[0];
      if (this.currentRoute.startsWith('/')) {
        this.currentRoute = this.currentRoute.substring(1);
      }
    });
  }

  /**
   * Get the hero configuration for the current route
   * @param translationPrefix The translation prefix for the component (e.g., 'ABOUT', 'ACADEMICS')
   * @returns The hero configuration
   */
  getHeroConfig(translationPrefix: string): HeroConfig {
    const routePath = this.currentRoute;
    const backgroundImage = this.heroImageService.getHeroImage(routePath);
    const overlayImage = this.heroImageService.getSchoolEmblem();
    
    // Extract the last part of the route for breadcrumbs
    const breadcrumbs = routePath.split('/').map(part => 
      part.charAt(0).toUpperCase() + part.slice(1).replace(/-/g, ' ')
    );

    // Default configuration
    return {
      title: `${translationPrefix}.TITLE`,
      subtitle: `${translationPrefix}.SUBTITLE`,
      description: `${translationPrefix}.DESCRIPTION`,
      backgroundImage,
      overlayImage,
      breadcrumbs,
      buttons: [],
      theme: 'light',
      size: 'large',
      alignment: 'center'
    };
  }

  /**
   * Create a hero button configuration
   * @param label Translation key for the button label
   * @param link The link the button should navigate to
   * @param isPrimary Whether this is a primary button
   * @param icon Optional icon for the button
   * @returns A hero button configuration
   */
  createButton(label: string, link: string, isPrimary: boolean = false, icon?: string): HeroButton {
    return { label, link, isPrimary, icon };
  }
}
