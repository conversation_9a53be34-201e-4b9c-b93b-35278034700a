import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { TenantManagementService } from '../../../services/tenant-management.service';

export interface TenantInfo {
  id: string;
  name: string;
  slug: string;
  displayName: string;
  type: string;
  status: string;
  isActive: boolean;
  isTrialActive: boolean;
  trialEndDate?: string;
  customDomain?: string;
  defaultLanguage: string;
  timeZone: string;
  currency: string;
  createdAt: string;
  userCount?: number;
  subscriptionStatus?: string;
}

@Component({
  selector: 'app-tenant-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatChipsModule,
    MatTooltipModule,
    MatSnackBarModule,
    MatDialogModule,
    MatProgressSpinnerModule,
    TranslateModule
  ],
  templateUrl: './tenant-list.component.html',
  styleUrls: ['./tenant-list.component.scss']
})
export class TenantListComponent implements OnInit {
  tenants: TenantInfo[] = [];
  loading = false;
  searchTerm = '';
  statusFilter = 'all';
  
  displayedColumns: string[] = [
    'name',
    'slug',
    'type',
    'status',
    'userCount',
    'trialStatus',
    'createdAt',
    'actions'
  ];

  constructor(
    private router: Router,
    private tenantService: TenantManagementService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.loadTenants();
  }

  loadTenants() {
    this.loading = true;
    this.tenantService.getAllTenants().subscribe({
      next: (tenants) => {
        this.tenants = tenants;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading tenants:', error);
        this.snackBar.open('Error loading tenants', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  onSearch() {
    // Implement search functionality
    this.loadTenants();
  }

  onStatusFilterChange() {
    // Implement status filtering
    this.loadTenants();
  }

  createTenant() {
    this.router.navigate(['/admin/tenants/create']);
  }

  editTenant(tenant: TenantInfo) {
    this.router.navigate(['/admin/tenants/edit', tenant.id]);
  }

  viewTenant(tenant: TenantInfo) {
    this.router.navigate(['/admin/tenants', tenant.id]);
  }

  toggleTenantStatus(tenant: TenantInfo) {
    const newStatus = !tenant.isActive;
    this.tenantService.updateTenantStatus(tenant.id, newStatus).subscribe({
      next: () => {
        tenant.isActive = newStatus;
        this.snackBar.open(
          `Tenant ${newStatus ? 'activated' : 'deactivated'} successfully`,
          'Close',
          { duration: 3000 }
        );
      },
      error: (error) => {
        console.error('Error updating tenant status:', error);
        this.snackBar.open('Error updating tenant status', 'Close', { duration: 3000 });
      }
    });
  }

  deleteTenant(tenant: TenantInfo) {
    if (confirm(`Are you sure you want to delete tenant "${tenant.displayName || tenant.name}"?`)) {
      this.tenantService.deleteTenant(tenant.id).subscribe({
        next: () => {
          this.snackBar.open('Tenant deleted successfully', 'Close', { duration: 3000 });
          this.loadTenants(); // Reload the list
        },
        error: (error) => {
          console.error('Error deleting tenant:', error);
          this.snackBar.open('Error deleting tenant', 'Close', { duration: 3000 });
        }
      });
    }
  }

  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'active': return 'primary';
      case 'inactive': return 'warn';
      case 'trial': return 'accent';
      default: return '';
    }
  }

  getTypeIcon(type: string): string {
    switch (type.toLowerCase()) {
      case 'school': return 'school';
      case 'university': return 'account_balance';
      case 'institute': return 'business';
      default: return 'business';
    }
  }
}
