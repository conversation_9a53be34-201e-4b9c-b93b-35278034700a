.holiday-form-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;

      .page-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0;
        font-size: 2rem;
        font-weight: 500;
        color: var(--primary-color);

        mat-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px;
    gap: 16px;

    p {
      margin: 0;
      color: var(--text-secondary);
    }
  }

  .form-content {
    .mat-mdc-tab-group {
      margin-bottom: 24px;
    }

    .tab-content {
      padding: 24px 0;

      mat-card {
        margin-bottom: 24px;

        mat-card-header {
          margin-bottom: 16px;
        }
      }
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      .full-width {
        flex: 1;
        width: 100%;
      }

      .half-width {
        flex: 1;
        min-width: 0;
      }

      .quarter-width {
        flex: 0 0 calc(25% - 12px);
        min-width: 150px;
      }
    }

    // Type and Color Options
    .type-option,
    .color-option {
      display: flex;
      align-items: center;
      gap: 8px;

      .color-indicator,
      .color-preview {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 1px solid rgba(0, 0, 0, 0.12);
      }
    }

    // Toggle Group
    .toggle-group {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 16px 0;

      mat-slide-toggle {
        margin-right: 16px;
      }
    }

    // Translation Panels
    .translation-panel {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .translation-form {
        padding: 16px 0;
      }
    }

    // Form Actions
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      padding: 24px 0;
      border-top: 1px solid var(--border-color);
      margin-top: 24px;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .holiday-form-container {
    padding: 16px;

    .page-header .header-content {
      flex-direction: column;
      align-items: stretch;

      .page-title {
        font-size: 1.5rem;
        justify-content: center;
      }

      .header-actions {
        justify-content: center;
      }
    }

    .form-content {
      .form-row {
        flex-direction: column;

        .half-width,
        .quarter-width {
          width: 100%;
          flex: none;
        }
      }

      .toggle-group {
        flex-direction: column;
        gap: 12px;
      }

      .form-actions {
        flex-direction: column-reverse;

        button {
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .holiday-form-container {
    padding: 12px;

    .page-header .header-content .page-title {
      font-size: 1.25rem;

      mat-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }
    }

    .form-content .tab-content {
      padding: 16px 0;
    }
  }
}

// Dark theme support
.dark-theme {
  .holiday-form-container {
    .page-header .header-content .page-title {
      color: var(--primary-color-light);
    }

    .loading-container p {
      color: var(--text-secondary-dark);
    }

    .form-actions {
      border-top-color: var(--border-color-dark);
    }
  }
}

// Form validation styles
.mat-mdc-form-field {
  &.ng-invalid.ng-touched {
    .mat-mdc-text-field-wrapper {
      .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-thick {
          color: #f44336;
        }
      }
    }
  }
}

// Custom styles for specific form elements
.mat-mdc-slide-toggle {
  .mat-mdc-slide-toggle-label {
    font-weight: 500;
  }
}

.mat-expansion-panel {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;

  .mat-expansion-panel-header {
    font-weight: 500;
  }
}

// Tab content spacing
.mat-mdc-tab-body-content {
  overflow: visible !important;
}

// Color picker improvements
.mat-mdc-select-panel {
  .color-option {
    padding: 8px 16px;

    .color-preview {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }
  }
}

// Print styles
@media print {
  .holiday-form-container {
    .page-header .header-actions,
    .form-actions {
      display: none;
    }

    .form-content {
      .mat-mdc-tab-group {
        .mat-mdc-tab-header {
          display: none;
        }

        .mat-mdc-tab-body-wrapper {
          .mat-mdc-tab-body {
            .mat-mdc-tab-body-content {
              .tab-content {
                padding: 0;
              }
            }
          }
        }
      }
    }
  }
}
