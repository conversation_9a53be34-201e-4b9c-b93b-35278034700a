using School.Domain.Common;

namespace School.Domain.Entities;

public class TermTranslation : BaseEntity
{
    public Guid TermId { get; set; }
    public string LanguageCode { get; set; } = string.Empty; // e.g., "bn-BD", "en-US"
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    
    // Navigation properties
    public Term Term { get; set; } = null!;
}
