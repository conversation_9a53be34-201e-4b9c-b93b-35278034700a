.dialog-container {
  max-width: 700px;
  width: 100%;

  h2[mat-dialog-title] {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 16px 0;
    color: #1976d2;
    font-weight: 500;

    mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
  }
}

.section-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 70vh;
  overflow-y: auto;

  mat-tab-group {
    .mat-mdc-tab-body-content {
      overflow: visible;
    }
  }

  .tab-content {
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .form-row {
      display: flex;
      gap: 16px;

      .form-field {
        flex: 1;
      }
    }

    .full-width {
      width: 100%;
    }
  }

  .status-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 16px;
    background-color: #f5f5f5;
    border-radius: 8px;
    margin-top: 16px;

    .status-hint {
      color: #666;
      font-size: 0.875rem;
      margin-left: 4px;
    }
  }
}

mat-dialog-actions {
  padding: 16px 0 0 0;
  margin: 0;

  button {
    margin-left: 8px;

    .button-spinner {
      margin-right: 8px;
    }
  }
}

// Tab customization
mat-tab-group {
  .mat-mdc-tab-header {
    border-bottom: 1px solid #e0e0e0;
  }

  .mat-mdc-tab-label {
    min-width: 120px;
    padding: 0 16px;
  }

  .mat-mdc-tab-body-wrapper {
    padding-top: 0;
  }
}

// Form field customizations
mat-form-field {
  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
  }

  .mat-mdc-form-field-hint-wrapper {
    margin-top: 4px;
  }

  &.mat-focused {
    .mat-mdc-form-field-outline-thick {
      color: #1976d2;
    }
  }

  &.mat-form-field-invalid {
    .mat-mdc-form-field-outline-thick {
      color: #f44336;
    }
  }
}

// Select dropdown customization
mat-select {
  .mat-mdc-select-trigger {
    min-height: 56px;
  }
}

// Textarea customization
textarea {
  resize: vertical;
  min-height: 60px;
}

// Slide toggle customization
mat-slide-toggle {
  .mat-mdc-slide-toggle-bar {
    height: 20px;
    border-radius: 10px;
  }

  .mat-mdc-slide-toggle-thumb {
    width: 16px;
    height: 16px;
  }
}

// Responsive design
@media (max-width: 700px) {
  .dialog-container {
    max-width: 100%;
    margin: 0;
  }

  .section-form {
    .tab-content {
      .form-row {
        flex-direction: column;
        gap: 8px;
      }
    }
  }

  mat-dialog-actions {
    flex-direction: column-reverse;
    gap: 8px;

    button {
      margin: 0;
      width: 100%;
    }
  }

  mat-tab-group {
    .mat-mdc-tab-label {
      min-width: 80px;
      padding: 0 8px;
      font-size: 0.875rem;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .dialog-container {
    h2[mat-dialog-title] {
      color: #90caf9;
    }
  }

  .section-form {
    .status-section {
      background-color: #424242;

      .status-hint {
        color: #ccc;
      }
    }
  }

  mat-tab-group {
    .mat-mdc-tab-header {
      border-bottom-color: #424242;
    }
  }
}

// Animation for tabs
.tab-content {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Custom scrollbar for form
.section-form::-webkit-scrollbar {
  width: 6px;
}

.section-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.section-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.section-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// Button loading state
button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-spinner {
  display: inline-block;
  vertical-align: middle;
}

// Validation styling
.mat-mdc-form-field-error {
  font-size: 0.75rem;
  margin-top: 4px;
}

// Hint styling
.mat-mdc-form-field-hint {
  font-size: 0.75rem;
  color: #666;
}

// Focus trap for accessibility
.dialog-container {
  outline: none;
}

// High contrast mode support
@media (prefers-contrast: high) {
  .section-form {
    .status-section {
      border: 2px solid #000;
    }
  }

  mat-form-field {
    .mat-mdc-form-field-outline {
      border-width: 2px;
    }
  }
}
