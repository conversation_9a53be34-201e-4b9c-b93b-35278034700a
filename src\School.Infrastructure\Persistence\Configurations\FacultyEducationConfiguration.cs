using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class FacultyEducationConfiguration : IEntityTypeConfiguration<FacultyEducation>
{
    public void Configure(EntityTypeBuilder<FacultyEducation> builder)
    {
        builder.Property(t => t.Degree)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(t => t.Institution)
            .HasMaxLength(200)
            .IsRequired();

        builder.HasOne(t => t.Faculty)
            .WithMany(t => t.Education)
            .HasForeignKey(t => t.FacultyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasQueryFilter(fe => !fe.IsDeleted);
    }
}
