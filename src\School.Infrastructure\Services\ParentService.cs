using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Parent;
using School.Application.Features.Student;
using School.Domain.Entities;
using School.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Services
{
    public class ParentService : IParentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IStudentService _studentService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<ParentService> _logger;

        public ParentService(
            IUnitOfWork unitOfWork,
            IStudentService studentService,
            ICurrentUserService currentUserService,
            ILogger<ParentService> logger)
        {
            _unitOfWork = unitOfWork;
            _studentService = studentService;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        #region Parent Management

        public async Task<(IEnumerable<ParentDto> Parents, int TotalCount)> GetAllParentsAsync(ParentFilterDto filter)
        {
            var repository = _unitOfWork.Repository<Parent>();
            var query = repository.AsQueryable("ProfileImage");

            // Apply filters
            if (!string.IsNullOrEmpty(filter.Name))
            {
                query = query.Where(p => p.FirstName.Contains(filter.Name) || p.LastName.Contains(filter.Name));
            }

            if (!string.IsNullOrEmpty(filter.Email))
            {
                query = query.Where(p => p.Email.Contains(filter.Email));
            }

            if (!string.IsNullOrEmpty(filter.Phone))
            {
                query = query.Where(p => p.Phone.Contains(filter.Phone) || p.AlternatePhone.Contains(filter.Phone));
            }

            if (filter.IsActive.HasValue)
            {
                query = query.Where(p => p.IsActive == filter.IsActive.Value);
            }

            if (filter.StudentId.HasValue)
            {
                query = query.Where(p => p.Students.Any(s => s.StudentId == filter.StudentId.Value && !s.IsDeleted));
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply sorting
            if (!string.IsNullOrEmpty(filter.SortBy))
            {
                query = filter.SortBy.ToLower() switch
                {
                    "firstname" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(p => p.FirstName) :
                        query.OrderBy(p => p.FirstName),
                    "lastname" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(p => p.LastName) :
                        query.OrderBy(p => p.LastName),
                    "email" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(p => p.Email) :
                        query.OrderBy(p => p.Email),
                    "createdat" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(p => p.CreatedAt) :
                        query.OrderBy(p => p.CreatedAt),
                    _ => query.OrderBy(p => p.LastName).ThenBy(p => p.FirstName)
                };
            }
            else
            {
                query = query.OrderBy(p => p.LastName).ThenBy(p => p.FirstName);
            }

            // Get paginated results
            var parents = await query
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(p => new ParentDto
                {
                    Id = p.Id,
                    FirstName = p.FirstName,
                    LastName = p.LastName,
                    Gender = p.Gender,
                    Email = p.Email,
                    Phone = p.Phone,
                    AlternatePhone = p.AlternatePhone,
                    Address = p.Address,
                    Occupation = p.Occupation,
                    UserId = p.UserId,
                    IsActive = p.IsActive,
                    ProfileImageId = p.ProfileImageId,
                    ProfileImage = p.ProfileImage != null ? new MediaItemDto
                    {
                        Id = p.ProfileImage.Id,
                        FileName = p.ProfileImage.FileName,
                        FilePath = p.ProfileImage.FilePath,
                        MimeType = p.ProfileImage.MimeType
                    } : null,
                    CreatedAt = p.CreatedAt,
                    UpdatedAt = p.UpdatedAt
                })
                .ToListAsync();

            return (parents, totalCount);
        }

        public async Task<ParentDetailDto?> GetParentByIdAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<Parent>();
            var parent = await repository.GetByIdAsync(id, new[] { "ProfileImage", "Students", "Students.Student", "Students.Student.ProfileImage" });

            if (parent == null) return null;

            return new ParentDetailDto
            {
                Id = parent.Id,
                FirstName = parent.FirstName,
                LastName = parent.LastName,
                Gender = parent.Gender,
                Email = parent.Email,
                Phone = parent.Phone,
                AlternatePhone = parent.AlternatePhone,
                Address = parent.Address,
                Occupation = parent.Occupation,
                UserId = parent.UserId,
                IsActive = parent.IsActive,
                ProfileImageId = parent.ProfileImageId,
                ProfileImage = parent.ProfileImage != null ? new MediaItemDto
                {
                    Id = parent.ProfileImage.Id,
                    FileName = parent.ProfileImage.FileName,
                    FilePath = parent.ProfileImage.FilePath,
                    MimeType = parent.ProfileImage.MimeType
                } : null,
                CreatedAt = parent.CreatedAt,
                UpdatedAt = parent.UpdatedAt,
                Students = parent.Students.Where(s => !s.IsDeleted && !s.Student.IsDeleted).Select(s => new ParentStudentDto
                {
                    Id = s.Id,
                    StudentId = s.StudentId,
                    ParentId = s.ParentId,
                    RelationType = s.RelationType,
                    IsPrimaryContact = s.IsPrimaryContact,
                    Student = new StudentDto
                    {
                        Id = s.Student.Id,
                        StudentId = s.Student.StudentId,
                        FirstName = s.Student.FirstName,
                        LastName = s.Student.LastName,
                        DateOfBirth = s.Student.DateOfBirth,
                        Gender = s.Student.Gender,
                        Email = s.Student.Email,
                        Phone = s.Student.Phone,
                        CurrentGrade = s.Student.CurrentGrade,
                        Section = s.Student.Section,
                        Medium = s.Student.Medium,
                        Shift = s.Student.Shift,
                        RollNumber = s.Student.RollNumber,
                        IsActive = s.Student.IsActive,
                        ProfileImageId = s.Student.ProfileImageId,
                        ProfileImage = s.Student.ProfileImage != null ? new MediaItemDto
                        {
                            Id = s.Student.ProfileImage.Id,
                            FileName = s.Student.ProfileImage.FileName,
                            FilePath = s.Student.ProfileImage.FilePath,
                            MimeType = s.Student.ProfileImage.MimeType
                        } : null
                    }
                }).ToList()
            };
        }

        public async Task<ParentDto?> GetParentByUserIdAsync(string userId)
        {
            var repository = _unitOfWork.Repository<Parent>();
            var parents = await repository.FindAsync(p => p.UserId == userId, new[] { "ProfileImage" });

            if (!parents.Any()) return null;

            var parent = parents.First();
            return new ParentDto
            {
                Id = parent.Id,
                FirstName = parent.FirstName,
                LastName = parent.LastName,
                Gender = parent.Gender,
                Email = parent.Email,
                Phone = parent.Phone,
                AlternatePhone = parent.AlternatePhone,
                Address = parent.Address,
                Occupation = parent.Occupation,
                UserId = parent.UserId,
                IsActive = parent.IsActive,
                ProfileImageId = parent.ProfileImageId,
                ProfileImage = parent.ProfileImage != null ? new MediaItemDto
                {
                    Id = parent.ProfileImage.Id,
                    FileName = parent.ProfileImage.FileName,
                    FilePath = parent.ProfileImage.FilePath,
                    MimeType = parent.ProfileImage.MimeType
                } : null,
                CreatedAt = parent.CreatedAt,
                UpdatedAt = parent.UpdatedAt
            };
        }

        public async Task<Guid> CreateParentAsync(CreateParentDto parentDto)
        {
            var repository = _unitOfWork.Repository<Parent>();

            var parent = new Parent
            {
                FirstName = parentDto.FirstName,
                LastName = parentDto.LastName,
                Gender = parentDto.Gender,
                Email = parentDto.Email,
                Phone = parentDto.Phone,
                AlternatePhone = parentDto.AlternatePhone,
                Address = parentDto.Address,
                Occupation = parentDto.Occupation,
                UserId = parentDto.UserId,
                IsActive = parentDto.IsActive,
                ProfileImageId = parentDto.ProfileImageId,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.AddAsync(parent, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Parent created with ID {ParentId}", parent.Id);
            return parent.Id;
        }

        public async Task<bool> UpdateParentAsync(Guid id, UpdateParentDto parentDto)
        {
            var repository = _unitOfWork.Repository<Parent>();
            var parent = await repository.GetByIdAsync(id);

            if (parent == null) return false;

            // Update properties
            parent.FirstName = parentDto.FirstName;
            parent.LastName = parentDto.LastName;
            parent.Gender = parentDto.Gender;
            parent.Email = parentDto.Email;
            parent.Phone = parentDto.Phone;
            parent.AlternatePhone = parentDto.AlternatePhone;
            parent.Address = parentDto.Address;
            parent.Occupation = parentDto.Occupation;
            parent.IsActive = parentDto.IsActive;
            parent.ProfileImageId = parentDto.ProfileImageId;
            parent.UpdatedAt = DateTime.UtcNow;
            // LastModifiedBy and LastModifiedAt will be set by the repository

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.UpdateAsync(parent, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Parent updated with ID {ParentId}", parent.Id);
            return true;
        }

        public async Task<bool> DeleteParentAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<Parent>();

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Parent deleted with ID {ParentId}", id);
            return true;
        }

        #endregion

        #region Student-Parent Relationship

        public async Task<IEnumerable<ParentStudentDto>> GetParentStudentsAsync(Guid parentId)
        {
            var repository = _unitOfWork.Repository<StudentParent>();
            var studentParents = await repository.FindAsync(
                sp => sp.ParentId == parentId,
                new[] { "Student", "Student.ProfileImage" });

            return studentParents.Where(sp => !sp.IsDeleted && !sp.Student.IsDeleted).Select(sp => new ParentStudentDto
            {
                Id = sp.Id,
                StudentId = sp.StudentId,
                ParentId = sp.ParentId,
                RelationType = sp.RelationType,
                IsPrimaryContact = sp.IsPrimaryContact,
                Student = new StudentDto
                {
                    Id = sp.Student.Id,
                    StudentId = sp.Student.StudentId,
                    FirstName = sp.Student.FirstName,
                    LastName = sp.Student.LastName,
                    DateOfBirth = sp.Student.DateOfBirth,
                    Gender = sp.Student.Gender,
                    Email = sp.Student.Email,
                    Phone = sp.Student.Phone,
                    CurrentGrade = sp.Student.CurrentGrade,
                    Section = sp.Student.Section,
                    Medium = sp.Student.Medium,
                    Shift = sp.Student.Shift,
                    RollNumber = sp.Student.RollNumber,
                    IsActive = sp.Student.IsActive,
                    ProfileImageId = sp.Student.ProfileImageId,
                    ProfileImage = sp.Student.ProfileImage != null ? new MediaItemDto
                    {
                        Id = sp.Student.ProfileImage.Id,
                        FileName = sp.Student.ProfileImage.FileName,
                        FilePath = sp.Student.ProfileImage.FilePath,
                        MimeType = sp.Student.ProfileImage.MimeType
                    } : null
                }
            }).ToList();
        }

        public async Task<bool> AddStudentToParentAsync(Guid parentId, Guid studentId, ParentRelationType relationType, bool isPrimaryContact)
        {
            // Verify parent exists
            var parentRepository = _unitOfWork.Repository<Parent>();
            var parent = await parentRepository.GetByIdAsync(parentId);

            if (parent == null)
            {
                _logger.LogWarning("Failed to add student to parent: Parent with ID {ParentId} not found", parentId);
                return false;
            }

            // Verify student exists
            var studentRepository = _unitOfWork.Repository<Student>();
            var student = await studentRepository.GetByIdAsync(studentId);

            if (student == null)
            {
                _logger.LogWarning("Failed to add student to parent: Student with ID {StudentId} not found", studentId);
                return false;
            }

            // Check if relationship already exists
            var studentParentRepository = _unitOfWork.Repository<StudentParent>();
            var existingRelationships = await studentParentRepository.FindAsync(
                sp => sp.ParentId == parentId && sp.StudentId == studentId && !sp.IsDeleted);

            if (existingRelationships.Any())
            {
                _logger.LogWarning("Relationship between parent {ParentId} and student {StudentId} already exists",
                    parentId, studentId);
                return false;
            }

            // If this is set as primary contact, update any existing primary contacts for this student
            if (isPrimaryContact)
            {
                var existingPrimaryContacts = await studentParentRepository.FindAsync(
                    sp => sp.StudentId == studentId && sp.IsPrimaryContact && !sp.IsDeleted);

                foreach (var contact in existingPrimaryContacts)
                {
                    contact.IsPrimaryContact = false;
                    contact.UpdatedAt = DateTime.UtcNow;
                    // LastModifiedBy and LastModifiedAt will be set by the repository

                    var contactUserId = _currentUserService.UserId?.ToString();
                    await studentParentRepository.UpdateAsync(contact, contactUserId);
                }
            }

            var studentParent = new StudentParent
            {
                StudentId = studentId,
                ParentId = parentId,
                RelationType = relationType,
                IsPrimaryContact = isPrimaryContact,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await studentParentRepository.AddAsync(studentParent, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Student {StudentId} added to parent {ParentId} with relation type {RelationType}",
                studentId, parentId, relationType);
            return true;
        }

        public async Task<bool> UpdateStudentParentRelationshipAsync(Guid parentId, Guid studentId, ParentRelationType relationType, bool isPrimaryContact)
        {
            var studentParentRepository = _unitOfWork.Repository<StudentParent>();
            var relationships = await studentParentRepository.FindAsync(
                sp => sp.ParentId == parentId && sp.StudentId == studentId && !sp.IsDeleted);

            if (!relationships.Any())
            {
                _logger.LogWarning("Relationship between parent {ParentId} and student {StudentId} not found",
                    parentId, studentId);
                return false;
            }

            var relationship = relationships.First();

            // If this is set as primary contact, update any existing primary contacts for this student
            if (isPrimaryContact && !relationship.IsPrimaryContact)
            {
                var existingPrimaryContacts = await studentParentRepository.FindAsync(
                    sp => sp.StudentId == studentId && sp.IsPrimaryContact && sp.Id != relationship.Id && !sp.IsDeleted);

                foreach (var contact in existingPrimaryContacts)
                {
                    contact.IsPrimaryContact = false;
                    contact.UpdatedAt = DateTime.UtcNow;
                    // LastModifiedBy and LastModifiedAt will be set by the repository

                    var contactUserId = _currentUserService.UserId?.ToString();
                    await studentParentRepository.UpdateAsync(contact, contactUserId);
                }
            }

            // Update relationship
            relationship.RelationType = relationType;
            relationship.IsPrimaryContact = isPrimaryContact;
            relationship.UpdatedAt = DateTime.UtcNow;
            // LastModifiedBy and LastModifiedAt will be set by the repository

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await studentParentRepository.UpdateAsync(relationship, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Relationship between parent {ParentId} and student {StudentId} updated",
                parentId, studentId);
            return true;
        }

        public async Task<bool> RemoveStudentFromParentAsync(Guid parentId, Guid studentId)
        {
            var studentParentRepository = _unitOfWork.Repository<StudentParent>();
            var relationships = await studentParentRepository.FindAsync(
                sp => sp.ParentId == parentId && sp.StudentId == studentId && !sp.IsDeleted);

            if (!relationships.Any())
            {
                _logger.LogWarning("Relationship between parent {ParentId} and student {StudentId} not found",
                    parentId, studentId);
                return false;
            }

            var relationship = relationships.First();

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await studentParentRepository.DeleteAsync(relationship, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Student {StudentId} removed from parent {ParentId}", studentId, parentId);
            return true;
        }

        #endregion

        #region Parent Portal Methods

        public async Task<IEnumerable<StudentAttendanceDto>> GetStudentAttendanceAsync(Guid parentId, Guid studentId, string? fromDate = null, string? toDate = null)
        {
            // Verify parent has access to this student
            var studentParentRepository = _unitOfWork.Repository<StudentParent>();
            var hasAccess = await studentParentRepository.ExistsAsync(
                sp => sp.ParentId == parentId && sp.StudentId == studentId && !sp.IsDeleted);

            if (!hasAccess)
            {
                _logger.LogWarning("Parent {ParentId} attempted to access attendance for student {StudentId} without permission",
                    parentId, studentId);
                return Enumerable.Empty<StudentAttendanceDto>();
            }

            // Use the student service to get attendance
            return await _studentService.GetStudentAttendanceAsync(studentId, fromDate, toDate);
        }

        public async Task<IEnumerable<StudentFeeDto>> GetStudentFeesAsync(Guid parentId, Guid studentId, int? academicYear = null)
        {
            // Verify parent has access to this student
            var studentParentRepository = _unitOfWork.Repository<StudentParent>();
            var hasAccess = await studentParentRepository.ExistsAsync(
                sp => sp.ParentId == parentId && sp.StudentId == studentId && !sp.IsDeleted);

            if (!hasAccess)
            {
                _logger.LogWarning("Parent {ParentId} attempted to access fees for student {StudentId} without permission",
                    parentId, studentId);
                return Enumerable.Empty<StudentFeeDto>();
            }

            // Use the student service to get fees
            return await _studentService.GetStudentFeesAsync(studentId, academicYear);
        }

        public async Task<IEnumerable<StudentResultDto>> GetStudentResultsAsync(Guid parentId, Guid studentId, string? academicYear = null, string? examType = null)
        {
            // Verify parent has access to this student
            var studentParentRepository = _unitOfWork.Repository<StudentParent>();
            var hasAccess = await studentParentRepository.ExistsAsync(
                sp => sp.ParentId == parentId && sp.StudentId == studentId && !sp.IsDeleted);

            if (!hasAccess)
            {
                _logger.LogWarning("Parent {ParentId} attempted to access results for student {StudentId} without permission",
                    parentId, studentId);
                return Enumerable.Empty<StudentResultDto>();
            }

            // Use the student service to get results
            return await _studentService.GetStudentResultsAsync(studentId, academicYear, examType);
        }

        public async Task<IEnumerable<StudentLeaveDto>> GetStudentLeavesAsync(Guid parentId, Guid studentId, string? fromDate = null, string? toDate = null, LeaveStatus? status = null)
        {
            // Verify parent has access to this student
            var studentParentRepository = _unitOfWork.Repository<StudentParent>();
            var hasAccess = await studentParentRepository.ExistsAsync(
                sp => sp.ParentId == parentId && sp.StudentId == studentId && !sp.IsDeleted);

            if (!hasAccess)
            {
                _logger.LogWarning("Parent {ParentId} attempted to access leaves for student {StudentId} without permission",
                    parentId, studentId);
                return Enumerable.Empty<StudentLeaveDto>();
            }

            var studentLeaveRepository = _unitOfWork.Repository<StudentLeave>();
            var query = studentLeaveRepository.AsQueryable();

            query = query.Where(l => l.StudentId == studentId);

            if (!string.IsNullOrEmpty(fromDate) && DateTime.TryParse(fromDate, out var fromDateValue))
            {
                query = query.Where(l => l.StartDate >= fromDateValue);
            }

            if (!string.IsNullOrEmpty(toDate) && DateTime.TryParse(toDate, out var toDateValue))
            {
                query = query.Where(l => l.EndDate <= toDateValue);
            }

            if (status.HasValue)
            {
                query = query.Where(l => l.Status == status.Value);
            }

            var leaves = await query
                .OrderByDescending(l => l.StartDate)
                .Select(l => new StudentLeaveDto
                {
                    Id = l.Id,
                    StudentId = l.StudentId,
                    StartDate = l.StartDate,
                    EndDate = l.EndDate,
                    Type = l.Type,
                    Reason = l.Reason,
                    Status = l.Status,
                    AttachmentPath = l.AttachmentPath,
                    CreatedAt = l.CreatedAt,
                    UpdatedAt = l.UpdatedAt
                })
                .ToListAsync();

            return leaves;
        }

        public async Task<Guid> CreateStudentLeaveAsync(Guid parentId, CreateStudentLeaveDto leaveDto)
        {
            // Verify parent has access to this student
            var studentParentRepository = _unitOfWork.Repository<StudentParent>();
            var hasAccess = await studentParentRepository.ExistsAsync(
                sp => sp.ParentId == parentId && sp.StudentId == leaveDto.StudentId && !sp.IsDeleted);

            if (!hasAccess)
            {
                _logger.LogWarning("Parent {ParentId} attempted to create leave for student {StudentId} without permission",
                    parentId, leaveDto.StudentId);
                throw new UnauthorizedAccessException("Parent does not have access to this student");
            }

            // Check for overlapping leaves
            var studentLeaveRepository = _unitOfWork.Repository<StudentLeave>();
            var overlappingLeaves = await studentLeaveRepository.FindAsync(l =>
                l.StudentId == leaveDto.StudentId &&
                !l.IsDeleted &&
                ((l.StartDate <= leaveDto.StartDate && l.EndDate >= leaveDto.StartDate) ||
                 (l.StartDate <= leaveDto.EndDate && l.EndDate >= leaveDto.EndDate) ||
                 (l.StartDate >= leaveDto.StartDate && l.EndDate <= leaveDto.EndDate)));

            if (overlappingLeaves.Any())
            {
                _logger.LogWarning("Overlapping leave application detected for student {StudentId}", leaveDto.StudentId);
                throw new InvalidOperationException("There is already a leave application for this period");
            }

            var leave = new StudentLeave
            {
                StudentId = leaveDto.StudentId,
                StartDate = leaveDto.StartDate,
                EndDate = leaveDto.EndDate,
                Type = leaveDto.Type,
                Reason = leaveDto.Reason,
                Status = LeaveStatus.Pending, // Always start as pending
                AttachmentPath = leaveDto.AttachmentPath,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await studentLeaveRepository.AddAsync(leave, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Leave created with ID {LeaveId} for student {StudentId} by parent {ParentId}",
                leave.Id, leaveDto.StudentId, parentId);
            return leave.Id;
        }

        public Task<bool> AddStudentAsync(Guid parentId, Guid studentId, ParentRelationType relationType, bool isPrimaryContact)
        {
            throw new NotImplementedException();
        }

        public Task<bool> UpdateStudentRelationAsync(Guid parentId, Guid studentId, ParentRelationType relationType, bool isPrimaryContact)
        {
            throw new NotImplementedException();
        }

        public Task<bool> RemoveStudentAsync(Guid parentId, Guid studentId)
        {
            throw new NotImplementedException();
        }

        #endregion
    }
}
