import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { EnhancedHeroComponent, HeroButton } from '../enhanced-hero/enhanced-hero.component';
import { HeroConfigService } from '../../../core/services/hero-config.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-default-hero',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    EnhancedHeroComponent
  ],
  template: `
    <app-enhanced-hero
      [title]="title | translate"
      [subtitle]="subtitle | translate"
      [description]="description | translate"
      [backgroundImage]="backgroundImage"
      [overlayImage]="overlayImage"
      [breadcrumbs]="breadcrumbs"
      [buttons]="buttons"
      [theme]="theme"
      [size]="size"
      [alignment]="alignment">
    </app-enhanced-hero>
  `,
  styles: []
})
export class DefaultHeroComponent implements OnInit {
  @Input() translationPrefix: string = '';
  @Input() title: string = '';
  @Input() subtitle: string = '';
  @Input() description: string = '';
  @Input() backgroundImage: string = '';
  @Input() overlayImage: string = '';
  @Input() breadcrumbs: string[] = [];
  @Input() buttons: HeroButton[] = [];
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() size: 'small' | 'medium' | 'large' = 'large';
  @Input() alignment: 'left' | 'center' | 'right' = 'center';

  constructor(
    private heroConfigService: HeroConfigService,
    private router: Router
  ) { }

  ngOnInit(): void {
    if (this.translationPrefix) {
      // Get the current route path
      const currentRoute = this.router.url.split('?')[0].split('#')[0];
      const routePath = currentRoute.startsWith('/') ? currentRoute.substring(1) : currentRoute;
      
      // Get the hero configuration for the current route
      const config = this.heroConfigService.getHeroConfig(this.translationPrefix);
      
      // Apply the configuration if inputs are not provided
      this.title = this.title || config.title;
      this.subtitle = this.subtitle || config.subtitle;
      this.description = this.description || config.description;
      this.backgroundImage = this.backgroundImage || config.backgroundImage;
      this.overlayImage = this.overlayImage || config.overlayImage;
      this.breadcrumbs = this.breadcrumbs.length > 0 ? this.breadcrumbs : config.breadcrumbs;
      this.buttons = this.buttons.length > 0 ? this.buttons : config.buttons;
      this.theme = this.theme || config.theme;
      this.size = this.size || config.size;
      this.alignment = this.alignment || config.alignment;
    }
  }
}
