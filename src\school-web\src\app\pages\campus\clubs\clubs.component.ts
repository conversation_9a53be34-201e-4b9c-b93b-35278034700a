import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { TranslateModule } from '@ngx-translate/core';
import { ClubService } from '../../../core/services/club.service';
import { Club, ClubFilter } from '../../../core/models/club.model';
import { ClubFilterPipe } from '../../../shared/pipes/club-filter.pipe';
import { DefaultHeroComponent } from '../../../shared/components/default-hero/default-hero.component';

// Local interface for club categories with clubs
interface ClubCategory {
  name: string;
  clubs: Club[];
}

@Component({
  selector: 'app-clubs',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatExpansionModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    TranslateModule,
    ClubFilterPipe,
    DefaultHeroComponent
  ],
  templateUrl: './clubs.component.html',
  styleUrls: ['./clubs.component.scss']
})
export class ClubsComponent implements OnInit {
  // Club categories
  clubCategories: string[] = [];

  // Clubs organized by category
  clubsByCategory: ClubCategory[] = [];

  // All clubs
  clubs: Club[] = [];

  // Loading and error states
  loading = true;
  error = false;

  // Club formation process
  formationSteps = [
    {
      title: 'Find a Faculty Advisor',
      description: 'Identify a teacher or staff member who is willing to supervise the club and attend meetings.'
    },
    {
      title: 'Develop a Club Proposal',
      description: 'Create a detailed proposal including the club\'s purpose, potential activities, meeting frequency, and membership requirements.'
    },
    {
      title: 'Submit Application',
      description: 'Complete the official club application form and submit it to the Student Activities Office along with your proposal.'
    },
    {
      title: 'Present to Student Council',
      description: 'Present your club idea to the Student Council, who will review and vote on new club approvals.'
    },
    {
      title: 'Administrative Approval',
      description: 'Once approved by Student Council, the proposal goes to administration for final approval.'
    },
    {
      title: 'Club Launch',
      description: 'After approval, you can begin recruiting members, scheduling meetings, and planning activities.'
    }
  ];

  // Club leadership opportunities
  leadershipRoles = [
    {
      title: 'Club President',
      responsibilities: [
        'Oversee all club activities and meetings',
        'Develop meeting agendas',
        'Serve as the primary liaison with the faculty advisor',
        'Represent the club at school events and meetings'
      ]
    },
    {
      title: 'Vice President',
      responsibilities: [
        'Assist the president with club management',
        'Lead meetings in the president\'s absence',
        'Coordinate special projects and initiatives',
        'Help resolve conflicts within the club'
      ]
    },
    {
      title: 'Secretary',
      responsibilities: [
        'Take and distribute meeting minutes',
        'Maintain club records and membership lists',
        'Handle club correspondence',
        'Track attendance at meetings and events'
      ]
    },
    {
      title: 'Treasurer',
      responsibilities: [
        'Manage club finances and budget',
        'Track expenses and fundraising',
        'Process reimbursement requests',
        'Provide regular financial reports'
      ]
    },
    {
      title: 'Events Coordinator',
      responsibilities: [
        'Plan and organize club events',
        'Coordinate logistics for activities',
        'Secure necessary permissions and resources',
        'Promote events to the school community'
      ]
    }
  ];

  constructor(
    private clubService: ClubService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadClubCategories();
    this.loadClubs();
  }

  /**
   * Load club categories from the API
   */
  loadClubCategories(): void {
    this.clubService.getClubCategories().subscribe({
      next: (categories) => {
        this.clubCategories = categories;
      },
      error: (error) => {
        console.error('Error loading club categories:', error);
        // Use default categories if API fails
        this.clubCategories = [
          'Academic',
          'Arts & Culture',
          'Community Service',
          'Leadership',
          'STEM',
          'Special Interest'
        ];
      }
    });
  }

  /**
   * Load clubs from the API
   */
  loadClubs(): void {
    this.loading = true;
    this.error = false;

    const filter: ClubFilter = {
      isActive: true,
      pageSize: 100 // Get a large number of clubs
    };

    this.clubService.getClubs(filter).subscribe({
      next: (result) => {
        this.clubs = result.items;
        this.organizeClubsByCategory();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading clubs:', error);
        this.error = true;
        this.loading = false;
        this.snackBar.open('Failed to load clubs. Please try again later.', 'Close', {
          duration: 5000
        });

        // Use mock data for development/demo purposes
        this.useMockClubs();
      }
    });
  }

  /**
   * Organize clubs by category
   */
  organizeClubsByCategory(): void {
    this.clubsByCategory = [];

    // Create a category entry for each category
    this.clubCategories.forEach(category => {
      this.clubsByCategory.push({
        name: category,
        clubs: this.clubs.filter(club => club.category === category)
      });
    });
  }

  /**
   * Use mock clubs data for development/demo purposes
   */
  useMockClubs(): void {
    // Mock clubs data (same as before)
    this.clubs = [
    {
      id: 1,
      name: 'Debate Club',
      description: 'Students develop public speaking, critical thinking, and argumentation skills through competitive debate tournaments and weekly practice sessions.',
      meetingTime: 'Tuesdays, 3:30-5:00 PM',
      advisor: 'Ms. Johnson',
      image: 'assets/images/campus/clubs/debate.jpg',
      category: 'Academic'
    },
    {
      id: 2,
      name: 'Math Team',
      description: 'Students practice advanced mathematical concepts and problem-solving strategies to prepare for regional and national mathematics competitions.',
      meetingTime: 'Mondays, 3:30-4:30 PM',
      advisor: 'Mr. Chen',
      image: 'assets/images/campus/clubs/math.jpg',
      category: 'Academic'
    },
    {
      id: 3,
      name: 'Model United Nations',
      description: 'Students simulate United Nations committees, representing different countries and debating global issues while developing research, public speaking, and diplomatic skills.',
      meetingTime: 'Thursdays, 3:30-5:00 PM',
      advisor: 'Ms. Garcia',
      image: 'assets/images/campus/clubs/model-un.jpg',
      category: 'Academic'
    },
    {
      id: 4,
      name: 'Drama Club',
      description: 'Students explore various aspects of theater arts, including acting, directing, set design, and costume creation, culminating in two major productions each year.',
      meetingTime: 'Wednesdays, 3:30-5:30 PM',
      advisor: 'Mr. Williams',
      image: 'assets/images/campus/clubs/drama.jpg',
      category: 'Arts & Culture'
    },
    {
      id: 5,
      name: 'Art Club',
      description: 'Students explore various artistic media and techniques, create collaborative and individual projects, and organize exhibitions of student artwork.',
      meetingTime: 'Fridays, 3:30-5:00 PM',
      advisor: 'Ms. Lee',
      image: 'assets/images/campus/clubs/art.jpg',
      category: 'Arts & Culture'
    },
    {
      id: 6,
      name: 'Music Ensemble',
      description: 'Students with musical interests collaborate to create and perform music across various genres, developing both technical skills and creative expression.',
      meetingTime: 'Mondays & Wednesdays, 3:30-4:30 PM',
      advisor: 'Mr. Rodriguez',
      image: 'assets/images/campus/clubs/music.jpg',
      category: 'Arts & Culture'
    },
    {
      id: 7,
      name: 'Community Outreach',
      description: 'Students organize and participate in service projects that address community needs, from food drives and environmental cleanups to tutoring and senior citizen support.',
      meetingTime: 'Tuesdays, 3:30-4:30 PM',
      advisor: 'Ms. Thompson',
      image: 'assets/images/campus/clubs/community.jpg',
      category: 'Community Service'
    },
    {
      id: 8,
      name: 'Environmental Club',
      description: 'Students promote environmental awareness and sustainability through campus initiatives, community education, and conservation projects.',
      meetingTime: 'Thursdays, 3:30-4:30 PM',
      advisor: 'Mr. Patel',
      image: 'assets/images/campus/clubs/environmental.jpg',
      category: 'Community Service'
    },
    {
      id: 9,
      name: 'Student Council',
      description: 'Elected student representatives plan school events, address student concerns, and serve as liaisons between the student body and administration.',
      meetingTime: 'Mondays, 12:00-12:45 PM',
      advisor: 'Ms. Wilson',
      image: 'assets/images/campus/clubs/student-council.jpg',
      category: 'Leadership'
    },
    {
      id: 10,
      name: 'Peer Mentoring',
      description: 'Upperclassmen provide academic and social support to younger students, helping them navigate school life and develop important skills.',
      meetingTime: 'Wednesdays, 12:00-12:45 PM',
      advisor: 'Mr. Jackson',
      image: 'assets/images/campus/clubs/peer-mentoring.jpg',
      category: 'Leadership'
    },
    {
      id: 11,
      name: 'Robotics Team',
      description: 'Students design, build, and program robots for competitions, developing skills in engineering, programming, teamwork, and problem-solving.',
      meetingTime: 'Tuesdays & Thursdays, 3:30-5:30 PM',
      advisor: 'Ms. Martinez',
      image: 'assets/images/campus/clubs/robotics.jpg',
      category: 'STEM'
    },
    {
      id: 12,
      name: 'Coding Club',
      description: 'Students learn programming languages, develop applications, and work on coding projects that solve real-world problems.',
      meetingTime: 'Wednesdays, 3:30-5:00 PM',
      advisor: 'Mr. Kim',
      image: 'assets/images/campus/clubs/coding.jpg',
      category: 'STEM'
    },
    {
      id: 13,
      name: 'Science Olympiad',
      description: 'Students prepare for Science Olympiad competitions by studying various scientific disciplines and practicing hands-on laboratory skills.',
      meetingTime: 'Fridays, 3:30-5:00 PM',
      advisor: 'Dr. Singh',
      image: 'assets/images/campus/clubs/science.jpg',
      category: 'STEM'
    },
    {
      id: 14,
      name: 'Chess Club',
      description: 'Students of all skill levels learn and practice chess strategies, participate in tournaments, and develop critical thinking and concentration skills.',
      meetingTime: 'Mondays, 3:30-4:30 PM',
      advisor: 'Mr. Brown',
      image: 'assets/images/campus/clubs/chess.jpg',
      category: 'Special Interest'
    },
    {
      id: 15,
      name: 'Photography Club',
      description: 'Students explore digital and film photography techniques, participate in photo walks, critique each other\'s work, and organize exhibitions.',
      meetingTime: 'Thursdays, 3:30-4:30 PM',
      advisor: 'Ms. Davis',
      image: 'assets/images/campus/clubs/photography.jpg',
      category: 'Special Interest'
    },
    {
      id: 16,
      name: 'Culinary Club',
      description: 'Students learn cooking techniques, explore cuisines from around the world, and develop skills in food preparation, nutrition, and presentation.',
      meetingTime: 'Fridays, 3:30-5:00 PM',
      advisor: 'Chef Anderson',
      image: 'assets/images/campus/clubs/culinary.jpg',
      category: 'Special Interest'
    }
  ];
  }
}
