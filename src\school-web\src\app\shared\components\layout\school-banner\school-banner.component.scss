@use "sass:color";

@use '../../../../../styles/_variables' as *; // Import variables from _variables.scss

.school-banner {
  background: transparent; // Changed to transparent to show background image
  padding: 10px 0 10px 0; // Reduced padding to make banner smaller in height
  box-shadow: none; // Remove shadow as navbar will be directly below
  border-bottom: none; // Remove border as it creates visual separation
  z-index: 1001;
  position: relative;
  transition: all 0.3s ease;
  display: block; // Ensure it's visible by default
  margin-bottom: 0; // Ensure no bottom margin
  overflow: hidden; // Ensure background stays within bounds

  // Background image container
  .banner-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-image: url('/assets/images/school-pattern.jpg'); // School-themed pattern background
    background-size: cover;
    background-position: center;
    z-index: -2;

    // Overlay with gradient
    .banner-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
      z-index: -1;
    }
  }
}

.banner-content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 40px; // Increased padding for better spacing
  box-sizing: border-box;
  position: relative; // Ensure content is above background
  z-index: 1; // Higher than background
  justify-content: space-between; // Space elements evenly

  // Mobile menu toggle button
  .mobile-menu-toggle {
    margin-right: 16px;
    color: $primary-color;
  }

  .school-logo-container {
    margin-right: 0;
    margin-left: 0;
    flex: 0 0 12%; // Increased to 12% width
    display: flex;
    align-items: center;
    justify-content: flex-start;
    max-width: 12%; // Ensure it doesn't exceed 12%

    .logo-link {
      display: flex;
      align-items: center;
      text-decoration: none;
    }

    .school-logo-img {
      width: auto;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
      transition: transform 0.3s ease;
      margin: 0; // Removed margin to reduce height

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .school-info {
    flex: 0 0 63%; // Reduced to 63% width to accommodate larger logo and contact section
    padding: 0 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0; // Prevent flex items from overflowing
    max-width: 63%; // Ensure it doesn't exceed 63%

    .school-name-container {
      position: relative;
      width: 100%;

      .title-container {
        position: relative;
        width: 100%;
        margin-bottom: 8px;
        display: flex;
        flex-direction: column;

        .school-title {
          font-size: 45px; // Increased from 36px to 45px
          font-weight: 800;
          margin: 0;
          letter-spacing: 2px; // Increased for better readability with larger font
          text-transform: uppercase;
          position: relative;
          display: block;
          width: 100%;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
          transition: all 0.3s ease;

          // Modern SCSS approach using CSS custom properties
          --title-color-dark: #{color.adjust($primary-color, $saturation: +20%, $lightness: -5%)};
          --title-color-main: #{color.adjust($primary-color, $saturation: +15%, $lightness: 0%)};
          --title-color-light: #{color.adjust($primary-color, $saturation: +10%, $lightness: +10%)};

          // More vibrant gradient with better color stops
          background-image: linear-gradient(
            135deg,
            var(--title-color-dark) 0%,
            var(--title-color-main) 50%,
            var(--title-color-light) 100%
          );
          color: transparent;
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;

          &:hover {
            transform: translateY(-2px);
            text-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);

            // More saturated colors on hover
            --title-color-dark: #{color.adjust($primary-color, $saturation: +30%, $lightness: -8%)};
            --title-color-main: #{color.adjust($primary-color, $saturation: +25%, $lightness: 0%)};
            --title-color-light: #{color.adjust($primary-color, $saturation: +20%, $lightness: +8%)};
          }
        }

        .title-decoration {
          height: 3px; // Reduced from 4px to 3px
          width: 100%;
          margin-top: 2px; // Further reduced for larger font size
          border-radius: 2px;
          position: relative;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          // Use the same custom properties for consistent colors
          background: linear-gradient(
            to right,
            var(--title-color-dark),
            var(--title-color-main),
            var(--title-color-light)
          );

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8), transparent);
            animation: shimmer 2.5s infinite;
          }
        }
      }

      .school-tagline-wrapper {
        margin-top: 2px; // Reduced from 4px to 2px
        width: 100%;

        .school-tagline {
          font-size: 14px; // Reduced from 18px to 14px
          margin: 0;
          color: $secondary-color;
          font-style: italic;
          letter-spacing: 0.8px;
          position: relative;
          display: block;
          width: 100%;
          transition: all 0.3s ease;
          text-align: right;
          padding-right: 20px;
          text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
          font-weight: 500;

          &:hover {
            color: adjust($secondary-color, 10%);
            transform: translateX(-5px);
          }
        }
      }
    }
  }

  // Contact information container
  .school-contact {
    display: flex;
    flex-direction: column;
    align-items: flex-start; // Changed to flex-start for better text alignment
    margin-left: 0;
    margin-right: 0;
    flex: 0 0 25%; // Increased to 25% width for better text display
    max-width: 25%; // Ensure it doesn't exceed 25%
    background: rgba(255, 255, 255, 0.5);
    padding: 8px; // Reduced from 15px to 8px
    border-radius: 6px; // Reduced from 8px to 6px
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    overflow: hidden; // Prevent overflow

    .contact-title {
      font-size: 16px; // Reduced from 18px to 16px
      font-weight: 600;
      color: $primary-color;
      margin: 0 0 6px 0; // Reduced from 12px to 6px
      padding-bottom: 4px; // Reduced from 8px to 4px
      border-bottom: 2px solid rgba($primary-color, 0.3);
      width: 100%;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.7);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .contact-item {
      display: flex;
      align-items: center;
      margin-bottom: 6px; // Reduced from 12px to 6px
      transition: all 0.2s ease;
      padding: 4px; // Reduced from 8px to 4px
      border-radius: 4px;
      background-color: rgba(255, 255, 255, 0.3);
      width: 100%;
      box-sizing: border-box;
      overflow: hidden;

      &:hover {
        transform: translateX(5px);
        background-color: rgba(255, 255, 255, 0.5);
      }

      &:last-child {
        margin-bottom: 0;
      }

      mat-icon {
        font-size: 16px; // Reduced from 20px to 16px
        height: 16px; // Reduced from 20px to 16px
        width: 16px; // Reduced from 20px to 16px
        margin-right: 8px; // Reduced from 10px to 8px
        color: $primary-color;
        flex-shrink: 0;
      }

      span {
        font-size: 14px;
        color: $text-color;
        white-space: normal; // Changed from nowrap to normal
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.4;
      }
    }

    .social-icons {
      display: flex;
      margin-top: 8px; // Reduced from 15px to 8px
      background-color: rgba(255, 255, 255, 0.3);
      padding: 4px; // Reduced from 8px to 4px
      border-radius: 30px;
      justify-content: flex-start; // Changed to flex-start for better alignment
      width: 100%;
      box-sizing: border-box;

      a {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px; // Reduced from 36px to 28px
        height: 28px; // Reduced from 36px to 28px
        border-radius: 50%;
        background-color: rgba($primary-color, 0.1);
        margin-left: 12px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

        &:first-child {
          margin-left: 0;
        }

        &:hover {
          background-color: $primary-color;
          transform: translateY(-3px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

          mat-icon {
            color: white;
          }
        }

        mat-icon {
          font-size: 20px;
          height: 20px;
          width: 20px;
          color: $primary-color;
          transition: color 0.3s ease;
        }
      }
    }
  }

  // Mobile actions container
  .mobile-actions {
    display: flex;
    align-items: center;
    margin-left: auto;

    .mobile-login-button {
      margin-right: 8px;
      color: $primary-color;
    }
  }
}

// Responsive styles
@media (max-width: 1200px) {
  .school-banner {
    .banner-content {
      .school-logo-container {
        .school-logo-img {
          height: 100px; // Increased from 80px to 100px
        }
      }

      .school-info {
        .school-name-container {
          .title-container {
            .school-title {
              font-size: 40px; // Adjusted for 45px base size
              // Maintain the same custom properties for consistent colors
              --title-color-dark: #{color.adjust($primary-color, $saturation: +20%, $lightness: -5%)};
              --title-color-main: #{color.adjust($primary-color, $saturation: +15%, $lightness: 0%)};
              --title-color-light: #{color.adjust($primary-color, $saturation: +10%, $lightness: +10%)};
            }
          }

          .school-tagline-wrapper {
            .school-tagline {
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 992px) {
  .school-banner {
    padding: 15px 0 15px 0; // Adjusted padding for medium screens

    .banner-content {
      padding: 0 20px;
      .school-logo-container {
        .school-logo-img {
          height: 80px; // Increased from 60px to 80px
        }
      }

      .school-info {
        padding: 0 16px;

        .school-name-container {
          .title-container {
            .school-title {
              font-size: 35px; // Adjusted for 45px base size
              // Slightly more saturated colors for better visibility at smaller size
              --title-color-dark: #{color.adjust($primary-color, $saturation: +25%, $lightness: -5%)};
              --title-color-main: #{color.adjust($primary-color, $saturation: +20%, $lightness: 0%)};
              --title-color-light: #{color.adjust($primary-color, $saturation: +15%, $lightness: +10%)};
            }

            .title-decoration {
              height: 3px;
            }
          }

          .school-tagline-wrapper {
            .school-tagline {
              font-size: 14px;
              padding-right: 10px;
            }
          }
        }
      }

      .school-logo-container {
        flex: 0 0 10%;
        max-width: 10%;
      }

      .school-contact {
        flex: 0 0 28%;
        max-width: 28%;
        padding: 12px;

        .contact-title {
          font-size: 16px;
          margin-bottom: 10px;
        }

        .contact-item {
          span {
            font-size: 13px;
          }
        }
      }

      .school-info {
        flex: 0 0 62%;
        max-width: 62%;
      }
    }
  }
}

@media (max-width: 768px) {
  .school-banner {
    padding: 15px 0 15px 0; // Adjusted padding for small screens

    .banner-content {
      flex-direction: column;
      justify-content: center;

      .school-logo-container {
        flex: 0 0 100%;
        max-width: 100%;
        justify-content: center;
        margin-bottom: 10px;

        .school-logo-img {
          height: 70px; // Increased from 50px to 70px
        }
      }

      .school-info {
        padding: 0 12px;
        flex: 0 0 80%;
        max-width: 80%;
        margin: 10px 0;

        .school-name-container {
          .title-container {
            .school-title {
              font-size: 28px; // Adjusted for 45px base size
              letter-spacing: 1px;
              text-align: center;
              // Even more saturated colors for better visibility at small size
              --title-color-dark: #{color.adjust($primary-color, $saturation: +30%, $lightness: -3%)};
              --title-color-main: #{color.adjust($primary-color, $saturation: +25%, $lightness: +2%)};
              --title-color-light: #{color.adjust($primary-color, $saturation: +20%, $lightness: +12%)};
            }

            .title-decoration {
              height: 3px;
              margin-top: 6px;
            }
          }

          .school-tagline-wrapper {
            margin-top: 4px;

            .school-tagline {
              font-size: 12px;
              padding-right: 5px;
              text-align: center;
            }
          }
        }
      }

      .school-contact {
        flex: 0 0 85%;
        max-width: 85%;
        padding: 10px;
        align-items: center;
        margin-top: 10px;

        .contact-title {
          font-size: 15px;
          margin-bottom: 8px;
          text-align: center;
        }

        .contact-item {
          margin-bottom: 5px;

          &:hover {
            transform: none;
          }

          span {
            font-size: 12px;
          }
        }

        .social-icons {
          margin-top: 8px;

          a {
            width: 28px;
            height: 28px;

            mat-icon {
              font-size: 16px;
              height: 16px;
              width: 16px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .school-banner {
    padding: 12px 0; // Adjusted padding for very small screens

    .banner-content {
      .school-logo-container {
        .school-logo-img {
          height: 60px; // Increased from 40px to 60px
        }
      }

      .school-info {
        padding: 0 8px;
        flex: 0 0 90%;
        max-width: 90%;

        .school-name-container {
          .title-container {
            .school-title {
              font-size: 22px; // Adjusted for 45px base size
              letter-spacing: 0.5px;
              // Maximum saturation for very small screens
              --title-color-dark: #{color.adjust($primary-color, $saturation: +35%, $lightness: 0%)};
              --title-color-main: #{color.adjust($primary-color, $saturation: +30%, $lightness: +5%)};
              --title-color-light: #{color.adjust($primary-color, $saturation: +25%, $lightness: +15%)};
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); // Enhanced shadow for better visibility
            }

            .title-decoration {
              height: 2px;
              margin-top: 4px;
            }
          }

          .school-tagline-wrapper {
            margin-top: 2px;

            .school-tagline {
              font-size: 10px;
              padding-right: 0;
            }
          }
        }
      }

      .school-contact {
        flex: 0 0 90%;
        max-width: 90%;
      }
    }
  }
}

// Shimmer animation for title decoration
@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}