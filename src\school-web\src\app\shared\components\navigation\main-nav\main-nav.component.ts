import { <PERSON>mponent, On<PERSON>nit, ViewChild, ElementRef, HostListener, Renderer2, AfterViewInit, Inject } from '@angular/core';
import { CommonModule, DOCUMENT } from '@angular/common';
import { Router, RouterModule, NavigationEnd } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule } from '@ngx-translate/core';
import { filter } from 'rxjs/operators';
import { AuthService } from '../../../../core/services/auth.service';
import { AboutMenuComponent } from '../about-menu/about-menu.component';
import { AcademicsMenuComponent } from '../academics-menu/academics-menu.component';
import { AdmissionsMenuComponent } from '../admissions-menu/admissions-menu.component';
import { CampusMenuComponent } from '../campus-menu/campus-menu.component';
import { NewsEventsMenuComponent } from '../news-events-menu/news-events-menu.component';
import { PortalsMenuComponent } from '../portals-menu/portals-menu.component';
import { LanguageSelectorComponent } from '../language-selector/language-selector.component';
import { QuickLinksComponent } from '../quick-links/quick-links.component';

@Component({
  selector: 'app-main-nav',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatSidenavModule,
    MatListModule,
    MatExpansionModule,
    TranslateModule,
    AboutMenuComponent,
    AcademicsMenuComponent,
    AdmissionsMenuComponent,
    CampusMenuComponent,
    NewsEventsMenuComponent,
    PortalsMenuComponent,
    LanguageSelectorComponent,
    QuickLinksComponent
  ],
  templateUrl: './main-nav.component.html',
  styleUrls: ['./main-nav.component.scss']
})
export class MainNavComponent implements OnInit, AfterViewInit {
  activeRoute: string = '';
  isFixed: boolean = false;
  scrollThreshold: number = 250; // Scroll threshold in pixels (school banner height + margin + some extra)
  isMobileMenuOpen: boolean = false;
  isMobile: boolean = false;
  isVerySmallScreen: boolean = false;

  @ViewChild('navbar') navbar!: ElementRef;
  @ViewChild('drawer') drawer: any;

  isLoggedIn = false;
  isAdmin = false;

  constructor(
    private router: Router,
    private renderer: Renderer2,
    public authService: AuthService,
    @Inject(DOCUMENT) private document: Document
  ) {}

  ngOnInit(): void {
    try {
      // Set initial active route
      this.activeRoute = this.router.url;

      // Update active route on navigation
      this.router.events.pipe(
        filter(event => event instanceof NavigationEnd)
      ).subscribe((event: any) => {
        this.activeRoute = event.url;
        // Close drawer when navigating on mobile
        if (this.isMobile && this.drawer) {
          this.drawer.close();
        }
      });

      // Check initial screen size (without DOM manipulation)
      this.isMobile = window.innerWidth < 992;
      this.isVerySmallScreen = window.innerWidth < 480;

      // Check authentication status
      this.isLoggedIn = this.authService.isLoggedIn();
      this.isAdmin = this.authService.isAdmin();

      // Subscribe to auth changes
      this.authService.currentUser$.subscribe(user => {
        this.isLoggedIn = !!user;
        this.isAdmin = this.authService.isAdmin();
      });
    } catch (error) {
      console.error('Error in main-nav component initialization:', error);
    }
  }

  ngAfterViewInit(): void {
    try {
      // Now that the view is initialized, we can safely access the navbar element
      // Apply mobile toolbar class if on mobile
      if (this.isMobile && this.navbar?.nativeElement) {
        this.renderer.addClass(this.navbar.nativeElement, 'mobile-toolbar');
      }

      // Check initial scroll position
      this.checkScroll();
    } catch (error) {
      console.error('Error in ngAfterViewInit:', error);
    }
  }

  @HostListener('window:scroll', [])
  checkScroll(): void {
    // Skip for mobile as the nav is always fixed
    if (this.isMobile) {
      return;
    }

    // Safety check - if navbar is not initialized or doesn't have nativeElement, exit
    if (!this.navbar?.nativeElement) {
      console.log('Navbar not initialized yet, skipping scroll check');
      return;
    }

    const scrollPosition = window.pageYOffset || this.document.documentElement.scrollTop || this.document.body.scrollTop || 0;
    const bannerElement = this.document.querySelector('app-school-banner') as HTMLElement;

    // Calculate threshold based on banner height (no margin)
    const bannerHeight = bannerElement ? bannerElement.offsetHeight : this.scrollThreshold;

    try {
      if (scrollPosition > bannerHeight && !this.isFixed) {
        this.isFixed = true;
        this.renderer.addClass(this.navbar.nativeElement, 'fixed-nav');

        // Check if document.body exists before adding class
        if (this.document?.body) {
          this.renderer.addClass(this.document.body, 'has-fixed-nav');
        }
      } else if (scrollPosition <= bannerHeight && this.isFixed) {
        this.isFixed = false;
        this.renderer.removeClass(this.navbar.nativeElement, 'fixed-nav');

        // Check if document.body exists before removing class
        if (this.document?.body) {
          this.renderer.removeClass(this.document.body, 'has-fixed-nav');
        }
      }
    } catch (error) {
      console.error('Error in checkScroll:', error);
    }
  }

  @HostListener('window:resize', [])
  checkScreenSize(): void {
    const wasMobile = this.isMobile;
    const wasVerySmall = this.isVerySmallScreen;

    this.isMobile = window.innerWidth < 992; // Breakpoint for mobile view
    this.isVerySmallScreen = window.innerWidth < 480; // Breakpoint for very small screens

    // If transitioning from mobile to desktop, ensure drawer is closed
    if (wasMobile && !this.isMobile) {
      this.handleMobileToDesktopTransition();
    }

    try {
      // Only apply DOM changes if navbar is initialized and has nativeElement
      if (this.navbar?.nativeElement) {
        // If transitioning from desktop to mobile, ensure nav is fixed
        if (!wasMobile && this.isMobile) {
          this.isFixed = true;
          this.renderer.addClass(this.navbar.nativeElement, 'mobile-toolbar');
        } else if (wasMobile && !this.isMobile) {
          // If transitioning from mobile to desktop, reset fixed state
          this.isFixed = false;
          this.renderer.removeClass(this.navbar.nativeElement, 'mobile-toolbar');
          // Check scroll position to determine if nav should be fixed
          this.checkScroll();
        }
      } else {
        console.log('Navbar not initialized yet, skipping resize DOM updates');
      }
    } catch (error) {
      console.error('Error in checkScreenSize:', error);
    }

    // If screen size category changed, update layout
    if (wasMobile !== this.isMobile || wasVerySmall !== this.isVerySmallScreen) {
      this.updateLayout();
    }
  }

  /**
   * Handle transition from mobile to desktop view
   */
  private handleMobileToDesktopTransition(): void {
    // Close the drawer if it's open
    if (this.drawer && this.drawer.opened) {
      this.drawer.close();
      this.isMobileMenuOpen = false;
    }
  }

  /**
   * Update layout after screen size changes
   */
  private updateLayout(): void {
    // Force a change detection cycle
    setTimeout(() => {
      // Any additional layout updates can go here
    }, 0);
  }

  toggleMobileMenu(): void {
    if (this.drawer) {
      this.drawer.toggle();
      this.isMobileMenuOpen = !this.isMobileMenuOpen;
    }
  }

  closeMobileMenu(): void {
    if (this.drawer && this.drawer.opened) {
      this.drawer.close();
      this.isMobileMenuOpen = false;
    }
  }
}
