using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using School.API.Common;
using School.Application.DTOs;
using School.Application.Features.Auth;
using School.Domain.Entities;
using System.Security.Claims;

namespace School.API.Endpoints;

public class AuthEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var authGroup = app.MapGroup("/api/auth").WithTags("Authentication").AllowAnonymous();
        var mfaGroup = app.MapGroup("/api/auth/mfa").WithTags("Multi-Factor Authentication").RequireAuthorization();
        var profileGroup = app.MapGroup("/api/auth").WithTags("Authentication").RequireAuthorization();
        var testGroup = app.MapGroup("/api/test").WithTags("Testing").AllowAnonymous();

        // Authentication endpoints
        authGroup.MapPost("/login", async ([FromBody] LoginDto loginDto, [FromServices] IAuthService authService) =>
        {
            try
            {
                var response = await authService.LoginAsync(loginDto);
                if (response == null)
                {
                    return ApiResults.ApiUnauthorized("Invalid credentials");
                }

                if (response.RequiresMfa)
                {
                    return ApiResults.ApiOk(new
                    {
                        requiresMfa = true,
                        mfaToken = response.MfaToken,
                        message = "MFA verification required"
                    }, "MFA verification required");
                }

                return ApiResults.ApiOk(response, "Login successful");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiBadRequest($"Login failed: {ex.Message}");
            }
        })
        .WithName("Login")
        .WithSummary("Authenticate user with username/password and optional MFA")
        .WithDescription("Authenticates a user with username/password. If MFA is enabled, either provide MFA code or receive MFA token for verification.")
        .WithOpenApi();

        authGroup.MapPost("/register", async ([FromBody] UserCreateDto userDto, [FromServices] IAuthService authService) =>
        {
            try
            {
                var userId = await authService.RegisterAsync(userDto);
                return ApiResults.ApiCreated(new { id = userId }, $"/api/users/{userId}", "User registered successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiBadRequest($"Registration failed: {ex.Message}");
            }
        })
        .WithName("Register")
        .WithSummary("Register a new user")
        .WithDescription("Creates a new user account with the provided information and preferences.")
        .WithOpenApi();

        authGroup.MapPost("/refresh", async ([FromBody] RefreshTokenDto refreshTokenDto, [FromServices] IAuthService authService) =>
        {
            try
            {
                var response = await authService.RefreshTokenAsync(refreshTokenDto);
                if (response == null)
                {
                    return ApiResults.ApiUnauthorized("Invalid or expired refresh token");
                }
                return ApiResults.ApiOk(response, "Token refreshed successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiBadRequest($"Token refresh failed: {ex.Message}");
            }
        })
        .WithName("RefreshToken")
        .WithSummary("Refresh access token")
        .WithDescription("Generates a new access token using a valid refresh token.")
        .WithOpenApi();

        // Get current user profile (for testing authentication)
        profileGroup.MapGet("/me", (ClaimsPrincipal user) =>
        {
            var userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userName = user.FindFirst(ClaimTypes.Name)?.Value;
            var userRole = user.FindFirst(ClaimTypes.Role)?.Value;
            var userEmail = user.FindFirst(ClaimTypes.Email)?.Value;

            return ApiResults.ApiOk(new
            {
                userId,
                userName,
                userRole,
                userEmail,
                claims = user.Claims.Select(c => new { c.Type, c.Value }).ToList()
            }, "Current user information retrieved successfully");
        })
        .WithName("GetCurrentUser")
        .WithSummary("Get current authenticated user information")
        .WithDescription("Returns information about the currently authenticated user for debugging purposes.")
        .WithOpenApi();

        authGroup.MapPost("/logout", async ([FromBody] RefreshTokenDto refreshTokenDto, [FromServices] IAuthService authService) =>
        {
            try
            {
                await authService.LogoutAsync(refreshTokenDto.RefreshToken);
                return ApiResults.ApiOk<object?>(null, "Logged out successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiBadRequest($"Logout failed: {ex.Message}");
            }
        })
        .WithName("Logout")
        .WithSummary("Logout user")
        .WithDescription("Logs out the user and revokes the refresh token.")
        .WithOpenApi();

        // MFA endpoints
        mfaGroup.MapPost("/setup", async ([FromBody] MfaSetupRequestDto setupDto, [FromServices] IMfaService mfaService, ClaimsPrincipal user) =>
        {
            try
            {
                var userId = user.FindFirst("userId")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return ApiResults.ApiUnauthorized("User not found");
                }

                var response = await mfaService.GenerateMfaSetupAsync(userId, setupDto.Password);
                return ApiResults.ApiOk(response, "MFA setup information generated");
            }
            catch (UnauthorizedAccessException)
            {
                return ApiResults.ApiUnauthorized("Invalid password");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiBadRequest($"MFA setup failed: {ex.Message}");
            }
        })
        .WithName("SetupMfa")
        .WithSummary("Generate MFA setup information")
        .WithDescription("Generates QR code and backup codes for MFA setup. Requires password confirmation.")
        .WithOpenApi();

        mfaGroup.MapPost("/verify", async ([FromBody] MfaVerifyDto verifyDto, [FromServices] IMfaService mfaService, ClaimsPrincipal user) =>
        {
            try
            {
                var userId = user.FindFirst("userId")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return ApiResults.ApiUnauthorized("User not found");
                }

                bool isValid;
                if (!string.IsNullOrEmpty(verifyDto.Secret))
                {
                    // Verifying during setup
                    isValid = await mfaService.VerifyMfaSetupAsync(userId, verifyDto.Secret, verifyDto.Code);
                }
                else
                {
                    // Verifying during login
                    isValid = await mfaService.VerifyMfaCodeAsync(userId, verifyDto.Code);
                }

                if (isValid)
                {
                    return ApiResults.ApiOk(new { verified = true }, "MFA code verified successfully");
                }
                else
                {
                    return ApiResults.ApiBadRequest("Invalid MFA code");
                }
            }
            catch (Exception ex)
            {
                return ApiResults.ApiBadRequest($"MFA verification failed: {ex.Message}");
            }
        })
        .WithName("VerifyMfa")
        .WithSummary("Verify MFA code")
        .WithDescription("Verifies an MFA code during setup or login.")
        .WithOpenApi();

        mfaGroup.MapPost("/enable", async ([FromBody] MfaVerifyDto enableDto, [FromServices] IMfaService mfaService, ClaimsPrincipal user) =>
        {
            try
            {
                var userId = user.FindFirst("userId")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return ApiResults.ApiUnauthorized("User not found");
                }

                var success = await mfaService.EnableMfaAsync(userId, enableDto.Secret!, enableDto.Code);
                if (success)
                {
                    return ApiResults.ApiOk(new { enabled = true }, "MFA enabled successfully");
                }
                else
                {
                    return ApiResults.ApiBadRequest("Failed to enable MFA. Invalid code or setup.");
                }
            }
            catch (Exception ex)
            {
                return ApiResults.ApiBadRequest($"MFA enable failed: {ex.Message}");
            }
        })
        .WithName("EnableMfa")
        .WithSummary("Enable MFA for user")
        .WithDescription("Enables MFA for the user after successful code verification.")
        .WithOpenApi();

        mfaGroup.MapPost("/disable", async ([FromBody] MfaDisableDto disableDto, [FromServices] IMfaService mfaService, ClaimsPrincipal user) =>
        {
            try
            {
                var userId = user.FindFirst("userId")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return ApiResults.ApiUnauthorized("User not found");
                }

                var success = await mfaService.DisableMfaAsync(userId, disableDto.Password, disableDto.MfaCode);
                if (success)
                {
                    return ApiResults.ApiOk(new { disabled = true }, "MFA disabled successfully");
                }
                else
                {
                    return ApiResults.ApiBadRequest("Failed to disable MFA. Invalid password or MFA code.");
                }
            }
            catch (Exception ex)
            {
                return ApiResults.ApiBadRequest($"MFA disable failed: {ex.Message}");
            }
        })
        .WithName("DisableMfa")
        .WithSummary("Disable MFA for user")
        .WithDescription("Disables MFA for the user. Requires password and MFA code confirmation.")
        .WithOpenApi();


    }
}
