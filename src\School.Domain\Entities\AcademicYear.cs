using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class AcademicYear : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this academic year belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public string Name { get; set; } = string.Empty; // e.g., "2024-2025"
    public string DisplayName { get; set; } = string.Empty; // e.g., "Academic Year 2024-2025"
    public string Code { get; set; } = string.Empty; // e.g., "AY2024-25"
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public AcademicYearStatus Status { get; set; } = AcademicYearStatus.Draft;
    public bool IsCurrentYear { get; set; } = false;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    
    // Academic configuration
    public int TotalWorkingDays { get; set; }
    public int TotalHolidays { get; set; }
    public DateTime? RegistrationStartDate { get; set; }
    public DateTime? RegistrationEndDate { get; set; }
    public DateTime? AdmissionStartDate { get; set; }
    public DateTime? AdmissionEndDate { get; set; }
    
    // Navigation properties
    public ICollection<Term> Terms { get; set; } = new List<Term>();
    public ICollection<AcademicCalendar> CalendarEvents { get; set; } = new List<AcademicCalendar>();
    public ICollection<Holiday> Holidays { get; set; } = new List<Holiday>();
    public ICollection<StudentAcademicHistory> StudentHistories { get; set; } = new List<StudentAcademicHistory>();
    public ICollection<AcademicYearTranslation> Translations { get; set; } = new List<AcademicYearTranslation>();
}
