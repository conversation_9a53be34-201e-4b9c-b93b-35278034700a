using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class Parent : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this parent belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public GenderType Gender { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string AlternatePhone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string Occupation { get; set; } = string.Empty;
    public ParentRelationType RelationType { get; set; } = ParentRelationType.Father;
    public string UserId { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public Guid? ProfileImageId { get; set; }
    public MediaItem? ProfileImage { get; set; }
    public ICollection<StudentParent> Students { get; set; } = new List<StudentParent>();
    public DateTime? UpdatedAt { get; set; }
}
