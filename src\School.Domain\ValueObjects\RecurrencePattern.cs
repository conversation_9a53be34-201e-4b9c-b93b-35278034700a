using School.Domain.Enums;

namespace School.Domain.ValueObjects;

public class RecurrencePattern
{
    public RecurrenceType Type { get; set; }
    public int Interval { get; set; } = 1; // Every X days/weeks/months/years
    public DateTime? EndDate { get; set; }
    public int? MaxOccurrences { get; set; }
    public List<DayOfWeek>? DaysOfWeek { get; set; } // For weekly recurrence
    public int? DayOfMonth { get; set; } // For monthly recurrence
    public int? MonthOfYear { get; set; } // For yearly recurrence
    public string? CustomPattern { get; set; } // For complex patterns
    
    public RecurrencePattern()
    {
    }
    
    public RecurrencePattern(RecurrenceType type, int interval = 1)
    {
        Type = type;
        Interval = interval;
    }
    
    /// <summary>
    /// Creates a daily recurrence pattern
    /// </summary>
    public static RecurrencePattern Daily(int interval = 1, DateTime? endDate = null, int? maxOccurrences = null)
    {
        return new RecurrencePattern(RecurrenceType.Daily, interval)
        {
            EndDate = endDate,
            MaxOccurrences = maxOccurrences
        };
    }
    
    /// <summary>
    /// Creates a weekly recurrence pattern
    /// </summary>
    public static RecurrencePattern Weekly(int interval = 1, List<DayOfWeek>? daysOfWeek = null, DateTime? endDate = null, int? maxOccurrences = null)
    {
        return new RecurrencePattern(RecurrenceType.Weekly, interval)
        {
            DaysOfWeek = daysOfWeek,
            EndDate = endDate,
            MaxOccurrences = maxOccurrences
        };
    }
    
    /// <summary>
    /// Creates a monthly recurrence pattern
    /// </summary>
    public static RecurrencePattern Monthly(int interval = 1, int? dayOfMonth = null, DateTime? endDate = null, int? maxOccurrences = null)
    {
        return new RecurrencePattern(RecurrenceType.Monthly, interval)
        {
            DayOfMonth = dayOfMonth,
            EndDate = endDate,
            MaxOccurrences = maxOccurrences
        };
    }
    
    /// <summary>
    /// Creates a yearly recurrence pattern
    /// </summary>
    public static RecurrencePattern Yearly(int interval = 1, int? monthOfYear = null, int? dayOfMonth = null, DateTime? endDate = null, int? maxOccurrences = null)
    {
        return new RecurrencePattern(RecurrenceType.Yearly, interval)
        {
            MonthOfYear = monthOfYear,
            DayOfMonth = dayOfMonth,
            EndDate = endDate,
            MaxOccurrences = maxOccurrences
        };
    }
    
    /// <summary>
    /// Generates the next occurrence date based on the pattern
    /// </summary>
    public DateTime? GetNextOccurrence(DateTime fromDate)
    {
        if (EndDate.HasValue && fromDate >= EndDate.Value)
            return null;
            
        return Type switch
        {
            RecurrenceType.Daily => fromDate.AddDays(Interval),
            RecurrenceType.Weekly => GetNextWeeklyOccurrence(fromDate),
            RecurrenceType.Monthly => GetNextMonthlyOccurrence(fromDate),
            RecurrenceType.Yearly => GetNextYearlyOccurrence(fromDate),
            _ => null
        };
    }
    
    private DateTime GetNextWeeklyOccurrence(DateTime fromDate)
    {
        if (DaysOfWeek == null || !DaysOfWeek.Any())
            return fromDate.AddDays(7 * Interval);
            
        var nextDate = fromDate.AddDays(1);
        while (!DaysOfWeek.Contains(nextDate.DayOfWeek))
        {
            nextDate = nextDate.AddDays(1);
        }
        
        return nextDate;
    }
    
    private DateTime GetNextMonthlyOccurrence(DateTime fromDate)
    {
        var nextDate = fromDate.AddMonths(Interval);
        
        if (DayOfMonth.HasValue)
        {
            var daysInMonth = DateTime.DaysInMonth(nextDate.Year, nextDate.Month);
            var targetDay = Math.Min(DayOfMonth.Value, daysInMonth);
            nextDate = new DateTime(nextDate.Year, nextDate.Month, targetDay, fromDate.Hour, fromDate.Minute, fromDate.Second);
        }
        
        return nextDate;
    }
    
    private DateTime GetNextYearlyOccurrence(DateTime fromDate)
    {
        var nextDate = fromDate.AddYears(Interval);
        
        if (MonthOfYear.HasValue)
        {
            var month = MonthOfYear.Value;
            var day = DayOfMonth ?? fromDate.Day;
            var daysInMonth = DateTime.DaysInMonth(nextDate.Year, month);
            var targetDay = Math.Min(day, daysInMonth);
            
            nextDate = new DateTime(nextDate.Year, month, targetDay, fromDate.Hour, fromDate.Minute, fromDate.Second);
        }
        
        return nextDate;
    }
    
    /// <summary>
    /// Validates the recurrence pattern
    /// </summary>
    public bool IsValid()
    {
        if (Interval <= 0) return false;
        
        return Type switch
        {
            RecurrenceType.Monthly when DayOfMonth.HasValue => DayOfMonth.Value >= 1 && DayOfMonth.Value <= 31,
            RecurrenceType.Yearly when MonthOfYear.HasValue => MonthOfYear.Value >= 1 && MonthOfYear.Value <= 12,
            RecurrenceType.Yearly when DayOfMonth.HasValue => DayOfMonth.Value >= 1 && DayOfMonth.Value <= 31,
            _ => true
        };
    }
}
