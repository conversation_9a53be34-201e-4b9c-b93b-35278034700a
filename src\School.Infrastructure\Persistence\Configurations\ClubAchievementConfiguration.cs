using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ClubAchievementConfiguration : IEntityTypeConfiguration<ClubAchievement>
{
    public void Configure(EntityTypeBuilder<ClubAchievement> builder)
    {
        builder.HasKey(a => a.Id);
        
        builder.Property(a => a.Description)
            .IsRequired();
            
        builder.Property(a => a.DisplayOrder)
            .HasDefaultValue(0);
            
        // Relationships
        builder.HasMany(a => a.Translations)
            .WithOne(t => t.ClubAchievement)
            .HasForeignKey(t => t.ClubAchievementId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
