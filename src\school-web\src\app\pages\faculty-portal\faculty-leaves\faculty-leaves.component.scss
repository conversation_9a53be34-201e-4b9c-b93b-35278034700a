.leaves-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.leaves-content {
  max-width: 1000px;
  margin: 0 auto;
}

.filter-card {
  margin-bottom: 24px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.leaves-loading,
.leaves-error {
  margin-bottom: 24px;
}

.leaves-error {
  text-align: center;
  padding: 16px;

  mat-icon {
    vertical-align: middle;
    margin-right: 8px;
  }
}

.no-leaves {
  text-align: center;
  padding: 16px;
}

.leaves-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.leave-card {
  margin-bottom: 0;
}

.student-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  mat-icon {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #757575;
  }
}

.leave-status {
  margin-left: auto;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.approved {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.pending {
  background-color: #fff8e1;
  color: #ff8f00;
}

.rejected {
  background-color: #ffebee;
  color: #c62828;
}

.cancelled {
  background-color: #f5f5f5;
  color: #757575;
}

.leave-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.leave-reason,
.leave-attachment,
.leave-applied {
  grid-column: span 2;
}

.leave-dates {
  display: flex;
  gap: 16px;
}

.leave-type,
.leave-duration,
.leave-date,
.leave-reason,
.leave-attachment,
.leave-applied {
  display: flex;
  flex-direction: column;
}

.leave-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.leave-value {
  font-size: 16px;
  font-weight: 500;
}

mat-card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px 16px;
}

@media (max-width: 768px) {
  .leave-details {
    grid-template-columns: 1fr;
  }

  .leave-reason,
  .leave-attachment,
  .leave-applied {
    grid-column: span 1;
  }
}
