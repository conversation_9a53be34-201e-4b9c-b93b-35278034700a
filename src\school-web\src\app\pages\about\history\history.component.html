<!-- Enhanced Hero Section -->
<app-enhanced-hero
  [title]="'ABOUT.HISTORY_TITLE' | translate"
  [subtitle]="'ABOUT.HISTORY_SUBTITLE' | translate"
  [description]="'ABOUT.HISTORY_DESCRIPTION' | translate"
  [backgroundImage]="'assets/images/history-hero.jpg'"
  [overlayImage]="'assets/images/school-emblem.png'"
  [breadcrumbs]="['About', 'History']"
  [buttons]="[
    {label: 'ABOUT.HISTORY_LEARN_MORE' | translate, link: '/about/mission', isPrimary: true, icon: 'history_edu'},
    {label: 'ABOUT.CONTACT_US' | translate, link: '/contact', isPrimary: false}
  ]"
  [theme]="'dark'"
  [size]="'large'"
  [alignment]="'left'">
</app-enhanced-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'ABOUT.HISTORY_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'ABOUT.HISTORY_INTRO_P1' | translate }}</p>
      <p>{{ 'ABOUT.HISTORY_INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Timeline Section -->
  <section class="timeline-section">
    <h2>{{ 'ABOUT.HISTORY_TIMELINE_TITLE' | translate }}</h2>
    <div class="timeline">
      <div class="timeline-item" *ngFor="let event of timelineEvents; let i = index" [class.right]="i % 2 !== 0">
        <div class="timeline-content">
          <div class="timeline-year">{{event.year}}</div>
          <div class="timeline-image">
            <img [src]="event.image" [alt]="event.title">
          </div>
          <h3>{{event.title}}</h3>
          <p>{{event.description}}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Founders Section -->
  <section class="founders-section">
    <h2>{{ 'ABOUT.HISTORY_FOUNDERS_TITLE' | translate }}</h2>
    <div class="founders-grid">
      <mat-card class="founder-card" *ngFor="let founder of founders">
        <div class="founder-image">
          <img [src]="founder.image" [alt]="founder.name">
        </div>
        <mat-card-content>
          <h3>{{founder.name}}</h3>
          <h4>{{founder.title}}</h4>
          <p>{{founder.bio}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Legacy Section -->
  <section class="legacy-section">
    <h2>{{ 'ABOUT.HISTORY_LEGACY_TITLE' | translate }}</h2>
    <div class="legacy-content">
      <p>{{ 'ABOUT.HISTORY_LEGACY_P1' | translate }}</p>
      <p>{{ 'ABOUT.HISTORY_LEGACY_P2' | translate }}</p>
      <div class="legacy-cta">
        <a mat-raised-button color="primary" routerLink="/about/mission">
          {{ 'ABOUT.HISTORY_LEARN_MORE' | translate }}
        </a>
      </div>
    </div>
  </section>
</div>
