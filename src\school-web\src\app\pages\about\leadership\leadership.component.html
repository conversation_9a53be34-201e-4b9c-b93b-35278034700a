<!-- Enhanced Hero Section -->
<app-enhanced-hero
  [title]="'ABOUT.LEADERSHIP_TITLE' | translate"
  [subtitle]="'ABOUT.LEADERSHIP_SUBTITLE' | translate"
  [description]="'ABOUT.LEADERSHIP_DESCRIPTION' | translate"
  [backgroundImage]="'assets/images/leadership-hero.jpg'"
  [overlayImage]="'assets/images/school-emblem.png'"
  [breadcrumbs]="['About', 'Leadership']"
  [buttons]="[
    {label: 'ABOUT.MEET_OUR_TEAM' | translate, link: '#executive-team', isPrimary: true, icon: 'groups'},
    {label: 'ABOUT.CONTACT_LEADERSHIP' | translate, link: '#contact', isPrimary: false}
  ]"
  [theme]="'dark'"
  [size]="'large'"
  [alignment]="'left'">
</app-enhanced-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'ABOUT.LEADERSHIP_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'ABOUT.LEADERSHIP_INTRO_P1' | translate }}</p>
      <p>{{ 'ABOUT.LEADERSHIP_INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Leadership Tabs -->
  <section id="executive-team" class="leadership-tabs-section">
    <mat-tab-group animationDuration="300ms">
      <!-- Executive Team Tab -->
      <mat-tab [label]="'ABOUT.EXECUTIVE_TEAM' | translate">
        <div class="tab-content">
          <h2>{{ 'ABOUT.EXECUTIVE_TEAM' | translate }}</h2>
          <p class="tab-description">{{ 'ABOUT.EXECUTIVE_TEAM_DESCRIPTION' | translate }}</p>

          <div class="leadership-grid">
            <mat-card class="leadership-card" *ngFor="let leader of executiveTeam">
              <div class="leader-image">
                <img [src]="leader.image" [alt]="leader.name">
              </div>
              <mat-card-content>
                <h3>{{leader.name}}</h3>
                <h4>{{leader.title}}</h4>
                <p class="leader-bio">{{leader.bio}}</p>

                <div class="leader-details">
                  <div class="detail-section">
                    <h5>{{ 'ABOUT.EDUCATION' | translate }}</h5>
                    <ul>
                      <li *ngFor="let edu of leader.education">{{edu}}</li>
                    </ul>
                  </div>

                  <div class="detail-section" *ngIf="leader.experience && leader.experience.length > 0">
                    <h5>{{ 'ABOUT.EXPERIENCE' | translate }}</h5>
                    <ul>
                      <li *ngFor="let exp of leader.experience">{{exp}}</li>
                    </ul>
                  </div>

                  <div class="detail-section">
                    <h5>{{ 'ABOUT.CONTACT' | translate }}</h5>
                    <div class="contact-info">
                      <div class="contact-item">
                        <mat-icon>email</mat-icon>
                        <span>{{leader.email}}</span>
                      </div>
                      <div class="contact-item" *ngIf="leader.phone">
                        <mat-icon>phone</mat-icon>
                        <span>{{leader.phone}}</span>
                      </div>
                    </div>

                    <div class="social-links" *ngIf="leader.socialLinks">
                      <a *ngIf="leader.socialLinks.linkedin" [href]="leader.socialLinks.linkedin" target="_blank" class="social-link">
                        <mat-icon>linkedin</mat-icon>
                      </a>
                      <a *ngIf="leader.socialLinks.twitter" [href]="leader.socialLinks.twitter" target="_blank" class="social-link">
                        <mat-icon>twitter</mat-icon>
                      </a>
                    </div>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </mat-tab>

      <!-- Board of Trustees Tab -->
      <mat-tab [label]="'ABOUT.BOARD_OF_TRUSTEES' | translate">
        <div class="tab-content">
          <h2>{{ 'ABOUT.BOARD_OF_TRUSTEES' | translate }}</h2>
          <p class="tab-description">{{ 'ABOUT.BOARD_DESCRIPTION' | translate }}</p>

          <div class="board-grid">
            <mat-card class="board-card" *ngFor="let member of boardMembers">
              <div class="board-image">
                <img [src]="member.image" [alt]="member.name">
              </div>
              <mat-card-content>
                <h3>{{member.name}}</h3>
                <h4>{{member.title}}</h4>
                <div class="profession">{{member.profession}}</div>
                <p class="board-bio">{{member.bio}}</p>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </mat-tab>

      <!-- Organizational Structure Tab -->
      <mat-tab [label]="'ABOUT.ORGANIZATIONAL_STRUCTURE' | translate">
        <div class="tab-content">
          <h2>{{ 'ABOUT.ORGANIZATIONAL_STRUCTURE' | translate }}</h2>
          <p class="tab-description">{{ 'ABOUT.STRUCTURE_DESCRIPTION' | translate }}</p>

          <div class="structure-diagram">
            <div class="structure-item" *ngFor="let item of organizationalStructure; let i = index">
              <div class="structure-number">{{i + 1}}</div>
              <div class="structure-content">
                <h3>{{item.title}}</h3>
                <p>{{item.description}}</p>
              </div>
            </div>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </section>

  <!-- Message from the Principal Section -->
  <section class="principal-message-section">
    <h2>{{ 'ABOUT.MESSAGE_FROM_PRINCIPAL' | translate }}</h2>

    <div class="message-content">
      <div class="principal-image">
        <img [src]="executiveTeam[0].image" [alt]="executiveTeam[0].name">
      </div>

      <div class="message-text">
        <h3>{{ 'ABOUT.WELCOME_MESSAGE' | translate }}</h3>
        <p>{{ 'ABOUT.PRINCIPAL_MESSAGE_P1' | translate }}</p>
        <p>{{ 'ABOUT.PRINCIPAL_MESSAGE_P2' | translate }}</p>
        <p>{{ 'ABOUT.PRINCIPAL_MESSAGE_P3' | translate }}</p>
        <div class="signature">
          <p>{{ 'ABOUT.SINCERELY' | translate }},</p>
          <p class="principal-name">{{executiveTeam[0].name}}</p>
          <p>{{ 'ABOUT.PRINCIPAL' | translate }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="contact-section">
    <div class="contact-content">
      <h2>{{ 'ABOUT.CONTACT_LEADERSHIP' | translate }}</h2>
      <p>{{ 'ABOUT.CONTACT_LEADERSHIP_TEXT' | translate }}</p>
      <a mat-raised-button color="primary" routerLink="/contact">
        {{ 'ABOUT.CONTACT_US' | translate }}
      </a>
    </div>
  </section>
</div>
