import { Compo<PERSON>, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, Validators, ReactiveFormsModule, FormArray } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

// Angular Material
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTooltipModule } from '@angular/material/tooltip';

// Services and Models
import { HolidayService } from '../../../../core/services/holiday.service';
import { AcademicYearService } from '../../../../core/services/academic-year.service';
import { LanguageService } from '../../../../core/services/language.service';
import {
  Holiday,
  CreateHoliday,
  UpdateHoliday,
  HolidayType,
  RecurrenceType,
  RecurrencePattern,
  CreateHolidayTranslation,
  HOLIDAY_COLORS
} from '../../../../core/models/holiday.model';
import { AcademicYear, Term } from '../../../../core/models/academic-year.model';

@Component({
  selector: 'app-holiday-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatSlideToggleModule,
    MatTabsModule,
    MatChipsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatExpansionModule,
    MatTooltipModule
  ],
  templateUrl: './holiday-form.component.html',
  styleUrls: ['./holiday-form.component.scss']
})
export class HolidayFormComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Form
  holidayForm!: FormGroup;
  translationForms: { [key: string]: FormGroup } = {};

  // Data
  holiday?: Holiday;
  academicYears: AcademicYear[] = [];
  terms: Term[] = [];
  languages: { code: string; name: string }[] = [];

  // State
  loading = false;
  saving = false;
  isEditMode = false;
  holidayId?: string;

  // Options
  holidayTypes = [
    { value: HolidayType.National, label: 'National', color: '#FF5722' },
    { value: HolidayType.Religious, label: 'Religious', color: '#9C27B0' },
    { value: HolidayType.Cultural, label: 'Cultural', color: '#FF9800' },
    { value: HolidayType.Academic, label: 'Academic', color: '#2196F3' },
    { value: HolidayType.Administrative, label: 'Administrative', color: '#607D8B' },
    { value: HolidayType.Seasonal, label: 'Seasonal', color: '#4CAF50' },
    { value: HolidayType.Custom, label: 'Custom', color: '#795548' }
  ];

  recurrenceTypes = [
    { value: RecurrenceType.None, label: 'None' },
    { value: RecurrenceType.Daily, label: 'Daily' },
    { value: RecurrenceType.Weekly, label: 'Weekly' },
    { value: RecurrenceType.Monthly, label: 'Monthly' },
    { value: RecurrenceType.Yearly, label: 'Yearly' },
    { value: RecurrenceType.Custom, label: 'Custom' }
  ];

  colorOptions = HOLIDAY_COLORS;

  daysOfWeek = [
    { value: 0, label: 'Sunday' },
    { value: 1, label: 'Monday' },
    { value: 2, label: 'Tuesday' },
    { value: 3, label: 'Wednesday' },
    { value: 4, label: 'Thursday' },
    { value: 5, label: 'Friday' },
    { value: 6, label: 'Saturday' }
  ];

  constructor(
    private fb: FormBuilder,
    private holidayService: HolidayService,
    private academicYearService: AcademicYearService,
    private languageService: LanguageService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadData();
    this.checkEditMode();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.holidayForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(200)]],
      description: ['', [Validators.maxLength(1000)]],
      startDate: ['', [Validators.required]],
      endDate: ['', [Validators.required]],
      type: [HolidayType.Academic, [Validators.required]],
      color: ['#2196F3', [Validators.required]],
      isRecurring: [false],
      recurrencePattern: this.fb.group({
        type: [RecurrenceType.None],
        interval: [1, [Validators.min(1), Validators.max(100)]],
        endDate: [''],
        maxOccurrences: ['', [Validators.min(1), Validators.max(1000)]],
        daysOfWeek: [[]],
        dayOfMonth: ['', [Validators.min(1), Validators.max(31)]],
        monthOfYear: ['', [Validators.min(1), Validators.max(12)]],
        customPattern: ['', [Validators.maxLength(500)]]
      }),
      isActive: [true],
      isPublic: [true],
      remarks: ['', [Validators.maxLength(1000)]],
      academicYearId: [''],
      termId: ['']
    });

    // Add custom validators
    this.holidayForm.setValidators([this.dateRangeValidator.bind(this)]);
    
    // Watch for changes
    this.setupFormWatchers();
  }

  private setupFormWatchers(): void {
    // Watch academic year changes to load terms
    this.holidayForm.get('academicYearId')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(academicYearId => {
        this.loadTermsForAcademicYear(academicYearId);
        this.holidayForm.get('termId')?.setValue('');
      });

    // Watch recurrence toggle
    this.holidayForm.get('isRecurring')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(isRecurring => {
        const recurrenceGroup = this.holidayForm.get('recurrencePattern');
        if (isRecurring) {
          recurrenceGroup?.get('type')?.setValue(RecurrenceType.Yearly);
          this.updateRecurrenceValidators(RecurrenceType.Yearly);
        } else {
          recurrenceGroup?.get('type')?.setValue(RecurrenceType.None);
          this.clearRecurrenceValidators();
        }
      });

    // Watch recurrence type changes
    this.holidayForm.get('recurrencePattern.type')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(type => {
        this.updateRecurrenceValidators(type);
      });

    // Watch holiday type changes to suggest color
    this.holidayForm.get('type')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(type => {
        if (!this.isEditMode) {
          const typeOption = this.holidayTypes.find(t => t.value === type);
          if (typeOption) {
            this.holidayForm.get('color')?.setValue(typeOption.color);
          }
        }
      });
  }

  private loadData(): void {
    this.loadAcademicYears();
    this.loadLanguages();
  }

  private loadAcademicYears(): void {
    this.academicYearService.getAcademicYears({ page: 1, pageSize: 100, sortDescending: false })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            this.academicYears = response.data.items;
          }
        },
        error: (error) => {
          console.error('Error loading academic years:', error);
          this.snackBar.open('Error loading academic years', 'Close', { duration: 3000 });
        }
      });
  }

  private loadTermsForAcademicYear(academicYearId: string): void {
    if (!academicYearId) {
      this.terms = [];
      return;
    }

    const selectedYear = this.academicYears.find(ay => ay.id === academicYearId);
    this.terms = selectedYear?.terms || [];
  }

  private loadLanguages(): void {
    this.languageService.getAvailableLanguages()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (languages) => {
          this.languages = languages.filter(lang => lang.code !== 'en'); // Exclude English as it's default
          this.initializeTranslationForms();
        },
        error: (error) => {
          console.error('Error loading languages:', error);
        }
      });
  }

  private initializeTranslationForms(): void {
    this.languages.forEach(lang => {
      this.translationForms[lang.code] = this.fb.group({
        name: ['', [Validators.required, Validators.maxLength(200)]],
        description: ['', [Validators.maxLength(1000)]],
        remarks: ['', [Validators.maxLength(1000)]]
      });
    });
  }

  private checkEditMode(): void {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.holidayId = params['id'];
        this.loadHoliday();
      }
    });
  }

  private loadHoliday(): void {
    if (!this.holidayId) return;

    this.loading = true;
    this.holidayService.getHolidayById(this.holidayId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.success && response.data) {
            this.holiday = response.data;
            this.populateForm();
            this.loadHolidayTranslations();
          }
        },
        error: (error) => {
          this.loading = false;
          console.error('Error loading holiday:', error);
          this.snackBar.open('Error loading holiday', 'Close', { duration: 3000 });
          this.router.navigate(['/admin/holidays']);
        }
      });
  }

  private populateForm(): void {
    if (!this.holiday) return;

    this.holidayForm.patchValue({
      name: this.holiday.name,
      description: this.holiday.description,
      startDate: new Date(this.holiday.startDate),
      endDate: new Date(this.holiday.endDate),
      type: this.holiday.type,
      color: this.holiday.color,
      isRecurring: this.holiday.isRecurring,
      isActive: this.holiday.isActive,
      isPublic: this.holiday.isPublic,
      remarks: this.holiday.remarks,
      academicYearId: this.holiday.academicYearId || '',
      termId: this.holiday.termId || ''
    });

    if (this.holiday.recurrencePattern) {
      this.holidayForm.get('recurrencePattern')?.patchValue({
        type: this.holiday.recurrencePattern.type,
        interval: this.holiday.recurrencePattern.interval,
        endDate: this.holiday.recurrencePattern.endDate ? new Date(this.holiday.recurrencePattern.endDate) : '',
        maxOccurrences: this.holiday.recurrencePattern.maxOccurrences || '',
        daysOfWeek: this.holiday.recurrencePattern.daysOfWeek || [],
        dayOfMonth: this.holiday.recurrencePattern.dayOfMonth || '',
        monthOfYear: this.holiday.recurrencePattern.monthOfYear || '',
        customPattern: this.holiday.recurrencePattern.customPattern || ''
      });
    }

    // Load terms for the selected academic year
    if (this.holiday.academicYearId) {
      this.loadTermsForAcademicYear(this.holiday.academicYearId);
    }
  }

  private loadHolidayTranslations(): void {
    if (!this.holidayId) return;

    this.holidayService.getTranslations(this.holidayId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            response.data.forEach(translation => {
              if (this.translationForms[translation.languageCode]) {
                this.translationForms[translation.languageCode].patchValue({
                  name: translation.name,
                  description: translation.description,
                  remarks: translation.remarks
                });
              }
            });
          }
        },
        error: (error) => {
          console.error('Error loading translations:', error);
        }
      });
  }

  // Validation methods
  private dateRangeValidator(form: any): { [key: string]: any } | null {
    const startDate = form.get('startDate')?.value;
    const endDate = form.get('endDate')?.value;

    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (end < start) {
        return { dateRange: { message: 'End date must be after start date' } };
      }

      // Check if duration is reasonable (max 365 days)
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      if (diffDays > 365) {
        return { dateRange: { message: 'Holiday duration cannot exceed 365 days' } };
      }
    }

    return null;
  }

  private updateRecurrenceValidators(type: RecurrenceType): void {
    const recurrenceGroup = this.holidayForm.get('recurrencePattern');
    if (!recurrenceGroup) return;

    // Clear all validators first
    this.clearRecurrenceValidators();

    // Add validators based on type
    switch (type) {
      case RecurrenceType.Weekly:
        recurrenceGroup.get('daysOfWeek')?.setValidators([Validators.required]);
        break;
      case RecurrenceType.Monthly:
        recurrenceGroup.get('dayOfMonth')?.setValidators([Validators.required, Validators.min(1), Validators.max(31)]);
        break;
      case RecurrenceType.Yearly:
        recurrenceGroup.get('monthOfYear')?.setValidators([Validators.required, Validators.min(1), Validators.max(12)]);
        break;
      case RecurrenceType.Custom:
        recurrenceGroup.get('customPattern')?.setValidators([Validators.required, Validators.maxLength(500)]);
        break;
    }

    // Update validity
    const controls = (recurrenceGroup as FormGroup).controls;
    Object.keys(controls).forEach(key => {
      recurrenceGroup.get(key)?.updateValueAndValidity();
    });
  }

  private clearRecurrenceValidators(): void {
    const recurrenceGroup = this.holidayForm.get('recurrencePattern');
    if (!recurrenceGroup) return;

    const controls = (recurrenceGroup as FormGroup).controls;
    Object.keys(controls).forEach(key => {
      if (key !== 'type' && key !== 'interval') {
        recurrenceGroup.get(key)?.clearValidators();
        recurrenceGroup.get(key)?.updateValueAndValidity();
      }
    });
  }

  // Form submission
  onSubmit(): void {
    if (this.holidayForm.invalid) {
      this.markFormGroupTouched(this.holidayForm);
      this.snackBar.open('Please fix the form errors', 'Close', { duration: 3000 });
      return;
    }

    this.saving = true;
    const formValue = this.holidayForm.value;

    // Prepare holiday data
    const holidayData = {
      name: formValue.name,
      description: formValue.description || '',
      startDate: formValue.startDate,
      endDate: formValue.endDate,
      type: formValue.type,
      color: formValue.color,
      isRecurring: formValue.isRecurring,
      isActive: formValue.isActive,
      isPublic: formValue.isPublic,
      remarks: formValue.remarks || '',
      academicYearId: formValue.academicYearId || undefined,
      termId: formValue.termId || undefined,
      recurrencePattern: formValue.isRecurring ? this.buildRecurrencePattern(formValue.recurrencePattern) : undefined
    };

    // Add translations for create
    if (!this.isEditMode) {
      const translations = this.buildTranslations();
      (holidayData as CreateHoliday).translations = translations.length > 0 ? translations : undefined;
    }

    if (this.isEditMode) {
      this.holidayService.updateHoliday(this.holidayId!, holidayData as UpdateHoliday)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: any) => {
            this.saving = false;
            if (response.success) {
              this.snackBar.open('Holiday updated successfully', 'Close', { duration: 3000 });
              this.saveTranslations();
            }
          },
          error: (error: any) => {
            this.saving = false;
            console.error('Error updating holiday:', error);
            this.snackBar.open('Error updating holiday', 'Close', { duration: 3000 });
          }
        });
    } else {
      this.holidayService.createHoliday(holidayData as CreateHoliday)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: any) => {
            this.saving = false;
            if (response.success) {
              this.snackBar.open('Holiday created successfully', 'Close', { duration: 3000 });
              this.router.navigate(['/admin/holidays']);
            }
          },
          error: (error: any) => {
            this.saving = false;
            console.error('Error creating holiday:', error);
            this.snackBar.open('Error creating holiday', 'Close', { duration: 3000 });
          }
        });
    }
  }

  private buildRecurrencePattern(pattern: any): RecurrencePattern {
    return {
      type: pattern.type,
      interval: pattern.interval || 1,
      endDate: pattern.endDate || undefined,
      maxOccurrences: pattern.maxOccurrences || undefined,
      daysOfWeek: pattern.daysOfWeek?.length > 0 ? pattern.daysOfWeek : undefined,
      dayOfMonth: pattern.dayOfMonth || undefined,
      monthOfYear: pattern.monthOfYear || undefined,
      customPattern: pattern.customPattern || undefined
    };
  }

  private buildTranslations(): CreateHolidayTranslation[] {
    const translations: CreateHolidayTranslation[] = [];

    Object.keys(this.translationForms).forEach(languageCode => {
      const form = this.translationForms[languageCode];
      if (form.valid && form.get('name')?.value) {
        translations.push({
          languageCode,
          name: form.get('name')?.value,
          description: form.get('description')?.value || '',
          remarks: form.get('remarks')?.value || ''
        });
      }
    });

    return translations;
  }

  // Navigation
  cancel(): void {
    this.router.navigate(['/admin/holidays']);
  }

  // UI Helper methods
  getErrorMessage(fieldName: string): string {
    const control = this.holidayForm.get(fieldName);
    if (control?.errors && control.touched) {
      if (control.errors['required']) return `${fieldName} is required`;
      if (control.errors['maxlength']) return `${fieldName} is too long`;
      if (control.errors['min']) return `${fieldName} is too small`;
      if (control.errors['max']) return `${fieldName} is too large`;
    }
    return '';
  }

  getRecurrenceErrorMessage(fieldName: string): string {
    const control = this.holidayForm.get(`recurrencePattern.${fieldName}`);
    if (control?.errors && control.touched) {
      if (control.errors['required']) return `${fieldName} is required for this recurrence type`;
      if (control.errors['min']) return `${fieldName} is too small`;
      if (control.errors['max']) return `${fieldName} is too large`;
    }
    return '';
  }

  isRecurrenceFieldRequired(fieldName: string): boolean {
    const recurrenceType = this.holidayForm.get('recurrencePattern.type')?.value;
    const isRecurring = this.holidayForm.get('isRecurring')?.value;

    if (!isRecurring) return false;

    switch (recurrenceType) {
      case RecurrenceType.Weekly:
        return fieldName === 'daysOfWeek';
      case RecurrenceType.Monthly:
        return fieldName === 'dayOfMonth';
      case RecurrenceType.Yearly:
        return fieldName === 'monthOfYear';
      case RecurrenceType.Custom:
        return fieldName === 'customPattern';
      default:
        return false;
    }
  }

  // Utility methods
  getNonEnglishLanguages(): { code: string; name: string }[] {
    return this.languages;
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  private saveTranslations(): void {
    if (!this.holidayId) {
      this.router.navigate(['/admin/holidays']);
      return;
    }

    const translations = this.buildTranslations();
    let savedCount = 0;
    const totalTranslations = translations.length;

    if (totalTranslations === 0) {
      this.router.navigate(['/admin/holidays']);
      return;
    }

    translations.forEach(translation => {
      this.holidayService.addTranslation(this.holidayId!, translation)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            savedCount++;
            if (savedCount === totalTranslations) {
              this.router.navigate(['/admin/holidays']);
            }
          },
          error: (error) => {
            console.error('Error saving translation:', error);
            savedCount++;
            if (savedCount === totalTranslations) {
              this.router.navigate(['/admin/holidays']);
            }
          }
        });
    });
  }
}