import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { AlumniService } from '../../../core/services/alumni.service';
import { Alumni } from '../../../core/models/alumni.model';



@Component({
  selector: 'app-alumni-profile',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatChipsModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    TranslateModule
  ],
  templateUrl: './alumni-profile.component.html',
  styleUrls: ['./alumni-profile.component.scss']
})
export class AlumniProfileComponent implements OnInit {
  // Alumni data
  alumni: Alumni | null = null;

  // Loading state
  loading = true;

  // Error state
  error = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private alumniService: AlumniService
  ) { }

  ngOnInit(): void {
    // Get alumni ID from route params
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.loadAlumni(parseInt(id, 10));
      } else {
        this.error = true;
        this.loading = false;
      }
    });
  }

  /**
   * Load alumni by ID
   */
  private loadAlumni(id: number): void {
    this.loading = true;
    this.error = false;

    this.alumniService.getAlumniById(id).subscribe({
      next: (alumni) => {
        this.alumni = alumni;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading alumni:', error);
        this.error = true;
        this.loading = false;
        // Load mock data as fallback
        this.loadMockAlumni(id);
      }
    });
  }

  /**
   * Load mock alumni data as fallback
   */
  private loadMockAlumni(id: number): void {
    // Mock alumni data
    const alumniData = [
      {
        id: 1,
        name: 'Dr. Sarah Johnson',
        graduationYear: 2005,
        profession: 'Neurosurgeon',
        image: 'https://via.placeholder.com/400x300?text=Dr.+Sarah+Johnson',
        bio: 'Dr. Sarah Johnson is a leading neurosurgeon at Mayo Clinic. After graduating from our school, she went on to complete her medical degree at Harvard Medical School. Her groundbreaking research on brain tumor treatment has saved countless lives and earned her international recognition in the medical community. Dr. Johnson regularly returns to our school to mentor students interested in medical careers and has established a scholarship fund for aspiring doctors.',
        achievements: [
          'Published over 30 research papers in neuroscience',
          'Recipient of the Young Scientist Award 2018',
          'Developed a new surgical technique for brain tumor removal',
          'Chief of Neurosurgery at Mayo Clinic'
        ],
        testimonial: 'The foundation I received at this school prepared me for the challenges of medical school and beyond. The teachers instilled in me not just knowledge, but a passion for learning and helping others.',
        featured: true,
        education: [
          'M.D., Harvard Medical School',
          'Ph.D. in Neuroscience, Johns Hopkins University',
          'B.S. in Biology, Stanford University'
        ],
        company: 'Mayo Clinic',
        location: 'Rochester, Minnesota',
        email: '<EMAIL>',
        socialLinks: {
          linkedin: 'https://linkedin.com/in/sarahjohnson',
          website: 'https://drsarahjohnson.com'
        },
        contributions: [
          'Established the Johnson Scholarship Fund for aspiring medical students',
          'Regular guest speaker at school career day events',
          'Mentors students interested in medical careers'
        ]
      },
      {
        id: 2,
        name: 'Michael Chen',
        graduationYear: 2010,
        profession: 'Tech Entrepreneur',
        image: 'https://via.placeholder.com/400x300?text=Michael+Chen',
        bio: 'Michael Chen is the founder and CEO of InnovateTech, a successful startup that specializes in artificial intelligence solutions. After graduating from our school, he earned his computer science degree from MIT and worked at several major tech companies before launching his own venture. His company now employs over 200 people and has been recognized as one of the fastest-growing tech startups in the country.',
        achievements: [
          'Founded InnovateTech, valued at $500 million',
          'Named to Forbes 30 Under 30 list',
          'Holds 5 patents in AI technology',
          'Raised $50 million in venture capital funding'
        ],
        testimonial: 'My time at this school taught me to think creatively and take intellectual risks. The computer science program and entrepreneurship club gave me my first taste of what would become my life\'s passion.',
        featured: true,
        education: [
          'M.S. in Computer Science, MIT',
          'B.S. in Computer Science, Stanford University'
        ],
        company: 'InnovateTech',
        location: 'San Francisco, California',
        email: '<EMAIL>',
        socialLinks: {
          linkedin: 'https://linkedin.com/in/michaelchen',
          twitter: 'https://twitter.com/michaelchen',
          website: 'https://innovatetech.com'
        },
        contributions: [
          'Donated state-of-the-art computers to the school\'s computer lab',
          'Established internship program for current students',
          'Sponsors annual hackathon event at the school'
        ]
      },
      {
        id: 3,
        name: 'Aisha Rahman',
        graduationYear: 2008,
        profession: 'Environmental Scientist',
        image: 'https://via.placeholder.com/400x300?text=Aisha+Rahman',
        bio: 'Aisha Rahman is a renowned environmental scientist working on climate change solutions. Her research on sustainable agriculture has been implemented in several developing countries, helping communities adapt to changing climate conditions. She credits her high school science teachers for inspiring her interest in environmental issues and encouraging her to pursue a career in science.',
        achievements: [
          'Lead researcher on UN climate adaptation project',
          'Published groundbreaking study on drought-resistant crops',
          'Recipient of the Global Environmental Leadership Award',
          'TED Talk speaker on climate solutions'
        ],
        featured: false,
        education: [
          'Ph.D. in Environmental Science, UC Berkeley',
          'M.S. in Ecology, University of Washington',
          'B.S. in Environmental Studies, Yale University'
        ],
        company: 'Global Climate Institute',
        location: 'Washington, D.C.',
        email: '<EMAIL>',
        socialLinks: {
          linkedin: 'https://linkedin.com/in/aisharahman',
          twitter: 'https://twitter.com/aisharahman'
        },
        contributions: [
          'Guest lecturer for AP Environmental Science classes',
          'Helped develop school\'s sustainability program',
          'Organizes annual environmental awareness workshops'
        ]
      },
      {
        id: 4,
        name: 'James Wilson',
        graduationYear: 2012,
        profession: 'Award-Winning Filmmaker',
        image: 'https://via.placeholder.com/400x300?text=James+Wilson',
        bio: 'James Wilson is an acclaimed filmmaker whose documentaries have been featured at major film festivals including Sundance and Cannes. His work focuses on social justice issues and has won several awards for its powerful storytelling. James first discovered his passion for filmmaking in our school\'s media arts program.',
        achievements: [
          'Winner, Best Documentary, Sundance Film Festival',
          'Emmy Award for Outstanding Documentary Filmmaking',
          'Founded Wilson Productions, a documentary film company',
          'Films distributed on major streaming platforms'
        ],
        testimonial: 'The media arts program at this school gave me my first camera and taught me the fundamentals of visual storytelling. I\'ll always be grateful for the teachers who encouraged my creativity.',
        featured: true,
        education: [
          'M.F.A. in Film Production, USC School of Cinematic Arts',
          'B.A. in Film Studies, NYU'
        ],
        company: 'Wilson Productions',
        location: 'Los Angeles, California',
        email: '<EMAIL>',
        socialLinks: {
          twitter: 'https://twitter.com/jameswilsonfilms',
          website: 'https://wilsonproductions.com'
        },
        contributions: [
          'Donated professional equipment to the school\'s media lab',
          'Conducts annual filmmaking workshops for students',
          'Created scholarship for students pursuing arts education'
        ]
      },
      {
        id: 5,
        name: 'Priya Patel',
        graduationYear: 2015,
        profession: 'Human Rights Attorney',
        image: 'https://via.placeholder.com/400x300?text=Priya+Patel',
        bio: 'Priya Patel is a human rights attorney working with the United Nations. Her work focuses on refugee rights and international humanitarian law. She has conducted field work in conflict zones and contributed to major policy reforms. Priya\'s interest in international affairs began in our school\'s Model UN program.',
        achievements: [
          'Lead counsel on landmark international human rights case',
          'Consultant to the UN High Commissioner for Refugees',
          'Published author on international humanitarian law',
          'Human Rights Watch Young Advocate Award'
        ],
        featured: false,
        education: [
          'J.D., Yale Law School',
          'M.A. in International Relations, Georgetown University',
          'B.A. in Political Science, Columbia University'
        ],
        company: 'United Nations Human Rights Council',
        location: 'Geneva, Switzerland',
        email: '<EMAIL>',
        socialLinks: {
          linkedin: 'https://linkedin.com/in/priyapatel'
        },
        contributions: [
          'Advisor to the school\'s Model UN program',
          'Established internship opportunities at human rights organizations',
          'Speaks at school events on international affairs'
        ]
      }
    ];

    const member = alumniData.find(alumni => alumni.id === id);

    if (member) {
      // Convert the mock data to match our Alumni interface
      this.alumni = {
        id: member.id,
        name: member.name,
        graduationYear: member.graduationYear,
        profession: member.profession,
        biography: member.bio,
        achievements: member.achievements.join(', '),
        isFeatured: member.featured,
        isActive: true,
        displayOrder: member.id,
        profileImage: {
          id: member.id,
          fileName: `${member.name.toLowerCase().replace(' ', '-')}.jpg`,
          filePath: member.image,
          mimeType: 'image/jpeg'
        },
        createdAt: new Date(),
        translations: []
      };
      this.loading = false;
    } else {
      this.error = true;
      this.loading = false;
    }
  }

  /**
   * Navigate back to alumni listing
   */
  goBack(): void {
    this.router.navigate(['/alumni']);
  }

  /**
   * Get years since graduation
   */
  getYearsSinceGraduation(): number {
    if (!this.alumni) return 0;
    const currentYear = new Date().getFullYear();
    return currentYear - this.alumni.graduationYear;
  }
}
