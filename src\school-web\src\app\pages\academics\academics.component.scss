// Hero Section
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('/assets/images/academics-hero.jpg');
  background-size: cover;
  background-position: center;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  margin-bottom: 2rem;

  .hero-content {
    max-width: 800px;
    padding: 0 20px;

    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    .subtitle {
      font-size: 1.5rem;
      font-weight: 300;
    }
  }
}

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Content Sections
.content-section {
  margin-bottom: 4rem;
  padding-top: 2rem;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: #333;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100px;
      height: 4px;
      background-color: #3f51b5;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #555;
  }
}

// Floating Navigation
.floating-nav {
  position: fixed;
  top: 50%;
  right: -200px;
  transform: translateY(-50%);
  background-color: white;
  border-radius: 8px 0 0 8px;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  z-index: 100;

  &.visible {
    right: 0;
  }

  .floating-nav-content {
    padding: 20px;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 1.2rem;
      color: #333;
    }

    .floating-nav-links {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 10px;

        a {
          display: flex;
          align-items: center;
          color: #666;
          text-decoration: none;
          padding: 8px 10px;
          border-radius: 4px;
          transition: background-color 0.2s;
          cursor: pointer;

          &:hover {
            background-color: #f5f5f5;
            color: #3f51b5;
          }

          &.active {
            background-color: #e8eaf6;
            color: #3f51b5;
            font-weight: 500;
          }

          mat-icon {
            margin-right: 10px;
          }
        }
      }
    }
  }
}

// Program Tabs
.program-tabs {
  margin-top: 30px;

  ::ng-deep .mat-mdc-tab-header {
    margin-bottom: 30px;
  }

  .program-content {
    display: flex;
    gap: 40px;
    margin-top: 20px;

    @media (max-width: 768px) {
      flex-direction: column;
    }

    .program-image {
      flex: 1;
      max-width: 500px;

      img {
        width: 100%;
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      }
    }

    .program-details {
      flex: 1;

      h3 {
        font-size: 1.8rem;
        margin-top: 0;
        margin-bottom: 10px;
        color: #333;
      }

      .program-grades {
        color: #666;
        margin-bottom: 20px;
        font-size: 1.1rem;
      }

      .program-description {
        line-height: 1.6;
        margin-bottom: 20px;
        color: #555;
      }

      h4 {
        font-size: 1.3rem;
        margin-bottom: 15px;
        color: #333;
      }

      .feature-list {
        list-style: none;
        padding: 0;
        margin-bottom: 25px;

        li {
          display: flex;
          align-items: flex-start;
          margin-bottom: 10px;

          mat-icon {
            color: #4caf50;
            margin-right: 10px;
            font-size: 20px;
          }

          span {
            line-height: 1.5;
          }
        }
      }

      .learn-more-btn {
        margin-top: 10px;
      }
    }
  }
}

.program-nav-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

// Special Programs
.special-programs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 30px;

  .special-program-card {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    text-align: center;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .program-icon {
      font-size: 40px;
      height: 40px;
      width: 40px;
      color: #3f51b5;
      margin-bottom: 20px;
    }

    h3 {
      font-size: 1.4rem;
      margin-bottom: 15px;
      color: #333;
    }

    p {
      color: #666;
      line-height: 1.6;
    }
  }
}

// Curriculum
.curriculum-container {
  margin-top: 30px;

  h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #333;
  }

  .courses-accordion {
    margin-top: 20px;

    ::ng-deep .mat-expansion-panel {
      margin-bottom: 10px;
      border-radius: 8px;
      overflow: hidden;
    }

    ::ng-deep .mat-expansion-panel-header {
      padding: 20px;
    }

    ::ng-deep .mat-expansion-panel-header-title {
      color: #333;
      font-weight: 500;
    }

    ::ng-deep .mat-expansion-panel-header-description {
      color: #666;
    }

    .course-details {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid #eee;

      .course-id {
        color: #666;
        font-size: 0.9rem;
      }
    }
  }
}

// Resources
.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 30px;

  .resource-card {
    padding: 25px;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    mat-icon {
      font-size: 40px;
      height: 40px;
      width: 40px;
      color: #3f51b5;
      margin-bottom: 15px;
    }

    h3 {
      font-size: 1.3rem;
      margin-bottom: 10px;
      color: #333;
    }

    p {
      color: #666;
      line-height: 1.6;
      margin-bottom: 20px;
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .hero-section {
    height: 350px;

    .hero-content h1 {
      font-size: 2.5rem;
    }
  }

  .floating-nav {
    display: none;
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 300px;

    .hero-content {
      h1 {
        font-size: 2rem;
      }

      .subtitle {
        font-size: 1.2rem;
      }
    }
  }

  .content-section h2 {
    font-size: 2rem;
  }

  .program-content {
    flex-direction: column;

    .program-image {
      margin-bottom: 20px;
    }
  }
}

@media (max-width: 576px) {
  .hero-section {
    height: 250px;

    .hero-content h1 {
      font-size: 1.8rem;
    }
  }

  .special-programs-grid,
  .resources-grid {
    grid-template-columns: 1fr;
  }
}
