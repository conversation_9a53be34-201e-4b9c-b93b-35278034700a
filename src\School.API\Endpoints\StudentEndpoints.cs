using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.DTOs;
using School.Application.Features.Student;
using School.Domain.Enums;

namespace School.API.Endpoints;

public class StudentEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/students").WithTags("Students");

        // Get all students with filtering and pagination
        group.MapGet("/", async ([AsParameters] StudentFilterDto filter, [FromServices] IStudentService studentService) =>
        {
            var (students, totalCount) = await studentService.GetAllStudentsAsync(filter);
            var response = new { TotalCount = totalCount, Items = students };
            return ApiResults.ApiOk(response, "Students retrieved successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("GetAllStudents")
        .WithOpenApi();

        // Get student by ID
        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IStudentService studentService) =>
        {
            var student = await studentService.GetStudentByIdAsync(id);
            if (student == null)
            {
                return ApiResults.ApiNotFound("Student not found");
            }
            return ApiResults.ApiOk(student, "Student retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetStudentById")
        .WithOpenApi();

        // Get student by student ID (school ID)
        group.MapGet("/by-student-id/{studentId}", async ([FromRoute] string studentId, [FromServices] IStudentService studentService) =>
        {
            var student = await studentService.GetStudentByStudentIdAsync(studentId);
            if (student == null)
            {
                return ApiResults.ApiNotFound("Student not found");
            }
            return ApiResults.ApiOk(student, "Student retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetStudentByStudentId")
        .WithOpenApi();

        // Get student by user ID
        group.MapGet("/by-user-id/{userId}", async ([FromRoute] string userId, [FromServices] IStudentService studentService) =>
        {
            var student = await studentService.GetStudentByUserIdAsync(userId);
            if (student == null)
            {
                return ApiResults.ApiNotFound("Student not found");
            }
            return ApiResults.ApiOk(student, "Student retrieved successfully");
        })
        .RequireAuthorization(policyBuilder =>
        {
            // Allow both admin users and students accessing their own data
            policyBuilder.RequireRole("Admin", "Student");
        })
        .WithName("GetStudentByUserId")
        .WithOpenApi();

        // Create new student
        group.MapPost("/", async ([FromBody] CreateStudentDto studentDto, [FromServices] IStudentService studentService) =>
        {
            var studentId = await studentService.CreateStudentAsync(studentDto);
            return ApiResults.ApiCreated($"/api/students/{studentId}", studentId.ToString(), "Student created successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("CreateStudent")
        .WithOpenApi();

        // Update student
        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateStudentDto studentDto, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.UpdateStudentAsync(id, studentDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Student not found");
            }
            return ApiResults.ApiOk("Student updated successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("UpdateStudent")
        .WithOpenApi();

        // Delete student
        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.DeleteStudentAsync(id);
            if (!result)
            {
                return ApiResults.ApiNotFound("Student not found");
            }
            return ApiResults.ApiOk("Student deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteStudent")
        .WithOpenApi();

        // Attendance endpoints
        var attendanceGroup = group.MapGroup("/{studentId}/attendance");

        // Get student attendance
        attendanceGroup.MapGet("/", async ([FromRoute] Guid studentId, [AsParameters] StudentAttendanceFilterDto filter, [FromServices] IStudentService studentService) =>
        {
            var attendance = await studentService.GetStudentAttendanceAsync(studentId, filter.FromDate, filter.ToDate);
            return ApiResults.ApiOk(attendance, "Student attendance retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetStudentAttendance")
        .WithOpenApi();

        // Create attendance record
        attendanceGroup.MapPost("/", async ([FromBody] CreateStudentAttendanceDto attendanceDto, [FromServices] IStudentService studentService) =>
        {
            var attendanceId = await studentService.CreateAttendanceAsync(attendanceDto);
            return ApiResults.ApiCreated($"/api/students/{attendanceDto.StudentId}/attendance/{attendanceId}", attendanceId.ToString(), "Attendance record created successfully");
        })
        .RequireAuthorization("TeacherPolicy")
        .WithName("CreateAttendance")
        .WithOpenApi();

        // Update attendance record
        attendanceGroup.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateStudentAttendanceDto attendanceDto, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.UpdateAttendanceAsync(id, attendanceDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Attendance record not found");
            }
            return ApiResults.ApiOk("Attendance record updated successfully");
        })
        .RequireAuthorization("TeacherPolicy")
        .WithName("UpdateAttendance")
        .WithOpenApi();

        // Delete attendance record
        attendanceGroup.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.DeleteAttendanceAsync(id);
            if (!result)
            {
                return ApiResults.ApiNotFound("Attendance record not found");
            }
            return ApiResults.ApiOk("Attendance record deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteAttendance")
        .WithOpenApi();

        // Fee endpoints
        var feeGroup = group.MapGroup("/{studentId}/fees");

        // Get student fees
        feeGroup.MapGet("/", async ([FromRoute] Guid studentId, [AsParameters] StudentFeeFilterDto filter, [FromServices] IStudentService studentService) =>
        {
            var fees = await studentService.GetStudentFeesAsync(studentId, !string.IsNullOrEmpty(filter.AcademicYear) && int.TryParse(filter.AcademicYear, out var academicYear) ? academicYear : null);
            return ApiResults.ApiOk(fees, "Student fees retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetStudentFees")
        .WithOpenApi();

        // Create fee record
        feeGroup.MapPost("/", async ([FromBody] CreateStudentFeeDto feeDto, [FromServices] IStudentService studentService) =>
        {
            var feeId = await studentService.CreateFeeAsync(feeDto);
            return ApiResults.ApiCreated($"/api/students/{feeDto.StudentId}/fees/{feeId}", feeId.ToString(), "Fee record created successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("CreateFee")
        .WithOpenApi();

        // Update fee record
        feeGroup.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateStudentFeeDto feeDto, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.UpdateFeeAsync(id, feeDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Fee record not found");
            }
            return ApiResults.ApiOk("Fee record updated successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("UpdateFee")
        .WithOpenApi();

        // Delete fee record
        feeGroup.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.DeleteFeeAsync(id);
            if (!result)
            {
                return ApiResults.ApiNotFound("Fee record not found");
            }
            return ApiResults.ApiOk("Fee record deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteFee")
        .WithOpenApi();

        // Result endpoints
        var resultGroup = group.MapGroup("/{studentId}/results");

        // Get student results
        resultGroup.MapGet("/", async ([FromRoute] Guid studentId, [AsParameters] StudentResultFilterDto filter, [FromServices] IStudentService studentService) =>
        {
            var results = await studentService.GetStudentResultsAsync(studentId, filter.AcademicYear, filter.ExamType);
            return ApiResults.ApiOk(results, "Student results retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetStudentResults")
        .WithOpenApi();

        // Calculate GPA
        resultGroup.MapGet("/gpa", async ([FromRoute] Guid studentId, [FromQuery] int academicYear, [FromQuery] ExamType examType, [FromServices] IStudentService studentService) =>
        {
            var gpa = await studentService.CalculateGPAAsync(studentId, academicYear, examType);
            if (!gpa.HasValue)
            {
                return ApiResults.ApiNotFound("No results found for the specified criteria");
            }
            return ApiResults.ApiOk(gpa.Value.ToString(), "GPA calculated successfully");
        })
        .RequireAuthorization()
        .WithName("CalculateStudentGPA")
        .WithOpenApi();

        // Create result record
        resultGroup.MapPost("/", async ([FromBody] CreateStudentResultDto resultDto, [FromServices] IStudentService studentService) =>
        {
            var resultId = await studentService.CreateResultAsync(resultDto);
            return ApiResults.ApiCreated($"/api/students/{resultDto.StudentId}/results/{resultId}", resultId.ToString(), "Result record created successfully");
        })
        .RequireAuthorization("TeacherPolicy")
        .WithName("CreateResult")
        .WithOpenApi();

        // Update result record
        resultGroup.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateStudentResultDto resultDto, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.UpdateResultAsync(id, resultDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Result record not found");
            }
            return ApiResults.ApiOk("Result record updated successfully");
        })
        .RequireAuthorization("TeacherPolicy")
        .WithName("UpdateResult")
        .WithOpenApi();

        // Delete result record
        resultGroup.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.DeleteResultAsync(id);
            if (!result)
            {
                return ApiResults.ApiNotFound("Result record not found");
            }
            return ApiResults.ApiOk("Result record deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteResult")
        .WithOpenApi();

        // Leave endpoints
        var leaveGroup = group.MapGroup("/{studentId}/leaves");

        // Get student leaves
        leaveGroup.MapGet("/", async ([FromRoute] Guid studentId, [AsParameters] StudentLeaveFilterDto filter, [FromServices] IStudentService studentService) =>
        {
            var leaves = await studentService.GetStudentLeavesAsync(studentId, filter.FromDate, filter.ToDate, filter.Status);
            return ApiResults.ApiOk(leaves, "Student leaves retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetStudentLeaves")
        .WithOpenApi();

        // Get leave by ID
        leaveGroup.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IStudentService studentService) =>
        {
            var leave = await studentService.GetLeaveByIdAsync(id);
            if (leave == null)
            {
                return ApiResults.ApiNotFound("Leave record not found");
            }
            return ApiResults.ApiOk(leave, "Leave record retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetLeaveById")
        .WithOpenApi();

        // Create leave application
        leaveGroup.MapPost("/", async ([FromBody] CreateStudentLeaveDto leaveDto, [FromServices] IStudentService studentService) =>
        {
            var leaveId = await studentService.CreateLeaveAsync(leaveDto);
            return ApiResults.ApiCreated($"/api/students/{leaveDto.StudentId}/leaves/{leaveId}", leaveId.ToString(), "Leave application submitted successfully");
        })
        .RequireAuthorization()
        .WithName("CreateLeave")
        .WithOpenApi();

        // Update leave status (for approval/rejection)
        leaveGroup.MapPut("/{id}/status", async ([FromRoute] Guid id, [FromBody] UpdateStudentLeaveDto leaveDto, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.UpdateLeaveStatusAsync(id, leaveDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Leave record not found");
            }
            return ApiResults.ApiOk("Leave status updated successfully");
        })
        .RequireAuthorization("TeacherPolicy")
        .WithName("UpdateLeaveStatus")
        .WithOpenApi();

        // Delete leave application
        leaveGroup.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.DeleteLeaveAsync(id);
            if (!result)
            {
                return ApiResults.ApiNotFound("Leave record not found");
            }
            return ApiResults.ApiOk("Leave application deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteLeave")
        .WithOpenApi();

        // Academic History endpoints
        var academicHistoryGroup = group.MapGroup("/{studentId}/academic-history");

        // Get student academic history
        academicHistoryGroup.MapGet("/", async ([FromRoute] Guid studentId, [FromServices] IStudentService studentService) =>
        {
            var academicHistory = await studentService.GetStudentAcademicHistoryAsync(studentId);
            return ApiResults.ApiOk(academicHistory, "Student academic history retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetStudentAcademicHistory")
        .WithOpenApi();

        // Get academic history by year
        academicHistoryGroup.MapGet("/{academicYear}", async ([FromRoute] Guid studentId, [FromRoute] int academicYear, [FromServices] IStudentService studentService) =>
        {
            var academicHistory = await studentService.GetAcademicHistoryByYearAsync(studentId, academicYear);
            if (academicHistory == null)
            {
                return ApiResults.ApiNotFound("Academic history record not found");
            }
            return ApiResults.ApiOk(academicHistory, "Academic history record retrieved successfully");
        })
        .RequireAuthorization()
        .WithName("GetAcademicHistoryByYear")
        .WithOpenApi();

        // Create academic history record
        academicHistoryGroup.MapPost("/", async ([FromBody] CreateStudentAcademicHistoryDto academicHistoryDto, [FromServices] IStudentService studentService) =>
        {
            var academicHistoryId = await studentService.CreateAcademicHistoryAsync(academicHistoryDto);
            return ApiResults.ApiCreated($"/api/students/{academicHistoryDto.StudentId}/academic-history/{academicHistoryId}", academicHistoryId.ToString(), "Academic history record created successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("CreateAcademicHistory")
        .WithOpenApi();

        // Update academic history record
        academicHistoryGroup.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateStudentAcademicHistoryDto academicHistoryDto, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.UpdateAcademicHistoryAsync(id, academicHistoryDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Academic history record not found");
            }
            return ApiResults.ApiOk("Academic history record updated successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("UpdateAcademicHistory")
        .WithOpenApi();

        // Promote student
        group.MapPost("/{studentId}/promote", async ([FromRoute] Guid studentId, [FromQuery] int fromAcademicYear, [FromQuery] int toAcademicYear, [FromQuery] int toGrade, [FromQuery] string toSection, [FromQuery] int toRollNumber, [FromQuery] string toStudentId, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.PromoteStudentAsync(studentId, fromAcademicYear, toAcademicYear, toGrade, toSection, toRollNumber, toStudentId);
            if (!result)
            {
                return ApiResults.ApiNotFound("Student not found or promotion failed");
            }
            return ApiResults.ApiOk("Student promoted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("PromoteStudent")
        .WithOpenApi();

        // Parent association endpoints
        var parentGroup = group.MapGroup("/{studentId}/parents");

        // Add parent to student
        parentGroup.MapPost("/", async ([FromRoute] Guid studentId, [FromBody] CreateStudentParentDto parentDto, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.AddParentAsync(studentId, parentDto);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Parent already associated with this student or parent/student not found");
            }
            return ApiResults.ApiOk("Parent added to student successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("AddParentToStudent")
        .WithOpenApi();

        // Update parent relation
        parentGroup.MapPut("/{parentId}", async ([FromRoute] Guid studentId, [FromRoute] Guid parentId, [FromQuery] ParentRelationType relationType, [FromQuery] bool isPrimaryContact, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.UpdateParentRelationAsync(studentId, parentId, relationType, isPrimaryContact);
            if (!result)
            {
                return ApiResults.ApiNotFound("Student-parent relationship not found");
            }
            return ApiResults.ApiOk("Student-parent relationship updated successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("UpdateStudentParentRelationFromStudent")
        .WithOpenApi();

        // Remove parent from student
        parentGroup.MapDelete("/{parentId}", async ([FromRoute] Guid studentId, [FromRoute] Guid parentId, [FromServices] IStudentService studentService) =>
        {
            var result = await studentService.RemoveParentAsync(studentId, parentId);
            if (!result)
            {
                return ApiResults.ApiNotFound("Student-parent relationship not found");
            }
            return ApiResults.ApiOk("Parent removed from student successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("RemoveParentFromStudent")
        .WithOpenApi();
    }
}
