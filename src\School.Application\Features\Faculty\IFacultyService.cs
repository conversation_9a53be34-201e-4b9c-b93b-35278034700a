using School.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace School.Application.Features.Faculty
{
    public interface IFacultyService
    {
        Task<(IEnumerable<FacultyDto> Faculties, int TotalCount)> GetAllFacultiesAsync(FacultyFilterDto filter);
        Task<FacultyDto?> GetFacultyByIdAsync(Guid id);
        Task<Guid> CreateFacultyAsync(FacultyCreateDto facultyDto);
        Task<bool> UpdateFacultyAsync(Guid id, FacultyUpdateDto facultyDto);
        Task<bool> DeleteFacultyAsync(Guid id);
    }
}