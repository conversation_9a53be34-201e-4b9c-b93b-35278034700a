.notice-list-container {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
  }
  
  .filter-card {
    margin-bottom: 20px;
    
    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: center;
      
      mat-form-field {
        flex: 1;
        min-width: 200px;
      }
      
      .filter-actions {
        display: flex;
        gap: 8px;
        margin-left: auto;
      }
    }
  }
  
  .loading-container, .error-container, .no-results-container {
    display: flex;
    justify-content: center;
    padding: 40px 0;
  }
  
  .error-card, .no-results-card {
    max-width: 500px;
    width: 100%;
    
    mat-card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      
      mat-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        margin-bottom: 16px;
      }
      
      p {
        margin-bottom: 16px;
        font-size: 16px;
      }
    }
  }
  
  .table-container {
    overflow-x: auto;
    
    .notices-table {
      width: 100%;
      
      .mat-column-actions {
        width: 120px;
        text-align: right;
      }
      
      .mat-column-priority, .mat-column-isActive {
        width: 120px;
      }
    }
  }
  
  // Priority chip styles
  .priority-low {
    background-color: #8bc34a;
    color: white;
  }
  
  .priority-medium {
    background-color: #ffc107;
    color: rgba(0, 0, 0, 0.87);
  }
  
  .priority-high {
    background-color: #ff9800;
    color: white;
  }
  
  .priority-urgent {
    background-color: #f44336;
    color: white;
  }
}
