using System;
using System.ComponentModel.DataAnnotations;

namespace School.Application.DTOs
{
    public class ClubAdvisorCreateDto
    {
        public int? FacultyId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        [EmailAddress]
        public string Email { get; set; }
        
        [Phone]
        public string Phone { get; set; }
        
        public int DisplayOrder { get; set; }
    }
}
