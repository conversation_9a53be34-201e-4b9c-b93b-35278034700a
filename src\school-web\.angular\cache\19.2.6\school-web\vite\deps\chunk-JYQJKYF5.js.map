{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/option-105bf9fa.mjs", "../../../../../../node_modules/@angular/material/fesm2022/index-2b66fc5f.mjs"], "sourcesContent": ["import { _IdGenerator } from '@angular/cdk/a11y';\nimport { ENTER, SPACE, hasModifier<PERSON>ey } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ElementRef, ChangeDetectorRef, EventEmitter, isSignal, Output, ViewChild } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { M as MatRipple } from './ripple-33861831.mjs';\nimport { M as MatPseudoCheckbox } from './pseudo-checkbox-18f8a087.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-afbfe518.mjs';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\n\n/**\n * Injection token used to provide the parent component to options.\n */\nconst _c0 = [\"*\", [[\"mat-option\"], [\"ng-container\"]]];\nconst _c1 = [\"*\", \"mat-option, ng-container\"];\nconst _c2 = [\"text\"];\nconst _c3 = [[[\"mat-icon\"]], \"*\"];\nconst _c4 = [\"mat-icon\", \"*\"];\nfunction MatOption_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled)(\"state\", ctx_r0.selected ? \"checked\" : \"unchecked\");\n  }\n}\nfunction MatOption_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled);\n  }\n}\nfunction MatOption_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r0.group.label, \")\");\n  }\n}\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup {\n  /** Label for the option group. */\n  label;\n  /** whether the option group is disabled. */\n  disabled = false;\n  /** Unique id for the underlying label. */\n  _labelId = inject(_IdGenerator).getId('mat-optgroup-label-');\n  /** Whether the group is in inert a11y mode. */\n  _inert;\n  constructor() {\n    const parent = inject(MAT_OPTION_PARENT_COMPONENT, {\n      optional: true\n    });\n    this._inert = parent?.inertGroups ?? false;\n  }\n  static ɵfac = function MatOptgroup_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatOptgroup)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatOptgroup,\n    selectors: [[\"mat-optgroup\"]],\n    hostAttrs: [1, \"mat-mdc-optgroup\"],\n    hostVars: 3,\n    hostBindings: function MatOptgroup_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx._inert ? null : \"group\")(\"aria-disabled\", ctx._inert ? null : ctx.disabled.toString())(\"aria-labelledby\", ctx._inert ? null : ctx._labelId);\n      }\n    },\n    inputs: {\n      label: \"label\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    exportAs: [\"matOptgroup\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_OPTGROUP,\n      useExisting: MatOptgroup\n    }])],\n    ngContentSelectors: _c1,\n    decls: 5,\n    vars: 4,\n    consts: [[\"role\", \"presentation\", 1, \"mat-mdc-optgroup-label\", 3, \"id\"], [1, \"mdc-list-item__primary-text\"]],\n    template: function MatOptgroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelementStart(0, \"span\", 0)(1, \"span\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵprojection(3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵprojection(4, 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-list-item--disabled\", ctx.disabled);\n        i0.ɵɵproperty(\"id\", ctx._labelId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\"\", ctx.label, \" \");\n      }\n    },\n    styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptgroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-optgroup',\n      exportAs: 'matOptgroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-optgroup',\n        '[attr.role]': '_inert ? null : \"group\"',\n        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n        '[attr.aria-labelledby]': '_inert ? null : _labelId'\n      },\n      providers: [{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }],\n      template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\",\n      styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\"]\n    }]\n  }], () => [], {\n    label: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n  source;\n  isUserInput;\n  constructor(/** Reference to the option that emitted the event. */\n  source, /** Whether the change in the option's value was a result of a user action. */\n  isUserInput = false) {\n    this.source = source;\n    this.isUserInput = isUserInput;\n  }\n}\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption {\n  _element = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _parent = inject(MAT_OPTION_PARENT_COMPONENT, {\n    optional: true\n  });\n  group = inject(MAT_OPTGROUP, {\n    optional: true\n  });\n  _signalDisableRipple = false;\n  _selected = false;\n  _active = false;\n  _disabled = false;\n  _mostRecentViewValue = '';\n  /** Whether the wrapping component is in multiple selection mode. */\n  get multiple() {\n    return this._parent && this._parent.multiple;\n  }\n  /** Whether or not the option is currently selected. */\n  get selected() {\n    return this._selected;\n  }\n  /** The form value of the option. */\n  value;\n  /** The unique ID of the option. */\n  id = inject(_IdGenerator).getId('mat-option-');\n  /** Whether the option is disabled. */\n  get disabled() {\n    return this.group && this.group.disabled || this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  /** Whether ripples for the option are disabled. */\n  get disableRipple() {\n    return this._signalDisableRipple ? this._parent.disableRipple() : !!this._parent?.disableRipple;\n  }\n  /** Whether to display checkmark for single-selection. */\n  get hideSingleSelectionIndicator() {\n    return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n  }\n  /** Event emitted when the option is selected or deselected. */\n  // tslint:disable-next-line:no-output-on-prefix\n  onSelectionChange = new EventEmitter();\n  /** Element containing the option's text. */\n  _text;\n  /** Emits when the state of the option changes and any parents have to be notified. */\n  _stateChanges = new Subject();\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_StructuralStylesLoader);\n    styleLoader.load(_VisuallyHiddenLoader);\n    this._signalDisableRipple = !!this._parent && isSignal(this._parent.disableRipple);\n  }\n  /**\n   * Whether or not the option is currently active and ready to be selected.\n   * An active option displays styles as if it is focused, but the\n   * focus is actually retained somewhere else. This comes in handy\n   * for components like autocomplete where focus must remain on the input.\n   */\n  get active() {\n    return this._active;\n  }\n  /**\n   * The displayed value of the option. It is necessary to show the selected option in the\n   * select's trigger.\n   */\n  get viewValue() {\n    // TODO(kara): Add input property alternative for node envs.\n    return (this._text?.nativeElement.textContent || '').trim();\n  }\n  /** Selects the option. */\n  select(emitEvent = true) {\n    if (!this._selected) {\n      this._selected = true;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Deselects the option. */\n  deselect(emitEvent = true) {\n    if (this._selected) {\n      this._selected = false;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Sets focus onto this option. */\n  focus(_origin, options) {\n    // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n    // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n    const element = this._getHostElement();\n    if (typeof element.focus === 'function') {\n      element.focus(options);\n    }\n  }\n  /**\n   * This method sets display styles on the option to make it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setActiveStyles() {\n    if (!this._active) {\n      this._active = true;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method removes display styles on the option that made it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setInactiveStyles() {\n    if (this._active) {\n      this._active = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    return this.viewValue;\n  }\n  /** Ensures the option is selected when activated from the keyboard. */\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n      this._selectViaInteraction();\n      // Prevent the page from scrolling down and form submits.\n      event.preventDefault();\n    }\n  }\n  /**\n   * `Selects the option while indicating the selection came from the user. Used to\n   * determine if the select's view -> model callback should be invoked.`\n   */\n  _selectViaInteraction() {\n    if (!this.disabled) {\n      this._selected = this.multiple ? !this._selected : true;\n      this._changeDetectorRef.markForCheck();\n      this._emitSelectionChangeEvent(true);\n    }\n  }\n  /** Returns the correct tabindex for the option depending on disabled state. */\n  // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n  // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n  // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Gets the host DOM element. */\n  _getHostElement() {\n    return this._element.nativeElement;\n  }\n  ngAfterViewChecked() {\n    // Since parent components could be using the option's label to display the selected values\n    // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n    // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n    // relatively cheap, however we still limit them only to selected options in order to avoid\n    // hitting the DOM too often.\n    if (this._selected) {\n      const viewValue = this.viewValue;\n      if (viewValue !== this._mostRecentViewValue) {\n        if (this._mostRecentViewValue) {\n          this._stateChanges.next();\n        }\n        this._mostRecentViewValue = viewValue;\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  /** Emits the selection change event. */\n  _emitSelectionChangeEvent(isUserInput = false) {\n    this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n  }\n  static ɵfac = function MatOption_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatOption)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatOption,\n    selectors: [[\"mat-option\"]],\n    viewQuery: function MatOption_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c2, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._text = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"option\", 1, \"mat-mdc-option\", \"mdc-list-item\"],\n    hostVars: 11,\n    hostBindings: function MatOption_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatOption_click_HostBindingHandler() {\n          return ctx._selectViaInteraction();\n        })(\"keydown\", function MatOption_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"aria-selected\", ctx.selected)(\"aria-disabled\", ctx.disabled.toString());\n        i0.ɵɵclassProp(\"mdc-list-item--selected\", ctx.selected)(\"mat-mdc-option-multiple\", ctx.multiple)(\"mat-mdc-option-active\", ctx.active)(\"mdc-list-item--disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      value: \"value\",\n      id: \"id\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    outputs: {\n      onSelectionChange: \"onSelectionChange\"\n    },\n    exportAs: [\"matOption\"],\n    ngContentSelectors: _c4,\n    decls: 8,\n    vars: 5,\n    consts: [[\"text\", \"\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\", \"state\"], [1, \"mdc-list-item__primary-text\"], [\"state\", \"checked\", \"aria-hidden\", \"true\", \"appearance\", \"minimal\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\"], [1, \"cdk-visually-hidden\"], [\"aria-hidden\", \"true\", \"mat-ripple\", \"\", 1, \"mat-mdc-option-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\"]],\n    template: function MatOption_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c3);\n        i0.ɵɵtemplate(0, MatOption_Conditional_0_Template, 1, 2, \"mat-pseudo-checkbox\", 1);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementStart(2, \"span\", 2, 0);\n        i0.ɵɵprojection(4, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, MatOption_Conditional_5_Template, 1, 1, \"mat-pseudo-checkbox\", 3)(6, MatOption_Conditional_6_Template, 2, 1, \"span\", 4);\n        i0.ɵɵelement(7, \"div\", 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.multiple ? 0 : -1);\n        i0.ɵɵadvance(5);\n        i0.ɵɵconditional(!ctx.multiple && ctx.selected && !ctx.hideSingleSelectionIndicator ? 5 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.group && ctx.group._inert ? 6 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disabled || ctx.disableRipple);\n      }\n    },\n    dependencies: [MatPseudoCheckbox, MatRipple],\n    styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-option',\n      exportAs: 'matOption',\n      host: {\n        'role': 'option',\n        '[class.mdc-list-item--selected]': 'selected',\n        '[class.mat-mdc-option-multiple]': 'multiple',\n        '[class.mat-mdc-option-active]': 'active',\n        '[class.mdc-list-item--disabled]': 'disabled',\n        '[id]': 'id',\n        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n        // [WAI ARIA Listbox authoring practices guide](\n        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n        // selected option has either aria-selected or aria-checked  set to true. All options that are\n        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n        // aria-selected implementation of Chips and List components.\n        //\n        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n        // every option as \"selected\" (#21491).\n        '[attr.aria-selected]': 'selected',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '(click)': '_selectViaInteraction()',\n        '(keydown)': '_handleKeydown($event)',\n        'class': 'mat-mdc-option mdc-list-item'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatPseudoCheckbox, MatRipple],\n      template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n@if (multiple) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n        aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n}\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n}\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n@if (group && group._inert) {\\n    <span class=\\\"cdk-visually-hidden\\\">({{ group.label }})</span>\\n}\\n\\n<div class=\\\"mat-mdc-option-ripple mat-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\",\n      styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\"]\n    }]\n  }], () => [], {\n    value: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    onSelectionChange: [{\n      type: Output\n    }],\n    _text: [{\n      type: ViewChild,\n      args: ['text', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n    return groupCounter;\n  }\n  return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n  return currentScrollPosition;\n}\nexport { MAT_OPTION_PARENT_COMPONENT as M, _countGroupLabelsBeforeOption as _, MatOption as a, MAT_OPTGROUP as b, MatOptionSelectionChange as c, _getOptionScrollPosition as d, MatOptgroup as e };\n", "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatRippleModule } from './index-91512b69.mjs';\nimport { M as MatCommonModule } from './common-module-5a9c16bb.mjs';\nimport { a as MatOption, e as MatOptgroup } from './option-105bf9fa.mjs';\nimport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-6293765e.mjs';\nclass MatOptionModule {\n  static ɵfac = function MatOptionModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatOptionModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatOptionModule,\n    imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n    exports: [MatOption, MatOptgroup]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n      exports: [MatOption, MatOptgroup]\n    }]\n  }], null, null);\n})();\nexport { MatOptionModule as M };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,IAAM,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,GAAG,CAAC,cAAc,CAAC,CAAC;AACpD,IAAM,MAAM,CAAC,KAAK,0BAA0B;AAC5C,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG;AAChC,IAAM,MAAM,CAAC,YAAY,GAAG;AAC5B,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,uBAAuB,CAAC;AAAA,EAC1C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,SAAS,OAAO,WAAW,YAAY,WAAW;AAAA,EAC/F;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,uBAAuB,CAAC;AAAA,EAC1C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,QAAQ;AAAA,EAC3C;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,MAAM,OAAO,GAAG;AAAA,EACpD;AACF;AACA,IAAM,8BAA8B,IAAI,eAAe,6BAA6B;AA0BpF,IAAM,eAAe,IAAI,eAAe,aAAa;AAIrD,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA,EAEhB;AAAA;AAAA,EAEA,WAAW;AAAA;AAAA,EAEX,WAAW,OAAO,YAAY,EAAE,MAAM,qBAAqB;AAAA;AAAA,EAE3D;AAAA,EACA,cAAc;AACZ,UAAM,SAAS,OAAO,6BAA6B;AAAA,MACjD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,SAAS,QAAQ,eAAe;AAAA,EACvC;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,GAAG,kBAAkB;AAAA,IACjC,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,QAAQ,IAAI,SAAS,OAAO,OAAO,EAAE,iBAAiB,IAAI,SAAS,OAAO,IAAI,SAAS,SAAS,CAAC,EAAE,mBAAmB,IAAI,SAAS,OAAO,IAAI,QAAQ;AAAA,MACvK;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,gBAAgB,GAAG,0BAA0B,GAAG,IAAI,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,IAC3G,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC5C,QAAG,OAAO,CAAC;AACX,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,EAAE;AAClB,QAAG,aAAa,GAAG,CAAC;AAAA,MACtB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,2BAA2B,IAAI,QAAQ;AACtD,QAAG,WAAW,MAAM,IAAI,QAAQ;AAChC,QAAG,UAAU,CAAC;AACd,QAAG,mBAAmB,IAAI,IAAI,OAAO,GAAG;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,y9BAAy9B;AAAA,IACl+B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,0BAA0B;AAAA,MAC5B;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,UAAU;AAAA,MACV,QAAQ,CAAC,y9BAAy9B;AAAA,IACp+B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,2BAAN,MAA+B;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,YACA,QACA,cAAc,OAAO;AACnB,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACrB;AACF;AAIA,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,WAAW,OAAO,UAAU;AAAA,EAC5B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,UAAU,OAAO,6BAA6B;AAAA,IAC5C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,QAAQ,OAAO,cAAc;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,uBAAuB;AAAA,EACvB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,uBAAuB;AAAA;AAAA,EAEvB,IAAI,WAAW;AACb,WAAO,KAAK,WAAW,KAAK,QAAQ;AAAA,EACtC;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,KAAK,OAAO,YAAY,EAAE,MAAM,aAAa;AAAA;AAAA,EAE7C,IAAI,WAAW;AACb,WAAO,KAAK,SAAS,KAAK,MAAM,YAAY,KAAK;AAAA,EACnD;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK,uBAAuB,KAAK,QAAQ,cAAc,IAAI,CAAC,CAAC,KAAK,SAAS;AAAA,EACpF;AAAA;AAAA,EAEA,IAAI,+BAA+B;AACjC,WAAO,CAAC,EAAE,KAAK,WAAW,KAAK,QAAQ;AAAA,EACzC;AAAA;AAAA;AAAA,EAGA,oBAAoB,IAAI,aAAa;AAAA;AAAA,EAErC;AAAA;AAAA,EAEA,gBAAgB,IAAI,QAAQ;AAAA,EAC5B,cAAc;AACZ,UAAM,cAAc,OAAO,sBAAsB;AACjD,gBAAY,KAAK,uBAAuB;AACxC,gBAAY,KAAK,qBAAqB;AACtC,SAAK,uBAAuB,CAAC,CAAC,KAAK,WAAW,SAAS,KAAK,QAAQ,aAAa;AAAA,EACnF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AAEd,YAAQ,KAAK,OAAO,cAAc,eAAe,IAAI,KAAK;AAAA,EAC5D;AAAA;AAAA,EAEA,OAAO,YAAY,MAAM;AACvB,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY;AACjB,WAAK,mBAAmB,aAAa;AACrC,UAAI,WAAW;AACb,aAAK,0BAA0B;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,SAAS,YAAY,MAAM;AACzB,QAAI,KAAK,WAAW;AAClB,WAAK,YAAY;AACjB,WAAK,mBAAmB,aAAa;AACrC,UAAI,WAAW;AACb,aAAK,0BAA0B;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,MAAM,SAAS,SAAS;AAGtB,UAAM,UAAU,KAAK,gBAAgB;AACrC,QAAI,OAAO,QAAQ,UAAU,YAAY;AACvC,cAAQ,MAAM,OAAO;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AACf,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,QAAI,KAAK,SAAS;AAChB,WAAK,UAAU;AACf,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,SAAK,MAAM,YAAY,SAAS,MAAM,YAAY,UAAU,CAAC,eAAe,KAAK,GAAG;AAClF,WAAK,sBAAsB;AAE3B,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,YAAY,KAAK,WAAW,CAAC,KAAK,YAAY;AACnD,WAAK,mBAAmB,aAAa;AACrC,WAAK,0BAA0B,IAAI;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK,WAAW,OAAO;AAAA,EAChC;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,qBAAqB;AAMnB,QAAI,KAAK,WAAW;AAClB,YAAM,YAAY,KAAK;AACvB,UAAI,cAAc,KAAK,sBAAsB;AAC3C,YAAI,KAAK,sBAAsB;AAC7B,eAAK,cAAc,KAAK;AAAA,QAC1B;AACA,aAAK,uBAAuB;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA;AAAA,EAEA,0BAA0B,cAAc,OAAO;AAC7C,SAAK,kBAAkB,KAAK,IAAI,yBAAyB,MAAM,WAAW,CAAC;AAAA,EAC7E;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,WAAW,SAAS,gBAAgB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,QAAQ,UAAU,GAAG,kBAAkB,eAAe;AAAA,IAClE,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,qCAAqC;AACnE,iBAAO,IAAI,sBAAsB;AAAA,QACnC,CAAC,EAAE,WAAW,SAAS,qCAAqC,QAAQ;AAClE,iBAAO,IAAI,eAAe,MAAM;AAAA,QAClC,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,MAAM,IAAI,EAAE;AAC9B,QAAG,YAAY,iBAAiB,IAAI,QAAQ,EAAE,iBAAiB,IAAI,SAAS,SAAS,CAAC;AACtF,QAAG,YAAY,2BAA2B,IAAI,QAAQ,EAAE,2BAA2B,IAAI,QAAQ,EAAE,yBAAyB,IAAI,MAAM,EAAE,2BAA2B,IAAI,QAAQ;AAAA,MAC/K;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,IAAI;AAAA,MACJ,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,SAAS;AAAA,MACP,mBAAmB;AAAA,IACrB;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,eAAe,QAAQ,GAAG,kCAAkC,GAAG,YAAY,OAAO,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,SAAS,WAAW,eAAe,QAAQ,cAAc,WAAW,GAAG,kCAAkC,GAAG,UAAU,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,eAAe,QAAQ,cAAc,IAAI,GAAG,yBAAyB,uBAAuB,GAAG,oBAAoB,mBAAmB,CAAC;AAAA,IAC/a,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,uBAAuB,CAAC;AACjF,QAAG,aAAa,CAAC;AACjB,QAAG,eAAe,GAAG,QAAQ,GAAG,CAAC;AACjC,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,uBAAuB,CAAC,EAAE,GAAG,kCAAkC,GAAG,GAAG,QAAQ,CAAC;AACvI,QAAG,UAAU,GAAG,OAAO,CAAC;AAAA,MAC1B;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,WAAW,IAAI,EAAE;AACtC,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,CAAC,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,+BAA+B,IAAI,EAAE;AAC5F,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,SAAS,IAAI,MAAM,SAAS,IAAI,EAAE;AACvD,QAAG,UAAU;AACb,QAAG,WAAW,oBAAoB,IAAI,gBAAgB,CAAC,EAAE,qBAAqB,IAAI,YAAY,IAAI,aAAa;AAAA,MACjH;AAAA,IACF;AAAA,IACA,cAAc,CAAC,mBAAmB,SAAS;AAAA,IAC3C,QAAQ,CAAC,2uHAA+uH;AAAA,IACxvH,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,mCAAmC;AAAA,QACnC,mCAAmC;AAAA,QACnC,iCAAiC;AAAA,QACjC,mCAAmC;AAAA,QACnC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUR,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,WAAW;AAAA,QACX,aAAa;AAAA,QACb,SAAS;AAAA,MACX;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,mBAAmB,SAAS;AAAA,MACtC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,2uHAA+uH;AAAA,IAC1vH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,SAAS,8BAA8B,aAAa,SAAS,cAAc;AACzE,MAAI,aAAa,QAAQ;AACvB,QAAI,eAAe,QAAQ,QAAQ;AACnC,QAAI,SAAS,aAAa,QAAQ;AAClC,QAAI,eAAe;AACnB,aAAS,IAAI,GAAG,IAAI,cAAc,GAAG,KAAK;AACxC,UAAI,aAAa,CAAC,EAAE,SAAS,aAAa,CAAC,EAAE,UAAU,OAAO,YAAY,GAAG;AAC3E;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AASA,SAAS,yBAAyB,cAAc,cAAc,uBAAuB,aAAa;AAChG,MAAI,eAAe,uBAAuB;AACxC,WAAO;AAAA,EACT;AACA,MAAI,eAAe,eAAe,wBAAwB,aAAa;AACrE,WAAO,KAAK,IAAI,GAAG,eAAe,cAAc,YAAY;AAAA,EAC9D;AACA,SAAO;AACT;;;AC/gBA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,iBAAiB,yBAAyB,WAAW,WAAW;AAAA,IAC3F,SAAS,CAAC,WAAW,WAAW;AAAA,EAClC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,iBAAiB,uBAAuB;AAAA,EACrE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB,yBAAyB,WAAW,WAAW;AAAA,MAC3F,SAAS,CAAC,WAAW,WAAW;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}