using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class StudentParent : BaseEntity
{
    public Guid StudentId { get; set; }
    public Guid ParentId { get; set; }
    public ParentRelationType RelationType { get; set; }
    public bool IsPrimaryContact { get; set; }
    
    // Navigation properties
    public Student Student { get; set; } = null!;
    public Parent Parent { get; set; } = null!;
    public DateTime UpdatedAt { get; set; }
}
