using System.ComponentModel.DataAnnotations;

namespace School.Application.DTOs.TenantSetup;

/// <summary>
/// DTO for tenant setup status
/// </summary>
public class TenantSetupStatusDto
{
    public Guid TenantId { get; set; }
    public bool IsSetupComplete { get; set; }
    public List<string> CompletedSteps { get; set; } = new();
    public string CurrentStep { get; set; } = string.Empty;
    public int SetupProgress { get; set; }
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// DTO for school profile setup
/// </summary>
public class SchoolProfileSetupDto
{
    [Required]
    [StringLength(200)]
    public string SchoolName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string SchoolType { get; set; } = string.Empty;

    [Required]
    public AddressDto Address { get; set; } = new();

    [Required]
    public ContactInfoDto ContactInfo { get; set; } = new();

    [Required]
    public AcademicInfoDto AcademicInfo { get; set; } = new();

    [Required]
    public SettingsDto Settings { get; set; } = new();
}

/// <summary>
/// DTO for address information
/// </summary>
public class AddressDto
{
    [Required]
    [StringLength(500)]
    public string Street { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string City { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string State { get; set; } = string.Empty;

    [Required]
    [StringLength(20)]
    public string PostalCode { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string Country { get; set; } = string.Empty;
}

/// <summary>
/// DTO for contact information
/// </summary>
public class ContactInfoDto
{
    [Required]
    [Phone]
    [StringLength(20)]
    public string Phone { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    [StringLength(200)]
    public string Email { get; set; } = string.Empty;

    [Url]
    [StringLength(200)]
    public string? Website { get; set; }
}

/// <summary>
/// DTO for academic information
/// </summary>
public class AcademicInfoDto
{
    [Required]
    [Range(1800, 2100)]
    public int EstablishedYear { get; set; }

    [Required]
    [StringLength(100)]
    public string AffiliationBoard { get; set; } = string.Empty;

    [StringLength(50)]
    public string? SchoolCode { get; set; }
}

/// <summary>
/// DTO for settings information
/// </summary>
public class SettingsDto
{
    [Required]
    [StringLength(100)]
    public string TimeZone { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string Currency { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string Language { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string AcademicYearStart { get; set; } = string.Empty; // MM-DD format
}

/// <summary>
/// DTO for academic structure setup
/// </summary>
public class AcademicStructureSetupDto
{
    [Required]
    [StringLength(10)]
    public string AcademicYearStart { get; set; } = string.Empty; // MM-DD format

    public bool HasTerms { get; set; } = true;

    [Range(1, 4)]
    public int TermCount { get; set; } = 3;

    [Required]
    [StringLength(50)]
    public string GradeStructure { get; set; } = string.Empty; // e.g., "K12", "1-10", etc.
}

/// <summary>
/// DTO for user roles setup
/// </summary>
public class UserRolesSetupDto
{
    public bool EnableCustomRoles { get; set; } = false;
    public bool DefaultRoles { get; set; } = true;
}

/// <summary>
/// DTO for initial users setup
/// </summary>
public class InitialUsersSetupDto
{
    public bool CreateInitialUsers { get; set; } = false;

    [EmailAddress]
    [StringLength(200)]
    public string? PrincipalEmail { get; set; }

    [EmailAddress]
    [StringLength(200)]
    public string? VicePrincipalEmail { get; set; }

    [EmailAddress]
    [StringLength(200)]
    public string? AdminEmail { get; set; }
}

/// <summary>
/// DTO for system settings setup
/// </summary>
public class SystemSettingsSetupDto
{
    [Required]
    [StringLength(100)]
    public string TimeZone { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string Currency { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string Language { get; set; } = string.Empty;

    public bool EnableNotifications { get; set; } = true;
    public bool EnableSMS { get; set; } = false;
    public bool EnableEmail { get; set; } = true;
}

/// <summary>
/// DTO for marking a step as complete
/// </summary>
public class MarkStepCompleteDto
{
    [Required]
    [StringLength(100)]
    public string StepId { get; set; } = string.Empty;
}

/// <summary>
/// DTO for skipping a step
/// </summary>
public class SkipStepDto
{
    [Required]
    [StringLength(100)]
    public string StepId { get; set; } = string.Empty;
}
