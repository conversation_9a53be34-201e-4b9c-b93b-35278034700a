import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { FacultyService } from '../../../core/services/faculty.service';
import { AuthService } from '../../../core/services/auth.service';
import { FacultyDetail, FacultySubject } from '../../../core/models/faculty.model';
import { RecordAttendanceDialogComponent } from './record-attendance-dialog/record-attendance-dialog.component';

@Component({
  selector: 'app-faculty-attendance',
  templateUrl: './faculty-attendance.component.html',
  styleUrls: ['./faculty-attendance.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatTableModule,
    MatDialogModule,
    MatSnackBarModule
  ]
})
export class FacultyAttendanceComponent implements OnInit {
  faculty: FacultyDetail | null = null;
  subjects: FacultySubject[] = [];
  filterForm: FormGroup;

  loading = {
    faculty: true,
    subjects: false,
    attendance: false
  };

  error = {
    faculty: false,
    subjects: false,
    attendance: false
  };

  attendanceData: any[] = [];
  displayedColumns: string[] = ['rollNumber', 'name', 'status', 'remarks'];

  constructor(
    private facultyService: FacultyService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    const currentDate = new Date();

    this.filterForm = this.formBuilder.group({
      grade: ['', Validators.required],
      section: ['', Validators.required],
      date: [currentDate, Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadFacultyData();
  }

  loadFacultyData(): void {
    this.loading.faculty = true;
    this.error.faculty = false;

    // Get the current user ID from the auth service
    const userId = this.authService.getCurrentUser()?.id;

    if (userId) {
      // If we have a user ID, get the faculty by user ID
      this.facultyService.getFacultyByUserId(userId).subscribe({
        next: (faculty) => {
          this.faculty = faculty as FacultyDetail;
          this.loading.faculty = false;
          this.loadSubjects();

          // If faculty is a class teacher, pre-select their assigned class
          if (faculty.isClassTeacher && faculty.assignedGrade && faculty.assignedSection) {
            this.filterForm.patchValue({
              grade: faculty.assignedGrade,
              section: faculty.assignedSection
            });
          }
        },
        error: (err) => {
          console.error('Error loading faculty data:', err);
          this.error.faculty = true;
          this.loading.faculty = false;
          this.snackBar.open('Failed to load faculty data', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
    } else {
      // Fallback to mock data if no user ID is available
      this.facultyService.getFaculty(1).subscribe({
        next: (faculty) => {
          this.faculty = faculty;
          this.loading.faculty = false;
          this.loadSubjects();

          // If faculty is a class teacher, pre-select their assigned class
          if (faculty.isClassTeacher && faculty.assignedGrade && faculty.assignedSection) {
            this.filterForm.patchValue({
              grade: faculty.assignedGrade,
              section: faculty.assignedSection
            });
          }
        },
        error: (err) => {
          console.error('Error loading faculty data:', err);
          this.error.faculty = true;
          this.loading.faculty = false;
          this.snackBar.open('Failed to load faculty data', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  loadSubjects(): void {
    if (!this.faculty) return;

    this.loading.subjects = true;
    this.error.subjects = false;

    const currentYear = new Date().getFullYear();

    this.facultyService.getFacultySubjects(this.faculty.id, currentYear)
      .subscribe({
        next: (subjects) => {
          this.subjects = subjects;
          this.loading.subjects = false;
        },
        error: (err) => {
          console.error('Error loading subjects:', err);
          this.error.subjects = true;
          this.loading.subjects = false;
          this.snackBar.open('Failed to load assigned subjects', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  loadAttendance(): void {
    if (!this.faculty || !this.filterForm.valid) return;

    this.loading.attendance = true;
    this.error.attendance = false;

    const formValues = this.filterForm.value;

    this.facultyService.getClassAttendance(
      this.faculty.id,
      formValues.grade,
      formValues.section,
      formValues.date
    ).subscribe({
      next: (data) => {
        this.attendanceData = data;
        this.loading.attendance = false;
      },
      error: (err) => {
        console.error('Error loading attendance data:', err);
        this.error.attendance = true;
        this.loading.attendance = false;
        this.snackBar.open('Failed to load attendance data', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  openRecordAttendanceDialog(): void {
    if (!this.faculty || !this.filterForm.valid) {
      this.snackBar.open('Please select grade, section, and date first', 'Close', {
        duration: 3000
      });
      return;
    }

    const formValues = this.filterForm.value;

    const dialogRef = this.dialog.open(RecordAttendanceDialogComponent, {
      width: '800px',
      data: {
        facultyId: this.faculty.id,
        grade: formValues.grade,
        section: formValues.section,
        date: formValues.date,
        attendanceData: this.attendanceData
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadAttendance();
        this.snackBar.open('Attendance recorded successfully', 'Close', {
          duration: 3000
        });
      }
    });
  }

  getUniqueGrades(): number[] {
    if (!this.subjects) return [];

    const grades = this.subjects.map(subject => subject.grade);
    return [...new Set(grades)].sort((a, b) => a - b);
  }

  getSectionsForGrade(grade: number): string[] {
    if (!this.subjects) return [];

    const sections = this.subjects
      .filter(subject => subject.grade === grade)
      .map(subject => subject.section);

    return [...new Set(sections)].sort();
  }

  getStatusClass(status: number): string {
    switch (status) {
      case 0: return 'present';
      case 1: return 'absent';
      case 2: return 'late';
      case 3: return 'excused';
      case 4: return 'on-leave';
      default: return '';
    }
  }

  getStatusText(status: number): string {
    switch (status) {
      case 0: return 'Present';
      case 1: return 'Absent';
      case 2: return 'Late';
      case 3: return 'Excused';
      case 4: return 'On Leave';
      default: return 'Unknown';
    }
  }
}
