// Use modern Sass module system
@use 'sass:color';

// Generic Hero Component Styles
// This class applies to all hero sections across the application
.hero-section, .page-hero, .hero {
  height: 40vh !important; /* Fixed height of 40% of the viewport */
  min-height: 250px !important; /* Minimum height for very small screens */
  max-height: 500px !important; /* Maximum height to prevent excessive space on large screens */
  position: relative !important;
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  color: white !important;
  background-size: cover !important;
  background-position: center !important;

  // Default background if none provided
  background-image: linear-gradient(135deg, #1976d2, #64b5f6) !important;

  &::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)) !important; /* Smooth gradient overlay */
    z-index: 1 !important;
  }

  .hero-content {
    position: relative !important;
    z-index: 2 !important;
    max-width: 800px !important;
    padding: 0 20px !important;

    h1 {
      font-size: 2.2rem !important;
      margin-bottom: 0.4rem !important;
      font-weight: 700 !important;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3) !important;
      color: white !important;
    }

    p {
      font-size: 1.1rem !important;
      margin-bottom: 0.8rem !important;
      opacity: 0.9 !important;
      max-width: 600px !important;
      margin-left: auto !important;
      margin-right: auto !important;
      color: rgba(255, 255, 255, 0.9) !important;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .hero-section, .page-hero, .hero {
    .hero-content {
      h1 {
        font-size: 1.8rem !important;
      }

      p {
        font-size: 0.95rem !important;
      }
    }
  }
}

@media (max-width: 480px) {
  .hero-section, .page-hero, .hero {
    .hero-content {
      h1 {
        font-size: 1.5rem !important;
      }

      p {
        font-size: 0.85rem !important;
      }
    }
  }
}
