@use 'sass:color';
@use '../../../../styles/variables' as *;
@use '../../../../styles/portal-theme' as portal;

// Variables
$sidebar-width: 260px;
$sidebar-collapsed-width: 70px;
$header-height: 64px;
$transition-duration: 0.3s;

// Apply consistent portal theme
:host {
  @include portal.apply-portal-theme;
}

// Admin Container
.admin-container {
  @include portal.portal-base-layout;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  position: relative; // Add position relative for absolute positioning of sidebar

  &.sidebar-collapsed {
    .main-content {
      margin-left: $sidebar-collapsed-width;
    }
  }
}

// Sidebar
.sidebar {
  @include portal.portal-sidebar-base;
  width: $sidebar-width;
  height: 100%;
  transition: width $transition-duration ease;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 2px 0 10px var(--portal-shadow-medium);
  z-index: 1000;
  position: absolute; // Position absolute to remove it from the flow
  left: 0;
  top: 0;

  &.collapsed {
    width: $sidebar-collapsed-width;
  }

  .sidebar-header {
    height: $header-height;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid var(--portal-divider);

    .logo-container {
      display: flex;
      align-items: center;
      overflow: hidden;

      .logo {
        height: 40px;
        width: 40px;
        object-fit: contain;
        margin-right: 12px;
      }

      .logo-text {
        font-size: 18px;
        font-weight: 500;
        white-space: nowrap;
        color: var(--portal-sidebar-text);
      }
    }

    .toggle-btn {
      color: var(--portal-sidebar-text);
      opacity: 0.8;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 1;
        background-color: var(--portal-sidebar-hover-bg);
      }
    }
  }

  .user-info {
    padding: 16px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--portal-sidebar-hover-bg);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      mat-icon {
        font-size: 24px;
        height: 24px;
        width: 24px;
        color: var(--portal-sidebar-text);
      }
    }

    .user-details {
      overflow: hidden;

      .user-name {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .user-role {
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }

  .sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 4px;
    }

    // Collapsed navigation styles
    .collapsed-nav {
      .nav-list {
        padding: 0;

        .nav-item {
          margin: 8px 0;
          position: relative; // For positioning the hover submenu

          .nav-link {
            display: flex;
            justify-content: center;
            padding: 12px 0;

            .nav-icon {
              margin-right: 0;
            }
          }

          .category-link {
            opacity: 0.7;

            &:hover, &.active {
              opacity: 1;
              background-color: rgba(255, 255, 255, 0.1);
            }

            &.active {
              border-left: 3px solid $accent-color;
            }
          }

          // Hover Menu Parent
          &.hover-menu-parent {
            position: relative;

            // Show submenu on hover
            &:hover .hover-submenu {
              visibility: visible;
              opacity: 1;
              transform: translateX(0);
              display: block;
            }

            // Keep submenu visible when hovering over it
            .hover-submenu:hover {
              visibility: visible;
              opacity: 1;
              transform: translateX(0);
              display: block;
            }
          }

          // Hover Submenu
          .hover-submenu {
            position: fixed; // Changed to fixed to ensure proper positioning
            top: auto; // Let JavaScript set this dynamically
            left: $sidebar-collapsed-width;
            width: 240px;
            background-color: $primary-color;
            border-radius: 0 4px 4px 0;
            box-shadow: 4px 0 10px rgba(0, 0, 0, 0.2);
            z-index: 9999; // Increased z-index to ensure it appears above other elements
            visibility: hidden;
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.3s ease;
            overflow: visible; // Changed to visible to ensure content is shown
            pointer-events: auto; // Ensure hover events work
            display: none; // Initially hidden

            // Active state
            &.active {
              display: block;
              visibility: visible;
              opacity: 1;
              transform: translateX(0);
            }

            // Submenu Header
            .hover-submenu-header {
              padding: 12px 16px;
              background-color: color.adjust($primary-color, $lightness: -5%);
              color: var(--portal-sidebar-text);
              font-size: 14px;
              font-weight: 500;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            // Submenu Items
            .hover-submenu-items {
              list-style: none;
              padding: 8px 0;
              margin: 0;
              max-height: 400px;
              overflow-y: auto;

              &::-webkit-scrollbar {
                width: 4px;
              }

              &::-webkit-scrollbar-thumb {
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 4px;
              }

              .hover-submenu-item {
                .hover-submenu-link {
                  display: flex;
                  align-items: center;
                  padding: 10px 16px;
                  color: var(--portal-sidebar-text);
                  text-decoration: none;
                  opacity: 0.8;
                  transition: all 0.2s ease;

                  &:hover {
                    opacity: 1;
                    background-color: rgba(255, 255, 255, 0.1);
                  }

                  &.active {
                    opacity: 1;
                    background-color: rgba(255, 255, 255, 0.1);
                    border-left: 3px solid $accent-color;
                  }

                  .hover-submenu-icon {
                    margin-right: 12px;
                    font-size: 18px;
                    height: 18px;
                    width: 18px;
                  }

                  .hover-submenu-text {
                    font-size: 14px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                }
              }
            }
          }
        }
      }
    }

    // Expanded navigation styles
    .expanded-nav {
      .nav-category {
        margin-bottom: 0;

        // Category panel
        .category-panel {
          .category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
              background-color: rgba(255, 255, 255, 0.05);
            }

            .category-header-content {
              display: flex;
              align-items: center;

              .category-icon {
                font-size: 18px;
                height: 18px;
                width: 18px;
                margin-right: 8px;
              }

              .category-name {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }

            .expand-icon {
              font-size: 18px;
              height: 18px;
              width: 18px;
              transition: transform 0.3s ease;
            }
          }

          // Category items container
          .category-items {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;

            &.expanded {
              max-height: 500px; // Adjust based on your needs
            }
          }
        }

        mat-divider {
          margin: 0;
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
    }

    .nav-list {
      list-style: none;
      padding: 0;
      margin: 0;

      .nav-item {
        margin: 2px 0;

        .nav-link {
          display: flex;
          align-items: center;
          padding: 10px 16px;
          text-decoration: none;
          color: var(--portal-sidebar-text);
          opacity: 0.8;
          transition: all 0.2s ease;
          border-left: 3px solid transparent;
          font-size: 14px;

          &:hover {
            opacity: 1;
            background: var(--portal-sidebar-hover-bg);
          }

          &.active {
            opacity: 1;
            background: var(--portal-sidebar-active-bg);
            color: var(--portal-sidebar-active-text);
            border-left-color: var(--portal-primary);
          }

          .nav-icon {
            margin-right: 16px;
            font-size: 20px;
            height: 20px;
            width: 20px;
          }

          .nav-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }

  .sidebar-footer {
    padding: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    .logout-btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      color: var(--portal-sidebar-text);
      opacity: 0.8;
      transition: opacity 0.2s ease;

      mat-icon {
        margin-right: 16px;
      }

      &:hover {
        opacity: 1;
      }
    }
  }
}

// Main Content
.main-content {
  @include portal.portal-content-base;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-left: $sidebar-width; // Add margin to account for the sidebar width
  transition: margin-left $transition-duration ease;
}

// Header
.header {
  @include portal.portal-header-base;
  height: $header-height;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 999;

  .header-left {
    display: flex;
    align-items: center;

    .menu-toggle {
      margin-right: 16px;
      display: none;

      @media (max-width: 991px) {
        display: block;
      }
    }

    .page-title {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: #333;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;

    .header-switcher {
      margin-right: 8px;

      ::ng-deep .language-theme-switcher {
        .switcher-group {
          background: rgba(63, 81, 181, 0.1);
          border-color: rgba(63, 81, 181, 0.2);
        }

        .switcher-btn {
          color: #555;

          &:hover {
            background-color: rgba(63, 81, 181, 0.1);
            color: #3f51b5;
          }
        }
      }
    }

    .action-btn {
      margin-left: 8px;
      color: #555;
    }
  }
}

// Content Wrapper
.content-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 24px;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--portal-outline);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-track {
    background-color: var(--portal-surface-container);
  }
}

// Enhanced sidebar styling for light theme
:host-context(.light-theme) {
  .sidebar {
    // Enhanced light theme sidebar with professional appearance
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

    .sidebar-header {
      .logo-text {
        color: #ffffff;
        font-weight: 600;
      }

      .toggle-btn {
        color: #e5e7eb;

        &:hover {
          color: #ffffff;
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
    }

    .user-info {
      .user-name {
        color: #ffffff;
        font-weight: 500;
      }

      .user-role {
        color: #d1d5db;
      }

      .avatar {
        background: rgba(255, 255, 255, 0.15);

        mat-icon {
          color: #ffffff;
        }
      }
    }

    .nav-list {
      .nav-item {
        .nav-link {
          color: #e5e7eb;

          &:hover {
            color: #ffffff;
            background: rgba(255, 255, 255, 0.1);
          }

          &.active {
            background: #6366f1;
            color: #ffffff;
            border-left-color: #ffffff;
            font-weight: 500;
          }

          .nav-icon {
            color: inherit;
          }

          .nav-text {
            color: inherit;
          }
        }
      }
    }

    .sidebar-footer {
      border-top: 1px solid rgba(255, 255, 255, 0.15);

      .logout-btn {
        color: #e5e7eb;

        &:hover {
          color: #ffffff;
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }
}

// Enhanced admin dashboard styling
:host-context(.dark-theme) {
  .sidebar {
    // Enhanced dark theme sidebar
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3);

    .sidebar-header {
      .logo-text {
        color: #ffffff;
        font-weight: 600;
      }

      .toggle-btn {
        color: #e0e0e0;

        &:hover {
          color: #ffffff;
          background-color: rgba(255, 255, 255, 0.08);
        }
      }
    }

    .user-info {
      .user-name {
        color: #ffffff;
        font-weight: 500;
      }

      .user-role {
        color: #d1d5db;
      }

      .avatar {
        background: rgba(255, 255, 255, 0.1);

        mat-icon {
          color: #ffffff;
        }
      }
    }

    .nav-list {
      .nav-item {
        .nav-link {
          color: #e0e0e0;

          &:hover {
            color: #ffffff;
            background: rgba(255, 255, 255, 0.08);
          }

          &.active {
            background: rgba(99, 102, 241, 0.2);
            color: #ffffff;
            border-left-color: #6366f1;
            font-weight: 500;
          }

          .nav-icon {
            color: inherit;
          }

          .nav-text {
            color: inherit;
          }
        }
      }
    }

    .sidebar-footer {
      border-top: 1px solid rgba(255, 255, 255, 0.1);

      .logout-btn {
        color: #e0e0e0;

        &:hover {
          color: #ffffff;
          background: rgba(255, 255, 255, 0.08);
        }
      }
    }
  }

  .content-wrapper {
    // Better text contrast in dark theme
    color: #e0e0e0;

    // Improve card readability
    ::ng-deep .mat-card {
      background-color: #1e1e1e !important;
      color: #ffffff !important;
      border: 1px solid #333333;
    }

    // Better table styling
    ::ng-deep .mat-table {
      background-color: #1e1e1e !important;
      color: #e0e0e0 !important;

      .mat-header-cell {
        color: #ffffff !important;
        background-color: #2a2a2a !important;
      }

      .mat-cell {
        color: #e0e0e0 !important;
        border-bottom-color: #333333 !important;
      }

      .mat-row:hover {
        background-color: rgba(255, 255, 255, 0.05) !important;
      }
    }

    // Better button contrast
    ::ng-deep .mat-button, ::ng-deep .mat-raised-button {
      &.mat-primary {
        background-color: #6366f1 !important;
        color: #ffffff !important;
      }
    }

    // Better form field styling
    ::ng-deep .mat-form-field {
      .mat-form-field-label {
        color: #e0e0e0 !important;
      }

      .mat-input-element {
        color: #ffffff !important;
      }

      .mat-form-field-underline {
        background-color: #404040 !important;
      }

      &.mat-focused .mat-form-field-underline {
        background-color: #6366f1 !important;
      }
    }
  }
}

:host-context(.light-theme) {
  .content-wrapper {
    // Better text contrast in light theme
    color: #1f2937;

    // Improve card readability
    ::ng-deep .mat-card {
      background-color: #ffffff !important;
      color: #1f2937 !important;
      border: 1px solid #e5e7eb;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    // Better table styling
    ::ng-deep .mat-table {
      background-color: #ffffff !important;
      color: #1f2937 !important;

      .mat-header-cell {
        color: #1f2937 !important;
        background-color: #f9fafb !important;
        font-weight: 600;
      }

      .mat-cell {
        color: #374151 !important;
        border-bottom-color: #e5e7eb !important;
      }

      .mat-row:hover {
        background-color: #f9fafb !important;
      }
    }

    // Better button contrast
    ::ng-deep .mat-button, ::ng-deep .mat-raised-button {
      &.mat-primary {
        background-color: #6366f1 !important;
        color: #ffffff !important;
      }
    }
  }
}

// Responsive Styles
@media (max-width: 991px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    transform: translateX(-100%);
    z-index: 1001;

    &.collapsed {
      transform: translateX(-100%);
    }

    &:not(.collapsed) {
      transform: translateX(0);
    }
  }

  .main-content {
    margin-left: 0 !important;
  }
}

// Animation for sidebar
.sidebar.collapsed ~ .main-content {
  margin-left: $sidebar-collapsed-width;
  transition: margin-left $transition-duration ease;
}

@media (max-width: 991px) {
  .sidebar.collapsed ~ .main-content,
  .sidebar:not(.collapsed) ~ .main-content {
    margin-left: 0;
  }
}


