import { Routes } from '@angular/router';
import { adminAuthGuard } from '../core/guards/admin-auth.guard';
import { AdminLayoutComponent } from './components/admin-layout/admin-layout.component';

export const ADMIN_ROUTES: Routes = [
  {
    path: '',
    component: AdminLayoutComponent,
    canActivate: [adminAuthGuard],
    children: [
      {
        path: '',
        loadComponent: () => import('./pages/dashboard/dashboard.component').then(m => m.DashboardComponent),
        title: 'Admin Dashboard'
      },
      {
        path: 'notices',
        loadComponent: () => import('./pages/notices/notice-list/notice-list.component').then(m => m.NoticeListComponent),
        title: 'Manage Notices'
      },
      {
        path: 'notices/create',
        loadComponent: () => import('./pages/notices/notice-form/notice-form.component').then(m => m.NoticeFormComponent),
        title: 'Create Notice'
      },
      {
        path: 'notices/:id',
        loadComponent: () => import('./pages/notices/notice-detail/notice-detail.component').then(m => m.NoticeDetailComponent),
        title: 'Notice Details'
      },
      {
        path: 'notices/edit/:id',
        loadComponent: () => import('./pages/notices/notice-form/notice-form.component').then(m => m.NoticeFormComponent),
        title: 'Edit Notice'
      },
      {
        path: 'content',
        loadComponent: () => import('../pages/home/<USER>').then(m => m.HomeComponent),
        title: 'Manage Content'
      },
      {
        path: 'media',
        loadComponent: () => import('./pages/media/media-list/media-list.component').then(m => m.MediaListComponent),
        title: 'Media Library'
      },
      {
        path: 'media/upload',
        loadComponent: () => import('./pages/media/media-upload/media-upload.component').then(m => m.MediaUploadComponent),
        title: 'Upload Media'
      },
      {
        path: 'users',
        loadComponent: () => import('../pages/home/<USER>').then(m => m.HomeComponent),
        title: 'Manage Users'
      },
      {
        path: 'academic-years',
        loadComponent: () => import('./pages/academic-years/academic-year-list/academic-year-list.component').then(m => m.AcademicYearListComponent),
        title: 'Academic Years'
      },
      {
        path: 'academic-years/create',
        loadComponent: () => import('./pages/academic-years/academic-year-form/academic-year-form.component').then(m => m.AcademicYearFormComponent),
        title: 'Create Academic Year'
      },
      {
        path: 'academic-years/:id',
        loadComponent: () => import('./pages/academic-years/academic-year-detail/academic-year-detail.component').then(m => m.AcademicYearDetailComponent),
        title: 'Academic Year Details'
      },
      {
        path: 'academic-years/edit/:id',
        loadComponent: () => import('./pages/academic-years/academic-year-form/academic-year-form.component').then(m => m.AcademicYearFormComponent),
        title: 'Edit Academic Year'
      },
      {
        path: 'terms',
        loadComponent: () => import('./pages/terms/term-list/term-list.component').then(m => m.TermListComponent),
        title: 'Terms'
      },
      {
        path: 'terms/create',
        loadComponent: () => import('./pages/terms/term-form/term-form.component').then(m => m.TermFormComponent),
        title: 'Create Term'
      },
      {
        path: 'terms/:id',
        loadComponent: () => import('./pages/terms/term-detail/term-detail.component').then(m => m.TermDetailComponent),
        title: 'Term Details'
      },
      {
        path: 'terms/edit/:id',
        loadComponent: () => import('./pages/terms/term-form/term-form.component').then(m => m.TermFormComponent),
        title: 'Edit Term'
      },
      {
        path: 'holidays',
        loadComponent: () => import('./pages/holidays/holiday-list/holiday-list.component').then(m => m.HolidayListComponent),
        title: 'Holidays'
      },
      {
        path: 'holidays/create',
        loadComponent: () => import('./pages/holidays/holiday-form/holiday-form.component').then(m => m.HolidayFormComponent),
        title: 'Create Holiday'
      },
      {
        path: 'holidays/edit/:id',
        loadComponent: () => import('./pages/holidays/holiday-form/holiday-form.component').then(m => m.HolidayFormComponent),
        title: 'Edit Holiday'
      },
      {
        path: 'holidays/:id',
        loadComponent: () => import('./pages/holidays/holiday-list/holiday-list.component').then(m => m.HolidayListComponent),
        title: 'Holiday Details'
      },
      // Grade Management Routes
      {
        path: 'grades',
        loadComponent: () => import('./pages/grades/grades.component').then(m => m.GradesComponent),
        title: 'Grades'
      },
      // Section Management Routes
      {
        path: 'sections',
        loadComponent: () => import('./pages/sections/sections.component').then(m => m.SectionsComponent),
        title: 'Sections'
      },
      // Class Teacher Management Routes
      {
        path: 'class-teachers',
        loadComponent: () => import('./pages/class-teachers/class-teachers.component').then(m => m.ClassTeachersComponent),
        title: 'Class Teachers'
      },
      // Tenant Management Routes (System Admin only)
      {
        path: 'tenants',
        loadComponent: () => import('./pages/tenants/tenant-list/tenant-list.component').then(m => m.TenantListComponent),
        title: 'Manage Tenants'
      },
      {
        path: 'tenants/create',
        loadComponent: () => import('./pages/tenants/tenant-form/tenant-form.component').then(m => m.TenantFormComponent),
        title: 'Create Tenant'
      },
      {
        path: 'tenants/:id',
        loadComponent: () => import('./pages/tenants/tenant-detail/tenant-detail.component').then(m => m.TenantDetailComponent),
        title: 'Tenant Details'
      },
      {
        path: 'tenants/edit/:id',
        loadComponent: () => import('./pages/tenants/tenant-form/tenant-form.component').then(m => m.TenantFormComponent),
        title: 'Edit Tenant'
      },

    ]
  }
];
