using Microsoft.AspNetCore.Http;
using School.API.Common;
using System;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;

namespace School.API.Middleware
{
    public class GlobalExceptionHandlerMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionHandlerMiddleware> _logger;
        private readonly IWebHostEnvironment _env;

        public GlobalExceptionHandlerMiddleware(
            RequestDelegate next,
            ILogger<GlobalExceptionHandlerMiddleware> logger,
            IWebHostEnvironment env)
        {
            _next = next;
            _logger = logger;
            _env = env;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred");
                await HandleExceptionAsync(context, ex);
            }
        }

        private Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";
            int statusCode = (int)HttpStatusCode.InternalServerError;
            string errorMessage;
            string[] errors;

            // Handle different types of exceptions
            switch (exception)
            {
                case School.Application.Common.Exceptions.ValidationException validationException:
                    statusCode = (int)HttpStatusCode.BadRequest;
                    errorMessage = "Validation error occurred. Please check your input.";
                    errors = validationException.Errors.SelectMany(e => e.Value).ToArray();
                    break;

                case School.Application.Common.Exceptions.NotFoundException notFoundException:
                    statusCode = (int)HttpStatusCode.NotFound;
                    errorMessage = notFoundException.Message;
                    errors = new[] { notFoundException.Message };
                    break;

                case UnauthorizedAccessException unauthorizedException:
                    statusCode = (int)HttpStatusCode.Unauthorized;
                    errorMessage = "You are not authorized to perform this action.";
                    errors = new[] { unauthorizedException.Message };
                    break;

                case InvalidOperationException invalidOpException:
                    statusCode = (int)HttpStatusCode.BadRequest;
                    errorMessage = invalidOpException.Message;
                    errors = new[] { invalidOpException.Message };
                    break;

                case JsonException jsonException:
                    statusCode = (int)HttpStatusCode.BadRequest;
                    errorMessage = "Invalid JSON format in request.";
                    errors = new[] { jsonException.Message };
                    break;

                case BadHttpRequestException badRequestException:
                    statusCode = (int)HttpStatusCode.BadRequest;
                    errorMessage = "Invalid request format.";
                    errors = new[] { badRequestException.Message };
                    break;

                default:
                    errorMessage = _env.IsDevelopment()
                        ? $"An error occurred: {exception.Message}"
                        : "An unexpected error occurred. Please try again later.";
                    errors = _env.IsDevelopment()
                        ? new[] { exception.Message }
                        : new[] { "An unexpected error occurred. Please try again later." };
                    break;
            }

            context.Response.StatusCode = statusCode;
            var response = ApiResponse.ErrorResponse(errorMessage, statusCode, errors);

            // Add detailed error info in development environment
            if (_env.IsDevelopment())
            {
                _logger.LogError(exception, "Exception details: {ExceptionType}", exception.GetType().Name);
            }

            return context.Response.WriteAsJsonAsync(response);
        }

        private object GetDetailedErrorInfo(Exception exception)
        {
            return new
            {
                Exception = exception.GetType().Name,
                Message = exception.Message,
                StackTrace = exception.StackTrace,
                InnerException = exception.InnerException != null
                    ? new
                    {
                        Exception = exception.InnerException.GetType().Name,
                        Message = exception.InnerException.Message,
                        StackTrace = exception.InnerException.StackTrace
                    }
                    : null
            };
        }
    }

    // Extension method to add the middleware to the pipeline
    public static class GlobalExceptionHandlerMiddlewareExtensions
    {
        public static IApplicationBuilder UseGlobalExceptionHandler(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<GlobalExceptionHandlerMiddleware>();
        }
    }
}
