import { <PERSON>mpo<PERSON>, <PERSON>Ini<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatStepperModule } from '@angular/material/stepper';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AuthService } from '../../core/services/auth.service';
import { ThemeService } from '../../core/services/theme.service';
import { LanguageService } from '../../core/services/language.service';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatCheckboxModule,
    MatDividerModule,
    MatSnackBarModule,
    MatProgressBarModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatTooltipModule,
    MatStepperModule,
    MatMenuModule,
    TranslateModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Forms
  loginForm!: FormGroup;
  mfaForm!: FormGroup;

  // UI State
  hidePassword = true;
  isLoading = false;
  showMfaStep = false;
  mfaToken = '';

  // Available options



  userTypes = [
    { value: 'student', label: 'STUDENT', icon: 'school' },
    { value: 'parent', label: 'PARENT', icon: 'family_restroom' },
    { value: 'faculty', label: 'FACULTY', icon: 'person' },
    { value: 'admin', label: 'ADMIN', icon: 'admin_panel_settings' },
    { value: 'alumni', label: 'ALUMNI', icon: 'groups' }
  ];

  selectedUserType = 'student';

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private translateService: TranslateService,
    private authService: AuthService,
    private themeService: ThemeService,
    private languageService: LanguageService,
    private router: Router
  ) {
    // Services will handle initialization automatically
    console.log('Login component initialized');
  }

  ngOnInit(): void {
    this.initForms();

    // Check for remembered username
    const rememberedUser = localStorage.getItem('rememberedUser');
    if (rememberedUser) {
      this.loginForm.patchValue({
        username: rememberedUser,
        rememberMe: true
      });
    }

    // Language changes are handled globally by the language service
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  initForms(): void {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      rememberMe: [false]
    });

    this.mfaForm = this.fb.group({
      mfaCode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });
  }

  onSubmit(): void {
    if (this.showMfaStep) {
      this.submitMfa();
      return;
    }

    if (this.loginForm.invalid) {
      return;
    }

    this.isLoading = true;
    const { username, password, rememberMe } = this.loginForm.value;

    console.log('Selected user type:', this.selectedUserType);

    // Call the authentication service
    this.authService.login(username, password, undefined, rememberMe).subscribe({
      next: (response) => {
        this.isLoading = false;
        console.log('Login response:', response);

        // Check if MFA is required
        if (response.requiresMfa) {
          this.showMfaStep = true;
          this.mfaToken = response.mfaToken || '';
          this.snackBar.open(
            this.translateService.instant('LOGIN.MFA_REQUIRED'),
            this.translateService.instant('COMMON.CLOSE'),
            { duration: 5000 }
          );
          return;
        }

        // Handle successful login
        this.handleSuccessfulLogin(response, rememberMe, username);
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Login error:', error);

        // Show error message
        this.snackBar.open(
          this.translateService.instant('LOGIN.ERROR_MESSAGE'),
          this.translateService.instant('COMMON.CLOSE'),
          { duration: 3000 }
        );
      }
    });
  }

  submitMfa(): void {
    if (this.mfaForm.invalid) {
      return;
    }

    this.isLoading = true;
    const { username, password, rememberMe } = this.loginForm.value;
    const { mfaCode } = this.mfaForm.value;

    this.authService.login(username, password, mfaCode, rememberMe).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (!response.requiresMfa) {
          this.handleSuccessfulLogin(response, rememberMe, username);
        } else {
          this.snackBar.open(
            this.translateService.instant('LOGIN.INVALID_MFA_CODE'),
            this.translateService.instant('COMMON.CLOSE'),
            { duration: 3000 }
          );
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('MFA verification error:', error);
        this.snackBar.open(
          this.translateService.instant('LOGIN.INVALID_MFA_CODE'),
          this.translateService.instant('COMMON.CLOSE'),
          { duration: 3000 }
        );
      }
    });
  }

  private handleSuccessfulLogin(response: any, rememberMe: boolean, username: string): void {
    console.log('Login successful', response);

    // Handle remember me
    if (rememberMe) {
      localStorage.setItem('rememberedUser', username);
    } else {
      localStorage.removeItem('rememberedUser');
    }

    // Convert single role to array for compatibility with existing logic
    const userRoles = response.user?.roleName ? [response.user.roleName] : [];

    // Check if user has access to the requested portal
    if (this.selectedUserType && !this.hasPortalAccess(userRoles, this.selectedUserType)) {
      // User doesn't have access to the requested portal
      const portalName = this.translateService.instant('LOGIN.' + this.selectedUserType.toUpperCase());
      const errorMessage = this.translateService.instant('LOGIN.PORTAL_ACCESS_DENIED')
        .replace('{portal}', portalName);

      this.snackBar.open(
        errorMessage,
        this.translateService.instant('COMMON.CLOSE'),
        { duration: 5000, panelClass: ['error-snackbar'] }
      );
      return;
    }

    // Show success message
    this.snackBar.open(
      this.translateService.instant('LOGIN.SUCCESS_MESSAGE'),
      this.translateService.instant('COMMON.CLOSE'),
      { duration: 3000, panelClass: ['success-snackbar'] }
    );

    // Navigate to the appropriate portal based on user's roles and selected portal
    this.navigateToUserDashboard(userRoles, this.selectedUserType);
  }

  // Theme and Language methods
  get currentTheme(): string {
    return this.themeService.currentTheme;
  }

  onThemeChange(theme: string): void {
    if (theme === 'light' || theme === 'dark') {
      this.themeService.setTheme(theme);
    }
  }

  get currentLanguage(): string {
    return this.languageService.currentLanguage;
  }

  get languages() {
    return this.languageService.availableLanguages;
  }

  onLanguageChange(language: string): void {
    if (language === 'en' || language === 'bn') {
      this.languageService.setLanguage(language as 'en' | 'bn');
    }
  }

  selectUserType(type: string): void {
    this.selectedUserType = type;
  }

  backToLogin(): void {
    this.showMfaStep = false;
    this.mfaForm.reset();
    this.mfaToken = '';
  }

  // Utility methods
  getErrorMessage(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (field?.hasError('required')) {
      return this.translateService.instant(`LOGIN.${fieldName.toUpperCase()}_REQUIRED`);
    }
    if (field?.hasError('minlength')) {
      return this.translateService.instant(`LOGIN.${fieldName.toUpperCase()}_MIN_LENGTH`);
    }
    return '';
  }

  getMfaErrorMessage(): string {
    const field = this.mfaForm.get('mfaCode');
    if (field?.hasError('required')) {
      return this.translateService.instant('LOGIN.MFA_CODE_REQUIRED');
    }
    if (field?.hasError('pattern')) {
      return this.translateService.instant('LOGIN.MFA_CODE_INVALID');
    }
    return '';
  }

  getCurrentLanguageFlag(): string {
    return this.languageService.getLanguageFlag();
  }

  getCurrentLanguageText(): string {
    return this.languageService.getLanguageName();
  }

  showBackupCodes(): void {
    // Implementation for showing backup codes
    this.snackBar.open(
      this.translateService.instant('LOGIN.BACKUP_CODES_FEATURE_COMING_SOON'),
      this.translateService.instant('COMMON.CLOSE'),
      { duration: 3000 }
    );
  }

  /**
   * Check if user has access to the requested portal based on their roles
   */
  private hasPortalAccess(userRoles: string[], requestedPortal: string): boolean {
    const rolePortalMapping: { [key: string]: string[] } = {
      'student': ['Student', 'User'],
      'parent': ['Parent', 'User'],
      'faculty': ['Faculty', 'User', 'Manager', 'Editor'],
      'admin': ['Admin', 'SystemAdmin', 'Manager'],
      'alumni': ['Alumni', 'User']
    };

    const requiredRoles = rolePortalMapping[requestedPortal] || [];
    return requiredRoles.some(role => userRoles.includes(role));
  }

  /**
   * Navigate to the appropriate dashboard based on user roles and requested portal
   */
  private navigateToUserDashboard(userRoles: string[], requestedPortal: string): void {
    // Define portal routes
    const portalRoutes: { [key: string]: string } = {
      'student': '/student-portal',
      'parent': '/parent-portal',
      'faculty': '/faculty-portal',
      'admin': '/admin',
      'alumni': '/alumni'
    };

    const targetRoute = portalRoutes[requestedPortal];

    if (targetRoute) {
      console.log(`Navigating to ${requestedPortal} portal:`, targetRoute);
      this.router.navigate([targetRoute]);
    } else {
      // Fallback: navigate based on highest priority role
      if (userRoles.includes('Admin')) {
        this.router.navigate(['/admin']);
      } else if (userRoles.includes('Faculty') || userRoles.includes('Manager') || userRoles.includes('Editor')) {
        this.router.navigate(['/faculty-portal']);
      } else if (userRoles.includes('Parent')) {
        this.router.navigate(['/parent-portal']);
      } else if (userRoles.includes('Student')) {
        this.router.navigate(['/student-portal']);
      } else if (userRoles.includes('Alumni')) {
        this.router.navigate(['/alumni']);
      } else {
        // Default fallback
        this.router.navigate(['/home']);
      }
    }
  }

  /**
   * Navigate to registration page
   */
  navigateToRegister(): void {
    this.router.navigate(['/register'], {
      queryParams: { portal: this.selectedUserType }
    });
  }

  /**
   * Navigate to forgot password page
   */
  navigateToForgotPassword(): void {
    this.router.navigate(['/forgot-password']);
  }

  /**
   * Navigate to help page
   */
  navigateToHelp(): void {
    this.router.navigate(['/help']);
  }
}
