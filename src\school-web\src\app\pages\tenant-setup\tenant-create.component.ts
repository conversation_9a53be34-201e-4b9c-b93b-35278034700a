import { <PERSON>mpo<PERSON>, <PERSON>Ini<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { AuthService } from '../../core/services/auth.service';

@Component({
  selector: 'app-tenant-create',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    TranslateModule
  ],
  template: `
    <div class="tenant-create-container">
      <mat-card class="create-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>add_business</mat-icon>
            Create Your School
          </mat-card-title>
          <mat-card-subtitle>
            Set up your school management system
          </mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="createForm" (ngSubmit)="onSubmit()">
            <div class="form-section">
              <h3>School Information</h3>
              
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>School Name</mat-label>
                <input matInput formControlName="schoolName" placeholder="Enter your school name">
                <mat-error *ngIf="createForm.get('schoolName')?.hasError('required')">
                  School name is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>School Identifier</mat-label>
                <input matInput formControlName="slug" placeholder="e.g., my-school (used in URL)">
                <mat-hint>This will be used in your school's URL: {{slug}}.edumanage.com</mat-hint>
                <mat-error *ngIf="createForm.get('slug')?.hasError('required')">
                  School identifier is required
                </mat-error>
                <mat-error *ngIf="createForm.get('slug')?.hasError('pattern')">
                  Only lowercase letters, numbers, and hyphens allowed
                </mat-error>
              </mat-form-field>

              <div class="form-row">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>School Type</mat-label>
                  <mat-select formControlName="schoolType">
                    <mat-option value="primary">Primary School</mat-option>
                    <mat-option value="secondary">Secondary School</mat-option>
                    <mat-option value="high">High School</mat-option>
                    <mat-option value="combined">Combined School</mat-option>
                    <mat-option value="international">International School</mat-option>
                    <mat-option value="private">Private School</mat-option>
                    <mat-option value="public">Public School</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Country</mat-label>
                  <mat-select formControlName="country">
                    <mat-option value="BD">Bangladesh</mat-option>
                    <mat-option value="IN">India</mat-option>
                    <mat-option value="US">United States</mat-option>
                    <mat-option value="UK">United Kingdom</mat-option>
                    <mat-option value="CA">Canada</mat-option>
                    <mat-option value="AU">Australia</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Contact Email</mat-label>
                <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
                <mat-error *ngIf="createForm.get('email')?.hasError('required')">
                  Email is required
                </mat-error>
                <mat-error *ngIf="createForm.get('email')?.hasError('email')">
                  Please enter a valid email
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-section">
              <h3>Subscription Plan</h3>
              <div class="plan-selection">
                <div class="plan-option" 
                     [class.selected]="createForm.get('plan')?.value === 'trial'"
                     (click)="selectPlan('trial')">
                  <h4>Free Trial</h4>
                  <p>30 days free trial</p>
                  <ul>
                    <li>Up to 100 students</li>
                    <li>Basic features</li>
                    <li>Email support</li>
                  </ul>
                </div>
                
                <div class="plan-option" 
                     [class.selected]="createForm.get('plan')?.value === 'basic'"
                     (click)="selectPlan('basic')">
                  <h4>Basic Plan</h4>
                  <p>$29/month</p>
                  <ul>
                    <li>Up to 500 students</li>
                    <li>All features</li>
                    <li>Priority support</li>
                  </ul>
                </div>
              </div>
            </div>
          </form>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button routerLink="/login">
            <mat-icon>arrow_back</mat-icon>
            Back to Login
          </button>
          <div class="spacer"></div>
          <button mat-raised-button color="primary" 
                  [disabled]="!createForm.valid || isLoading"
                  (click)="onSubmit()">
            <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
            <mat-icon *ngIf="!isLoading">school</mat-icon>
            Create School
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .tenant-create-container {
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
      padding: 20px;
      overflow-y: auto;
    }
    
    .create-card {
      max-width: 600px;
      width: 100%;
    }
    
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .form-section {
      margin-bottom: 24px;
    }
    
    .form-section h3 {
      margin: 0 0 16px 0;
      color: #333;
      font-weight: 500;
    }
    
    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }
    
    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .form-field {
      flex: 1;
    }
    
    .plan-selection {
      display: flex;
      gap: 16px;
    }
    
    .plan-option {
      flex: 1;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      padding: 16px;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .plan-option:hover {
      border-color: #4caf50;
    }
    
    .plan-option.selected {
      border-color: #4caf50;
      background-color: #f1f8e9;
    }
    
    .plan-option h4 {
      margin: 0 0 8px 0;
      color: #333;
    }
    
    .plan-option p {
      margin: 0 0 12px 0;
      font-weight: 500;
      color: #4caf50;
    }
    
    .plan-option ul {
      margin: 0;
      padding-left: 16px;
    }
    
    .plan-option li {
      margin-bottom: 4px;
      font-size: 0.9rem;
    }
    
    mat-card-actions {
      display: flex;
      align-items: center;
      padding: 16px 24px;
    }
    
    .spacer {
      flex: 1;
    }
    
    .button-spinner {
      margin-right: 8px;
    }
    
    @media (max-width: 768px) {
      .form-row {
        flex-direction: column;
        gap: 8px;
      }
      
      .plan-selection {
        flex-direction: column;
      }
    }
  `]
})
export class TenantCreateComponent implements OnInit, OnDestroy {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);
  private authService = inject(AuthService);

  createForm!: FormGroup;
  isLoading = false;
  slug = '';

  ngOnInit() {
    this.createForm = this.fb.group({
      schoolName: ['', Validators.required],
      slug: ['', [Validators.required, Validators.pattern(/^[a-z0-9-]+$/)]],
      schoolType: ['', Validators.required],
      country: ['BD', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      plan: ['trial', Validators.required]
    });

    // Auto-generate slug from school name
    this.createForm.get('schoolName')?.valueChanges.subscribe(name => {
      if (name) {
        const generatedSlug = name.toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim();
        this.createForm.get('slug')?.setValue(generatedSlug);
        this.slug = generatedSlug;
      }
    });
  }

  ngOnDestroy() {
    // Cleanup if needed
  }

  selectPlan(plan: string) {
    this.createForm.get('plan')?.setValue(plan);
  }

  onSubmit() {
    if (this.createForm.valid) {
      this.isLoading = true;
      
      // TODO: Implement actual tenant creation API call
      console.log('Creating tenant:', this.createForm.value);
      
      // Simulate API call
      setTimeout(() => {
        this.isLoading = false;
        this.snackBar.open('School created successfully! Redirecting to setup...', 'Close', { duration: 3000 });
        
        // Redirect to setup wizard with the new tenant
        this.router.navigate(['/tenant-setup/wizard'], {
          queryParams: { tenant: this.createForm.value.slug }
        });
      }, 2000);
    }
  }
}
