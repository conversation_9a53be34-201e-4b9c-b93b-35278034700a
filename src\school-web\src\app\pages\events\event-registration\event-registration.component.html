<div class="event-registration-container">
  <div class="back-button">
    <button mat-button (click)="goToEvent()">
      <mat-icon>arrow_back</mat-icon>
      {{ 'EVENTS.BACK_TO_EVENT' | translate }}
    </button>
  </div>

  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <div class="error-container" *ngIf="error">
    <p class="error-message">{{ error }}</p>
    <button mat-raised-button color="primary" (click)="goToEvents()">
      {{ 'EVENTS.BACK_TO_EVENTS' | translate }}
    </button>
  </div>

  <div class="registration-content" *ngIf="!isLoading && !error && event">
    <div class="registration-header">
      <h1>{{ 'EVENTS.REGISTER_FOR' | translate }}</h1>
      <h2>{{ getTranslation(event, 'title') }}</h2>
      <p class="event-date">{{ formatDate(event.startDate) }}</p>
    </div>

    <div class="registration-success" *ngIf="registrationSuccess">
      <mat-icon class="success-icon">check_circle</mat-icon>
      <h2>{{ 'EVENTS.REGISTRATION_COMPLETE' | translate }}</h2>
      <p>{{ 'EVENTS.REGISTRATION_CONFIRMATION' | translate }}</p>
      <button mat-raised-button color="primary" (click)="goToEvent()">
        {{ 'EVENTS.BACK_TO_EVENT' | translate }}
      </button>
    </div>

    <div class="registration-form-container" *ngIf="!registrationSuccess">
      <form [formGroup]="registrationForm" (ngSubmit)="onSubmit()">
        <mat-card>
          <mat-card-content>
            <h3>{{ 'EVENTS.PERSONAL_INFORMATION' | translate }}</h3>
            
            <div class="form-field">
              <mat-form-field appearance="outline">
                <mat-label>{{ 'EVENTS.FORM.NAME' | translate }}</mat-label>
                <input matInput formControlName="name" required>
                <mat-error *ngIf="registrationForm.get('name')?.hasError('required')">
                  {{ 'EVENTS.FORM.NAME_REQUIRED' | translate }}
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-field">
              <mat-form-field appearance="outline">
                <mat-label>{{ 'EVENTS.FORM.EMAIL' | translate }}</mat-label>
                <input matInput formControlName="email" required type="email">
                <mat-error *ngIf="registrationForm.get('email')?.hasError('required')">
                  {{ 'EVENTS.FORM.EMAIL_REQUIRED' | translate }}
                </mat-error>
                <mat-error *ngIf="registrationForm.get('email')?.hasError('email')">
                  {{ 'EVENTS.FORM.EMAIL_INVALID' | translate }}
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-field">
              <mat-form-field appearance="outline">
                <mat-label>{{ 'EVENTS.FORM.PHONE' | translate }}</mat-label>
                <input matInput formControlName="phone" required>
                <mat-error *ngIf="registrationForm.get('phone')?.hasError('required')">
                  {{ 'EVENTS.FORM.PHONE_REQUIRED' | translate }}
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-field">
              <mat-form-field appearance="outline">
                <mat-label>{{ 'EVENTS.FORM.STUDENT_ID' | translate }}</mat-label>
                <input matInput formControlName="studentId">
                <mat-hint>{{ 'EVENTS.FORM.STUDENT_ID_HINT' | translate }}</mat-hint>
              </mat-form-field>
            </div>

            <div class="form-field">
              <mat-form-field appearance="outline">
                <mat-label>{{ 'EVENTS.FORM.ADDITIONAL_INFO' | translate }}</mat-label>
                <textarea matInput formControlName="additionalInfo" rows="4"></textarea>
                <mat-hint>{{ 'EVENTS.FORM.ADDITIONAL_INFO_HINT' | translate }}</mat-hint>
              </mat-form-field>
            </div>
          </mat-card-content>

          <mat-card-actions align="end">
            <button mat-button type="button" (click)="goToEvent()">
              {{ 'COMMON.CANCEL' | translate }}
            </button>
            <button mat-raised-button color="primary" type="submit" [disabled]="registrationForm.invalid || isSubmitting">
              <mat-spinner *ngIf="isSubmitting" diameter="24" class="spinner"></mat-spinner>
              <span *ngIf="!isSubmitting">{{ 'EVENTS.REGISTER' | translate }}</span>
            </button>
          </mat-card-actions>
        </mat-card>
      </form>
    </div>
  </div>
</div>
