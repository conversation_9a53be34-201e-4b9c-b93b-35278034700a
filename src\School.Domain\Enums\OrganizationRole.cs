namespace School.Domain.Enums;

/// <summary>
/// Roles a user can have within an organization
/// </summary>
public enum OrganizationRole
{
    /// <summary>
    /// Regular member with basic access
    /// </summary>
    Member = 1,

    /// <summary>
    /// Faculty/Teacher with teaching privileges
    /// </summary>
    Faculty = 2,

    /// <summary>
    /// Administrative staff with management access
    /// </summary>
    Staff = 3,

    /// <summary>
    /// Department head or coordinator
    /// </summary>
    Coordinator = 4,

    /// <summary>
    /// Manager with broader administrative access
    /// </summary>
    Manager = 5,

    /// <summary>
    /// Administrator with full access to organization features
    /// </summary>
    Administrator = 6,

    /// <summary>
    /// Owner with complete control over the organization
    /// </summary>
    Owner = 7,

    /// <summary>
    /// Super admin with system-level access (platform admin)
    /// </summary>
    SuperAdmin = 8,

    /// <summary>
    /// Student role for student users
    /// </summary>
    Student = 9,

    /// <summary>
    /// Parent role for parent users
    /// </summary>
    Parent = 10,

    /// <summary>
    /// Alumni role for graduated students
    /// </summary>
    Alumni = 11,

    /// <summary>
    /// Guest with limited read-only access
    /// </summary>
    Guest = 12
}
