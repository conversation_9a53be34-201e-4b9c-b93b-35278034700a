using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;

using School.Application.Common.Interfaces;
using School.Application.Features.AcademicCalendar;
using School.Application.Features.AcademicYear;
using School.Application.Features.Alumni;
using School.Application.Features.Term;
using School.Application.Features.Grade;
using School.Application.Features.Section;
using School.Application.Features.ClassTeacher;
using School.Application.Features.Auth;
using School.Infrastructure.Auth;
using School.Infrastructure.Services;
using School.Application.Features.Career;
using School.Application.Features.Clubs;
using School.Application.Features.Content;
using School.Application.Features.Department;
using School.Application.Features.Event;
using School.Application.Features.Faculty;
using School.Application.Features.HostelFacility;
using School.Application.Features.Media;
using School.Application.Features.Notice;
using School.Application.Features.Parent;
using School.Application.Features.Student;
using School.Application.Features.TuitionFee;
using School.Application.Features.User;
using School.Application.Features.TenantSetup;
using School.Domain.Common;
using School.Domain.Entities;
using School.Domain.Enums;
using School.Infrastructure.Identity;
using School.Infrastructure.Persistence;
using School.Infrastructure.Persistence.Interceptors;
using School.Infrastructure.Persistence.Repositories;

using System.Text;
using System.Linq;
using System.Security.Claims;

namespace School.Infrastructure;

public static class ConfigureServices
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Database
        services.AddDbContext<ApplicationDbContext>((sp, options) => {
            var auditInterceptor = sp.GetRequiredService<AuditableEntitySaveChangesInterceptor>();
            options.UseNpgsql(
                configuration.GetConnectionString("DefaultConnection"),
                b => b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName))
                .AddInterceptors(auditInterceptor);
        });

        services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
        services.AddScoped<IClubRepository, ClubRepository>();
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        // Legacy Identity services - will be removed once we fully migrate to ASP.NET Core Identity
        // services.AddScoped<IIdentityService, IdentityService>();
        // services.AddScoped<ITokenGenerator, JwtTokenGenerator>();

        // Services
        services.AddTransient<ICurrentUserService, CurrentUserService>();

        // Add HttpContextAccessor for CurrentUserService
        services.AddHttpContextAccessor();

        // Authentication: JWT Bearer Only (Identity for user management)
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddJwtBearer(options =>
        {
            var jwtKey = configuration["Jwt:Key"];
            var jwtIssuer = configuration["Jwt:Issuer"];
            var jwtAudience = configuration["Jwt:Audience"];

            if (string.IsNullOrEmpty(jwtKey))
                throw new InvalidOperationException("JWT Key not configured");

            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = jwtIssuer,
                ValidAudience = jwtAudience,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey)),
                ClockSkew = TimeSpan.Zero
            };

            options.RequireHttpsMetadata = false; // Allow HTTP for development
            options.SaveToken = true;
        });

        // Authorization
        services.AddAuthorization(options =>
        {
            options.AddPolicy("AdminPolicy", policy => policy.RequireRole("Admin", "SystemAdmin", "SuperAdmin"));
            options.AddPolicy("StudentPolicy", policy => policy.RequireRole("Student"));
            options.AddPolicy("EditorPolicy", policy => policy.RequireRole("Admin", "Editor", "SystemAdmin", "SuperAdmin"));
            options.AddPolicy("TeacherPolicy", policy => policy.RequireRole("Admin", "Faculty", "SystemAdmin", "SuperAdmin"));
            options.AddPolicy("ManagerPolicy", policy => policy.RequireRole("Admin", "Manager", "SystemAdmin", "SuperAdmin"));
            options.AddPolicy("AlumniPolicy", policy => policy.RequireRole("Admin", "Alumni", "SystemAdmin", "SuperAdmin"));
            options.AddPolicy("UserPolicy", policy => policy.RequireAuthenticatedUser());

            // Add a policy that allows students to access only their own data or admins to access any data
            options.AddPolicy("StudentSelfOrAdmin", policy =>
                policy.RequireAssertion(context =>
                {
                    // Check if user is an admin (Admin or SystemAdmin)
                    if (context.User.IsInRole("Admin") || context.User.IsInRole("SystemAdmin"))
                    {
                        return true;
                    }

                    // Check if user is a student accessing their own data
                    if (context.User.IsInRole("Student"))
                    {
                        // Get the user ID from the route
                        var routeUserId = context.Resource as string;
                        if (string.IsNullOrEmpty(routeUserId))
                        {
                            return false;
                        }

                        // Get the user ID from the claims
                        var userIdClaim = context.User.FindFirst("userId")?.Value ??
                                         context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                        // Allow access if the user ID in the route matches the user ID in the claims
                        return !string.IsNullOrEmpty(userIdClaim) && routeUserId == userIdClaim;
                    }

                    return false;
                }));

            // Set the default policy to require authentication
            options.DefaultPolicy = new AuthorizationPolicyBuilder()
                .RequireAuthenticatedUser()
                .Build();
            options.FallbackPolicy = null;

            // Configure the fallback policy to not apply to endpoints with the AllowAnonymous attribute
            options.DefaultPolicy = new AuthorizationPolicyBuilder()
                .RequireAuthenticatedUser()
                .Build();
            options.FallbackPolicy = null;
        });

        // ASP.NET Core Identity (for user management only, not authentication)
        services.AddIdentityCore<ApplicationUser>(options =>
        {
            // Password settings
            options.Password.RequireDigit = true;
            options.Password.RequireLowercase = true;
            options.Password.RequireUppercase = true;
            options.Password.RequireNonAlphanumeric = true;
            options.Password.RequiredLength = 8;

            // Lockout settings
            options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(15);
            options.Lockout.MaxFailedAccessAttempts = 5;

            // User settings
            options.User.RequireUniqueEmail = true;
        })
        .AddRoles<IdentityRole>()
        .AddEntityFrameworkStores<ApplicationDbContext>()
        .AddDefaultTokenProviders()
        .AddSignInManager<SignInManager<ApplicationUser>>();

        // Register all application services
        services.AddHttpContextAccessor();
        services.AddScoped<IDateTime, DateTimeService>();
        services.AddScoped<IFileStorageService, LocalFileStorageService>();

        // Register Enhanced Auth Services (Identity + JWT + Refresh Tokens)
        services.AddScoped<EnhancedJwtService>();
        services.AddScoped<ITokenGenerator, EnhancedJwtService>();
        services.AddScoped<IAuthService, EnhancedAuthService>();
        services.AddScoped<IAdminAuthService, AdminAuthService>();

        // Register multi-tenant services
        services.AddScoped<ITenantContext, TenantContext>();
        services.AddScoped<TenantService>(); // Register the concrete implementation
        services.AddScoped<ITenantService>(provider =>
        {
            var innerService = provider.GetRequiredService<TenantService>();
            var cache = provider.GetRequiredService<IMemoryCache>();
            var logger = provider.GetRequiredService<ILogger<CachedTenantService>>();
            return new CachedTenantService(innerService, cache, logger);
        });

        // Add memory cache for tenant caching
        services.AddMemoryCache();

        // Register other services
        services.AddScoped<IIdentityService, IdentityService>();
        services.AddScoped<IMfaService, MfaService>();
        services.AddScoped<IUserService, UserService>();

        // Register all service implementations
        services.AddScoped<IAcademicYearService, AcademicYearService>();
        services.AddScoped<ITermService, TermService>();
        services.AddScoped<IAcademicCalendarService, AcademicCalendarService>();
        services.AddScoped<IHolidayService, HolidayService>();

        // Sprint 3: Grade & Section Management Services
        services.AddScoped<IGradeService, GradeService>();
        services.AddScoped<ISectionService, SectionService>();
        services.AddScoped<IClassTeacherService, ClassTeacherService>();
        services.AddScoped<IAlumniService, AlumniService>();
        services.AddScoped<ICareerService, CareerService>();
        services.AddScoped<IContentService, ContentService>();
        services.AddScoped<IDepartmentService, DepartmentService>();
        services.AddScoped<IEventService, EventService>();
        services.AddScoped<IFacultyService, FacultyService>();
        services.AddScoped<IHostelFacilityService, HostelFacilityService>();
        services.AddScoped<IMediaService, MediaService>();
        services.AddScoped<INoticeService, NoticeService>();
        services.AddScoped<IParentService, ParentService>();
        services.AddScoped<IStudentService, StudentService>();
        services.AddScoped<ITuitionFeeService, TuitionFeeService>();
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IClubService, ClubService>();
        services.AddScoped<ITenantSetupService, TenantSetupService>();

        // Add audit interceptor
        services.AddScoped<AuditableEntitySaveChangesInterceptor>();

        return services;
    }
}
