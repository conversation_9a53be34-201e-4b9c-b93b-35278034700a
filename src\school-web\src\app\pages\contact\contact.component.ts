import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule } from '@ngx-translate/core';
import { SafePipe } from '../../shared/pipes/safe.pipe';

interface Department {
  name: string;
  email: string;
  phone: string;
  description: string;
  icon: string;
}

interface SocialMedia {
  name: string;
  url: string;
  icon: string;
}

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatTabsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSnackBarModule,
    MatExpansionModule,
    TranslateModule,
    SafePipe
  ],
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.scss'
})
export class ContactComponent implements OnInit {
  contactForm: FormGroup;

  // School locations
  locations = [
    {
      name: 'Main Campus',
      address: '123 School Street, Cityville, State 12345',
      phone: '(*************',
      email: '<EMAIL>',
      hours: 'Monday-Friday: 7:30 AM - 4:30 PM',
      mapUrl: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3022.215151997078!2d-73.98784492439748!3d40.75798833440443!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c25855c6480299%3A0x55194ec5a1ae072e!2sTimes%20Square!5m2!1s!2s'
    },
    {
      name: 'Elementary School Campus',
      address: '456 Learning Lane, Cityville, State 12345',
      phone: '(*************',
      email: '<EMAIL>',
      hours: 'Monday-Friday: 7:30 AM - 3:30 PM',
      mapUrl: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3022.215151997078!2d-73.98784492439748!3d40.75798833440443!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c25855c6480299%3A0x55194ec5a1ae072e!2sTimes%20Square!5m2!1s!2s'
    }
  ];

  // Departments
  departments: Department[] = [
    {
      name: 'Admissions',
      email: '<EMAIL>',
      phone: '(*************',
      description: 'For inquiries about the application process, campus tours, and enrollment.',
      icon: 'how_to_reg'
    },
    {
      name: 'Academic Affairs',
      email: '<EMAIL>',
      phone: '(*************',
      description: 'For questions related to curriculum, programs, and academic policies.',
      icon: 'school'
    },
    {
      name: 'Student Services',
      email: '<EMAIL>',
      phone: '(*************',
      description: 'For student support, counseling, and extracurricular activities.',
      icon: 'people'
    },
    {
      name: 'Finance Office',
      email: '<EMAIL>',
      phone: '(*************',
      description: 'For tuition payments, financial aid, and billing inquiries.',
      icon: 'payments'
    },
    {
      name: 'Technology Support',
      email: '<EMAIL>',
      phone: '(*************',
      description: 'For assistance with school technology systems and platforms.',
      icon: 'computer'
    },
    {
      name: 'Athletics Department',
      email: '<EMAIL>',
      phone: '(*************',
      description: 'For sports programs, schedules, and athletic events.',
      icon: 'sports_soccer'
    }
  ];

  // Social media links
  socialMedia: SocialMedia[] = [
    {
      name: 'Facebook',
      url: 'https://facebook.com/yourschool',
      icon: 'facebook'
    },
    {
      name: 'Twitter',
      url: 'https://twitter.com/yourschool',
      icon: 'twitter'
    },
    {
      name: 'Instagram',
      url: 'https://instagram.com/yourschool',
      icon: 'instagram'
    },
    {
      name: 'LinkedIn',
      url: 'https://linkedin.com/school/yourschool',
      icon: 'linkedin'
    },
    {
      name: 'YouTube',
      url: 'https://youtube.com/c/yourschool',
      icon: 'youtube'
    }
  ];

  // FAQ items
  faqs = [
    {
      question: 'What are the school hours?',
      answer: 'Our school hours are from 8:00 AM to 3:30 PM, Monday through Friday. The campus opens at 7:30 AM for early drop-off, and after-school programs run until 6:00 PM.'
    },
    {
      question: 'How do I schedule a campus tour?',
      answer: 'Campus tours can be scheduled through our Admissions Office. <NAME_EMAIL> or call (************* to arrange a visit.'
    },
    {
      question: 'Where can I find the academic calendar?',
      answer: 'The academic calendar is available on our website under the "Academics" section. It includes important dates such as holidays, breaks, and exam periods.'
    },
    {
      question: 'How do I update my contact information?',
      answer: 'Parents can update their contact information through the Parent Portal. If you need assistance, please contact our administrative office.'
    },
    {
      question: 'Who do I contact about bus transportation?',
      answer: 'For questions about bus routes, schedules, or transportation issues, please contact our Transportation <NAME_EMAIL> or call (*************.'
    }
  ];

  constructor(private fb: FormBuilder, private snackBar: MatSnackBar) {
    this.contactForm = this.fb.group({
      name: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      subject: ['', Validators.required],
      department: [''],
      message: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    // Initialize the component
  }

  submitForm(): void {
    if (this.contactForm.valid) {
      console.log('Form submitted:', this.contactForm.value);
      // Here you would typically send the form data to a server

      // Show success message
      this.snackBar.open('Your message has been sent. We will contact you soon.', 'Close', {
        duration: 5000,
        horizontalPosition: 'center',
        verticalPosition: 'bottom'
      });

      // Reset the form after submission
      this.contactForm.reset();
    } else {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.contactForm.controls).forEach(key => {
        const control = this.contactForm.get(key);
        control?.markAsTouched();
      });

      // Show error message
      this.snackBar.open('Please fill in all required fields correctly.', 'Close', {
        duration: 5000,
        horizontalPosition: 'center',
        verticalPosition: 'bottom'
      });
    }
  }

  // Helper method to get social media icon class
  getSocialIconClass(icon: string): string {
    return `fa-brands fa-${icon}`;
  }
}
