using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using School.Application.DTOs;
using School.Application.Features.Auth;
using System;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Web;

namespace School.Infrastructure.Identity
{
    public class MfaService : IMfaService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<MfaService> _logger;

        public MfaService(
            UserManager<ApplicationUser> userManager,
            ILogger<MfaService> logger)
        {
            _userManager = userManager;
            _logger = logger;
        }

        public async Task<MfaSetupResponseDto> GenerateMfaSetupAsync(string userId, string password)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                throw new InvalidOperationException("User not found");

            // Verify password
            var passwordValid = await _userManager.CheckPasswordAsync(user, password);
            if (!passwordValid)
                throw new UnauthorizedAccessException("Invalid password");

            // Generate secret
            var secret = GenerateSecret();
            var backupCodes = GenerateBackupCodes();
            var qrCodeUrl = GenerateQrCodeUrl(user.Email!, secret);

            _logger.LogInformation("MFA setup initiated for user {UserId}", userId);

            return new MfaSetupResponseDto
            {
                Secret = secret,
                QrCodeUrl = qrCodeUrl,
                BackupCodes = backupCodes
            };
        }

        public async Task<bool> VerifyMfaSetupAsync(string userId, string secret, string code)
        {
            return VerifyTotpCode(secret, code);
        }

        public async Task<bool> EnableMfaAsync(string userId, string secret, string code)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                return false;

            // Verify the code first
            if (!VerifyTotpCode(secret, code))
                return false;

            // Generate backup codes
            var backupCodes = GenerateBackupCodes();

            // Update user
            user.IsMfaEnabled = true;
            user.MfaSecret = secret;
            user.MfaBackupCodes = JsonSerializer.Serialize(backupCodes);
            user.LastMfaSetup = DateTime.UtcNow;

            var result = await _userManager.UpdateAsync(user);
            if (result.Succeeded)
            {
                _logger.LogInformation("MFA enabled for user {UserId}", userId);
                return true;
            }

            _logger.LogError("Failed to enable MFA for user {UserId}: {Errors}", 
                userId, string.Join(", ", result.Errors));
            return false;
        }

        public async Task<bool> DisableMfaAsync(string userId, string password, string mfaCode)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                return false;

            // Verify password
            var passwordValid = await _userManager.CheckPasswordAsync(user, password);
            if (!passwordValid)
                return false;

            // Verify MFA code
            if (!await VerifyMfaCodeAsync(userId, mfaCode))
                return false;

            // Disable MFA
            user.IsMfaEnabled = false;
            user.MfaSecret = null;
            user.MfaBackupCodes = null;

            var result = await _userManager.UpdateAsync(user);
            if (result.Succeeded)
            {
                _logger.LogInformation("MFA disabled for user {UserId}", userId);
                return true;
            }

            return false;
        }

        public async Task<bool> VerifyMfaCodeAsync(string userId, string code)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null || !user.IsMfaEnabled || string.IsNullOrEmpty(user.MfaSecret))
                return false;

            return VerifyTotpCode(user.MfaSecret, code);
        }

        public async Task<bool> VerifyBackupCodeAsync(string userId, string backupCode)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null || !user.IsMfaEnabled || string.IsNullOrEmpty(user.MfaBackupCodes))
                return false;

            try
            {
                var backupCodes = JsonSerializer.Deserialize<string[]>(user.MfaBackupCodes);
                if (backupCodes == null)
                    return false;

                // Check if backup code exists and remove it (one-time use)
                var codeIndex = Array.IndexOf(backupCodes, backupCode);
                if (codeIndex == -1)
                    return false;

                // Remove the used backup code
                var newBackupCodes = new string[backupCodes.Length - 1];
                Array.Copy(backupCodes, 0, newBackupCodes, 0, codeIndex);
                Array.Copy(backupCodes, codeIndex + 1, newBackupCodes, codeIndex, backupCodes.Length - codeIndex - 1);

                user.MfaBackupCodes = JsonSerializer.Serialize(newBackupCodes);
                await _userManager.UpdateAsync(user);

                _logger.LogInformation("Backup code used for user {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying backup code for user {UserId}", userId);
                return false;
            }
        }

        public async Task<string[]> GenerateNewBackupCodesAsync(string userId, string password, string mfaCode)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                throw new InvalidOperationException("User not found");

            // Verify password
            var passwordValid = await _userManager.CheckPasswordAsync(user, password);
            if (!passwordValid)
                throw new UnauthorizedAccessException("Invalid password");

            // Verify MFA code
            if (!await VerifyMfaCodeAsync(userId, mfaCode))
                throw new UnauthorizedAccessException("Invalid MFA code");

            // Generate new backup codes
            var backupCodes = GenerateBackupCodes();
            user.MfaBackupCodes = JsonSerializer.Serialize(backupCodes);

            await _userManager.UpdateAsync(user);

            _logger.LogInformation("New backup codes generated for user {UserId}", userId);
            return backupCodes;
        }

        public string GenerateQrCodeUrl(string userEmail, string secret, string issuer = "School Management System")
        {
            var encodedIssuer = HttpUtility.UrlEncode(issuer);
            var encodedEmail = HttpUtility.UrlEncode(userEmail);
            return $"otpauth://totp/{encodedIssuer}:{encodedEmail}?secret={secret}&issuer={encodedIssuer}";
        }

        private string GenerateSecret()
        {
            var bytes = new byte[20];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(bytes);
            }
            return Convert.ToBase32String(bytes);
        }

        private string[] GenerateBackupCodes()
        {
            var codes = new string[10];
            using (var rng = RandomNumberGenerator.Create())
            {
                for (int i = 0; i < codes.Length; i++)
                {
                    var bytes = new byte[4];
                    rng.GetBytes(bytes);
                    codes[i] = BitConverter.ToUInt32(bytes, 0).ToString("D8");
                }
            }
            return codes;
        }

        private bool VerifyTotpCode(string secret, string code)
        {
            if (string.IsNullOrEmpty(secret) || string.IsNullOrEmpty(code))
                return false;

            try
            {
                var secretBytes = Convert.FromBase32String(secret);
                var unixTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var timeStep = unixTime / 30;

                // Check current time step and previous/next for clock skew
                for (int i = -1; i <= 1; i++)
                {
                    var testTimeStep = timeStep + i;
                    var hash = ComputeHmacSha1(secretBytes, BitConverter.GetBytes(testTimeStep).Reverse().ToArray());
                    var offset = hash[hash.Length - 1] & 0x0F;
                    var truncatedHash = ((hash[offset] & 0x7F) << 24) |
                                      ((hash[offset + 1] & 0xFF) << 16) |
                                      ((hash[offset + 2] & 0xFF) << 8) |
                                      (hash[offset + 3] & 0xFF);
                    var totpCode = (truncatedHash % 1000000).ToString("D6");

                    if (totpCode == code)
                        return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying TOTP code");
                return false;
            }
        }

        private byte[] ComputeHmacSha1(byte[] key, byte[] data)
        {
            using (var hmac = new HMACSHA1(key))
            {
                return hmac.ComputeHash(data);
            }
        }
    }

    // Helper class for Base32 encoding
    public static class Convert
    {
        private const string Base32Alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";

        public static string ToBase32String(byte[] bytes)
        {
            if (bytes == null || bytes.Length == 0)
                return string.Empty;

            var result = new StringBuilder();
            int buffer = bytes[0];
            int next = 1;
            int bitsLeft = 8;

            while (bitsLeft > 0 || next < bytes.Length)
            {
                if (bitsLeft < 5)
                {
                    if (next < bytes.Length)
                    {
                        buffer <<= 8;
                        buffer |= bytes[next++] & 0xFF;
                        bitsLeft += 8;
                    }
                    else
                    {
                        int pad = 5 - bitsLeft;
                        buffer <<= pad;
                        bitsLeft += pad;
                    }
                }

                int index = 0x1F & (buffer >> (bitsLeft - 5));
                bitsLeft -= 5;
                result.Append(Base32Alphabet[index]);
            }

            return result.ToString();
        }

        public static byte[] FromBase32String(string base32)
        {
            if (string.IsNullOrEmpty(base32))
                return Array.Empty<byte>();

            base32 = base32.ToUpperInvariant();
            var result = new List<byte>();
            int buffer = 0;
            int bitsLeft = 0;

            foreach (char c in base32)
            {
                int value = Base32Alphabet.IndexOf(c);
                if (value < 0)
                    continue;

                buffer = (buffer << 5) | value;
                bitsLeft += 5;

                if (bitsLeft >= 8)
                {
                    result.Add((byte)(buffer >> (bitsLeft - 8)));
                    bitsLeft -= 8;
                }
            }

            return result.ToArray();
        }
    }
}
