using System;

namespace School.Application.DTOs
{
    public class ClubAdvisorDto
    {
        public Guid id { get; set; }
        public int ClubId { get; set; }
        public int? FacultyId { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public int DisplayOrder { get; set; }
        
        // Related faculty (if available)
        public FacultyDto Faculty { get; set; }
    }
}
