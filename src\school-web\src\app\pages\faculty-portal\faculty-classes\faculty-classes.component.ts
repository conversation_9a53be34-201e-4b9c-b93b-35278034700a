import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatDialogModule } from '@angular/material/dialog';
import { FacultyService } from '../../../core/services/faculty.service';

@Component({
  selector: 'app-faculty-classes',
  templateUrl: './faculty-classes.component.html',
  styleUrls: ['./faculty-classes.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatDialogModule
  ]
})
export class FacultyClassesComponent implements OnInit {
  classes: any[] = [];
  isLoading = true;
  error: string | null = null;
  
  // Class details
  selectedClass: any = null;
  students: any[] = [];
  isLoadingStudents = false;
  
  // Display columns for the classes table
  displayedColumns: string[] = ['className', 'section', 'subject', 'schedule', 'actions'];
  
  constructor(private facultyService: FacultyService) { }

  ngOnInit(): void {
    this.loadClasses();
  }

  loadClasses(): void {
    this.isLoading = true;
    this.error = null;
    
    // Replace with actual API call
    setTimeout(() => {
      this.classes = [
        {
          id: 1,
          className: 'Class 6',
          section: 'A',
          subject: 'Mathematics',
          schedule: 'Mon, Wed, Fri 9:00 AM - 10:00 AM',
          totalStudents: 35
        },
        {
          id: 2,
          className: 'Class 7',
          section: 'B',
          subject: 'Science',
          schedule: 'Tue, Thu 10:30 AM - 12:00 PM',
          totalStudents: 32
        },
        {
          id: 3,
          className: 'Class 8',
          section: 'A',
          subject: 'English',
          schedule: 'Mon, Wed 1:00 PM - 2:30 PM',
          totalStudents: 30
        }
      ];
      this.isLoading = false;
    }, 1000);
  }

  viewClassDetails(classItem: any): void {
    this.selectedClass = classItem;
    this.loadStudents(classItem.id);
  }

  loadStudents(classId: number): void {
    this.isLoadingStudents = true;
    
    // Replace with actual API call
    setTimeout(() => {
      this.students = Array(this.selectedClass.totalStudents).fill(0).map((_, i) => ({
        id: i + 1,
        name: `Student ${i + 1}`,
        rollNumber: `R-${this.selectedClass.className.split(' ')[1]}${this.selectedClass.section}-${i + 1}`,
        attendance: Math.floor(Math.random() * 20) + 80, // Random attendance between 80-100%
        performance: ['Excellent', 'Good', 'Average', 'Needs Improvement'][Math.floor(Math.random() * 4)]
      }));
      this.isLoadingStudents = false;
    }, 1000);
  }

  backToClasses(): void {
    this.selectedClass = null;
    this.students = [];
  }
}
