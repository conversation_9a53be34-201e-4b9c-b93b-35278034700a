import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { AuthService } from '../../core/services/auth.service';
import { AlumniService } from '../../core/services/alumni.service';
import { Alumni } from '../../core/models/alumni.model';

@Component({
  selector: 'app-alumni-portal',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatToolbarModule,
    MatSidenavModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  templateUrl: './alumni-portal.component.html',
  styleUrls: ['./alumni-portal.component.scss']
})
export class AlumniPortalComponent implements OnInit {
  alumni: Alumni | null = null;
  loading = true;
  error = false;

  navItems = [
    { label: 'Dashboard', icon: 'dashboard', route: '/alumni-portal/dashboard' },
    { label: 'Profile', icon: 'person', route: '/alumni-portal/profile' },
    { label: 'Alumni Network', icon: 'people', route: '/alumni-portal/network' },
    { label: 'Events', icon: 'event', route: '/alumni-portal/events' },
    { label: 'Donations', icon: 'volunteer_activism', route: '/alumni-portal/donations' },
    { label: 'Job Board', icon: 'work', route: '/alumni-portal/jobs' }
  ];

  constructor(
    private authService: AuthService,
    private alumniService: AlumniService,
    private router: Router,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadAlumniData();
  }

  loadAlumniData(): void {
    this.loading = true;
    this.error = false;

    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      this.router.navigate(['/login']);
      return;
    }

    this.alumniService.getAlumniById(currentUser.id).subscribe({
      next: (alumni) => {
        this.alumni = alumni;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading alumni data:', error);
        this.error = true;
        this.loading = false;
        this.snackBar.open('Failed to load alumni data', 'Close', {
          duration: 3000
        });
      }
    });
  }

  logout(): void {
    this.authService.logout().subscribe({
      next: () => {
        this.router.navigate(['/login']);
      },
      error: (error) => {
        console.error('Logout error:', error);
        this.snackBar.open('Error during logout', 'Close', {
          duration: 3000
        });
      }
    });
  }
}
