<div class="attendance-container">
  <h1 class="page-title">Student Attendance</h1>

  <!-- Filter Form -->
  <mat-card class="filter-card">
    <mat-card-header>
      <mat-card-title>Filter Attendance Records</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="filterForm" (ngSubmit)="loadAttendance()">
        <div class="filter-form">
          <mat-form-field appearance="outline">
            <mat-label>From Date</mat-label>
            <input matInput [matDatepicker]="fromPicker" formControlName="fromDate">
            <mat-datepicker-toggle matSuffix [for]="fromPicker"></mat-datepicker-toggle>
            <mat-datepicker #fromPicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>To Date</mat-label>
            <input matInput [matDatepicker]="toPicker" formControlName="toDate">
            <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
            <mat-datepicker #toPicker></mat-datepicker>
          </mat-form-field>

          <div class="filter-actions">
            <button mat-raised-button color="primary" type="submit">
              Apply Filter
            </button>
          </div>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Loading Indicator -->
  <div *ngIf="loading.attendance" class="attendance-loading">
    <mat-progress-bar mode="indeterminate"></mat-progress-bar>
  </div>

  <!-- Error Message -->
  <div *ngIf="error.attendance" class="attendance-error">
    <mat-error>
      <mat-icon>error</mat-icon>
      <span>Failed to load attendance records. Please try again.</span>
      <button mat-button color="warn" (click)="loadAttendance()">Retry</button>
    </mat-error>
  </div>

  <!-- Attendance Table -->
  <div *ngIf="!loading.attendance && !error.attendance && attendanceRecords.length > 0" class="attendance-table-container mat-elevation-z8">
    <table mat-table [dataSource]="attendanceRecords" class="attendance-table">
      <!-- Date Column -->
      <ng-container matColumnDef="date">
        <th mat-header-cell *matHeaderCellDef>Date</th>
        <td mat-cell *matCellDef="let record">{{ record.date | date:'mediumDate' }}</td>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>Status</th>
        <td mat-cell *matCellDef="let record">
          <span class="status-badge" [ngClass]="getStatusClass(record.status)">
            {{ getStatusText(record.status) }}
          </span>
        </td>
      </ng-container>

      <!-- Period Column -->
      <ng-container matColumnDef="period">
        <th mat-header-cell *matHeaderCellDef>Period</th>
        <td mat-cell *matCellDef="let record">{{ record.period }}</td>
      </ng-container>

      <!-- Subject Column -->
      <ng-container matColumnDef="subjectCode">
        <th mat-header-cell *matHeaderCellDef>Subject</th>
        <td mat-cell *matCellDef="let record">{{ record.subjectCode }}</td>
      </ng-container>

      <!-- Remarks Column -->
      <ng-container matColumnDef="remarks">
        <th mat-header-cell *matHeaderCellDef>Remarks</th>
        <td mat-cell *matCellDef="let record">{{ record.remarks }}</td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>

  <!-- No Data Message -->
  <div *ngIf="!loading.attendance && !error.attendance && attendanceRecords.length === 0" class="no-data">
    <mat-card>
      <mat-card-content>
        <p>No attendance records found for the selected date range.</p>
      </mat-card-content>
    </mat-card>
  </div>
</div>
