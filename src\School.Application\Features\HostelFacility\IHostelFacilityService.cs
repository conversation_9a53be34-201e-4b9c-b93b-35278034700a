using School.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace School.Application.Features.HostelFacility
{
    public interface IHostelFacilityService
    {
        Task<(IEnumerable<HostelFacilityDto> Facilities, int TotalCount)> GetAllFacilitiesAsync(HostelFacilityFilterDto filter);
        Task<HostelFacilityDto?> GetFacilityByIdAsync(Guid id);
        Task<Guid> CreateFacilityAsync(CreateHostelFacilityDto facilityDto);
        Task<bool> UpdateFacilityAsync(Guid id, UpdateHostelFacilityDto facilityDto);
        Task<bool> DeleteFacilityAsync(Guid id);
    }
}