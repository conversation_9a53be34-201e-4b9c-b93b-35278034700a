using School.Domain.Common;

namespace School.Domain.Entities;

public class HostelFacilityTranslation : BaseEntity
{
    public Guid HostelFacilityId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;

    // Navigation properties
    public HostelFacility HostelFacility { get; set; } = null!;
}
