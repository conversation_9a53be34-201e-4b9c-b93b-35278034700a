import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../../shared/components/default-hero/default-hero.component';

interface Program {
  title: string;
  description: string;
  icon: string;
}

interface Course {
  name: string;
  description: string;
  credits: string;
  grades: string;
}

interface CollegeAcceptance {
  university: string;
  location: string;
}

@Component({
  selector: 'app-high',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatDividerModule,
    MatTableModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './high.component.html',
  styleUrls: ['./high.component.scss']
})
export class HighComponent {
  // Key features of the high school program
  keyFeatures: Program[] = [
    {
      title: 'College Preparatory Curriculum',
      description: 'Our rigorous academic program prepares students for success in selective colleges and universities through advanced coursework and comprehensive college counseling.',
      icon: 'school'
    },
    {
      title: 'Advanced Placement (AP) Courses',
      description: 'We offer a wide range of AP courses that allow students to earn college credit and demonstrate their commitment to academic excellence.',
      icon: 'workspace_premium'
    },
    {
      title: 'Personalized Learning',
      description: 'Students have the flexibility to pursue their interests and strengths through elective courses, independent study options, and specialized academic tracks.',
      icon: 'person'
    },
    {
      title: 'Global Perspective',
      description: 'Through international exchanges, global issues courses, and world languages, students develop the cultural competence needed in our interconnected world.',
      icon: 'public'
    },
    {
      title: 'Leadership Development',
      description: 'Students have numerous opportunities to develop leadership skills through student government, clubs, athletics, community service, and mentoring programs.',
      icon: 'groups'
    },
    {
      title: 'College Counseling',
      description: 'Our dedicated college counselors work closely with students and families throughout the college search and application process to find the best fit for each student.',
      icon: 'psychology'
    }
  ];

  // Departments
  departments = [
    {
      name: 'English',
      description: 'Our English curriculum develops critical reading, analytical thinking, and effective communication skills through the study of literature, composition, and rhetoric.',
      courses: ['English 9: World Literature', 'English 10: American Literature', 'English 11: British Literature', 'AP English Language', 'AP English Literature', 'Creative Writing', 'Journalism']
    },
    {
      name: 'Mathematics',
      description: 'Our mathematics program builds problem-solving skills, logical reasoning, and quantitative literacy through a sequence of courses from Algebra to advanced topics.',
      courses: ['Algebra I', 'Geometry', 'Algebra II', 'Pre-Calculus', 'AP Calculus AB', 'AP Calculus BC', 'AP Statistics', 'Multivariable Calculus']
    },
    {
      name: 'Science',
      description: 'Through laboratory investigations and research projects, students develop scientific inquiry skills and explore concepts in biology, chemistry, physics, and environmental science.',
      courses: ['Biology', 'Chemistry', 'Physics', 'AP Biology', 'AP Chemistry', 'AP Physics', 'AP Environmental Science', 'Anatomy & Physiology']
    },
    {
      name: 'Social Studies',
      description: 'Students examine historical events, cultural developments, and contemporary issues while developing research skills, critical thinking, and global awareness.',
      courses: ['World History', 'U.S. History', 'Government & Economics', 'AP U.S. History', 'AP World History', 'AP Government', 'AP Economics', 'Psychology']
    },
    {
      name: 'World Languages',
      description: 'Students develop proficiency in listening, speaking, reading, and writing in their chosen language while exploring the cultures of the target language.',
      courses: ['Spanish I-V', 'French I-V', 'Mandarin I-V', 'AP Spanish Language', 'AP French Language', 'AP Chinese Language']
    },
    {
      name: 'Arts',
      description: 'Our arts program provides opportunities for creative expression and skill development in visual arts, music, theater, and digital media.',
      courses: ['Studio Art', 'AP Studio Art', 'Photography', 'Digital Media', 'Concert Band', 'Jazz Ensemble', 'Choir', 'Theater Arts', 'Advanced Theater']
    }
  ];

  // Sample courses
  sampleCourses: Course[] = [
    {
      name: 'AP Calculus BC',
      description: 'This college-level course covers limits, derivatives, integrals, and series, preparing students for the AP Calculus BC exam and further study in mathematics, science, and engineering.',
      credits: '1.0',
      grades: '11-12'
    },
    {
      name: 'AP Biology',
      description: 'This course provides an in-depth exploration of biological concepts including cellular processes, genetics, evolution, and ecology, with extensive laboratory investigations.',
      credits: '1.0',
      grades: '11-12'
    },
    {
      name: 'AP English Literature',
      description: 'Students engage in close reading and critical analysis of imaginative literature, deepening their understanding of the ways writers use language to provide meaning and pleasure.',
      credits: '1.0',
      grades: '12'
    },
    {
      name: 'Global Issues',
      description: 'This interdisciplinary course examines contemporary global challenges such as climate change, human rights, economic inequality, and international conflict.',
      credits: '0.5',
      grades: '10-12'
    },
    {
      name: 'Entrepreneurship',
      description: 'Students learn the principles of business development, marketing, finance, and innovation while creating and implementing their own business plans.',
      credits: '0.5',
      grades: '11-12'
    },
    {
      name: 'Digital Media Production',
      description: 'This course focuses on the creation of digital content including video, audio, graphics, and interactive media for various platforms and audiences.',
      credits: '0.5',
      grades: '9-12'
    }
  ];

  // Graduation requirements
  graduationRequirements = [
    { subject: 'English', credits: '4.0' },
    { subject: 'Mathematics', credits: '4.0' },
    { subject: 'Science', credits: '3.0' },
    { subject: 'Social Studies', credits: '3.0' },
    { subject: 'World Languages', credits: '3.0' },
    { subject: 'Arts', credits: '1.0' },
    { subject: 'Physical Education/Health', credits: '1.0' },
    { subject: 'Electives', credits: '5.0' },
    { subject: 'Total Required', credits: '24.0' }
  ];

  // Special programs
  specialPrograms = [
    {
      title: 'International Exchange',
      description: 'Students have the opportunity to study abroad for a semester or year at one of our partner schools in Europe, Asia, or South America.',
      image: 'assets/images/academics/high-exchange.jpg'
    },
    {
      title: 'Senior Capstone Project',
      description: 'Seniors design and complete an independent research or creative project that demonstrates their learning and passion in a specific area of interest.',
      image: 'assets/images/academics/high-capstone.jpg'
    },
    {
      title: 'Internship Program',
      description: 'Juniors and seniors can participate in internships with local businesses, non-profits, and research institutions to gain real-world experience.',
      image: 'assets/images/academics/high-internship.jpg'
    },
    {
      title: 'STEM Research',
      description: 'Students conduct original scientific research under the guidance of faculty mentors and present their findings at regional and national competitions.',
      image: 'assets/images/academics/high-research.jpg'
    }
  ];

  // College acceptances
  collegeAcceptances: CollegeAcceptance[] = [
    { university: 'Harvard University', location: 'Cambridge, MA' },
    { university: 'Stanford University', location: 'Stanford, CA' },
    { university: 'Massachusetts Institute of Technology', location: 'Cambridge, MA' },
    { university: 'Yale University', location: 'New Haven, CT' },
    { university: 'Princeton University', location: 'Princeton, NJ' },
    { university: 'Columbia University', location: 'New York, NY' },
    { university: 'University of Chicago', location: 'Chicago, IL' },
    { university: 'Duke University', location: 'Durham, NC' },
    { university: 'University of California, Berkeley', location: 'Berkeley, CA' },
    { university: 'University of Michigan', location: 'Ann Arbor, MI' },
    { university: 'New York University', location: 'New York, NY' },
    { university: 'University of Texas at Austin', location: 'Austin, TX' },
    { university: 'University of Washington', location: 'Seattle, WA' },
    { university: 'Boston University', location: 'Boston, MA' },
    { university: 'University of Wisconsin-Madison', location: 'Madison, WI' }
  ];

  // Extracurricular activities
  extracurriculars = [
    {
      category: 'Athletics',
      activities: ['Basketball', 'Soccer', 'Track & Field', 'Volleyball', 'Cross Country', 'Tennis', 'Swimming', 'Golf', 'Baseball/Softball']
    },
    {
      category: 'Arts',
      activities: ['Wind Ensemble', 'Jazz Band', 'Chamber Choir', 'Theater Productions', 'Art Club', 'Photography Club', 'Film Society']
    },
    {
      category: 'Academic Clubs',
      activities: ['Math Team', 'Science Olympiad', 'Debate Team', 'Model UN', 'Robotics Club', 'Coding Club', 'Literary Magazine']
    },
    {
      category: 'Student Leadership',
      activities: ['Student Government', 'Honor Council', 'Peer Tutoring', 'Yearbook Committee', 'Environmental Club', 'Community Service Club']
    }
  ];
}
