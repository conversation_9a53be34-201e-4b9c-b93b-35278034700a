import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ApiResponse } from '../models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class ApiResponseHandlerService {
  constructor(private snackBar: MatSnackBar) {}

  /**
   * Handle successful API responses
   * @param response The API response
   * @param showSuccessMessage Whether to show a success message
   * @returns The data from the API response
   */
  handleSuccess<T>(response: ApiResponse<T>, showSuccessMessage: boolean = false): T {
    if (response && response.success && response.data !== undefined) {
      if (showSuccessMessage && response.message) {
        this.showSuccessMessage(response.message);
      }
      return response.data;
    } else {
      console.error('Invalid API response format:', response);
      throw new Error('Invalid API response format');
    }
  }

  /**
   * Handle API errors
   * @param error The HTTP error response
   * @param fallbackMessage A fallback error message
   * @returns An observable that errors with the error message
   */
  handleError(error: HttpErrorResponse, fallbackMessage: string = 'An error occurred'): Observable<never> {
    let errorMessage = fallbackMessage;

    if (error.error && error.error.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    this.showErrorMessage(errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  /**
   * Process an observable API response
   * @param source The source observable
   * @param showSuccessMessage Whether to show a success message
   * @param fallbackErrorMessage A fallback error message
   * @returns An observable that emits the data from the API response
   */
  processResponse<T>(
    source: Observable<ApiResponse<T>>,
    showSuccessMessage: boolean = false,
    fallbackErrorMessage: string = 'An error occurred'
  ): Observable<T> {
    return source.pipe(
      map(response => this.handleSuccess(response, showSuccessMessage)),
      catchError(error => this.handleError(error, fallbackErrorMessage))
    );
  }

  /**
   * Handle API response
   * @param response The API response
   * @returns The data from the API response
   */
  handleResponse<T>(response: ApiResponse<T>): T {
    return this.handleSuccess(response, false);
  }

  /**
   * Show a success message
   * @param message The success message
   */
  showSuccessMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  /**
   * Show an error message
   * @param message The error message
   */
  showErrorMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }
}
