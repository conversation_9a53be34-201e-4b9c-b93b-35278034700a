import { Component, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-quick-links',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    TranslateModule
  ],
  templateUrl: './quick-links.component.html',
  styleUrls: ['./quick-links.component.scss']
})
export class QuickLinksComponent {
  @ViewChild('quickLinksMenuTrigger') quickLinksMenuTrigger!: MatMenuTrigger;
  
  private closeTimeout: any;
  
  openMenu(trigger: MatMenuTrigger): void {
    if (this.closeTimeout) {
      clearTimeout(this.closeTimeout);
      this.closeTimeout = null;
    }
    trigger.openMenu();
  }
  
  closeMenu(): void {
    this.closeTimeout = setTimeout(() => {
      if (this.quickLinksMenuTrigger.menuOpen) {
        this.quickLinksMenuTrigger.closeMenu();
      }
    }, 100);
  }
  
  clearCloseTimeout(): void {
    if (this.closeTimeout) {
      clearTimeout(this.closeTimeout);
      this.closeTimeout = null;
    }
  }
}
