using School.Domain.Common;

namespace School.Domain.Entities;

public class AlumniTestimonial : BaseEntity
{
    public Guid AlumniId { get; set; }
    public string Content { get; set; } = string.Empty;
    public bool IsApproved { get; set; } = false;
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }

    // Navigation properties
    public Alumni Alumni { get; set; } = null!;
    public ICollection<AlumniTestimonialTranslation> Translations { get; set; } = new List<AlumniTestimonialTranslation>();
    public DateTime? UpdatedAt { get; set; }
}
