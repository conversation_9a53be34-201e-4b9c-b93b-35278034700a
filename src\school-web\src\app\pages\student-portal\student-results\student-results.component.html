<div class="results-container">
  <h1 class="page-title">Academic Results</h1>

  <div *ngIf="loading.student" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error.student" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load student data. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadStudentData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading.student && !error.student && student" class="results-content">
    <!-- Filter Form -->
    <mat-card class="filter-card">
      <mat-card-content>
        <form [formGroup]="filterForm" (ngSubmit)="applyFilter()">
          <div class="filter-form">
            <mat-form-field appearance="outline">
              <mat-label>Academic Year</mat-label>
              <input matInput type="number" formControlName="academicYear">
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Exam Type</mat-label>
              <mat-select formControlName="examType">
                <mat-option *ngFor="let type of examTypes" [value]="type.value">
                  {{ type.label }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <div class="filter-actions">
              <button mat-raised-button color="primary" type="submit">View Results</button>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>

    <!-- GPA Card -->
    <mat-card class="gpa-card" *ngIf="!loading.gpa && !error.gpa && gpa !== null">
      <mat-card-content>
        <div class="gpa-display">
          <div class="gpa-value">{{ gpa !== null ? gpa.toFixed(2) : 'N/A' }}</div>
          <div class="gpa-label">GPA</div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Loading Indicator -->
    <div *ngIf="loading.results" class="results-loading">
      <mat-progress-bar mode="indeterminate"></mat-progress-bar>
    </div>

    <!-- Error Message -->
    <div *ngIf="error.results" class="results-error">
      <mat-error>
        <mat-icon>error</mat-icon>
        <span>Failed to load results. Please try again.</span>
        <button mat-button color="warn" (click)="loadResults()">Retry</button>
      </mat-error>
    </div>

    <!-- Results Table -->
    <div *ngIf="!loading.results && !error.results" class="results-table-container">
      <div *ngIf="results.length === 0" class="no-results">
        <p>No results found for the selected criteria.</p>
      </div>

      <div *ngIf="results.length > 0" class="results-table">
        <table>
          <thead>
            <tr>
              <th>Subject</th>
              <th>Marks</th>
              <th>Grade</th>
              <th>Grade Point</th>
              <th>Remarks</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let result of results" [class.optional-subject]="result.isOptional">
              <td>
                {{ result.subjectName }}
                <span *ngIf="result.isOptional" class="optional-badge">Optional</span>
              </td>
              <td>{{ result.marksObtained }} / {{ result.totalMarks }}</td>
              <td>
                <span class="grade-badge" [style.background-color]="getGradeColor(result.letterGrade)">
                  {{ result.letterGrade }}
                </span>
              </td>
              <td>{{ result.gradePoint.toFixed(2) }}</td>
              <td>{{ result.remarks }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Result Summary -->
    <mat-card *ngIf="!loading.results && !error.results && results.length > 0" class="summary-card">
      <mat-card-header>
        <mat-card-title>Result Summary</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="summary-grid">
          <div class="summary-item">
            <span class="summary-label">Exam Type</span>
            <span class="summary-value">{{ getExamTypeLabel(filterForm.value.examType) }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Academic Year</span>
            <span class="summary-value">{{ filterForm.value.academicYear }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Total Subjects</span>
            <span class="summary-value">{{ results.length }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Optional Subjects</span>
            <span class="summary-value">{{ getOptionalSubjectsCount() }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">GPA</span>
            <span class="summary-value">{{ gpa !== null ? gpa.toFixed(2) : 'N/A' }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
