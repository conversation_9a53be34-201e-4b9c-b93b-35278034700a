import { Directive, ElementRef, HostListener, OnDestroy } from '@angular/core';

@Directive({
  selector: '[appHoverMenuPanel]',
  standalone: true
})
export class HoverMenuPanelDirective implements OnDestroy {
  private timeoutId: any;

  constructor(private elementRef: ElementRef) {}

  ngOnDestroy(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  @HostListener('mouseenter')
  onMouseEnter(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  @HostListener('mouseleave')
  onMouseLeave(): void {
    this.timeoutId = setTimeout(() => {
      // Find and click the backdrop to close the menu
      const backdrop = document.querySelector('.cdk-overlay-backdrop');
      if (backdrop) {
        (backdrop as HTMLElement).click();
      }
    }, 300); // Delay to allow moving back to the trigger
  }
}
