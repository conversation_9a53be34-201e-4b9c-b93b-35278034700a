namespace School.Domain.Enums;

/// <summary>
/// Status of an organization in the system
/// </summary>
public enum OrganizationStatus
{
    /// <summary>
    /// Organization is pending approval/setup
    /// </summary>
    Pending = 1,

    /// <summary>
    /// Organization is active and operational
    /// </summary>
    Active = 2,

    /// <summary>
    /// Organization is temporarily suspended
    /// </summary>
    Suspended = 3,

    /// <summary>
    /// Organization is inactive but can be reactivated
    /// </summary>
    Inactive = 4,

    /// <summary>
    /// Organization has been permanently closed
    /// </summary>
    Closed = 5,

    /// <summary>
    /// Organization is in trial period
    /// </summary>
    Trial = 6,

    /// <summary>
    /// Organization setup is in progress
    /// </summary>
    Setup = 7
}
