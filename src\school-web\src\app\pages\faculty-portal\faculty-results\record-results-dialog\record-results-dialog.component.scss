.dialog-subtitle {
  margin-top: -16px;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.6);
  font-weight: normal;
  font-size: 14px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.total-marks-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;

  mat-form-field {
    width: 150px;
  }
}

.students-table-container {
  max-height: 400px;
  overflow-y: auto;
}

.students-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 8px;
    text-align: left;
    vertical-align: middle;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  }

  th {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.87);
    background-color: #f5f5f5;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  mat-form-field {
    width: 100%;
    margin: 0;
  }
}

.grade-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

mat-dialog-actions {
  padding: 16px 0;
}

button[type="submit"] {
  min-width: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
}

mat-spinner {
  margin-right: 8px;
}
