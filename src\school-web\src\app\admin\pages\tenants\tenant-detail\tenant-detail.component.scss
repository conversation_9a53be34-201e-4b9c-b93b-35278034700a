.tenant-detail-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  gap: 16px;

  .header-content {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;

    .back-button {
      color: var(--primary-color);
    }

    .tenant-info {
      display: flex;
      align-items: center;
      gap: 16px;

      .tenant-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56px;
        height: 56px;
        border-radius: 12px;
        background-color: var(--primary-color);
        color: white;

        mat-icon {
          font-size: 28px;
          width: 28px;
          height: 28px;
        }
      }

      .tenant-details {
        .tenant-name {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 500;
          color: var(--text-primary);
        }

        .tenant-meta {
          display: flex;
          align-items: center;
          gap: 12px;
          margin: 0;

          .tenant-slug {
            font-family: 'Courier New', monospace;
            background-color: var(--background-secondary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

.tenant-tabs {
  .tab-content {
    padding: 24px 0;
  }
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.info-card {
  mat-card-header {
    margin-bottom: 16px;

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 500;

      mat-icon {
        color: var(--primary-color);
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }

  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-weight: 500;
      color: var(--text-secondary);
      min-width: 120px;
    }

    .value {
      color: var(--text-primary);
      text-align: right;
      flex: 1;
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;

    .stat-item {
      text-align: center;
      padding: 16px;
      border-radius: 8px;
      background-color: var(--background-secondary);

      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

.users-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .users-table-card {
    .users-table {
      width: 100%;

      .user-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .user-details {
          .user-name {
            font-weight: 500;
            margin-bottom: 2px;
          }

          .user-email {
            font-size: 12px;
            color: var(--text-secondary);
          }
        }
      }

      .granted-info {
        .granted-date {
          font-size: 13px;
          margin-bottom: 2px;
        }

        .granted-by {
          font-size: 11px;
          color: var(--text-secondary);
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px;
      text-align: center;
      gap: 16px;

      mat-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        color: var(--text-secondary);
        opacity: 0.5;
      }

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
      }

      p {
        margin: 0;
        color: var(--text-secondary);
        max-width: 300px;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  gap: 16px;

  p {
    margin: 0;
    color: var(--text-secondary);
  }
}

// Responsive design
@media (max-width: 768px) {
  .tenant-detail-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;

    .header-content {
      .tenant-info {
        .tenant-icon {
          width: 48px;
          height: 48px;

          mat-icon {
            font-size: 24px;
            width: 24px;
            height: 24px;
          }
        }

        .tenant-details {
          .tenant-name {
            font-size: 20px;
          }

          .tenant-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
        }
      }
    }

    .header-actions {
      justify-content: flex-end;
      flex-wrap: wrap;
    }
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .users-section {
    .section-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .tenant-slug {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .stat-item {
    background-color: rgba(255, 255, 255, 0.05);
  }
}
