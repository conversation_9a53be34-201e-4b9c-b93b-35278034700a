// Admin Dashboard Theme Enhancements
// Specific styling for dashboard components with better contrast

// Dashboard card styling
.dashboard-card {
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
}

// Statistics cards
.stats-card {
  .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
  }
  
  .stats-label {
    font-size: 0.875rem;
    font-weight: 500;
    opacity: 0.8;
    margin-top: 8px;
  }
  
  .stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
  }
}

// Activity feed styling
.activity-item {
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateX(4px);
  }
  
  .activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .activity-content {
    flex: 1;
    
    .activity-title {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .activity-time {
      font-size: 0.75rem;
      opacity: 0.7;
    }
  }
}

// Task list styling
.task-item {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateX(2px);
  }
  
  .task-content {
    flex: 1;
    
    .task-title {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .task-meta {
      display: flex;
      gap: 16px;
      font-size: 0.75rem;
      opacity: 0.7;
    }
  }
  
  .task-actions {
    display: flex;
    gap: 8px;
    
    button {
      min-width: 32px;
      width: 32px;
      height: 32px;
      padding: 0;
    }
  }
}

// Priority indicators
.priority-high {
  border-left: 4px solid #ef4444;
}

.priority-medium {
  border-left: 4px solid #f59e0b;
}

.priority-low {
  border-left: 4px solid #10b981;
}

// Progress bars
.progress-bar {
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8px;
  
  .progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
  }
}

// Quick action buttons
.quick-action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

.quick-action-btn {
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  
  &:hover {
    transform: translateY(-2px);
  }
  
  .action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    margin: 0 auto 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
  }
  
  .action-title {
    font-weight: 600;
    margin-bottom: 4px;
  }
  
  .action-description {
    font-size: 0.875rem;
    opacity: 0.8;
  }
}

// Dark theme specific overrides
:host-context(.dark-theme) {
  .dashboard-card {
    background-color: #1e1e1e;
    border: 1px solid #333333;
    color: #e0e0e0;
    
    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }
  }
  
  .stats-card {
    .stats-number {
      color: #ffffff;
    }
    
    .stats-icon {
      background-color: rgba(99, 102, 241, 0.2);
      color: #6366f1;
    }
  }
  
  .activity-item {
    background-color: #242424;
    border: 1px solid #333333;
    
    &:hover {
      background-color: #2a2a2a;
    }
    
    .activity-icon {
      background-color: rgba(99, 102, 241, 0.2);
      color: #6366f1;
    }
  }
  
  .task-item {
    background-color: #242424;
    border: 1px solid #333333;
    
    &:hover {
      background-color: #2a2a2a;
    }
  }
  
  .progress-bar {
    background-color: #333333;
    
    .progress-fill {
      background-color: #6366f1;
    }
  }
  
  .quick-action-btn {
    background-color: #1e1e1e;
    border: 1px solid #333333;
    color: #e0e0e0;
    
    &:hover {
      background-color: #242424;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }
    
    .action-icon {
      background-color: rgba(99, 102, 241, 0.2);
      color: #6366f1;
    }
  }
}

// Light theme specific overrides
:host-context(.light-theme) {
  .dashboard-card {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    color: #1f2937;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }
  
  .stats-card {
    .stats-number {
      color: #1f2937;
    }
    
    .stats-icon {
      background-color: #e0e7ff;
      color: #6366f1;
    }
  }
  
  .activity-item {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    
    &:hover {
      background-color: #f3f4f6;
    }
    
    .activity-icon {
      background-color: #e0e7ff;
      color: #6366f1;
    }
  }
  
  .task-item {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    
    &:hover {
      background-color: #f3f4f6;
    }
  }
  
  .progress-bar {
    background-color: #e5e7eb;
    
    .progress-fill {
      background-color: #6366f1;
    }
  }
  
  .quick-action-btn {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    color: #1f2937;
    
    &:hover {
      background-color: #f9fafb;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .action-icon {
      background-color: #e0e7ff;
      color: #6366f1;
    }
  }
}
