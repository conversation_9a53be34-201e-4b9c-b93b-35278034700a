using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ClubAchievementTranslationConfiguration : IEntityTypeConfiguration<ClubAchievementTranslation>
{
    public void Configure(EntityTypeBuilder<ClubAchievementTranslation> builder)
    {
        builder.HasKey(t => t.Id);
        
        builder.Property(t => t.LanguageCode)
            .IsRequired()
            .HasMaxLength(5);
            
        builder.Property(t => t.Description)
            .IsRequired();
            
        // Create a unique constraint for ClubAchievementId and LanguageCode
        builder.HasIndex(t => new { t.ClubAchievementId, t.LanguageCode })
            .IsUnique();
    }
}
