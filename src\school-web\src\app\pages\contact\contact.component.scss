// Hero Section
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('/assets/images/contact-hero.jpg');
  background-size: cover;
  background-position: center;
  height: 350px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  margin-bottom: 2rem;

  .hero-content {
    max-width: 800px;
    padding: 0 20px;

    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    .subtitle {
      font-size: 1.5rem;
      font-weight: 300;
    }
  }
}

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Section Styles
section {
  margin-bottom: 4rem;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: #333;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100px;
      height: 4px;
      background-color: #3f51b5;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #555;
  }
}

// Contact Form Section
.contact-form-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  margin-bottom: 4rem;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }

  .form-container {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

    h2 {
      font-size: 2rem;
      margin-top: 0;
      margin-bottom: 1rem;
    }

    .form-row {
      margin-bottom: 20px;

      &.two-columns {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }
      }

      mat-form-field {
        width: 100%;
      }
    }

    .form-actions {
      margin-top: 30px;
      text-align: right;

      button {
        padding: 8px 24px;
      }
    }
  }

  .contact-info-sidebar {
    .info-card {
      background-color: #e8eaf6;
      border-radius: 8px;
      padding: 25px;
      margin-bottom: 30px;

      h3 {
        font-size: 1.5rem;
        margin-top: 0;
        margin-bottom: 20px;
        color: #333;
      }

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        mat-icon {
          color: #3f51b5;
          margin-right: 15px;
        }

        span {
          color: #555;
          line-height: 1.5;
        }
      }
    }

    .social-media-links {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 25px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

      h3 {
        font-size: 1.5rem;
        margin-top: 0;
        margin-bottom: 20px;
        color: #333;
      }

      .social-icons {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;

        .social-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          background-color: #3f51b5;
          color: white;
          border-radius: 50%;
          text-decoration: none;
          transition: transform 0.3s, background-color 0.3s;

          &:hover {
            transform: translateY(-3px);
            background-color: #303f9f;
          }

          i {
            font-size: 20px;
          }
        }
      }
    }
  }
}

// Locations Section
.locations-section {
  .locations-container {
    margin-top: 30px;

    ::ng-deep .mat-mdc-tab-header {
      margin-bottom: 30px;
    }

    .location-details {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 40px;

      @media (max-width: 992px) {
        grid-template-columns: 1fr;
      }

      .location-info {
        h3 {
          font-size: 1.8rem;
          margin-top: 0;
          margin-bottom: 20px;
          color: #333;
        }

        .info-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 15px;

          mat-icon {
            color: #3f51b5;
            margin-right: 15px;
            margin-top: 3px;
          }

          span {
            color: #555;
            line-height: 1.5;
          }
        }

        .directions-button {
          margin-top: 25px;

          a {
            display: inline-flex;
            align-items: center;

            mat-icon {
              margin-right: 8px;
            }
          }
        }
      }

      .location-map {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

        @media (max-width: 992px) {
          margin-top: 20px;
        }
      }
    }
  }
}

// Departments Section
.departments-section {
  .departments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;

    .department-card {
      border-radius: 8px;
      padding: 25px;
      transition: transform 0.3s, box-shadow 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }

      .dept-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background-color: #e8eaf6;
        border-radius: 50%;
        margin-bottom: 20px;

        mat-icon {
          font-size: 30px;
          height: 30px;
          width: 30px;
          color: #3f51b5;
        }
      }

      h3 {
        font-size: 1.5rem;
        margin-top: 0;
        margin-bottom: 10px;
        color: #333;
      }

      p {
        color: #555;
        line-height: 1.6;
        margin-bottom: 20px;
      }

      .dept-contact {
        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }

          mat-icon {
            color: #3f51b5;
            margin-right: 10px;
            font-size: 18px;
            height: 18px;
            width: 18px;
          }

          span {
            color: #555;
            font-size: 0.9rem;
          }
        }
      }
    }
  }
}

// FAQ Section
.faq-section {
  .faq-container {
    margin-top: 30px;

    mat-accordion {
      mat-expansion-panel {
        margin-bottom: 15px;
        border-radius: 8px;
        overflow: hidden;

        ::ng-deep .mat-expansion-panel-header {
          padding: 20px;
        }

        ::ng-deep .mat-expansion-panel-header-title {
          color: #333;
          font-weight: 500;
        }

        p {
          color: #555;
          line-height: 1.6;
        }
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .hero-section {
    height: 300px;

    .hero-content h1 {
      font-size: 2.5rem;
    }
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 250px;

    .hero-content {
      h1 {
        font-size: 2rem;
      }

      .subtitle {
        font-size: 1.2rem;
      }
    }
  }

  section h2 {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .hero-section {
    height: 200px;

    .hero-content h1 {
      font-size: 1.8rem;
    }
  }

  .departments-grid {
    grid-template-columns: 1fr;
  }
}
