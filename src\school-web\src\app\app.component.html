<!-- Public Layout -->
<div class="main-container" [class]="currentTheme + '-theme'" *ngIf="!isAdminRoute && !isAuthRoute && !isTenantSetupRoute">
  <!-- Notice Bar -->
  <app-notice-bar></app-notice-bar>

  <!-- Header Section (contains banner and nav) -->
  <header class="header-section">
    <!-- School Banner (Desktop Only) -->
    <app-school-banner *ngIf="!isMobile"></app-school-banner>

    <!-- Main Navigation -->
    <app-main-nav #mainNav></app-main-nav>
  </header>

  <!-- Main Content Area -->
  <main class="main-content">
    <router-outlet></router-outlet>
  </main>

  <!-- Footer -->
  <app-footer></app-footer>
</div>

<!-- Admin Layout -->
<div class="admin-container" [class]="currentTheme + '-theme'" *ngIf="isAdminRoute">
  <!-- Admin Content Area -->
  <router-outlet></router-outlet>
</div>

<!-- Auth Layout (Login/Register) -->
<div class="auth-container" [class]="currentTheme + '-theme'" *ngIf="isAuthRoute">
  <!-- Auth Content Area -->
  <router-outlet></router-outlet>
</div>

<!-- Tenant Setup Layout (Standalone) -->
<div class="tenant-setup-container" *ngIf="isTenantSetupRoute">
  <!-- Tenant Setup Content Area (No additional styling - components handle their own layout) -->
  <router-outlet></router-outlet>
</div>
