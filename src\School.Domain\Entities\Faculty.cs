using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class Faculty : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this faculty member belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Office { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string ShortBio { get; set; } = string.Empty;
    public int JoinedYear { get; set; }
    public bool IsFeatured { get; set; }
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }

    // Social links
    public string Website { get; set; } = string.Empty;
    public string LinkedIn { get; set; } = string.Empty;
    public string Twitter { get; set; } = string.Empty;
    public string ResearchGate { get; set; } = string.Empty;

    // Department relationship
    public string Department { get; set; } = string.Empty; // Will be replaced with proper relationship when Department entity is created

    // Navigation properties
    public Guid? ProfileImageId { get; set; }
    public MediaItem? ProfileImage { get; set; }
    public ICollection<FacultyTranslation> Translations { get; set; } = new List<FacultyTranslation>();

    // Relational collections
    public ICollection<FacultyEducation> Education { get; set; } = new List<FacultyEducation>();
    public ICollection<FacultySpecialization> Specializations { get; set; } = new List<FacultySpecialization>();
    public ICollection<FacultyCourse> Courses { get; set; } = new List<FacultyCourse>();
    public ICollection<FacultyPublication> Publications { get; set; } = new List<FacultyPublication>();
    public ICollection<FacultyAward> Awards { get; set; } = new List<FacultyAward>();
    public string Description { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
}
