<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <title><PERSON><PERSON> Memorial Academy</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Modern school website with comprehensive educational resources and information">
  <link rel="icon" type="image/x-icon" href="favicon.ico">

  <!-- Preconnect to Google Fonts to improve loading performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Material Icons - Direct CDN links with display=swap for better performance -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Sharp&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Two+Tone&display=swap" rel="stylesheet">

  <!-- Inline Material Icons CSS with proper font-family declarations -->
  <style>
    /* Base Material Icons styles */
    .material-icons,
    .material-icons-outlined,
    .material-icons-round,
    .material-icons-sharp,
    .material-icons-two-tone {
      font-weight: normal;
      font-style: normal;
      font-size: 24px;
      line-height: 1;
      letter-spacing: normal;
      text-transform: none;
      display: inline-block;
      white-space: nowrap;
      word-wrap: normal;
      direction: ltr;
      -webkit-font-feature-settings: 'liga';
      font-feature-settings: 'liga';
      -webkit-font-smoothing: antialiased;
      text-rendering: optimizeLegibility;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Specific font-family declarations for each variant */
    .material-icons {
      font-family: 'Material Icons', sans-serif;
    }

    .material-icons-outlined {
      font-family: 'Material Icons Outlined', sans-serif;
    }

    .material-icons-round {
      font-family: 'Material Icons Round', sans-serif;
    }

    .material-icons-sharp {
      font-family: 'Material Icons Sharp', sans-serif;
    }

    .material-icons-two-tone {
      font-family: 'Material Icons Two Tone', sans-serif;
    }
  </style>

  <!-- Fonts for both English and Bengali -->
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="mat-typography suma-theme">
  <app-root></app-root>
</body>
</html>
