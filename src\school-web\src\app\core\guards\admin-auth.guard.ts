import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

export const adminAuthGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  // Check if user has admin privileges (SystemAdmin or TenantAdmin)
  if (authService.isAdmin() || authService.isSystemAdmin() || authService.isTenantAdmin()) {
    return true;
  }

  // Redirect to login page if not authenticated
  if (!authService.isLoggedIn()) {
    router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
    return false;
  }

  // Redirect to home page if authenticated but not admin
  router.navigate(['/home']);
  return false;
};
