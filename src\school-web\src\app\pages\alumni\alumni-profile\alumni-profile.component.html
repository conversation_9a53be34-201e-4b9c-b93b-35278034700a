<!-- Loading State -->
<div class="loading-container" *ngIf="loading">
  <mat-spinner diameter="40"></mat-spinner>
  <p>{{ 'ALUMNI.LOADING_PROFILE' | translate }}</p>
</div>

<!-- Error State -->
<div class="error-container" *ngIf="error">
  <mat-icon class="error-icon">error_outline</mat-icon>
  <h2>{{ 'ALUMNI.ALUMNI_NOT_FOUND' | translate }}</h2>
  <p>{{ 'ALUMNI.ALUMNI_NOT_FOUND_MESSAGE' | translate }}</p>
  <button mat-raised-button color="primary" (click)="goBack()">{{ 'ALUMNI.BACK_TO_DIRECTORY' | translate }}</button>
</div>

<!-- Alumni Profile Content -->
<div class="alumni-profile-container" *ngIf="!loading && !error && alumni">
  <!-- Profile Header -->
  <section class="profile-header">
    <div class="container">
      <button mat-button class="back-button" (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> {{ 'ALUMNI.BACK_TO_DIRECTORY' | translate }}
      </button>

      <div class="profile-header-content">
        <div class="profile-image-container">
          <img [src]="alumni.profileImage?.filePath" [alt]="alumni.name" class="profile-image">
        </div>

        <div class="profile-header-info">
          <h1>{{ alumni.name }}</h1>
          <h2>{{ alumni.profession }}</h2>
          <p class="graduation-year">{{ 'ALUMNI.CLASS_OF' | translate }} {{ alumni.graduationYear }}</p>

          <div class="profile-contact" *ngIf="alumni.organization || alumni.email">
            <div class="contact-item" *ngIf="alumni.organization">
              <mat-icon>business</mat-icon>
              <span>{{ alumni.organization }}</span>
            </div>
            <div class="contact-item" *ngIf="alumni.email">
              <mat-icon>email</mat-icon>
              <span>{{ alumni.email }}</span>
            </div>
          </div>

          <div class="profile-social">
            <a *ngIf="alumni.linkedInProfile" [href]="alumni.linkedInProfile" target="_blank" class="social-link" aria-label="LinkedIn">
              <mat-icon>business</mat-icon>
            </a>
            <a *ngIf="alumni.facebookProfile" [href]="alumni.facebookProfile" target="_blank" class="social-link" aria-label="Facebook">
              <mat-icon>public</mat-icon>
            </a>
            <a *ngIf="alumni.twitterProfile" [href]="alumni.twitterProfile" target="_blank" class="social-link" aria-label="Twitter">
              <mat-icon>chat</mat-icon>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Profile Content -->
  <section class="profile-content">
    <div class="container">
      <mat-tab-group animationDuration="300ms">
        <!-- Biography Tab -->
        <mat-tab label="{{ 'ALUMNI.BIOGRAPHY' | translate }}">
          <div class="tab-content">
            <div class="bio-section">
              <h3>{{ 'ALUMNI.ABOUT' | translate }}</h3>
              <p>{{ alumni.biography }}</p>

              <div class="years-since-graduation">
                <strong>{{ 'ALUMNI.YEARS_SINCE_GRADUATION' | translate }}:</strong> {{ getYearsSinceGraduation() }}
              </div>
            </div>

            <div class="designation-section" *ngIf="alumni.designation">
              <h3>{{ 'ALUMNI.DESIGNATION' | translate }}</h3>
              <p>{{ alumni.designation }}</p>
            </div>
          </div>
        </mat-tab>

        <!-- Achievements Tab -->
        <mat-tab label="{{ 'ALUMNI.ACHIEVEMENTS' | translate }}">
          <div class="tab-content">
            <h3>{{ 'ALUMNI.KEY_ACHIEVEMENTS' | translate }}</h3>
            <p class="achievements-text">{{ alumni.achievements }}</p>
          </div>
        </mat-tab>

        <!-- Testimonials Tab -->
        <mat-tab label="{{ 'ALUMNI.TESTIMONIALS' | translate }}" *ngIf="alumni.testimonials && alumni.testimonials.length > 0">
          <div class="tab-content">
            <h3>{{ 'ALUMNI.TESTIMONIALS' | translate }}</h3>
            <div class="testimonials-list">
              <div class="testimonial-item" *ngFor="let testimonial of alumni.testimonials">
                <blockquote>
                  <p>"{{ testimonial.content }}"</p>
                </blockquote>
              </div>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </section>

  <!-- Connect Section -->
  <section class="connect-section">
    <div class="container">
      <h2>{{ 'ALUMNI.CONNECT_WITH_ALUMNI' | translate }}</h2>
      <p>{{ 'ALUMNI.CONNECT_MESSAGE' | translate }}</p>
      <div class="connect-buttons">
        <a mat-raised-button color="primary" href="mailto:<EMAIL>">
          <mat-icon>email</mat-icon>
          {{ 'ALUMNI.REQUEST_CONNECTION' | translate }}
        </a>
        <button mat-stroked-button color="primary" (click)="goBack()">
          <mat-icon>people</mat-icon>
          {{ 'ALUMNI.EXPLORE_MORE_ALUMNI' | translate }}
        </button>
      </div>
    </div>
  </section>
</div>
