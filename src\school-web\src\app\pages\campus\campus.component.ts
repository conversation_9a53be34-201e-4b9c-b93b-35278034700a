import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../shared/components/default-hero/default-hero.component';

interface CampusFeature {
  title: string;
  description: string;
  image: string;
  link: string;
  icon: string;
}

@Component({
  selector: 'app-campus',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './campus.component.html',
  styleUrls: ['./campus.component.scss']
})
export class CampusComponent {
  // Campus life features
  campusFeatures: CampusFeature[] = [
    {
      title: 'CAMPUS_LIFE.CLUBS',
      description: 'CAMPUS_LIFE.CLUBS_DESCRIPTION',
      image: 'assets/images/campus/clubs-feature.jpg',
      link: '/campus/clubs',
      icon: 'groups'
    },
    {
      title: 'CAMPUS_LIFE.SPORTS',
      description: 'CAMPUS_LIFE.SPORTS_DESCRIPTION',
      image: 'assets/images/campus/sports-feature.jpg',
      link: '/campus/sports',
      icon: 'sports_soccer'
    },
    {
      title: 'CAMPUS_LIFE.ARTS',
      description: 'CAMPUS_LIFE.ARTS_DESCRIPTION',
      image: 'assets/images/campus/arts-feature.jpg',
      link: '/campus/arts',
      icon: 'palette'
    },
    {
      title: 'CAMPUS_LIFE.DINING',
      description: 'CAMPUS_LIFE.DINING_DESCRIPTION',
      image: 'assets/images/campus/dining-feature.jpg',
      link: '/campus/dining',
      icon: 'restaurant'
    },
    {
      title: 'CAMPUS_LIFE.HEALTH',
      description: 'CAMPUS_LIFE.HEALTH_DESCRIPTION',
      image: 'assets/images/campus/health-feature.jpg',
      link: '/campus/health',
      icon: 'favorite'
    },
    {
      title: 'CAMPUS_LIFE.HOUSING',
      description: 'CAMPUS_LIFE.HOUSING_DESCRIPTION',
      image: 'assets/images/campus/housing-feature.jpg',
      link: '/campus/housing',
      icon: 'home'
    },
    {
      title: 'CAMPUS_LIFE.FACILITIES',
      description: 'CAMPUS_LIFE.FACILITIES_DESCRIPTION',
      image: 'assets/images/campus/facilities-feature.jpg',
      link: '#',
      icon: 'location_city'
    }
  ];

  // Campus highlights
  campusHighlights = [
    {
      title: 'CAMPUS_LIFE.HIGHLIGHT1_TITLE',
      description: 'CAMPUS_LIFE.HIGHLIGHT1_DESCRIPTION',
      image: 'assets/images/campus/highlight1.jpg'
    },
    {
      title: 'CAMPUS_LIFE.HIGHLIGHT2_TITLE',
      description: 'CAMPUS_LIFE.HIGHLIGHT2_DESCRIPTION',
      image: 'assets/images/campus/highlight2.jpg'
    },
    {
      title: 'CAMPUS_LIFE.HIGHLIGHT3_TITLE',
      description: 'CAMPUS_LIFE.HIGHLIGHT3_DESCRIPTION',
      image: 'assets/images/campus/highlight3.jpg'
    }
  ];

  // Upcoming events
  upcomingEvents = [
    {
      title: 'Fall Festival',
      date: 'October 15, 2023',
      time: '12:00 PM - 4:00 PM',
      location: 'School Grounds',
      description: 'Join us for our annual Fall Festival featuring games, food, music, and activities for the whole family.'
    },
    {
      title: 'Homecoming Game',
      date: 'October 20, 2023',
      time: '7:00 PM',
      location: 'Athletic Field',
      description: 'Cheer on our varsity soccer team as they take on their rivals in this exciting homecoming match.'
    },
    {
      title: 'Fall Concert',
      date: 'November 5, 2023',
      time: '6:30 PM',
      location: 'Auditorium',
      description: 'Our music department presents a showcase of talent featuring performances by the band, orchestra, and choir.'
    }
  ];

  // Student testimonials
  studentTestimonials = [
    {
      quote: 'CAMPUS_LIFE.TESTIMONIAL1_QUOTE',
      student: 'CAMPUS_LIFE.TESTIMONIAL1_STUDENT',
      image: 'assets/images/campus/student1.jpg'
    },
    {
      quote: 'CAMPUS_LIFE.TESTIMONIAL2_QUOTE',
      student: 'CAMPUS_LIFE.TESTIMONIAL2_STUDENT',
      image: 'assets/images/campus/student2.jpg'
    },
    {
      quote: 'CAMPUS_LIFE.TESTIMONIAL3_QUOTE',
      student: 'CAMPUS_LIFE.TESTIMONIAL3_STUDENT',
      image: 'assets/images/campus/student3.jpg'
    }
  ];
}
