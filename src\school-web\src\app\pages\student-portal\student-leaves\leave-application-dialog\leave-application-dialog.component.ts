import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

// Services and models
import { StudentService } from '../../../../core/services/student.service';
import { LeaveType } from '../../../../core/models/student.model';

@Component({
  selector: 'app-leave-application-dialog',
  templateUrl: './leave-application-dialog.component.html',
  styleUrls: ['./leave-application-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule
  ]
})
export class LeaveApplicationDialogComponent implements OnInit {
  leaveForm: FormGroup;
  loading = false;

  leaveTypes = [
    { value: LeaveType.Sick, label: 'Sick' },
    { value: LeaveType.Personal, label: 'Personal' },
    { value: LeaveType.Family, label: 'Family' },
    { value: LeaveType.Religious, label: 'Religious' },
    { value: LeaveType.Other, label: 'Other' }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private studentService: StudentService,
    public dialogRef: MatDialogRef<LeaveApplicationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { studentId: number }
  ) {
    this.leaveForm = this.formBuilder.group({
      type: [LeaveType.Sick, Validators.required],
      startDate: [null, Validators.required],
      endDate: [null, Validators.required],
      reason: ['', [Validators.required, Validators.minLength(10)]],
      attachmentPath: ['']
    });
  }

  ngOnInit(): void {
  }

  onSubmit(): void {
    if (this.leaveForm.invalid) {
      return;
    }

    this.loading = true;

    const formValue = this.leaveForm.value;

    this.studentService.createLeave({
      studentId: this.data.studentId,
      type: formValue.type,
      startDate: formValue.startDate,
      endDate: formValue.endDate,
      reason: formValue.reason,
      attachmentPath: formValue.attachmentPath
    }).subscribe({
      next: (response) => {
        this.loading = false;
        this.dialogRef.close(response);
      },
      error: (err) => {
        console.error('Error submitting leave application:', err);
        this.loading = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onFileSelected(event: Event): void {
    const fileInput = event.target as HTMLInputElement;
    if (fileInput.files && fileInput.files.length > 0) {
      const file = fileInput.files[0];
      // In a real application, you would upload the file to a server
      // For now, we'll just set the file name
      this.leaveForm.patchValue({
        attachmentPath: file.name
      });
    }
  }
}
