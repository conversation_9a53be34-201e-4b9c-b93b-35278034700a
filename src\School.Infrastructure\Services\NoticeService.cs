using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Notice;
using School.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Services;

public class NoticeService : INoticeService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<NoticeService> _logger;

    public NoticeService(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        ILogger<NoticeService> logger)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<(List<NoticeDto> Items, int TotalCount)> GetAllNoticesAsync(NoticeFilterDto filter)
    {
        _logger.LogInformation("Getting all notices with filter: {Filter}", new { filter.Category, filter.IsActive, filter.Search, filter.Priority, filter.StartDateFrom, filter.StartDateTo, filter.Page, filter.PageSize });

        try
        {
            var repository = _unitOfWork.Repository<Notice>();
            var query = repository.AsQueryable("CreatedBy", "Translations");

            // Apply filters
            if (!string.IsNullOrEmpty(filter.Category))
            {
                query = query.Where(n => n.Category == filter.Category);
            }

            if (filter.IsActive.HasValue)
            {
                query = query.Where(n => n.IsActive == filter.IsActive.Value);
            }

            if (!string.IsNullOrEmpty(filter.Search))
            {
                query = query.Where(n =>
                    n.Title.Contains(filter.Search) ||
                    n.Content.Contains(filter.Search) ||
                    n.Translations.Any(t => t.Title.Contains(filter.Search) || t.Content.Contains(filter.Search)));
            }

            if (filter.Priority.HasValue)
            {
                query = query.Where(n => n.Priority == filter.Priority.Value);
            }

            if (filter.StartDateFrom.HasValue)
            {
                query = query.Where(n => n.StartDate >= filter.StartDateFrom.Value);
            }

            if (filter.StartDateTo.HasValue)
            {
                query = query.Where(n => n.StartDate <= filter.StartDateTo.Value);
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply sorting
            IQueryable<Notice> sortedQuery;
            if (!string.IsNullOrEmpty(filter.SortBy))
            {
                switch (filter.SortBy.ToLower())
                {
                    case "title":
                        sortedQuery = filter.SortDirection?.ToLower() == "desc" ?
                            query.OrderByDescending(n => n.Title) :
                            query.OrderBy(n => n.Title);
                        break;
                    case "priority":
                        sortedQuery = filter.SortDirection?.ToLower() == "desc" ?
                            query.OrderByDescending(n => n.Priority) :
                            query.OrderBy(n => n.Priority);
                        break;
                    case "category":
                        sortedQuery = filter.SortDirection?.ToLower() == "desc" ?
                            query.OrderByDescending(n => n.Category) :
                            query.OrderBy(n => n.Category);
                        break;
                    case "createdat":
                        sortedQuery = filter.SortDirection?.ToLower() == "desc" ?
                            query.OrderByDescending(n => n.CreatedAt) :
                            query.OrderBy(n => n.CreatedAt);
                        break;
                    default:
                        sortedQuery = query.OrderByDescending(n => n.StartDate);
                        break;
                }
            }
            else
            {
                sortedQuery = query.OrderByDescending(n => n.StartDate);
            }

            // Get paginated results
            var items = await sortedQuery
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(n => new NoticeDto
            {
                Id = n.Id,
                Title = n.Title,
                Content = n.Content,
                StartDate = n.StartDate,
                EndDate = n.EndDate,
                Priority = n.Priority,
                Category = n.Category,
                IsActive = n.IsActive,
                CreatedById = n.CreatedById,
                CreatedByName = $"{n.CreatedBy.FirstName} {n.CreatedBy.LastName}",
                CreatedAt = n.CreatedAt,
                LastModifiedAt = n.LastModifiedAt,
                Translations = n.Translations.Select(t => new NoticeTranslationDto
                {
                    Id = t.Id,
                    NoticeId = t.NoticeId,
                    LanguageCode = t.LanguageCode,
                    Title = t.Title,
                    Content = t.Content
                }).ToList()
            })
            .ToListAsync();

            _logger.LogInformation("Retrieved {Count} notices out of {TotalCount}", items.Count, totalCount);
            return (items, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError("Error retrieving notices: {ErrorMessage}", ex.Message);
            throw;
        }
    }

    public async Task<NoticeDto?> GetNoticeByIdAsync(Guid id)
    {
        _logger.LogInformation("Getting notice by ID: {NoticeId}", id);

        try
        {
            var repository = _unitOfWork.Repository<Notice>();
            var notice = await repository.GetByIdAsync(id, new[] { "CreatedBy", "Translations" });

            if (notice == null)
            {
                _logger.LogWarning("Notice with ID {NoticeId} not found", id);
                return null;
            }

            return new NoticeDto
            {
                Id = notice.Id,
                Title = notice.Title,
                Content = notice.Content,
                StartDate = notice.StartDate,
                EndDate = notice.EndDate,
                Priority = notice.Priority,
                Category = notice.Category,
                IsActive = notice.IsActive,
                CreatedById = notice.CreatedById,
                CreatedByName = $"{notice.CreatedBy?.FirstName} {notice.CreatedBy?.LastName}",
                CreatedAt = notice.CreatedAt,
                LastModifiedAt = notice.LastModifiedAt,
                Translations = notice.Translations.Select(t => new NoticeTranslationDto
                {
                    Id = t.Id,
                    NoticeId = t.NoticeId,
                    LanguageCode = t.LanguageCode,
                    Title = t.Title,
                    Content = t.Content
                }).ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notice by ID {NoticeId}: {ErrorMessage}", id, ex.Message);
            throw;
        }
    }

    public async Task<Guid> CreateNoticeAsync(NoticeCreateDto noticeDto, Guid createdById)
    {
        _logger.LogInformation("Creating new notice with title: {Title}", noticeDto.Title);

        try
        {
            var repository = _unitOfWork.Repository<Notice>();
            var userId = _currentUserService.UserId?.ToString();

            var notice = new Notice
            {
                Title = noticeDto.Title,
                Content = noticeDto.Content,
                StartDate = noticeDto.StartDate,
                EndDate = noticeDto.EndDate,
                Priority = noticeDto.Priority,
                Category = noticeDto.Category,
                IsActive = noticeDto.IsActive,
                CreatedById = createdById
                // CreatedAt and CreatedBy will be set by the repository
            };

            if (noticeDto.Translations != null && noticeDto.Translations.Any())
            {
                notice.Translations = new List<NoticeTranslation>();
                foreach (var translationDto in noticeDto.Translations)
                {
                    notice.Translations.Add(new NoticeTranslation
                    {
                        LanguageCode = translationDto.LanguageCode,
                        Title = translationDto.Title,
                        Content = translationDto.Content
                        // CreatedAt and CreatedBy will be set by the repository
                    });
                }
            }

            // Pass the current user ID to the repository for audit trail
            await repository.AddAsync(notice, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Notice created successfully with ID: {NoticeId}", notice.Id);
            return notice.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating notice: {ErrorMessage}", ex.Message);
            throw;
        }
    }

    public async Task<bool> UpdateNoticeAsync(Guid id, NoticeUpdateDto noticeDto)
    {
        _logger.LogInformation("Updating notice with ID: {NoticeId}", id);

        try
        {
            var repository = _unitOfWork.Repository<Notice>();
            var notice = await repository.GetByIdAsync(id);

            if (notice == null)
            {
                _logger.LogWarning("Notice with ID {NoticeId} not found for update", id);
                return false;
            }

            if (noticeDto.Title != null)
                notice.Title = noticeDto.Title;

            if (noticeDto.Content != null)
                notice.Content = noticeDto.Content;

            if (noticeDto.StartDate.HasValue)
                notice.StartDate = noticeDto.StartDate.Value;

            if (noticeDto.EndDate.HasValue)
                notice.EndDate = noticeDto.EndDate;

            if (noticeDto.Priority.HasValue)
                notice.Priority = noticeDto.Priority.Value;

            if (noticeDto.Category != null)
                notice.Category = noticeDto.Category;

            if (noticeDto.IsActive.HasValue)
                notice.IsActive = noticeDto.IsActive.Value;

            // Pass the current user ID to the repository for audit trail
            var userId = _currentUserService.UserId?.ToString();
            await repository.UpdateAsync(notice, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Notice with ID {NoticeId} updated successfully", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating notice with ID {NoticeId}: {ErrorMessage}", id, ex.Message);
            throw;
        }
    }

    public async Task<bool> DeleteNoticeAsync(Guid id)
    {
        _logger.LogInformation("Deleting notice with ID: {NoticeId}", id);

        try
        {
            var repository = _unitOfWork.Repository<Notice>();

            // Check if notice exists
            var notice = await repository.GetByIdAsync(id);
            if (notice == null)
            {
                _logger.LogWarning("Notice with ID {NoticeId} not found for deletion", id);
                return false;
            }

            // Use the repository's DeleteByIdAsync method which handles soft delete
            var userId = _currentUserService.UserId?.ToString();
            await repository.DeleteByIdAsync(id, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Notice with ID {NoticeId} deleted successfully", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting notice with ID {NoticeId}: {ErrorMessage}", id, ex.Message);
            throw;
        }
    }
}