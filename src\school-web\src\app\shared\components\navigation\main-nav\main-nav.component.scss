@use 'sass:color';
@use '../../../../../styles/_variables' as *;
@use '../../../styles/_mega-menu' as mega;

// Sidenav container styles
.sidenav-container {
  width: 100%;
  position: relative; // Changed from absolute to relative
  transition: all 0.3s ease;
}
.mat-drawer-content {
  overflow: hidden;
}
// Mobile sidenav styles
.sidenav {
  width: 280px;
  box-shadow: 3px 0 6px rgba(0, 0, 0, 0.1);

  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;

    .drawer-title {
      font-size: 1.2rem;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .drawer-close-button {
      margin-left: 8px;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        transform: rotate(90deg);
      }
    }
  }

  mat-nav-list {
    padding: 0;

    a.active {
      background-color: rgba(0, 0, 0, 0.04);
      color: $primary-color;
      font-weight: 500;
    }
  }

  mat-expansion-panel {
    box-shadow: none;
    border-radius: 0;
    background: transparent;

    &:not(:last-child) {
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    }

    mat-expansion-panel-header {
      padding: 0 16px;
      height: 48px;

      mat-panel-title {
        font-weight: 500;
      }
    }
  }

  .mobile-language-selector {
    padding: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    margin-top: 16px;
  }
}

// Toolbar styles
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px; // Reduced padding to give more space for menu items
  height: 60px; // Reduced from 70px to 60px to match menu link height
  position: relative; // Initially relative, not fixed
  z-index: 999; // Lower than school banner (1001) but higher than content
  background-color: white;
  color: #333;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin-top: 0; // No gap between school banner and navigation
  border-top: 3px solid $primary-color; // Add top border to connect with banner

  &.fixed-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1002; // Higher z-index than mega menu panels
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    animation: slideDown 0.3s ease-in-out;
    margin-top: 0; // Remove margin when fixed

    // Ensure smooth transition for logo appearance
    .school-brand .home-link img.school-logo {
      animation: fadeIn 0.3s ease-in-out;
    }
  }

  &.mobile-toolbar {
    background-color: #3f51b5; // Primary color for mobile
    color: white;
    height: 64px;
    padding: 0 16px;
    position: fixed; // Always fixed on mobile
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1002; // Match fixed-nav z-index
    margin-top: 0; // Remove margin on mobile
  }

  // Mobile menu toggle button
  .menu-toggle {
    margin-right: 12px;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Add padding to body when navbar is fixed
body.has-fixed-nav .main-content {
  padding-top: 60px; // Same as navbar height (no need to include margin as it's removed when fixed)
}

// School brand styles
.school-brand {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
  margin-right: 8px; // Add some margin to separate from mega menu

  .home-link {
    display: flex;
    align-items: center;
    color: inherit;
    text-decoration: none;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.02);
    }

    .school-logo {
      height: 40px; // Slightly smaller for better fit
      margin-right: 12px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    .school-name {
      font-size: 1.2rem;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 200px;
    }

    mat-icon {
      font-size: 24px;
      height: 24px;
      width: 24px;
      color: $primary-color; // Use primary color for home icon
      margin-right: 8px; // Add some margin
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }

  .mobile-toolbar & .home-link {
    color: white;
  }
}

// Desktop mega menu styles
.mega-menu {
  @include mega.mega-menu-container;

  .menu-item {
    @include mega.menu-item;

    .menu-link {
      @include mega.menu-trigger;
    }
  }
}

// Nav actions container
.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px; // Reduced gap
  flex-shrink: 0; // Prevent shrinking
  margin-left: 8px; // Add some margin from the mega menu

  .nav-action-item {
    display: flex;
    align-items: center;
  }
}

// Login button styles
.login-button {
  .login-link {
    display: flex;
    align-items: center;
    border-radius: 24px;
    padding: 0 16px; // Reduced padding
    height: 40px;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    mat-icon {
      margin-right: 8px;
      font-size: 18px;
      height: 18px;
      width: 18px;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

// Mobile actions container
.mobile-actions {
  display: flex;
  align-items: center;

  .mobile-login-button {
    margin-left: 8px;
  }
}

// Media queries for responsive design
@media (max-width: 991px) {
  .toolbar {
    padding: 0 16px;
  }

  .school-brand {
    justify-content: flex-start;
    flex: 1;
  }

  // Mobile menu toggle button
  .menu-toggle {
    margin-right: 12px;
  }

  // Mobile actions container
  .mobile-actions {
    display: flex;
    align-items: center;

    .mobile-login-button {
      margin-left: 8px;
    }
  }
}

@media (max-width: 599px) {
  .toolbar {
    height: 56px;
    padding: 0 12px;
  }

  .school-brand .home-link .school-logo {
    height: 32px;
  }

  .school-brand .home-link .school-name {
    font-size: 1rem;
    max-width: 150px;
  }

  :host-context(body.has-fixed-nav) {
    padding-top: 56px;
  }

  .menu-toggle {
    margin-right: 8px;
  }
}
