using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

/// <summary>
/// Represents a grade level in the school (K-12)
/// </summary>
public class Grade : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this grade belongs to
    /// </summary>
    public Guid TenantId { get; set; }

    /// <summary>
    /// Grade name (e.g., "Grade 6", "Class 10", "Kindergarten")
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Grade code (e.g., "G6", "C10", "KG")
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Numeric level for ordering (1-12, 0 for kindergarten)
    /// </summary>
    public int Level { get; set; }

    /// <summary>
    /// Education level category
    /// </summary>
    public EducationLevel EducationLevel { get; set; } = EducationLevel.Secondary;

    /// <summary>
    /// Description of the grade
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Minimum age requirement for this grade
    /// </summary>
    public int MinAge { get; set; }

    /// <summary>
    /// Maximum age allowed for this grade
    /// </summary>
    public int MaxAge { get; set; }

    /// <summary>
    /// Maximum number of students allowed across all sections
    /// </summary>
    public int MaxStudents { get; set; }

    /// <summary>
    /// Whether this grade is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Display order for sorting
    /// </summary>
    public int DisplayOrder { get; set; }

    /// <summary>
    /// Academic year this grade configuration is for
    /// </summary>
    public Guid AcademicYearId { get; set; }

    /// <summary>
    /// Promotion requirements (JSON or text)
    /// </summary>
    public string PromotionCriteria { get; set; } = string.Empty;

    /// <summary>
    /// Minimum passing grade percentage
    /// </summary>
    public decimal MinPassingGrade { get; set; } = 40.0m;

    /// <summary>
    /// Remarks or additional notes
    /// </summary>
    public string Remarks { get; set; } = string.Empty;

    // Navigation properties
    /// <summary>
    /// Academic year this grade belongs to
    /// </summary>
    public AcademicYear AcademicYear { get; set; } = null!;

    /// <summary>
    /// Sections within this grade
    /// </summary>
    public ICollection<Section> Sections { get; set; } = new List<Section>();

    /// <summary>
    /// Students in this grade
    /// </summary>
    public ICollection<Student> Students { get; set; } = new List<Student>();

    /// <summary>
    /// Translation support
    /// </summary>
    public ICollection<GradeTranslation> Translations { get; set; } = new List<GradeTranslation>();
}

/// <summary>
/// Translation entity for Grade
/// </summary>
public class GradeTranslation : BaseEntity
{
    public Guid GradeId { get; set; }
    public string LanguageCode { get; set; } = string.Empty; // "en", "bn"
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string PromotionCriteria { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;

    // Navigation property
    public Grade Grade { get; set; } = null!;
}
