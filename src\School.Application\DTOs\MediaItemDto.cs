using School.Application.Common.Mappings;
using School.Application.DTOs.Common;
using School.Domain.Entities;
using School.Domain.Enums;

namespace School.Application.DTOs;

public class MediaItemDto : IMapFrom<MediaItem>
{
    public Guid Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string OriginalFileName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string MimeType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public MediaType Type { get; set; }
    public string AltText { get; set; } = string.Empty;
    public string Caption { get; set; } = string.Empty;
    public Guid? ContentId { get; set; }
    public string UploadedById { get; set; }
    public string UploadedByName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class MediaItemCreateDto
{
    public string OriginalFileName { get; set; } = string.Empty;
    public string MimeType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public MediaType Type { get; set; }
    public string AltText { get; set; } = string.Empty;
    public string Caption { get; set; } = string.Empty;
    public Guid? ContentId { get; set; }
    public Stream? FileStream { get; set; }
}

public class MediaItemUpdateDto
{
    public MediaType? Type { get; set; }
    public string? AltText { get; set; }
    public string? Caption { get; set; }
    public Guid? ContentId { get; set; }
}

/// <summary>
/// Filter DTO for media queries
/// </summary>
public class MediaFilterDto : BaseFilterDto
{
    /// <summary>
    /// Search term for file name
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// Filter by media type
    /// </summary>
    public MediaType? Type { get; set; }

    /// <summary>
    /// Filter by content ID
    /// </summary>
    public Guid? ContentId { get; set; }
}
