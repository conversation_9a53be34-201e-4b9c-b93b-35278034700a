import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCardModule } from '@angular/material/card';

@Component({
  selector: 'app-loading',
  standalone: true,
  imports: [CommonModule, MatProgressSpinnerModule, MatCardModule],
  template: `
    <div class="loading-container" [ngStyle]="{'height': height}">
      <mat-spinner [diameter]="diameter"></mat-spinner>
      <p *ngIf="message" class="loading-message">{{ message }}</p>
    </div>
  `,
  styles: [`
    .loading-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 200px;
    }
    
    .loading-message {
      margin-top: 16px;
      color: rgba(0, 0, 0, 0.6);
      font-size: 16px;
    }
  `]
})
export class LoadingComponent {
  @Input() diameter: number = 40;
  @Input() message: string = '';
  @Input() height: string = '300px';
}
