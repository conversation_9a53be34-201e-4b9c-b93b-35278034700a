import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { ErrorHandlerService } from './error-handler.service';
import { ApiResponseHandlerService } from './api-response-handler.service';
import { ApiResponse } from '../models/api-response.model';
import { Parent, ParentDetail, ParentFilter, CreateParent, UpdateParent } from '../models/parent.model';
import {
  ParentRelationType, StudentAttendance, StudentFee, StudentResult,
  StudentLeave, CreateStudentLeave, ExamType, LeaveStatus
} from '../models/student.model';

@Injectable({
  providedIn: 'root'
})
export class ParentService extends BaseApiService {
  constructor(
    protected override http: HttpClient,
    protected override errorHandler: ErrorHandlerService,
    private apiResponseHandler: ApiResponseHandlerService
  ) {
    super(http, errorHandler);
  }

  // Parent CRUD operations
  getParents(filter: ParentFilter = {}): Observable<{ totalCount: number, items: Parent[] }> {
    let httpParams = new HttpParams();

    if (filter.name) httpParams = httpParams.set('name', filter.name);
    if (filter.email) httpParams = httpParams.set('email', filter.email);
    if (filter.phone) httpParams = httpParams.set('phone', filter.phone);
    if (filter.isActive !== undefined) httpParams = httpParams.set('isActive', filter.isActive.toString());
    if (filter.studentId !== undefined) httpParams = httpParams.set('studentId', filter.studentId.toString());
    if (filter.page) httpParams = httpParams.set('page', filter.page.toString());
    if (filter.pageSize) httpParams = httpParams.set('pageSize', filter.pageSize.toString());
    if (filter.sortBy) httpParams = httpParams.set('sortBy', filter.sortBy);
    if (filter.sortDirection) httpParams = httpParams.set('sortDirection', filter.sortDirection);

    return this.apiResponseHandler.processResponse<{ totalCount: number, items: Parent[] }>(
      this.http.get<ApiResponse<{ totalCount: number, items: Parent[] }>>(`${this.apiUrl}/parents`, { params: httpParams }),
      false,
      'Failed to retrieve parents'
    );
  }

  getParent(id: number): Observable<ParentDetail> {
    return this.apiResponseHandler.processResponse<ParentDetail>(
      this.http.get<ApiResponse<ParentDetail>>(`${this.apiUrl}/parents/${id}`),
      false,
      `Failed to retrieve parent with ID ${id}`
    );
  }

  getParentByUserId(userId: string): Observable<ParentDetail> {
    return this.apiResponseHandler.processResponse<ParentDetail>(
      this.http.get<ApiResponse<ParentDetail>>(`${this.apiUrl}/parents/user/${userId}`),
      false,
      `Failed to retrieve parent with user ID ${userId}`
    );
  }

  createParent(parent: CreateParent): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.post<ApiResponse<{ id: number }>>(`${this.apiUrl}/parents`, parent),
      true,
      'Parent created successfully'
    );
  }

  updateParent(id: number, parent: UpdateParent): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/parents/${id}`, parent),
      true,
      'Parent updated successfully'
    );
  }

  deleteParent(id: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/parents/${id}`),
      true,
      'Parent deleted successfully'
    );
  }

  // Student Association operations
  getParentStudents(parentId: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.get<ApiResponse<any>>(`${this.apiUrl}/parents/${parentId}/students`),
      false,
      'Failed to retrieve parent students'
    );
  }

  addStudentToParent(parentId: number, studentId: number, relationType: ParentRelationType, isPrimaryContact: boolean): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.post<ApiResponse<any>>(`${this.apiUrl}/parents/${parentId}/students`, {
        studentId,
        relationType,
        isPrimaryContact
      }),
      true,
      'Student added to parent successfully'
    );
  }

  updateStudentParentRelation(parentId: number, studentId: number, relationType: ParentRelationType, isPrimaryContact: boolean): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/parents/${parentId}/students/${studentId}`, {
        relationType,
        isPrimaryContact
      }),
      true,
      'Parent-student relationship updated successfully'
    );
  }

  removeStudentFromParent(parentId: number, studentId: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/parents/${parentId}/students/${studentId}`),
      true,
      'Student removed from parent successfully'
    );
  }

  // Parent Portal operations
  getStudentAttendanceForParent(parentId: number, studentId: number, fromDate?: Date, toDate?: Date): Observable<StudentAttendance[]> {
    let httpParams = new HttpParams();

    if (fromDate) httpParams = httpParams.set('fromDate', fromDate.toISOString());
    if (toDate) httpParams = httpParams.set('toDate', toDate.toISOString());

    return this.apiResponseHandler.processResponse<StudentAttendance[]>(
      this.http.get<ApiResponse<StudentAttendance[]>>(`${this.apiUrl}/parents/${parentId}/portal/${studentId}/attendance`, { params: httpParams }),
      false,
      'Failed to retrieve student attendance'
    );
  }

  getStudentFeesForParent(parentId: number, studentId: number, academicYear?: number): Observable<StudentFee[]> {
    let httpParams = new HttpParams();

    if (academicYear !== undefined) httpParams = httpParams.set('academicYear', academicYear.toString());

    return this.apiResponseHandler.processResponse<StudentFee[]>(
      this.http.get<ApiResponse<StudentFee[]>>(`${this.apiUrl}/parents/${parentId}/portal/${studentId}/fees`, { params: httpParams }),
      false,
      'Failed to retrieve student fees'
    );
  }

  getStudentResultsForParent(parentId: number, studentId: number, academicYear?: number, examType?: ExamType): Observable<StudentResult[]> {
    let httpParams = new HttpParams();

    if (academicYear !== undefined) httpParams = httpParams.set('academicYear', academicYear.toString());
    if (examType !== undefined) httpParams = httpParams.set('examType', examType.toString());

    return this.apiResponseHandler.processResponse<StudentResult[]>(
      this.http.get<ApiResponse<StudentResult[]>>(`${this.apiUrl}/parents/${parentId}/portal/${studentId}/results`, { params: httpParams }),
      false,
      'Failed to retrieve student results'
    );
  }

  getStudentLeavesForParent(parentId: number, studentId: number, fromDate?: Date, toDate?: Date, status?: LeaveStatus): Observable<StudentLeave[]> {
    let httpParams = new HttpParams();

    if (fromDate) httpParams = httpParams.set('fromDate', fromDate.toISOString());
    if (toDate) httpParams = httpParams.set('toDate', toDate.toISOString());
    if (status !== undefined) httpParams = httpParams.set('status', status.toString());

    return this.apiResponseHandler.processResponse<StudentLeave[]>(
      this.http.get<ApiResponse<StudentLeave[]>>(`${this.apiUrl}/parents/${parentId}/portal/${studentId}/leaves`, { params: httpParams }),
      false,
      'Failed to retrieve student leaves'
    );
  }

  createLeaveByParent(parentId: number, leave: CreateStudentLeave): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.post<ApiResponse<{ id: number }>>(`${this.apiUrl}/parents/${parentId}/portal/${leave.studentId}/leaves`, leave),
      true,
      'Leave application submitted successfully'
    );
  }
}
