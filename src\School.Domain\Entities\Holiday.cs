using School.Domain.Common;
using School.Domain.Enums;
using School.Domain.ValueObjects;

namespace School.Domain.Entities;

public class Holiday : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public HolidayType Type { get; set; }
    public bool IsRecurring { get; set; } = false;
    public RecurrencePattern? RecurrencePattern { get; set; }
    public string Color { get; set; } = "#FF5722"; // Default orange color
    public bool IsActive { get; set; } = true;
    public bool IsPublic { get; set; } = true;
    public string Remarks { get; set; } = string.Empty;
    
    // Foreign key relationships
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }
    
    // Navigation properties
    public AcademicYear? AcademicYear { get; set; }
    public Term? Term { get; set; }
    public ICollection<HolidayTranslation> Translations { get; set; } = new List<HolidayTranslation>();
}

public class HolidayTranslation : BaseEntity
{
    public Guid HolidayId { get; set; }
    public string LanguageCode { get; set; } = string.Empty; // e.g., "bn-BD", "en-US"
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    
    // Navigation properties
    public Holiday Holiday { get; set; } = null!;
}
