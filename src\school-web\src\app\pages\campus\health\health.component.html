<!-- Hero Section -->
<app-default-hero
  translationPrefix="CAMPUS_LIFE"
  title="CAMPUS_LIFE.HEALTH"
  subtitle="CAMPUS_LIFE.HEALTH_SUBTITLE"
  theme="dark"
  size="large"
  alignment="center"
  backgroundImage="assets/images/campus/health-hero.jpg">
</app-default-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'CAMPUS_LIFE.HEALTH_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.HEALTH_INTRO_P1' | translate }}</p>
      <p>{{ 'CAMPUS_LIFE.HEALTH_INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Health Center Information Section -->
  <section class="health-center-section">
    <h2>{{ 'CAMPUS_LIFE.HEALTH_CENTER' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.HEALTH_CENTER_INTRO' | translate }}</p>

    <div class="health-center-info">
      <div class="info-card">
        <div class="info-icon">
          <mat-icon>schedule</mat-icon>
        </div>
        <h3>{{ 'CAMPUS_LIFE.HOURS' | translate }}</h3>
        <p>{{healthCenter.hours}}</p>
      </div>

      <div class="info-card">
        <div class="info-icon">
          <mat-icon>location_on</mat-icon>
        </div>
        <h3>{{ 'CAMPUS_LIFE.LOCATION' | translate }}</h3>
        <p>{{healthCenter.location}}</p>
      </div>

      <div class="info-card">
        <div class="info-icon">
          <mat-icon>phone</mat-icon>
        </div>
        <h3>{{ 'CAMPUS_LIFE.CONTACT' | translate }}</h3>
        <p>{{healthCenter.phone}}</p>
        <p>{{healthCenter.email}}</p>
      </div>
    </div>

    <div class="emergency-info">
      <mat-icon>emergency</mat-icon>
      <p>{{healthCenter.emergencyInfo}}</p>
    </div>
  </section>

  <!-- Health Services Section -->
  <section class="services-section">
    <h2>{{ 'CAMPUS_LIFE.HEALTH_SERVICES' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.SERVICES_INTRO' | translate }}</p>

    <div class="services-grid">
      <div class="service-card" *ngFor="let service of services">
        <div class="service-icon">
          <mat-icon>{{service.icon}}</mat-icon>
        </div>
        <h3>{{service.title}}</h3>
        <p>{{service.description}}</p>
      </div>
    </div>
  </section>

  <!-- Health Center Staff Section -->
  <section class="staff-section">
    <h2>{{ 'CAMPUS_LIFE.HEALTH_STAFF' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.STAFF_INTRO' | translate }}</p>

    <div class="staff-grid">
      <mat-card class="staff-card" *ngFor="let member of staff">
        <div class="staff-image">
          <img [src]="member.image" [alt]="member.name">
        </div>
        <mat-card-content>
          <h3>{{member.name}}</h3>
          <div class="staff-title">{{member.title}}</div>
          <div class="staff-credentials">{{member.credentials}}</div>
          <p class="staff-bio">{{member.bio}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Wellness Programs Section -->
  <section class="wellness-section">
    <h2>{{ 'CAMPUS_LIFE.WELLNESS_PROGRAMS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.WELLNESS_INTRO' | translate }}</p>

    <div class="wellness-grid">
      <mat-card class="wellness-card" *ngFor="let program of wellnessPrograms">
        <div class="wellness-image">
          <img [src]="program.image" [alt]="program.title">
        </div>
        <mat-card-content>
          <h3>{{program.title}}</h3>
          <p>{{program.description}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Health Resources Section -->
  <section class="resources-section">
    <h2>{{ 'CAMPUS_LIFE.HEALTH_RESOURCES' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.RESOURCES_INTRO' | translate }}</p>

    <div class="resources-grid">
      <div class="resource-card" *ngFor="let resource of healthResources">
        <h3>{{resource.title}}</h3>
        <p>{{resource.description}}</p>
        <a mat-button color="primary" [href]="resource.link" target="_blank">
          {{ 'CAMPUS_LIFE.DOWNLOAD' | translate }}
          <mat-icon>download</mat-icon>
        </a>
      </div>
    </div>
  </section>

  <!-- FAQs Section -->
  <section class="faqs-section">
    <h2>{{ 'CAMPUS_LIFE.FAQS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.FAQS_INTRO' | translate }}</p>

    <div class="faqs-container">
      <mat-accordion>
        <mat-expansion-panel *ngFor="let faq of faqs">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{faq.question}}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <p>{{faq.answer}}</p>
        </mat-expansion-panel>
      </mat-accordion>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="contact-section">
    <div class="contact-content">
      <h2>{{ 'CAMPUS_LIFE.QUESTIONS_CONCERNS' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.CONTACT_TEXT' | translate }}</p>
      <div class="contact-buttons">
        <a mat-raised-button color="primary" href="mailto:healthcenter&#64;school.edu">
          <mat-icon>email</mat-icon>
          {{ 'CAMPUS_LIFE.EMAIL_US' | translate }}
        </a>
        <a mat-raised-button color="accent" href="tel:**********">
          <mat-icon>phone</mat-icon>
          {{ 'CAMPUS_LIFE.CALL_US' | translate }}
        </a>
      </div>
    </div>
  </section>
</div>
