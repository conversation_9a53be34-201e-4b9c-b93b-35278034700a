import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { AlumniService } from '../../core/services/alumni.service';
import { Alumni, AlumniEvent, AlumniFilter } from '../../core/models/alumni.model';
import { DefaultHeroComponent } from '../../shared/components/default-hero/default-hero.component';



@Component({
  selector: 'app-alumni',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatDividerModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './alumni.component.html',
  styleUrl: './alumni.component.scss'
})
export class AlumniComponent implements OnInit {
  // Search filters
  searchTerm: string = '';
  selectedYear: number | null = null;
  graduationYears: number[] = [];

  // Alumni data
  featuredAlumni: Alumni[] = [];
  allAlumni: Alumni[] = [];
  filteredAlumni: Alumni[] = [];

  // Events data
  upcomingEvents: AlumniEvent[] = [];
  pastEvents: AlumniEvent[] = [];

  // Loading and error states
  isLoading: boolean = false;
  error: string | null = null;

  // Pagination
  totalCount: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;

  constructor(private alumniService: AlumniService) { }

  ngOnInit(): void {
    this.loadAlumniData();
    this.loadEventsData();
  }

  /**
   * Load alumni data from API
   */
  loadAlumniData(): void {
    this.isLoading = true;
    this.error = null;

    const filter: AlumniFilter = {
      page: this.currentPage,
      pageSize: this.pageSize,
      sortBy: 'graduationYear',
      sortDirection: 'desc'
    };

    if (this.searchTerm) {
      filter.name = this.searchTerm;
    }

    if (this.selectedYear) {
      filter.graduationYear = this.selectedYear;
    }

    this.alumniService.getAllAlumni(filter).subscribe({
      next: (response) => {
        this.allAlumni = response.items;
        this.totalCount = response.totalCount;
        this.filteredAlumni = [...this.allAlumni];

        // Extract featured alumni
        this.featuredAlumni = this.allAlumni.filter(alumni => alumni.isFeatured);

        // Extract unique graduation years for filter
        this.graduationYears = [...new Set(this.allAlumni.map(a => a.graduationYear))].sort((a, b) => b - a);

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading alumni data:', error);
        this.error = 'Failed to load alumni data. Please try again later.';
        this.isLoading = false;

        // Load mock data as fallback
        this.loadMockAlumniData();
      }
    });
  }

  /**
   * Load mock alumni data as fallback
   */
  loadMockAlumniData(): void {
    // Mock data for alumni
    this.allAlumni = [
      {
        id: 1,
        name: 'Dr. Sarah Johnson',
        graduationYear: 2005,
        profession: 'Neurosurgeon',
        biography: 'Dr. Sarah Johnson is a leading neurosurgeon at Mayo Clinic. After graduating from our school, she went on to complete her medical degree at Harvard Medical School.',
        achievements: 'Published over 30 research papers in neuroscience, Recipient of the Young Scientist Award 2018, Developed a new surgical technique for brain tumor removal',
        isFeatured: true,
        isActive: true,
        displayOrder: 1,
        profileImage: {
          id: 1,
          fileName: 'sarah-johnson.jpg',
          filePath: 'https://via.placeholder.com/400x300?text=Dr.+Sarah+Johnson',
          mimeType: 'image/jpeg'
        },
        createdAt: new Date(),
        translations: []
      },
      {
        id: 2,
        name: 'Michael Chen',
        graduationYear: 2010,
        profession: 'Tech Entrepreneur',
        biography: 'Michael Chen is the founder and CEO of InnovateTech, a startup focused on sustainable technology solutions. He credits his success to the entrepreneurial mindset fostered during his school years.',
        achievements: 'Named in Forbes 30 Under 30, Raised $50 million in venture capital funding, Created jobs for over 200 people',
        isFeatured: true,
        isActive: true,
        displayOrder: 2,
        profileImage: {
          id: 2,
          fileName: 'michael-chen.jpg',
          filePath: 'https://via.placeholder.com/400x300?text=Michael+Chen',
          mimeType: 'image/jpeg'
        },
        createdAt: new Date(),
        translations: []
      },
      {
        id: 3,
        name: 'Priya Patel',
        graduationYear: 2008,
        profession: 'Environmental Scientist',
        biography: 'Priya Patel works with the United Nations on climate change initiatives. Her passion for environmental science was sparked during the school\'s annual science fair.',
        achievements: 'Led climate research expeditions to Antarctica, Advisor to government on environmental policy, Published author of "Climate Solutions for the Future"',
        isFeatured: true,
        isActive: true,
        displayOrder: 3,
        profileImage: {
          id: 3,
          fileName: 'priya-patel.jpg',
          filePath: 'https://via.placeholder.com/400x300?text=Priya+Patel',
          mimeType: 'image/jpeg'
        },
        createdAt: new Date(),
        translations: []
      },
      {
        id: 4,
        name: 'James Wilson',
        graduationYear: 2012,
        profession: 'Film Director',
        biography: 'James Wilson is an award-winning documentary filmmaker whose work focuses on social justice issues. He discovered his passion for storytelling in the school\'s media arts program.',
        achievements: 'Winner of Sundance Film Festival Documentary Award, Emmy nomination for Outstanding Direction, Films featured on Netflix and HBO',
        isFeatured: false,
        isActive: true,
        displayOrder: 4,
        profileImage: {
          id: 4,
          fileName: 'james-wilson.jpg',
          filePath: 'https://via.placeholder.com/400x300?text=James+Wilson',
          mimeType: 'image/jpeg'
        },
        createdAt: new Date(),
        translations: []
      },
      {
        id: 5,
        name: 'Aisha Rahman',
        graduationYear: 2015,
        profession: 'Human Rights Lawyer',
        biography: 'Aisha Rahman works with international organizations to defend human rights globally. She was inspired by the school\'s Model UN program and community service initiatives.',
        achievements: 'Argued cases before the International Criminal Court, Founded a non-profit providing legal aid to refugees, Recipient of the Human Rights Advocate Award',
        isFeatured: false,
        isActive: true,
        displayOrder: 5,
        profileImage: {
          id: 5,
          fileName: 'aisha-rahman.jpg',
          filePath: 'https://via.placeholder.com/400x300?text=Aisha+Rahman',
          mimeType: 'image/jpeg'
        },
        createdAt: new Date(),
        translations: []
      },
      {
        id: 6,
        name: 'David Park',
        graduationYear: 2007,
        profession: 'Olympic Athlete',
        biography: 'David Park represented his country in swimming at two Olympic Games, winning a silver medal. His athletic journey began on the school\'s swim team.',
        achievements: 'Olympic silver medalist, Multiple national records in swimming, Sports ambassador for youth development',
        isFeatured: false,
        isActive: true,
        displayOrder: 6,
        profileImage: {
          id: 6,
          fileName: 'david-park.jpg',
          filePath: 'https://via.placeholder.com/400x300?text=David+Park',
          mimeType: 'image/jpeg'
        },
        createdAt: new Date(),
        translations: []
      },
      {
        id: 7,
        name: 'Emma Rodriguez',
        graduationYear: 2010,
        profession: 'Architect',
        biography: 'Emma Rodriguez is known for her innovative sustainable architecture designs. She credits her math and art teachers for encouraging her to pursue this interdisciplinary field.',
        achievements: 'Designed award-winning eco-friendly buildings, Featured in Architectural Digest, Visiting professor at Columbia University',
        isFeatured: false,
        isActive: true,
        displayOrder: 7,
        profileImage: {
          id: 7,
          fileName: 'emma-rodriguez.jpg',
          filePath: 'https://via.placeholder.com/400x300?text=Emma+Rodriguez',
          mimeType: 'image/jpeg'
        },
        createdAt: new Date(),
        translations: []
      },
      {
        id: 8,
        name: 'Omar Hassan',
        graduationYear: 2009,
        profession: 'Pediatrician',
        biography: 'Dr. Omar Hassan specializes in pediatric oncology, bringing hope to children battling cancer. His volunteer work at the local hospital during high school shaped his career path.',
        achievements: 'Developed new treatment protocol for childhood leukemia, Medical volunteer in underserved communities, Recipient of Compassionate Doctor Award',
        isFeatured: false,
        isActive: true,
        displayOrder: 8,
        profileImage: {
          id: 8,
          fileName: 'omar-hassan.jpg',
          filePath: 'https://via.placeholder.com/400x300?text=Omar+Hassan',
          mimeType: 'image/jpeg'
        },
        createdAt: new Date(),
        translations: []
      }
    ];

    // Set featured alumni
    this.featuredAlumni = this.allAlumni.filter(alumni => alumni.isFeatured);

    // Extract unique graduation years for filter
    this.graduationYears = [...new Set(this.allAlumni.map(a => a.graduationYear))].sort((a, b) => b - a);

    // Initialize filtered alumni
    this.filteredAlumni = [...this.allAlumni];
  }

  /**
   * Load mock events data
   * In a real application, this would come from an API
   */
  loadEventsData(): void {
    const currentDate = new Date();

    // Mock data for events
    const allEvents: AlumniEvent[] = [
      {
        id: 1,
        title: 'Annual Alumni Reunion',
        date: new Date(currentDate.getFullYear(), 5, 15), // June 15 of current year
        location: 'School Campus, Main Auditorium',
        description: 'Join us for our biggest alumni event of the year! Reconnect with classmates, tour the campus to see what\'s new, and enjoy a special dinner celebration.',
        image: 'https://via.placeholder.com/400x200?text=Alumni+Reunion'
      },
      {
        id: 2,
        title: 'Career Mentorship Day',
        date: new Date(currentDate.getFullYear(), 8, 10), // September 10 of current year
        location: 'School Campus, Conference Center',
        description: 'Alumni are invited back to mentor current students. Share your professional journey and provide guidance to the next generation.',
        image: 'https://via.placeholder.com/400x200?text=Mentorship+Day'
      },
      {
        id: 3,
        title: 'Class of 2010 Reunion',
        date: new Date(currentDate.getFullYear(), 10, 5), // November 5 of current year
        location: 'Grand Hotel Downtown',
        description: 'Special reunion for the Class of 2010 celebrating their 10-year anniversary. Includes dinner, dancing, and a special commemorative gift.',
        image: 'https://via.placeholder.com/400x200?text=Class+Reunion'
      },
      {
        id: 4,
        title: 'Alumni Sports Tournament',
        date: new Date(currentDate.getFullYear() - 1, 7, 20), // August 20 of last year
        location: 'School Sports Complex',
        description: 'Annual sports competition between alumni teams. Sports include basketball, soccer, volleyball, and tennis. All skill levels welcome!',
        image: 'https://via.placeholder.com/400x200?text=Sports+Tournament'
      },
      {
        id: 5,
        title: 'Alumni Achievement Awards',
        date: new Date(currentDate.getFullYear() - 1, 11, 12), // December 12 of last year
        location: 'City Convention Center',
        description: 'Annual ceremony recognizing outstanding alumni achievements in various fields. Includes dinner and keynote speech from a distinguished alumnus.',
        image: 'https://via.placeholder.com/400x200?text=Achievement+Awards'
      }
    ];

    // Split events into upcoming and past
    this.upcomingEvents = allEvents.filter(event => event.date > currentDate);
    this.pastEvents = allEvents.filter(event => event.date <= currentDate);

    // Sort events by date
    this.upcomingEvents.sort((a, b) => a.date.getTime() - b.date.getTime());
    this.pastEvents.sort((a, b) => b.date.getTime() - a.date.getTime());
  }

  /**
   * Filter alumni based on search term and selected year
   */
  filterAlumni(): void {
    this.currentPage = 1;
    this.loadAlumniData();
  }

  /**
   * Reset all filters
   */
  resetFilters(): void {
    this.searchTerm = '';
    this.selectedYear = null;
    this.currentPage = 1;
    this.loadAlumniData();
  }

  /**
   * Handle page change
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadAlumniData();
  }

  /**
   * Format date for display
   */
  formatEventDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}