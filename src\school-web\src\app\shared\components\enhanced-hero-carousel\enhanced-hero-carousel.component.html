<div class="enhanced-hero-carousel" [ngClass]="[themeClass, sizeClass]">
  <!-- Slides -->
  <div class="carousel-slides">
    <div class="carousel-slide"
         *ngFor="let slide of slides; let i = index"
         [class.active]="i === currentSlideIndex"
         [style.backgroundImage]="slide.image ? 'url(' + slide.image + ')' : ''">
      <!-- Overlay with pattern -->
      <div class="slide-overlay"></div>

      <!-- Decorative elements -->
      <div class="slide-decorations">
        <div class="circle-decoration circle-1"></div>
        <div class="circle-decoration circle-2"></div>
        <div class="circle-decoration circle-3"></div>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="carousel-container">
    <div class="carousel-content" *ngIf="slides && slides.length > 0 && currentSlideIndex < slides.length">
      <h1 class="slide-title" [ngClass]="getTitleClass(slides[currentSlideIndex].title)">{{slides[currentSlideIndex].title}}</h1>
      <h2 class="slide-subtitle" *ngIf="slides[currentSlideIndex].subtitle" [ngClass]="getSubtitleClass(slides[currentSlideIndex].subtitle)">{{slides[currentSlideIndex].subtitle}}</h2>
      <p class="slide-description" *ngIf="slides[currentSlideIndex].description" [ngClass]="getDescriptionClass(slides[currentSlideIndex].description)">{{slides[currentSlideIndex].description}}</p>

      <!-- Slide buttons -->
      <div class="slide-buttons" *ngIf="slides[currentSlideIndex].buttonText || slides[currentSlideIndex].secondaryButtonText">
        <a *ngIf="slides[currentSlideIndex].buttonText"
           mat-raised-button
           color="primary"
           [routerLink]="slides[currentSlideIndex].buttonLink">
          {{slides[currentSlideIndex].buttonText}}
        </a>
        <a *ngIf="slides[currentSlideIndex].secondaryButtonText"
           mat-stroked-button
           color="primary"
           [routerLink]="slides[currentSlideIndex].secondaryButtonLink">
          {{slides[currentSlideIndex].secondaryButtonText}}
        </a>
      </div>

      <!-- Custom content projection -->
      <div class="custom-content">
        <ng-content></ng-content>
      </div>
    </div>
  </div>

  <!-- Navigation controls -->
  <div class="carousel-controls" *ngIf="showControls && slides && slides.length > 1">
    <button class="carousel-arrow prev" (click)="prevSlide()" aria-label="Previous slide">
      <mat-icon>chevron_left</mat-icon>
    </button>
    <button class="carousel-arrow next" (click)="nextSlide()" aria-label="Next slide">
      <mat-icon>chevron_right</mat-icon>
    </button>
  </div>

  <!-- Indicators -->
  <div class="carousel-indicators" *ngIf="showIndicators && slides && slides.length > 1">
    <button *ngFor="let slide of slides; let i = index"
            [class.active]="i === currentSlideIndex"
            (click)="setCurrentSlide(i)"
            [attr.aria-label]="'Go to slide ' + (i + 1)">
    </button>
  </div>

  <!-- Decorative wave shape at the bottom -->
  <div class="carousel-wave">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 100" preserveAspectRatio="none">
      <path d="M0,50 C150,100 350,0 500,50 C650,100 850,0 1000,50 C1150,100 1350,0 1440,50 L1440,100 L0,100 Z"
            fill="white"></path>
    </svg>
  </div>
</div>
