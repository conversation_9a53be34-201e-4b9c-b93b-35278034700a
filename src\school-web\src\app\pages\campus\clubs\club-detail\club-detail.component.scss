@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Loading and Error States
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
  text-align: center;
  min-height: 400px;

  mat-spinner {
    margin-bottom: 20px;
  }

  .error-icon {
    font-size: 60px;
    height: 60px;
    width: 60px;
    margin-bottom: 20px;
    color: $primary-color;
  }

  h2 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: $text-color;
  }

  p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: $dark-gray;
    max-width: 600px;
  }

  .error-actions {
    display: flex;
    gap: 15px;

    @media (max-width: 576px) {
      flex-direction: column;
    }
  }
}

// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Club Header
.club-header {
  padding: 40px 0;

  .back-button {
    margin-bottom: 20px;

    mat-icon {
      margin-right: 5px;
    }
  }

  .club-header-content {
    display: flex;
    gap: 40px;

    @media (max-width: 992px) {
      flex-direction: column;
    }

    .club-image {
      flex: 0 0 40%;
      position: relative;
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;

      img {
        width: 100%;
        height: auto;
        display: block;
      }

      .club-category {
        position: absolute;
        top: 20px;
        left: 20px;
        background-color: $primary-color;
        color: $white;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        text-transform: uppercase;
      }
    }

    .club-info {
      flex: 1;

      .club-name {
        font-size: 2.5rem;
        margin-bottom: 20px;
        color: $text-color;
      }

      .club-meta {
        margin-bottom: 20px;

        .meta-item {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          color: $dark-gray;

          &:last-child {
            margin-bottom: 0;
          }

          mat-icon {
            font-size: 20px;
            height: 20px;
            width: 20px;
            margin-right: 10px;
            color: $primary-color;
          }
        }
      }

      .club-social {
        display: flex;
        gap: 15px;

        .social-link {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          background-color: $primary-color;
          border-radius: 50%;
          color: $white;
          transition: transform 0.3s, background-color 0.3s;

          &:hover {
            transform: translateY(-3px);
            background-color: color.adjust($primary-color, $lightness: -10%);
          }

          mat-icon {
            font-size: 20px;
            height: 20px;
            width: 20px;
          }
        }
      }
    }
  }
}

// Club Content
.club-content {
  padding: 40px 0;
  background-color: $light-gray;

  .tab-content {
    padding: 30px 0;

    h2 {
      font-size: 1.8rem;
      margin-bottom: 20px;
      color: $text-color;
      position: relative;
      padding-bottom: 10px;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 3px;
        background-color: $primary-color;
      }
    }

    h3 {
      font-size: 1.4rem;
      margin-bottom: 15px;
      color: $text-color;
    }

    p {
      font-size: 1.1rem;
      line-height: 1.6;
      color: $text-color;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .description-section, .activities-section, .achievements-section {
      margin-bottom: 40px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .activities-list, .achievements-list {
      list-style-type: none;
      padding: 0;
      margin: 0;

      li {
        position: relative;
        padding-left: 25px;
        margin-bottom: 15px;
        color: $text-color;
        font-size: 1.1rem;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 8px;
          width: 10px;
          height: 10px;
          background-color: $primary-color;
          border-radius: 50%;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .leaders-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 30px;

      .leader-card {
        background-color: $white;
        border-radius: $border-radius;
        overflow: hidden;
        box-shadow: $box-shadow;

        .leader-image {
          height: 200px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .leader-info {
          padding: 20px;

          .leader-name {
            font-size: 1.3rem;
            margin: 0 0 5px;
            color: $text-color;
          }

          .leader-role {
            font-size: 1.1rem;
            margin: 0 0 5px;
            color: $primary-color;
          }

          .leader-grade {
            font-size: 0.9rem;
            margin: 0;
            color: $dark-gray;
          }
        }
      }
    }

    .requirements-section, .process-section, .contact-section {
      margin-bottom: 30px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .contact-section {
      a {
        display: inline-flex;
        align-items: center;

        mat-icon {
          margin-right: 8px;
        }
      }
    }

    .gallery-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;

      .gallery-item {
        border-radius: $border-radius;
        overflow: hidden;
        box-shadow: $box-shadow;
        position: relative;

        img {
          width: 100%;
          height: auto;
          display: block;
          transition: transform 0.3s;

          &:hover {
            transform: scale(1.05);
          }
        }

        .gallery-caption {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background-color: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 10px;
          font-size: 0.9rem;
        }
      }
    }

    .events-list {
      .event-item {
        background-color: $white;
        border-radius: $border-radius;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: $box-shadow;

        &:last-child {
          margin-bottom: 0;
        }

        .event-date {
          font-size: 1rem;
          color: $primary-color;
          margin-bottom: 10px;
          font-weight: 500;
        }

        .event-details {
          .event-title {
            font-size: 1.3rem;
            margin: 0 0 10px;
            color: $text-color;
          }

          .event-location,
          .event-time {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
            color: $dark-gray;
            margin-bottom: 8px;

            mat-icon {
              font-size: 16px;
              height: 16px;
              width: 16px;
              margin-right: 5px;
              color: $primary-color;
            }
          }

          .event-description {
            font-size: 1rem;
            margin: 0;
            color: $dark-gray;
            line-height: 1.5;
          }
        }
      }
    }
  }
}

// Call to Action
.cta-section {
  background-color: $primary-color;
  color: $white;
  padding: 60px 0;
  text-align: center;

  h2 {
    font-size: 2rem;
    margin-bottom: 20px;
  }

  p {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;

    @media (max-width: 576px) {
      flex-direction: column;
      align-items: center;

      a, button {
        width: 100%;
        max-width: 250px;
      }
    }

    a, button {
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .club-header {
    padding: 30px 0;

    .club-header-content {
      .club-info {
        .club-name {
          font-size: 2rem;
        }
      }
    }
  }

  .club-content {
    padding: 30px 0;

    .tab-content {
      h2 {
        font-size: 1.6rem;
      }

      h3 {
        font-size: 1.3rem;
      }
    }
  }
}

@media (max-width: 576px) {
  .club-header {
    .club-header-content {
      .club-info {
        .club-name {
          font-size: 1.8rem;
        }
      }
    }
  }

  .club-content {
    .tab-content {
      h2 {
        font-size: 1.5rem;
      }

      h3 {
        font-size: 1.2rem;
      }

      p, .activities-list li, .achievements-list li {
        font-size: 1rem;
      }

      .leaders-grid {
        grid-template-columns: 1fr;
      }

      .gallery-grid {
        grid-template-columns: 1fr;
      }
    }
  }

  .cta-section {
    padding: 40px 0;

    h2 {
      font-size: 1.8rem;
    }

    p {
      font-size: 1.1rem;
    }
  }
}
