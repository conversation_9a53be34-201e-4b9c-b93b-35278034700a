@use 'sass:color';
@use '../../../../../styles/_variables' as *;

.footer {
  background-color: #f5f5f5;
  color: $text-color;

  // Top Footer with Newsletter
  .footer-top {
    background-color: $primary-color;
    color: white;
    padding: 40px 0;

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 16px;
    }

    .newsletter-signup {
      max-width: 600px;
      margin: 0 auto;
      text-align: center;

      h3 {
        font-size: 24px;
        margin-bottom: 16px;
      }

      p {
        font-size: 16px;
        margin-bottom: 24px;
      }

      .newsletter-form {
        display: flex;
        gap: 16px;

        mat-form-field {
          flex: 1;

          ::ng-deep .mat-mdc-form-field-subscript-wrapper {
            display: none;
          }

          ::ng-deep .mat-mdc-text-field-wrapper {
            background-color: white;
          }
        }

        button {
          height: 56px;
        }
      }
    }
  }

  // Main Footer Content
  .footer-main {
    padding: 60px 0;

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 16px;
    }

    .footer-content {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 40px;

      .footer-section {
        h3 {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 24px;
          color: $primary-color;
          position: relative;

          &:after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 40px;
            height: 2px;
            background-color: $primary-color;
          }
        }

        // School Info Section
        &.school-info {
          .footer-logo {
            margin-bottom: 16px;

            img {
              height: 60px;
              width: auto;
            }
          }

          h3 {
            font-size: 18px;
            margin-bottom: 8px;

            &:after {
              display: none;
            }
          }

          p {
            font-size: 14px;
            margin-bottom: 16px;
            color: $text-color;
          }

          .social-links {
            display: flex;
            gap: 16px;

            a {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 36px;
              height: 36px;
              border-radius: 50%;
              background-color: $primary-color;
              color: white;
              transition: background-color 0.3s;

              &:hover {
                background-color: color.adjust($primary-color, $lightness: -10%);
              }

              mat-icon {
                font-size: 20px;
                height: 20px;
                width: 20px;
              }
            }
          }
        }

        // Links Sections
        &.links, &.resources {
          ul {
            list-style: none;
            padding: 0;
            margin: 0;

            li {
              margin-bottom: 12px;

              a {
                color: $text-color;
                text-decoration: none;
                font-size: 14px;
                transition: color 0.3s;
                display: inline-block;
                position: relative;

                &:hover {
                  color: $primary-color;
                }

                &:after {
                  content: '';
                  position: absolute;
                  bottom: -2px;
                  left: 0;
                  width: 0;
                  height: 1px;
                  background-color: $primary-color;
                  transition: width 0.3s;
                }

                &:hover:after {
                  width: 100%;
                }
              }
            }
          }
        }

        // Contact Info Section
        &.contact-info {
          address {
            font-style: normal;
            margin-bottom: 24px;

            p {
              display: flex;
              align-items: flex-start;
              margin-bottom: 12px;
              font-size: 14px;

              mat-icon {
                font-size: 20px;
                height: 20px;
                width: 20px;
                margin-right: 12px;
                color: $primary-color;
                margin-top: 2px;
              }
            }
          }

          a {
            display: inline-block;
          }
        }
      }
    }
  }

  // Bottom Footer
  .footer-bottom {
    background-color: #e0e0e0;
    padding: 20px 0;

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .copyright {
        p {
          font-size: 14px;
          margin: 0;
        }
      }

      .legal-links {
        a {
          color: $text-color;
          text-decoration: none;
          font-size: 14px;
          transition: color 0.3s;

          &:hover {
            color: $primary-color;
          }
        }

        .separator {
          margin: 0 8px;
          color: $text-color;
        }
      }

      .back-to-top {
        button {
          width: 40px;
          height: 40px;

          mat-icon {
            font-size: 20px;
            height: 20px;
            width: 20px;
          }
        }
      }
    }
  }
}

// Responsive Styles
@media (max-width: 992px) {
  .footer {
    .footer-main {
      .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;

        .footer-section {
          &.school-info {
            grid-column: span 2;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .footer {
    .footer-top {
      padding: 30px 0;

      .newsletter-signup {
        h3 {
          font-size: 20px;
        }

        p {
          font-size: 14px;
        }

        .newsletter-form {
          flex-direction: column;
          gap: 12px;

          button {
            height: 48px;
          }
        }
      }
    }

    .footer-main {
      padding: 40px 0 30px;

      .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;

        .footer-section {
          &.links, &.resources {
            ul {
              columns: 2;
              column-gap: 30px;
            }
          }
        }
      }
    }

    .footer-bottom {
      .container {
        flex-direction: column;
        text-align: center;

        .copyright {
          margin-bottom: 16px;
        }

        .legal-links {
          margin-bottom: 16px;
        }

        .back-to-top {
          margin-top: 8px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .footer {
    .footer-top {
      padding: 24px 0;
    }

    .footer-main {
      padding: 30px 0 20px;

      .footer-content {
        gap: 24px;

        .footer-section {
          h3 {
            font-size: 16px;
            margin-bottom: 16px;
          }

          &.school-info {
            .footer-logo img {
              height: 50px;
            }

            h3 {
              font-size: 16px;
            }

            p {
              font-size: 13px;
            }

            .social-links {
              gap: 12px;

              a {
                width: 32px;
                height: 32px;

                mat-icon {
                  font-size: 16px;
                  height: 16px;
                  width: 16px;
                }
              }
            }
          }

          &.links, &.resources {
            ul {
              columns: 1;

              li {
                margin-bottom: 10px;

                a {
                  font-size: 13px;
                }
              }
            }
          }

          &.contact-info {
            address p {
              font-size: 13px;

              mat-icon {
                font-size: 18px;
                height: 18px;
                width: 18px;
              }
            }
          }
        }
      }
    }

    .footer-bottom {
      padding: 16px 0;

      .container {
        .copyright p {
          font-size: 12px;
        }

        .legal-links a {
          font-size: 12px;
        }

        .back-to-top button {
          width: 36px;
          height: 36px;

          mat-icon {
            font-size: 18px;
            height: 18px;
            width: 18px;
          }
        }
      }
    }
  }
}

