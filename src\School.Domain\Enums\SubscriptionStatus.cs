namespace School.Domain.Enums;

/// <summary>
/// Status of a subscription
/// </summary>
public enum SubscriptionStatus
{
    /// <summary>
    /// Subscription is pending activation
    /// </summary>
    Pending = 1,

    /// <summary>
    /// Subscription is active and current
    /// </summary>
    Active = 2,

    /// <summary>
    /// Subscription is in trial period
    /// </summary>
    Trial = 3,

    /// <summary>
    /// Subscription payment is past due
    /// </summary>
    PastDue = 4,

    /// <summary>
    /// Subscription has been cancelled but still active until end of period
    /// </summary>
    Cancelled = 5,

    /// <summary>
    /// Subscription has expired
    /// </summary>
    Expired = 6,

    /// <summary>
    /// Subscription is suspended due to non-payment or violation
    /// </summary>
    Suspended = 7,

    /// <summary>
    /// Subscription is on hold (temporary pause)
    /// </summary>
    OnHold = 8,

    /// <summary>
    /// Subscription has been refunded
    /// </summary>
    Refunded = 9
}
