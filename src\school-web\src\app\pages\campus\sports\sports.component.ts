import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../../shared/components/default-hero/default-hero.component';

interface Sport {
  name: string;
  description: string;
  season: string;
  coach: string;
  image: string;
  level: string;
}

interface Achievement {
  year: string;
  sport: string;
  achievement: string;
}

@Component({
  selector: 'app-sports',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatTableModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './sports.component.html',
  styleUrls: ['./sports.component.scss']
})
export class SportsComponent {
  // Sports seasons
  seasons = ['Fall', 'Winter', 'Spring'];

  // Levels
  levels = ['Elementary', 'Middle School', 'High School'];

  // Sports listings
  sports: Sport[] = [
    {
      name: 'Soccer',
      description: 'Our soccer program emphasizes teamwork, skill development, and strategic play. Teams compete in local and regional leagues.',
      season: 'Fall',
      coach: 'Coach Rodriguez',
      image: 'assets/images/campus/sports/soccer.jpg',
      level: 'High School'
    },
    {
      name: 'Basketball',
      description: 'The basketball program focuses on fundamentals, conditioning, and competitive play. Both boys and girls teams participate in conference games and tournaments.',
      season: 'Winter',
      coach: 'Coach Johnson',
      image: 'assets/images/campus/sports/basketball.jpg',
      level: 'High School'
    },
    {
      name: 'Track & Field',
      description: 'Our track and field program offers opportunities in sprints, distance running, jumps, and throws. Athletes compete in meets throughout the region.',
      season: 'Spring',
      coach: 'Coach Williams',
      image: 'assets/images/campus/sports/track.jpg',
      level: 'High School'
    },
    {
      name: 'Volleyball',
      description: 'The volleyball program develops skills, teamwork, and competitive spirit. Teams participate in regular season matches and tournaments.',
      season: 'Fall',
      coach: 'Coach Martinez',
      image: 'assets/images/campus/sports/volleyball.jpg',
      level: 'High School'
    },
    {
      name: 'Swimming',
      description: 'Our swimming program offers training in all competitive strokes and events. Swimmers participate in dual meets and championship competitions.',
      season: 'Winter',
      coach: 'Coach Thompson',
      image: 'assets/images/campus/sports/swimming.jpg',
      level: 'High School'
    },
    {
      name: 'Tennis',
      description: 'The tennis program focuses on skill development, match strategy, and sportsmanship. Players compete in singles and doubles matches against area schools.',
      season: 'Spring',
      coach: 'Coach Lee',
      image: 'assets/images/campus/sports/tennis.jpg',
      level: 'High School'
    },
    {
      name: 'Cross Country',
      description: 'Our cross country program builds endurance, determination, and team spirit. Runners compete in meets on varied terrain throughout the region.',
      season: 'Fall',
      coach: 'Coach Wilson',
      image: 'assets/images/campus/sports/cross-country.jpg',
      level: 'High School'
    },
    {
      name: 'Wrestling',
      description: 'The wrestling program develops strength, technique, and mental toughness. Athletes compete in individual and team competitions.',
      season: 'Winter',
      coach: 'Coach Garcia',
      image: 'assets/images/campus/sports/wrestling.jpg',
      level: 'High School'
    },
    {
      name: 'Baseball',
      description: 'Our baseball program emphasizes fundamentals, teamwork, and competitive play. The team competes in conference games and tournaments.',
      season: 'Spring',
      coach: 'Coach Brown',
      image: 'assets/images/campus/sports/baseball.jpg',
      level: 'High School'
    },
    {
      name: 'Soccer',
      description: 'Middle school soccer introduces players to competitive team play while continuing to develop fundamental skills and game understanding.',
      season: 'Fall',
      coach: 'Coach Davis',
      image: 'assets/images/campus/sports/soccer-ms.jpg',
      level: 'Middle School'
    },
    {
      name: 'Basketball',
      description: 'The middle school basketball program focuses on skill development, teamwork, and introduction to competitive play.',
      season: 'Winter',
      coach: 'Coach Taylor',
      image: 'assets/images/campus/sports/basketball-ms.jpg',
      level: 'Middle School'
    },
    {
      name: 'Track & Field',
      description: 'Middle school track and field introduces students to various events while building fitness and athletic skills.',
      season: 'Spring',
      coach: 'Coach Anderson',
      image: 'assets/images/campus/sports/track-ms.jpg',
      level: 'Middle School'
    },
    {
      name: 'Soccer',
      description: 'Elementary soccer focuses on basic skills, teamwork, and enjoyment of the game in a supportive, non-competitive environment.',
      season: 'Fall',
      coach: 'Coach Miller',
      image: 'assets/images/campus/sports/soccer-elem.jpg',
      level: 'Elementary'
    },
    {
      name: 'Basketball',
      description: 'The elementary basketball program introduces young players to the fundamentals of the game through drills, games, and fun activities.',
      season: 'Winter',
      coach: 'Coach White',
      image: 'assets/images/campus/sports/basketball-elem.jpg',
      level: 'Elementary'
    },
    {
      name: 'Track & Field',
      description: 'Elementary track and field introduces children to running, jumping, and throwing events in a fun, supportive environment.',
      season: 'Spring',
      coach: 'Coach Clark',
      image: 'assets/images/campus/sports/track-elem.jpg',
      level: 'Elementary'
    }
  ];

  // Athletic facilities
  facilities = [
    {
      name: 'Main Gymnasium',
      features: [
        'Full-size basketball court',
        'Volleyball court',
        'Bleacher seating for 800 spectators',
        'Electronic scoreboard',
        'Sound system',
        'Locker rooms'
      ],
      image: 'assets/images/campus/sports/gymnasium.jpg'
    },
    {
      name: 'Auxiliary Gymnasium',
      features: [
        'Full-size basketball court',
        'Volleyball court',
        'Wrestling mats',
        'Training equipment',
        'Locker rooms'
      ],
      image: 'assets/images/campus/sports/aux-gym.jpg'
    },
    {
      name: 'Athletic Field',
      features: [
        'Soccer field',
        'Track (400m)',
        'Field event areas',
        'Bleacher seating',
        'Lighting for evening events'
      ],
      image: 'assets/images/campus/sports/field.jpg'
    },
    {
      name: 'Tennis Courts',
      features: [
        '6 regulation courts',
        'Lighting for evening play',
        'Spectator seating',
        'Practice wall'
      ],
      image: 'assets/images/campus/sports/tennis-courts.jpg'
    },
    {
      name: 'Swimming Pool',
      features: [
        '25-meter pool',
        '6 lanes',
        'Diving area',
        'Spectator seating',
        'Locker rooms'
      ],
      image: 'assets/images/campus/sports/pool.jpg'
    },
    {
      name: 'Fitness Center',
      features: [
        'Cardio equipment',
        'Weight training area',
        'Stretching area',
        'Training staff'
      ],
      image: 'assets/images/campus/sports/fitness.jpg'
    }
  ];

  // Athletic achievements
  achievements: Achievement[] = [
    { year: '2023', sport: 'Basketball (Boys)', achievement: 'State Champions' },
    { year: '2023', sport: 'Swimming (Girls)', achievement: 'Conference Champions' },
    { year: '2023', sport: 'Track & Field', achievement: '3 Individual State Champions' },
    { year: '2022', sport: 'Soccer (Girls)', achievement: 'State Finalists' },
    { year: '2022', sport: 'Tennis (Boys)', achievement: 'Conference Champions' },
    { year: '2022', sport: 'Cross Country (Girls)', achievement: 'State Champions' },
    { year: '2021', sport: 'Volleyball (Girls)', achievement: 'State Semi-Finalists' },
    { year: '2021', sport: 'Basketball (Girls)', achievement: 'Conference Champions' },
    { year: '2021', sport: 'Wrestling', achievement: '2 Individual State Champions' },
    { year: '2020', sport: 'Soccer (Boys)', achievement: 'State Champions' },
    { year: '2020', sport: 'Swimming (Boys)', achievement: 'Conference Champions' },
    { year: '2020', sport: 'Track & Field', achievement: 'State Runners-Up' }
  ];

  // Athletic philosophy
  philosophyPoints = [
    {
      title: 'Character Development',
      description: 'We believe athletics provides unique opportunities to develop integrity, responsibility, teamwork, and leadership skills that benefit students throughout their lives.'
    },
    {
      title: 'Inclusive Participation',
      description: 'We strive to provide athletic opportunities for students of all ability levels, encouraging participation and personal growth regardless of natural talent or experience.'
    },
    {
      title: 'Academic Priority',
      description: 'We emphasize that student-athletes are students first, maintaining high academic standards and supporting educational achievement alongside athletic development.'
    },
    {
      title: 'Skill Development',
      description: 'We focus on teaching fundamental skills, game understanding, and strategic thinking, building a strong foundation for athletic success at all levels.'
    },
    {
      title: 'Competitive Excellence',
      description: 'We encourage the pursuit of excellence through hard work, discipline, and commitment, teaching athletes to compete with intensity while maintaining sportsmanship and respect.'
    },
    {
      title: 'Health and Wellness',
      description: 'We promote lifelong physical fitness, proper nutrition, injury prevention, and overall wellness as essential components of athletic participation.'
    }
  ];

  // Filter sports by season and level
  getSportsBySeasonAndLevel(season: string, level: string): Sport[] {
    return this.sports.filter(sport => sport.season === season && sport.level === level);
  }

  // Display achievements by year
  getAchievementsByYear(year: string): Achievement[] {
    return this.achievements.filter(achievement => achievement.year === year);
  }

  // Get unique years from achievements
  getUniqueYears(): string[] {
    return [...new Set(this.achievements.map(achievement => achievement.year))];
  }
}
