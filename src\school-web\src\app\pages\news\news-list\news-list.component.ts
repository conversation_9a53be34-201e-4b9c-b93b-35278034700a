import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { TranslateModule } from '@ngx-translate/core';

interface NewsArticle {
  id: string;
  title: string;
  date: Date;
  author: string;
  category: string;
  image: string;
  excerpt: string;
  tags: string[];
}

@Component({
  selector: 'app-news-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatPaginatorModule,
    TranslateModule
  ],
  templateUrl: './news-list.component.html',
  styleUrls: ['./news-list.component.scss']
})
export class NewsListComponent {
  // Search and filter
  searchQuery: string = '';
  selectedCategory: string = 'All';
  
  // Pagination
  pageSize = 6;
  pageSizeOptions = [3, 6, 9, 12];
  pageIndex = 0;
  
  // Categories
  categories = ['All', 'Academics', 'Events', 'Achievements', 'Facilities', 'Sports', 'Arts'];
  
  // Mock data for demonstration
  allArticles: NewsArticle[] = [
    {
      id: '1',
      title: 'School Wins National Science Competition',
      date: new Date('2023-09-15'),
      author: 'Admin',
      category: 'Achievements',
      image: 'assets/images/news/science-competition.jpg',
      excerpt: 'Our school\'s science team has won the National Science Competition 2023, competing against 50 other schools from across the country with their innovative project on renewable energy solutions.',
      tags: ['Science', 'Competition', 'Achievement', 'STEM']
    },
    {
      id: '2',
      title: 'Annual Cultural Festival Celebrates Diversity',
      date: new Date('2023-08-20'),
      author: 'Cultural Committee',
      category: 'Events',
      image: 'assets/images/news/cultural-festival.jpg',
      excerpt: 'Our school\'s Annual Cultural Festival was held last weekend, showcasing the rich diversity of our student body with performances, exhibitions, and activities representing cultures from around the world.',
      tags: ['Culture', 'Festival', 'Diversity', 'Arts']
    },
    {
      id: '3',
      title: 'New Sports Complex Inauguration',
      date: new Date('2023-07-10'),
      author: 'Sports Department',
      category: 'Facilities',
      image: 'assets/images/news/sports-complex.jpg',
      excerpt: 'We are excited to announce the inauguration of our new state-of-the-art sports complex, featuring an Olympic-sized swimming pool, multipurpose indoor courts, a fully equipped gymnasium, and outdoor fields.',
      tags: ['Sports', 'Facilities', 'Physical Education', 'Infrastructure']
    },
    {
      id: '4',
      title: 'Students Excel in International Math Olympiad',
      date: new Date('2023-06-25'),
      author: 'Mathematics Department',
      category: 'Academics',
      image: 'assets/images/news/math-olympiad.jpg',
      excerpt: 'Three of our students have received gold medals at the International Mathematics Olympiad, demonstrating exceptional problem-solving skills and mathematical aptitude on the global stage.',
      tags: ['Mathematics', 'Olympiad', 'Achievement', 'Academic']
    },
    {
      id: '5',
      title: 'Annual Sports Day Highlights Athletic Talent',
      date: new Date('2023-05-15'),
      author: 'Sports Department',
      category: 'Sports',
      image: 'assets/images/news/sports-day.jpg',
      excerpt: 'The Annual Sports Day was a grand success with students participating in various track and field events, team sports, and athletic competitions, showcasing their sporting prowess and team spirit.',
      tags: ['Sports', 'Competition', 'Athletics', 'Physical Education']
    },
    {
      id: '6',
      title: 'School Choir Wins Regional Championship',
      date: new Date('2023-04-20'),
      author: 'Music Department',
      category: 'Arts',
      image: 'assets/images/news/choir.jpg',
      excerpt: 'Our school choir has been crowned champions at the Regional Choral Competition, impressing the judges with their harmonious performance and diverse repertoire of classical and contemporary pieces.',
      tags: ['Music', 'Choir', 'Competition', 'Arts']
    },
    {
      id: '7',
      title: 'New STEM Lab Enhances Science Education',
      date: new Date('2023-03-10'),
      author: 'Science Department',
      category: 'Facilities',
      image: 'assets/images/news/stem-lab.jpg',
      excerpt: 'We have inaugurated a cutting-edge STEM laboratory equipped with the latest technology and resources to provide students with hands-on experience in science, technology, engineering, and mathematics.',
      tags: ['STEM', 'Laboratory', 'Science', 'Technology']
    },
    {
      id: '8',
      title: 'Parent-Teacher Conference Strengthens School-Home Partnership',
      date: new Date('2023-02-15'),
      author: 'Admin',
      category: 'Events',
      image: 'assets/images/news/parent-teacher.jpg',
      excerpt: 'The recent Parent-Teacher Conference provided a valuable opportunity for parents and teachers to discuss student progress, address concerns, and collaborate on strategies to support student learning.',
      tags: ['Parents', 'Teachers', 'Conference', 'Partnership']
    },
    {
      id: '9',
      title: 'Students Participate in Model United Nations',
      date: new Date('2023-01-20'),
      author: 'Social Studies Department',
      category: 'Academics',
      image: 'assets/images/news/model-un.jpg',
      excerpt: 'A delegation of our students participated in the Model United Nations conference, representing different countries and engaging in diplomatic discussions on global issues, developing their leadership and public speaking skills.',
      tags: ['Model UN', 'Diplomacy', 'Leadership', 'Global Issues']
    }
  ];

  // Getters for filtered and paginated articles
  get filteredArticles(): NewsArticle[] {
    return this.allArticles.filter(article => {
      const matchesSearch = this.searchQuery === '' || 
        article.title.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        article.excerpt.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        article.tags.some(tag => tag.toLowerCase().includes(this.searchQuery.toLowerCase()));
      
      const matchesCategory = this.selectedCategory === 'All' || 
        article.category === this.selectedCategory;
      
      return matchesSearch && matchesCategory;
    });
  }

  get paginatedArticles(): NewsArticle[] {
    const startIndex = this.pageIndex * this.pageSize;
    return this.filteredArticles.slice(startIndex, startIndex + this.pageSize);
  }

  get totalArticles(): number {
    return this.filteredArticles.length;
  }

  // Handle page change
  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
  }

  // Reset filters
  resetFilters(): void {
    this.searchQuery = '';
    this.selectedCategory = 'All';
    this.pageIndex = 0;
  }

  // Format date for display
  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}
