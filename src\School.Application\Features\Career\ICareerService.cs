using School.Application.DTOs;
using School.Domain.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace School.Application.Features.Career
{
    public interface ICareerService
    {
        Task<(IEnumerable<CareerDto> Careers, int TotalCount)> GetAllCareersAsync(CareerFilterDto filter);
        Task<CareerDetailDto?> GetCareerByIdAsync(Guid id);
        Task<Guid> CreateCareerAsync(CreateCareerDto careerDto);
        Task<bool> UpdateCareerAsync(Guid id, UpdateCareerDto careerDto);
        Task<bool> DeleteCareerAsync(Guid id);
        Task<bool> UpdateCareerStatusAsync(Guid id, CareerStatus status);

        // Translation methods
        Task<bool> AddTranslationAsync(Guid careerId, CreateCareerTranslationDto translationDto);
        Task<bool> UpdateTranslationAsync(Guid careerId, string languageCode, UpdateCareerTranslationDto translationDto);
        Task<bool> DeleteTranslationAsync(Guid careerId, string languageCode);

        // Application methods
        Task<(IEnumerable<CareerApplicationDto> Applications, int TotalCount)> GetCareerApplicationsAsync(CareerApplicationFilterDto filter);
        Task<CareerApplicationDto?> GetApplicationByIdAsync(Guid id);
        Task<Guid> CreateApplicationAsync(CreateCareerApplicationDto applicationDto);
        Task<bool> UpdateApplicationStatusAsync(Guid id, UpdateCareerApplicationStatusDto statusDto);
        Task<bool> DeleteApplicationAsync(Guid id);
    }
}
