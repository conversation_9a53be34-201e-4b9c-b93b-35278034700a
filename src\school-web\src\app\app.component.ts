import { Component, OnInit, ViewChild, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, Router, Scroll, NavigationEnd } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule, MatIconRegistry } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TranslationDebugService } from './translation-debug.service';
import { ViewportScroller } from '@angular/common';
import { filter } from 'rxjs/operators';
import { DomSanitizer } from '@angular/platform-browser';
import { MainNavComponent } from './shared/components/navigation/main-nav/main-nav.component';
import { NoticeBarComponent, SchoolBannerComponent, FooterComponent } from './shared/components/layout';
import { ThemeService } from './core/services/theme.service';
import { LanguageService } from './core/services/language.service';
import { AuthService } from './core/services/auth.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    MatButtonModule,
    MatToolbarModule,
    MatIconModule,
    MatMenuModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    TranslateModule,
    MainNavComponent,
    NoticeBarComponent,
    SchoolBannerComponent,
    FooterComponent
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  currentYear = new Date().getFullYear();
  activeRoute: string = '';
  isAdminRoute: boolean = false;
  isAuthRoute: boolean = false;
  isTenantSetupRoute: boolean = false;
  isMobile: boolean = false;
  isVerySmallScreen: boolean = false;
  isMainNavFixed: boolean = false;

  @ViewChild('mainNav') mainNav!: MainNavComponent;



  constructor(
    private translate: TranslateService,
    private router: Router,
    private translationDebug: TranslationDebugService,
    private viewportScroller: ViewportScroller,
    private matIconRegistry: MatIconRegistry,
    private domSanitizer: DomSanitizer,
    private themeService: ThemeService,
    private languageService: LanguageService,
    private authService: AuthService
  ) {
    // Language service will handle language initialization automatically
    // Theme service will handle theme initialization automatically

    // Theme service will automatically apply themes to the body element

    // Handle fragment scrolling
    this.router.events.pipe(
      filter(e => e instanceof Scroll)
    ).subscribe((e: Scroll) => {
      if (e.position) {
        // Backward navigation
        this.viewportScroller.scrollToPosition(e.position);
      } else if (e.anchor) {
        // Anchor navigation
        this.viewportScroller.scrollToAnchor(e.anchor);
      } else {
        // Forward navigation
        this.viewportScroller.scrollToPosition([0, 0]);
      }
    });

    // Configure Material Icons with improved error handling
    try {
      // Set default font set
      this.matIconRegistry.setDefaultFontSetClass('material-icons');
      console.log('Default font set class configured');

      // Register all icon font sets with proper class names
      this.matIconRegistry.registerFontClassAlias('material-icons', 'material-icons');
      this.matIconRegistry.registerFontClassAlias('material-icons-outlined', 'material-icons-outlined');
      this.matIconRegistry.registerFontClassAlias('material-icons-round', 'material-icons-round');
      this.matIconRegistry.registerFontClassAlias('material-icons-sharp', 'material-icons-sharp');
      this.matIconRegistry.registerFontClassAlias('material-icons-two-tone', 'material-icons-two-tone');

      console.log('All Material Icons font sets registered successfully');
    } catch (error) {
      console.error('Error configuring Material Icons:', error);
    }

    // Register SVG icon set if it exists
    try {
      this.matIconRegistry.addSvgIconSet(
        this.domSanitizer.bypassSecurityTrustResourceUrl('./assets/icons/icon-set.svg')
      );
      console.log('SVG icon set registered successfully');
    } catch (error) {
      console.warn('Could not register SVG icon set:', error);
    }

    // Register individual SVG icons for testing
    try {
      this.matIconRegistry.addSvgIcon(
        'test_icon',
        this.domSanitizer.bypassSecurityTrustResourceUrl('./assets/icons/test-icon.svg')
      );
      console.log('Test SVG icon registered successfully');
    } catch (error) {
      console.warn('Could not register test SVG icon:', error);
    }
  }

  ngOnInit(): void {
    // Initialize authentication service first
    console.log('App component initializing - loading stored authentication state');
    try {
      // This will load stored user data and restore authentication state
      this.authService.getCurrentUser();
      console.log('Authentication state initialized successfully');
    } catch (error) {
      console.error('Error initializing authentication state:', error);
    }

    // Check screen size on init
    this.checkScreenSize();

    // Add error handling for debugging
    try {
      // Enable verbose logging in development mode
      const isDevMode = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
      if (isDevMode) {
        console.log('App component initialized');
        this.translationDebug.setVerbose(true);
      }
    } catch (error) {
      console.error('Error in app component initialization:', error);
    }

    // Track active route for menu highlighting
    this.activeRoute = this.router.url;
    this.isAdminRoute = this.activeRoute.startsWith('/admin');
    this.isAuthRoute = this.activeRoute.startsWith('/login') || this.activeRoute.startsWith('/register');
    this.isTenantSetupRoute = this.activeRoute.startsWith('/tenant-setup');

    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.activeRoute = event.url;
        this.isAdminRoute = this.activeRoute.startsWith('/admin');
        this.isAuthRoute = this.activeRoute.startsWith('/login') || this.activeRoute.startsWith('/register');
        this.isTenantSetupRoute = this.activeRoute.startsWith('/tenant-setup');
        console.log('Active route updated:', this.activeRoute);
        console.log('Is admin route:', this.isAdminRoute);
        console.log('Is auth route:', this.isAuthRoute);
        console.log('Is tenant setup route:', this.isTenantSetupRoute);
      }
    });

    // Use the translation debug service to check translation files
    this.translationDebug.checkTranslationFiles().subscribe({
      next: (result) => {
        const isDevMode = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        if (result.status === 'success') {
          if (isDevMode) {
            console.log('Translation modules available:', result.modules);
            console.log('Supported languages:', result.languages);

            // Check if legacy files are loaded
            if (result.legacy?.en?.status === 'success') {
              console.log('Legacy English file is available');
            }

            if (result.legacy?.bn?.status === 'success') {
              console.log('Legacy Bengali file is available');
            }
          }
        } else {
          console.warn('Translation configuration could not be loaded completely');
        }
      },
      error: (error) => {
        console.error('Error checking translation files:', error);
      }
    });

    // Validate translations for missing keys
    this.translationDebug.validateTranslations().subscribe({
      next: (results) => {
        const isDevMode = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        // Count total missing keys
        let totalMissingKeys = 0;
        Object.keys(results).forEach(module => {
          if (results[module].status === 'incomplete') {
            totalMissingKeys += results[module].count;
          }
        });

        if (totalMissingKeys > 0) {
          console.warn(`Total missing translation keys: ${totalMissingKeys}`);
        } else if (isDevMode) {
          console.log('All translations are complete!');
        }
      },
      error: (error) => {
        console.error('Error validating translations:', error);
      }
    });

    // Additional debugging for translation service
    const isDevMode = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    this.translate.get(['SITE.TITLE', 'LOGIN.TITLE', 'NAV.LOGIN']).subscribe({
      next: (res: any) => {
        if (isDevMode) {
          console.log('Translation test results:', res);
        }
      },
      error: (error) => {
        console.error('Error getting translations:', error);
      }
    });

    // Force reload of translations to ensure all modules are loaded
    const currentLang = this.translate.currentLang;
    this.translate.reloadLang(currentLang).subscribe({
      next: () => {
        if (isDevMode) {
          console.log(`Reloaded translations for ${currentLang}`);
        }
      },
      error: (error) => {
        console.error(`Error reloading translations for ${currentLang}:`, error);
      }
    });
  }

  /**
   * Get current language from language service
   */
  get currentLang(): string {
    return this.languageService.currentLanguage;
  }

  /**
   * Get available languages
   */
  get languages() {
    return this.languageService.availableLanguages;
  }

  /**
   * Switch language method
   */
  switchLanguage(lang: string): void {
    if (lang === 'en' || lang === 'bn') {
      this.languageService.setLanguage(lang as 'en' | 'bn');
    }
  }

  // These methods have been moved to their respective components

  /**
   * Check screen size and set responsive flags
   */
  @HostListener('window:resize', [])
  checkScreenSize(): void {
    const wasMobile = this.isMobile;
    const wasVerySmall = this.isVerySmallScreen;

    this.isMobile = window.innerWidth < 992; // Breakpoint for mobile view
    this.isVerySmallScreen = window.innerWidth < 480; // Breakpoint for very small screens

    // If we're transitioning from mobile to desktop, ensure proper layout restoration
    if (wasMobile && !this.isMobile) {
      this.handleMobileToDesktopTransition();
    }

    // If we're transitioning from very small to larger screen, update layout
    if (wasVerySmall && !this.isVerySmallScreen) {
      this.handleScreenSizeChange();
    }
  }

  /**
   * Handle transition from mobile to desktop view
   */
  private handleMobileToDesktopTransition(): void {
    // Close the mobile drawer if it's open
    if (this.mainNav && this.mainNav.drawer && this.mainNav.drawer.opened) {
      this.mainNav.drawer.close();
    }

    // Reset any mobile-specific states
    this.isMainNavFixed = false;

    // Force a layout update
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 100);
  }

  /**
   * Handle general screen size changes
   */
  private handleScreenSizeChange(): void {
    // Update any components that need to know about screen size changes
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 100);
  }

  /**
   * Toggle mobile menu drawer
   */
  toggleMobileMenu(): void {
    try {
      console.log('Toggling mobile menu');
      if (this.mainNav && this.mainNav.drawer) {
        console.log('Main nav and drawer found, toggling drawer');
        this.mainNav.drawer.toggle();
      } else {
        console.warn('Main nav or drawer not found');
        if (!this.mainNav) console.warn('Main nav is null or undefined');
        if (this.mainNav && !this.mainNav.drawer) console.warn('Drawer is null or undefined');
      }
    } catch (error) {
      console.error('Error toggling mobile menu:', error);
    }
  }

  /**
   * Get current theme from theme service
   */
  get currentTheme(): string {
    return this.themeService.currentTheme;
  }

  /**
   * Switch theme method
   */
  switchTheme(theme: string): void {
    if (theme === 'light' || theme === 'dark') {
      this.themeService.setTheme(theme);
    }
  }
}
