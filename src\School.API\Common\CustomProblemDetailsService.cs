using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Options;
using School.API.Common;
using System.Diagnostics;

namespace School.API.Common
{
    public class CustomProblemDetailsService : ProblemDetailsFactory
    {
        private readonly ApiBehaviorOptions _options;
        private readonly IWebHostEnvironment _env;

        public CustomProblemDetailsService(
            IOptions<ApiBehaviorOptions> options,
            IWebHostEnvironment env)
        {
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _env = env ?? throw new ArgumentNullException(nameof(env));
        }

        public override ProblemDetails CreateProblemDetails(
            HttpContext httpContext,
            int? statusCode = null,
            string? title = null,
            string? type = null,
            string? detail = null,
            string? instance = null)
        {
            statusCode ??= 500;

            var problemDetails = new ProblemDetails
            {
                Status = statusCode,
                Title = title,
                Type = type,
                Detail = detail,
                Instance = instance,
            };

            ApplyProblemDetailsDefaults(httpContext, problemDetails, statusCode.Value);

            return problemDetails;
        }

        public override ValidationProblemDetails CreateValidationProblemDetails(
            HttpContext httpContext,
            ModelStateDictionary modelStateDictionary,
            int? statusCode = null,
            string? title = null,
            string? type = null,
            string? detail = null,
            string? instance = null)
        {
            ArgumentNullException.ThrowIfNull(modelStateDictionary);

            statusCode ??= 400;

            var validationProblemDetails = new ValidationProblemDetails(modelStateDictionary)
            {
                Status = statusCode,
                Type = type,
                Detail = detail,
                Instance = instance,
            };

            if (title != null)
            {
                validationProblemDetails.Title = title;
            }

            ApplyProblemDetailsDefaults(httpContext, validationProblemDetails, statusCode.Value);

            return validationProblemDetails;
        }

        private void ApplyProblemDetailsDefaults(HttpContext httpContext, ProblemDetails problemDetails, int statusCode)
        {
            problemDetails.Status ??= statusCode;

            if (_env.IsDevelopment() && problemDetails.Extensions.TryGetValue("traceId", out var traceId) == false)
            {
                var traceIdString = Activity.Current?.Id ?? httpContext.TraceIdentifier;
                problemDetails.Extensions["traceId"] = traceIdString;
            }

            // Create a custom response that matches our API response format
            var errors = new List<string>();

            if (problemDetails is ValidationProblemDetails validationDetails)
            {
                foreach (var error in validationDetails.Errors)
                {
                    errors.AddRange(error.Value);
                }
            }
            else if (!string.IsNullOrEmpty(problemDetails.Detail))
            {
                errors.Add(problemDetails.Detail);
            }

            // Store our custom response in the HttpContext items to be picked up by middleware
            httpContext.Items["CustomProblemDetails"] = ApiResponse.ErrorResponse(
                problemDetails.Title ?? "An error occurred",
                statusCode,
                errors.ToArray());
        }
    }
}
