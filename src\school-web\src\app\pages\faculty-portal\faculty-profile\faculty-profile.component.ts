import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { RouterModule } from '@angular/router';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { FacultyService } from '../../../core/services/faculty.service';
import { AuthService } from '../../../core/services/auth.service';
import { FacultyDetail } from '../../../core/models/faculty.model';

@Component({
  selector: 'app-faculty-profile',
  templateUrl: './faculty-profile.component.html',
  styleUrls: ['./faculty-profile.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    DatePipe,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatChipsModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatSnackBarModule
  ]
})
export class FacultyProfileComponent implements OnInit {
  faculty: FacultyDetail | null = null;
  loading = true;
  error = false;

  constructor(
    private facultyService: FacultyService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadFacultyData();
  }

  loadFacultyData(): void {
    this.loading = true;

    // In a real application, you would fetch the faculty by user ID
    // For now, we'll use a mock faculty ID
    this.facultyService.getFaculty(1)
      .subscribe({
        next: (faculty) => {
          this.faculty = faculty;
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading faculty data:', err);
          this.error = true;
          this.loading = false;
          this.snackBar.open('Failed to load faculty profile', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  getGenderLabel(gender: number | undefined): string {
    if (gender === undefined) return 'Unknown';
    return gender === 0 ? 'Male' : gender === 1 ? 'Female' : 'Other';
  }
}
