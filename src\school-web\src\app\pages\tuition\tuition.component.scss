// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
$floating-nav-width: 250px;
$floating-nav-transition: all 0.3s ease;

// Global Styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;

  // Add padding to left side when floating nav is visible
  @media (min-width: 1400px) {
    padding-left: calc(#{$floating-nav-width} + 20px);
  }
}

// Floating Navigation Styles
.floating-nav {
  position: fixed;
  top: 50%;
  left: -$floating-nav-width;
  transform: translateY(-50%);
  width: $floating-nav-width;
  background-color: $white;
  border-radius: 0 $border-radius $border-radius 0;
  box-shadow: $box-shadow;
  z-index: 1000;
  transition: $floating-nav-transition;

  &.visible {
    left: 0;
  }

  .floating-nav-content {
    padding: 20px;

    h3 {
      font-size: 1.2rem;
      margin-bottom: 15px;
      color: $primary-color;
      text-align: center;
      padding-bottom: 10px;
      border-bottom: 1px solid $medium-gray;
    }

    .floating-nav-links {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 10px;

        a {
          display: flex;
          align-items: center;
          padding: 10px;
          border-radius: $border-radius;
          text-decoration: none;
          color: $text-color;
          transition: $floating-nav-transition;

          &:hover {
            background-color: rgba($primary-color, 0.05);
            color: $primary-color;
          }

          &.active {
            background-color: rgba($primary-color, 0.1);
            color: $primary-color;
            font-weight: 500;
          }

          mat-icon {
            margin-right: 10px;
            color: $primary-color;
          }
        }
      }
    }
  }
}

section {
  padding: $section-padding;
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 0.5rem;
  color: $text-color;
}

.section-description {
  font-size: 1.2rem;
  text-align: center;
  margin-bottom: 3rem;
  color: $dark-gray;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

// Hero Section styles are now handled by the DefaultHeroComponent

// Custom styles for academic year in the hero section
::ng-deep app-default-hero .academic-year {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 500;
  color: white;
}

// Introduction Section
.intro-section {
  background-color: $white;

  .intro-content {
    max-width: 900px;
    margin: 0 auto;

    h2 {
      font-size: 2.2rem;
      margin-bottom: 1.5rem;
      text-align: center;
      color: $text-color;
    }

    p {
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;
    }

    .quick-links {
      margin-top: 3rem;

      h3 {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
        text-align: center;
        color: $text-color;
      }

      .links-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;

        .quick-link {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-decoration: none;
          color: $text-color;
          background-color: $light-gray;
          padding: 20px;
          border-radius: $border-radius;
          width: 150px;
          transition: transform 0.3s ease, box-shadow 0.3s ease;

          &:hover {
            transform: translateY(-5px);
            box-shadow: $box-shadow;
            color: $primary-color;
          }

          mat-icon {
            font-size: 36px;
            height: 36px;
            width: 36px;
            margin-bottom: 10px;
            color: $primary-color;
          }

          span {
            font-size: 1rem;
            font-weight: 500;
            text-align: center;
          }
        }
      }
    }
  }
}

// Fee Structure Section
.fee-structure-section {
  background-color: $light-gray;

  .fee-accordion {
    margin-top: 40px;

    ::ng-deep .mat-expansion-panel {
      margin-bottom: 16px;
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;

      .mat-expansion-panel-header {
        padding: 20px 24px;

        .mat-expansion-panel-header-title {
          color: $primary-color;
          font-size: 1.3rem;
          font-weight: 500;
        }

        .mat-expansion-panel-header-description {
          color: $dark-gray;
          font-size: 1rem;
        }
      }

      .mat-expansion-panel-content {
        .mat-expansion-panel-body {
          padding: 24px;
        }
      }
    }
  }

  .level-details {
    .level-summary {
      display: flex;
      justify-content: center;
      margin-bottom: 30px;

      .summary-item {
        text-align: center;
        padding: 20px;
        background-color: $white;
        border-radius: $border-radius;
        box-shadow: $box-shadow;

        h4 {
          font-size: 1.2rem;
          margin-bottom: 10px;
          color: $text-color;
        }

        .cost {
          font-size: 2rem;
          font-weight: 700;
          color: $primary-color;
          margin-bottom: 5px;
        }

        .note {
          font-size: 0.9rem;
          color: $dark-gray;
          margin: 0;
        }
      }
    }

    ::ng-deep .mat-tab-group {
      .mat-tab-header {
        margin-bottom: 20px;
      }

      .mat-tab-label {
        min-width: 120px;
      }
    }

    .fees-table-container {
      overflow-x: auto;

      .fees-table {
        width: 100%;
        border-collapse: collapse;

        th, td {
          padding: 12px 16px;
          text-align: left;
          border-bottom: 1px solid $medium-gray;
        }

        th {
          background-color: $primary-color;
          color: $white;
          font-weight: 500;
        }

        tr:nth-child(even) {
          background-color: rgba($light-gray, 0.5);
        }

        tr:hover {
          background-color: rgba($primary-color, 0.05);
        }

        .optional-fee {
          background-color: rgba($light-gray, 0.8);
        }

        .optional-tag {
          display: inline-block;
          background-color: $accent-color;
          color: $white;
          font-size: 0.8rem;
          padding: 2px 8px;
          border-radius: 12px;
          margin-left: 8px;
        }
      }
    }

    .discounts-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;

      .discount-card {
        border-radius: $border-radius;
        box-shadow: $box-shadow;

        ::ng-deep .mat-card-avatar {
          background-color: rgba($primary-color, 0.1);
          color: $primary-color;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        ::ng-deep .mat-card-title {
          font-size: 1.2rem;
          margin-bottom: 8px;
        }

        p {
          margin-bottom: 8px;
          line-height: 1.5;
        }

        .discount-amount {
          font-weight: 700;
          color: $primary-color;
          font-size: 1.1rem;
        }
      }
    }
  }
}

// Payment Plans Section
.payment-plans-section {
  background-color: $white;

  .payment-plans-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .payment-plan-card {
      border-radius: $border-radius;
      box-shadow: $box-shadow;
      height: 100%;
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      ::ng-deep .mat-card-avatar {
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      ::ng-deep .mat-card-title {
        font-size: 1.3rem;
        margin-bottom: 8px;
      }

      ::ng-deep .discount-subtitle {
        color: $accent-color;
        font-weight: 500;
      }

      ::ng-deep .mat-card-content {
        padding-top: 10px;

        p {
          margin-bottom: 12px;
          line-height: 1.5;
        }

        .due-date {
          color: $dark-gray;
          font-size: 0.95rem;
        }
      }
    }
  }
}

// Financial Aid Section
.financial-aid-section {
  background-color: $light-gray;

  .financial-aid-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 40px;

    .aid-info, .scholarship-info {
      background-color: $white;
      padding: 30px;
      border-radius: $border-radius;
      box-shadow: $box-shadow;

      h3 {
        font-size: 1.5rem;
        margin-bottom: 20px;
        color: $text-color;
        position: relative;
        padding-bottom: 10px;

        &:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 50px;
          height: 3px;
          background-color: $primary-color;
        }
      }

      p {
        margin-bottom: 15px;
        line-height: 1.6;
      }

      ul {
        margin-bottom: 20px;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          line-height: 1.5;
        }
      }

      button {
        margin-top: 10px;
      }
    }
  }
}

// FAQ Section
.faq-section {
  background-color: $white;

  .faq-accordion {
    max-width: 900px;
    margin: 40px auto 0;

    ::ng-deep .mat-expansion-panel {
      margin-bottom: 16px;
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;

      .mat-expansion-panel-header {
        padding: 20px 24px;

        .mat-expansion-panel-header-title {
          color: $text-color;
          font-size: 1.1rem;
          font-weight: 500;
        }
      }

      .mat-expansion-panel-content {
        .mat-expansion-panel-body {
          padding: 24px;
          line-height: 1.6;
        }
      }
    }
  }
}

// Contact Section
.contact-section {
  background-color: $light-gray;

  .contact-info {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 40px;
    margin: 40px 0;

    .contact-item {
      display: flex;
      align-items: flex-start;
      gap: 15px;

      mat-icon {
        color: $primary-color;
        font-size: 28px;
        height: 28px;
        width: 28px;
      }

      .contact-details {
        h3 {
          font-size: 1.2rem;
          margin-bottom: 8px;
          color: $text-color;
        }

        p {
          margin: 0;
          line-height: 1.5;
          color: $dark-gray;
        }
      }
    }
  }

  .cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;

    button {
      min-width: 180px;
    }
  }
}

// Responsive Adjustments
@media (max-width: 768px) {
  // Adjust floating navigation for smaller screens
  .floating-nav {
    top: auto;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    transform: translateY(100%);
    border-radius: $border-radius $border-radius 0 0;

    &.visible {
      transform: translateY(0);
    }

    .floating-nav-content {
      padding: 15px;

      h3 {
        font-size: 1rem;
        margin-bottom: 10px;
      }

      .floating-nav-links {
        display: flex;
        justify-content: space-around;

        li {
          margin-bottom: 0;
          margin-right: 5px;

          &:last-child {
            margin-right: 0;
          }

          a {
            flex-direction: column;
            padding: 8px;
            text-align: center;

            mat-icon {
              margin-right: 0;
              margin-bottom: 5px;
            }

            span {
              font-size: 0.8rem;
            }
          }
        }
      }
    }
  }
  .section-title {
    font-size: 2rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .hero-section {
    padding: 100px 0;

    .hero-content {
      h1 {
        font-size: 2.5rem;
      }

      .academic-year {
        font-size: 1.3rem;
      }

      .hero-description {
        font-size: 1rem;
      }
    }
  }

  .intro-section {
    .intro-content {
      h2 {
        font-size: 1.8rem;
      }

      .quick-links {
        .links-container {
          .quick-link {
            width: 120px;
            padding: 15px;
          }
        }
      }
    }
  }

  .fee-structure-section {
    .fee-accordion {
      ::ng-deep .mat-expansion-panel {
        .mat-expansion-panel-header {
          .mat-expansion-panel-header-title {
            font-size: 1.1rem;
          }
        }
      }
    }

    .level-details {
      .level-summary {
        .summary-item {
          .cost {
            font-size: 1.8rem;
          }
        }
      }
    }
  }

  .payment-plans-container,
  .financial-aid-content {
    grid-template-columns: 1fr;
  }

  .contact-section {
    .contact-info {
      flex-direction: column;
      align-items: center;
      gap: 30px;
    }

    .cta-buttons {
      flex-direction: column;
      align-items: center;
      gap: 15px;

      button {
        width: 100%;
        max-width: 300px;
      }
    }
  }
}

@media (max-width: 480px) {
  section {
    padding: 60px 0;
  }

  .hero-section {
    padding: 80px 0;

    .hero-content {
      h1 {
        font-size: 2rem;
      }

      .academic-year {
        font-size: 1.1rem;
      }
    }
  }

  .intro-section {
    .intro-content {
      .quick-links {
        .links-container {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    }
  }

  .fee-structure-section {
    .level-details {
      .fees-table-container {
        .fees-table {
          th, td {
            padding: 10px 12px;
            font-size: 0.9rem;
          }
        }
      }
    }
  }
}
