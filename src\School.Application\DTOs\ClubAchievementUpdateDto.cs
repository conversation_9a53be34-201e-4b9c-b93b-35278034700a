using System.Collections.Generic;

namespace School.Application.DTOs;

public class ClubAchievementUpdateDto
{
    public int? Id { get; set; }
    public string? Description { get; set; }
    public int? Year { get; set; }
    public bool? IsActive { get; set; }
    public List<ClubAchievementTranslationUpdateDto>? Translations { get; set; }
}

public class ClubAchievementTranslationUpdateDto
{
    public int? Id { get; set; }
    public string? LanguageCode { get; set; }
    public string? Description { get; set; }
}
