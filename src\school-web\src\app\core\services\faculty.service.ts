import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { ErrorHandlerService } from './error-handler.service';
import { ApiResponseHandlerService } from './api-response-handler.service';
import { ApiResponse } from '../models/api-response.model';
import {
  Faculty, FacultyDetail, FacultyFilter, CreateFaculty, UpdateFaculty,
  FacultySubject, FacultyStudent, FacultySchedule, DayOfWeek
} from '../models/faculty.model';
import { ExamType } from '../models/student.model';

@Injectable({
  providedIn: 'root'
})
export class FacultyService extends BaseApiService {
  constructor(
    protected override http: HttpClient,
    protected override errorHandler: ErrorHandlerService,
    private apiResponseHandler: ApiResponseHandlerService
  ) {
    super(http, errorHandler);
  }

  // Faculty CRUD operations
  getFaculties(filter: FacultyFilter = {}): Observable<{ totalCount: number, items: Faculty[] }> {
    let httpParams = new HttpParams();

    if (filter.name) httpParams = httpParams.set('name', filter.name);
    if (filter.employeeId) httpParams = httpParams.set('employeeId', filter.employeeId);
    if (filter.department) httpParams = httpParams.set('department', filter.department);
    if (filter.designation) httpParams = httpParams.set('designation', filter.designation);
    if (filter.isClassTeacher !== undefined) httpParams = httpParams.set('isClassTeacher', filter.isClassTeacher.toString());
    if (filter.isActive !== undefined) httpParams = httpParams.set('isActive', filter.isActive.toString());
    if (filter.page) httpParams = httpParams.set('page', filter.page.toString());
    if (filter.pageSize) httpParams = httpParams.set('pageSize', filter.pageSize.toString());
    if (filter.sortBy) httpParams = httpParams.set('sortBy', filter.sortBy);
    if (filter.sortDirection) httpParams = httpParams.set('sortDirection', filter.sortDirection);

    return this.apiResponseHandler.processResponse<{ totalCount: number, items: Faculty[] }>(
      this.http.get<ApiResponse<{ totalCount: number, items: Faculty[] }>>(`${this.apiUrl}/faculties`, { params: httpParams }),
      false,
      'Failed to retrieve faculties'
    );
  }

  getFaculty(id: number): Observable<FacultyDetail> {
    return this.apiResponseHandler.processResponse<FacultyDetail>(
      this.http.get<ApiResponse<FacultyDetail>>(`${this.apiUrl}/faculties/${id}`),
      false,
      `Failed to retrieve faculty with ID ${id}`
    );
  }

  getFacultyByUserId(userId: string): Observable<Faculty> {
    return this.apiResponseHandler.processResponse<Faculty>(
      this.http.get<ApiResponse<Faculty>>(`${this.apiUrl}/faculties/user/${userId}`),
      false,
      `Failed to retrieve faculty with user ID ${userId}`
    );
  }

  createFaculty(faculty: CreateFaculty): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.post<ApiResponse<{ id: number }>>(`${this.apiUrl}/faculties`, faculty),
      true,
      'Faculty created successfully'
    );
  }

  updateFaculty(id: number, faculty: UpdateFaculty): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/faculties/${id}`, faculty),
      true,
      'Faculty updated successfully'
    );
  }

  deleteFaculty(id: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/faculties/${id}`),
      true,
      'Faculty deleted successfully'
    );
  }

  // Faculty Subjects operations
  getFacultySubjects(facultyId: number, academicYear?: number): Observable<FacultySubject[]> {
    let httpParams = new HttpParams();

    if (academicYear !== undefined) httpParams = httpParams.set('academicYear', academicYear.toString());

    return this.apiResponseHandler.processResponse<FacultySubject[]>(
      this.http.get<ApiResponse<FacultySubject[]>>(`${this.apiUrl}/faculties/${facultyId}/subjects`, { params: httpParams }),
      false,
      'Failed to retrieve faculty subjects'
    );
  }

  // Faculty Students operations
  getFacultyStudents(facultyId: number, grade?: number, section?: string): Observable<FacultyStudent[]> {
    let httpParams = new HttpParams();

    if (grade !== undefined) httpParams = httpParams.set('grade', grade.toString());
    if (section) httpParams = httpParams.set('section', section);

    return this.apiResponseHandler.processResponse<FacultyStudent[]>(
      this.http.get<ApiResponse<FacultyStudent[]>>(`${this.apiUrl}/faculties/${facultyId}/students`, { params: httpParams }),
      false,
      'Failed to retrieve faculty students'
    );
  }

  // Faculty Schedule operations
  getFacultySchedule(facultyId: number, day?: DayOfWeek): Observable<FacultySchedule[]> {
    let httpParams = new HttpParams();

    if (day !== undefined) httpParams = httpParams.set('day', day.toString());

    return this.apiResponseHandler.processResponse<FacultySchedule[]>(
      this.http.get<ApiResponse<FacultySchedule[]>>(`${this.apiUrl}/faculties/${facultyId}/schedule`, { params: httpParams }),
      false,
      'Failed to retrieve faculty schedule'
    );
  }

  // Faculty Attendance Management operations
  getClassAttendance(facultyId: number, grade: number, section: string, date: Date): Observable<any> {
    let httpParams = new HttpParams()
      .set('grade', grade.toString())
      .set('section', section)
      .set('date', date.toISOString());

    return this.apiResponseHandler.processResponse<any>(
      this.http.get<ApiResponse<any>>(`${this.apiUrl}/faculties/${facultyId}/attendance`, { params: httpParams }),
      false,
      'Failed to retrieve class attendance'
    );
  }

  recordClassAttendance(facultyId: number, grade: number, section: string, date: Date, attendanceRecords: any[]): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.post<ApiResponse<any>>(`${this.apiUrl}/faculties/${facultyId}/attendance`, {
        grade,
        section,
        date: date.toISOString(),
        attendanceRecords
      }),
      true,
      'Class attendance recorded successfully'
    );
  }

  // Faculty Results Management operations
  getClassResults(facultyId: number, grade: number, section: string, subjectCode: string, examType: ExamType, academicYear: number): Observable<any> {
    let httpParams = new HttpParams()
      .set('grade', grade.toString())
      .set('section', section)
      .set('subjectCode', subjectCode)
      .set('examType', examType.toString())
      .set('academicYear', academicYear.toString());

    return this.apiResponseHandler.processResponse<any>(
      this.http.get<ApiResponse<any>>(`${this.apiUrl}/faculties/${facultyId}/results`, { params: httpParams }),
      false,
      'Failed to retrieve class results'
    );
  }

  recordClassResults(facultyId: number, grade: number, section: string, subjectCode: string, examType: ExamType, academicYear: number, resultRecords: any[]): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.post<ApiResponse<any>>(`${this.apiUrl}/faculties/${facultyId}/results`, {
        grade,
        section,
        subjectCode,
        examType,
        academicYear,
        resultRecords
      }),
      true,
      'Class results recorded successfully'
    );
  }

  // Faculty Leave Management operations
  getPendingLeaveApplications(facultyId: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.get<ApiResponse<any>>(`${this.apiUrl}/faculties/${facultyId}/leave-applications/pending`),
      false,
      'Failed to retrieve pending leave applications'
    );
  }

  approveLeaveApplication(facultyId: number, leaveId: number, comments: string): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/faculties/${facultyId}/leave-applications/${leaveId}/approve`, { comments }),
      true,
      'Leave application approved successfully'
    );
  }

  rejectLeaveApplication(facultyId: number, leaveId: number, comments: string): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/faculties/${facultyId}/leave-applications/${leaveId}/reject`, { comments }),
      true,
      'Leave application rejected successfully'
    );
  }
}
