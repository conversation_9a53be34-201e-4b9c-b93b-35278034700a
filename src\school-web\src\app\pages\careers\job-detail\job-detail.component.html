<!-- Loading State -->
<div class="loading-container" *ngIf="loading">
  <mat-icon class="loading-icon">hourglass_empty</mat-icon>
  <p>{{ 'CAREERS.LOADING_JOB' | translate }}</p>
</div>

<!-- Error State -->
<div class="error-container" *ngIf="error">
  <mat-icon class="error-icon">error_outline</mat-icon>
  <h2>{{ 'CAREERS.JOB_NOT_FOUND' | translate }}</h2>
  <p>{{ 'CAREERS.JOB_NOT_FOUND_MESSAGE' | translate }}</p>
  <button mat-raised-button color="primary" (click)="goBack()">{{ 'CAREERS.BACK_TO_CAREERS' | translate }}</button>
</div>

<!-- Job Detail Content -->
<div class="job-detail-container" *ngIf="!loading && !error && job">
  <!-- Job Header -->
  <section class="job-header">
    <div class="container">
      <button mat-button class="back-button" (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> {{ 'CAREERS.BACK_TO_CAREERS' | translate }}
      </button>
      
      <div class="job-header-content">
        <div class="job-title-section">
          <h1 class="job-title">{{job.title}}</h1>
          <div class="job-meta">
            <div class="meta-item">
              <mat-icon>business</mat-icon>
              <span>{{job.department}}</span>
            </div>
            <div class="meta-item">
              <mat-icon>work</mat-icon>
              <span>{{job.type}}</span>
            </div>
            <div class="meta-item">
              <mat-icon>location_on</mat-icon>
              <span>{{job.location}}</span>
            </div>
            <div class="meta-item">
              <mat-icon>event</mat-icon>
              <span>{{ 'CAREERS.POSTED' | translate }} {{getDaysSincePosting()}} {{ 'CAREERS.DAYS_AGO' | translate }}</span>
            </div>
          </div>
        </div>
        
        <div class="job-actions">
          <a mat-raised-button color="primary" [href]="job.applicationUrl" target="_blank" *ngIf="!isDeadlinePassed() && job.applicationUrl">
            {{ 'CAREERS.APPLY_NOW' | translate }}
          </a>
          <button mat-stroked-button color="primary" (click)="goBack()">
            {{ 'CAREERS.VIEW_OTHER_JOBS' | translate }}
          </button>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Job Content -->
  <section class="job-content">
    <div class="container">
      <div class="content-grid">
        <!-- Main Content -->
        <div class="main-content">
          <div class="description-section">
            <h2>{{ 'CAREERS.JOB_DESCRIPTION' | translate }}</h2>
            <p>{{job.description}}</p>
          </div>
          
          <div class="responsibilities-section">
            <h2>{{ 'CAREERS.RESPONSIBILITIES' | translate }}</h2>
            <ul class="responsibilities-list">
              <li *ngFor="let responsibility of job.responsibilities">{{responsibility}}</li>
            </ul>
          </div>
          
          <div class="qualifications-section">
            <h2>{{ 'CAREERS.QUALIFICATIONS' | translate }}</h2>
            <ul class="qualifications-list">
              <li *ngFor="let qualification of job.qualifications">{{qualification}}</li>
            </ul>
          </div>
          
          <div class="benefits-section" *ngIf="job.benefits && job.benefits.length > 0">
            <h2>{{ 'CAREERS.BENEFITS' | translate }}</h2>
            <ul class="benefits-list">
              <li *ngFor="let benefit of job.benefits">{{benefit}}</li>
            </ul>
          </div>
          
          <div class="application-process-section" *ngIf="job.applicationProcess && job.applicationProcess.length > 0">
            <h2>{{ 'CAREERS.APPLICATION_PROCESS' | translate }}</h2>
            <mat-vertical-stepper>
              <mat-step *ngFor="let step of job.applicationProcess; let i = index" [completed]="false">
                <ng-template matStepLabel>{{step}}</ng-template>
                <p *ngIf="i === 0 && job.requiredDocuments && job.requiredDocuments.length > 0">
                  {{ 'CAREERS.REQUIRED_DOCUMENTS' | translate }}:
                </p>
                <ul *ngIf="i === 0 && job.requiredDocuments && job.requiredDocuments.length > 0">
                  <li *ngFor="let document of job.requiredDocuments">{{document}}</li>
                </ul>
              </mat-step>
            </mat-vertical-stepper>
          </div>
        </div>
        
        <!-- Sidebar -->
        <div class="sidebar">
          <div class="application-card">
            <h3>{{ 'CAREERS.APPLICATION_INFO' | translate }}</h3>
            
            <div class="application-deadline" *ngIf="job.applicationDeadline">
              <h4>{{ 'CAREERS.APPLICATION_DEADLINE' | translate }}</h4>
              <p>{{formatDate(job.applicationDeadline)}}</p>
              
              <div class="deadline-status" *ngIf="isDeadlinePassed()">
                <mat-icon>event_busy</mat-icon>
                <span>{{ 'CAREERS.DEADLINE_PASSED' | translate }}</span>
              </div>
              
              <div class="deadline-status" *ngIf="!isDeadlinePassed()">
                <mat-icon>event_available</mat-icon>
                <span>{{ getDaysUntilDeadline() }} {{ 'CAREERS.DAYS_REMAINING' | translate }}</span>
              </div>
            </div>
            
            <div class="start-date" *ngIf="job.startDate">
              <h4>{{ 'CAREERS.START_DATE' | translate }}</h4>
              <p>{{job.startDate}}</p>
            </div>
            
            <div class="salary" *ngIf="job.salary">
              <h4>{{ 'CAREERS.SALARY' | translate }}</h4>
              <p>{{job.salary}}</p>
            </div>
            
            <div class="application-actions" *ngIf="!isDeadlinePassed()">
              <a mat-raised-button color="primary" [href]="job.applicationUrl" target="_blank" *ngIf="job.applicationUrl" class="apply-button">
                {{ 'CAREERS.APPLY_NOW' | translate }}
              </a>
            </div>
          </div>
          
          <div class="contact-card" *ngIf="job.contactEmail">
            <h3>{{ 'CAREERS.QUESTIONS' | translate }}</h3>
            <p>{{ 'CAREERS.QUESTIONS_TEXT' | translate }}</p>
            <a mat-stroked-button color="primary" [href]="'mailto:' + job.contactEmail">
              <mat-icon>email</mat-icon>
              {{ 'CAREERS.CONTACT_HR' | translate }}
            </a>
          </div>
          
          <div class="share-card">
            <h3>{{ 'CAREERS.SHARE_JOB' | translate }}</h3>
            <div class="share-buttons">
              <a mat-mini-fab color="primary" href="javascript:void(0)" aria-label="Share on LinkedIn">
                <mat-icon>business</mat-icon>
              </a>
              <a mat-mini-fab color="primary" href="javascript:void(0)" aria-label="Share on Twitter">
                <mat-icon>twitter</mat-icon>
              </a>
              <a mat-mini-fab color="primary" href="javascript:void(0)" aria-label="Share by Email">
                <mat-icon>email</mat-icon>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Call to Action -->
  <section class="cta-section" *ngIf="!isDeadlinePassed()">
    <div class="container">
      <h2>{{ 'CAREERS.INTERESTED' | translate }}</h2>
      <p>{{ 'CAREERS.INTERESTED_TEXT' | translate }}</p>
      
      <div class="cta-buttons">
        <a mat-raised-button color="primary" [href]="job.applicationUrl" target="_blank" *ngIf="job.applicationUrl">
          {{ 'CAREERS.APPLY_NOW' | translate }}
        </a>
        <button mat-stroked-button color="primary" (click)="goBack()">
          {{ 'CAREERS.EXPLORE_MORE_JOBS' | translate }}
        </button>
      </div>
    </div>
  </section>
</div>
