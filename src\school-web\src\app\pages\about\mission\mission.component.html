<!-- Enhanced Hero Section -->
<app-enhanced-hero
  [title]="'ABOUT.MISSION_TITLE' | translate"
  [subtitle]="'ABOUT.MISSION_SUBTITLE' | translate"
  [description]="'ABOUT.MISSION_DESCRIPTION' | translate"
  [backgroundImage]="'assets/images/mission-hero.jpg'"
  [overlayImage]="'assets/images/school-emblem.png'"
  [breadcrumbs]="['About', 'Mission']"
  [buttons]="[
    {label: 'ABOUT.LEARN_ABOUT_ADMISSIONS' | translate, link: '/admissions', isPrimary: true, icon: 'school'},
    {label: 'ABOUT.CONTACT_US' | translate, link: '/contact', isPrimary: false}
  ]"
  [theme]="'light'"
  [size]="'large'"
  [alignment]="'center'">
</app-enhanced-hero>

<!-- Main Content -->
<div class="container">
  <!-- Mission Statement Section -->
  <section class="mission-statement-section">
    <div class="mission-content">
      <h2>{{ 'ABOUT.OUR_MISSION' | translate }}</h2>
      <div class="mission-box">
        <p class="mission-text">{{ 'ABOUT.MISSION_STATEMENT' | translate }}</p>
      </div>
    </div>
  </section>

  <!-- Vision Statement Section -->
  <section class="vision-statement-section">
    <div class="vision-content">
      <h2>{{ 'ABOUT.OUR_VISION' | translate }}</h2>
      <div class="vision-box">
        <p class="vision-text">{{ 'ABOUT.VISION_STATEMENT' | translate }}</p>
      </div>
    </div>
  </section>

  <!-- Core Values Section -->
  <section class="core-values-section">
    <h2>{{ 'ABOUT.CORE_VALUES' | translate }}</h2>
    <p class="section-intro">{{ 'ABOUT.CORE_VALUES_INTRO' | translate }}</p>

    <div class="values-grid">
      <mat-card class="value-card" *ngFor="let value of coreValues">
        <div class="value-icon">
          <mat-icon>{{value.icon}}</mat-icon>
        </div>
        <mat-card-content>
          <h3>{{value.title}}</h3>
          <p>{{value.description}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Strategic Goals Section -->
  <section class="strategic-goals-section">
    <h2>{{ 'ABOUT.STRATEGIC_GOALS' | translate }}</h2>
    <p class="section-intro">{{ 'ABOUT.STRATEGIC_GOALS_INTRO' | translate }}</p>

    <div class="goals-container">
      <div class="goal-item" *ngFor="let goal of strategicGoals; let i = index">
        <div class="goal-number">{{i + 1}}</div>
        <div class="goal-content">
          <div class="goal-icon">
            <mat-icon>{{goal.icon}}</mat-icon>
          </div>
          <h3>{{goal.title}}</h3>
          <p>{{goal.description}}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Philosophy Section -->
  <section class="philosophy-section">
    <h2>{{ 'ABOUT.EDUCATIONAL_PHILOSOPHY' | translate }}</h2>
    <div class="philosophy-content">
      <p>{{ 'ABOUT.PHILOSOPHY_P1' | translate }}</p>
      <p>{{ 'ABOUT.PHILOSOPHY_P2' | translate }}</p>
      <p>{{ 'ABOUT.PHILOSOPHY_P3' | translate }}</p>
    </div>
  </section>

  <!-- Call to Action Section -->
  <section class="cta-section">
    <div class="cta-content">
      <h2>{{ 'ABOUT.JOIN_OUR_COMMUNITY' | translate }}</h2>
      <p>{{ 'ABOUT.JOIN_COMMUNITY_TEXT' | translate }}</p>
      <div class="cta-buttons">
        <a mat-raised-button color="primary" routerLink="/admissions">
          {{ 'ABOUT.LEARN_ABOUT_ADMISSIONS' | translate }}
        </a>
        <a mat-stroked-button color="primary" routerLink="/contact">
          {{ 'ABOUT.CONTACT_US' | translate }}
        </a>
      </div>
    </div>
  </section>
</div>
