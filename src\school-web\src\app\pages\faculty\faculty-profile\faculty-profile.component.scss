// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Global Styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

section {
  padding: $section-padding;
}

// Loading State
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  
  .loading-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
    color: $primary-color;
    animation: spin 1.5s linear infinite;
  }
  
  p {
    font-size: 1.2rem;
    color: $dark-gray;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Error State
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 40px 20px;
  
  .error-icon {
    font-size: 64px;
    height: 64px;
    width: 64px;
    margin-bottom: 24px;
    color: $accent-color;
  }
  
  h2 {
    font-size: 2rem;
    margin-bottom: 16px;
    color: $text-color;
  }
  
  p {
    font-size: 1.2rem;
    margin-bottom: 24px;
    color: $dark-gray;
    max-width: 600px;
  }
  
  button {
    padding: 8px 24px;
  }
}

// Profile Header
.profile-header {
  background-color: $light-gray;
  padding-top: 40px;
  padding-bottom: 40px;
  
  .back-button {
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    
    mat-icon {
      margin-right: 8px;
    }
  }
  
  .profile-header-content {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    
    .profile-image-container {
      flex: 0 0 300px;
      
      .profile-image {
        width: 100%;
        height: auto;
        border-radius: $border-radius;
        box-shadow: $box-shadow;
      }
    }
    
    .profile-header-info {
      flex: 1 1 500px;
      
      h1 {
        font-size: 2.5rem;
        margin-bottom: 8px;
        color: $text-color;
      }
      
      h2 {
        font-size: 1.5rem;
        margin-bottom: 8px;
        color: $primary-color;
        font-weight: 500;
      }
      
      .department {
        font-size: 1.1rem;
        margin-bottom: 16px;
        color: $dark-gray;
      }
      
      .profile-specializations {
        margin: 24px 0;
        
        mat-chip-set {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }
      }
      
      .profile-contact {
        margin: 24px 0;
        
        .contact-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          
          mat-icon {
            margin-right: 12px;
            color: $primary-color;
          }
          
          span {
            color: $dark-gray;
          }
        }
      }
      
      .profile-social {
        display: flex;
        gap: 16px;
        margin-top: 24px;
        
        .social-link {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: $light-gray;
          color: $primary-color;
          transition: background-color 0.3s ease, color 0.3s ease;
          box-shadow: $box-shadow;
          
          &:hover {
            background-color: $primary-color;
            color: $white;
          }
          
          mat-icon {
            font-size: 20px;
            height: 20px;
            width: 20px;
          }
        }
      }
    }
  }
}

// Profile Content
.profile-content {
  background-color: $white;
  
  ::ng-deep .mat-tab-group {
    .mat-tab-header {
      margin-bottom: 30px;
    }
    
    .mat-tab-label {
      min-width: 120px;
    }
  }
  
  .tab-content {
    padding: 20px 0;
    
    h3 {
      font-size: 1.5rem;
      margin-bottom: 16px;
      color: $text-color;
      position: relative;
      padding-bottom: 8px;
      
      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 40px;
        height: 3px;
        background-color: $primary-color;
      }
    }
    
    p {
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 24px;
      color: $text-color;
    }
    
    .years-of-service {
      font-size: 1.1rem;
      color: $dark-gray;
      margin-top: 24px;
      
      strong {
        color: $text-color;
      }
    }
    
    .education-section {
      margin-top: 40px;
    }
    
    .education-list,
    .courses-list,
    .publications-list,
    .awards-list {
      padding-left: 20px;
      
      li {
        margin-bottom: 12px;
        line-height: 1.5;
        color: $text-color;
      }
    }
  }
}

// Other Faculty Section
.other-faculty-section {
  background-color: $light-gray;
  text-align: center;
  
  h2 {
    font-size: 2rem;
    margin-bottom: 24px;
    color: $text-color;
  }
  
  button {
    padding: 8px 24px;
  }
}

// Responsive Adjustments
@media (max-width: 768px) {
  .profile-header {
    .profile-header-content {
      .profile-image-container {
        flex: 0 0 250px;
      }
      
      .profile-header-info {
        h1 {
          font-size: 2rem;
        }
        
        h2 {
          font-size: 1.3rem;
        }
      }
    }
  }
  
  .profile-content {
    .tab-content {
      h3 {
        font-size: 1.3rem;
      }
    }
  }
  
  .other-faculty-section {
    h2 {
      font-size: 1.8rem;
    }
  }
}

@media (max-width: 600px) {
  .profile-header {
    .profile-header-content {
      flex-direction: column;
      gap: 24px;
      
      .profile-image-container {
        flex: 0 0 auto;
        max-width: 250px;
        margin: 0 auto;
      }
      
      .profile-header-info {
        flex: 1 1 auto;
        text-align: center;
        
        h1, h2, .department {
          text-align: center;
        }
        
        .profile-specializations {
          mat-chip-set {
            justify-content: center;
          }
        }
        
        .profile-contact {
          .contact-item {
            justify-content: center;
          }
        }
        
        .profile-social {
          justify-content: center;
        }
      }
    }
  }
  
  .profile-content {
    .tab-content {
      h3 {
        &:after {
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
}
