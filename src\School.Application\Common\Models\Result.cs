namespace School.Application.Common.Models;

public class ApiResult
{
    internal ApiResult(bool success, IEnumerable<string> errors)
    {
        Success = success;
        Errors = errors.ToArray();
    }

    public bool Success { get; set; }

    public string[] Errors { get; set; }

    public static ApiResult SuccessResult()
    {
        return new ApiResult(true, Array.Empty<string>());
    }

    public static ApiResult FailureResult(IEnumerable<string> errors)
    {
        return new ApiResult(false, errors);
    }
}

public class ApiResult<T> : ApiResult
{
    private ApiResult(bool success, T data, IEnumerable<string> errors)
        : base(success, errors)
    {
        Data = data;
    }

    public T Data { get; set; }

    public static ApiResult<T> SuccessResult(T data)
    {
        return new ApiResult<T>(true, data, Array.Empty<string>());
    }

    public static new ApiResult<T> FailureResult(IEnumerable<string> errors)
    {
        return new ApiResult<T>(false, default!, errors);
    }

    public static ApiResult<T> FailureResult(string error)
    {
        return new ApiResult<T>(false, default!, new[] { error });
    }
}
