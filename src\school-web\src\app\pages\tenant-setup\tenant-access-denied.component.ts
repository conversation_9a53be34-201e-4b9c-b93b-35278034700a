import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-tenant-access-denied',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    RouterModule,
    TranslateModule
  ],
  template: `
    <div class="access-denied-container">
      <mat-card class="access-denied-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>school</mat-icon>
            School Setup Required
          </mat-card-title>
          <mat-card-subtitle>
            No school access found
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p><strong>You need a school to be created for you before you can proceed with setup.</strong></p>

          <div class="info-section">
            <h3><mat-icon>info</mat-icon> How it works:</h3>
            <ol>
              <li><strong>System Administrator</strong> creates a school/tenant for you</li>
              <li><strong>You receive</strong> credentials and access to your school</li>
              <li><strong>You complete</strong> the school setup wizard</li>
            </ol>
          </div>

          <div class="contact-section">
            <h3><mat-icon>contact_support</mat-icon> Next Steps:</h3>
            <p>Please contact your <strong>System Administrator</strong> to:</p>
            <ul>
              <li>Create a new school/tenant for your organization</li>
              <li>Grant you Tenant Administrator access</li>
              <li>Provide you with login credentials</li>
            </ul>
          </div>

          <div class="note-section">
            <p><mat-icon>lightbulb</mat-icon> <em>Once your school is created, you'll be able to complete the setup wizard to configure your school management system.</em></p>
          </div>
          <div class="actions">
            <button mat-raised-button color="primary" routerLink="/login">
              <mat-icon>arrow_back</mat-icon>
              Back to Login
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .access-denied-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
      padding: 20px;
      z-index: 9999;
      overflow-y: auto;
    }
    .access-denied-card {
      max-width: 600px;
      width: 100%;
    }
    mat-card-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    .info-section, .contact-section, .note-section {
      margin: 20px 0;
      padding: 16px;
      border-radius: 8px;
      background: rgba(0,0,0,0.02);
    }
    .info-section h3, .contact-section h3 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      color: #1976d2;
    }
    ol, ul {
      text-align: left;
      margin: 12px 0;
      padding-left: 20px;
    }
    ol li, ul li {
      margin: 8px 0;
    }
    .note-section {
      background: rgba(255, 193, 7, 0.1);
      border-left: 4px solid #ffc107;
    }
    .note-section p {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-style: italic;
    }
    .actions {
      margin-top: 24px;
      text-align: center;
    }
  `]
})
export class TenantAccessDeniedComponent {}
