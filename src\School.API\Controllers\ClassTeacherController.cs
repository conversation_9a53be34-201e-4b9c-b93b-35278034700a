using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.Application.DTOs;
using School.Application.Features.ClassTeacher;
using School.Domain.Enums;

namespace School.API.Controllers;

/// <summary>
/// API controller for ClassTeacher management operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ClassTeacherController : ControllerBase
{
    private readonly IClassTeacherService _classTeacherService;
    private readonly ILogger<ClassTeacherController> _logger;

    public ClassTeacherController(IClassTeacherService classTeacherService, ILogger<ClassTeacherController> logger)
    {
        _classTeacherService = classTeacherService;
        _logger = logger;
    }

    /// <summary>
    /// Get all class teacher assignments with filtering and pagination
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<object>> GetClassTeachers([FromQuery] ClassTeacherFilterDto filter)
    {
        try
        {
            var (classTeachers, totalCount) = await _classTeacherService.GetAllClassTeachersAsync(filter);
            
            return Ok(new
            {
                data = classTeachers,
                totalCount,
                page = filter.Page,
                pageSize = filter.PageSize,
                totalPages = (int)Math.Ceiling((double)totalCount / filter.PageSize)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving class teachers");
            return StatusCode(500, "An error occurred while retrieving class teachers");
        }
    }

    /// <summary>
    /// Get class teacher assignment by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ClassTeacherDto>> GetClassTeacher(Guid id)
    {
        try
        {
            var classTeacher = await _classTeacherService.GetClassTeacherByIdAsync(id);
            
            if (classTeacher == null)
            {
                return NotFound($"Class teacher assignment with ID {id} not found");
            }

            return Ok(classTeacher);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving class teacher {ClassTeacherId}", id);
            return StatusCode(500, "An error occurred while retrieving the class teacher assignment");
        }
    }

    /// <summary>
    /// Get class teacher assignments by academic year
    /// </summary>
    [HttpGet("academic-year/{academicYearId}")]
    public async Task<ActionResult<IEnumerable<ClassTeacherDto>>> GetClassTeachersByAcademicYear(Guid academicYearId)
    {
        try
        {
            var classTeachers = await _classTeacherService.GetClassTeachersByAcademicYearAsync(academicYearId);
            return Ok(classTeachers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving class teachers for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while retrieving class teachers");
        }
    }

    /// <summary>
    /// Get active class teacher assignments by academic year
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/active")]
    public async Task<ActionResult<IEnumerable<ClassTeacherDto>>> GetActiveClassTeachers(Guid academicYearId)
    {
        try
        {
            var classTeachers = await _classTeacherService.GetActiveClassTeachersAsync(academicYearId);
            return Ok(classTeachers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active class teachers for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while retrieving active class teachers");
        }
    }

    /// <summary>
    /// Get class teacher assignments by faculty
    /// </summary>
    [HttpGet("faculty/{facultyId}")]
    public async Task<ActionResult<IEnumerable<ClassTeacherDto>>> GetClassTeachersByFaculty(Guid facultyId)
    {
        try
        {
            var classTeachers = await _classTeacherService.GetClassTeachersByFacultyAsync(facultyId);
            return Ok(classTeachers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving class teachers for faculty {FacultyId}", facultyId);
            return StatusCode(500, "An error occurred while retrieving class teachers");
        }
    }

    /// <summary>
    /// Get class teacher assignment by section
    /// </summary>
    [HttpGet("section/{sectionId}")]
    public async Task<ActionResult<ClassTeacherDto>> GetClassTeacherBySection(Guid sectionId)
    {
        try
        {
            var classTeacher = await _classTeacherService.GetClassTeacherBySectionAsync(sectionId);
            
            if (classTeacher == null)
            {
                return NotFound($"No class teacher assigned to section {sectionId}");
            }

            return Ok(classTeacher);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving class teacher for section {SectionId}", sectionId);
            return StatusCode(500, "An error occurred while retrieving the class teacher");
        }
    }

    /// <summary>
    /// Assign class teacher to section
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<object>> AssignClassTeacher([FromBody] CreateClassTeacherDto assignmentDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Validate assignment
            var isValid = await _classTeacherService.ValidateClassTeacherAssignmentAsync(
                assignmentDto.FacultyId, 
                assignmentDto.SectionId, 
                assignmentDto.AcademicYearId);
            
            if (!isValid)
            {
                return BadRequest("Invalid class teacher assignment. Faculty may already be assigned or section may already have a class teacher.");
            }

            var assignmentId = await _classTeacherService.CreateClassTeacherAsync(assignmentDto);
            
            return CreatedAtAction(nameof(GetClassTeacher), new { id = assignmentId }, new { id = assignmentId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning class teacher");
            return StatusCode(500, "An error occurred while assigning the class teacher");
        }
    }

    /// <summary>
    /// Update class teacher assignment
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult> UpdateClassTeacher(Guid id, [FromBody] UpdateClassTeacherDto assignmentDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var success = await _classTeacherService.UpdateClassTeacherAsync(id, assignmentDto);
            
            if (!success)
            {
                return NotFound($"Class teacher assignment with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating class teacher assignment {ClassTeacherId}", id);
            return StatusCode(500, "An error occurred while updating the class teacher assignment");
        }
    }

    /// <summary>
    /// Delete class teacher assignment
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteClassTeacher(Guid id)
    {
        try
        {
            // Check if assignment can be deleted
            var canDelete = await _classTeacherService.CanDeleteClassTeacherAsync(id);
            if (!canDelete)
            {
                return BadRequest("Cannot delete active class teacher assignment");
            }

            var success = await _classTeacherService.DeleteClassTeacherAsync(id);
            
            if (!success)
            {
                return NotFound($"Class teacher assignment with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting class teacher assignment {ClassTeacherId}", id);
            return StatusCode(500, "An error occurred while deleting the class teacher assignment");
        }
    }

    /// <summary>
    /// Reassign class teacher to different section
    /// </summary>
    [HttpPost("{sectionId}/reassign/{newFacultyId}")]
    public async Task<ActionResult> ReassignClassTeacher(Guid sectionId, Guid newFacultyId, [FromBody] string reason = "")
    {
        try
        {
            var success = await _classTeacherService.ReassignClassTeacherAsync(sectionId, newFacultyId, reason);
            
            if (!success)
            {
                return BadRequest("Failed to reassign class teacher. Section or faculty not found.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reassigning class teacher for section {SectionId} to faculty {FacultyId}", 
                sectionId, newFacultyId);
            return StatusCode(500, "An error occurred while reassigning the class teacher");
        }
    }

    /// <summary>
    /// Remove class teacher from section
    /// </summary>
    [HttpDelete("section/{sectionId}")]
    public async Task<ActionResult> RemoveClassTeacher(Guid sectionId, [FromBody] string reason = "")
    {
        try
        {
            var success = await _classTeacherService.RemoveClassTeacherAsync(sectionId, reason);
            
            if (!success)
            {
                return NotFound($"No active class teacher found for section {sectionId}");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing class teacher from section {SectionId}", sectionId);
            return StatusCode(500, "An error occurred while removing the class teacher");
        }
    }

    /// <summary>
    /// Transfer class teacher between sections
    /// </summary>
    [HttpPost("transfer")]
    public async Task<ActionResult> TransferClassTeacher([FromBody] TransferClassTeacherDto transferDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var success = await _classTeacherService.TransferClassTeacherAsync(
                transferDto.FacultyId, 
                transferDto.FromSectionId, 
                transferDto.ToSectionId);
            
            if (!success)
            {
                return BadRequest("Failed to transfer class teacher. Sections not found or target section already has a class teacher.");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error transferring class teacher {FacultyId} from section {FromSectionId} to {ToSectionId}", 
                transferDto.FacultyId, transferDto.FromSectionId, transferDto.ToSectionId);
            return StatusCode(500, "An error occurred while transferring the class teacher");
        }
    }

    /// <summary>
    /// Update class teacher status
    /// </summary>
    [HttpPatch("{id}/status")]
    public async Task<ActionResult> UpdateStatus(Guid id, [FromBody] UpdateClassTeacherStatusDto statusDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var success = await _classTeacherService.UpdateClassTeacherStatusAsync(id, statusDto.Status, statusDto.Reason);

            if (!success)
            {
                return NotFound($"Class teacher assignment with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating status for class teacher {ClassTeacherId}", id);
            return StatusCode(500, "An error occurred while updating the status");
        }
    }

    /// <summary>
    /// Activate class teacher assignment
    /// </summary>
    [HttpPatch("{id}/activate")]
    public async Task<ActionResult> ActivateClassTeacher(Guid id)
    {
        try
        {
            var success = await _classTeacherService.ActivateClassTeacherAsync(id);

            if (!success)
            {
                return NotFound($"Class teacher assignment with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating class teacher {ClassTeacherId}", id);
            return StatusCode(500, "An error occurred while activating the class teacher");
        }
    }

    /// <summary>
    /// Suspend class teacher assignment
    /// </summary>
    [HttpPatch("{id}/suspend")]
    public async Task<ActionResult> SuspendClassTeacher(Guid id, [FromBody] string reason = "")
    {
        try
        {
            var success = await _classTeacherService.SuspendClassTeacherAsync(id, reason);

            if (!success)
            {
                return NotFound($"Class teacher assignment with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error suspending class teacher {ClassTeacherId}", id);
            return StatusCode(500, "An error occurred while suspending the class teacher");
        }
    }

    /// <summary>
    /// Complete class teacher assignment
    /// </summary>
    [HttpPatch("{id}/complete")]
    public async Task<ActionResult> CompleteAssignment(Guid id, [FromBody] DateTime? endDate = null)
    {
        try
        {
            var success = await _classTeacherService.CompleteClassTeacherAssignmentAsync(id, endDate);

            if (!success)
            {
                return NotFound($"Class teacher assignment with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing class teacher assignment {ClassTeacherId}", id);
            return StatusCode(500, "An error occurred while completing the assignment");
        }
    }

    /// <summary>
    /// Get faculty workload
    /// </summary>
    [HttpGet("faculty/{facultyId}/workload/{academicYearId}")]
    public async Task<ActionResult<ClassTeacherWorkloadDto>> GetFacultyWorkload(Guid facultyId, Guid academicYearId)
    {
        try
        {
            var workload = await _classTeacherService.GetFacultyWorkloadAsync(facultyId, academicYearId);
            return Ok(workload);
        }
        catch (ArgumentException)
        {
            return NotFound($"Faculty with ID {facultyId} not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving workload for faculty {FacultyId}", facultyId);
            return StatusCode(500, "An error occurred while retrieving faculty workload");
        }
    }

    /// <summary>
    /// Get all faculty workloads for academic year
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/workloads")]
    public async Task<ActionResult<IEnumerable<ClassTeacherWorkloadDto>>> GetAllFacultyWorkloads(Guid academicYearId)
    {
        try
        {
            var workloads = await _classTeacherService.GetAllFacultyWorkloadsAsync(academicYearId);
            return Ok(workloads);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving faculty workloads for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while retrieving faculty workloads");
        }
    }

    /// <summary>
    /// Get overloaded faculty
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/overloaded")]
    public async Task<ActionResult<IEnumerable<ClassTeacherWorkloadDto>>> GetOverloadedFaculty(
        Guid academicYearId,
        [FromQuery] decimal threshold = 100.0m)
    {
        try
        {
            var overloadedFaculty = await _classTeacherService.GetOverloadedFacultyAsync(academicYearId, threshold);
            return Ok(overloadedFaculty);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving overloaded faculty for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while retrieving overloaded faculty");
        }
    }

    /// <summary>
    /// Get class teacher performance
    /// </summary>
    [HttpGet("{id}/performance")]
    public async Task<ActionResult<ClassTeacherPerformanceDto>> GetPerformance(Guid id)
    {
        try
        {
            var performance = await _classTeacherService.GetClassTeacherPerformanceAsync(id);
            return Ok(performance);
        }
        catch (ArgumentException)
        {
            return NotFound($"Class teacher assignment with ID {id} not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving performance for class teacher {ClassTeacherId}", id);
            return StatusCode(500, "An error occurred while retrieving performance data");
        }
    }

    /// <summary>
    /// Get unassigned sections
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/unassigned-sections")]
    public async Task<ActionResult<IEnumerable<SectionDto>>> GetUnassignedSections(Guid academicYearId)
    {
        try
        {
            var sections = await _classTeacherService.GetUnassignedSectionsAsync(academicYearId);
            return Ok(sections);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving unassigned sections for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while retrieving unassigned sections");
        }
    }

    /// <summary>
    /// Get available faculty for assignment
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/available-faculty")]
    public async Task<ActionResult<IEnumerable<FacultyDto>>> GetAvailableFaculty(Guid academicYearId)
    {
        try
        {
            var faculty = await _classTeacherService.GetAvailableFacultyForAssignmentAsync(academicYearId);
            return Ok(faculty);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving available faculty for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while retrieving available faculty");
        }
    }

    /// <summary>
    /// Bulk assign class teachers
    /// </summary>
    [HttpPost("bulk-assign")]
    public async Task<ActionResult> BulkAssignClassTeachers([FromBody] BulkClassTeacherAssignmentDto assignmentDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var success = await _classTeacherService.BulkAssignClassTeachersAsync(
                assignmentDto.SectionFacultyMappings,
                assignmentDto.AcademicYearId);

            if (!success)
                return BadRequest("Failed to assign class teachers");

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk assigning class teachers");
            return StatusCode(500, "An error occurred while bulk assigning class teachers");
        }
    }

    /// <summary>
    /// Export class teachers to CSV
    /// </summary>
    [HttpGet("academic-year/{academicYearId}/export")]
    public async Task<ActionResult> ExportClassTeachers(Guid academicYearId)
    {
        try
        {
            var csvStream = await _classTeacherService.ExportClassTeachersToCsvAsync(academicYearId);
            return File(csvStream, "text/csv", $"class_teachers_{academicYearId}_{DateTime.Now:yyyyMMdd}.csv");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting class teachers for academic year {AcademicYearId}", academicYearId);
            return StatusCode(500, "An error occurred while exporting class teachers");
        }
    }
}
