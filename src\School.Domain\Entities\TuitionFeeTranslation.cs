using School.Domain.Common;

namespace School.Domain.Entities;

public class TuitionFeeTranslation : BaseEntity
{
    public Guid TuitionFeeId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;

    // Navigation properties
    public TuitionFee TuitionFee { get; set; } = null!;
}
