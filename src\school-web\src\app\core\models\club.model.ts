import { MediaItem } from './media.model';

/**
 * Club model aligned with the API ClubDto
 */
export interface Club {
  id: number;
  name: string;
  description: string;
  shortDescription?: string;
  category: string;
  meetingSchedule?: string;
  location?: string;
  requirements?: string;
  joinProcess?: string;
  contactEmail?: string;
  website?: string;
  instagram?: string;
  facebook?: string;
  isFeatured?: boolean;
  displayOrder?: number;
  isActive?: boolean;
  profileImageId?: number;
  profileImageUrl?: string;
  createdAt?: Date;
  updatedAt?: Date;
  meetingTime?: string;
  image?: string;
  advisor?: string;

  // Related collections
  translations?: ClubTranslation[];
  advisors?: ClubAdvisor[] | string[];
  leaders?: ClubLeader[];
  activities?: ClubActivity[];
  achievements?: ClubAchievement[];
  events?: ClubEvent[];
  galleryItems?: ClubGalleryItem[];
}

/**
 * Club detail model with additional information
 */
export interface ClubDetail extends Club {
  // Additional properties for detail view
}

/**
 * Club translation model aligned with ClubTranslationDto
 */
export interface ClubTranslation {
  id: number;
  clubId: number;
  languageCode: string;
  name: string;
  description: string;
  shortDescription: string;
  requirements: string;
  joinProcess: string;
}

/**
 * Club advisor model aligned with ClubAdvisorDto
 */
export interface ClubAdvisor {
  id: number;
  clubId: number;
  facultyId?: number;
  name: string;
  email: string;
  phone: string;
  displayOrder: number;
  faculty?: any; // FacultyDto
}

/**
 * Club leader model aligned with ClubLeaderDto
 */
export interface ClubLeader {
  id: number;
  clubId: number;
  studentId?: number;
  name: string;
  role: string;
  grade: string;
  profileImageId?: number;
  profileImageUrl?: string;
  displayOrder: number;
  student?: any; // StudentDto
}

/**
 * Club activity model aligned with ClubActivityDto
 */
export interface ClubActivity {
  id: number;
  clubId: number;
  description: string;
  displayOrder: number;
  translations?: ClubActivityTranslation[];
}

/**
 * Club activity translation model aligned with ClubActivityTranslationDto
 */
export interface ClubActivityTranslation {
  id: number;
  clubActivityId: number;
  languageCode: string;
  description: string;
}

/**
 * Club achievement model aligned with ClubAchievementDto
 */
export interface ClubAchievement {
  id: number;
  clubId: number;
  description: string;
  year?: number;
  displayOrder: number;
  translations?: ClubAchievementTranslation[];
}

/**
 * Club achievement translation model aligned with ClubAchievementTranslationDto
 */
export interface ClubAchievementTranslation {
  id: number;
  clubAchievementId: number;
  languageCode: string;
  description: string;
}

/**
 * Club event model aligned with ClubEventDto
 */
export interface ClubEvent {
  id: number;
  clubId: number;
  title: string;
  date: Date;
  time: string;
  location: string;
  description: string;
  isActive: boolean;
  translations?: ClubEventTranslation[];
}

/**
 * Club event translation model aligned with ClubEventTranslationDto
 */
export interface ClubEventTranslation {
  id: number;
  clubEventId: number;
  languageCode: string;
  title: string;
  location: string;
  description: string;
}

/**
 * Club gallery item model aligned with ClubGalleryItemDto
 */
export interface ClubGalleryItem {
  id: number;
  clubId: number;
  mediaId?: number;
  imageUrl: string;
  caption: string;
  displayOrder: number;
}

/**
 * Create club model aligned with ClubCreateDto
 */
export interface CreateClub {
  name: string;
  description: string;
  shortDescription?: string;
  category: string;
  meetingSchedule?: string;
  location?: string;
  requirements?: string;
  joinProcess?: string;
  contactEmail?: string;
  website?: string;
  instagram?: string;
  facebook?: string;
  isFeatured?: boolean;
  displayOrder?: number;
  isActive?: boolean;
  profileImageId?: number;
  profileImageUrl?: string;

  // Related collections
  translations?: CreateClubTranslation[];
  advisors?: CreateClubAdvisor[];
  leaders?: CreateClubLeader[];
  activities?: CreateClubActivity[];
  achievements?: CreateClubAchievement[];
  events?: CreateClubEvent[];
  galleryItems?: CreateClubGalleryItem[];
}

/**
 * Create club translation model aligned with ClubTranslationCreateDto
 */
export interface CreateClubTranslation {
  languageCode: string;
  name: string;
  description: string;
  shortDescription?: string;
  requirements?: string;
  joinProcess?: string;
}

/**
 * Create club advisor model aligned with ClubAdvisorCreateDto
 */
export interface CreateClubAdvisor {
  facultyId?: number;
  name: string;
  email?: string;
  phone?: string;
  displayOrder?: number;
}

/**
 * Create club leader model aligned with ClubLeaderCreateDto
 */
export interface CreateClubLeader {
  studentId?: number;
  name: string;
  role: string;
  grade?: string;
  profileImageId?: number;
  profileImageUrl?: string;
  displayOrder?: number;
}

/**
 * Create club activity model aligned with ClubActivityCreateDto
 */
export interface CreateClubActivity {
  description: string;
  displayOrder?: number;
  translations?: CreateClubActivityTranslation[];
}

/**
 * Create club activity translation model aligned with ClubActivityTranslationCreateDto
 */
export interface CreateClubActivityTranslation {
  languageCode: string;
  description: string;
}

/**
 * Create club achievement model aligned with ClubAchievementCreateDto
 */
export interface CreateClubAchievement {
  description: string;
  year?: number;
  displayOrder?: number;
  translations?: CreateClubAchievementTranslation[];
}

/**
 * Create club achievement translation model aligned with ClubAchievementTranslationCreateDto
 */
export interface CreateClubAchievementTranslation {
  languageCode: string;
  description: string;
}

/**
 * Create club event model aligned with ClubEventCreateDto
 */
export interface CreateClubEvent {
  title: string;
  date: Date;
  time?: string;
  location?: string;
  description: string;
  isActive?: boolean;
  translations?: CreateClubEventTranslation[];
}

/**
 * Create club event translation model aligned with ClubEventTranslationCreateDto
 */
export interface CreateClubEventTranslation {
  languageCode: string;
  title: string;
  location?: string;
  description: string;
}

/**
 * Create club gallery item model aligned with ClubGalleryItemCreateDto
 */
export interface CreateClubGalleryItem {
  mediaId?: number;
  imageUrl: string;
  caption?: string;
  displayOrder?: number;
}

/**
 * Update club model aligned with ClubUpdateDto
 */
export interface UpdateClub {
  name?: string;
  description?: string;
  shortDescription?: string;
  category?: string;
  meetingSchedule?: string;
  location?: string;
  requirements?: string;
  joinProcess?: string;
  contactEmail?: string;
  website?: string;
  instagram?: string;
  facebook?: string;
  isFeatured?: boolean;
  displayOrder?: number;
  isActive?: boolean;
  profileImageId?: number;
  profileImageUrl?: string;

  // Related collections
  translations?: UpdateClubTranslation[];
  advisors?: UpdateClubAdvisor[];
  leaders?: UpdateClubLeader[];
  activities?: UpdateClubActivity[];
  achievements?: UpdateClubAchievement[];
  events?: UpdateClubEvent[];
  galleryItems?: UpdateClubGalleryItem[];
}

/**
 * Update club translation model aligned with ClubTranslationUpdateDto
 */
export interface UpdateClubTranslation {
  languageCode: string;
  name?: string;
  description?: string;
  shortDescription?: string;
  requirements?: string;
  joinProcess?: string;
}

/**
 * Update club advisor model aligned with ClubAdvisorUpdateDto
 */
export interface UpdateClubAdvisor {
  id: number;
  facultyId?: number;
  name?: string;
  email?: string;
  phone?: string;
  displayOrder?: number;
}

/**
 * Update club leader model aligned with ClubLeaderUpdateDto
 */
export interface UpdateClubLeader {
  id: number;
  studentId?: number;
  name?: string;
  role?: string;
  grade?: string;
  profileImageId?: number;
  profileImageUrl?: string;
  displayOrder?: number;
}

/**
 * Update club activity model aligned with ClubActivityUpdateDto
 */
export interface UpdateClubActivity {
  id: number;
  description?: string;
  displayOrder?: number;
  translations?: UpdateClubActivityTranslation[];
}

/**
 * Update club activity translation model aligned with ClubActivityTranslationUpdateDto
 */
export interface UpdateClubActivityTranslation {
  languageCode: string;
  description?: string;
}

/**
 * Update club achievement model aligned with ClubAchievementUpdateDto
 */
export interface UpdateClubAchievement {
  id: number;
  description?: string;
  year?: number;
  displayOrder?: number;
  translations?: UpdateClubAchievementTranslation[];
}

/**
 * Update club achievement translation model aligned with ClubAchievementTranslationUpdateDto
 */
export interface UpdateClubAchievementTranslation {
  languageCode: string;
  description?: string;
}

/**
 * Update club event model aligned with ClubEventUpdateDto
 */
export interface UpdateClubEvent {
  id: number;
  title?: string;
  date?: Date;
  time?: string;
  location?: string;
  description?: string;
  isActive?: boolean;
  translations?: UpdateClubEventTranslation[];
}

/**
 * Update club event translation model aligned with ClubEventTranslationUpdateDto
 */
export interface UpdateClubEventTranslation {
  languageCode: string;
  title?: string;
  location?: string;
  description?: string;
}

/**
 * Update club gallery item model aligned with ClubGalleryItemUpdateDto
 */
export interface UpdateClubGalleryItem {
  id: number;
  mediaId?: number;
  imageUrl?: string;
  caption?: string;
  displayOrder?: number;
}

/**
 * Club filter model aligned with ClubFilterDto
 */
export interface ClubFilter {
  name?: string;
  category?: string;
  isFeatured?: boolean;
  isActive?: boolean;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: string;
}
