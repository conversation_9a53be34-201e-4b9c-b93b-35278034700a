using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Media;
using School.Domain.Entities;
using School.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Services
{
    public class MediaService : IMediaService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<MediaService> _logger;
        private readonly IFileStorageService _fileStorageService;

        public MediaService(
            IUnitOfWork unitOfWork,
            ICurrentUserService currentUserService,
            ILogger<MediaService> logger,
            IFileStorageService fileStorageService)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
            _logger = logger;
            _fileStorageService = fileStorageService;
        }

        public async Task<(IEnumerable<MediaItemDto> MediaItems, int TotalCount)> GetAllMediaAsync(MediaFilterDto filter)
        {
            _logger.LogInformation("Getting all media items with filter: {Filter}", new { filter.Search, filter.Type, filter.ContentId, filter.Page, filter.PageSize });

            try
            {
                var repository = _unitOfWork.Repository<MediaItem>();
                var query = repository.AsQueryable("UploadedBy");

                // Apply filters
                if (!string.IsNullOrEmpty(filter.Search))
                {
                    query = query.Where(m => m.FileName.Contains(filter.Search) ||
                                           m.OriginalFileName.Contains(filter.Search) ||
                                           m.Caption.Contains(filter.Search) ||
                                           m.AltText.Contains(filter.Search));
                }

                if (filter.Type.HasValue)
                {
                    query = query.Where(m => m.Type == filter.Type.Value);
                }

                if (filter.ContentId.HasValue)
                {
                    query = query.Where(m => m.ContentId == filter.ContentId.Value);
                }

                // Get total count
                var totalCount = await query.CountAsync();

                // Get paginated results
                var mediaItems = await query
                    .OrderByDescending(m => m.CreatedAt)
                    .Skip((filter.Page - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .Select(m => new MediaItemDto
                    {
                        Id = m.Id,
                        FileName = m.FileName,
                        OriginalFileName = m.OriginalFileName,
                        FilePath = m.FilePath,
                        MimeType = m.MimeType,
                        FileSize = m.FileSize,
                        Type = m.Type,
                        AltText = m.AltText,
                        Caption = m.Caption,
                        ContentId = m.ContentId,
                        UploadedById = m.UploadedById.ToString(),
                        UploadedByName = m.UploadedBy != null ? m.UploadedBy.Username : string.Empty,
                        CreatedAt = m.CreatedAt,
                        UpdatedAt = m.UpdatedAt
                    })
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} media items out of {TotalCount}", mediaItems.Count(), totalCount);
                return (mediaItems, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving media items: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        public async Task<MediaItemDto?> GetMediaByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting media item by ID: {MediaId}", id);

            try
            {
                var repository = _unitOfWork.Repository<MediaItem>();
                var media = await repository.GetByIdAsync(id, new[] { "UploadedBy" });

                if (media == null)
                {
                    _logger.LogWarning("Media item with ID {MediaId} not found", id);
                    return null;
                }

                return new MediaItemDto
                {
                    Id = media.Id,
                    FileName = media.FileName,
                    OriginalFileName = media.OriginalFileName,
                    FilePath = media.FilePath,
                    MimeType = media.MimeType,
                    FileSize = media.FileSize,
                    Type = media.Type,
                    AltText = media.AltText,
                    Caption = media.Caption,
                    ContentId = media.ContentId,
                    UploadedById = media.UploadedById.ToString(),
                    UploadedByName = media.UploadedBy != null ? media.UploadedBy.Username : string.Empty,
                    CreatedAt = media.CreatedAt,
                    UpdatedAt = media.UpdatedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting media item by ID {MediaId}: {ErrorMessage}", id, ex.Message);
                throw;
            }
        }

        public async Task<Guid> UploadMediaAsync(MediaItemCreateDto mediaDto)
        {
            _logger.LogInformation("Uploading new media item: {FileName}", mediaDto.OriginalFileName);

            try
            {
                var repository = _unitOfWork.Repository<MediaItem>();

                // Generate a unique filename
                string uniqueFileName = $"{Guid.NewGuid()}_{mediaDto.OriginalFileName}";

                // Determine the file path based on media type
                string subFolder = GetSubFolderByMediaType(mediaDto.Type);

                // Save the file using the file storage service
                string filePath;
                if (mediaDto.FileStream != null)
                {
                    filePath = await _fileStorageService.SaveFileAsync(mediaDto.FileStream, uniqueFileName, subFolder);
                }
                else
                {
                    // If no file stream is provided, just use the path
                    filePath = $"{subFolder}/{uniqueFileName}";
                }

                var media = new MediaItem
                {
                    FileName = uniqueFileName,
                    OriginalFileName = mediaDto.OriginalFileName,
                    FilePath = filePath,
                    MimeType = mediaDto.MimeType,
                    FileSize = mediaDto.FileSize,
                    UploadedById = _currentUserService.UserId.HasValue ? _currentUserService.UserId.Value : throw new InvalidOperationException("UserId cannot be null"),
                    Type = mediaDto.Type,
                    AltText = mediaDto.AltText,
                    Caption = mediaDto.Caption,
                    ContentId = mediaDto.ContentId,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = _currentUserService.UserId.ToString()
                };

                await repository.AddAsync(media);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Media item created successfully with ID: {MediaId}", media.Id);
                return media.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading media item: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        public async Task<bool> UpdateMediaAsync(Guid id, MediaItemUpdateDto mediaDto)
        {
            _logger.LogInformation("Updating media item with ID: {MediaId}", id);

            try
            {
                var repository = _unitOfWork.Repository<MediaItem>();
                var media = await repository.GetByIdAsync(id);

                if (media == null)
                {
                    _logger.LogWarning("Media item with ID {MediaId} not found for update", id);
                    return false;
                }

                // Update properties
                if (mediaDto.Type.HasValue)
                    media.Type = mediaDto.Type.Value;

                if (mediaDto.AltText != null)
                    media.AltText = mediaDto.AltText;

                if (mediaDto.Caption != null)
                    media.Caption = mediaDto.Caption;

                if (mediaDto.ContentId.HasValue)
                    media.ContentId = mediaDto.ContentId;

                media.UpdatedAt = DateTime.UtcNow;
                media.LastModifiedBy = _currentUserService.UserId.ToString();
                media.LastModifiedAt = DateTime.UtcNow;

                await repository.UpdateAsync(media);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Media item with ID {MediaId} updated successfully", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating media item with ID {MediaId}: {ErrorMessage}", id, ex.Message);
                throw;
            }
        }

        public async Task<bool> DeleteMediaAsync(Guid id)
        {
            _logger.LogInformation("Deleting media item with ID: {MediaId}", id);

            try
            {
                var repository = _unitOfWork.Repository<MediaItem>();

                // Check if media exists
                var media = await repository.GetByIdAsync(id);
                if (media == null)
                {
                    _logger.LogWarning("Media item with ID {MediaId} not found for deletion", id);
                    return false;
                }

                // Use the repository's DeleteByIdAsync method which handles soft delete
                await repository.DeleteByIdAsync(id);
                await _unitOfWork.SaveChangesAsync();

                // Try to delete the physical file (but don't fail if this fails)
                try
                {
                    if (!string.IsNullOrEmpty(media.FilePath))
                    {
                        await _fileStorageService.DeleteFileAsync(media.FilePath);
                    }
                }
                catch (Exception ex)
                {
                    // Log but don't throw - we've already deleted the database record
                    _logger.LogWarning(ex, "Failed to delete physical file for media ID {MediaId}: {ErrorMessage}", id, ex.Message);
                }

                _logger.LogInformation("Media item with ID {MediaId} deleted successfully", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting media item with ID {MediaId}: {ErrorMessage}", id, ex.Message);
                throw;
            }
        }


        #region Helper Methods

        private string GetSubFolderByMediaType(MediaType type)
        {
            return type switch
            {
                MediaType.Image => "images",
                MediaType.Document => "documents",
                MediaType.Video => "videos",
                MediaType.Audio => "audio",
                _ => "misc"
            };
        }

        #endregion
    }
}
