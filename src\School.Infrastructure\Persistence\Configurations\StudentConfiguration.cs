using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class StudentConfiguration : IEntityTypeConfiguration<Student>
{
    public void Configure(EntityTypeBuilder<Student> builder)
    {
        builder.ToTable("Students");

        builder.HasKey(s => s.Id);

        // Configure existing properties only
        builder.Property(s => s.FirstName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(s => s.LastName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(s => s.Email)
            .HasMaxLength(100);

        builder.Property(s => s.Phone)
            .HasMaxLength(20);

        builder.Property(s => s.Address)
            .HasMaxLength(500);

        builder.Property(s => s.BloodGroup)
            .HasMaxLength(10);

        builder.Property(s => s.UserId)
            .IsRequired()
            .HasMaxLength(450);

        builder.Property(s => s.CreatedBy)
            .HasMaxLength(450);

        builder.Property(s => s.LastModifiedBy)
            .HasMaxLength(450);

        // Configure relationships that exist
        builder.HasOne(s => s.ProfileImage)
            .WithMany()
            .HasForeignKey(s => s.ProfileImageId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(s => s.ClassTeacher)
            .WithMany()
            .HasForeignKey(s => s.ClassTeacherId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure indexes
        builder.HasIndex(s => s.Email);
        builder.HasIndex(s => s.Phone);
        builder.HasIndex(s => s.RollNumber);
        builder.HasIndex(s => s.ClassTeacherId);
        builder.HasIndex(s => s.UserId);

        // Global query filter for multi-tenancy
        builder.HasQueryFilter(s => EF.Property<string>(s, "TenantId") == null || 
                                   EF.Property<string>(s, "TenantId") == "");
    }
}
