<div class="results-container">
  <h1 class="page-title">Results Management</h1>

  <div *ngIf="loading.faculty" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error.faculty" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load faculty data. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadFacultyData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading.faculty && !error.faculty && faculty" class="results-content">
    <!-- Filter Form -->
    <mat-card class="filter-card">
      <mat-card-header>
        <mat-card-title>Select Class and Exam</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <form [formGroup]="filterForm" (ngSubmit)="loadResults()">
          <div class="filter-form">
            <mat-form-field appearance="outline">
              <mat-label>Subject</mat-label>
              <mat-select formControlName="subject">
                <mat-option *ngFor="let subject of subjects" [value]="subject">
                  Class {{ subject.grade }}-{{ subject.section }} | {{ subject.subjectName }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="filterForm.get('subject')?.hasError('required')">
                Subject is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Exam Type</mat-label>
              <mat-select formControlName="examType">
                <mat-option *ngFor="let type of examTypes" [value]="type.value">
                  {{ type.label }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="filterForm.get('examType')?.hasError('required')">
                Exam type is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Academic Year</mat-label>
              <input matInput type="number" formControlName="academicYear">
              <mat-error *ngIf="filterForm.get('academicYear')?.hasError('required')">
                Academic year is required
              </mat-error>
            </mat-form-field>

            <div class="filter-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="filterForm.invalid">
                View Results
              </button>
              <button mat-raised-button color="accent" type="button" (click)="openRecordResultsDialog()" [disabled]="filterForm.invalid">
                Record Results
              </button>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>

    <!-- Loading Indicator -->
    <div *ngIf="loading.results" class="results-loading">
      <mat-progress-bar mode="indeterminate"></mat-progress-bar>
    </div>

    <!-- Error Message -->
    <div *ngIf="error.results" class="results-error">
      <mat-error>
        <mat-icon>error</mat-icon>
        <span>Failed to load results data. Please try again.</span>
        <button mat-button color="warn" (click)="loadResults()">Retry</button>
      </mat-error>
    </div>

    <!-- Results Table -->
    <div *ngIf="!loading.results && !error.results && resultsData.length > 0" class="results-table-container mat-elevation-z8">
      <table mat-table [dataSource]="resultsData" class="results-table">
        <!-- Roll Number Column -->
        <ng-container matColumnDef="rollNumber">
          <th mat-header-cell *matHeaderCellDef>Roll No.</th>
          <td mat-cell *matCellDef="let result">{{ result.student.rollNumber }}</td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Student Name</th>
          <td mat-cell *matCellDef="let result">{{ result.student.firstName }} {{ result.student.lastName }}</td>
        </ng-container>

        <!-- Marks Obtained Column -->
        <ng-container matColumnDef="marksObtained">
          <th mat-header-cell *matHeaderCellDef>Marks Obtained</th>
          <td mat-cell *matCellDef="let result">{{ result.marksObtained }}</td>
        </ng-container>

        <!-- Total Marks Column -->
        <ng-container matColumnDef="totalMarks">
          <th mat-header-cell *matHeaderCellDef>Total Marks</th>
          <td mat-cell *matCellDef="let result">{{ result.totalMarks }}</td>
        </ng-container>

        <!-- Percentage Column -->
        <ng-container matColumnDef="percentage">
          <th mat-header-cell *matHeaderCellDef>Percentage</th>
          <td mat-cell *matCellDef="let result">{{ (result.marksObtained / result.totalMarks * 100).toFixed(2) }}%</td>
        </ng-container>

        <!-- Grade Column -->
        <ng-container matColumnDef="grade">
          <th mat-header-cell *matHeaderCellDef>Grade</th>
          <td mat-cell *matCellDef="let result">
            <span class="grade-badge" [style.background-color]="getGradeColor(result.letterGrade)">
              {{ result.letterGrade }}
            </span>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>

    <!-- No Data Message -->
    <div *ngIf="!loading.results && !error.results && resultsData.length === 0 && filterForm.valid && filterForm.touched" class="no-data">
      <mat-card>
        <mat-card-content>
          <p>No results found for the selected criteria.</p>
          <p>Click "Record Results" to create new records.</p>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
