import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { TenantManagementService } from '../../../services/tenant-management.service';

export interface CreateTenantRequest {
  name: string;
  slug: string;
  displayName: string;
  type: string;
  customDomain?: string;
  defaultLanguage: string;
  timeZone: string;
  currency: string;
  isTrialActive: boolean;
  trialEndDate?: string;
  adminUser: {
    firstName: string;
    lastName: string;
    email: string;
    username: string;
    password: string;
  };
}

@Component({
  selector: 'app-tenant-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    TranslateModule
  ],
  templateUrl: './tenant-form.component.html',
  styleUrls: ['./tenant-form.component.scss']
})
export class TenantFormComponent implements OnInit {
  tenantForm: FormGroup;
  adminForm: FormGroup;
  loading = false;
  isEditMode = false;
  tenantId?: string;

  organizationTypes = [
    { value: 'School', label: 'School' },
    { value: 'University', label: 'University' },
    { value: 'Institute', label: 'Institute' },
    { value: 'Academy', label: 'Academy' }
  ];

  languages = [
    { value: 'en-US', label: 'English (US)' },
    { value: 'en-GB', label: 'English (UK)' },
    { value: 'bn-BD', label: 'Bengali (Bangladesh)' },
    { value: 'hi-IN', label: 'Hindi (India)' },
    { value: 'es-ES', label: 'Spanish' },
    { value: 'fr-FR', label: 'French' }
  ];

  timeZones = [
    { value: 'Asia/Dhaka', label: 'Asia/Dhaka (GMT+6)' },
    { value: 'Asia/Kolkata', label: 'Asia/Kolkata (GMT+5:30)' },
    { value: 'UTC', label: 'UTC (GMT+0)' },
    { value: 'America/New_York', label: 'America/New_York (GMT-5)' },
    { value: 'Europe/London', label: 'Europe/London (GMT+0)' }
  ];

  currencies = [
    { value: 'USD', label: 'US Dollar (USD)' },
    { value: 'BDT', label: 'Bangladeshi Taka (BDT)' },
    { value: 'INR', label: 'Indian Rupee (INR)' },
    { value: 'EUR', label: 'Euro (EUR)' },
    { value: 'GBP', label: 'British Pound (GBP)' }
  ];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private tenantService: TenantManagementService,
    private snackBar: MatSnackBar
  ) {
    this.tenantForm = this.createTenantForm();
    this.adminForm = this.createAdminForm();
  }

  ngOnInit() {
    this.tenantId = this.route.snapshot.params['id'];
    this.isEditMode = !!this.tenantId;

    if (this.isEditMode) {
      this.loadTenant();
    }

    // Auto-generate slug from name
    this.tenantForm.get('name')?.valueChanges.subscribe(name => {
      if (name && !this.isEditMode) {
        const slug = this.generateSlug(name);
        this.tenantForm.patchValue({ slug }, { emitEvent: false });
      }
    });
  }

  createTenantForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      slug: ['', [Validators.required, Validators.pattern(/^[a-z0-9-]+$/), Validators.minLength(2), Validators.maxLength(50)]],
      displayName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      type: ['School', Validators.required],
      customDomain: ['', [Validators.pattern(/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/)]],
      defaultLanguage: ['en-US', Validators.required],
      timeZone: ['Asia/Dhaka', Validators.required],
      currency: ['USD', Validators.required],
      isTrialActive: [true],
      trialEndDate: [new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)] // 30 days from now
    });
  }

  createAdminForm(): FormGroup {
    return this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      lastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      email: ['', [Validators.required, Validators.email]],
      username: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(50), Validators.pattern(/^[a-zA-Z0-9_]+$/)]],
      password: ['', [Validators.required, Validators.minLength(8)]]
    });
  }

  loadTenant() {
    if (!this.tenantId) return;

    this.loading = true;
    this.tenantService.getTenantById(this.tenantId).subscribe({
      next: (tenant) => {
        this.tenantForm.patchValue(tenant);
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading tenant:', error);
        this.snackBar.open('Error loading tenant', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  onSubmit() {
    if (this.tenantForm.valid && (!this.isEditMode ? this.adminForm.valid : true)) {
      this.loading = true;

      const formValue = this.tenantForm.value;

      // Convert date to ISO string if it's a Date object
      let trialEndDate = formValue.trialEndDate;
      if (trialEndDate instanceof Date) {
        trialEndDate = trialEndDate.toISOString();
      }

      const tenantData: CreateTenantRequest = {
        ...formValue,
        trialEndDate: trialEndDate,
        adminUser: this.isEditMode ? undefined : this.adminForm.value
      };

      const operation = this.isEditMode
        ? this.tenantService.updateTenant(this.tenantId!, tenantData)
        : this.tenantService.createTenant(tenantData);

      operation.subscribe({
        next: (result) => {
          this.snackBar.open(
            `Tenant ${this.isEditMode ? 'updated' : 'created'} successfully`,
            'Close',
            { duration: 3000 }
          );
          this.router.navigate(['/admin/tenants']);
        },
        error: (error) => {
          console.error('Error saving tenant:', error);
          this.snackBar.open(
            `Error ${this.isEditMode ? 'updating' : 'creating'} tenant`,
            'Close',
            { duration: 3000 }
          );
          this.loading = false;
        }
      });
    } else {
      this.markFormGroupTouched(this.tenantForm);
      if (!this.isEditMode) {
        this.markFormGroupTouched(this.adminForm);
      }
    }
  }

  onCancel() {
    this.router.navigate(['/admin/tenants']);
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  get isTrialActive() {
    return this.tenantForm.get('isTrialActive')?.value;
  }
}
