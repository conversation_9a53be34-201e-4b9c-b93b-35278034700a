using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class User : BaseEntity
{
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PasswordHash { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public UserRole Role { get; set; } = UserRole.User;
    public DateTime? LastLogin { get; set; }
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public ICollection<Content> CreatedContents { get; set; } = new List<Content>();
    public ICollection<Content> ModifiedContents { get; set; } = new List<Content>();
    public ICollection<MediaItem> UploadedMedia { get; set; } = new List<MediaItem>();
    public ICollection<Notice> CreatedNotices { get; set; } = new List<Notice>();
}
