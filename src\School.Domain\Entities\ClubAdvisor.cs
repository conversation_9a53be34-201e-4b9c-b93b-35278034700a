using School.Domain.Common;

using System;

namespace School.Domain.Entities
{
    public class ClubAdvisor : BaseEntity
    {
        public Guid ClubId { get; set; }
        public Guid? FacultyId { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public int DisplayOrder { get; set; }
        
        // Navigation properties
        public Club Club { get; set; }
        public Faculty Faculty { get; set; }
    }
}
