import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatNativeDateModule } from '@angular/material/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';
import { MatDialogModule } from '@angular/material/dialog';

import { FacultyPortalComponent } from './faculty-portal.component';
import { FacultyDashboardComponent } from './faculty-dashboard/faculty-dashboard.component';
import { FacultyProfileComponent } from './faculty-profile/faculty-profile.component';
import { FacultyScheduleComponent } from './faculty-schedule/faculty-schedule.component';
import { FacultyClassesComponent } from './faculty-classes/faculty-classes.component';
import { FacultyAttendanceComponent } from './faculty-attendance/faculty-attendance.component';
import { FacultyResultsComponent } from './faculty-results/faculty-results.component';
import { FacultyLeavesComponent } from './faculty-leaves/faculty-leaves.component';
import { RecordAttendanceDialogComponent } from './faculty-attendance/record-attendance-dialog/record-attendance-dialog.component';
import { RecordResultsDialogComponent } from './faculty-results/record-results-dialog/record-results-dialog.component';

const routes: Routes = [
  {
    path: '',
    component: FacultyPortalComponent,
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: FacultyDashboardComponent },
      { path: 'profile', component: FacultyProfileComponent },
      { path: 'schedule', component: FacultyScheduleComponent },
      { path: 'classes', component: FacultyClassesComponent },
      { path: 'attendance', component: FacultyAttendanceComponent },
      { path: 'results', component: FacultyResultsComponent },
      { path: 'leaves', component: FacultyLeavesComponent }
    ]
  }
];

@NgModule({
  declarations: [
    // All components are now standalone
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatListModule,
    MatMenuModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatSidenavModule,
    MatSnackBarModule,
    MatSortModule,
    MatTableModule,
    MatTabsModule,
    MatToolbarModule,
    MatTooltipModule,
    MatNativeDateModule,
    MatExpansionModule,
    MatChipsModule,
    MatBadgeModule,
    MatDialogModule,

    // Import standalone components
    FacultyPortalComponent,
    FacultyDashboardComponent,
    FacultyProfileComponent,
    FacultyScheduleComponent,
    FacultyClassesComponent,
    FacultyAttendanceComponent,
    FacultyResultsComponent,
    FacultyLeavesComponent,
    RecordAttendanceDialogComponent,
    RecordResultsDialogComponent
  ]
})
export class FacultyPortalModule { }
