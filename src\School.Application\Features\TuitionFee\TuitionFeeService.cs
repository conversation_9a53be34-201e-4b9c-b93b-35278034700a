using School.Application.DTOs;
using School.Domain.Enums;

namespace School.Application.Features.TuitionFee;

public interface ITuitionFeeService
{
    Task<List<TuitionFeeDto>> GetAllTuitionFeesAsync(EducationLevel? level, FeeType? type, bool? isActive);
    Task<TuitionFeeDto?> GetTuitionFeeByIdAsync(Guid id);
    Task<TuitionFeeDto> CreateTuitionFeeAsync(CreateTuitionFeeDto createDto);
    Task<bool> UpdateTuitionFeeAsync(Guid id, UpdateTuitionFeeDto updateDto);
    Task<bool> DeleteTuitionFeeAsync(Guid id);
}