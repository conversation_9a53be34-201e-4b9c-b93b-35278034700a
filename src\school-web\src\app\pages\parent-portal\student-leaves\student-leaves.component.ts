import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { ParentService } from '../../../core/services/parent.service';
import { AuthService } from '../../../core/services/auth.service';
import { StudentLeave, LeaveStatus, LeaveType } from '../../../core/models/student.model';
import { LeaveApplicationDialogComponent } from './leave-application-dialog/leave-application-dialog.component';

@Component({
  selector: 'app-student-leaves',
  templateUrl: './student-leaves.component.html',
  styleUrls: ['./student-leaves.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    DatePipe,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatDialogModule,
    MatSnackBarModule
  ]
})
export class StudentLeavesComponent implements OnInit {
  studentId: number = 0;
  leaveRecords: StudentLeave[] = [];
  filterForm: FormGroup;

  loading = {
    leaves: true
  };

  error = {
    leaves: false
  };

  leaveTypes = [
    { value: LeaveType.Sick, label: 'Sick' },
    { value: LeaveType.Personal, label: 'Personal' },
    { value: LeaveType.Family, label: 'Family' },
    { value: LeaveType.Religious, label: 'Religious' },
    { value: LeaveType.Other, label: 'Other' }
  ];

  leaveStatuses = [
    { value: LeaveStatus.Pending, label: 'Pending' },
    { value: LeaveStatus.Approved, label: 'Approved' },
    { value: LeaveStatus.Rejected, label: 'Rejected' },
    { value: LeaveStatus.Cancelled, label: 'Cancelled' }
  ];

  constructor(
    private route: ActivatedRoute,
    private parentService: ParentService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    this.filterForm = this.formBuilder.group({
      status: [null]
    });
  }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.studentId = +params['id'];
      this.loadLeaves();
    });
  }

  loadLeaves(): void {
    if (!this.studentId) return;

    this.loading.leaves = true;
    this.error.leaves = false;

    const parentId = 1; // In a real app, get from auth service
    const status = this.filterForm.value.status;

    this.parentService.getStudentLeavesForParent(
      parentId,
      this.studentId,
      status
    ).subscribe({
      next: (records) => {
        this.leaveRecords = records;
        this.loading.leaves = false;
      },
      error: (err) => {
        console.error('Error loading leave records:', err);
        this.error.leaves = true;
        this.loading.leaves = false;
        this.snackBar.open('Failed to load leave records', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  openLeaveApplicationDialog(): void {
    const dialogRef = this.dialog.open(LeaveApplicationDialogComponent, {
      width: '600px',
      data: {
        studentId: this.studentId
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadLeaves();
        this.snackBar.open('Leave application submitted successfully', 'Close', {
          duration: 3000
        });
      }
    });
  }

  cancelLeave(leaveId: number): void {
    if (!this.studentId) return;

    // In a real app, this would call an API to cancel the leave
    // For now, we'll just reload the leaves to simulate a successful cancellation
    this.loadLeaves();
    this.snackBar.open('Leave application cancelled successfully', 'Close', {
      duration: 3000
    });
  }

  getLeaveTypeLabel(type: number): string {
    return this.leaveTypes.find(t => t.value === type)?.label || 'Unknown';
  }

  getStatusLabel(status: number): string {
    return this.leaveStatuses.find(s => s.value === status)?.label || 'Unknown';
  }

  getStatusClass(status: number): string {
    switch (status) {
      case LeaveStatus.Approved: return 'approved';
      case LeaveStatus.Pending: return 'pending';
      case LeaveStatus.Rejected: return 'rejected';
      case LeaveStatus.Cancelled: return 'cancelled';
      default: return '';
    }
  }

  calculateLeaveDays(startDate: Date, endDate: Date): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  }
}
