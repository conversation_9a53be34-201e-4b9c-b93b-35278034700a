import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>rray, FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

// Services and models
import { FacultyService } from '../../../../core/services/faculty.service';
import { ExamType } from '../../../../core/models/student.model';

@Component({
  selector: 'app-record-results-dialog',
  templateUrl: './record-results-dialog.component.html',
  styleUrls: ['./record-results-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatProgressSpinnerModule
  ]
})
export class RecordResultsDialogComponent implements OnInit {
  resultsForm: FormGroup;
  loading = false;
  totalMarks = 100; // Default total marks

  constructor(
    private formBuilder: FormBuilder,
    private facultyService: FacultyService,
    public dialogRef: MatDialogRef<RecordResultsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: {
      facultyId: number;
      grade: number;
      section: string;
      subjectCode: string;
      subjectName: string;
      examType: ExamType;
      academicYear: number;
      resultsData: any[];
    }
  ) {
    this.resultsForm = this.formBuilder.group({
      totalMarks: [this.totalMarks, [Validators.required, Validators.min(1)]],
      students: this.formBuilder.array([])
    });
  }

  ngOnInit(): void {
    this.loadStudents();
  }

  get students(): FormArray {
    return this.resultsForm.get('students') as FormArray;
  }

  loadStudents(): void {
    this.loading = true;

    this.facultyService.getFacultyStudents(
      this.data.facultyId,
      this.data.grade,
      this.data.section
    ).subscribe({
      next: (students) => {
        // Clear existing form array
        while (this.students.length) {
          this.students.removeAt(0);
        }

        // Add students to form array
        students.forEach(student => {
          // Check if there's existing results data for this student
          const existingData = this.data.resultsData?.find(r => r.student.id === student.id);

          this.students.push(this.formBuilder.group({
            studentId: [student.id, Validators.required],
            rollNumber: [student.rollNumber],
            firstName: [student.firstName],
            lastName: [student.lastName],
            marksObtained: [existingData?.marksObtained ?? '', [Validators.required, Validators.min(0), Validators.max(this.totalMarks)]],
            remarks: [existingData?.remarks ?? '']
          }));
        });

        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading students:', err);
        this.loading = false;
        this.dialogRef.close();
      }
    });
  }

  onSubmit(): void {
    if (this.resultsForm.invalid) {
      return;
    }

    this.loading = true;

    const formValues = this.resultsForm.value;
    const totalMarks = formValues.totalMarks;

    const resultRecords = this.students.value.map((student: any) => ({
      studentId: student.studentId,
      marksObtained: student.marksObtained,
      totalMarks: totalMarks,
      remarks: student.remarks
    }));

    this.facultyService.recordClassResults(
      this.data.facultyId,
      this.data.grade,
      this.data.section,
      this.data.subjectCode,
      this.data.examType,
      this.data.academicYear,
      resultRecords
    ).subscribe({
      next: () => {
        this.loading = false;
        this.dialogRef.close(true);
      },
      error: (err) => {
        console.error('Error recording results:', err);
        this.loading = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  updateTotalMarks(): void {
    const totalMarks = this.resultsForm.get('totalMarks')?.value;

    // Update validators for all marksObtained fields
    this.students.controls.forEach(control => {
      control.get('marksObtained')?.setValidators([
        Validators.required,
        Validators.min(0),
        Validators.max(totalMarks)
      ]);
      control.get('marksObtained')?.updateValueAndValidity();
    });
  }

  getPercentage(marksObtained: number): number {
    const totalMarks = this.resultsForm.get('totalMarks')?.value;
    if (!totalMarks) return 0;
    return (marksObtained / totalMarks) * 100;
  }

  getGrade(marksObtained: number): string {
    const percentage = this.getPercentage(marksObtained);

    if (percentage >= 80) return 'A+';
    if (percentage >= 70) return 'A';
    if (percentage >= 65) return 'A-';
    if (percentage >= 60) return 'B+';
    if (percentage >= 55) return 'B';
    if (percentage >= 50) return 'C+';
    if (percentage >= 45) return 'C';
    if (percentage >= 40) return 'D';
    return 'F';
  }

  getGradeColor(grade: string): string {
    switch (grade) {
      case 'A+': return '#2e7d32';
      case 'A': return '#388e3c';
      case 'A-': return '#43a047';
      case 'B+': return '#689f38';
      case 'B': return '#7cb342';
      case 'C+': return '#ffa000';
      case 'C': return '#ffb300';
      case 'D': return '#f57c00';
      case 'F': return '#d32f2f';
      default: return '#757575';
    }
  }
}
