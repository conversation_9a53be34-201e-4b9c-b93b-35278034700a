using School.Domain.Common;

namespace School.Domain.Entities;

public class FacultyEducationTranslation : BaseEntity
{
    public Guid FacultyEducationId { get; set; }
    public FacultyEducation? FacultyEducation { get; set; }
    
    public string LanguageCode { get; set; } = string.Empty;
    public string Degree { get; set; } = string.Empty;
    public string Institution { get; set; } = string.Empty;
}
