using School.Domain.Enums;

namespace School.Application.DTOs;

public class TermDto
{
    public Guid Id { get; set; }
    public Guid AcademicYearId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public TermType Type { get; set; }
    public TermStatus Status { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int OrderIndex { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public int TotalWorkingDays { get; set; }
    public int TotalHolidays { get; set; }
    public DateTime? ExamStartDate { get; set; }
    public DateTime? ExamEndDate { get; set; }
    public DateTime? ResultPublishDate { get; set; }
    public DateTime? RegistrationDeadline { get; set; }
    public DateTime? FeePaymentDeadline { get; set; }
    public decimal? PassingGrade { get; set; }
    public decimal? MaximumGrade { get; set; }
    public string GradingScale { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public string AcademicYearName { get; set; } = string.Empty;
    public List<TermTranslationDto> Translations { get; set; } = new List<TermTranslationDto>();
}

public class CreateTermDto
{
    public Guid AcademicYearId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public TermType Type { get; set; } = TermType.Semester;
    public TermStatus Status { get; set; } = TermStatus.Planned;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int OrderIndex { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public int TotalWorkingDays { get; set; }
    public int TotalHolidays { get; set; }
    public DateTime? ExamStartDate { get; set; }
    public DateTime? ExamEndDate { get; set; }
    public DateTime? ResultPublishDate { get; set; }
    public DateTime? RegistrationDeadline { get; set; }
    public DateTime? FeePaymentDeadline { get; set; }
    public decimal? PassingGrade { get; set; }
    public decimal? MaximumGrade { get; set; }
    public string GradingScale { get; set; } = string.Empty;
    public List<CreateTermTranslationDto>? Translations { get; set; }
}

public class UpdateTermDto
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public TermType Type { get; set; }
    public TermStatus Status { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int OrderIndex { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public int TotalWorkingDays { get; set; }
    public int TotalHolidays { get; set; }
    public DateTime? ExamStartDate { get; set; }
    public DateTime? ExamEndDate { get; set; }
    public DateTime? ResultPublishDate { get; set; }
    public DateTime? RegistrationDeadline { get; set; }
    public DateTime? FeePaymentDeadline { get; set; }
    public decimal? PassingGrade { get; set; }
    public decimal? MaximumGrade { get; set; }
    public string GradingScale { get; set; } = string.Empty;
}

public class TermFilterDto
{
    public Guid? AcademicYearId { get; set; }
    public string? Name { get; set; }
    public string? Code { get; set; }
    public TermType? Type { get; set; }
    public TermStatus? Status { get; set; }
    public DateTime? StartDateFrom { get; set; }
    public DateTime? StartDateTo { get; set; }
    public DateTime? EndDateFrom { get; set; }
    public DateTime? EndDateTo { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = false;
}

public class TermTranslationDto
{
    public Guid Id { get; set; }
    public Guid TermId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

public class CreateTermTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

public class UpdateTermTranslationDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}
