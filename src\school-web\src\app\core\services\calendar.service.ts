import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Calendar Models
export interface CalendarEvent {
  id: string;
  title: string;
  description: string;
  startDate: Date;
  endDate?: Date;
  isAllDay: boolean;
  location: string;
  color: string;
  eventType: CalendarEventType;
  eventTypeName: string;
  isActive: boolean;
  isPublic: boolean;
  academicYearId?: string;
  termId?: string;
  academicYearName: string;
  termName: string;
  isRecurring: boolean;
  source: string; // "AcademicCalendar" or "Holiday"
}

export interface CalendarStatistics {
  totalEvents: number;
  academicEvents: number;
  holidays: number;
  examEvents: number;
  admissionEvents: number;
  culturalEvents: number;
  sportsEvents: number;
  meetingEvents: number;
  otherEvents: number;
  publicEvents: number;
  privateEvents: number;
  recurringEvents: number;
  oneTimeEvents: number;
  currentMonthEvents: number;
  upcomingEvents: number;
  eventsByMonth: { [key: string]: number };
  eventsByType: { [key: string]: number };
}

export enum CalendarEventType {
  AcademicEvent = 0,
  Holiday = 1,
  Exam = 2,
  Admission = 3,
  Cultural = 4,
  Sports = 5,
  Meeting = 6,
  Other = 7
}

export interface ValidateEventDatesRequest {
  academicYearId?: string;
  termId?: string;
  startDate: Date;
  endDate: Date;
  excludeId?: string;
}

export interface CalendarFilter {
  startDate: Date;
  endDate: Date;
  academicYearId?: string;
  termId?: string;
  eventTypes?: CalendarEventType[];
  showHolidays?: boolean;
  showAcademicEvents?: boolean;
  showPublicOnly?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CalendarService {
  private readonly apiUrl = `${environment.apiUrl}/calendar`;
  
  // State management
  private currentFilterSubject = new BehaviorSubject<CalendarFilter | null>(null);
  public currentFilter$ = this.currentFilterSubject.asObservable();
  
  private eventsSubject = new BehaviorSubject<CalendarEvent[]>([]);
  public events$ = this.eventsSubject.asObservable();
  
  private statisticsSubject = new BehaviorSubject<CalendarStatistics | null>(null);
  public statistics$ = this.statisticsSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Enhanced Calendar Events
  getIntegratedCalendarEvents(filter: CalendarFilter): Observable<CalendarEvent[]> {
    let params = new HttpParams()
      .set('startDate', filter.startDate.toISOString())
      .set('endDate', filter.endDate.toISOString());

    if (filter.academicYearId) {
      params = params.set('academicYearId', filter.academicYearId);
    }
    if (filter.termId) {
      params = params.set('termId', filter.termId);
    }

    return this.http.get<CalendarEvent[]>(`${this.apiUrl}/events`, { params })
      .pipe(
        map(events => this.processEvents(events, filter)),
        catchError(this.handleError<CalendarEvent[]>('getIntegratedCalendarEvents', []))
      );
  }

  getAcademicYearCalendar(academicYearId: string): Observable<CalendarEvent[]> {
    return this.http.get<CalendarEvent[]>(`${this.apiUrl}/events/academic-year/${academicYearId}`)
      .pipe(
        map(events => this.processEvents(events)),
        catchError(this.handleError<CalendarEvent[]>('getAcademicYearCalendar', []))
      );
  }

  getTermCalendar(termId: string): Observable<CalendarEvent[]> {
    return this.http.get<CalendarEvent[]>(`${this.apiUrl}/events/term/${termId}`)
      .pipe(
        map(events => this.processEvents(events)),
        catchError(this.handleError<CalendarEvent[]>('getTermCalendar', []))
      );
  }

  getCurrentAcademicYearCalendar(): Observable<CalendarEvent[]> {
    return this.http.get<CalendarEvent[]>(`${this.apiUrl}/events/current`)
      .pipe(
        map(events => this.processEvents(events)),
        catchError(this.handleError<CalendarEvent[]>('getCurrentAcademicYearCalendar', []))
      );
  }

  // Calendar Statistics
  getCalendarStatistics(academicYearId?: string, termId?: string): Observable<CalendarStatistics> {
    let params = new HttpParams();
    if (academicYearId) {
      params = params.set('academicYearId', academicYearId);
    }
    if (termId) {
      params = params.set('termId', termId);
    }

    return this.http.get<CalendarStatistics>(`${this.apiUrl}/statistics`, { params })
      .pipe(
        catchError(this.handleError<CalendarStatistics>('getCalendarStatistics', {} as CalendarStatistics))
      );
  }

  getTotalEventsInPeriod(startDate: Date, endDate: Date, academicYearId?: string, termId?: string): Observable<number> {
    let params = new HttpParams()
      .set('startDate', startDate.toISOString())
      .set('endDate', endDate.toISOString());

    if (academicYearId) {
      params = params.set('academicYearId', academicYearId);
    }
    if (termId) {
      params = params.set('termId', termId);
    }

    return this.http.get<number>(`${this.apiUrl}/statistics/count`, { params })
      .pipe(
        catchError(this.handleError<number>('getTotalEventsInPeriod', 0))
      );
  }

  getEventTypeDistribution(academicYearId?: string, termId?: string): Observable<{ [key: string]: number }> {
    let params = new HttpParams();
    if (academicYearId) {
      params = params.set('academicYearId', academicYearId);
    }
    if (termId) {
      params = params.set('termId', termId);
    }

    return this.http.get<{ [key: string]: number }>(`${this.apiUrl}/statistics/distribution`, { params })
      .pipe(
        catchError(this.handleError<{ [key: string]: number }>('getEventTypeDistribution', {}))
      );
  }

  // Calendar Validation
  validateEventDates(request: ValidateEventDatesRequest): Observable<boolean> {
    return this.http.post<boolean>(`${this.apiUrl}/validate`, request)
      .pipe(
        catchError(this.handleError<boolean>('validateEventDates', false))
      );
  }

  getOverlappingEvents(
    startDate: Date, 
    endDate: Date, 
    academicYearId?: string, 
    termId?: string, 
    excludeId?: string
  ): Observable<any[]> {
    let params = new HttpParams()
      .set('startDate', startDate.toISOString())
      .set('endDate', endDate.toISOString());

    if (academicYearId) {
      params = params.set('academicYearId', academicYearId);
    }
    if (termId) {
      params = params.set('termId', termId);
    }
    if (excludeId) {
      params = params.set('excludeId', excludeId);
    }

    return this.http.get<any[]>(`${this.apiUrl}/overlapping`, { params })
      .pipe(
        catchError(this.handleError<any[]>('getOverlappingEvents', []))
      );
  }

  // State Management Methods
  setCurrentFilter(filter: CalendarFilter): void {
    this.currentFilterSubject.next(filter);
  }

  getCurrentFilter(): CalendarFilter | null {
    return this.currentFilterSubject.value;
  }

  updateEvents(events: CalendarEvent[]): void {
    this.eventsSubject.next(events);
  }

  updateStatistics(statistics: CalendarStatistics): void {
    this.statisticsSubject.next(statistics);
  }

  // Utility Methods
  private processEvents(events: CalendarEvent[], filter?: CalendarFilter): CalendarEvent[] {
    let processedEvents = events.map(event => ({
      ...event,
      startDate: new Date(event.startDate),
      endDate: event.endDate ? new Date(event.endDate) : undefined
    }));

    if (filter) {
      // Apply client-side filtering
      if (filter.eventTypes && filter.eventTypes.length > 0) {
        processedEvents = processedEvents.filter(event => 
          filter.eventTypes!.includes(event.eventType)
        );
      }

      if (filter.showHolidays === false) {
        processedEvents = processedEvents.filter(event => event.source !== 'Holiday');
      }

      if (filter.showAcademicEvents === false) {
        processedEvents = processedEvents.filter(event => event.source !== 'AcademicCalendar');
      }

      if (filter.showPublicOnly === true) {
        processedEvents = processedEvents.filter(event => event.isPublic);
      }
    }

    return processedEvents;
  }

  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(`${operation} failed:`, error);
      return new Observable<T>(observer => {
        observer.next(result as T);
        observer.complete();
      });
    };
  }

  // Helper methods for calendar display
  getEventTypeColor(eventType: CalendarEventType): string {
    const colorMap = {
      [CalendarEventType.AcademicEvent]: '#2196F3',
      [CalendarEventType.Holiday]: '#F44336',
      [CalendarEventType.Exam]: '#FF9800',
      [CalendarEventType.Admission]: '#9C27B0',
      [CalendarEventType.Cultural]: '#E91E63',
      [CalendarEventType.Sports]: '#4CAF50',
      [CalendarEventType.Meeting]: '#607D8B',
      [CalendarEventType.Other]: '#795548'
    };
    return colorMap[eventType] || '#9E9E9E';
  }

  getEventTypeIcon(eventType: CalendarEventType): string {
    const iconMap = {
      [CalendarEventType.AcademicEvent]: 'school',
      [CalendarEventType.Holiday]: 'celebration',
      [CalendarEventType.Exam]: 'quiz',
      [CalendarEventType.Admission]: 'person_add',
      [CalendarEventType.Cultural]: 'theater_comedy',
      [CalendarEventType.Sports]: 'sports',
      [CalendarEventType.Meeting]: 'meeting_room',
      [CalendarEventType.Other]: 'event'
    };
    return iconMap[eventType] || 'event';
  }

  formatEventDuration(event: CalendarEvent): string {
    if (event.isAllDay) {
      return 'All Day';
    }
    
    const start = event.startDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    if (event.endDate) {
      const end = event.endDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      return `${start} - ${end}`;
    }
    
    return start;
  }
}
