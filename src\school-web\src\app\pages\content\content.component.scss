.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.loading-container,
.error-container,
.not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  text-align: center;
}

.error-message {
  color: #f44336;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.content-wrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-header {
  padding: 2rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.content-title {
  margin: 0 0 1rem;
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
}

.content-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.content-date {
  color: #757575;
  font-size: 0.9rem;
}

.content-body {
  padding: 2rem;
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333;
  
  h2 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-size: 1.8rem;
    font-weight: 600;
  }
  
  h3 {
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    font-size: 1.5rem;
    font-weight: 600;
  }
  
  p {
    margin-bottom: 1.5rem;
  }
  
  ul, ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
    
    li {
      margin-bottom: 0.5rem;
    }
  }
  
  img {
    max-width: 100%;
    height: auto;
    margin: 1.5rem 0;
    border-radius: 4px;
  }
  
  a {
    color: #3f51b5;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  blockquote {
    margin: 1.5rem 0;
    padding: 1rem 1.5rem;
    border-left: 4px solid #3f51b5;
    background-color: #f5f5f5;
    font-style: italic;
  }
  
  table {
    width: 100%;
    margin: 1.5rem 0;
    border-collapse: collapse;
    
    th, td {
      padding: 0.75rem;
      border: 1px solid #e0e0e0;
    }
    
    th {
      background-color: #f5f5f5;
      font-weight: 600;
    }
    
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }
}

.content-media {
  padding: 0 2rem 2rem;
  
  h3 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.media-item {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  img {
    width: 100%;
    height: 150px;
    object-fit: cover;
  }
  
  .media-icon {
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    
    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #757575;
    }
  }
  
  .media-caption {
    padding: 0.5rem;
    font-size: 0.9rem;
    color: #757575;
    text-align: center;
  }
}

.not-found {
  h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #333;
  }
  
  p {
    margin-bottom: 2rem;
    color: #757575;
    font-size: 1.1rem;
  }
}

@media (max-width: 768px) {
  .content-title {
    font-size: 2rem;
  }
  
  .content-body {
    font-size: 1rem;
  }
  
  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
