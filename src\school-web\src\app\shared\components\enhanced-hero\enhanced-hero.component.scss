@use 'sass:color';

// Variables
$primary-color: #3f51b5;
$primary-light: #757de8;
$primary-dark: #002984;
$accent-color: #ff4081;
$light-text: #ffffff;
$dark-text: #212121;
$pattern-color: rgba(255, 255, 255, 0.05);

// Enhanced Hero Base Styles
.enhanced-hero {
  position: relative;
  width: 100%;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-color: $primary-color; // Default background if no image
  color: $light-text;
  display: flex;
  align-items: center;
  justify-content: center;

  // Fixed height of 40% viewport height
  height: 40vh;
  min-height: 250px; // Minimum height for very small screens
  max-height: 500px; // Maximum height to prevent excessive space on large screens
  padding: 20px 0;

  // Size variations only affect padding and content scaling
  &.hero-small {
    padding: 15px 0;
  }

  &.hero-medium {
    padding: 20px 0;
  }

  &.hero-large {
    padding: 25px 0;
  }

  // Alignment variations
  &.align-left {
    .hero-container {
      align-items: flex-start;
    }

    .hero-content {
      text-align: left;
      margin-right: auto;

      .hero-title::after {
        margin-left: 0;
        margin-right: auto;
      }
    }
  }

  &.align-center {
    .hero-container {
      align-items: center;
    }

    .hero-content {
      text-align: center;
      margin-left: auto;
      margin-right: auto;

      .hero-title::after {
        margin-left: auto;
        margin-right: auto;
      }
    }
  }

  &.align-right {
    .hero-container {
      align-items: flex-end;
    }

    .hero-content {
      text-align: right;
      margin-left: auto;

      .hero-title::after {
        margin-left: auto;
        margin-right: 0;
      }
    }
  }

  // Theme variations
  &.theme-light {
    color: $dark-text;

    .hero-overlay {
      background-color: rgba(255, 255, 255, 0.85);
    }

    .hero-title, .hero-subtitle {
      color: $primary-dark;
    }

    .breadcrumbs {
      color: $primary-dark;

      a {
        color: $primary-color;
      }
    }
  }

  &.theme-dark {
    color: $light-text;

    .hero-overlay {
      background-color: rgba(0, 0, 0, 0.65);
    }

    .hero-title, .hero-subtitle {
      color: $light-text;
    }

    .breadcrumbs {
      color: rgba(255, 255, 255, 0.7);

      a {
        color: $light-text;
      }
    }
  }
}

// Overlay with pattern
.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(45deg, $pattern-color 25%, transparent 25%, transparent 75%, $pattern-color 75%, $pattern-color),
    linear-gradient(45deg, $pattern-color 25%, transparent 25%, transparent 75%, $pattern-color 75%, $pattern-color);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
  z-index: 1;
}

// Decorative elements
.hero-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  overflow: hidden;

  .circle-decoration {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);

    &.circle-1 {
      width: 300px;
      height: 300px;
      top: -100px;
      right: 10%;
    }

    &.circle-2 {
      width: 200px;
      height: 200px;
      bottom: -50px;
      left: 5%;
    }

    &.circle-3 {
      width: 150px;
      height: 150px;
      top: 30%;
      left: 20%;
    }
  }
}

// Overlay image (school emblem/mascot)
.overlay-image {
  position: absolute;
  right: 5%;
  bottom: 0;
  z-index: 3;
  height: 80%;
  display: flex;
  align-items: flex-end;

  .emblem {
    max-height: 100%;
    max-width: 250px;
    object-fit: contain;
  }
}

// Content container
.hero-container {
  position: relative;
  z-index: 4;
  width: 100%;
  max-width: 1200px;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  height: 100%;
  box-sizing: border-box;
}

// Breadcrumbs
.breadcrumbs {
  margin-bottom: 20px;
  font-size: 0.9rem;

  span {
    display: inline-flex;
    align-items: center;

    a {
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        text-decoration: underline;
      }
    }

    mat-icon {
      font-size: 18px;
      height: 18px;
      width: 18px;
      margin: 0 5px;
    }
  }
}

// Hero content
.hero-content {
  max-width: 600px;
  padding: 0;
  margin: auto 0; // This centers content vertically
  width: 100%;

  .hero-title {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.2;
    position: relative;

    &::after {
      content: '';
      display: block;
      width: 80px;
      height: 4px;
      background-color: $accent-color;
      margin-top: 10px;
      margin-bottom: 10px;
    }

    &.animated {
      animation: fadeInUp 0.8s ease-out;
    }

    // Handle long titles
    &.long-text {
      font-size: 2.2rem;
      line-height: 1.1;
    }

    &.very-long-text {
      font-size: 1.8rem;
      line-height: 1.1;
    }
  }

  .hero-subtitle {
    font-size: 1.8rem;
    font-weight: 500;
    margin-bottom: 0.7rem;
    opacity: 0.9;

    &.animated {
      animation: fadeInUp 0.8s ease-out 0.2s both;
    }

    // Handle long subtitles
    &.long-text {
      font-size: 1.4rem;
      line-height: 1.2;
    }

    &.very-long-text {
      font-size: 1.2rem;
      line-height: 1.2;
    }
  }

  .hero-description {
    font-size: 1.2rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    max-width: 650px;

    &.animated {
      animation: fadeInUp 0.8s ease-out 0.4s both;
    }

    // Handle long descriptions
    &.long-text {
      font-size: 1rem;
      line-height: 1.4;
      max-height: 5.6em; // 4 lines of text
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      line-clamp: 4;
      -webkit-box-orient: vertical;
    }

    &.very-long-text {
      font-size: 0.9rem;
      line-height: 1.3;
      max-height: 3.9em; // 3 lines of text
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
    }
  }

  .hero-buttons {
    display: flex;
    gap: 12px;
    margin-top: 15px;

    &.animated {
      animation: fadeInUp 0.8s ease-out 0.6s both;
    }

    a {
      padding: 8px 20px;
      font-weight: 500;
      border-radius: 30px;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      display: inline-flex;
      align-items: center;
      font-size: 1rem;

      mat-icon {
        margin-right: 8px;
        font-size: 20px;
        height: 20px;
        width: 20px;
      }

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }
    }
  }

  .additional-content {
    margin-top: 20px;
  }
}

// Wave shape at bottom
.hero-wave {
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  z-index: 5;
  line-height: 0;

  svg {
    width: 100%;
    height: 50px; // Increased height for taller hero
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive adjustments
@media (max-width: 992px) {
  .enhanced-hero {
    .hero-content {
      .hero-title {
        font-size: 2.4rem;
      }

      .hero-subtitle {
        font-size: 1.6rem;
      }

      .hero-description {
        font-size: 1.1rem;
      }
    }
  }

  .overlay-image {
    opacity: 0.7;

    .emblem {
      max-width: 120px;
    }
  }
}

@media (max-width: 768px) {
  .enhanced-hero {
    padding: 30px 0;

    .hero-container {
      padding: 0 20px;
    }

    .hero-content {
      max-width: 100%;
      padding: 20px 0;

      .hero-title {
        font-size: 2.2rem;

        &::after {
          width: 70px;
          height: 3px;
          margin-top: 12px;
          margin-bottom: 12px;
        }
      }

      .hero-subtitle {
        font-size: 1.4rem;
      }

      .hero-description {
        font-size: 1rem;
        margin-bottom: 1rem;
      }

      .hero-buttons {
        margin-top: 20px;

        a {
          padding: 8px 20px;
          font-size: 1rem;
        }
      }
    }
  }

  .overlay-image {
    right: 0;
    opacity: 0.4;

    .emblem {
      max-width: 100px;
    }
  }

  .hero-buttons {
    flex-wrap: wrap;
  }
}

@media (max-width: 576px) {
  .enhanced-hero {
    padding: 20px 0;

    .hero-container {
      padding: 0 15px;
    }

    .hero-content {
      padding: 15px 0;

      .hero-title {
        font-size: 1.8rem;

        &::after {
          width: 60px;
          height: 3px;
          margin-top: 8px;
          margin-bottom: 8px;
        }
      }

      .hero-subtitle {
        font-size: 1.2rem;
      }

      .hero-description {
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 0.8rem;
      }

      .hero-buttons {
        margin-top: 15px;

        a {
          padding: 7px 18px;
          font-size: 0.9rem;

          mat-icon {
            font-size: 18px;
            height: 18px;
            width: 18px;
            margin-right: 6px;
          }
        }
      }
    }
  }

  .overlay-image {
    display: none;
  }

  .hero-wave svg {
    height: 25px;
  }

  .breadcrumbs {
    display: none; // Hide breadcrumbs on very small screens
  }
}
