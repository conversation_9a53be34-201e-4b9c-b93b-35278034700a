.profile-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.profile-content {
  max-width: 1000px;
  margin: 0 auto;
}

.profile-card {
  margin-bottom: 24px;
}

.profile-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  mat-icon {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #757575;
  }
}

.tab-content {
  padding: 24px 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
}

.parent-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  margin-bottom: 16px;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
}

.parent-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  margin-right: 16px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #757575;
  }
}

.parent-details {
  flex: 1;

  h3 {
    margin: 0 0 4px 0;
    font-weight: 500;
  }

  p {
    margin: 0 0 4px 0;
    color: rgba(0, 0, 0, 0.6);
  }

  .primary-contact {
    color: #3f51b5;
    font-weight: 500;
  }
}

.parent-contact {
  margin-top: 8px;

  p {
    display: flex;
    align-items: center;
    margin-bottom: 4px;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }
}

.no-data {
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
  padding: 24px 0;
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
