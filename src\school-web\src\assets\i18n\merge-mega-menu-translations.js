/**
 * <PERSON><PERSON><PERSON> to merge mega menu translations into the appropriate module files
 * 
 * This script takes the mega-menu-translations.json files (both English and Bengali)
 * and merges their contents into the appropriate module files based on the key structure.
 * 
 * Usage: 
 * 1. Place this file in the i18n directory
 * 2. Run with Node.js: node merge-mega-menu-translations.js
 */

const fs = require('fs');
const path = require('path');

// Define paths
const basePath = __dirname;
const enMegaMenuPath = path.join(basePath, 'mega-menu-translations.json');
const bnMegaMenuPath = path.join(basePath, 'bn', 'mega-menu-translations.json');

// Define module mapping - which keys go to which module files
const moduleMapping = {
  'NAV': 'core',
  'QUICK_LINKS': 'core',
  'ABOUT': 'about',
  'ACADEMICS': 'academics',
  'ADMISSIONS': 'admissions',
  'CAMPUS_LIFE': 'campus-life',
  'NEWS': 'news',
  'EVENTS': 'events',
  'PORTALS': 'core', // Portals keys go to core module
  'FACULTY': 'faculty',
  'CONTACT': 'contact'
};

// Function to read JSON file
function readJsonFile(filePath) {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    return null;
  }
}

// Function to write JSON file
function writeJsonFile(filePath, data) {
  try {
    const dirPath = path.dirname(filePath);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    console.log(`Successfully wrote to ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error writing file ${filePath}:`, error);
    return false;
  }
}

// Function to merge translations into module files
function mergeTranslations(language) {
  console.log(`\nProcessing ${language} translations...`);
  
  // Read mega menu translations
  const megaMenuPath = language === 'en' ? enMegaMenuPath : bnMegaMenuPath;
  const megaMenuTranslations = readJsonFile(megaMenuPath);
  
  if (!megaMenuTranslations) {
    console.error(`Could not read mega menu translations for ${language}`);
    return;
  }
  
  // Process each top-level key in the mega menu translations
  for (const [key, value] of Object.entries(megaMenuTranslations)) {
    // Determine which module this key belongs to
    const module = moduleMapping[key];
    
    if (!module) {
      console.warn(`No module mapping found for key: ${key}`);
      continue;
    }
    
    // Construct the path to the module file
    const modulePath = language === 'en' 
      ? path.join(basePath, 'en', `${module}.json`)
      : path.join(basePath, 'bn', `${module}.json`);
    
    console.log(`Processing ${key} -> ${module} (${modulePath})`);
    
    // Read the module file
    let moduleData = readJsonFile(modulePath);
    
    // If the file doesn't exist or couldn't be read, create a new object
    if (!moduleData) {
      console.log(`Creating new module file: ${modulePath}`);
      moduleData = {};
    }
    
    // Merge the translations
    if (!moduleData[key]) {
      moduleData[key] = {};
    }
    
    // Deep merge the translations
    for (const [subKey, subValue] of Object.entries(value)) {
      moduleData[key][subKey] = subValue;
    }
    
    // Write the updated module file
    writeJsonFile(modulePath, moduleData);
  }
}

// Main function
function main() {
  console.log('Starting mega menu translation merge...');
  
  // Process English translations
  mergeTranslations('en');
  
  // Process Bengali translations
  mergeTranslations('bn');
  
  console.log('\nMerge completed successfully!');
}

// Run the main function
main();
