import { provideHttpClient, withInterceptorsFromDi, withJsonpSupport, HTTP_INTERCEPTORS } from '@angular/common/http';
import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter, withHashLocation, withViewTransitions } from '@angular/router';
import { provideAnimations } from '@angular/platform-browser/animations';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { MergeTranslateLoader } from './core/services/merge-translate-loader';
import { routes } from './app.routes';
import { HttpRequestInterceptor } from './core/interceptors/http-request.interceptor';
import { TenantInterceptor } from './core/interceptors/tenant.interceptor';

export function HttpLoaderFactory(http: HttpClient) {
  return new MergeTranslateLoader(http);
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes, withViewTransitions(), withHashLocation()),
    provideAnimations(),
    provideHttpClient(
      withInterceptorsFromDi(),
      withJsonpSupport()
    ),
    // Register HTTP interceptors
    // Tenant interceptor - adds tenant headers to API requests
    { provide: HTTP_INTERCEPTORS, useClass: TenantInterceptor, multi: true },
    // HTTP request interceptor - handles basic headers and authentication
    { provide: HTTP_INTERCEPTORS, useClass: HttpRequestInterceptor, multi: true },
    importProvidersFrom(
      TranslateModule.forRoot({
        defaultLanguage: 'en',
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient]
        }
      })
    )
  ]
};
