import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { ErrorHandlerService } from './error-handler.service';

@Injectable({
  providedIn: 'root'
})
export class CareerService extends BaseApiService {
  constructor(
    protected override http: HttpClient,
    protected override errorHandler: ErrorHandlerService
  ) {
    super(http, errorHandler);
  }

  // Career CRUD operations
  getCareers(params: any = {}): Observable<any> {
    let httpParams = new HttpParams();

    if (params.category) httpParams = httpParams.set('category', params.category);
    if (params.isActive !== undefined) httpParams = httpParams.set('isActive', params.isActive.toString());
    if (params.search) httpParams = httpParams.set('search', params.search);
    if (params.page) httpParams = httpParams.set('page', params.page.toString());
    if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());

    return this.http.get(`${this.apiUrl}/careers`, { params: httpParams });
  }

  getCareer(id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/careers/${id}`);
  }

  createCareer(career: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/careers`, career);
  }

  updateCareer(id: number, career: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/careers/${id}`, career);
  }

  deleteCareer(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/careers/${id}`);
  }

  updateCareerStatus(id: number, status: number): Observable<any> {
    return this.http.put(`${this.apiUrl}/careers/${id}/status`, { status });
  }

  // Translation operations
  addCareerTranslation(careerId: number, translation: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/careers/${careerId}/translations`, translation);
  }

  updateCareerTranslation(careerId: number, languageCode: string, translation: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/careers/${careerId}/translations/${languageCode}`, translation);
  }

  deleteCareerTranslation(careerId: number, languageCode: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/careers/${careerId}/translations/${languageCode}`);
  }

  // Application operations
  getCareerApplications(params: any = {}): Observable<any> {
    let httpParams = new HttpParams();

    if (params.careerId) httpParams = httpParams.set('careerId', params.careerId.toString());
    if (params.status !== undefined) httpParams = httpParams.set('status', params.status.toString());
    if (params.search) httpParams = httpParams.set('search', params.search);
    if (params.page) httpParams = httpParams.set('page', params.page.toString());
    if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());

    return this.http.get(`${this.apiUrl}/career-applications`, { params: httpParams });
  }

  getCareerApplication(id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/career-applications/${id}`);
  }

  createCareerApplication(application: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/career-applications`, application);
  }

  updateCareerApplicationStatus(id: number, statusDto: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/career-applications/${id}/status`, statusDto);
  }

  deleteCareerApplication(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/career-applications/${id}`);
  }
}
