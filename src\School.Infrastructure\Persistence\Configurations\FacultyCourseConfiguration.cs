using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class FacultyCourseConfiguration : IEntityTypeConfiguration<FacultyCourse>
{
    public void Configure(EntityTypeBuilder<FacultyCourse> builder)
    {
        builder.Property(t => t.Name)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(t => t.Description)
            .HasMaxLength(500);

        builder.HasOne(t => t.Faculty)
            .WithMany(t => t.Courses)
            .HasForeignKey(t => t.FacultyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasQueryFilter(fc => !fc.IsDeleted);
    }
}
