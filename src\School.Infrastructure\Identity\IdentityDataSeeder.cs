using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using School.Domain.Enums;
using School.Domain.Entities;
using School.Infrastructure.Persistence;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Identity
{
    public static class IdentityDataSeeder
    {
        public static async Task SeedRolesAndUsersAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<IdentityRole>>();

            // Seed roles
            await SeedRolesAsync(roleManager, logger);

            // Seed all users (admin + test users) and their corresponding entities
            await SeedAllUsersAsync(userManager, context, logger);
        }

        private static async Task SeedRolesAsync(RoleManager<IdentityRole> roleManager, ILogger logger)
        {
            logger.LogInformation("Seeding roles...");

            // Get all role names from the UserRole enum
            var roleNames = Enum.GetNames(typeof(UserRole));

            // Add additional roles that are not in the UserRole enum
            var additionalRoles = new[] { "Editor" };
            var allRoles = roleNames.Concat(additionalRoles);

            foreach (var roleName in allRoles)
            {
                // Check if the role already exists
                var roleExists = await roleManager.RoleExistsAsync(roleName);
                if (!roleExists)
                {
                    // Create the role
                    var result = await roleManager.CreateAsync(new IdentityRole(roleName));
                    if (result.Succeeded)
                    {
                        logger.LogInformation("Created role {RoleName}", roleName);
                    }
                    else
                    {
                        logger.LogError("Failed to create role {RoleName}. Errors: {Errors}",
                            roleName, string.Join(", ", result.Errors.Select(e => e.Description)));
                    }
                }
            }
        }

        private static async Task SeedAllUsersAsync(UserManager<ApplicationUser> userManager, ApplicationDbContext context, ILogger logger)
        {
            logger.LogInformation("Seeding all users...");

            // Define comprehensive test users with different roles
            var allUsers = new List<(string Username, string Email, string Password, string FirstName, string LastName, UserRole Role, string? PhoneNumber, DateTime? DateOfBirth, string? Address)>
            {
                // System Admin users - manage system-wide operations across all tenants
                ("systemadmin", "<EMAIL>", "SystemAdmin@123456", "System", "Administrator", UserRole.SystemAdmin, "+8801712345678", DateTime.SpecifyKind(new DateTime(1980, 1, 15), DateTimeKind.Utc), "123 System Admin Street, Dhaka"),
                ("sysadmin2", "<EMAIL>", "SystemAdmin@123456", "Super", "SystemAdmin", UserRole.SystemAdmin, "+8801712345679", DateTime.SpecifyKind(new DateTime(1975, 5, 20), DateTimeKind.Utc), "456 System Management Ave, Dhaka"),

                // Tenant Admin users - manage school-related tasks within a tenant
                ("admin", "<EMAIL>", "Admin@123456", "School", "Administrator", UserRole.Admin, "+8801712345680", DateTime.SpecifyKind(new DateTime(1982, 3, 10), DateTimeKind.Utc), "789 School Admin Street, Dhaka"),
                ("admin2", "<EMAIL>", "Admin@123456", "Tenant", "Admin", UserRole.Admin, "+8801712345681", DateTime.SpecifyKind(new DateTime(1978, 8, 25), DateTimeKind.Utc), "321 Tenant Management Ave, Dhaka"),

                // Faculty users
                ("faculty1", "<EMAIL>", "Faculty@123", "Dr. John", "Smith", UserRole.Faculty, "+8801712345680", DateTime.SpecifyKind(new DateTime(1985, 3, 10), DateTimeKind.Utc), "789 Faculty Lane, Dhaka"),
                ("faculty2", "<EMAIL>", "Faculty@123", "Prof. Sarah", "Johnson", UserRole.Faculty, "+8801712345681", DateTime.SpecifyKind(new DateTime(1982, 7, 25), DateTimeKind.Utc), "321 Education Blvd, Dhaka"),

                // Student users
                ("student1", "<EMAIL>", "Student@123", "Michael", "Brown", UserRole.Student, "+8801712345685", DateTime.SpecifyKind(new DateTime(2008, 4, 12), DateTimeKind.Utc), "258 Student Hostel, Dhaka"),
                ("student2", "<EMAIL>", "Student@123", "Emily", "Davis", UserRole.Student, "+8801712345686", DateTime.SpecifyKind(new DateTime(2007, 8, 18), DateTimeKind.Utc), "369 Youth Quarter, Dhaka"),

                // Parent users
                ("parent1", "<EMAIL>", "Parent@123", "Robert", "Taylor", UserRole.Parent, "+8801712345691", DateTime.SpecifyKind(new DateTime(1975, 10, 8), DateTimeKind.Utc), "258 Student Hostel, Dhaka"),

                // Manager users
                ("manager1", "<EMAIL>", "Manager@123", "William", "Harris", UserRole.Manager, "+8801712345699", DateTime.SpecifyKind(new DateTime(1979, 6, 18), DateTimeKind.Utc), "987 Management Plaza, Dhaka"),

                // Editor users
                ("editor1", "<EMAIL>", "Editor@123", "Elizabeth", "Clark", UserRole.Editor, "+8801712345701", DateTime.SpecifyKind(new DateTime(1986, 1, 30), DateTimeKind.Utc), "258 Media House, Dhaka")
            };

            foreach (var (username, email, password, firstName, lastName, role, phoneNumber, dateOfBirth, address) in allUsers)
            {
                await CreateUserAsync(userManager, context, logger, username, email, password, firstName, lastName, role, phoneNumber, dateOfBirth, address);
            }

            logger.LogInformation("All users seeding completed");
        }

        private static async Task CreateUserAsync(UserManager<ApplicationUser> userManager, ApplicationDbContext context, ILogger logger,
            string username, string email, string password, string firstName, string lastName, UserRole role,
            string? phoneNumber, DateTime? dateOfBirth, string? address)
        {
            // Check if user already exists and delete if found (to handle DateTime UTC fix)
            var existingUser = await userManager.FindByNameAsync(username);
            if (existingUser != null)
            {
                logger.LogInformation("Deleting existing user: {Username} to recreate with proper DateTime UTC", username);
                var deleteResult = await userManager.DeleteAsync(existingUser);
                if (deleteResult.Succeeded)
                {
                    logger.LogInformation("Successfully deleted existing user: {Username}", username);
                }
                else
                {
                    logger.LogError("Failed to delete existing user {Username}: {Errors}", username, string.Join(", ", deleteResult.Errors.Select(e => e.Description)));
                    return; // Skip this user if deletion failed
                }
            }

            logger.LogInformation("Creating user: {Username} with role {Role}", username, role);

            var user = new ApplicationUser
            {
                Id = Guid.NewGuid().ToString(),
                UserName = username,
                Email = email,
                EmailConfirmed = true,
                FirstName = firstName,
                LastName = lastName,
                Role = role,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                PhoneNumber = phoneNumber,
                DateOfBirth = dateOfBirth?.ToUniversalTime(),
                Address = address,
                PreferredLanguage = "en",
                PreferredTheme = "light",
                TimeZone = "Asia/Dhaka"
            };

            var result = await userManager.CreateAsync(user, password);
            if (result.Succeeded)
            {
                logger.LogInformation("Created user {Username} with ID {UserId}", username, user.Id);

                // Add user to role
                result = await userManager.AddToRoleAsync(user, role.ToString());
                if (result.Succeeded)
                {
                    logger.LogInformation("Added user {Username} to role {Role}", username, role);

                    // Create corresponding entity based on role
                    await CreateRoleSpecificEntityAsync(context, user, role, username, logger);
                }
                else
                {
                    logger.LogError("Failed to add user {Username} to role {Role}. Errors: {Errors}",
                        username, role, string.Join(", ", result.Errors.Select(e => e.Description)));
                }
            }
            else
            {
                logger.LogError("Failed to create user {Username}. Errors: {Errors}",
                    username, string.Join(", ", result.Errors.Select(e => e.Description)));
            }
        }

        private static async Task CreateRoleSpecificEntityAsync(ApplicationDbContext context, ApplicationUser user, UserRole role, string username, ILogger logger)
        {
            try
            {
                switch (role)
                {
                    case UserRole.Student:
                        var student = new Student
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id, // 🔧 FIX: Link Student to ApplicationUser
                            StudentId = $"STU{DateTime.Now.Year}{Random.Shared.Next(1000, 9999)}",
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            Email = user.Email ?? string.Empty,
                            Phone = user.PhoneNumber ?? string.Empty,
                            Address = user.Address ?? string.Empty,
                            DateOfBirth = user.DateOfBirth ?? DateTime.Now.AddYears(-16),
                            Gender = GenderType.Mixed, // Default, can be updated later
                            CurrentGrade = 10, // Default grade
                            Section = "A", // Default section
                            RollNumber = Random.Shared.Next(1, 50),
                            AcademicYear = DateTime.Now.Year,
                            AdmissionYear = DateTime.Now.Year, // 🔧 FIX: Add missing AdmissionYear
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow,
                            EmergencyContactName = "Emergency Contact",
                            EmergencyContactPhone = "+8801712345678",
                            EmergencyContactRelation = "Parent",
                            BloodGroup = "O+",
                            MedicalConditions = "",
                            Allergies = "",
                            Medium = TeachingMedium.English,
                            Shift = ShiftType.Morning,
                            IsHosteler = false
                        };
                        context.Students.Add(student);
                        logger.LogInformation("Created Student entity for user {Username}", username);
                        break;

                    case UserRole.Faculty:
                        var faculty = new Faculty
                        {
                            Id = Guid.NewGuid(),
                            Name = $"{user.FirstName} {user.LastName}",
                            Title = "Teacher",
                            Email = user.Email ?? string.Empty,
                            Phone = user.PhoneNumber ?? string.Empty,
                            Office = "Main Building",
                            Biography = "Experienced educator",
                            ShortBio = "Teacher",
                            JoinedYear = DateTime.Now.Year - 2,
                            IsFeatured = false,
                            IsActive = true,
                            DisplayOrder = 1,
                            Department = "General",
                            CreatedAt = DateTime.UtcNow
                        };
                        context.Faculty.Add(faculty);
                        logger.LogInformation("Created Faculty entity for user {Username}", username);
                        break;

                    case UserRole.Parent:
                        var parent = new Parent
                        {
                            Id = Guid.NewGuid(),
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            Gender = GenderType.Mixed,
                            Email = user.Email ?? string.Empty,
                            Phone = user.PhoneNumber ?? string.Empty,
                            Address = user.Address ?? string.Empty,
                            Occupation = "Professional",
                            RelationType = ParentRelationType.Father,
                            UserId = user.Id,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        };
                        context.Parents.Add(parent);
                        logger.LogInformation("Created Parent entity for user {Username}", username);
                        break;

                    case UserRole.Alumni:
                        var alumni = new Alumni
                        {
                            Id = Guid.NewGuid(),
                            Name = $"{user.FirstName} {user.LastName}",
                            Email = user.Email ?? string.Empty,
                            Phone = user.PhoneNumber ?? string.Empty,
                            GraduationYear = DateTime.Now.Year - 2,
                            Profession = "Professional",
                            Organization = "Tech Company",
                            Designation = "Software Engineer",
                            Biography = "Successful graduate",
                            Achievements = "Various achievements",
                            IsFeatured = false,
                            IsActive = true,
                            DisplayOrder = 1,
                            CreatedAt = DateTime.UtcNow
                        };
                        context.Alumni.Add(alumni);
                        logger.LogInformation("Created Alumni entity for user {Username}", username);
                        break;

                    // Admin, Manager, Editor, User roles don't need specific entities
                    default:
                        logger.LogInformation("No specific entity needed for role {Role}", role);
                        break;
                }

                // Save changes to database
                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to create entity for user {Username} with role {Role}", username, role);
            }
        }
    }
}
