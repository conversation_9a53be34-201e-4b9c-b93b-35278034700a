<div class="notice-detail-container">
  <div class="page-header">
    <div class="header-left">
      <button mat-icon-button (click)="goBack()" aria-label="Back">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <h1>{{ 'admin.notices.view' | translate }}</h1>
    </div>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="editNotice()">
        <mat-icon>edit</mat-icon>
        {{ 'admin.common.edit' | translate }}
      </button>
      <button mat-raised-button color="warn" (click)="deleteNotice()">
        <mat-icon>delete</mat-icon>
        {{ 'admin.common.delete' | translate }}
      </button>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Error Message -->
  <div class="error-container" *ngIf="error">
    <mat-card class="error-card">
      <mat-card-content>
        <mat-icon color="warn">error</mat-icon>
        <p>{{ 'admin.common.error_loading' | translate }}</p>
        <button mat-raised-button color="primary" (click)="loadNotice(notice?.id!)">
          <mat-icon>refresh</mat-icon>
          {{ 'admin.common.retry' | translate }}
        </button>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Notice Details -->
  <div class="detail-container" *ngIf="!isLoading && !error && notice">
    <mat-card class="notice-card">
      <mat-card-header>
        <mat-card-title>{{ notice.title }}</mat-card-title>
        <mat-card-subtitle>
          <div class="notice-meta">
            <span class="notice-category">{{ notice.category }}</span>
            <span class="notice-date">{{ notice.startDate | date:'mediumDate' }}</span>
            <mat-chip [ngClass]="getPriorityClass(notice.priority)">
              {{ getPriorityLabel(notice.priority) }}
            </mat-chip>
            <mat-chip [color]="notice.isActive ? 'primary' : 'warn'" selected>
              {{ notice.isActive ? ('admin.common.active' | translate) : ('admin.common.inactive' | translate) }}
            </mat-chip>
          </div>
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <mat-tab-group>
          <!-- Default Language (English) Tab -->
          <mat-tab label="English">
            <div class="notice-content">
              <h3>{{ 'admin.notices.content' | translate }}</h3>
              <p>{{ notice.content }}</p>

              <div class="notice-details">
                <div class="detail-item">
                  <span class="detail-label">{{ 'admin.notices.start_date' | translate }}:</span>
                  <span class="detail-value">{{ notice.startDate | date:'mediumDate' }}</span>
                </div>

                <div class="detail-item" *ngIf="notice.endDate">
                  <span class="detail-label">{{ 'admin.notices.end_date' | translate }}:</span>
                  <span class="detail-value">{{ notice.endDate | date:'mediumDate' }}</span>
                </div>

                <div class="detail-item">
                  <span class="detail-label">{{ 'admin.notices.created_at' | translate }}:</span>
                  <span class="detail-value">{{ notice.createdAt | date:'medium' }}</span>
                </div>

                <div class="detail-item" *ngIf="notice.lastModifiedAt">
                  <span class="detail-label">{{ 'admin.notices.updated_at' | translate }}:</span>
                  <span class="detail-value">{{ notice.lastModifiedAt | date:'medium' }}</span>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Translation Tabs -->
          <mat-tab *ngFor="let translation of notice.translations" [label]="translation.languageCode === 'bn' ? 'Bengali' : translation.languageCode">
            <div class="notice-content">
              <h3>{{ translation.title }}</h3>
              <p>{{ translation.content }}</p>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card-content>
    </mat-card>
  </div>
</div>
