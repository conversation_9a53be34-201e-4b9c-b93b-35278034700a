import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { ErrorHandlerService } from './error-handler.service';
import { ApiResponseHandlerService } from './api-response-handler.service';
import {
  Notice,
  NoticeFilter,
  CreateNotice,
  UpdateNotice
} from '../models/notice.model';
import { ApiResponse } from '../models/api-response.model';
import { PaginatedResponse } from '../models/base.model';

@Injectable({
  providedIn: 'root'
})
export class NoticeService extends BaseApiService {
  constructor(
    protected override http: HttpClient,
    protected override errorHandler: ErrorHandlerService,
    private apiResponseHandler: ApiResponseHandlerService
  ) {
    super(http, errorHandler);
  }

  /**
   * Get all notices with filtering and pagination
   */
  getAllNotices(filter: NoticeFilter = {}): Observable<PaginatedResponse<Notice>> {
    const params = this.buildParams(filter);
    return this.apiResponseHandler.processResponse<PaginatedResponse<Notice>>(
      this.http.get<ApiResponse<PaginatedResponse<Notice>>>(`${this.apiUrl}/notices`, { params }),
      false,
      'Failed to retrieve notices'
    );
  }

  /**
   * Get notice by ID
   */
  getNoticeById(id: string): Observable<Notice> {
    return this.apiResponseHandler.processResponse<Notice>(
      this.http.get<ApiResponse<Notice>>(`${this.apiUrl}/notices/${id}`),
      false,
      'Failed to retrieve notice'
    );
  }

  /**
   * Create a new notice
   */
  createNotice(notice: CreateNotice): Observable<{ id: string }> {
    return this.apiResponseHandler.processResponse<{ id: string }>(
      this.http.post<ApiResponse<{ id: string }>>(`${this.apiUrl}/notices`, notice),
      true,
      'Notice created successfully'
    );
  }

  /**
   * Update an existing notice
   */
  updateNotice(id: string, notice: UpdateNotice): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/notices/${id}`, notice),
      true,
      'Notice updated successfully'
    );
  }

  /**
   * Delete a notice
   */
  deleteNotice(id: string): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/notices/${id}`),
      true,
      'Notice deleted successfully'
    );
  }

  /**
   * Get active notices for public display
   */
  getActiveNotices(): Observable<Notice[]> {
    return this.apiResponseHandler.processResponse<Notice[]>(
      this.http.get<ApiResponse<Notice[]>>(`${this.apiUrl}/notices/active`),
      false,
      'Failed to retrieve active notices'
    );
  }
}
