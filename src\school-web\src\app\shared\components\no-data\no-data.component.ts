import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-no-data',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule],
  template: `
    <div class="no-data-container" [ngStyle]="{'height': height}">
      <mat-card>
        <mat-card-content>
          <div class="no-data-content">
            <mat-icon class="no-data-icon">{{ icon }}</mat-icon>
            <p class="no-data-message">{{ message }}</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .no-data-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
    }
    
    .no-data-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 16px;
    }
    
    .no-data-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
      color: rgba(0, 0, 0, 0.38);
    }
    
    .no-data-message {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.6);
    }
  `]
})
export class NoDataComponent {
  @Input() message: string = 'No data available';
  @Input() icon: string = 'inbox';
  @Input() height: string = '300px';
}
