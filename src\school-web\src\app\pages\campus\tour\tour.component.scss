@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Map Legend Colors
$academic-color: #4caf50;
$athletic-color: #2196f3;
$residential-color: #ff9800;
$support-color: #9c27b0;
$parking-color: #607d8b;

// Hero Section styles are now handled by the DefaultHeroComponent

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Section Styles
section {
  padding: $section-padding;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: $dark-gray;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Introduction Section
.intro-section {
  background-color: $white;

  .intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-of-type {
        margin-bottom: 30px;
      }
    }

    .tour-cta {
      a {
        padding: 10px 30px;
        font-size: 1.1rem;
      }
    }
  }
}

// Map Section
.map-section {
  background-color: $light-gray;

  .map-container {
    margin-top: 40px;

    .campus-map {
      width: 100%;
      max-width: 1000px;
      height: auto;
      display: block;
      margin: 0 auto 30px;
      border-radius: $border-radius;
      box-shadow: $box-shadow;
    }

    .map-legend {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 20px;

      .legend-item {
        display: flex;
        align-items: center;
        margin-right: 20px;

        &:last-child {
          margin-right: 0;
        }

        .legend-color {
          width: 20px;
          height: 20px;
          border-radius: 4px;
          margin-right: 8px;

          &.academic {
            background-color: $academic-color;
          }

          &.athletic {
            background-color: $athletic-color;
          }

          &.residential {
            background-color: $residential-color;
          }

          &.support {
            background-color: $support-color;
          }

          &.parking {
            background-color: $parking-color;
          }
        }

        span {
          color: $dark-gray;
          font-size: 0.9rem;
        }
      }
    }
  }
}

// Facilities Section
.facilities-section {
  background-color: $white;

  .facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .facility-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .facility-image {
        height: 200px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 15px;
          color: $text-color;
        }

        .facility-description {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 20px;
        }

        .facility-features {
          h4 {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: $text-color;
          }

          ul {
            padding-left: 20px;
            margin-bottom: 0;

            li {
              color: $dark-gray;
              margin-bottom: 5px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}

// Virtual Tour Section
.virtual-tour-section {
  background-color: $light-gray;

  .videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .video-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);

        .play-button {
          transform: scale(1.1);
        }
      }

      .video-thumbnail {
        height: 200px;
        overflow: hidden;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-button {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 60px;
          height: 60px;
          background-color: rgba(0, 0, 0, 0.7);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: transform 0.3s;

          mat-icon {
            color: $white;
            font-size: 40px;
            height: 40px;
            width: 40px;
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 15px;
          color: $text-color;
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }
}

// Panoramas Section
.panoramas-section {
  background-color: $white;

  .panoramas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .panorama-item {
      text-align: center;

      .panorama-image {
        position: relative;
        border-radius: $border-radius;
        overflow: hidden;
        box-shadow: $box-shadow;
        margin-bottom: 15px;

        img {
          width: 100%;
          height: 180px;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }

        .panorama-icon {
          position: absolute;
          bottom: 10px;
          right: 10px;
          width: 40px;
          height: 40px;
          background-color: rgba(0, 0, 0, 0.7);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          mat-icon {
            color: $white;
            font-size: 24px;
            height: 24px;
            width: 24px;
          }
        }
      }

      h3 {
        font-size: 1.3rem;
        margin-bottom: 10px;
        color: $text-color;
      }

      p {
        color: $dark-gray;
        line-height: 1.6;
        margin-bottom: 0;
      }
    }
  }
}

// Visit Section
.visit-section {
  background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -15%));
  color: $white;

  .visit-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h2 {
      color: $white;

      &:after {
        background-color: $white;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
    }

    .visit-details {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: 40px;
      margin-bottom: 40px;

      .visit-item {
        display: flex;
        align-items: flex-start;
        text-align: left;

        mat-icon {
          font-size: 30px;
          height: 30px;
          width: 30px;
          margin-right: 15px;
          margin-top: 5px;
        }

        .visit-info {
          h3 {
            font-size: 1.3rem;
            margin-bottom: 10px;
          }

          p {
            font-size: 1rem;
            margin-bottom: 5px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    .visit-cta {
      display: flex;
      justify-content: center;
      gap: 20px;

      a {
        padding: 10px 30px;
        font-size: 1.1rem;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .hero-section {
    height: 350px;

    .hero-content h1 {
      font-size: 2.5rem;
    }
  }

  section {
    padding: 60px 0;

    h2 {
      font-size: 2rem;
    }
  }

  .facilities-grid, .videos-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .panoramas-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  // Hero section styles removed

  .map-legend {
    flex-direction: column;
    align-items: center;

    .legend-item {
      margin-right: 0 !important;
      margin-bottom: 10px;
    }
  }

  .visit-details {
    flex-direction: column;
    gap: 20px !important;
  }

  .visit-cta {
    flex-direction: column;
    align-items: center;

    a {
      width: 100%;
      max-width: 300px;
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

@media (max-width: 576px) {
  // Hero section styles removed

  section h2 {
    font-size: 1.8rem;
  }

  .facilities-grid, .videos-grid {
    grid-template-columns: 1fr;
  }

  .panoramas-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
