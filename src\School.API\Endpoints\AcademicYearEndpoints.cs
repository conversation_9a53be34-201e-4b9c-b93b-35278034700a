using Carter;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.DTOs;
using School.Application.Features.AcademicYear;

namespace School.API.Endpoints;

public class AcademicYearEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/academic-years").WithTags("Academic Years");

        // Get all academic years with filtering and pagination
        group.MapGet("/", async ([AsParameters] AcademicYearFilterDto filter, [FromServices] IAcademicYearService academicYearService) =>
        {
            var (academicYears, totalCount) = await academicYearService.GetAllAcademicYearsAsync(filter);
            var response = new { TotalCount = totalCount, Items = academicYears };
            return ApiResults.ApiOk(response, "Academic years retrieved successfully");
        }).WithName("GetAllAcademicYears").WithOpenApi();

        // Get academic year by ID
        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IAcademicYearService academicYearService) =>
        {
            var academicYear = await academicYearService.GetAcademicYearByIdAsync(id);
            if (academicYear == null)
            {
                return ApiResults.ApiNotFound("Academic year not found");
            }
            return ApiResults.ApiOk(academicYear, "Academic year retrieved successfully");
        }).WithName("GetAcademicYearById").WithOpenApi();

        // Get current academic year
        group.MapGet("/current", async ([FromServices] IAcademicYearService academicYearService) =>
        {
            var currentAcademicYear = await academicYearService.GetCurrentAcademicYearAsync();
            if (currentAcademicYear == null)
            {
                return ApiResults.ApiNotFound("No current academic year found");
            }
            return ApiResults.ApiOk(currentAcademicYear, "Current academic year retrieved successfully");
        }).WithName("GetCurrentAcademicYear").WithOpenApi();

        // Create new academic year
        group.MapPost("/", async ([FromBody] CreateAcademicYearDto academicYearDto, [FromServices] IAcademicYearService academicYearService) =>
        {
            try
            {
                var academicYearId = await academicYearService.CreateAcademicYearAsync(academicYearDto);
                return ApiResults.ApiCreated($"/api/academic-years/{academicYearId}", academicYearId.ToString(), "Academic year created successfully");
            }
            catch (InvalidOperationException ex)
            {
                return ApiResults.ApiBadRequest(ex.Message);
            }
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("CreateAcademicYear")
        .WithOpenApi();

        // Update academic year
        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateAcademicYearDto academicYearDto, [FromServices] IAcademicYearService academicYearService) =>
        {
            try
            {
                var result = await academicYearService.UpdateAcademicYearAsync(id, academicYearDto);
                if (!result)
                {
                    return ApiResults.ApiNotFound("Academic year not found");
                }
                return ApiResults.ApiOk("Academic year updated successfully");
            }
            catch (InvalidOperationException ex)
            {
                return ApiResults.ApiBadRequest(ex.Message);
            }
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("UpdateAcademicYear")
        .WithOpenApi();

        // Delete academic year
        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IAcademicYearService academicYearService) =>
        {
            var result = await academicYearService.DeleteAcademicYearAsync(id);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Cannot delete academic year. It may have associated data or not exist.");
            }
            return ApiResults.ApiOk("Academic year deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteAcademicYear")
        .WithOpenApi();

        // Set current academic year
        group.MapPost("/{id}/set-current", async ([FromRoute] Guid id, [FromServices] IAcademicYearService academicYearService) =>
        {
            var result = await academicYearService.SetCurrentAcademicYearAsync(id);
            if (!result)
            {
                return ApiResults.ApiNotFound("Academic year not found");
            }
            return ApiResults.ApiOk("Academic year set as current successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("SetCurrentAcademicYear")
        .WithOpenApi();

        // Activate academic year
        group.MapPost("/{id}/activate", async ([FromRoute] Guid id, [FromServices] IAcademicYearService academicYearService) =>
        {
            var result = await academicYearService.ActivateAcademicYearAsync(id);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Cannot activate academic year. It may not exist or not be in draft status.");
            }
            return ApiResults.ApiOk("Academic year activated successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("ActivateAcademicYear")
        .WithOpenApi();

        // Complete academic year
        group.MapPost("/{id}/complete", async ([FromRoute] Guid id, [FromServices] IAcademicYearService academicYearService) =>
        {
            var result = await academicYearService.CompleteAcademicYearAsync(id);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Cannot complete academic year. It may not exist or not be active.");
            }
            return ApiResults.ApiOk("Academic year completed successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("CompleteAcademicYear")
        .WithOpenApi();

        // Archive academic year
        group.MapPost("/{id}/archive", async ([FromRoute] Guid id, [FromServices] IAcademicYearService academicYearService) =>
        {
            var result = await academicYearService.ArchiveAcademicYearAsync(id);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Cannot archive academic year. It may not exist or not be completed.");
            }
            return ApiResults.ApiOk("Academic year archived successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("ArchiveAcademicYear")
        .WithOpenApi();

        // Get academic year statistics
        group.MapGet("/{id}/statistics", async ([FromRoute] Guid id, [FromServices] IAcademicYearService academicYearService) =>
        {
            var statistics = await academicYearService.GetAcademicYearStatisticsAsync(id);
            return ApiResults.ApiOk(statistics, "Academic year statistics retrieved successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("GetAcademicYearStatistics")
        .WithOpenApi();

        // Translation endpoints
        var translationGroup = group.MapGroup("/{academicYearId}/translations");

        // Add translation
        translationGroup.MapPost("/", async ([FromRoute] Guid academicYearId, [FromBody] CreateAcademicYearTranslationDto translationDto, [FromServices] IAcademicYearService academicYearService) =>
        {
            var result = await academicYearService.AddTranslationAsync(academicYearId, translationDto);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Translation already exists or academic year not found");
            }
            return ApiResults.ApiOk("Translation added successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("AddAcademicYearTranslation")
        .WithOpenApi();

        // Update translation
        translationGroup.MapPut("/{languageCode}", async ([FromRoute] Guid academicYearId, [FromRoute] string languageCode, [FromBody] UpdateAcademicYearTranslationDto translationDto, [FromServices] IAcademicYearService academicYearService) =>
        {
            var result = await academicYearService.UpdateTranslationAsync(academicYearId, languageCode, translationDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Translation not found");
            }
            return ApiResults.ApiOk("Translation updated successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("UpdateAcademicYearTranslation")
        .WithOpenApi();

        // Delete translation
        translationGroup.MapDelete("/{languageCode}", async ([FromRoute] Guid academicYearId, [FromRoute] string languageCode, [FromServices] IAcademicYearService academicYearService) =>
        {
            var result = await academicYearService.DeleteTranslationAsync(academicYearId, languageCode);
            if (!result)
            {
                return ApiResults.ApiNotFound("Translation not found");
            }
            return ApiResults.ApiOk("Translation deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteAcademicYearTranslation")
        .WithOpenApi();

        // Get all translations
        translationGroup.MapGet("/", async ([FromRoute] Guid academicYearId, [FromServices] IAcademicYearService academicYearService) =>
        {
            var translations = await academicYearService.GetTranslationsAsync(academicYearId);
            return ApiResults.ApiOk(translations, "Translations retrieved successfully");
        })
        .WithName("GetAcademicYearTranslations")
        .WithOpenApi();
    }
}
