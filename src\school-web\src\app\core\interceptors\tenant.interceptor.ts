import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { TenantService } from '../services/tenant.service';
import { AuthService } from '../services/auth.service';

@Injectable()
export class TenantInterceptor implements HttpInterceptor {

  constructor(
    private tenantService: TenantService,
    private authService: AuthService
  ) {}

  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
    // Only add tenant headers to API requests
    if (this.isApiRequest(req.url)) {
      // Skip tenant headers for admin/system routes if user is SystemAdmin
      if (this.isAdminOrSystemRoute(req.url) && this.authService.isSystemAdmin()) {
        console.log('TenantInterceptor: Skipping tenant headers for SystemAdmin on admin route:', req.url);
        return next.handle(req);
      }

      const tenantHeaders = this.tenantService.getTenantHeaders();

      // Clone the request and add tenant headers
      if (Object.keys(tenantHeaders).length > 0) {
        const tenantReq = req.clone({
          setHeaders: tenantHeaders
        });

        return next.handle(tenantReq);
      }
    }

    // For non-API requests or when no tenant headers, proceed normally
    return next.handle(req);
  }

  /**
   * Check if the request is an API request
   */
  private isApiRequest(url: string): boolean {
    // Check if URL contains API endpoint patterns
    return url.includes('/api/') || url.startsWith('api/');
  }

  /**
   * Check if the request is for admin or system routes that don't require tenant context
   */
  private isAdminOrSystemRoute(url: string): boolean {
    const adminRoutes = [
      '/api/auth/admin/',
      '/api/auth/login',
      '/api/auth/register',
      '/api/auth/refresh',
      '/api/auth/logout',
      '/api/admin/',
      '/api/system/',
      '/api/tenant/list',
      '/api/tenant/create',
      '/api/health',
      '/api/users', // User management endpoints
      '/api/notices', // Notice management
      '/api/media', // Media management
      '/api/content' // Content management
    ];

    return adminRoutes.some(route => url.includes(route));
  }
}
