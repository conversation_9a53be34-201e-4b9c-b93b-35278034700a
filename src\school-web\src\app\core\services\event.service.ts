import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { ErrorHandlerService } from './error-handler.service';

@Injectable({
  providedIn: 'root'
})
export class EventService extends BaseApiService {
  constructor(
    protected override http: HttpClient,
    protected override errorHandler: ErrorHandlerService
  ) {
    super(http, errorHandler);
  }

  getEvents(params: any = {}): Observable<any> {
    let httpParams = new HttpParams();

    if (params.category) httpParams = httpParams.set('category', params.category);
    if (params.fromDate) httpParams = httpParams.set('fromDate', params.fromDate.toISOString());
    if (params.toDate) httpParams = httpParams.set('toDate', params.toDate.toISOString());
    if (params.isActive !== undefined) httpParams = httpParams.set('isActive', params.isActive.toString());
    if (params.search) httpParams = httpParams.set('search', params.search);
    if (params.page) httpParams = httpParams.set('page', params.page.toString());
    if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());

    return this.http.get(`${this.apiUrl}/events`, { params: httpParams });
  }

  getCalendarEvents(params: any = {}): Observable<any> {
    let httpParams = new HttpParams();

    if (params.fromDate) httpParams = httpParams.set('fromDate', params.fromDate.toISOString());
    if (params.toDate) httpParams = httpParams.set('toDate', params.toDate.toISOString());
    if (params.category) httpParams = httpParams.set('category', params.category);

    return this.http.get(`${this.apiUrl}/events/calendar`, { params: httpParams });
  }

  getEvent(id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/events/${id}`);
  }

  createEvent(event: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/events`, event);
  }

  updateEvent(id: number, event: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/events/${id}`, event);
  }

  deleteEvent(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/events/${id}`);
  }

  addEventTranslation(eventId: number, translation: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/events/${eventId}/translations`, translation);
  }

  updateEventTranslation(eventId: number, translationId: number, translation: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/events/${eventId}/translations/${translationId}`, translation);
  }

  deleteEventTranslation(eventId: number, translationId: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/events/${eventId}/translations/${translationId}`);
  }

  registerForEvent(eventId: number, registration: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/events/${eventId}/register`, registration);
  }

  getEventRegistrations(eventId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/events/${eventId}/registrations`);
  }

  updateEventRegistration(eventId: number, registrationId: number, registration: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/events/${eventId}/registrations/${registrationId}`, registration);
  }
}
