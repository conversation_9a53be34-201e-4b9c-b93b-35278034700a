<!-- Loading State -->
<div class="loading-container" *ngIf="loading">
  <mat-icon class="loading-icon">hourglass_empty</mat-icon>
  <p>Loading faculty profile...</p>
</div>

<!-- Error State -->
<div class="error-container" *ngIf="error">
  <mat-icon class="error-icon">error_outline</mat-icon>
  <h2>Faculty Member Not Found</h2>
  <p>We couldn't find the faculty member you're looking for.</p>
  <button mat-raised-button color="primary" (click)="goBack()">Back to Faculty Directory</button>
</div>

<!-- Faculty Profile Content -->
<div class="faculty-profile-container" *ngIf="!loading && !error && facultyMember">
  <!-- Profile Header -->
  <section class="profile-header">
    <div class="container">
      <button mat-button class="back-button" (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> Back to Faculty Directory
      </button>
      
      <div class="profile-header-content">
        <div class="profile-image-container">
          <img [src]="facultyMember.image" [alt]="facultyMember.name" class="profile-image">
        </div>
        
        <div class="profile-header-info">
          <h1>{{ facultyMember.name }}</h1>
          <h2>{{ facultyMember.title }}</h2>
          <p class="department">{{ getDepartmentName(facultyMember.department) }}</p>
          
          <div class="profile-specializations">
            <mat-chip-set>
              <mat-chip *ngFor="let specialization of facultyMember.specializations">
                {{ specialization }}
              </mat-chip>
            </mat-chip-set>
          </div>
          
          <div class="profile-contact">
            <div class="contact-item" *ngIf="facultyMember.email">
              <mat-icon>email</mat-icon>
              <span>{{ facultyMember.email }}</span>
            </div>
            <div class="contact-item" *ngIf="facultyMember.phone">
              <mat-icon>phone</mat-icon>
              <span>{{ facultyMember.phone }}</span>
            </div>
            <div class="contact-item" *ngIf="facultyMember.office">
              <mat-icon>location_on</mat-icon>
              <span>{{ facultyMember.office }}</span>
            </div>
          </div>
          
          <div class="profile-social" *ngIf="facultyMember.socialLinks">
            <a *ngIf="facultyMember.socialLinks.website" [href]="facultyMember.socialLinks.website" target="_blank" class="social-link" aria-label="Website">
              <mat-icon>language</mat-icon>
            </a>
            <a *ngIf="facultyMember.socialLinks.linkedin" [href]="facultyMember.socialLinks.linkedin" target="_blank" class="social-link" aria-label="LinkedIn">
              <mat-icon>business</mat-icon>
            </a>
            <a *ngIf="facultyMember.socialLinks.twitter" [href]="facultyMember.socialLinks.twitter" target="_blank" class="social-link" aria-label="Twitter">
              <mat-icon>chat</mat-icon>
            </a>
            <a *ngIf="facultyMember.socialLinks.researchGate" [href]="facultyMember.socialLinks.researchGate" target="_blank" class="social-link" aria-label="ResearchGate">
              <mat-icon>school</mat-icon>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Profile Content -->
  <section class="profile-content">
    <div class="container">
      <mat-tab-group animationDuration="300ms">
        <!-- Biography Tab -->
        <mat-tab label="Biography">
          <div class="tab-content">
            <div class="bio-section">
              <h3>About</h3>
              <p>{{ facultyMember.bio }}</p>
              
              <div class="years-of-service">
                <strong>Years at Our School:</strong> {{ getYearsOfService() }}
              </div>
            </div>
            
            <div class="education-section">
              <h3>Education</h3>
              <ul class="education-list">
                <li *ngFor="let education of facultyMember.education">{{ education }}</li>
              </ul>
            </div>
          </div>
        </mat-tab>
        
        <!-- Courses Tab -->
        <mat-tab label="Courses" *ngIf="facultyMember.courses && facultyMember.courses.length > 0">
          <div class="tab-content">
            <h3>Courses Taught</h3>
            <ul class="courses-list">
              <li *ngFor="let course of facultyMember.courses">{{ course }}</li>
            </ul>
          </div>
        </mat-tab>
        
        <!-- Publications Tab -->
        <mat-tab label="Publications" *ngIf="facultyMember.publications && facultyMember.publications.length > 0">
          <div class="tab-content">
            <h3>Selected Publications</h3>
            <ul class="publications-list">
              <li *ngFor="let publication of facultyMember.publications">{{ publication }}</li>
            </ul>
          </div>
        </mat-tab>
        
        <!-- Awards Tab -->
        <mat-tab label="Awards" *ngIf="facultyMember.awards && facultyMember.awards.length > 0">
          <div class="tab-content">
            <h3>Awards & Recognition</h3>
            <ul class="awards-list">
              <li *ngFor="let award of facultyMember.awards">{{ award }}</li>
            </ul>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </section>
  
  <!-- Other Faculty Section -->
  <section class="other-faculty-section">
    <div class="container">
      <h2>Meet More Faculty</h2>
      <button mat-raised-button color="primary" (click)="goBack()">View All Faculty</button>
    </div>
  </section>
</div>
