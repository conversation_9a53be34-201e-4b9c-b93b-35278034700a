using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class GradeTranslationConfiguration : IEntityTypeConfiguration<GradeTranslation>
{
    public void Configure(EntityTypeBuilder<GradeTranslation> builder)
    {
        builder.ToTable("GradeTranslations");

        builder.HasKey(gt => gt.Id);

        // Configure properties
        builder.Property(gt => gt.LanguageCode)
            .IsRequired()
            .HasMaxLength(10);

        builder.Property(gt => gt.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(gt => gt.Description)
            .HasMaxLength(500);

        builder.Property(gt => gt.PromotionCriteria)
            .HasMaxLength(1000);

        builder.Property(gt => gt.Remarks)
            .HasMaxLength(500);

        builder.Property(gt => gt.CreatedBy)
            .HasMaxLength(450);

        builder.Property(gt => gt.LastModifiedBy)
            .HasMaxLength(450);

        // Configure relationships
        builder.HasOne(gt => gt.Grade)
            .WithMany(g => g.Translations)
            .HasForeignKey(gt => gt.GradeId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(gt => new { gt.GradeId, gt.LanguageCode })
            .IsUnique();

        builder.HasIndex(gt => gt.LanguageCode);

        // Global query filter for multi-tenancy
        builder.HasQueryFilter(gt => EF.Property<string>(gt, "TenantId") == null || 
                                    EF.Property<string>(gt, "TenantId") == "");
    }
}
