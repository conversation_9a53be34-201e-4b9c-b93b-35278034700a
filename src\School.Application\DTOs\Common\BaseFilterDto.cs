namespace School.Application.DTOs.Common;

/// <summary>
/// Base filter DTO for pagination and sorting
/// </summary>
public class BaseFilterDto
{
    private int _page = 1;
    private int _pageSize = 10;

    /// <summary>
    /// Current page number (1-based)
    /// </summary>
    public int Page
    {
        get => _page;
        set => _page = value < 1 ? 1 : value;
    }

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize
    {
        get => _pageSize;
        set => _pageSize = value < 1 ? 10 : (value > 100 ? 100 : value);
    }

    /// <summary>
    /// Property name to sort by
    /// </summary>
    public string? SortBy { get; set; }

    /// <summary>
    /// Sort direction (asc or desc)
    /// </summary>
    public string? SortDirection { get; set; }
}
