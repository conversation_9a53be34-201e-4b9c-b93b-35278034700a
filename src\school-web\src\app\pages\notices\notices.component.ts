import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { NoticeService } from '../../core/services/notice.service';

interface Notice {
  id: number;
  title: string;
  content: string;
  startDate: Date;
  endDate?: Date;
  category: string;
  priority: number;
  isActive: boolean;
  translations?: any[];
}

@Component({
  selector: 'app-notices',
  standalone: true,
  imports: [
    CommonModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatPaginatorModule,
    RouterModule,
    TranslateModule
  ],
  templateUrl: './notices.component.html',
  styleUrl: './notices.component.scss'
})
export class NoticesComponent implements OnInit {
  // Filter and search
  selectedFilter: string = 'all';
  searchText: string = '';

  // Pagination
  pageSize: number = 10;
  currentPage: number = 0;
  totalNotices: number = 0;

  // Notices data
  notices: Notice[] = [];
  filteredNotices: Notice[] = [];
  isLoading: boolean = true;
  error: string | null = null;

  constructor(
    private noticeService: NoticeService,
    private translateService: TranslateService
  ) { }

  ngOnInit(): void {
    this.loadNotices();
  }

  loadNotices(): void {
    this.isLoading = true;
    this.error = null;

    const params = {
      active: true,
      page: this.currentPage + 1,
      pageSize: this.pageSize
    };

    this.noticeService.getAllNotices(params).subscribe({
      next: (response: { items: any[], totalCount: number }) => {
        this.notices = response.items;
        this.totalNotices = response.totalCount;
        this.applyFilters();
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading notices:', error);
        this.error = 'Failed to load notices. Please try again later.';
        this.isLoading = false;
      }
    });
  }

  applyFilters(): void {
    let filtered = [...this.notices];

    // Apply category filter
    if (this.selectedFilter !== 'all') {
      filtered = filtered.filter(notice =>
        notice.category.toLowerCase() === this.selectedFilter.toLowerCase());
    }

    // Apply search filter
    if (this.searchText.trim() !== '') {
      const searchLower = this.searchText.toLowerCase();
      filtered = filtered.filter(notice =>
        notice.title.toLowerCase().includes(searchLower) ||
        notice.content.toLowerCase().includes(searchLower) ||
        (notice.translations && notice.translations.some(t =>
          t.title.toLowerCase().includes(searchLower) ||
          t.content.toLowerCase().includes(searchLower)
        )));
    }

    this.filteredNotices = filtered;
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadNotices();
  }

  getIconForCategory(category: string): string {
    switch (category.toLowerCase()) {
      case 'admissions': return 'school';
      case 'academics': return 'event_note';
      case 'events': return 'celebration';
      case 'sports': return 'sports_soccer';
      default: return 'announcement';
    }
  }

  getPriorityClass(priority: number): string {
    switch (priority) {
      case 0: return 'priority-high';
      case 1: return 'priority-medium';
      case 2: return 'priority-low';
      default: return 'priority-medium';
    }
  }
}
