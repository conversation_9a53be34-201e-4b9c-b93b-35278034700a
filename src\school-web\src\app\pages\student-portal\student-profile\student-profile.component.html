<div class="profile-container">
  <h1 class="page-title">Student Profile</h1>

  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load student profile. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadStudentData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading && !error && student" class="profile-content">
    <mat-card class="profile-card">
      <mat-card-header>
        <div mat-card-avatar class="profile-avatar">
          <img *ngIf="student.profileImage" [src]="student.profileImage.filePath" alt="Student Photo">
          <mat-icon *ngIf="!student.profileImage">account_circle</mat-icon>
        </div>
        <mat-card-title>{{ student.firstName }} {{ student.lastName }}</mat-card-title>
        <mat-card-subtitle>Student ID: {{ student.studentId }}</mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <mat-tab-group>
          <mat-tab label="Personal Information">
            <div class="tab-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">Full Name</span>
                  <span class="info-value">{{ student.firstName }} {{ student.lastName }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Date of Birth</span>
                  <span class="info-value">{{ student.dateOfBirth | date:'mediumDate' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Gender</span>
                  <span class="info-value">{{ student.gender === 0 ? 'Male' : student.gender === 1 ? 'Female' : 'Other' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Email</span>
                  <span class="info-value">{{ student.email }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Phone</span>
                  <span class="info-value">{{ student.phone }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Address</span>
                  <span class="info-value">{{ student.address }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Blood Group</span>
                  <span class="info-value">{{ student.bloodGroup }}</span>
                </div>
                <div class="info-item" *ngIf="student.medicalConditions">
                  <span class="info-label">Medical Conditions</span>
                  <span class="info-value">{{ student.medicalConditions }}</span>
                </div>
                <div class="info-item" *ngIf="student.allergies">
                  <span class="info-label">Allergies</span>
                  <span class="info-value">{{ student.allergies }}</span>
                </div>
              </div>
            </div>
          </mat-tab>

          <mat-tab label="Academic Information">
            <div class="tab-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">Current Grade</span>
                  <span class="info-value">{{ student.currentGrade }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Section</span>
                  <span class="info-value">{{ student.section }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Medium</span>
                  <span class="info-value">{{ student.medium === 0 ? 'Bengali' : 'English' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Shift</span>
                  <span class="info-value">{{
                    student.shift === 0 ? 'Morning' :
                    student.shift === 1 ? 'Day' : 'Evening'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Academic Year</span>
                  <span class="info-value">{{ student.academicYear || 'N/A' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Roll Number</span>
                  <span class="info-value">{{ student.rollNumber || 'N/A' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Admission Year</span>
                  <span class="info-value">{{ student.admissionYear || 'N/A' }}</span>
                </div>
                <div class="info-item" *ngIf="student.graduationDate">
                  <span class="info-label">Graduation Date</span>
                  <span class="info-value">{{ student.graduationDate | date:'mediumDate' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Hosteler</span>
                  <span class="info-value">{{ student.isHosteler ? 'Yes' : 'No' }}</span>
                </div>
                <div class="info-item" *ngIf="student.classTeacher">
                  <span class="info-label">Class Teacher</span>
                  <span class="info-value">{{ student.classTeacher.firstName }} {{ student.classTeacher.lastName }}</span>
                </div>
              </div>
            </div>
          </mat-tab>

          <mat-tab label="Emergency Contact">
            <div class="tab-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">Contact Name</span>
                  <span class="info-value">{{ student.emergencyContactName || 'N/A' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Contact Phone</span>
                  <span class="info-value">{{ student.emergencyContactPhone || 'N/A' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Relation</span>
                  <span class="info-value">{{ student.emergencyContactRelation || 'N/A' }}</span>
                </div>
              </div>
            </div>
          </mat-tab>

          <mat-tab label="Parents">
            <div class="tab-content">
              <div *ngIf="student?.parents && student.parents.length > 0">
                <div *ngFor="let parentRelation of student.parents" class="parent-item">
                  <div class="parent-avatar">
                    <img *ngIf="parentRelation?.parent?.profileImage" [src]="parentRelation?.parent?.profileImage?.filePath" alt="Parent Photo">
                    <mat-icon *ngIf="!parentRelation?.parent?.profileImage">account_circle</mat-icon>
                  </div>
                  <div class="parent-details">
                    <h3>{{ parentRelation?.parent?.firstName }} {{ parentRelation?.parent?.lastName }}</h3>
                    <p>{{
                      parentRelation?.relationType === 0 ? 'Father' :
                      parentRelation?.relationType === 1 ? 'Mother' :
                      parentRelation?.relationType === 2 ? 'Guardian' : 'Other'
                    }}</p>
                    <p *ngIf="parentRelation?.isPrimaryContact" class="primary-contact">Primary Contact</p>
                    <div class="parent-contact">
                      <p><mat-icon>email</mat-icon> {{ parentRelation?.parent?.email || 'N/A' }}</p>
                      <p><mat-icon>phone</mat-icon> {{ parentRelation?.parent?.phone || 'N/A' }}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div *ngIf="!student.parents || student.parents.length === 0" class="no-data">
                <p>No parent information available</p>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card-content>
    </mat-card>
  </div>
</div>
