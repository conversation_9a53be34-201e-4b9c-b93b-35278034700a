<div class="sections-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>
        <mat-toolbar color="primary">
          <span>{{ 'SECTIONS.TITLE' | translate }}</span>
          <span class="spacer"></span>
          <button mat-raised-button color="accent" (click)="openCreateDialog()">
            <mat-icon>add</mat-icon>
            {{ 'SECTIONS.ADD_SECTION' | translate }}
          </button>
        </mat-toolbar>
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <!-- Search and Filter Section -->
      <div class="filter-section">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>{{ 'COMMON.SEARCH' | translate }}</mat-label>
          <input matInput 
                 [(ngModel)]="searchTerm" 
                 (keyup.enter)="onSearch()"
                 placeholder="{{ 'SECTIONS.SEARCH_PLACEHOLDER' | translate }}">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>{{ 'SECTIONS.GRADE' | translate }}</mat-label>
          <mat-select [(ngModel)]="selectedGrade" (selectionChange)="onSearch()">
            <mat-option value="">{{ 'COMMON.ALL' | translate }}</mat-option>
            <mat-option *ngFor="let grade of grades" [value]="grade.id">
              {{ grade.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>{{ 'SECTIONS.ACADEMIC_YEAR' | translate }}</mat-label>
          <mat-select [(ngModel)]="selectedAcademicYear" (selectionChange)="onSearch()">
            <mat-option value="">{{ 'COMMON.ALL' | translate }}</mat-option>
            <mat-option *ngFor="let year of academicYears" [value]="year.id">
              {{ year.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>{{ 'SECTIONS.TYPE' | translate }}</mat-label>
          <mat-select [(ngModel)]="selectedType" (selectionChange)="onSearch()">
            <mat-option value="">{{ 'COMMON.ALL' | translate }}</mat-option>
            <mat-option *ngFor="let type of sectionTypes" [value]="type.value">
              {{ type.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>{{ 'SECTIONS.MEDIUM' | translate }}</mat-label>
          <mat-select [(ngModel)]="selectedMedium" (selectionChange)="onSearch()">
            <mat-option value="">{{ 'COMMON.ALL' | translate }}</mat-option>
            <mat-option *ngFor="let medium of mediums" [value]="medium.value">
              {{ medium.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-raised-button color="primary" (click)="onSearch()">
          <mat-icon>search</mat-icon>
          {{ 'COMMON.SEARCH' | translate }}
        </button>

        <button mat-button (click)="clearFilters()">
          <mat-icon>clear</mat-icon>
          {{ 'COMMON.CLEAR' | translate }}
        </button>
      </div>

      <!-- Data Table -->
      <div class="table-container">
        <table mat-table [dataSource]="sections" class="sections-table" matSort>
          <!-- Code Column -->
          <ng-container matColumnDef="code">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'SECTIONS.CODE' | translate }}
            </th>
            <td mat-cell *matCellDef="let section">
              <span class="section-code">{{ section.code }}</span>
            </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'SECTIONS.NAME' | translate }}
            </th>
            <td mat-cell *matCellDef="let section">
              <div class="section-info">
                <span class="section-name">{{ section.name }}</span>
                <small class="section-description">{{ section.description }}</small>
              </div>
            </td>
          </ng-container>

          <!-- Grade Column -->
          <ng-container matColumnDef="grade">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'SECTIONS.GRADE' | translate }}
            </th>
            <td mat-cell *matCellDef="let section">
              <span class="grade-name">{{ section.gradeName }}</span>
            </td>
          </ng-container>

          <!-- Type Column -->
          <ng-container matColumnDef="type">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'SECTIONS.TYPE' | translate }}
            </th>
            <td mat-cell *matCellDef="let section">
              <mat-chip class="type-chip">{{ getTypeDisplay(section.type) }}</mat-chip>
            </td>
          </ng-container>

          <!-- Medium Column -->
          <ng-container matColumnDef="medium">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'SECTIONS.MEDIUM' | translate }}
            </th>
            <td mat-cell *matCellDef="let section">
              <mat-chip class="medium-chip">{{ getMediumDisplay(section.medium) }}</mat-chip>
            </td>
          </ng-container>

          <!-- Capacity Column -->
          <ng-container matColumnDef="capacity">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'SECTIONS.CAPACITY' | translate }}
            </th>
            <td mat-cell *matCellDef="let section">
              <div class="capacity-info">
                <div class="capacity-text">
                  {{ section.currentEnrollment }} / {{ section.capacity }}
                </div>
                <mat-progress-bar 
                  [value]="getCapacityPercentage(section)"
                  [color]="getCapacityColor(getCapacityPercentage(section))"
                  mode="determinate">
                </mat-progress-bar>
                <small class="capacity-percentage">
                  {{ getCapacityPercentage(section) | number:'1.0-0' }}%
                </small>
              </div>
            </td>
          </ng-container>

          <!-- Classroom Column -->
          <ng-container matColumnDef="classroom">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'SECTIONS.CLASSROOM' | translate }}
            </th>
            <td mat-cell *matCellDef="let section">
              <div class="classroom-info">
                <span class="classroom-name">{{ section.classroom }}</span>
                <small class="room-number">{{ section.roomNumber }}</small>
              </div>
            </td>
          </ng-container>

          <!-- Academic Year Column -->
          <ng-container matColumnDef="academicYear">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'SECTIONS.ACADEMIC_YEAR' | translate }}
            </th>
            <td mat-cell *matCellDef="let section">
              <span class="academic-year">{{ section.academicYearName }}</span>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="isActive">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'COMMON.STATUS' | translate }}
            </th>
            <td mat-cell *matCellDef="let section">
              <mat-slide-toggle 
                [checked]="section.isActive"
                (change)="toggleSectionStatus(section)"
                [color]="'primary'">
              </mat-slide-toggle>
              <span class="status-text" [class.active]="section.isActive" [class.inactive]="!section.isActive">
                {{ section.isActive ? ('COMMON.ACTIVE' | translate) : ('COMMON.INACTIVE' | translate) }}
              </span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'COMMON.ACTIONS' | translate }}
            </th>
            <td mat-cell *matCellDef="let section">
              <div class="action-buttons">
                <button mat-icon-button 
                        color="primary" 
                        (click)="openEditDialog(section)"
                        matTooltip="{{ 'COMMON.EDIT' | translate }}">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button 
                        color="warn" 
                        (click)="deleteSection(section)"
                        matTooltip="{{ 'COMMON.DELETE' | translate }}">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- Loading Indicator -->
        <div *ngIf="loading" class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
          <p>{{ 'COMMON.LOADING' | translate }}</p>
        </div>

        <!-- No Data Message -->
        <div *ngIf="!loading && sections.length === 0" class="no-data-container">
          <mat-icon class="no-data-icon">class</mat-icon>
          <h3>{{ 'SECTIONS.NO_SECTIONS_FOUND' | translate }}</h3>
          <p>{{ 'SECTIONS.NO_SECTIONS_MESSAGE' | translate }}</p>
          <button mat-raised-button color="primary" (click)="openCreateDialog()">
            <mat-icon>add</mat-icon>
            {{ 'SECTIONS.CREATE_FIRST_SECTION' | translate }}
          </button>
        </div>
      </div>

      <!-- Pagination -->
      <mat-paginator 
        [length]="totalCount"
        [pageSize]="pageSize"
        [pageSizeOptions]="[5, 10, 25, 50]"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
