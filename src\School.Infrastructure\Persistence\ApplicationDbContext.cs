using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using System.Reflection;

using School.Application.Common.Interfaces;
using School.Domain.Common;
using School.Domain.Entities;
using School.Infrastructure.Identity;

namespace School.Infrastructure.Persistence;

public class ApplicationDbContext : IdentityDbContext<ApplicationUser, IdentityRole, string>, IApplicationDbContext
{
    private readonly ICurrentUserService? _currentUserService;
    private readonly ITenantContext? _tenantContext;

    public ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        ICurrentUserService? currentUserService = null,
        ITenantContext? tenantContext = null) : base(options)
    {
        _currentUserService = currentUserService;
        _tenantContext = tenantContext;
    }

    // Multi-tenant organization entities
    public DbSet<Organization> Organizations => Set<Organization>();
    public DbSet<OrganizationUser> OrganizationUsers => Set<OrganizationUser>();
    public DbSet<OrganizationSubscription> OrganizationSubscriptions => Set<OrganizationSubscription>();
    public DbSet<OrganizationSettings> OrganizationSettings => Set<OrganizationSettings>();


    // Authentication entities
    public DbSet<UserRefreshToken> UserRefreshTokens => Set<UserRefreshToken>();
    public DbSet<UserLoginHistory> UserLoginHistory => Set<UserLoginHistory>();
    public DbSet<Content> Contents => Set<Content>();
    public DbSet<ContentTranslation> ContentTranslations => Set<ContentTranslation>();
    public DbSet<MediaItem> MediaItems => Set<MediaItem>();
    public DbSet<Notice> Notices => Set<Notice>();
    public DbSet<NoticeTranslation> NoticeTranslations => Set<NoticeTranslation>();
    public DbSet<TuitionFee> TuitionFees => Set<TuitionFee>();
    public DbSet<TuitionFeeTranslation> TuitionFeeTranslations => Set<TuitionFeeTranslation>();
    public DbSet<HostelFacility> HostelFacilities => Set<HostelFacility>();
    public DbSet<HostelFacilityTranslation> HostelFacilityTranslations => Set<HostelFacilityTranslation>();
    public DbSet<HostelImage> HostelImages => Set<HostelImage>();
    public DbSet<Event> Events => Set<Event>();
    public DbSet<EventTranslation> EventTranslations => Set<EventTranslation>();
    public DbSet<EventImage> EventImages => Set<EventImage>();
    public DbSet<EventRegistration> EventRegistrations => Set<EventRegistration>();
    public DbSet<Alumni> Alumni => Set<Alumni>();
    public DbSet<AlumniTranslation> AlumniTranslations => Set<AlumniTranslation>();
    public DbSet<AlumniTestimonial> AlumniTestimonials => Set<AlumniTestimonial>();
    public DbSet<AlumniTestimonialTranslation> AlumniTestimonialTranslations => Set<AlumniTestimonialTranslation>();
    public DbSet<Faculty> Faculty => Set<Faculty>();
    public DbSet<FacultyTranslation> FacultyTranslations => Set<FacultyTranslation>();
    public DbSet<FacultyEducation> FacultyEducation => Set<FacultyEducation>();
    public DbSet<FacultySpecialization> FacultySpecializations => Set<FacultySpecialization>();
    public DbSet<FacultyCourse> FacultyCourses => Set<FacultyCourse>();
    public DbSet<FacultyPublication> FacultyPublications => Set<FacultyPublication>();
    public DbSet<FacultyAward> FacultyAwards => Set<FacultyAward>();
    public DbSet<FacultyEducationTranslation> FacultyEducationTranslations => Set<FacultyEducationTranslation>();
    public DbSet<FacultySpecializationTranslation> FacultySpecializationTranslations => Set<FacultySpecializationTranslation>();
    public DbSet<Department> Departments => Set<Department>();
    public DbSet<DepartmentTranslation> DepartmentTranslations => Set<DepartmentTranslation>();
    public DbSet<AcademicYear> AcademicYears => Set<AcademicYear>();
    public DbSet<AcademicYearTranslation> AcademicYearTranslations => Set<AcademicYearTranslation>();
    public DbSet<Term> Terms => Set<Term>();
    public DbSet<TermTranslation> TermTranslations => Set<TermTranslation>();
    public DbSet<AcademicCalendar> AcademicCalendars => Set<AcademicCalendar>();
    public DbSet<AcademicCalendarTranslation> AcademicCalendarTranslations => Set<AcademicCalendarTranslation>();
    public DbSet<Holiday> Holidays => Set<Holiday>();
    public DbSet<HolidayTranslation> HolidayTranslations => Set<HolidayTranslation>();

    // Grade & Section Management (Sprint 3)
    public DbSet<Grade> Grades => Set<Grade>();
    public DbSet<GradeTranslation> GradeTranslations => Set<GradeTranslation>();
    public DbSet<Section> Sections => Set<Section>();
    public DbSet<SectionTranslation> SectionTranslations => Set<SectionTranslation>();
    public DbSet<ClassTeacher> ClassTeachers => Set<ClassTeacher>();

    public DbSet<Student> Students => Set<Student>();
    public DbSet<Parent> Parents => Set<Parent>();
    public DbSet<StudentParent> StudentParents => Set<StudentParent>();
    public DbSet<StudentAttendance> StudentAttendances => Set<StudentAttendance>();
    public DbSet<StudentFee> StudentFees => Set<StudentFee>();
    public DbSet<StudentResult> StudentResults => Set<StudentResult>();
    public DbSet<StudentLeave> StudentLeaves => Set<StudentLeave>();
    public DbSet<StudentAcademicHistory> StudentAcademicHistories => Set<StudentAcademicHistory>();
    public DbSet<Career> Careers => Set<Career>();
    public DbSet<CareerTranslation> CareerTranslations => Set<CareerTranslation>();
    public DbSet<CareerApplication> CareerApplications => Set<CareerApplication>();

    // Tenant Setup
    public DbSet<TenantSetupStep> TenantSetupSteps => Set<TenantSetupStep>();

    // Campus Life - Clubs
    public DbSet<Club> Clubs => Set<Club>();
    public DbSet<ClubTranslation> ClubTranslations => Set<ClubTranslation>();
    public DbSet<ClubAdvisor> ClubAdvisors => Set<ClubAdvisor>();
    public DbSet<ClubLeader> ClubLeaders => Set<ClubLeader>();
    public DbSet<ClubActivity> ClubActivities => Set<ClubActivity>();
    public DbSet<ClubActivityTranslation> ClubActivityTranslations => Set<ClubActivityTranslation>();
    public DbSet<ClubAchievement> ClubAchievements => Set<ClubAchievement>();
    public DbSet<ClubAchievementTranslation> ClubAchievementTranslations => Set<ClubAchievementTranslation>();
    public DbSet<ClubEvent> ClubEvents => Set<ClubEvent>();
    public DbSet<ClubEventTranslation> ClubEventTranslations => Set<ClubEventTranslation>();
    public DbSet<ClubGalleryItem> ClubGalleryItems => Set<ClubGalleryItem>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Apply entity configurations from assembly
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

        // Configure Identity tables
        base.OnModelCreating(modelBuilder);

        // Customize Identity table names
        modelBuilder.Entity<ApplicationUser>().ToTable("Users");
        modelBuilder.Entity<IdentityRole>().ToTable("Roles");
        modelBuilder.Entity<IdentityUserRole<string>>().ToTable("UserRoles");
        modelBuilder.Entity<IdentityUserClaim<string>>().ToTable("UserClaims");
        modelBuilder.Entity<IdentityUserLogin<string>>().ToTable("UserLogins");
        modelBuilder.Entity<IdentityUserToken<string>>().ToTable("UserTokens");
        modelBuilder.Entity<IdentityRoleClaim<string>>().ToTable("RoleClaims");

        // Configure ApplicationUser properties
        modelBuilder.Entity<ApplicationUser>()
            .Property(u => u.FirstName)
            .HasMaxLength(50)
            .IsRequired();

        modelBuilder.Entity<ApplicationUser>()
            .Property(u => u.LastName)
            .HasMaxLength(50)
            .IsRequired();

        // Configure shadow properties for soft delete
        modelBuilder.Entity<ApplicationUser>().HasQueryFilter(u => !u.IsDeleted);

        // Configure global query filters for multi-tenancy
        ConfigureTenantQueryFilters(modelBuilder);
    }

    /// <summary>
    /// Configures global query filters for tenant isolation
    /// </summary>
    private void ConfigureTenantQueryFilters(ModelBuilder modelBuilder)
    {
        // Get all entity types that implement ITenantEntity
        var tenantEntityTypes = modelBuilder.Model.GetEntityTypes()
            .Where(et => typeof(ITenantEntity).IsAssignableFrom(et.ClrType))
            .ToList();

        foreach (var entityType in tenantEntityTypes)
        {
            // Create parameter for the entity
            var parameter = Expression.Parameter(entityType.ClrType, "e");

            // Create property access for TenantId
            var tenantIdProperty = Expression.Property(parameter, nameof(ITenantEntity.TenantId));

            // Create method call to get current tenant ID
            var getCurrentTenantMethod = typeof(ApplicationDbContext)
                .GetMethod("GetCurrentTenantId", BindingFlags.NonPublic | BindingFlags.Instance);

            var getCurrentTenantCall = Expression.Call(Expression.Constant(this), getCurrentTenantMethod!);

            // Create the filter expression: e.TenantId == GetCurrentTenantId()
            var equalExpression = Expression.Equal(tenantIdProperty, getCurrentTenantCall);
            var filterExpression = equalExpression;

            // Create lambda expression
            var lambda = Expression.Lambda(filterExpression, parameter);

            // Apply the filter
            entityType.SetQueryFilter(lambda);
        }
    }



    /// <summary>
    /// Executes a query without tenant filtering (for system operations)
    /// </summary>
    public async Task<T> ExecuteWithoutTenantFilterAsync<T>(Func<Task<T>> operation)
    {
        // Temporarily clear tenant context
        var originalTenantId = _tenantContext?.GetCurrentTenantId();
        _tenantContext?.ClearTenantContext();

        try
        {
            return await operation();
        }
        finally
        {
            // Restore original tenant context
            if (originalTenantId.HasValue)
            {
                _tenantContext?.SetCurrentTenantId(originalTenantId);
            }
        }
    }

    /// <summary>
    /// Executes a query without tenant filtering (synchronous version)
    /// </summary>
    public T ExecuteWithoutTenantFilter<T>(Func<T> operation)
    {
        // Temporarily clear tenant context
        var originalTenantId = _tenantContext?.GetCurrentTenantId();
        _tenantContext?.ClearTenantContext();

        try
        {
            return operation();
        }
        finally
        {
            // Restore original tenant context
            if (originalTenantId.HasValue)
            {
                _tenantContext?.SetCurrentTenantId(originalTenantId);
            }
        }
    }



    /// <summary>
    /// Gets the current tenant ID for query filtering
    /// This method is called by the global query filter
    /// </summary>
    private Guid GetCurrentTenantId()
    {
        // Get tenant ID from current user service
        return _currentUserService?.TenantId ?? Guid.Empty;
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Apply audit information if not already handled by UnitOfWork
        ApplyAuditInformation();

        return await base.SaveChangesAsync(cancellationToken);
    }

    private void ApplyAuditInformation()
    {
        string? userId = _currentUserService?.UserId?.ToString();

        // Handle BaseEntity audit properties
        foreach (var entry in ChangeTracker.Entries<BaseEntity>())
        {
            // Skip if audit fields are already set (e.g., by Repository methods)
            switch (entry.State)
            {
                case EntityState.Added:
                    if (entry.Entity.CreatedBy == null)
                    {
                        entry.Entity.CreatedAt = DateTime.UtcNow;
                        entry.Entity.CreatedBy = userId;
                    }
                    break;

                case EntityState.Modified:
                    if (entry.Entity.LastModifiedBy == null)
                    {
                        entry.Entity.LastModifiedAt = DateTime.UtcNow;
                        entry.Entity.LastModifiedBy = userId;
                    }
                    break;

                case EntityState.Deleted:
                    // Implement soft delete if not already handled
                    entry.State = EntityState.Modified;
                    entry.Entity.IsDeleted = true;
                    entry.Entity.LastModifiedAt = DateTime.UtcNow;
                    entry.Entity.LastModifiedBy = userId;
                    break;
            }
        }

        // Handle ApplicationUser audit properties
        foreach (var entry in ChangeTracker.Entries<ApplicationUser>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    if (entry.Entity.CreatedBy == null)
                    {
                        entry.Entity.CreatedAt = DateTime.UtcNow;
                        entry.Entity.CreatedBy = userId;
                    }
                    break;

                case EntityState.Modified:
                    if (entry.Entity.LastModifiedBy == null)
                    {
                        entry.Entity.LastModifiedAt = DateTime.UtcNow;
                        entry.Entity.LastModifiedBy = userId;
                    }
                    break;

                case EntityState.Deleted:
                    // Implement soft delete if not already handled
                    entry.State = EntityState.Modified;
                    entry.Entity.IsDeleted = true;
                    entry.Entity.LastModifiedAt = DateTime.UtcNow;
                    entry.Entity.LastModifiedBy = userId;
                    break;
            }
        }
    }
}
