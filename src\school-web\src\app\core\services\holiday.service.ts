import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ApiResponse } from '../models/api-response.model';
import { PagedResult } from '../models/paged-result.model';
import {
  Holiday,
  CreateHoliday,
  UpdateHoliday,
  HolidayFilter,
  HolidayEvent,
  HolidayStatistics,
  CreateHolidayTranslation,
  UpdateHolidayTranslation,
  HolidayTranslation,
  ValidateHolidayDatesRequest,
  GenerateRecurringHolidaysRequest,
  UpdateRecurringHolidayRequest
} from '../models/holiday.model';

@Injectable({
  providedIn: 'root'
})
export class HolidayService {
  private readonly apiUrl = `${environment.apiUrl}/holidays`;

  constructor(private http: HttpClient) {}

  // Holiday CRUD operations
  getHolidays(filter: HolidayFilter): Observable<ApiResponse<PagedResult<Holiday>>> {
    let params = new HttpParams()
      .set('page', filter.page.toString())
      .set('pageSize', filter.pageSize.toString());

    if (filter.sortBy) {
      params = params.set('sortBy', filter.sortBy);
    }
    if (filter.sortDirection) {
      params = params.set('sortDirection', filter.sortDirection);
    }
    if (filter.name) {
      params = params.set('name', filter.name);
    }
    if (filter.type !== undefined) {
      params = params.set('type', filter.type.toString());
    }
    if (filter.academicYearId) {
      params = params.set('academicYearId', filter.academicYearId);
    }
    if (filter.termId) {
      params = params.set('termId', filter.termId);
    }
    if (filter.startDate) {
      params = params.set('startDate', filter.startDate.toISOString());
    }
    if (filter.endDate) {
      params = params.set('endDate', filter.endDate.toISOString());
    }
    if (filter.isActive !== undefined) {
      params = params.set('isActive', filter.isActive.toString());
    }
    if (filter.isPublic !== undefined) {
      params = params.set('isPublic', filter.isPublic.toString());
    }
    if (filter.isRecurring !== undefined) {
      params = params.set('isRecurring', filter.isRecurring.toString());
    }

    return this.http.get<ApiResponse<PagedResult<Holiday>>>(this.apiUrl, { params });
  }

  getHolidayById(id: string): Observable<ApiResponse<Holiday>> {
    return this.http.get<ApiResponse<Holiday>>(`${this.apiUrl}/${id}`);
  }

  getHolidaysByAcademicYear(academicYearId: string): Observable<ApiResponse<Holiday[]>> {
    return this.http.get<ApiResponse<Holiday[]>>(`${this.apiUrl}/academic-year/${academicYearId}`);
  }

  getHolidaysByTerm(termId: string): Observable<ApiResponse<Holiday[]>> {
    return this.http.get<ApiResponse<Holiday[]>>(`${this.apiUrl}/term/${termId}`);
  }

  getHolidaysByDateRange(startDate: Date, endDate: Date): Observable<ApiResponse<Holiday[]>> {
    const params = new HttpParams()
      .set('startDate', startDate.toISOString())
      .set('endDate', endDate.toISOString());

    return this.http.get<ApiResponse<Holiday[]>>(`${this.apiUrl}/date-range`, { params });
  }

  getHolidayEvents(startDate: Date, endDate: Date, academicYearId?: string, termId?: string): Observable<ApiResponse<HolidayEvent[]>> {
    let params = new HttpParams()
      .set('startDate', startDate.toISOString())
      .set('endDate', endDate.toISOString());

    if (academicYearId) {
      params = params.set('academicYearId', academicYearId);
    }
    if (termId) {
      params = params.set('termId', termId);
    }

    return this.http.get<ApiResponse<HolidayEvent[]>>(`${this.apiUrl}/events`, { params });
  }

  getOverlappingHolidays(academicYearId?: string, termId?: string, startDate?: Date, endDate?: Date, excludeId?: string): Observable<ApiResponse<Holiday[]>> {
    let params = new HttpParams();

    if (academicYearId) {
      params = params.set('academicYearId', academicYearId);
    }
    if (termId) {
      params = params.set('termId', termId);
    }
    if (startDate) {
      params = params.set('startDate', startDate.toISOString());
    }
    if (endDate) {
      params = params.set('endDate', endDate.toISOString());
    }
    if (excludeId) {
      params = params.set('excludeId', excludeId);
    }

    return this.http.get<ApiResponse<Holiday[]>>(`${this.apiUrl}/overlapping`, { params });
  }

  getHolidayStatistics(academicYearId?: string, termId?: string): Observable<ApiResponse<HolidayStatistics>> {
    let params = new HttpParams();

    if (academicYearId) {
      params = params.set('academicYearId', academicYearId);
    }
    if (termId) {
      params = params.set('termId', termId);
    }

    return this.http.get<ApiResponse<HolidayStatistics>>(`${this.apiUrl}/statistics`, { params });
  }

  getHolidayCount(startDate: Date, endDate: Date, academicYearId?: string, termId?: string): Observable<ApiResponse<{ count: number }>> {
    let params = new HttpParams()
      .set('startDate', startDate.toISOString())
      .set('endDate', endDate.toISOString());

    if (academicYearId) {
      params = params.set('academicYearId', academicYearId);
    }
    if (termId) {
      params = params.set('termId', termId);
    }

    return this.http.get<ApiResponse<{ count: number }>>(`${this.apiUrl}/count`, { params });
  }

  createHoliday(holiday: CreateHoliday): Observable<ApiResponse<string>> {
    return this.http.post<ApiResponse<string>>(this.apiUrl, holiday);
  }

  updateHoliday(id: string, holiday: UpdateHoliday): Observable<ApiResponse<void>> {
    return this.http.put<ApiResponse<void>>(`${this.apiUrl}/${id}`, holiday);
  }

  deleteHoliday(id: string): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  activateHoliday(id: string): Observable<ApiResponse<void>> {
    return this.http.patch<ApiResponse<void>>(`${this.apiUrl}/${id}/activate`, {});
  }

  deactivateHoliday(id: string): Observable<ApiResponse<void>> {
    return this.http.patch<ApiResponse<void>>(`${this.apiUrl}/${id}/deactivate`, {});
  }

  validateHolidayDates(request: ValidateHolidayDatesRequest): Observable<ApiResponse<{ isValid: boolean }>> {
    return this.http.post<ApiResponse<{ isValid: boolean }>>(`${this.apiUrl}/validate`, request);
  }

  generateRecurringHolidays(id: string, request: GenerateRecurringHolidaysRequest): Observable<ApiResponse<Holiday[]>> {
    return this.http.post<ApiResponse<Holiday[]>>(`${this.apiUrl}/${id}/generate-recurring`, request);
  }

  updateRecurringHolidaySeries(id: string, request: UpdateRecurringHolidayRequest): Observable<ApiResponse<void>> {
    return this.http.put<ApiResponse<void>>(`${this.apiUrl}/${id}/recurring-series`, request);
  }

  // Translation management
  getTranslations(holidayId: string): Observable<ApiResponse<HolidayTranslation[]>> {
    return this.http.get<ApiResponse<HolidayTranslation[]>>(`${this.apiUrl}/${holidayId}/translations`);
  }

  addTranslation(holidayId: string, translation: CreateHolidayTranslation): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/${holidayId}/translations`, translation);
  }

  updateTranslation(holidayId: string, languageCode: string, translation: UpdateHolidayTranslation): Observable<ApiResponse<void>> {
    return this.http.put<ApiResponse<void>>(`${this.apiUrl}/${holidayId}/translations/${languageCode}`, translation);
  }

  deleteTranslation(holidayId: string, languageCode: string): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${holidayId}/translations/${languageCode}`);
  }

  // Utility methods
  getHolidayTypeOptions() {
    return [
      { value: 0, label: 'National', color: '#FF5722' },
      { value: 1, label: 'Religious', color: '#9C27B0' },
      { value: 2, label: 'Cultural', color: '#FF9800' },
      { value: 3, label: 'Academic', color: '#2196F3' },
      { value: 4, label: 'Administrative', color: '#607D8B' },
      { value: 5, label: 'Seasonal', color: '#4CAF50' },
      { value: 6, label: 'Custom', color: '#795548' }
    ];
  }

  getRecurrenceTypeOptions() {
    return [
      { value: 0, label: 'None' },
      { value: 1, label: 'Daily' },
      { value: 2, label: 'Weekly' },
      { value: 3, label: 'Monthly' },
      { value: 4, label: 'Yearly' },
      { value: 5, label: 'Custom' }
    ];
  }

  getColorOptions() {
    return [
      '#FF5722', '#E91E63', '#9C27B0', '#673AB7',
      '#3F51B5', '#2196F3', '#03A9F4', '#00BCD4',
      '#009688', '#4CAF50', '#8BC34A', '#CDDC39',
      '#FFEB3B', '#FFC107', '#FF9800', '#FF5722',
      '#795548', '#9E9E9E', '#607D8B'
    ];
  }
}
