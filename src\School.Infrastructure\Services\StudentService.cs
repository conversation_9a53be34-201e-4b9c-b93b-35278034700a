using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Student;
using School.Domain.Entities;
using School.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Services
{
    public class StudentService : IStudentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<StudentService> _logger;

        public StudentService(
            IUnitOfWork unitOfWork,
            ICurrentUserService currentUserService,
            ILogger<StudentService> logger)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        #region Student Management

        public async Task<(IEnumerable<StudentDto> Students, int TotalCount)> GetAllStudentsAsync(StudentFilterDto filter)
        {
            var repository = _unitOfWork.Repository<Student>();
            var query = repository.AsQueryable("ProfileImage", "ClassTeacher");

            // Apply filters
            if (!string.IsNullOrEmpty(filter.StudentId))
            {
                query = query.Where(s => s.StudentId.Contains(filter.StudentId));
            }

            if (!string.IsNullOrEmpty(filter.Name))
            {
                query = query.Where(s => s.FirstName.Contains(filter.Name) || s.LastName.Contains(filter.Name));
            }

            if (filter.Grade.HasValue)
            {
                query = query.Where(s => s.CurrentGrade == filter.Grade.Value);
            }

            if (!string.IsNullOrEmpty(filter.Section))
            {
                query = query.Where(s => s.Section == filter.Section);
            }

            if (filter.Medium.HasValue)
            {
                query = query.Where(s => s.Medium == filter.Medium.Value);
            }

            if (filter.Shift.HasValue)
            {
                query = query.Where(s => s.Shift == filter.Shift.Value);
            }

            if (filter.AcademicYear.HasValue)
            {
                query = query.Where(s => s.AcademicYear == filter.AcademicYear.Value);
            }

            if (filter.RollNumber.HasValue)
            {
                query = query.Where(s => s.RollNumber == filter.RollNumber.Value);
            }

            if (filter.AdmissionYear.HasValue)
            {
                query = query.Where(s => s.AdmissionYear == filter.AdmissionYear.Value);
            }

            if (filter.ClassTeacherId.HasValue)
            {
                query = query.Where(s => s.ClassTeacherId == filter.ClassTeacherId.Value);
            }

            if (filter.IsActive.HasValue)
            {
                query = query.Where(s => s.IsActive == filter.IsActive.Value);
            }

            if (filter.IsHosteler.HasValue)
            {
                query = query.Where(s => s.IsHosteler == filter.IsHosteler.Value);
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply sorting
            if (!string.IsNullOrEmpty(filter.SortBy))
            {
                query = filter.SortBy.ToLower() switch
                {
                    "firstname" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(s => s.FirstName) :
                        query.OrderBy(s => s.FirstName),
                    "lastname" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(s => s.LastName) :
                        query.OrderBy(s => s.LastName),
                    "grade" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(s => s.CurrentGrade) :
                        query.OrderBy(s => s.CurrentGrade),
                    "section" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(s => s.Section) :
                        query.OrderBy(s => s.Section),
                    "rollnumber" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(s => s.RollNumber) :
                        query.OrderBy(s => s.RollNumber),
                    "admissionyear" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(s => s.AdmissionYear) :
                        query.OrderBy(s => s.AdmissionYear),
                    "createdat" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(s => s.CreatedAt) :
                        query.OrderBy(s => s.CreatedAt),
                    _ => query.OrderBy(s => s.CurrentGrade).ThenBy(s => s.Section).ThenBy(s => s.RollNumber)
                };
            }
            else
            {
                query = query.OrderBy(s => s.CurrentGrade).ThenBy(s => s.Section).ThenBy(s => s.RollNumber);
            }

            // Apply pagination
            var students = await query
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(s => MapToStudentDto(s))
                .ToListAsync();

            return (students, totalCount);
        }

        public async Task<StudentDetailDto?> GetStudentByIdAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<Student>();
            var student = await repository.GetByIdAsync(id, new[] {
                "ProfileImage",
                "ClassTeacher",
                "Parents",
                "Parents.Parent",
                "Parents.Parent.ProfileImage"
            });

            if (student == null) return null;

            var studentDto = MapToStudentDetailDto(student);

            // Get recent attendance (last 30 days)
            var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);
            var attendanceRepository = _unitOfWork.Repository<StudentAttendance>();
            studentDto.RecentAttendance = await attendanceRepository.AsQueryable()
                .Where(a => a.StudentId == id && a.Date >= thirtyDaysAgo)
                .OrderByDescending(a => a.Date)
                .Take(10)
                .Select(a => MapToStudentAttendanceDto(a))
                .ToListAsync();

            // Get recent fees (current academic year)
            var feeRepository = _unitOfWork.Repository<StudentFee>();
            studentDto.RecentFees = await feeRepository.AsQueryable()
                .Where(f => f.StudentId == id)
                .OrderByDescending(f => f.DueDate)
                .Take(5)
                .Select(f => MapToStudentFeeDto(f))
                .ToListAsync();

            // Get recent results (current academic year)
            var resultRepository = _unitOfWork.Repository<StudentResult>();
            studentDto.RecentResults = await resultRepository.AsQueryable()
                .Where(r => r.StudentId == id && r.AcademicYear == student.AcademicYear)
                .OrderByDescending(r => r.CreatedAt)
                .Take(10)
                .Select(r => MapToStudentResultDto(r))
                .ToListAsync();

            // Get recent leaves
            var leaveRepository = _unitOfWork.Repository<StudentLeave>();
            studentDto.RecentLeaves = await leaveRepository.AsQueryable()
                .Where(l => l.StudentId == id)
                .OrderByDescending(l => l.StartDate)
                .Take(5)
                .Select(l => MapToStudentLeaveDto(l))
                .ToListAsync();

            // Get academic history
            var academicHistoryRepository = _unitOfWork.Repository<StudentAcademicHistory>();
            studentDto.AcademicHistory = await academicHistoryRepository.AsQueryable("ClassTeacher")
                .Where(h => h.StudentId == id)
                .OrderByDescending(h => h.AcademicYear)
                .Select(h => MapToStudentAcademicHistoryDto(h))
                .ToListAsync();

            // Calculate current year GPA if available
            var annualResults = await resultRepository.AsQueryable()
                .Where(r => r.StudentId == id &&
                           r.AcademicYear == student.AcademicYear &&
                           r.ExamType == ExamType.Annual)
                .ToListAsync();

            if (annualResults.Any())
            {
                studentDto.CurrentYearGPA = CalculateGPA(annualResults);
            }

            return studentDto;
        }

        public async Task<StudentDto?> GetStudentByStudentIdAsync(string studentId)
        {
            var repository = _unitOfWork.Repository<Student>();
            var students = await repository.FindAsync(
                s => s.StudentId == studentId,
                new[] { "ProfileImage", "ClassTeacher" });

            if (!students.Any()) return null;

            var student = students.First();
            return MapToStudentDto(student);
        }

        public async Task<StudentDto?> GetStudentByUserIdAsync(string userId)
        {
            _logger.LogInformation("Getting student by user ID: {UserId}", userId);

            var repository = _unitOfWork.Repository<Student>();
            var students = await repository.FindAsync(
                s => s.UserId == userId,
                new[] { "ProfileImage", "ClassTeacher" });

            if (!students.Any())
            {
                _logger.LogWarning("Student with user ID {UserId} not found", userId);
                return null;
            }

            var student = students.First();
            _logger.LogInformation("Found student with ID {StudentId} for user ID {UserId}", student.Id, userId);
            return MapToStudentDto(student);
        }

        public async Task<Guid> CreateStudentAsync(CreateStudentDto studentDto)
        {
            var repository = _unitOfWork.Repository<Student>();

            var student = new Student
            {
                StudentId = studentDto.StudentId,
                FirstName = studentDto.FirstName,
                LastName = studentDto.LastName,
                DateOfBirth = studentDto.DateOfBirth,
                Gender = studentDto.Gender,
                Email = studentDto.Email,
                Phone = studentDto.Phone,
                Address = studentDto.Address,
                EmergencyContactName = studentDto.EmergencyContactName,
                EmergencyContactPhone = studentDto.EmergencyContactPhone,
                EmergencyContactRelation = studentDto.EmergencyContactRelation,
                BloodGroup = studentDto.BloodGroup,
                MedicalConditions = studentDto.MedicalConditions,
                Allergies = studentDto.Allergies,
                CurrentGrade = studentDto.CurrentGrade,
                Section = studentDto.Section,
                Medium = studentDto.Medium,
                Shift = studentDto.Shift,
                AcademicYear = studentDto.AcademicYear,
                RollNumber = studentDto.RollNumber,
                AdmissionYear = studentDto.AdmissionYear,
                GraduationDate = studentDto.GraduationDate,
                IsActive = studentDto.IsActive,
                IsHosteler = studentDto.IsHosteler,
                UserId = studentDto.UserId,
                ClassTeacherId = studentDto.ClassTeacherId,
                ProfileImageId = studentDto.ProfileImageId,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.AddAsync(student, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            // Add parents if provided
            if (studentDto.Parents != null && studentDto.Parents.Any())
            {
                foreach (var parentDto in studentDto.Parents)
                {
                    await AddParentInternalAsync(student.Id, parentDto);
                }
            }

            _logger.LogInformation("Student created with ID {StudentId}", student.Id);
            return student.Id;
        }

        public async Task<bool> UpdateStudentAsync(Guid id, UpdateStudentDto studentDto)
        {
            var repository = _unitOfWork.Repository<Student>();
            var student = await repository.GetByIdAsync(id);

            if (student == null) return false;

            student.FirstName = studentDto.FirstName;
            student.LastName = studentDto.LastName;
            student.DateOfBirth = studentDto.DateOfBirth;
            student.Gender = studentDto.Gender;
            student.Email = studentDto.Email;
            student.Phone = studentDto.Phone;
            student.Address = studentDto.Address;
            student.EmergencyContactName = studentDto.EmergencyContactName;
            student.EmergencyContactPhone = studentDto.EmergencyContactPhone;
            student.EmergencyContactRelation = studentDto.EmergencyContactRelation;
            student.BloodGroup = studentDto.BloodGroup;
            student.MedicalConditions = studentDto.MedicalConditions;
            student.Allergies = studentDto.Allergies;
            student.CurrentGrade = studentDto.CurrentGrade;
            student.Section = studentDto.Section;
            student.Medium = studentDto.Medium;
            student.Shift = studentDto.Shift;
            student.AcademicYear = studentDto.AcademicYear;
            student.RollNumber = studentDto.RollNumber;
            student.AdmissionYear = studentDto.AdmissionYear;
            student.GraduationDate = studentDto.GraduationDate;
            student.IsActive = studentDto.IsActive;
            student.IsHosteler = studentDto.IsHosteler;
            student.ClassTeacherId = studentDto.ClassTeacherId;
            student.ProfileImageId = studentDto.ProfileImageId;
            student.UpdatedAt = DateTime.UtcNow;
            // LastModifiedBy and LastModifiedAt will be set by the repository

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.UpdateAsync(student, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Student updated with ID {StudentId}", student.Id);
            return true;
        }

        public async Task<bool> DeleteStudentAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<Student>();

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Student deleted with ID {StudentId}", id);
            return true;
        }

        #endregion

        #region Attendance Methods

        public async Task<IEnumerable<StudentAttendanceDto>> GetStudentAttendanceAsync(Guid studentId, string? fromDate = null, string? toDate = null)
        {
            var repository = _unitOfWork.Repository<StudentAttendance>();
            var query = repository.AsQueryable();

            query = query.Where(a => a.StudentId == studentId);

            if (!string.IsNullOrEmpty(fromDate) && DateTime.TryParse(fromDate, out var fromDateValue))
            {
                query = query.Where(a => a.Date >= fromDateValue);
            }

            if (!string.IsNullOrEmpty(toDate) && DateTime.TryParse(toDate, out var toDateValue))
            {
                query = query.Where(a => a.Date <= toDateValue);
            }

            var attendances = await query
                .OrderByDescending(a => a.Date)
                .Select(a => MapToStudentAttendanceDto(a))
                .ToListAsync();

            return attendances;
        }

        public async Task<Guid> CreateAttendanceAsync(CreateStudentAttendanceDto attendanceDto)
        {
            // Verify student exists
            var studentRepository = _unitOfWork.Repository<Student>();
            var student = await studentRepository.GetByIdAsync(attendanceDto.StudentId);

            if (student == null)
                throw new InvalidOperationException("Student not found");

            // Check if attendance record already exists for this date
            var attendanceRepository = _unitOfWork.Repository<StudentAttendance>();
            var existingAttendances = await attendanceRepository.FindAsync(
                a => a.StudentId == attendanceDto.StudentId &&
                     a.Date.Date == attendanceDto.Date.Date &&
                     a.Period == attendanceDto.Period);

            if (existingAttendances.Any())
                throw new InvalidOperationException("Attendance record already exists for this date and period");

            // Check if student is on approved leave for this date
            var leaveRepository = _unitOfWork.Repository<StudentLeave>();
            var approvedLeaves = await leaveRepository.FindAsync(
                l => l.StudentId == attendanceDto.StudentId &&
                     l.StartDate.Date <= attendanceDto.Date.Date &&
                     l.EndDate.Date >= attendanceDto.Date.Date &&
                     l.Status == LeaveStatus.Approved);

            var approvedLeave = approvedLeaves.FirstOrDefault();

            var attendance = new StudentAttendance
            {
                StudentId = attendanceDto.StudentId,
                Date = attendanceDto.Date,
                Status = approvedLeave != null ? AttendanceStatus.OnLeave : attendanceDto.Status,
                Remarks = attendanceDto.Remarks,
                RecordedBy = attendanceDto.RecordedBy,
                AcademicYear = student.AcademicYear,
                Grade = student.CurrentGrade,
                Section = student.Section,
                Period = attendanceDto.Period,
                SubjectCode = attendanceDto.SubjectCode,
                IsLeaveApproved = approvedLeave != null,
                LeaveId = approvedLeave?.Id,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await attendanceRepository.AddAsync(attendance, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Attendance created with ID {AttendanceId} for student {StudentId}",
                attendance.Id, attendanceDto.StudentId);
            return attendance.Id;
        }

        public async Task<bool> UpdateAttendanceAsync(Guid id, UpdateStudentAttendanceDto attendanceDto)
        {
            var repository = _unitOfWork.Repository<StudentAttendance>();
            var attendance = await repository.GetByIdAsync(id);

            if (attendance == null) return false;

            attendance.Status = attendanceDto.Status;
            attendance.Remarks = attendanceDto.Remarks;
            attendance.RecordedBy = attendanceDto.RecordedBy;
            attendance.UpdatedAt = DateTime.UtcNow;
            // LastModifiedBy and LastModifiedAt will be set by the repository

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.UpdateAsync(attendance, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Attendance updated with ID {AttendanceId}", attendance.Id);
            return true;
        }

        public async Task<bool> DeleteAttendanceAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<StudentAttendance>();

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Attendance deleted with ID {AttendanceId}", id);
            return true;
        }

        #endregion

        #region Fee Methods

        public async Task<IEnumerable<StudentFeeDto>> GetStudentFeesAsync(Guid studentId, int? academicYear = null)
        {
            var repository = _unitOfWork.Repository<StudentFee>();
            var query = repository.AsQueryable();

            query = query.Where(f => f.StudentId == studentId);

            if (academicYear.HasValue)
            {
                // Filter by academic year (assuming fees have academic year information)
                var startDate = new DateTime(academicYear.Value, 1, 1);
                var endDate = new DateTime(academicYear.Value, 12, 31);
                query = query.Where(f => f.DueDate >= startDate && f.DueDate <= endDate);
            }

            var fees = await query
                .OrderByDescending(f => f.DueDate)
                .Select(f => MapToStudentFeeDto(f))
                .ToListAsync();

            return fees;
        }

        public async Task<Guid> CreateFeeAsync(CreateStudentFeeDto feeDto)
        {
            // Verify student exists
            var studentRepository = _unitOfWork.Repository<Student>();
            var student = await studentRepository.GetByIdAsync(feeDto.StudentId);

            if (student == null)
                throw new InvalidOperationException("Student not found");

            var feeRepository = _unitOfWork.Repository<StudentFee>();

            var fee = new StudentFee
            {
                StudentId = feeDto.StudentId,
                Type = feeDto.Type,
                Description = feeDto.Description,
                Amount = feeDto.Amount,
                PaidAmount = feeDto.PaidAmount,
                DueAmount = feeDto.DueAmount,
                DueDate = feeDto.DueDate,
                PaidDate = feeDto.PaidDate,
                Status = feeDto.Status,
                TransactionId = feeDto.TransactionId,
                PaymentMethod = feeDto.PaymentMethod,
                Remarks = feeDto.Remarks,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await feeRepository.AddAsync(fee, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Fee created with ID {FeeId} for student {StudentId}",
                fee.Id, feeDto.StudentId);
            return fee.Id;
        }

        public async Task<bool> UpdateFeeAsync(Guid id, UpdateStudentFeeDto feeDto)
        {
            var repository = _unitOfWork.Repository<StudentFee>();
            var fee = await repository.GetByIdAsync(id);

            if (fee == null) return false;

            fee.PaidAmount = feeDto.PaidAmount;
            fee.DueAmount = feeDto.DueAmount;
            fee.PaidDate = feeDto.PaidDate;
            fee.Status = feeDto.Status;
            fee.TransactionId = feeDto.TransactionId;
            fee.PaymentMethod = feeDto.PaymentMethod;
            fee.Remarks = feeDto.Remarks;
            fee.UpdatedAt = DateTime.UtcNow;
            // LastModifiedBy and LastModifiedAt will be set by the repository

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.UpdateAsync(fee, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Fee updated with ID {FeeId}", fee.Id);
            return true;
        }

        public async Task<bool> DeleteFeeAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<StudentFee>();

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Fee deleted with ID {FeeId}", id);
            return true;
        }

        #endregion

        #region Result Methods

        public async Task<IEnumerable<StudentResultDto>> GetStudentResultsAsync(Guid studentId, string? academicYear = null, string? examType = null)
        {
            var repository = _unitOfWork.Repository<StudentResult>();
            var query = repository.AsQueryable();

            query = query.Where(r => r.StudentId == studentId);

            if (!string.IsNullOrEmpty(academicYear) && int.TryParse(academicYear, out var academicYearValue))
            {
                query = query.Where(r => r.AcademicYear == academicYearValue);
            }

            if (!string.IsNullOrEmpty(examType) && Enum.TryParse<ExamType>(examType, out var examTypeValue))
            {
                query = query.Where(r => r.ExamType == examTypeValue);
            }

            var results = await query
                .OrderByDescending(r => r.AcademicYear)
                .ThenBy(r => r.ExamType)
                .ThenBy(r => r.SubjectCode)
                .Select(r => MapToStudentResultDto(r))
                .ToListAsync();

            return results;
        }

        public async Task<decimal?> CalculateGPAAsync(Guid studentId, int academicYear, ExamType examType)
        {
            var repository = _unitOfWork.Repository<StudentResult>();
            var results = await repository.FindAsync(
                r => r.StudentId == studentId &&
                     r.AcademicYear == academicYear &&
                     r.ExamType == examType);

            if (!results.Any()) return null;

            return CalculateGPA(results.ToList());
        }

        private decimal CalculateGPA(List<StudentResult> results)
        {
            if (!results.Any()) return 0;

            decimal totalPoints = 0;
            decimal totalCredits = 0;

            foreach (var result in results)
            {
                // Convert marks to grade points (assuming a 4.0 scale)
                decimal gradePoints = 0;
                if (result.Marks >= 80) gradePoints = 4.0m;
                else if (result.Marks >= 70) gradePoints = 3.5m;
                else if (result.Marks >= 60) gradePoints = 3.0m;
                else if (result.Marks >= 50) gradePoints = 2.5m;
                else if (result.Marks >= 40) gradePoints = 2.0m;
                else if (result.Marks >= 33) gradePoints = 1.0m;

                // Assume each subject has a credit value (default to 1 if not specified)
                decimal credits = result.Credits ?? 1.0m;

                totalPoints += gradePoints * credits;
                totalCredits += credits;
            }

            return totalCredits > 0 ? Math.Round(totalPoints / totalCredits, 2) : 0;
        }

        public async Task<Guid> CreateResultAsync(CreateStudentResultDto resultDto)
        {
            // Verify student exists
            var studentRepository = _unitOfWork.Repository<Student>();
            var student = await studentRepository.GetByIdAsync(resultDto.StudentId);

            if (student == null)
                throw new InvalidOperationException("Student not found");

            // Check if result already exists for this subject and exam
            var resultRepository = _unitOfWork.Repository<StudentResult>();
            var existingResults = await resultRepository.FindAsync(
                r => r.StudentId == resultDto.StudentId &&
                     r.AcademicYear == resultDto.AcademicYear &&
                     r.ExamType == resultDto.ExamType &&
                     r.SubjectCode == resultDto.SubjectCode);

            if (existingResults.Any())
                throw new InvalidOperationException("Result already exists for this subject and exam");

            var result = new StudentResult
            {
                StudentId = resultDto.StudentId,
                AcademicYear = resultDto.AcademicYear,
                ExamType = resultDto.ExamType,
                SubjectCode = resultDto.SubjectCode,
                SubjectName = resultDto.SubjectName,
                Marks = resultDto.Marks,
                MaxMarks = resultDto.MaxMarks.ToString() ?? string.Empty,
                Grade = resultDto.Grade,
                Credits = resultDto.Credits,
                Remarks = resultDto.Remarks,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await resultRepository.AddAsync(result, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Result created with ID {ResultId} for student {StudentId}",
                result.Id, resultDto.StudentId);
            return result.Id;
        }

        public async Task<bool> UpdateResultAsync(Guid id, UpdateStudentResultDto resultDto)
        {
            var repository = _unitOfWork.Repository<StudentResult>();
            var result = await repository.GetByIdAsync(id);

            if (result == null) return false;

            result.Marks = resultDto.Marks;
            result.MaxMarks = resultDto.MaxMarks.ToString() ?? string.Empty;
            result.Grade = resultDto.Grade;
            result.Credits = resultDto.Credits;
            result.Remarks = resultDto.Remarks;
            result.UpdatedAt = DateTime.UtcNow;
            // LastModifiedBy and LastModifiedAt will be set by the repository

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.UpdateAsync(result, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Result updated with ID {ResultId}", result.Id);
            return true;
        }

        public async Task<bool> DeleteResultAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<StudentResult>();

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Result deleted with ID {ResultId}", id);
            return true;
        }

        #endregion

        #region Leave Methods

        public async Task<IEnumerable<StudentLeaveDto>> GetStudentLeavesAsync(Guid studentId, string? fromDate = null, string? toDate = null, LeaveStatus? status = null)
        {
            var repository = _unitOfWork.Repository<StudentLeave>();
            var query = repository.AsQueryable();

            query = query.Where(l => l.StudentId == studentId);

            if (!string.IsNullOrEmpty(fromDate) && DateTime.TryParse(fromDate, out var fromDateValue))
            {
                query = query.Where(l => l.StartDate >= fromDateValue);
            }

            if (!string.IsNullOrEmpty(toDate) && DateTime.TryParse(toDate, out var toDateValue))
            {
                query = query.Where(l => l.EndDate <= toDateValue);
            }

            if (status.HasValue)
            {
                query = query.Where(l => l.Status == status.Value);
            }

            var leaves = await query
                .OrderByDescending(l => l.StartDate)
                .Select(l => MapToStudentLeaveDto(l))
                .ToListAsync();

            return leaves;
        }

        public async Task<StudentLeaveDto?> GetLeaveByIdAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<StudentLeave>();
            var leave = await repository.GetByIdAsync(id);

            if (leave == null) return null;

            return MapToStudentLeaveDto(leave);
        }

        public async Task<Guid> CreateLeaveAsync(CreateStudentLeaveDto leaveDto)
        {
            // Verify student exists
            var studentRepository = _unitOfWork.Repository<Student>();
            var student = await studentRepository.GetByIdAsync(leaveDto.StudentId);

            if (student == null)
                throw new InvalidOperationException("Student not found");

            // Check for overlapping leaves
            var leaveRepository = _unitOfWork.Repository<StudentLeave>();
            var overlappingLeaves = await leaveRepository.FindAsync(l =>
                l.StudentId == leaveDto.StudentId &&
                !l.IsDeleted &&
                ((l.StartDate <= leaveDto.StartDate && l.EndDate >= leaveDto.StartDate) ||
                 (l.StartDate <= leaveDto.EndDate && l.EndDate >= leaveDto.EndDate) ||
                 (l.StartDate >= leaveDto.StartDate && l.EndDate <= leaveDto.EndDate)));

            if (overlappingLeaves.Any())
                throw new InvalidOperationException("There is already a leave application for this period");

            var leave = new StudentLeave
            {
                StudentId = leaveDto.StudentId,
                StartDate = leaveDto.StartDate,
                EndDate = leaveDto.EndDate,
                Type = leaveDto.Type,
                Reason = leaveDto.Reason,
                Status = LeaveStatus.Pending, // Always start as pending
                AttachmentPath = leaveDto.AttachmentPath,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await leaveRepository.AddAsync(leave, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Leave created with ID {LeaveId} for student {StudentId}",
                leave.Id, leaveDto.StudentId);
            return leave.Id;
        }

        public async Task<bool> UpdateLeaveStatusAsync(Guid id, UpdateStudentLeaveDto leaveDto)
        {
            var repository = _unitOfWork.Repository<StudentLeave>();
            var leave = await repository.GetByIdAsync(id);

            if (leave == null) return false;

            leave.Status = leaveDto.Status;
            leave.ApprovedBy = leaveDto.ApprovedBy;
            leave.ApprovalRemarks = leaveDto.ApprovalRemarks?.ToString() ?? string.Empty;
            leave.UpdatedAt = DateTime.UtcNow;
            // LastModifiedBy and LastModifiedAt will be set by the repository

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.UpdateAsync(leave, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Leave status updated with ID {LeaveId} to {Status}",
                leave.Id, leave.Status);
            return true;
        }

        public async Task<bool> DeleteLeaveAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<StudentLeave>();

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Leave deleted with ID {LeaveId}", id);
            return true;
        }

        #endregion

        #region Academic History Methods

        public async Task<IEnumerable<StudentAcademicHistoryDto>> GetStudentAcademicHistoryAsync(Guid studentId)
        {
            var repository = _unitOfWork.Repository<StudentAcademicHistory>();
            var academicHistories = await repository.FindAsync(
                h => h.StudentId == studentId,
                new[] { "ClassTeacher" });

            return academicHistories.Select(h => MapToStudentAcademicHistoryDto(h)).ToList();
        }

        public async Task<StudentAcademicHistoryDto?> GetAcademicHistoryByYearAsync(Guid studentId, int academicYear)
        {
            // TODO: This method needs to be updated to work with AcademicYear entities instead of int
            // For now, we'll return null to avoid compilation errors
            await Task.CompletedTask;
            return null;
        }

        public async Task<Guid> CreateAcademicHistoryAsync(CreateStudentAcademicHistoryDto academicHistoryDto)
        {
            // Verify student exists
            var studentRepository = _unitOfWork.Repository<Student>();
            var student = await studentRepository.GetByIdAsync(academicHistoryDto.StudentId);

            if (student == null)
                throw new InvalidOperationException("Student not found");

            // Check if academic history already exists for this academic year
            var academicHistoryRepository = _unitOfWork.Repository<StudentAcademicHistory>();
            var existingHistories = await academicHistoryRepository.FindAsync(
                h => h.StudentId == academicHistoryDto.StudentId &&
                     h.AcademicYearId == academicHistoryDto.AcademicYearId);

            if (existingHistories.Any())
                throw new InvalidOperationException("Academic history already exists for this academic year");

            var academicHistory = new StudentAcademicHistory
            {
                StudentId = academicHistoryDto.StudentId,
                AcademicYearId = academicHistoryDto.AcademicYearId,
                TermId = academicHistoryDto.TermId,
                Grade = academicHistoryDto.Grade,
                Section = academicHistoryDto.Section,
                Medium = academicHistoryDto.Medium,
                Shift = academicHistoryDto.Shift,
                RollNumber = academicHistoryDto.RollNumber,
                StudentIdForYear = academicHistoryDto.StudentIdForYear,
                FinalGPA = academicHistoryDto.FinalGPA,
                IsPromoted = academicHistoryDto.IsPromoted,
                ClassTeacherId = academicHistoryDto.ClassTeacherId,
                Remarks = academicHistoryDto.Remarks,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await academicHistoryRepository.AddAsync(academicHistory, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Academic history created with ID {AcademicHistoryId} for student {StudentId}",
                academicHistory.Id, academicHistoryDto.StudentId);
            return academicHistory.Id;
        }

        public async Task<bool> UpdateAcademicHistoryAsync(Guid id, UpdateStudentAcademicHistoryDto academicHistoryDto)
        {
            var repository = _unitOfWork.Repository<StudentAcademicHistory>();
            var academicHistory = await repository.GetByIdAsync(id);

            if (academicHistory == null) return false;

            academicHistory.Grade = academicHistoryDto.Grade;
            academicHistory.Section = academicHistoryDto.Section;
            academicHistory.RollNumber = academicHistoryDto.RollNumber;
            academicHistory.FinalGPA = academicHistoryDto.FinalGPA;
            academicHistory.IsPromoted = academicHistoryDto.IsPromoted;
            academicHistory.ClassTeacherId = academicHistoryDto.ClassTeacherId;
            academicHistory.Remarks = academicHistoryDto.Remarks;
            // LastModifiedBy and LastModifiedAt will be set by the repository

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.UpdateAsync(academicHistory, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Academic history updated with ID {AcademicHistoryId}", academicHistory.Id);
            return true;
        }

        public Task<bool> PromoteStudentAsync(Guid studentId, int fromAcademicYear, int toAcademicYear, int toGrade, string toSection, int toRollNumber, string toStudentId)
        {
            // TODO: This method needs to be completely rewritten to work with the new AcademicYear and Term entities
            // For now, we'll throw a NotImplementedException to avoid compilation errors
            throw new NotImplementedException("Student promotion functionality needs to be updated to work with new Academic Year management system");
        }

        #endregion

        #region Parent Association Methods

        public async Task<bool> AddParentAsync(Guid studentId, CreateStudentParentDto parentDto)
        {
            return await AddParentInternalAsync(studentId, parentDto);
        }

        private async Task<bool> AddParentInternalAsync(Guid studentId, CreateStudentParentDto parentDto)
        {
            // Verify student exists
            var studentRepository = _unitOfWork.Repository<Student>();
            var student = await studentRepository.GetByIdAsync(studentId);

            if (student == null)
            {
                _logger.LogWarning("Failed to add parent to student: Student with ID {StudentId} not found", studentId);
                return false;
            }

            // Check if parent exists by email or create new parent
            var parentRepository = _unitOfWork.Repository<Parent>();
            var existingParents = await parentRepository.FindAsync(p => p.Email == parentDto.Email);

            Parent parent;
            if (existingParents.Any())
            {
                parent = existingParents.First();
                _logger.LogInformation("Using existing parent with ID {ParentId} for student {StudentId}",
                    parent.Id, studentId);
            }
            else
            {
                // Create new parent
                parent = new Parent
                {
                    FirstName = parentDto.FirstName,
                    LastName = parentDto.LastName,
                    Gender = parentDto.Gender,
                    Email = parentDto.Email,
                    Phone = parentDto.Phone,
                    AlternatePhone = parentDto.AlternatePhone,
                    Address = parentDto.Address,
                    Occupation = parentDto.Occupation,
                    UserId = parentDto.UserId,
                    IsActive = true,
                    ProfileImageId = parentDto.ProfileImageId,
                    // CreatedAt and CreatedBy will be set by the repository
                };

                // Get the current user ID for audit trail
                var userId = _currentUserService.UserId?.ToString();

                // Pass the current user ID to the repository for audit trail
                await parentRepository.AddAsync(parent, userId);
                await _unitOfWork.SaveChangesAsync(userId);

                _logger.LogInformation("Created new parent with ID {ParentId} for student {StudentId}",
                    parent.Id, studentId);
            }

            // Check if relationship already exists
            var studentParentRepository = _unitOfWork.Repository<StudentParent>();
            var existingRelationships = await studentParentRepository.FindAsync(
                sp => sp.ParentId == parent.Id && sp.StudentId == studentId && !sp.IsDeleted);

            if (existingRelationships.Any())
            {
                _logger.LogWarning("Relationship between parent {ParentId} and student {StudentId} already exists",
                    parent.Id, studentId);
                return false;
            }

            // If this is set as primary contact, update any existing primary contacts for this student
            if (parentDto.IsPrimaryContact)
            {
                var existingPrimaryContacts = await studentParentRepository.FindAsync(
                    sp => sp.StudentId == studentId && sp.IsPrimaryContact && !sp.IsDeleted);

                foreach (var contact in existingPrimaryContacts)
                {
                    contact.IsPrimaryContact = false;
                    contact.UpdatedAt = DateTime.UtcNow;
                    // LastModifiedBy and LastModifiedAt will be set by the repository

                    // Get a new user ID for this specific update
                    var contactUpdateUserId = _currentUserService.UserId?.ToString();
                    await studentParentRepository.UpdateAsync(contact, contactUpdateUserId);
                }
            }

            var studentParent = new StudentParent
            {
                StudentId = studentId,
                ParentId = parent.Id,
                RelationType = parentDto.RelationType,
                IsPrimaryContact = parentDto.IsPrimaryContact,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Get the current user ID for audit trail
            var studentParentUserId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await studentParentRepository.AddAsync(studentParent, studentParentUserId);
            await _unitOfWork.SaveChangesAsync(studentParentUserId);

            _logger.LogInformation("Parent {ParentId} added to student {StudentId} with relation type {RelationType}",
                parent.Id, studentId, parentDto.RelationType);
            return true;
        }

        public async Task<bool> UpdateParentRelationAsync(Guid studentId, Guid parentId, ParentRelationType relationType, bool isPrimaryContact)
        {
            var studentParentRepository = _unitOfWork.Repository<StudentParent>();
            var relationships = await studentParentRepository.FindAsync(
                sp => sp.ParentId == parentId && sp.StudentId == studentId && !sp.IsDeleted);

            if (!relationships.Any())
            {
                _logger.LogWarning("Relationship between parent {ParentId} and student {StudentId} not found",
                    parentId, studentId);
                return false;
            }

            var relationship = relationships.First();

            // If this is set as primary contact, update any existing primary contacts for this student
            if (isPrimaryContact && !relationship.IsPrimaryContact)
            {
                var existingPrimaryContacts = await studentParentRepository.FindAsync(
                    sp => sp.StudentId == studentId && sp.IsPrimaryContact && sp.Id != relationship.Id && !sp.IsDeleted);

                foreach (var contact in existingPrimaryContacts)
                {
                    contact.IsPrimaryContact = false;
                    contact.UpdatedAt = DateTime.UtcNow;
                    // LastModifiedBy and LastModifiedAt will be set by the repository

                    // Get a new user ID for this specific update
                    var contactUpdateUserId = _currentUserService.UserId?.ToString();
                    await studentParentRepository.UpdateAsync(contact, contactUpdateUserId);
                }
            }

            // Update relationship
            relationship.RelationType = relationType;
            relationship.IsPrimaryContact = isPrimaryContact;
            relationship.UpdatedAt = DateTime.UtcNow;
            // LastModifiedBy and LastModifiedAt will be set by the repository

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await studentParentRepository.UpdateAsync(relationship, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Relationship between parent {ParentId} and student {StudentId} updated",
                parentId, studentId);
            return true;
        }

        public async Task<bool> RemoveParentAsync(Guid studentId, Guid parentId)
        {
            var studentParentRepository = _unitOfWork.Repository<StudentParent>();
            var relationships = await studentParentRepository.FindAsync(
                sp => sp.ParentId == parentId && sp.StudentId == studentId && !sp.IsDeleted);

            if (!relationships.Any())
            {
                _logger.LogWarning("Relationship between parent {ParentId} and student {StudentId} not found",
                    parentId, studentId);
                return false;
            }

            var relationship = relationships.First();

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await studentParentRepository.DeleteAsync(relationship, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Parent {ParentId} removed from student {StudentId}", parentId, studentId);
            return true;
        }

        #endregion

        #region DTO Mapping Methods
        private StudentDto MapToStudentDto(Student student)
        {
            return new StudentDto
            {
                Id = student.Id,
                StudentId = student.StudentId,
                FirstName = student.FirstName,
                LastName = student.LastName,
                DateOfBirth = student.DateOfBirth,
                Gender = student.Gender,
                Email = student.Email,
                Phone = student.Phone,
                Address = student.Address,
                EmergencyContactName = student.EmergencyContactName,
                EmergencyContactPhone = student.EmergencyContactPhone,
                EmergencyContactRelation = student.EmergencyContactRelation,
                BloodGroup = student.BloodGroup,
                MedicalConditions = student.MedicalConditions,
                Allergies = student.Allergies,
                CurrentGrade = student.CurrentGrade,
                Section = student.Section,
                Medium = student.Medium,
                Shift = student.Shift,
                AcademicYear = student.AcademicYear,
                RollNumber = student.RollNumber,
                AdmissionYear = student.AdmissionYear,
                GraduationDate = student.GraduationDate,
                IsActive = student.IsActive,
                IsHosteler = student.IsHosteler,
                UserId = student.UserId,
                ClassTeacherId = student.ClassTeacherId,
                ClassTeacher = student.ClassTeacher != null ? new FacultyDto
                {
                    Id = student.ClassTeacher.Id,
                    Name = student.ClassTeacher.Name,
                    Title = student.ClassTeacher.Title,
                    Email = student.ClassTeacher.Email,
                    Phone = student.ClassTeacher.Phone
                } : null,
                ProfileImageId = student.ProfileImageId,
                ProfileImage = student.ProfileImage != null ? new MediaItemDto
                {
                    Id = student.ProfileImage.Id,
                    FileName = student.ProfileImage.FileName,
                    FilePath = student.ProfileImage.FilePath,
                    MimeType = student.ProfileImage.MimeType
                } : null,
                CreatedAt = student.CreatedAt,
                UpdatedAt = student.UpdatedAt,
                Parents = student.Parents?.Where(p => !p.IsDeleted).Select(p => new StudentParentDto
                {
                    Id = p.Id,
                    StudentId = p.StudentId,
                    ParentId = p.ParentId,
                    RelationType = p.RelationType,
                    IsPrimaryContact = p.IsPrimaryContact,
                    Parent = p.Parent != null ? new ParentDto
                    {
                        Id = p.Parent.Id,
                        FirstName = p.Parent.FirstName,
                        LastName = p.Parent.LastName,
                        Email = p.Parent.Email,
                        Phone = p.Parent.Phone
                    } : null!
                }).ToList() ?? new List<StudentParentDto>()
            };
        }

        private StudentDetailDto MapToStudentDetailDto(Student student)
        {
            var studentDto = MapToStudentDto(student);

            return new StudentDetailDto
            {
                Id = studentDto.Id,
                StudentId = studentDto.StudentId,
                FirstName = studentDto.FirstName,
                LastName = studentDto.LastName,
                DateOfBirth = studentDto.DateOfBirth,
                Gender = studentDto.Gender,
                Email = studentDto.Email,
                Phone = studentDto.Phone,
                Address = studentDto.Address,
                EmergencyContactName = studentDto.EmergencyContactName,
                EmergencyContactPhone = studentDto.EmergencyContactPhone,
                EmergencyContactRelation = studentDto.EmergencyContactRelation,
                BloodGroup = studentDto.BloodGroup,
                MedicalConditions = studentDto.MedicalConditions,
                Allergies = studentDto.Allergies,
                CurrentGrade = studentDto.CurrentGrade,
                Section = studentDto.Section,
                Medium = studentDto.Medium,
                Shift = studentDto.Shift,
                AcademicYear = studentDto.AcademicYear,
                RollNumber = studentDto.RollNumber,
                AdmissionYear = studentDto.AdmissionYear,
                GraduationDate = studentDto.GraduationDate,
                IsActive = studentDto.IsActive,
                IsHosteler = studentDto.IsHosteler,
                UserId = studentDto.UserId,
                ClassTeacherId = studentDto.ClassTeacherId,
                ClassTeacher = studentDto.ClassTeacher,
                ProfileImageId = studentDto.ProfileImageId,
                ProfileImage = studentDto.ProfileImage,
                CreatedAt = studentDto.CreatedAt,
                UpdatedAt = studentDto.UpdatedAt,
                Parents = studentDto.Parents,
                RecentAttendance = new List<StudentAttendanceDto>(),
                RecentFees = new List<StudentFeeDto>(),
                RecentResults = new List<StudentResultDto>(),
                RecentLeaves = new List<StudentLeaveDto>(),
                AcademicHistory = new List<StudentAcademicHistoryDto>()
            };
        }

        private StudentAttendanceDto MapToStudentAttendanceDto(StudentAttendance attendance)
        {
            return new StudentAttendanceDto
            {
                Id = attendance.Id,
                StudentId = attendance.StudentId,
                Date = attendance.Date,
                Status = attendance.Status,
                Remarks = attendance.Remarks,
                RecordedBy = attendance.RecordedBy,
                AcademicYear = attendance.AcademicYear,
                Grade = attendance.Grade,
                Section = attendance.Section,
                Period = attendance.Period,
                SubjectCode = attendance.SubjectCode,
                IsLeaveApproved = attendance.IsLeaveApproved,
                LeaveId = attendance.LeaveId,
                CreatedAt = attendance.CreatedAt,
                UpdatedAt = attendance.UpdatedAt
            };
        }

        private StudentFeeDto MapToStudentFeeDto(StudentFee fee)
        {
            return new StudentFeeDto
            {
                Id = fee.Id,
                StudentId = fee.StudentId,
                Type = fee.Type,
                Description = fee.Description,
                Amount = fee.Amount,
                PaidAmount = fee.PaidAmount,
                DueAmount = fee.DueAmount,
                DueDate = fee.DueDate,
                PaidDate = fee.PaidDate,
                Status = fee.Status,
                TransactionId = fee.TransactionId,
                PaymentMethod = fee.PaymentMethod,
                Remarks = fee.Remarks,
                CreatedAt = fee.CreatedAt,
                UpdatedAt = fee.UpdatedAt
            };
        }

        private StudentResultDto MapToStudentResultDto(StudentResult result)
        {
            return new StudentResultDto
            {
                Id = result.Id,
                StudentId = result.StudentId,
                AcademicYear = result.AcademicYear,
                ExamType = result.ExamType,
                SubjectCode = result.SubjectCode,
                SubjectName = result.SubjectName,
                Marks = result.Marks,
                MaxMarks = result.MaxMarks,
                Grade = result.Grade,
                Credits = result.Credits,
                Remarks = result.Remarks,
                CreatedAt = result.CreatedAt,
                UpdatedAt = result.UpdatedAt
            };
        }

        private StudentLeaveDto MapToStudentLeaveDto(StudentLeave leave)
        {
            return new StudentLeaveDto
            {
                Id = leave.Id,
                StudentId = leave.StudentId,
                StartDate = leave.StartDate,
                EndDate = leave.EndDate,
                Type = leave.Type,
                Reason = leave.Reason,
                Status = leave.Status,
                ApprovedBy = leave.ApprovedBy,
                ApprovalRemarks = leave.ApprovalRemarks,
                AttachmentPath = leave.AttachmentPath,
                CreatedAt = leave.CreatedAt,
                UpdatedAt = leave.UpdatedAt
            };
        }

        private static StudentAcademicHistoryDto MapToStudentAcademicHistoryDto(StudentAcademicHistory history)
        {
            return new StudentAcademicHistoryDto
            {
                Id = history.Id,
                StudentId = history.StudentId,
                AcademicYearId = history.AcademicYearId,
                TermId = history.TermId,
                Grade = history.Grade,
                Section = history.Section,
                Medium = history.Medium,
                Shift = history.Shift,
                RollNumber = history.RollNumber,
                StudentIdForYear = history.StudentIdForYear,
                FinalGPA = history.FinalGPA,
                IsPromoted = history.IsPromoted,
                ClassTeacherId = history.ClassTeacherId,
                ClassTeacher = history.ClassTeacher != null ? new FacultyDto
                {
                    Id = history.ClassTeacher.Id,
                    Name = history.ClassTeacher.Name,
                    Title = history.ClassTeacher.Title,
                    Email = history.ClassTeacher.Email,
                    Phone = history.ClassTeacher.Phone
                } : null,
                Remarks = history.Remarks,
                CreatedAt = history.CreatedAt,
                LastModifiedAt = history.LastModifiedAt,
                AcademicYearName = history.AcademicYear?.Name ?? "",
                TermName = history.Term?.Name ?? ""
            };
        }
        #endregion
    }
}
