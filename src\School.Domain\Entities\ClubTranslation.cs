using School.Domain.Common;

using System;

namespace School.Domain.Entities
{
    public class ClubTranslation : BaseEntity
    {
        public Guid ClubId { get; set; }
        public string LanguageCode { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string ShortDescription { get; set; }
        public string Requirements { get; set; }
        public string JoinProcess { get; set; }
        
        // Navigation properties
        public Club Club { get; set; }
    }
}
