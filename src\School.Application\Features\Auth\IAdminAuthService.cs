using School.Application.DTOs;

namespace School.Application.Features.Auth;

/// <summary>
/// Interface for admin authentication service
/// </summary>
public interface IAdminAuthService
{
    /// <summary>
    /// Authenticates an admin user without requiring tenant context
    /// </summary>
    /// <param name="loginDto">Admin login credentials</param>
    /// <returns>Admin login response with tenant access information</returns>
    Task<AdminLoginResponseDto?> AdminLoginAsync(AdminLoginDto loginDto);

    /// <summary>
    /// Gets list of tenants accessible to an admin user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>List of accessible tenants</returns>
    Task<List<TenantAccessDto>> GetAccessibleTenantsAsync(string userId);
}
