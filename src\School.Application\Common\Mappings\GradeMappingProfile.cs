using AutoMapper;
using School.Application.DTOs;
using School.Domain.Entities;

namespace School.Application.Common.Mappings;

/// <summary>
/// AutoMapper profile for Grade entity mappings
/// </summary>
public class GradeMappingProfile : Profile
{
    public GradeMappingProfile()
    {
        // Grade entity mappings
        CreateMap<Grade, GradeDto>()
            .ForMember(dest => dest.AcademicYearName, opt => opt.MapFrom(src => src.AcademicYear.Name))
            .ForMember(dest => dest.TotalSections, opt => opt.MapFrom(src => src.Sections.Count))
            .ForMember(dest => dest.TotalStudents, opt => opt.MapFrom(src => src.Students.Count))
            .ForMember(dest => dest.Sections, opt => opt.MapFrom(src => src.Sections));

        CreateMap<CreateGradeDto, Grade>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.TenantId, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedBy, opt => opt.Ignore())
            .ForMember(dest => dest.AcademicYear, opt => opt.Ignore())
            .ForMember(dest => dest.Sections, opt => opt.Ignore())
            .ForMember(dest => dest.Students, opt => opt.Ignore())
            .ForMember(dest => dest.Translations, opt => opt.Ignore());

        CreateMap<UpdateGradeDto, Grade>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.TenantId, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedBy, opt => opt.Ignore())
            .ForMember(dest => dest.AcademicYear, opt => opt.Ignore())
            .ForMember(dest => dest.Sections, opt => opt.Ignore())
            .ForMember(dest => dest.Students, opt => opt.Ignore())
            .ForMember(dest => dest.Translations, opt => opt.Ignore());

        // Grade translation mappings
        CreateMap<GradeTranslation, GradeTranslationDto>();

        CreateMap<CreateGradeTranslationDto, GradeTranslation>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.GradeId, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedBy, opt => opt.Ignore())
            .ForMember(dest => dest.Grade, opt => opt.Ignore());

        CreateMap<UpdateGradeTranslationDto, GradeTranslation>()
            .ForMember(dest => dest.GradeId, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedBy, opt => opt.Ignore())
            .ForMember(dest => dest.Grade, opt => opt.Ignore());
    }
}

/// <summary>
/// AutoMapper profile for Section entity mappings
/// </summary>
public class SectionMappingProfile : Profile
{
    public SectionMappingProfile()
    {
        // Section entity mappings
        CreateMap<Section, SectionDto>()
            .ForMember(dest => dest.GradeName, opt => opt.MapFrom(src => src.Grade.Name))
            .ForMember(dest => dest.AcademicYearName, opt => opt.MapFrom(src => src.AcademicYear.Name))
            .ForMember(dest => dest.ClassTeacherName, opt => opt.MapFrom(src => src.ClassTeacher != null ? src.ClassTeacher.Faculty.Name : null))
            .ForMember(dest => dest.Students, opt => opt.MapFrom(src => src.Students));

        CreateMap<CreateSectionDto, Section>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.TenantId, opt => opt.Ignore())
            .ForMember(dest => dest.CurrentEnrollment, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedBy, opt => opt.Ignore())
            .ForMember(dest => dest.Grade, opt => opt.Ignore())
            .ForMember(dest => dest.AcademicYear, opt => opt.Ignore())
            .ForMember(dest => dest.ClassTeacher, opt => opt.Ignore())
            .ForMember(dest => dest.Students, opt => opt.Ignore())
            .ForMember(dest => dest.Translations, opt => opt.Ignore());

        CreateMap<UpdateSectionDto, Section>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.TenantId, opt => opt.Ignore())
            .ForMember(dest => dest.CurrentEnrollment, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedBy, opt => opt.Ignore())
            .ForMember(dest => dest.Grade, opt => opt.Ignore())
            .ForMember(dest => dest.AcademicYear, opt => opt.Ignore())
            .ForMember(dest => dest.ClassTeacher, opt => opt.Ignore())
            .ForMember(dest => dest.Students, opt => opt.Ignore())
            .ForMember(dest => dest.Translations, opt => opt.Ignore());

        // Section translation mappings
        CreateMap<SectionTranslation, SectionTranslationDto>();

        CreateMap<CreateSectionTranslationDto, SectionTranslation>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.SectionId, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedBy, opt => opt.Ignore())
            .ForMember(dest => dest.Section, opt => opt.Ignore());

        CreateMap<UpdateSectionTranslationDto, SectionTranslation>()
            .ForMember(dest => dest.SectionId, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedBy, opt => opt.Ignore())
            .ForMember(dest => dest.Section, opt => opt.Ignore());
    }
}

/// <summary>
/// AutoMapper profile for ClassTeacher entity mappings
/// </summary>
public class ClassTeacherMappingProfile : Profile
{
    public ClassTeacherMappingProfile()
    {
        // ClassTeacher entity mappings
        CreateMap<ClassTeacher, ClassTeacherDto>()
            .ForMember(dest => dest.FacultyName, opt => opt.MapFrom(src => src.Faculty.Name))
            .ForMember(dest => dest.FacultyEmail, opt => opt.MapFrom(src => src.Faculty.Email))
            .ForMember(dest => dest.FacultyPhone, opt => opt.MapFrom(src => src.Faculty.Phone))
            .ForMember(dest => dest.SectionName, opt => opt.MapFrom(src => src.Section.Name))
            .ForMember(dest => dest.GradeName, opt => opt.MapFrom(src => src.Section.Grade.Name))
            .ForMember(dest => dest.AcademicYearName, opt => opt.MapFrom(src => src.AcademicYear.Name))
            .ForMember(dest => dest.TermName, opt => opt.MapFrom(src => src.Term != null ? src.Term.Name : null))
            .ForMember(dest => dest.StudentCount, opt => opt.MapFrom(src => src.Students.Count));

        CreateMap<CreateClassTeacherDto, ClassTeacher>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.TenantId, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedBy, opt => opt.Ignore())
            .ForMember(dest => dest.Faculty, opt => opt.Ignore())
            .ForMember(dest => dest.Section, opt => opt.Ignore())
            .ForMember(dest => dest.AcademicYear, opt => opt.Ignore())
            .ForMember(dest => dest.Term, opt => opt.Ignore())
            .ForMember(dest => dest.Students, opt => opt.Ignore());

        CreateMap<UpdateClassTeacherDto, ClassTeacher>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.TenantId, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedAt, opt => opt.Ignore())
            .ForMember(dest => dest.LastModifiedBy, opt => opt.Ignore())
            .ForMember(dest => dest.Faculty, opt => opt.Ignore())
            .ForMember(dest => dest.Section, opt => opt.Ignore())
            .ForMember(dest => dest.AcademicYear, opt => opt.Ignore())
            .ForMember(dest => dest.Term, opt => opt.Ignore())
            .ForMember(dest => dest.Students, opt => opt.Ignore());
    }
}
