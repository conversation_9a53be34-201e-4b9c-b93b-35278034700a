<div class="results-container">
  <h1 class="page-title">Student Results</h1>

  <!-- Filter Form -->
  <mat-card class="filter-card">
    <mat-card-header>
      <mat-card-title>Filter Results</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="filterForm" (ngSubmit)="loadResults()">
        <div class="filter-form">
          <mat-form-field appearance="outline">
            <mat-label>Exam Type</mat-label>
            <mat-select formControlName="examType">
              <mat-option *ngFor="let type of examTypes" [value]="type.value">
                {{ type.label }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Academic Year</mat-label>
            <input matInput type="number" formControlName="academicYear">
          </mat-form-field>

          <div class="filter-actions">
            <button mat-raised-button color="primary" type="submit">
              View Results
            </button>
          </div>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Loading Indicator -->
  <div *ngIf="loading.results" class="results-loading">
    <mat-progress-bar mode="indeterminate"></mat-progress-bar>
  </div>

  <!-- Error Message -->
  <div *ngIf="error.results" class="results-error">
    <mat-error>
      <mat-icon>error</mat-icon>
      <span>Failed to load result records. Please try again.</span>
      <button mat-button color="warn" (click)="loadResults()">Retry</button>
    </mat-error>
  </div>

  <!-- Results Summary -->
  <div *ngIf="!loading.results && !error.results && resultRecords.length > 0" class="results-summary">
    <mat-card>
      <mat-card-content>
        <div class="summary-content">
          <div class="summary-item">
            <span class="summary-label">Exam Type:</span>
            <span class="summary-value">{{ getExamTypeLabel(filterForm.value.examType) }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Academic Year:</span>
            <span class="summary-value">{{ filterForm.value.academicYear }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">GPA:</span>
            <span class="summary-value">{{ calculateTotalGPA() | number:'1.2-2' }}</span>
          </div>
          <div class="summary-actions">
            <button mat-raised-button color="primary" (click)="downloadResultSheet()">
              <mat-icon>download</mat-icon> Download Result Sheet
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Results Table -->
  <div *ngIf="!loading.results && !error.results && resultRecords.length > 0" class="results-table-container mat-elevation-z8">
    <table mat-table [dataSource]="resultRecords" class="results-table">
      <!-- Subject Code Column -->
      <ng-container matColumnDef="subjectCode">
        <th mat-header-cell *matHeaderCellDef>Subject Code</th>
        <td mat-cell *matCellDef="let record">{{ record.subjectCode }}</td>
      </ng-container>

      <!-- Subject Name Column -->
      <ng-container matColumnDef="subjectName">
        <th mat-header-cell *matHeaderCellDef>Subject Name</th>
        <td mat-cell *matCellDef="let record">{{ record.subjectName }}</td>
      </ng-container>

      <!-- Marks Obtained Column -->
      <ng-container matColumnDef="marksObtained">
        <th mat-header-cell *matHeaderCellDef>Marks Obtained</th>
        <td mat-cell *matCellDef="let record">{{ record.marksObtained }}</td>
      </ng-container>

      <!-- Total Marks Column -->
      <ng-container matColumnDef="totalMarks">
        <th mat-header-cell *matHeaderCellDef>Total Marks</th>
        <td mat-cell *matCellDef="let record">{{ record.totalMarks }}</td>
      </ng-container>

      <!-- Percentage Column -->
      <ng-container matColumnDef="percentage">
        <th mat-header-cell *matHeaderCellDef>Percentage</th>
        <td mat-cell *matCellDef="let record">{{ (record.marksObtained / record.totalMarks * 100) | number:'1.2-2' }}%</td>
      </ng-container>

      <!-- Grade Column -->
      <ng-container matColumnDef="grade">
        <th mat-header-cell *matHeaderCellDef>Grade</th>
        <td mat-cell *matCellDef="let record">
          <span class="grade-badge" [style.background-color]="getGradeColor(record.letterGrade)">
            {{ record.letterGrade }}
          </span>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>

  <!-- No Data Message -->
  <div *ngIf="!loading.results && !error.results && resultRecords.length === 0" class="no-data">
    <mat-card>
      <mat-card-content>
        <p>No result records found for the selected exam type and academic year.</p>
      </mat-card-content>
    </mat-card>
  </div>
</div>
