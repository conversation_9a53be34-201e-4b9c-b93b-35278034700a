using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Policy;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using System.Globalization;
using School.API.Authorization;
using School.API.Common;
using School.API.Middleware;
using School.Application;
using School.Infrastructure.Persistence;
using School.Infrastructure;
using Serilog;
using Serilog.Events;
using School.Application.Features.AcademicCalendar;
using School.Infrastructure.Services;
using School.Application.Features.Alumni;
using School.Application.Features.Career;
using School.Application.Features.Auth;
using School.Application.Features.Content;
using School.Application.Features.Department;
using School.Application.Features.Event;
using School.Application.Features.Faculty;
using School.Application.Features.HostelFacility;
using School.Application.Features.Media;
using School.Application.Features.Notice;
using School.Application.Features.Parent;
using School.Application.Features.Student;
using School.Application.Features.Clubs;
using School.Application.Features.TuitionFee;
using School.Application.Features.User;
using System.Text.Json;
using Swashbuckle.AspNetCore.SwaggerUI;
using System.Collections.Generic;

// Make Program.cs async
await RunAsync(args);

async Task RunAsync(string[] args)
{
    // Configure Serilog
    Log.Logger = new LoggerConfiguration()
        .MinimumLevel.Debug()
        .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
        .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)
        .Enrich.FromLogContext()
        .WriteTo.Console()
        .WriteTo.File(
            path: "Logs/application-.log",
            rollingInterval: RollingInterval.Day,
            restrictedToMinimumLevel: LogEventLevel.Information)
        .WriteTo.File(
            path: "Logs/error-.log",
            rollingInterval: RollingInterval.Day,
            restrictedToMinimumLevel: LogEventLevel.Error)
        .WriteTo.File(
            path: "Logs/operations-.log",
            rollingInterval: RollingInterval.Day,
            restrictedToMinimumLevel: LogEventLevel.Information,
            outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj} {Properties:j}{NewLine}")
        .CreateLogger();

    try
    {
        Log.Information("Starting web application");

        var builder = WebApplication.CreateBuilder(args);

        builder.Host.UseSerilog();

        // Add services to the container
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen(options => {
            options.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
            {
                Title = "School API",
                Version = "v1",
                Description = "API for School Management System",
                Contact = new Microsoft.OpenApi.Models.OpenApiContact
                {
                    Name = "School Admin",
                    Email = "<EMAIL>"
                }
            });

            // Add JWT Authentication support to Swagger
            options.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Name = "Authorization",
                Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
                Scheme = "Bearer",
                BearerFormat = "JWT",
                In = Microsoft.OpenApi.Models.ParameterLocation.Header,
                Description = "JWT Authorization header using the Bearer scheme. \r\n\r\n Enter 'Bearer' [space] and then your token in the text input below.\r\n\r\nExample: \"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\""
            });

            options.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
            {
                {
                    new Microsoft.OpenApi.Models.OpenApiSecurityScheme
                    {
                        Reference = new Microsoft.OpenApi.Models.OpenApiReference
                        {
                            Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });

            // Add operation filter to check for [Authorize] attribute
            options.OperationFilter<AuthorizeCheckOperationFilter>();
        });

        // Configure problem details for model validation errors
        builder.Services.AddProblemDetails();
        builder.Services.AddSingleton<ProblemDetailsFactory, CustomProblemDetailsService>();

        // Configure JSON serialization options
        builder.Services.Configure<JsonOptions>(options =>
        {
            options.JsonSerializerOptions.Converters.Add(new System.Text.Json.Serialization.JsonStringEnumConverter());
            options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
        });

        // Configure localization
        builder.Services.AddLocalization(options => options.ResourcesPath = "Resources");
        builder.Services.Configure<RequestLocalizationOptions>(options =>
        {
            var supportedCultures = new[]
            {
                new CultureInfo("en-US"),
                new CultureInfo("bn-BD")
            };

            options.DefaultRequestCulture = new RequestCulture("en-US");
            options.SupportedCultures = supportedCultures;
            options.SupportedUICultures = supportedCultures;

            // Configure culture providers
            options.RequestCultureProviders.Clear();
            options.RequestCultureProviders.Add(new QueryStringRequestCultureProvider());
            options.RequestCultureProviders.Add(new CookieRequestCultureProvider());
            options.RequestCultureProviders.Add(new AcceptLanguageHeaderRequestCultureProvider());
        });

        // Add health checks
        builder.Services.AddHealthChecks()
            .AddDbContextCheck<ApplicationDbContext>("Database")
            .AddCheck("API", () => HealthCheckResult.Healthy());

        builder.Services.AddCors(options =>
        {
            options.AddPolicy("AllowAll", policy =>
            {
                policy.WithOrigins("http://localhost:4200", "https://localhost:4200", "http://localhost:4201", "https://localhost:4201")
                    .AllowAnyMethod()
                    .AllowAnyHeader()
                    .AllowCredentials();
            });
        });

        // Register Carter
        builder.Services.AddCarter();

        // HttpContextAccessor is registered in Infrastructure layer

        // Add application and infrastructure services
        builder.Services.AddApplicationServices();
        builder.Services.AddInfrastructureServices(builder.Configuration);

        // Add custom authorization handlers
        builder.Services.AddSingleton<IAuthorizationMiddlewareResultHandler, CustomAuthorizationFilter>();
        builder.Services.AddScoped<IAuthorizationHandler, StudentSelfOrAdminAuthorizationHandler>();

        var app = builder.Build();

        // Configure the HTTP request pipeline
        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI(options =>
            {
                options.SwaggerEndpoint("/swagger/v1/swagger.json", "School API v1");
                options.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
                options.DefaultModelsExpandDepth(-1); // Hide schemas section
                options.EnableFilter();
                options.EnableDeepLinking();
                options.DisplayRequestDuration();
            });
        }
        else
        {
            // Enable Swagger in production but with authentication
            app.UseSwagger(options =>
            {
                options.PreSerializeFilters.Add((swagger, httpReq) =>
                {
                    swagger.Servers = new List<Microsoft.OpenApi.Models.OpenApiServer>
                    {
                        new Microsoft.OpenApi.Models.OpenApiServer { Url = $"{httpReq.Scheme}://{httpReq.Host.Value}" }
                    };
                });
            });
            app.UseSwaggerUI(options =>
            {
                options.SwaggerEndpoint("/swagger/v1/swagger.json", "School API v1");
                options.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
                options.DefaultModelsExpandDepth(-1);
                options.EnableFilter();
                options.EnableDeepLinking();
                options.DisplayRequestDuration();
                options.RoutePrefix = "api-docs";
                options.OAuthUsePkce();
            });
        }

        // Apply CORS before any other middleware that might return a response
        app.UseCors("AllowAll");

        // Configure request localization
        app.UseRequestLocalization();

        app.UseHttpsRedirection();

        // Add Serilog request logging
        app.UseSerilogRequestLogging(options =>
        {
            options.MessageTemplate = "HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms";
        });

        // Add global exception handler middleware
        app.UseGlobalExceptionHandler();

        // Add Authentication and Authorization middleware
        app.UseAuthentication();

        // Add tenant middleware for multi-tenancy support
        app.UseMiddleware<School.Infrastructure.Middleware.TenantMiddleware>();

        app.UseAuthorization();

        // Add student access middleware
        app.UseStudentAccessMiddleware();

        // Add validation problem details middleware
        app.UseValidationProblemDetails();

        // Add API response standardization middleware
        app.UseApiResponseMiddleware();

        // Map Carter modules
        app.MapCarter();

        // Ensure database is created and seeded
        using (var scope = app.Services.CreateScope())
        {
            try
            {
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

                try
                {
                    // Check if database exists and has migration history
                    var canConnect = await context.Database.CanConnectAsync();
                    if (canConnect)
                    {
                        var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
                        if (pendingMigrations.Any())
                        {
                            Log.Information("Applying {Count} pending migrations", pendingMigrations.Count());
                            await context.Database.MigrateAsync();
                            Log.Information("Database migrations applied successfully");
                        }
                        else
                        {
                            Log.Information("Database is up to date, no migrations needed");
                        }
                    }
                    else
                    {
                        Log.Information("Database does not exist, creating with migrations");
                        await context.Database.MigrateAsync();
                        Log.Information("Database created and migrations applied successfully");
                    }
                }
                catch (Exception ex)
                {
                    // If migrations fail, try to handle the situation gracefully
                    Log.Warning(ex, "Failed to apply migrations, attempting to resolve");

                    try
                    {
                        // Check if tables exist but migration history is missing
                        var tablesExist = await context.Database.GetAppliedMigrationsAsync();
                        if (!tablesExist.Any())
                        {
                            // Database exists but no migration history - this happens when EnsureCreated was used
                            Log.Information("Database exists but migration history is missing, ensuring database is created");
                            context.Database.EnsureCreated();
                            Log.Information("Database ensured successfully");
                        }
                    }
                    catch (Exception innerEx)
                    {
                        Log.Error(innerEx, "Failed to resolve database migration issues");
                        throw;
                    }
                }

                // Seed Identity roles and all users
                await School.Infrastructure.Identity.IdentityDataSeeder.SeedRolesAndUsersAsync(app.Services);
                Log.Information("Identity data seeded successfully");

              
                // Seed default data if needed
                // await ApplicationDbContextSeed.SeedDefaultDataAsync(context, logger);

                Log.Information("Database initialization completed successfully");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An error occurred while initializing the database");
            }
        }

        await app.RunAsync();
    }
    catch (Exception ex)
    {
        Log.Fatal(ex, "Application terminated unexpectedly");
    }
    finally
    {
        Log.CloseAndFlush();
    }
}
