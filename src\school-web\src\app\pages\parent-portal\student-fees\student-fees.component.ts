import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe, CurrencyPipe } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ParentService } from '../../../core/services/parent.service';

@Component({
  selector: 'app-student-fees',
  templateUrl: './student-fees.component.html',
  styleUrls: ['./student-fees.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    DatePipe,
    CurrencyPipe,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatTableModule,
    MatFormFieldModule,
    MatInputModule,
    MatPaginatorModule,
    MatSortModule
  ]
})
export class StudentFeesComponent implements OnInit {
  isLoading = true;
  error: string | null = null;
  studentId: number | null = null;
  studentName: string = '';
  
  // Fee details
  feeDetails: any = null;
  feeHistory: any[] = [];
  
  // Display columns for the fee history table
  displayedColumns: string[] = ['feeType', 'dueDate', 'amount', 'status', 'paidDate', 'actions'];
  
  constructor(private parentService: ParentService) { }

  ngOnInit(): void {
    // In a real app, get the student ID from the route or a service
    this.studentId = 1;
    this.loadStudentFees();
  }

  loadStudentFees(): void {
    this.isLoading = true;
    this.error = null;
    
    // Replace with actual API call
    setTimeout(() => {
      this.studentName = 'John Doe';
      
      this.feeDetails = {
        currentDue: 15000,
        totalPaid: 45000,
        totalAnnual: 60000,
        dueDate: new Date(2023, 6, 15), // July 15, 2023
        paymentStatus: 'Partially Paid',
        lastPaymentDate: new Date(2023, 5, 10), // June 10, 2023
        lastPaymentAmount: 15000
      };
      
      this.feeHistory = [
        {
          id: 1,
          feeType: 'Tuition Fee - Q1',
          dueDate: new Date(2023, 3, 15), // April 15, 2023
          amount: 15000,
          status: 'Paid',
          paidDate: new Date(2023, 3, 10), // April 10, 2023
          receiptNo: 'REC-2023-001'
        },
        {
          id: 2,
          feeType: 'Tuition Fee - Q2',
          dueDate: new Date(2023, 5, 15), // June 15, 2023
          amount: 15000,
          status: 'Paid',
          paidDate: new Date(2023, 5, 10), // June 10, 2023
          receiptNo: 'REC-2023-002'
        },
        {
          id: 3,
          feeType: 'Tuition Fee - Q3',
          dueDate: new Date(2023, 8, 15), // September 15, 2023
          amount: 15000,
          status: 'Paid',
          paidDate: new Date(2023, 8, 12), // September 12, 2023
          receiptNo: 'REC-2023-003'
        },
        {
          id: 4,
          feeType: 'Tuition Fee - Q4',
          dueDate: new Date(2023, 11, 15), // December 15, 2023
          amount: 15000,
          status: 'Due',
          paidDate: null,
          receiptNo: null
        }
      ];
      
      this.isLoading = false;
    }, 1000);
  }

  downloadReceipt(receiptNo: string): void {
    // In a real app, this would download the receipt
    console.log(`Downloading receipt: ${receiptNo}`);
    alert(`Receipt ${receiptNo} download started.`);
  }

  makePayment(): void {
    // In a real app, this would navigate to a payment gateway
    console.log('Initiating payment process');
    alert('Payment gateway would open here.');
  }
}
