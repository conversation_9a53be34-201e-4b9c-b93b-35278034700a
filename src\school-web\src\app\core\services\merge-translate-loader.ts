import { HttpClient } from '@angular/common/http';
import { TranslateLoader } from '@ngx-translate/core';
import { Observable, forkJoin, of, catchError, map } from 'rxjs';

export class MergeTranslateLoader implements TranslateLoader {
  // Define the translation modules to load for each language
  private readonly translationModules = [
    'core',
    'about',
    'academics',
    'admissions',
    'campus-life',
    'events',
    'hostel',
    'careers',
    'contact',
    'news',
    'login',
    'register',
    'forgot-password',
    'reset-password',
    'staff-portal',
    'terms-and-conditions',
    'privacy-policy',
    'faq',
    'scholarships',
    'alumni',
    'faculty',
    'student',
    'parent',
    'admin',
    'common',
    'dashboard',
    'settings',
    'notifications',
    'messages',
    'calendar',
    'library',
    'transportation',
    'exams',
    'grades',
    'attendance',
    'events-calendar',
    'timetable',
    'assignments',
    'projects',
    'resources',
    'feedback',
    'reports',
    'analytics',
    'performance',
  ];

  constructor(
    private http: HttpClient,
    private prefix: string = './assets/i18n/',
    private suffix: string = '.json'
  ) {}

  /**
   * Gets the translations from the server
   */
  public getTranslation(lang: string): Observable<any> {
    console.log(`Loading translations for language: ${lang}`);

    // Try to load from the new folder structure first
    const moduleRequests = this.translationModules.map(module => {
      const url = `${this.prefix}${lang}/${module}${this.suffix}`;
      console.log(`Attempting to load module: ${url}`);

      return this.http.get(url).pipe(
        catchError(error => {
          console.warn(`Could not load module '${module}' for language '${lang}':`, error.message);
          return of({});
        })
      );
    });

    // Also try to load the legacy file as fallback
    const legacyUrl = `${this.prefix}${lang}${this.suffix}`;
    console.log(`Attempting to load legacy file: ${legacyUrl}`);

    const legacyFile = this.http.get(legacyUrl).pipe(
      catchError(error => {
        console.warn(`Could not load legacy file for language '${lang}':`, error.message);
        return of({});
      })
    );

    // Combine all requests
    return forkJoin([...moduleRequests, legacyFile]).pipe(
      map(results => {
        // The last result is the legacy file
        const legacyResult = results[results.length - 1];
        // The rest are module results
        const moduleResults = results.slice(0, results.length - 1);

        // Merge all translations, with modules taking precedence over legacy file
        const mergedTranslations = { ...legacyResult };

        // Log which modules were loaded successfully
        const loadedModules = this.translationModules.filter((moduleName, index) => {
          const hasContent = Object.keys(moduleResults[index] || {}).length > 0;
          if (hasContent) {
            console.log(`✓ Successfully loaded module '${moduleName}' for '${lang}'`);
          } else {
            console.warn(`✗ Failed to load module '${moduleName}' for '${lang}' or it was empty`);
          }
          return hasContent;
        });

        console.log(`Successfully loaded ${loadedModules.length}/${this.translationModules.length} translation modules for '${lang}':`,
          loadedModules.length > 0 ? loadedModules : 'None');

        // Merge all module translations
        moduleResults.forEach(module => {
          if (Object.keys(module).length > 0) {
            Object.assign(mergedTranslations, module);
          }
        });

        // Log total number of translation keys
        const totalKeys = Object.keys(mergedTranslations).length;
        console.log(`Total translation keys for '${lang}': ${totalKeys}`);

        return mergedTranslations;
      })
    );
  }
}

export function createTranslateLoader(http: HttpClient) {
  return new MergeTranslateLoader(http);
}
