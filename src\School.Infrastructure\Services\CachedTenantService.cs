using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Domain.Entities;
using School.Domain.Enums;

namespace School.Infrastructure.Services;

/// <summary>
/// Cached wrapper for TenantService to improve performance
/// </summary>
public class CachedTenantService : ITenantService
{
    private readonly ITenantService _innerService;
    private readonly IMemoryCache _cache;
    private readonly ILogger<CachedTenantService> _logger;

    private static readonly TimeSpan DefaultCacheDuration = TimeSpan.FromMinutes(15);
    private static readonly TimeSpan ShortCacheDuration = TimeSpan.FromMinutes(5);

    public CachedTenantService(
        ITenantService innerService,
        IMemoryCache cache,
        ILogger<CachedTenantService> logger)
    {
        _innerService = innerService;
        _cache = cache;
        _logger = logger;
    }

    public Guid? GetCurrentTenantId()
    {
        // Don't cache current tenant ID as it's context-dependent
        return _innerService.GetCurrentTenantId();
    }

    public async Task<Organization?> GetCurrentTenantAsync()
    {
        // Don't cache current tenant as it's context-dependent
        return await _innerService.GetCurrentTenantAsync();
    }

    public async Task<bool> SetTenantAsync(string identifier)
    {
        // Don't cache tenant setting as it's a state change operation
        var result = await _innerService.SetTenantAsync(identifier);
        
        // If successful, pre-cache the tenant for future lookups
        if (result)
        {
            var tenant = await _innerService.GetCurrentTenantAsync();
            if (tenant != null)
            {
                CacheTenant(tenant);
            }
        }
        
        return result;
    }

    public async Task<bool> UserHasAccessToTenantAsync(string userId, Guid tenantId)
    {
        var cacheKey = $"user_access_{userId}_{tenantId}";
        
        if (_cache.TryGetValue(cacheKey, out bool cachedResult))
        {
            _logger.LogDebug("Cache hit for user access check: {UserId} -> {TenantId}", userId, tenantId);
            return cachedResult;
        }

        var result = await _innerService.UserHasAccessToTenantAsync(userId, tenantId);
        
        // Cache for shorter duration as access can change
        _cache.Set(cacheKey, result, ShortCacheDuration);
        _logger.LogDebug("Cached user access check: {UserId} -> {TenantId} = {Result}", userId, tenantId, result);
        
        return result;
    }

    public async Task<IEnumerable<Organization>> GetUserTenantsAsync(string userId)
    {
        var cacheKey = $"user_tenants_{userId}";
        
        if (_cache.TryGetValue(cacheKey, out IEnumerable<Organization>? cachedTenants))
        {
            _logger.LogDebug("Cache hit for user tenants: {UserId}", userId);
            return cachedTenants!;
        }

        var tenants = await _innerService.GetUserTenantsAsync(userId);
        
        // Cache for shorter duration as user tenants can change
        _cache.Set(cacheKey, tenants, ShortCacheDuration);
        _logger.LogDebug("Cached user tenants: {UserId} -> {Count} tenants", userId, tenants.Count());
        
        return tenants;
    }

    public async Task<Organization?> GetTenantBySlugAsync(string slug)
    {
        var cacheKey = $"tenant_slug_{slug.ToLowerInvariant()}";
        
        if (_cache.TryGetValue(cacheKey, out Organization? cachedTenant))
        {
            _logger.LogDebug("Cache hit for tenant by slug: {Slug}", slug);
            return cachedTenant;
        }

        var tenant = await _innerService.GetTenantBySlugAsync(slug);
        
        if (tenant != null)
        {
            _cache.Set(cacheKey, tenant, DefaultCacheDuration);
            _logger.LogDebug("Cached tenant by slug: {Slug} -> {TenantName}", slug, tenant.Name);
            
            // Also cache by ID for cross-reference
            CacheTenant(tenant);
        }
        else
        {
            // Cache null results for shorter duration to avoid repeated DB hits
            _cache.Set(cacheKey, (Organization?)null, TimeSpan.FromMinutes(2));
        }
        
        return tenant;
    }

    public async Task<Organization?> GetTenantByIdAsync(Guid tenantId)
    {
        var cacheKey = $"tenant_id_{tenantId}";

        if (_cache.TryGetValue(cacheKey, out Organization? cachedTenant))
        {
            _logger.LogDebug("Cache hit for tenant by ID: {TenantId}", tenantId);
            return cachedTenant;
        }

        var tenant = await _innerService.GetTenantByIdAsync(tenantId);

        if (tenant != null)
        {
            _cache.Set(cacheKey, tenant, DefaultCacheDuration);
            _logger.LogDebug("Cached tenant by ID: {TenantId} -> {TenantName}", tenantId, tenant.Name);

            // Also cache by slug and domain for cross-reference
            CacheTenant(tenant);
        }
        else
        {
            // Cache null results for shorter duration
            _cache.Set(cacheKey, (Organization?)null, TimeSpan.FromMinutes(2));
        }

        return tenant;
    }

    public async Task<Organization?> GetTenantByDomainAsync(string domain)
    {
        var cacheKey = $"tenant_domain_{domain.ToLowerInvariant()}";
        
        if (_cache.TryGetValue(cacheKey, out Organization? cachedTenant))
        {
            _logger.LogDebug("Cache hit for tenant by domain: {Domain}", domain);
            return cachedTenant;
        }

        var tenant = await _innerService.GetTenantByDomainAsync(domain);
        
        if (tenant != null)
        {
            _cache.Set(cacheKey, tenant, DefaultCacheDuration);
            _logger.LogDebug("Cached tenant by domain: {Domain} -> {TenantName}", domain, tenant.Name);
            
            // Also cache by ID and slug for cross-reference
            CacheTenant(tenant);
        }
        else
        {
            // Cache null results for shorter duration
            _cache.Set(cacheKey, (Organization?)null, TimeSpan.FromMinutes(2));
        }
        
        return tenant;
    }

    public async Task<bool> IsSlugAvailableAsync(string slug, Guid? excludeTenantId = null)
    {
        // Don't cache availability checks as they're used during creation/updates
        return await _innerService.IsSlugAvailableAsync(slug, excludeTenantId);
    }

    public async Task<bool> IsDomainAvailableAsync(string domain, Guid? excludeTenantId = null)
    {
        // Don't cache availability checks as they're used during creation/updates
        return await _innerService.IsDomainAvailableAsync(domain, excludeTenantId);
    }

    public async Task<Organization> CreateTenantAsync(Organization organization)
    {
        var result = await _innerService.CreateTenantAsync(organization);
        
        // Cache the newly created tenant
        CacheTenant(result);
        
        // Invalidate related caches
        InvalidateUserTenantCaches();
        
        return result;
    }

    public async Task<Organization> UpdateTenantAsync(Organization organization)
    {
        var result = await _innerService.UpdateTenantAsync(organization);
        
        // Update cache with new data
        CacheTenant(result);
        
        // Invalidate slug/domain caches that might have changed
        InvalidateTenantLookupCaches(organization.Id);
        
        return result;
    }

    public async Task<bool> DeactivateTenantAsync(Guid tenantId)
    {
        var result = await _innerService.DeactivateTenantAsync(tenantId);
        
        if (result)
        {
            // Remove from all caches
            InvalidateTenantCaches(tenantId);
        }
        
        return result;
    }

    public async Task<bool> AddUserToTenantAsync(Guid tenantId, string userId, OrganizationRole role)
    {
        var result = await _innerService.AddUserToTenantAsync(tenantId, userId, role);
        
        if (result)
        {
            // Invalidate user-related caches
            InvalidateUserCaches(userId);
        }
        
        return result;
    }

    public async Task<bool> RemoveUserFromTenantAsync(Guid tenantId, string userId)
    {
        var result = await _innerService.RemoveUserFromTenantAsync(tenantId, userId);
        
        if (result)
        {
            // Invalidate user-related caches
            InvalidateUserCaches(userId);
        }
        
        return result;
    }

    public async Task<bool> UpdateUserRoleInTenantAsync(Guid tenantId, string userId, OrganizationRole role)
    {
        var result = await _innerService.UpdateUserRoleInTenantAsync(tenantId, userId, role);
        
        if (result)
        {
            // Invalidate user-related caches
            InvalidateUserCaches(userId);
        }
        
        return result;
    }

    /// <summary>
    /// Cache tenant by multiple keys for efficient lookups
    /// </summary>
    private void CacheTenant(Organization tenant)
    {
        var tenantIdKey = $"tenant_id_{tenant.Id}";
        var tenantSlugKey = $"tenant_slug_{tenant.Slug.ToLowerInvariant()}";
        
        _cache.Set(tenantIdKey, tenant, DefaultCacheDuration);
        _cache.Set(tenantSlugKey, tenant, DefaultCacheDuration);
        
        if (!string.IsNullOrEmpty(tenant.CustomDomain))
        {
            var tenantDomainKey = $"tenant_domain_{tenant.CustomDomain.ToLowerInvariant()}";
            _cache.Set(tenantDomainKey, tenant, DefaultCacheDuration);
        }
    }

    /// <summary>
    /// Invalidate all caches for a specific tenant
    /// </summary>
    private void InvalidateTenantCaches(Guid tenantId)
    {
        var patterns = new[]
        {
            $"tenant_id_{tenantId}",
            $"tenant_slug_*",
            $"tenant_domain_*"
        };

        foreach (var pattern in patterns)
        {
            // Note: MemoryCache doesn't support pattern-based invalidation
            // In production, consider using Redis with pattern support
            // For now, we'll remove specific keys we know about
        }
        
        _logger.LogDebug("Invalidated tenant caches for: {TenantId}", tenantId);
    }

    /// <summary>
    /// Invalidate tenant lookup caches (slug/domain)
    /// </summary>
    private void InvalidateTenantLookupCaches(Guid tenantId)
    {
        // This is a simplified implementation
        // In production, you'd want to track which keys belong to which tenant
        _logger.LogDebug("Invalidated tenant lookup caches for: {TenantId}", tenantId);
    }

    /// <summary>
    /// Invalidate user-related caches
    /// </summary>
    private void InvalidateUserCaches(string userId)
    {
        var userAccessPattern = $"user_access_{userId}_*";
        var userTenantsKey = $"user_tenants_{userId}";
        
        _cache.Remove(userTenantsKey);
        
        _logger.LogDebug("Invalidated user caches for: {UserId}", userId);
    }

    /// <summary>
    /// Invalidate all user tenant caches (used when tenants are created/deleted)
    /// </summary>
    private void InvalidateUserTenantCaches()
    {
        // This would require a more sophisticated cache implementation
        // to track and invalidate pattern-based keys
        _logger.LogDebug("Invalidated all user tenant caches");
    }
}
