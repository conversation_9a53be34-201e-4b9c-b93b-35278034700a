import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { ErrorHandlerService } from './error-handler.service';

@Injectable({
  providedIn: 'root'
})
export class TuitionFeeService extends BaseApiService {
  constructor(
    protected override http: HttpClient,
    protected override errorHandler: ErrorHandlerService
  ) {
    super(http, errorHandler);
  }

  getTuitionFees(params: any = {}): Observable<any> {
    let httpParams = new HttpParams();

    if (params.grade !== undefined) httpParams = httpParams.set('grade', params.grade.toString());
    if (params.academicYear !== undefined) httpParams = httpParams.set('academicYear', params.academicYear.toString());
    if (params.isActive !== undefined) httpParams = httpParams.set('isActive', params.isActive.toString());

    return this.http.get(`${this.apiUrl}/tuition-fees`, { params: httpParams });
  }

  getTuitionFee(id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/tuition-fees/${id}`);
  }

  createTuitionFee(tuitionFee: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/tuition-fees`, tuitionFee);
  }

  updateTuitionFee(id: number, tuitionFee: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/tuition-fees/${id}`, tuitionFee);
  }

  deleteTuitionFee(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/tuition-fees/${id}`);
  }

  addTuitionFeeTranslation(tuitionFeeId: number, translation: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/tuition-fees/${tuitionFeeId}/translations`, translation);
  }

  updateTuitionFeeTranslation(tuitionFeeId: number, translationId: number, translation: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/tuition-fees/${tuitionFeeId}/translations/${translationId}`, translation);
  }

  deleteTuitionFeeTranslation(tuitionFeeId: number, translationId: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/tuition-fees/${tuitionFeeId}/translations/${translationId}`);
  }
}
