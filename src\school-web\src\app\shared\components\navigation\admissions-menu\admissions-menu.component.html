<div class="menu-item" (mouseenter)="openMenu(admissionsMenuTrigger)" (mouseleave)="closeMenu()">
  <button mat-button [matMenuTriggerFor]="admissionsMenu" #admissionsMenuTrigger="matMenuTrigger"
          class="menu-trigger" [class.active]="activeRoute.startsWith('/admissions') || activeRoute.startsWith('/tuition')">
    {{ 'NAV.ADMISSIONS' | translate }}
    <mat-icon>arrow_drop_down</mat-icon>
  </button>
  <mat-menu #admissionsMenu="matMenu" class="mega-menu-panel" [overlapTrigger]="false" [hasBackdrop]="false">
    <div class="mega-menu-content" (mouseenter)="clearCloseTimeout()" (mouseleave)="closeMenu()">
      <div class="menu-column">
        <h3>{{ 'ADMISSIONS.PROCESS' | translate }}</h3>
        <a mat-menu-item routerLink="/admissions" [class.active]="activeRoute === '/admissions'">{{ 'ADMISSIONS.HOW_TO_APPLY' | translate }}</a>
        <a mat-menu-item routerLink="/admissions/requirements" [class.active]="activeRoute === '/admissions/requirements'">{{ 'ADMISSIONS.REQUIREMENTS' | translate }}</a>
        <a mat-menu-item routerLink="/admissions/dates" [class.active]="activeRoute === '/admissions/dates'">{{ 'ADMISSIONS.DEADLINES' | translate }}</a>
      </div>
      <div class="menu-column">
        <h3>{{ 'ADMISSIONS.COSTS' | translate }}</h3>
        <a mat-menu-item routerLink="/tuition" [class.active]="activeRoute === '/tuition'">{{ 'ADMISSIONS.TUITION' | translate }}</a>
        <a mat-menu-item routerLink="/admissions/financial-aid" [class.active]="activeRoute === '/admissions/financial-aid'">{{ 'ADMISSIONS.FINANCIAL_AID' | translate }}</a>
      </div>
      <div class="menu-column">
        <h3>{{ 'ADMISSIONS.VISIT_US' | translate }}</h3>
        <a mat-menu-item routerLink="/admissions/visit" [class.active]="activeRoute === '/admissions/visit'">{{ 'ADMISSIONS.CAMPUS_TOUR' | translate }}</a>
        <a mat-menu-item routerLink="/contact" [class.active]="activeRoute === '/contact'">{{ 'CONTACT.TITLE' | translate }}</a>
      </div>
    </div>
  </mat-menu>
</div>
