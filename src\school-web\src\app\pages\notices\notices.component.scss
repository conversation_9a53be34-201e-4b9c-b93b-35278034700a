.notices-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;

  .page-header {
    text-align: center;
    margin-bottom: 2rem;

    h1 {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
      color: #3f51b5; // Primary color
    }

    .subtitle {
      font-size: 1.2rem;
      color: #666;
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .notices-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: space-between;

    mat-form-field {
      flex: 1;
      min-width: 200px;
    }

    .search-field {
      flex: 2;
    }
  }

  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 0;
    text-align: center;
  }

  .error-message {
    color: #f44336;
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }

  .no-notices {
    text-align: center;
    padding: 2rem;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 1rem;

    p {
      font-size: 1.1rem;
      color: #666;
    }
  }

  .notices-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;

    .notice-card {
      display: flex;
      flex-direction: column;
      height: 100%;
      transition: transform 0.2s, box-shadow 0.2s;
      border-left: 4px solid #ccc;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
      }

      &.priority-high {
        border-left-color: #f44336; // Red for high priority
      }

      &.priority-medium {
        border-left-color: #ff9800; // Orange for medium priority
      }

      &.priority-low {
        border-left-color: #4caf50; // Green for low priority
      }

      mat-card-header {
        .notice-icon {
          background-color: #f5f5f5;
          display: flex;
          align-items: center;
          justify-content: center;

          mat-icon {
            color: #3f51b5;
          }
        }
      }

      mat-card-content {
        flex-grow: 1;
        margin-top: 1rem;

        p {
          color: #555;
          line-height: 1.5;
        }
      }

      mat-card-actions {
        padding: 8px 16px 16px;
      }
    }

    .no-notices {
      grid-column: 1 / -1;
      text-align: center;
      padding: 3rem 0;
      color: #666;

      mat-icon {
        font-size: 3rem;
        height: 3rem;
        width: 3rem;
        margin-bottom: 1rem;
        color: #ccc;
      }

      p {
        font-size: 1.2rem;
      }
    }
  }

  mat-paginator {
    margin-top: 1rem;
    background-color: transparent;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .notices-container {
    .notices-list {
      grid-template-columns: 1fr;
    }
  }
}
