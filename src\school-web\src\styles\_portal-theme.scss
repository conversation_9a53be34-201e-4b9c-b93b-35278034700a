// Portal Theme Mixin - Consistent theming across all portals
// This file provides consistent theme variables for all portal layouts

@mixin portal-theme-variables {
  // Base portal theme variables using Material Design 3 tokens
  --portal-primary: var(--sys-color-primary);
  --portal-on-primary: var(--sys-color-on-primary);
  --portal-primary-container: var(--sys-color-primary-container);
  --portal-on-primary-container: var(--sys-color-on-primary-container);
  
  --portal-secondary: var(--sys-color-secondary);
  --portal-on-secondary: var(--sys-color-on-secondary);
  --portal-secondary-container: var(--sys-color-secondary-container);
  --portal-on-secondary-container: var(--sys-color-on-secondary-container);
  
  --portal-surface: var(--sys-color-surface);
  --portal-on-surface: var(--sys-color-on-surface);
  --portal-surface-variant: var(--sys-color-surface-variant);
  --portal-on-surface-variant: var(--sys-color-on-surface-variant);
  --portal-surface-container: var(--sys-color-surface-container);
  --portal-surface-container-low: var(--sys-color-surface-container-low);
  --portal-surface-container-lowest: var(--sys-color-surface-container-lowest);
  --portal-surface-container-high: var(--sys-color-surface-container-high);
  --portal-surface-container-highest: var(--sys-color-surface-container-highest);
  
  --portal-outline: var(--sys-color-outline);
  --portal-outline-variant: var(--sys-color-outline-variant);
  --portal-shadow: var(--sys-color-shadow);
  --portal-scrim: var(--sys-color-scrim);
  
  --portal-error: var(--sys-color-error);
  --portal-on-error: var(--sys-color-on-error);
  --portal-error-container: var(--sys-color-error-container);
  --portal-on-error-container: var(--sys-color-on-error-container);
  
  // Portal-specific semantic variables
  --portal-sidebar-bg: var(--portal-surface-container-high);
  --portal-sidebar-text: var(--portal-on-surface);
  --portal-sidebar-active-bg: var(--portal-primary-container);
  --portal-sidebar-active-text: var(--portal-on-primary-container);
  --portal-sidebar-hover-bg: var(--portal-surface-container-highest);
  
  --portal-header-bg: var(--portal-surface);
  --portal-header-text: var(--portal-on-surface);
  --portal-header-border: var(--portal-outline-variant);
  
  --portal-content-bg: var(--portal-surface-container-lowest);
  --portal-content-text: var(--portal-on-surface);
  
  --portal-card-bg: var(--portal-surface);
  --portal-card-text: var(--portal-on-surface);
  --portal-card-border: var(--portal-outline-variant);
  
  --portal-button-primary-bg: var(--portal-primary);
  --portal-button-primary-text: var(--portal-on-primary);
  --portal-button-secondary-bg: var(--portal-secondary);
  --portal-button-secondary-text: var(--portal-on-secondary);
  
  --portal-input-bg: var(--portal-surface-variant);
  --portal-input-text: var(--portal-on-surface-variant);
  --portal-input-border: var(--portal-outline);
  --portal-input-focus-border: var(--portal-primary);
  
  --portal-divider: var(--portal-outline-variant);
  --portal-shadow-light: rgba(var(--portal-shadow), 0.08);
  --portal-shadow-medium: rgba(var(--portal-shadow), 0.12);
  --portal-shadow-heavy: rgba(var(--portal-shadow), 0.16);
}

// Dark theme overrides for portals
@mixin portal-dark-theme-overrides {
  // Enhanced dark theme with better contrast
  --portal-sidebar-bg: #1a1a1a;
  --portal-sidebar-text: #e0e0e0;
  --portal-sidebar-active-bg: rgba(99, 102, 241, 0.2);
  --portal-sidebar-active-text: #ffffff;
  --portal-sidebar-hover-bg: rgba(255, 255, 255, 0.08);

  --portal-header-bg: #1e1e1e;
  --portal-header-text: #ffffff;
  --portal-header-border: #333333;

  --portal-content-bg: #121212;
  --portal-content-text: #e0e0e0;

  --portal-card-bg: #1e1e1e;
  --portal-card-text: #ffffff;
  --portal-card-border: #333333;

  --portal-button-primary-bg: #6366f1;
  --portal-button-primary-text: #ffffff;

  --portal-input-bg: #2a2a2a;
  --portal-input-text: #ffffff;
  --portal-input-border: #404040;
  --portal-input-focus-border: #6366f1;

  --portal-divider: #333333;

  // Enhanced shadows for dark theme
  --portal-shadow-light: rgba(0, 0, 0, 0.4);
  --portal-shadow-medium: rgba(0, 0, 0, 0.6);
  --portal-shadow-heavy: rgba(0, 0, 0, 0.8);

  // Override Material Design tokens for better dark contrast
  --sys-color-primary: #6366f1;
  --sys-color-on-primary: #ffffff;
  --sys-color-primary-container: rgba(99, 102, 241, 0.2);
  --sys-color-on-primary-container: #ffffff;

  --sys-color-surface: #1e1e1e;
  --sys-color-on-surface: #e0e0e0;
  --sys-color-surface-variant: #2a2a2a;
  --sys-color-on-surface-variant: #ffffff;
  --sys-color-surface-container: #1a1a1a;
  --sys-color-surface-container-low: #161616;
  --sys-color-surface-container-lowest: #121212;
  --sys-color-surface-container-high: #242424;
  --sys-color-surface-container-highest: #2a2a2a;

  --sys-color-outline: #404040;
  --sys-color-outline-variant: #333333;
}

// Light theme overrides for portals
@mixin portal-light-theme-overrides {
  // Enhanced light theme with better contrast and professional sidebar
  --portal-sidebar-bg: #1f2937;
  --portal-sidebar-text: #e5e7eb;
  --portal-sidebar-active-bg: #6366f1;
  --portal-sidebar-active-text: #ffffff;
  --portal-sidebar-hover-bg: rgba(255, 255, 255, 0.1);

  --portal-header-bg: #ffffff;
  --portal-header-text: #1f2937;
  --portal-header-border: #e5e7eb;

  --portal-content-bg: #f9fafb;
  --portal-content-text: #1f2937;

  --portal-card-bg: #ffffff;
  --portal-card-text: #1f2937;
  --portal-card-border: #e5e7eb;

  --portal-button-primary-bg: #6366f1;
  --portal-button-primary-text: #ffffff;

  --portal-input-bg: #ffffff;
  --portal-input-text: #1f2937;
  --portal-input-border: #d1d5db;
  --portal-input-focus-border: #6366f1;

  --portal-divider: #e5e7eb;

  // Light theme shadows
  --portal-shadow-light: rgba(0, 0, 0, 0.05);
  --portal-shadow-medium: rgba(0, 0, 0, 0.1);
  --portal-shadow-heavy: rgba(0, 0, 0, 0.15);

  // Override Material Design tokens for better light contrast
  --sys-color-primary: #6366f1;
  --sys-color-on-primary: #ffffff;
  --sys-color-primary-container: #e0e7ff;
  --sys-color-on-primary-container: #1e1b4b;

  --sys-color-surface: #ffffff;
  --sys-color-on-surface: #1f2937;
  --sys-color-surface-variant: #f3f4f6;
  --sys-color-on-surface-variant: #374151;
  --sys-color-surface-container: #f9fafb;
  --sys-color-surface-container-low: #ffffff;
  --sys-color-surface-container-lowest: #ffffff;
  --sys-color-surface-container-high: #f3f4f6;
  --sys-color-surface-container-highest: #e5e7eb;

  --sys-color-outline: #d1d5db;
  --sys-color-outline-variant: #e5e7eb;
}

// Base portal layout styles
@mixin portal-base-layout {
  display: flex;
  min-height: 100vh;
  background: var(--portal-content-bg);
  color: var(--portal-content-text);
  
  // Ensure theme transitions are smooth
  transition: background-color 0.3s ease, color 0.3s ease;
}

// Sidebar base styles
@mixin portal-sidebar-base {
  background: var(--portal-sidebar-bg);
  color: var(--portal-sidebar-text);
  border-right: 1px solid var(--portal-divider);
  transition: all 0.3s ease;
  
  .sidebar-header {
    background: var(--portal-sidebar-bg);
    color: var(--portal-sidebar-text);
    border-bottom: 1px solid var(--portal-divider);
  }
  
  .nav-item {
    color: var(--portal-sidebar-text);
    
    &:hover {
      background: var(--portal-sidebar-hover-bg);
    }
    
    &.active {
      background: var(--portal-sidebar-active-bg);
      color: var(--portal-sidebar-active-text);
    }
  }
}

// Header base styles
@mixin portal-header-base {
  background: var(--portal-header-bg);
  color: var(--portal-header-text);
  border-bottom: 1px solid var(--portal-header-border);
  box-shadow: 0 2px 4px var(--portal-shadow-light);
  transition: all 0.3s ease;
}

// Content area base styles
@mixin portal-content-base {
  background: var(--portal-content-bg);
  color: var(--portal-content-text);
  transition: all 0.3s ease;
}

// Card base styles
@mixin portal-card-base {
  background: var(--portal-card-bg);
  color: var(--portal-card-text);
  border: 1px solid var(--portal-card-border);
  box-shadow: 0 2px 4px var(--portal-shadow-light);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 8px var(--portal-shadow-medium);
  }
}

// Apply theme to host element
@mixin apply-portal-theme {
  @include portal-theme-variables;
  
  // Light theme (default)
  &,
  &.light-theme {
    @include portal-light-theme-overrides;
  }
  
  // Dark theme
  &.dark-theme {
    @include portal-dark-theme-overrides;
  }
  
  // Context-based theme application
  :host-context(.light-theme) & {
    @include portal-light-theme-overrides;
  }
  
  :host-context(.dark-theme) & {
    @include portal-dark-theme-overrides;
  }
}
