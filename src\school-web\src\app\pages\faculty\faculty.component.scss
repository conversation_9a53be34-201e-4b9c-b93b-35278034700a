// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Global Styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

section {
  padding: $section-padding;
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 0.5rem;
  color: $text-color;
}

.section-description {
  font-size: 1.2rem;
  text-align: center;
  margin-bottom: 3rem;
  color: $dark-gray;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

// Hero Section
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), linear-gradient(to right, #3f51b5, #2196f3);
  color: $white;
  text-align: center;
  padding: 120px 0;
  margin-bottom: 0;

  .hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;

    h1 {
      font-size: 3.5rem;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    .hero-description {
      font-size: 1.2rem;
      margin-bottom: 0;
      line-height: 1.6;
    }
  }
}

// Introduction Section
.intro-section {
  background-color: $white;

  .intro-content {
    max-width: 900px;
    margin: 0 auto;

    h2 {
      font-size: 2.2rem;
      margin-bottom: 1.5rem;
      text-align: center;
      color: $text-color;
    }

    p {
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Featured Faculty Section
.featured-faculty-section {
  background-color: $light-gray;

  .featured-faculty-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
  }

  .faculty-card {
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    &.featured-card {
      border-top: 4px solid $primary-color;
    }
  }

  .faculty-image-container {
    height: 250px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  mat-card-header {
    padding-top: 16px;
  }

  mat-card-title {
    font-size: 1.5rem;
    margin-bottom: 8px;
  }

  mat-card-subtitle {
    color: $dark-gray;
    font-size: 1rem;
  }

  .faculty-department {
    color: $primary-color;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .faculty-bio {
    margin: 16px 0;
    line-height: 1.6;
  }

  .faculty-specializations {
    margin: 16px 0;

    mat-chip-set {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  mat-card-actions {
    margin-top: auto;
    padding: 16px;
  }
}

// Faculty Directory Section
.faculty-directory-section {
  background-color: $white;

  .directory-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
    align-items: center;

    .search-field {
      flex: 1 1 300px;
    }

    .department-filter {
      flex: 0 1 200px;
    }

    .reset-button {
      height: 56px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .faculty-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
  }

  .faculty-card {
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }
  }

  .faculty-image-container {
    height: 180px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .faculty-department {
    color: $primary-color;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .faculty-bio-short {
    color: $dark-gray;
    line-height: 1.5;
    margin: 10px 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  mat-card-actions {
    margin-top: auto;
    padding: 16px;
  }

  .no-results-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 0;
    color: $dark-gray;
    font-size: 1.1rem;
  }
}

// Join Our Team Section
.join-team-section {
  background-color: $light-gray;

  .join-team-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h2 {
      font-size: 2.2rem;
      margin-bottom: 1.5rem;
      color: $text-color;
    }

    p {
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 2rem;
      color: $text-color;
    }

    .cta-button {
      padding: 12px 32px;
      font-size: 1.1rem;
      font-weight: 500;
    }
  }
}

// Responsive Adjustments
@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .hero-section {
    padding: 100px 0;

    .hero-content {
      h1 {
        font-size: 2.5rem;
      }

      .hero-description {
        font-size: 1rem;
      }
    }
  }

  .featured-faculty-grid,
  .faculty-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .directory-filters {
    flex-direction: column;
    align-items: stretch;

    .search-field,
    .department-filter {
      flex: 1 1 100%;
    }
  }

  .join-team-content {
    h2 {
      font-size: 1.8rem;
    }
  }
}

@media (max-width: 480px) {
  section {
    padding: 60px 0;
  }

  .hero-section {
    padding: 80px 0;

    .hero-content {
      h1 {
        font-size: 2rem;
      }
    }
  }

  .featured-faculty-grid,
  .faculty-grid {
    grid-template-columns: 1fr;
  }

  .faculty-card {
    max-width: 100%;
  }
}