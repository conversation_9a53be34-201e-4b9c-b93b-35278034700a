import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-alumni-network',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div class="network-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Alumni Network</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Alumni networking features coming soon...</p>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .network-container {
      padding: 24px;
    }
  `]
})
export class AlumniNetworkComponent { }
