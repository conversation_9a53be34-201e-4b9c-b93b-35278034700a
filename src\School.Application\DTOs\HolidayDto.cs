using School.Application.DTOs.Common;
using School.Domain.Enums;
using School.Domain.ValueObjects;

namespace School.Application.DTOs;

public class HolidayDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public HolidayType Type { get; set; }
    public bool IsRecurring { get; set; }
    public RecurrencePattern? RecurrencePattern { get; set; }
    public string Color { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsPublic { get; set; }
    public string Remarks { get; set; } = string.Empty;
    
    // Foreign key relationships
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }
    
    // Navigation properties for display
    public string AcademicYearName { get; set; } = string.Empty;
    public string TermName { get; set; } = string.Empty;
    
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public List<HolidayTranslationDto> Translations { get; set; } = new List<HolidayTranslationDto>();
}

public class HolidayTranslationDto
{
    public Guid Id { get; set; }
    public Guid HolidayId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

public class CreateHolidayDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public HolidayType Type { get; set; }
    public bool IsRecurring { get; set; } = false;
    public RecurrencePattern? RecurrencePattern { get; set; }
    public string Color { get; set; } = "#FF5722";
    public bool IsActive { get; set; } = true;
    public bool IsPublic { get; set; } = true;
    public string Remarks { get; set; } = string.Empty;
    
    // Foreign key relationships
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }
    
    public List<CreateHolidayTranslationDto>? Translations { get; set; }
}

public class UpdateHolidayDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public HolidayType Type { get; set; }
    public bool IsRecurring { get; set; }
    public RecurrencePattern? RecurrencePattern { get; set; }
    public string Color { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsPublic { get; set; }
    public string Remarks { get; set; } = string.Empty;
    
    // Foreign key relationships
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }
}

public class CreateHolidayTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

public class UpdateHolidayTranslationDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

/// <summary>
/// Filter DTO for holiday queries
/// </summary>
public class HolidayFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filter by name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Filter by holiday type
    /// </summary>
    public HolidayType? Type { get; set; }

    /// <summary>
    /// Filter by academic year ID
    /// </summary>
    public Guid? AcademicYearId { get; set; }

    /// <summary>
    /// Filter by term ID
    /// </summary>
    public Guid? TermId { get; set; }

    /// <summary>
    /// Filter by start date
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Filter by end date
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter by public status
    /// </summary>
    public bool? IsPublic { get; set; }

    /// <summary>
    /// Filter by recurring status
    /// </summary>
    public bool? IsRecurring { get; set; }
}

/// <summary>
/// Holiday event DTO for calendar display
/// </summary>
public class HolidayEventDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public DateTime Start { get; set; }
    public DateTime End { get; set; }
    public bool AllDay { get; set; } = true;
    public HolidayType Type { get; set; }
    public string Color { get; set; } = string.Empty;
    public bool IsRecurring { get; set; }
}
