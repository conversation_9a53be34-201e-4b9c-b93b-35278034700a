<div class="media-upload-container">
  <div class="page-header">
    <h1>{{ 'admin.media.uploadMedia' | translate }}</h1>
    <button mat-button color="primary" routerLink="/admin/media">
      <mat-icon>arrow_back</mat-icon>
      {{ 'BUTTONS.BACK' | translate }} - {{ 'admin.media.title' | translate }}
    </button>
  </div>

  <div class="upload-content">
    <mat-card class="upload-form-card">
      <mat-card-content>
        <form [formGroup]="uploadForm" (ngSubmit)="uploadFiles()">
          <div class="form-fields">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>{{ 'admin.media.type' | translate }}</mat-label>
              <mat-select formControlName="type" required>
                <mat-option [value]="0">{{ 'admin.media.types.image' | translate }}</mat-option>
                <mat-option [value]="1">{{ 'admin.media.types.document' | translate }}</mat-option>
                <mat-option [value]="2">{{ 'admin.media.types.video' | translate }}</mat-option>
                <mat-option [value]="3">{{ 'admin.media.types.audio' | translate }}</mat-option>
              </mat-select>
              <mat-error *ngIf="uploadForm.get('type')?.hasError('required')">
                {{ 'admin.media.typeRequired' | translate }}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>{{ 'admin.media.altText' | translate }}</mat-label>
              <input matInput formControlName="altText">
              <mat-hint>{{ 'admin.media.altTextHint' | translate }}</mat-hint>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>{{ 'admin.media.caption' | translate }}</mat-label>
              <textarea matInput formControlName="caption" rows="3"></textarea>
              <mat-hint>{{ 'admin.media.captionHint' | translate }}</mat-hint>
            </mat-form-field>

            <div class="file-upload-container">
              <input type="file" #fileInput style="display: none" (change)="onFileSelected($event)" multiple>
              <button type="button" mat-raised-button color="primary" (click)="fileInput.click()">
                <mat-icon>attach_file</mat-icon>
                {{ 'admin.media.selectFiles' | translate }}
              </button>
              <span class="file-hint">{{ 'admin.media.selectFilesHint' | translate }}</span>
            </div>
          </div>

          <div class="selected-files" *ngIf="selectedFiles.length > 0">
            <h3>{{ 'admin.media.selectedFiles' | translate }} ({{ selectedFiles.length }})</h3>

            <div class="file-list">
              <div class="file-item" *ngFor="let file of selectedFiles; let i = index">
                <div class="file-preview" *ngIf="previewUrls[i]">
                  <img [src]="previewUrls[i]" alt="Preview">
                </div>
                <div class="file-preview file-icon" *ngIf="!previewUrls[i]">
                  <mat-icon>insert_drive_file</mat-icon>
                </div>
                <div class="file-info">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-size">{{ file.size | number }} bytes</div>
                </div>
                <button type="button" mat-icon-button color="warn" (click)="removeFile(i)">
                  <mat-icon>close</mat-icon>
                </button>
              </div>
            </div>
          </div>

          <div class="upload-progress" *ngIf="isUploading">
            <mat-progress-bar mode="determinate" [value]="uploadProgress"></mat-progress-bar>
            <div class="progress-text">{{ uploadProgress }}%</div>
          </div>

          <div class="form-actions">
            <button mat-button type="button" (click)="cancel()" [disabled]="isUploading">
              {{ 'BUTTONS.CANCEL' | translate }}
            </button>
            <button mat-raised-button color="primary" type="submit" [disabled]="uploadForm.invalid || selectedFiles.length === 0 || isUploading">
              <mat-spinner *ngIf="isUploading" diameter="24" class="spinner"></mat-spinner>
              <span *ngIf="!isUploading">{{ 'BUTTONS.UPLOAD' | translate }}</span>
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>
