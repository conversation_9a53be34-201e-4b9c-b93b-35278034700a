using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Policy;
using Microsoft.Extensions.Logging;
using School.API.Common;
using System.Security.Claims;

namespace School.API.Middleware
{
    public class CustomAuthorizationFilter : IAuthorizationMiddlewareResultHandler
    {
        private readonly AuthorizationMiddlewareResultHandler _defaultHandler = new();
        private readonly ILogger<CustomAuthorizationFilter> _logger;

        public CustomAuthorizationFilter(ILogger<CustomAuthorizationFilter> logger)
        {
            _logger = logger;
        }

        public async Task HandleAsync(
            RequestDelegate next,
            HttpContext context,
            AuthorizationPolicy policy,
            PolicyAuthorizationResult authorizeResult)
        {
            // Log the authorization attempt
            _logger.LogInformation("Authorization attempt for path: {Path}", context.Request.Path);

            // Log the authorization headers
            var authHeader = context.Request.Headers.Authorization.ToString();
            _logger.LogInformation("Authorization header: {AuthHeader}",
                !string.IsNullOrEmpty(authHeader) ?
                $"{authHeader.Substring(0, Math.Min(20, authHeader.Length))}..." :
                "Not present");

            // If the authorization was successful, continue with the request
            if (authorizeResult.Succeeded)
            {
                _logger.LogInformation("Authorization succeeded for path: {Path}", context.Request.Path);

                // Log the user claims for debugging
                if (context.User?.Identity?.IsAuthenticated == true)
                {
                    var userId = context.User.FindFirstValue(ClaimTypes.NameIdentifier) ??
                                context.User.FindFirstValue("userId") ?? "Unknown";
                    var username = context.User.Identity.Name ?? "Unknown";
                    var role = context.User.FindFirstValue(ClaimTypes.Role) ??
                              context.User.FindFirstValue("role") ?? "Unknown";

                    _logger.LogInformation("Authenticated user: {Username} (ID: {UserId}, Role: {Role})",
                        username, userId, role);
                }

                await next(context);
                return;
            }

            // If the user is authenticated but doesn't have the required role
            if (context.User.Identity?.IsAuthenticated == true && authorizeResult.Challenged == false && authorizeResult.Forbidden)
            {
                _logger.LogWarning("Forbidden access for path: {Path}. User is authenticated but lacks required role.",
                    context.Request.Path);

                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                await context.Response.WriteAsJsonAsync(
                    ApiResponse.ErrorResponse(
                        "You do not have permission to access this resource. This endpoint requires specific privileges.",
                        StatusCodes.Status403Forbidden));
                return;
            }

            // If the user is not authenticated
            if (authorizeResult.Challenged)
            {
                _logger.LogWarning("Unauthorized access for path: {Path}. Authentication required.",
                    context.Request.Path);

                // Log the failure reason if available
                if (authorizeResult.AuthorizationFailure != null)
                {
                    foreach (var failure in authorizeResult.AuthorizationFailure.FailedRequirements)
                    {
                        _logger.LogWarning("Authorization requirement failed: {Requirement}",
                            failure.GetType().Name);
                    }
                }

                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsJsonAsync(
                    ApiResponse.ErrorResponse(
                        "Authentication required. Please log in to access this resource.",
                        StatusCodes.Status401Unauthorized));
                return;
            }

            // For any other case, use the default handler
            _logger.LogWarning("Using default authorization handler for path: {Path}",
                context.Request.Path);
            await _defaultHandler.HandleAsync(next, context, policy, authorizeResult);
        }
    }
}
