import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';

interface Event {
  id: number;
  title: string;
  date: Date;
  endDate?: Date;
  time: string;
  location: string;
  category: string;
  image: string;
  description: string;
  organizer: string;
  contactEmail?: string;
  contactPhone?: string;
  registrationRequired: boolean;
  registrationLink?: string;
  registrationDeadline?: Date;
  featured: boolean;
  tags?: string[];
  schedule?: {
    time: string;
    activity: string;
  }[];
  speakers?: {
    name: string;
    title: string;
    bio?: string;
    image?: string;
  }[];
}

@Component({
  selector: 'app-event-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatDividerModule,
    TranslateModule
  ],
  templateUrl: './event-detail.component.html',
  styleUrls: ['./event-detail.component.scss']
})
export class EventDetailComponent implements OnInit {
  // Event data
  event: Event | null = null;

  // Loading state
  loading = true;

  // Error state
  error = false;

  // Mock event data (in a real app, this would come from a service)
  private eventsData: Event[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadEventsData();

    // Get event ID from route params
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.loadEvent(parseInt(id, 10));
      } else {
        this.error = true;
        this.loading = false;
      }
    });
  }

  /**
   * Load events data
   */
  private loadEventsData(): void {
    // In a real app, this would be a service call
    this.eventsData = [
      {
        id: 1,
        title: 'Annual Science Fair',
        date: new Date('2023-11-15'),
        time: '9:00 AM - 4:00 PM',
        location: 'School Gymnasium',
        category: 'Academic',
        image: 'https://via.placeholder.com/800x400?text=Science+Fair',
        description: 'Join us for our Annual Science Fair where students from all grades will showcase their innovative science projects. This year\'s theme is "Sustainable Solutions for a Better Future." Projects will be judged by a panel of science professionals from local universities and research institutions. Parents, family members, and the community are invited to attend and support our young scientists. Refreshments will be provided.',
        organizer: 'Science Department',
        contactEmail: '<EMAIL>',
        registrationRequired: false,
        featured: true,
        tags: ['Science', 'STEM', 'Competition', 'Exhibition'],
        schedule: [
          { time: '9:00 AM - 10:00 AM', activity: 'Setup and Registration' },
          { time: '10:00 AM - 12:00 PM', activity: 'Judging Session (Closed to Public)' },
          { time: '12:00 PM - 1:00 PM', activity: 'Lunch Break' },
          { time: '1:00 PM - 3:30 PM', activity: 'Public Viewing' },
          { time: '3:30 PM - 4:00 PM', activity: 'Awards Ceremony' }
        ]
      },
      {
        id: 2,
        title: 'Parent-Teacher Conference',
        date: new Date('2023-10-20'),
        time: '1:00 PM - 7:00 PM',
        location: 'School Classrooms',
        category: 'Meeting',
        image: 'https://via.placeholder.com/800x400?text=Parent-Teacher+Conference',
        description: 'Our fall Parent-Teacher Conference provides an opportunity for parents to meet with teachers and discuss their child\'s academic progress, behavior, and social development. Each conference session is scheduled for 15 minutes. Please arrive 5 minutes before your scheduled time. If you need more time with a particular teacher, please arrange for a follow-up meeting.',
        organizer: 'Administration',
        contactEmail: '<EMAIL>',
        contactPhone: '(*************',
        registrationRequired: true,
        registrationLink: 'https://school.edu/ptc-registration',
        registrationDeadline: new Date('2023-10-15'),
        featured: true,
        tags: ['Parents', 'Teachers', 'Academic Progress']
      },
      {
        id: 3,
        title: 'Annual Sports Day',
        date: new Date('2023-12-05'),
        time: '8:30 AM - 3:00 PM',
        location: 'School Sports Field',
        category: 'Sports',
        image: 'https://via.placeholder.com/800x400?text=Sports+Day',
        description: 'Our Annual Sports Day is a celebration of physical fitness, teamwork, and sportsmanship. Students will participate in various track and field events, team sports, and fun competitions. Parents and family members are encouraged to attend and cheer for the participants. There will be refreshments available and a special awards ceremony at the end of the day to recognize outstanding performances.',
        organizer: 'Physical Education Department',
        contactEmail: '<EMAIL>',
        registrationRequired: false,
        featured: false,
        tags: ['Sports', 'Competition', 'Physical Fitness'],
        schedule: [
          { time: '8:30 AM - 9:00 AM', activity: 'Opening Ceremony' },
          { time: '9:00 AM - 11:30 AM', activity: 'Track Events' },
          { time: '11:30 AM - 12:30 PM', activity: 'Lunch Break' },
          { time: '12:30 PM - 2:30 PM', activity: 'Field Events and Team Sports' },
          { time: '2:30 PM - 3:00 PM', activity: 'Awards Ceremony' }
        ]
      },
      {
        id: 4,
        title: 'Career Day',
        date: new Date('2023-11-10'),
        time: '10:00 AM - 2:00 PM',
        location: 'School Auditorium',
        category: 'Career',
        image: 'https://via.placeholder.com/800x400?text=Career+Day',
        description: 'Career Day provides students with the opportunity to learn about various professions and career paths. We have invited professionals from different fields to share their experiences, educational background, and insights about their careers. Students will have the chance to ask questions and engage in discussions about potential career options. This event is particularly valuable for high school students who are beginning to think about their future educational and career choices.',
        organizer: 'Guidance Department',
        contactEmail: '<EMAIL>',
        registrationRequired: false,
        featured: false,
        tags: ['Career', 'Professional Development', 'Guidance'],
        speakers: [
          {
            name: 'Dr. Sarah Johnson',
            title: 'Neurosurgeon',
            bio: 'Dr. Johnson is a leading neurosurgeon at City Hospital with over 15 years of experience.',
            image: 'https://via.placeholder.com/100x100?text=Dr.+Johnson'
          },
          {
            name: 'Mr. Michael Chen',
            title: 'Software Engineer',
            bio: 'Mr. Chen is a senior software engineer at Tech Innovations and a former graduate of our school.',
            image: 'https://via.placeholder.com/100x100?text=Mr.+Chen'
          },
          {
            name: 'Ms. Priya Patel',
            title: 'Environmental Scientist',
            bio: 'Ms. Patel works with the Environmental Protection Agency and specializes in climate change research.',
            image: 'https://via.placeholder.com/100x100?text=Ms.+Patel'
          }
        ]
      },
      {
        id: 5,
        title: 'Winter Concert',
        date: new Date('2023-12-15'),
        time: '6:30 PM - 8:30 PM',
        location: 'School Auditorium',
        category: 'Arts',
        image: 'https://via.placeholder.com/800x400?text=Winter+Concert',
        description: 'Join us for an evening of music and celebration at our annual Winter Concert. Students from our choir, band, and orchestra will perform a variety of seasonal and classical pieces. This event showcases the hard work and talent of our music students and provides an opportunity for the community to enjoy live music. Tickets are required for this event and can be purchased online or at the school office.',
        organizer: 'Music Department',
        contactEmail: '<EMAIL>',
        registrationRequired: true,
        registrationLink: 'https://school.edu/winter-concert-tickets',
        featured: true,
        tags: ['Music', 'Performance', 'Arts'],
        schedule: [
          { time: '6:30 PM - 7:00 PM', activity: 'Choir Performance' },
          { time: '7:00 PM - 7:30 PM', activity: 'Band Performance' },
          { time: '7:30 PM - 8:00 PM', activity: 'Orchestra Performance' },
          { time: '8:00 PM - 8:30 PM', activity: 'Combined Finale' }
        ]
      }
    ];
  }

  /**
   * Load event by ID
   */
  private loadEvent(id: number): void {
    const event = this.eventsData.find(event => event.id === id);

    if (event) {
      this.event = event;
      this.loading = false;
    } else {
      this.error = true;
      this.loading = false;
    }
  }

  /**
   * Navigate back to events listing
   */
  goBack(): void {
    this.router.navigate(['/events']);
  }

  /**
   * Format date range for display
   */
  formatDateRange(): string {
    if (!this.event) return '';
    
    const options: Intl.DateTimeFormatOptions = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    
    if (this.event.endDate) {
      const startDate = this.event.date.toLocaleDateString('en-US', options);
      const endDate = this.event.endDate.toLocaleDateString('en-US', options);
      return `${startDate} - ${endDate}`;
    } else {
      return this.event.date.toLocaleDateString('en-US', options);
    }
  }

  /**
   * Check if registration deadline has passed
   */
  isRegistrationClosed(): boolean {
    if (!this.event || !this.event.registrationDeadline) return false;
    
    const today = new Date();
    return today > this.event.registrationDeadline;
  }

  /**
   * Check if event has already occurred
   */
  isEventPast(): boolean {
    if (!this.event) return false;
    
    const today = new Date();
    const eventDate = this.event.endDate || this.event.date;
    return today > eventDate;
  }
}
