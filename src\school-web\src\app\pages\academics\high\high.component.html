<!-- Hero Section -->
<app-default-hero
  translationPrefix="ACADEMICS"
  title="ACADEMICS.HIGH"
  subtitle="ACADEMICS.HIGH_SUBTITLE"
  theme="dark"
  size="large"
  alignment="center"
  backgroundImage="assets/images/high-hero.jpg">
</app-default-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'ACADEMICS.HIGH_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'ACADEMICS.HIGH_INTRO_P1' | translate }}</p>
      <p>{{ 'ACADEMICS.HIGH_INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Key Features Section -->
  <section class="features-section">
    <h2>{{ 'ACADEMICS.KEY_FEATURES' | translate }}</h2>
    <div class="features-grid">
      <mat-card class="feature-card" *ngFor="let feature of keyFeatures">
        <div class="feature-icon">
          <mat-icon>{{feature.icon}}</mat-icon>
        </div>
        <mat-card-content>
          <h3>{{feature.title}}</h3>
          <p>{{feature.description}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Academic Departments Section -->
  <section class="departments-section">
    <h2>{{ 'ACADEMICS.DEPARTMENTS' | translate }}</h2>
    <div class="departments-container">
      <mat-tab-group animationDuration="300ms">
        <mat-tab *ngFor="let department of departments" [label]="department.name">
          <div class="department-content">
            <p class="department-description">{{department.description}}</p>

            <h3>{{ 'ACADEMICS.COURSES' | translate }}</h3>
            <div class="courses-list">
              <div class="course-item" *ngFor="let course of department.courses">
                <mat-icon>school</mat-icon>
                <span>{{course}}</span>
              </div>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </section>

  <!-- Curriculum Section -->
  <section class="curriculum-section">
    <h2>{{ 'ACADEMICS.CURRICULUM' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.HIGH_CURRICULUM_INTRO' | translate }}</p>

    <div class="courses-container">
      <h3>{{ 'ACADEMICS.SAMPLE_COURSES' | translate }}</h3>
      <div class="courses-grid">
        <mat-card class="course-card" *ngFor="let course of sampleCourses">
          <mat-card-content>
            <h4>{{course.name}}</h4>
            <div class="course-details">
              <span class="course-grades">{{ 'ACADEMICS.GRADES' | translate }}: {{course.grades}}</span>
              <span class="course-credits">{{ 'ACADEMICS.CREDITS' | translate }}: {{course.credits}}</span>
            </div>
            <p>{{course.description}}</p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </section>

  <!-- Graduation Requirements Section -->
  <section class="requirements-section">
    <h2>{{ 'ACADEMICS.GRADUATION_REQUIREMENTS' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.REQUIREMENTS_INTRO' | translate }}</p>

    <div class="requirements-table">
      <table>
        <thead>
          <tr>
            <th>{{ 'ACADEMICS.SUBJECT' | translate }}</th>
            <th>{{ 'ACADEMICS.CREDITS_REQUIRED' | translate }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let req of graduationRequirements">
            <td>{{req.subject}}</td>
            <td>{{req.credits}}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="requirements-note">
      <mat-icon>info</mat-icon>
      <p>{{ 'ACADEMICS.REQUIREMENTS_NOTE' | translate }}</p>
    </div>
  </section>

  <!-- Special Programs Section -->
  <section class="special-programs-section">
    <h2>{{ 'ACADEMICS.SPECIAL_PROGRAMS' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.HIGH_SPECIAL_PROGRAMS_INTRO' | translate }}</p>

    <div class="programs-grid">
      <mat-card class="program-card" *ngFor="let program of specialPrograms">
        <div class="program-image">
          <img [src]="program.image" [alt]="program.title">
        </div>
        <mat-card-content>
          <h3>{{program.title}}</h3>
          <p>{{program.description}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- College Counseling Section -->
  <section class="college-section">
    <h2>{{ 'ACADEMICS.COLLEGE_COUNSELING' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.COLLEGE_COUNSELING_INTRO' | translate }}</p>

    <div class="college-process">
      <div class="process-step">
        <div class="step-number">1</div>
        <div class="step-content">
          <h3>{{ 'ACADEMICS.COLLEGE_STEP1_TITLE' | translate }}</h3>
          <p>{{ 'ACADEMICS.COLLEGE_STEP1_DESC' | translate }}</p>
        </div>
      </div>

      <div class="process-step">
        <div class="step-number">2</div>
        <div class="step-content">
          <h3>{{ 'ACADEMICS.COLLEGE_STEP2_TITLE' | translate }}</h3>
          <p>{{ 'ACADEMICS.COLLEGE_STEP2_DESC' | translate }}</p>
        </div>
      </div>

      <div class="process-step">
        <div class="step-number">3</div>
        <div class="step-content">
          <h3>{{ 'ACADEMICS.COLLEGE_STEP3_TITLE' | translate }}</h3>
          <p>{{ 'ACADEMICS.COLLEGE_STEP3_DESC' | translate }}</p>
        </div>
      </div>

      <div class="process-step">
        <div class="step-number">4</div>
        <div class="step-content">
          <h3>{{ 'ACADEMICS.COLLEGE_STEP4_TITLE' | translate }}</h3>
          <p>{{ 'ACADEMICS.COLLEGE_STEP4_DESC' | translate }}</p>
        </div>
      </div>
    </div>

    <div class="college-acceptances">
      <h3>{{ 'ACADEMICS.RECENT_ACCEPTANCES' | translate }}</h3>
      <div class="acceptances-grid">
        <div class="acceptance-item" *ngFor="let college of collegeAcceptances">
          <div class="college-name">{{college.university}}</div>
          <div class="college-location">{{college.location}}</div>
        </div>
      </div>
    </div>
  </section>

  <!-- Extracurricular Activities Section -->
  <section class="extracurricular-section">
    <h2>{{ 'ACADEMICS.EXTRACURRICULAR' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.HIGH_EXTRACURRICULAR_INTRO' | translate }}</p>

    <div class="activities-container">
      <div class="activity-category" *ngFor="let category of extracurriculars">
        <h3>{{category.category}}</h3>
        <div class="activities-list">
          <div class="activity-item" *ngFor="let activity of category.activities">
            <mat-icon>star</mat-icon>
            <span>{{activity}}</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Faculty Section -->
  <section class="faculty-section">
    <h2>{{ 'ACADEMICS.FACULTY' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.HIGH_FACULTY_INTRO' | translate }}</p>

    <div class="faculty-cta">
      <a mat-raised-button color="primary" routerLink="/faculty">
        {{ 'ACADEMICS.MEET_FACULTY' | translate }}
      </a>
    </div>
  </section>

  <!-- Call to Action Section -->
  <section class="cta-section">
    <div class="cta-content">
      <h2>{{ 'ACADEMICS.VISIT_HIGH' | translate }}</h2>
      <p>{{ 'ACADEMICS.VISIT_HIGH_TEXT' | translate }}</p>
      <div class="cta-buttons">
        <a mat-raised-button color="primary" routerLink="/admissions">
          {{ 'ACADEMICS.APPLY_NOW' | translate }}
        </a>
        <a mat-stroked-button color="primary" routerLink="/contact">
          {{ 'ACADEMICS.SCHEDULE_TOUR' | translate }}
        </a>
      </div>
    </div>
  </section>
</div>
