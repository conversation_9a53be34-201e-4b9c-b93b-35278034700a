using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Term;
using School.Domain.Entities;
using School.Domain.Enums;

namespace School.Infrastructure.Services;

public class TermService : ITermService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<TermService> _logger;

    public TermService(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        ILogger<TermService> logger)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<(IEnumerable<TermDto> Terms, int TotalCount)> GetAllTermsAsync(TermFilterDto filter)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var query = repository.AsQueryable("AcademicYear,Translations");

        // Apply filters
        if (filter.AcademicYearId.HasValue)
        {
            query = query.Where(t => t.AcademicYearId == filter.AcademicYearId.Value);
        }

        if (!string.IsNullOrEmpty(filter.Name))
        {
            query = query.Where(t => t.Name.Contains(filter.Name));
        }

        if (!string.IsNullOrEmpty(filter.Code))
        {
            query = query.Where(t => t.Code.Contains(filter.Code));
        }

        if (filter.Type.HasValue)
        {
            query = query.Where(t => t.Type == filter.Type.Value);
        }

        if (filter.Status.HasValue)
        {
            query = query.Where(t => t.Status == filter.Status.Value);
        }

        if (filter.StartDateFrom.HasValue)
        {
            query = query.Where(t => t.StartDate >= filter.StartDateFrom.Value);
        }

        if (filter.StartDateTo.HasValue)
        {
            query = query.Where(t => t.StartDate <= filter.StartDateTo.Value);
        }

        if (filter.EndDateFrom.HasValue)
        {
            query = query.Where(t => t.EndDate >= filter.EndDateFrom.Value);
        }

        if (filter.EndDateTo.HasValue)
        {
            query = query.Where(t => t.EndDate <= filter.EndDateTo.Value);
        }

        // Apply sorting
        if (!string.IsNullOrEmpty(filter.SortBy))
        {
            switch (filter.SortBy.ToLower())
            {
                case "name":
                    query = filter.SortDescending ? query.OrderByDescending(t => t.Name) : query.OrderBy(t => t.Name);
                    break;
                case "startdate":
                    query = filter.SortDescending ? query.OrderByDescending(t => t.StartDate) : query.OrderBy(t => t.StartDate);
                    break;
                case "enddate":
                    query = filter.SortDescending ? query.OrderByDescending(t => t.EndDate) : query.OrderBy(t => t.EndDate);
                    break;
                case "orderindex":
                    query = filter.SortDescending ? query.OrderByDescending(t => t.OrderIndex) : query.OrderBy(t => t.OrderIndex);
                    break;
                default:
                    query = query.OrderBy(t => t.AcademicYearId).ThenBy(t => t.OrderIndex);
                    break;
            }
        }
        else
        {
            query = query.OrderBy(t => t.AcademicYearId).ThenBy(t => t.OrderIndex);
        }

        var totalCount = await query.CountAsync();

        // Apply pagination
        var terms = await query
            .Skip((filter.Page - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .Select(t => new TermDto
            {
                Id = t.Id,
                AcademicYearId = t.AcademicYearId,
                Name = t.Name,
                Code = t.Code,
                Type = t.Type,
                Status = t.Status,
                StartDate = t.StartDate,
                EndDate = t.EndDate,
                OrderIndex = t.OrderIndex,
                Description = t.Description,
                Remarks = t.Remarks,
                TotalWorkingDays = t.TotalWorkingDays,
                TotalHolidays = t.TotalHolidays,
                ExamStartDate = t.ExamStartDate,
                ExamEndDate = t.ExamEndDate,
                ResultPublishDate = t.ResultPublishDate,
                RegistrationDeadline = t.RegistrationDeadline,
                FeePaymentDeadline = t.FeePaymentDeadline,
                PassingGrade = t.PassingGrade,
                MaximumGrade = t.MaximumGrade,
                GradingScale = t.GradingScale,
                CreatedAt = t.CreatedAt,
                LastModifiedAt = t.LastModifiedAt,
                AcademicYearName = t.AcademicYear != null ? t.AcademicYear.Name : "",
                Translations = t.Translations.Select(tr => new TermTranslationDto
                {
                    Id = tr.Id,
                    TermId = tr.TermId,
                    LanguageCode = tr.LanguageCode,
                    Name = tr.Name,
                    Description = tr.Description,
                    Remarks = tr.Remarks
                }).ToList()
            })
            .ToListAsync();

        return (terms, totalCount);
    }

    public async Task<TermDto?> GetTermByIdAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var term = await repository.AsQueryable("AcademicYear,Translations")
            .FirstOrDefaultAsync(t => t.Id == id);

        if (term == null)
            return null;

        return new TermDto
        {
            Id = term.Id,
            AcademicYearId = term.AcademicYearId,
            Name = term.Name,
            Code = term.Code,
            Type = term.Type,
            Status = term.Status,
            StartDate = term.StartDate,
            EndDate = term.EndDate,
            OrderIndex = term.OrderIndex,
            Description = term.Description,
            Remarks = term.Remarks,
            TotalWorkingDays = term.TotalWorkingDays,
            TotalHolidays = term.TotalHolidays,
            ExamStartDate = term.ExamStartDate,
            ExamEndDate = term.ExamEndDate,
            ResultPublishDate = term.ResultPublishDate,
            RegistrationDeadline = term.RegistrationDeadline,
            FeePaymentDeadline = term.FeePaymentDeadline,
            PassingGrade = term.PassingGrade,
            MaximumGrade = term.MaximumGrade,
            GradingScale = term.GradingScale,
            CreatedAt = term.CreatedAt,
            LastModifiedAt = term.LastModifiedAt,
            AcademicYearName = term.AcademicYear?.Name ?? "",
            Translations = term.Translations.Select(tr => new TermTranslationDto
            {
                Id = tr.Id,
                TermId = tr.TermId,
                LanguageCode = tr.LanguageCode,
                Name = tr.Name,
                Description = tr.Description,
                Remarks = tr.Remarks
            }).ToList()
        };
    }

    public async Task<IEnumerable<TermDto>> GetTermsByAcademicYearAsync(Guid academicYearId)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var terms = await repository.AsQueryable("Translations")
            .Where(t => t.AcademicYearId == academicYearId)
            .OrderBy(t => t.OrderIndex)
            .Select(t => new TermDto
            {
                Id = t.Id,
                AcademicYearId = t.AcademicYearId,
                Name = t.Name,
                Code = t.Code,
                Type = t.Type,
                Status = t.Status,
                StartDate = t.StartDate,
                EndDate = t.EndDate,
                OrderIndex = t.OrderIndex,
                Description = t.Description,
                Remarks = t.Remarks,
                TotalWorkingDays = t.TotalWorkingDays,
                TotalHolidays = t.TotalHolidays,
                ExamStartDate = t.ExamStartDate,
                ExamEndDate = t.ExamEndDate,
                ResultPublishDate = t.ResultPublishDate,
                RegistrationDeadline = t.RegistrationDeadline,
                FeePaymentDeadline = t.FeePaymentDeadline,
                PassingGrade = t.PassingGrade,
                MaximumGrade = t.MaximumGrade,
                GradingScale = t.GradingScale,
                CreatedAt = t.CreatedAt,
                LastModifiedAt = t.LastModifiedAt,
                Translations = t.Translations.Select(tr => new TermTranslationDto
                {
                    Id = tr.Id,
                    TermId = tr.TermId,
                    LanguageCode = tr.LanguageCode,
                    Name = tr.Name,
                    Description = tr.Description,
                    Remarks = tr.Remarks
                }).ToList()
            })
            .ToListAsync();

        return terms;
    }

    public async Task<TermDto?> GetCurrentTermAsync()
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var currentTerm = await repository.AsQueryable("AcademicYear,Translations")
            .Where(t => t.Status == TermStatus.Active && t.AcademicYear.IsCurrentYear)
            .FirstOrDefaultAsync();

        if (currentTerm == null)
            return null;

        return await GetTermByIdAsync(currentTerm.Id);
    }

    public async Task<TermDto?> GetActiveTermByAcademicYearAsync(Guid academicYearId)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var activeTerm = await repository.AsQueryable("Translations")
            .Where(t => t.AcademicYearId == academicYearId && t.Status == TermStatus.Active)
            .FirstOrDefaultAsync();

        if (activeTerm == null)
            return null;

        return await GetTermByIdAsync(activeTerm.Id);
    }

    public async Task<Guid> CreateTermAsync(CreateTermDto termDto)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();

        // Validate dates
        if (!await ValidateTermDatesAsync(termDto.AcademicYearId, termDto.StartDate, termDto.EndDate))
        {
            throw new InvalidOperationException("Term dates overlap with existing term in the same academic year.");
        }

        // Validate order index
        if (!await ValidateTermOrderAsync(termDto.AcademicYearId, termDto.OrderIndex))
        {
            throw new InvalidOperationException("Term order index already exists in the academic year.");
        }

        var term = new Domain.Entities.Term
        {
            AcademicYearId = termDto.AcademicYearId,
            Name = termDto.Name,
            Code = termDto.Code,
            Type = termDto.Type,
            Status = termDto.Status,
            StartDate = termDto.StartDate,
            EndDate = termDto.EndDate,
            OrderIndex = termDto.OrderIndex,
            Description = termDto.Description,
            Remarks = termDto.Remarks,
            TotalWorkingDays = termDto.TotalWorkingDays,
            TotalHolidays = termDto.TotalHolidays,
            ExamStartDate = termDto.ExamStartDate,
            ExamEndDate = termDto.ExamEndDate,
            ResultPublishDate = termDto.ResultPublishDate,
            RegistrationDeadline = termDto.RegistrationDeadline,
            FeePaymentDeadline = termDto.FeePaymentDeadline,
            PassingGrade = termDto.PassingGrade,
            MaximumGrade = termDto.MaximumGrade,
            GradingScale = termDto.GradingScale,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _currentUserService.UserId.ToString()
        };

        await repository.AddAsync(term);
        await _unitOfWork.SaveChangesAsync();

        // Add translations if provided
        if (termDto.Translations?.Any() == true)
        {
            foreach (var translation in termDto.Translations)
            {
                await AddTranslationAsync(term.Id, translation);
            }
        }

        _logger.LogInformation("Term created with ID {TermId}", term.Id);
        return term.Id;
    }

    public async Task<bool> UpdateTermAsync(Guid id, UpdateTermDto termDto)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var term = await repository.AsQueryable().FirstOrDefaultAsync(t => t.Id == id);

        if (term == null)
            return false;

        // Validate dates if changed
        if (term.StartDate != termDto.StartDate || term.EndDate != termDto.EndDate)
        {
            if (!await ValidateTermDatesAsync(term.AcademicYearId, termDto.StartDate, termDto.EndDate, id))
            {
                throw new InvalidOperationException("Term dates overlap with existing term in the same academic year.");
            }
        }

        // Validate order index if changed
        if (term.OrderIndex != termDto.OrderIndex)
        {
            if (!await ValidateTermOrderAsync(term.AcademicYearId, termDto.OrderIndex, id))
            {
                throw new InvalidOperationException("Term order index already exists in the academic year.");
            }
        }

        term.Name = termDto.Name;
        term.Code = termDto.Code;
        term.Type = termDto.Type;
        term.Status = termDto.Status;
        term.StartDate = termDto.StartDate;
        term.EndDate = termDto.EndDate;
        term.OrderIndex = termDto.OrderIndex;
        term.Description = termDto.Description;
        term.Remarks = termDto.Remarks;
        term.TotalWorkingDays = termDto.TotalWorkingDays;
        term.TotalHolidays = termDto.TotalHolidays;
        term.ExamStartDate = termDto.ExamStartDate;
        term.ExamEndDate = termDto.ExamEndDate;
        term.ResultPublishDate = termDto.ResultPublishDate;
        term.RegistrationDeadline = termDto.RegistrationDeadline;
        term.FeePaymentDeadline = termDto.FeePaymentDeadline;
        term.PassingGrade = termDto.PassingGrade;
        term.MaximumGrade = termDto.MaximumGrade;
        term.GradingScale = termDto.GradingScale;
        term.LastModifiedAt = DateTime.UtcNow;
        term.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(term);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Term updated with ID {TermId}", term.Id);
        return true;
    }

    public async Task<bool> DeleteTermAsync(Guid id)
    {
        if (!await CanDeleteTermAsync(id))
        {
            return false;
        }

        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        await repository.DeleteByIdAsync(id);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Term deleted with ID {TermId}", id);
        return true;
    }

    public async Task<bool> ActivateTermAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var term = await repository.AsQueryable().FirstOrDefaultAsync(t => t.Id == id);

        if (term == null || term.Status != TermStatus.Planned)
            return false;

        term.Status = TermStatus.Active;
        term.LastModifiedAt = DateTime.UtcNow;
        term.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(term);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Term activated with ID {TermId}", id);
        return true;
    }

    public async Task<bool> CompleteTermAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var term = await repository.AsQueryable().FirstOrDefaultAsync(t => t.Id == id);

        if (term == null || term.Status != TermStatus.Active)
            return false;

        term.Status = TermStatus.Completed;
        term.LastModifiedAt = DateTime.UtcNow;
        term.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(term);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Term completed with ID {TermId}", id);
        return true;
    }

    public async Task<bool> CancelTermAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var term = await repository.AsQueryable().FirstOrDefaultAsync(t => t.Id == id);

        if (term == null)
            return false;

        term.Status = TermStatus.Cancelled;
        term.LastModifiedAt = DateTime.UtcNow;
        term.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(term);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Term cancelled with ID {TermId}", id);
        return true;
    }

    public async Task<bool> ValidateTermDatesAsync(Guid academicYearId, DateTime startDate, DateTime endDate, Guid? excludeId = null)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var query = repository.AsQueryable().Where(t => t.AcademicYearId == academicYearId);

        if (excludeId.HasValue)
        {
            query = query.Where(t => t.Id != excludeId.Value);
        }

        var overlapping = await query
            .Where(t => (startDate >= t.StartDate && startDate <= t.EndDate) ||
                       (endDate >= t.StartDate && endDate <= t.EndDate) ||
                       (startDate <= t.StartDate && endDate >= t.EndDate))
            .AnyAsync();

        return !overlapping;
    }

    public async Task<bool> ValidateTermOrderAsync(Guid academicYearId, int orderIndex, Guid? excludeId = null)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var query = repository.AsQueryable().Where(t => t.AcademicYearId == academicYearId && t.OrderIndex == orderIndex);

        if (excludeId.HasValue)
        {
            query = query.Where(t => t.Id != excludeId.Value);
        }

        var exists = await query.AnyAsync();
        return !exists;
    }

    public async Task<bool> CanDeleteTermAsync(Guid id)
    {
        // Check if there are any students enrolled in this term
        var studentHistoryRepository = _unitOfWork.Repository<StudentAcademicHistory>();
        var hasStudents = await studentHistoryRepository.AsQueryable()
            .AnyAsync(sh => sh.TermId == id);

        return !hasStudents;
    }

    public async Task<bool> ReorderTermsAsync(Guid academicYearId, List<Guid> termIds)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var terms = await repository.AsQueryable()
            .Where(t => t.AcademicYearId == academicYearId && termIds.Contains(t.Id))
            .ToListAsync();

        if (terms.Count != termIds.Count)
            return false;

        for (int i = 0; i < termIds.Count; i++)
        {
            var term = terms.FirstOrDefault(t => t.Id == termIds[i]);
            if (term != null)
            {
                term.OrderIndex = i + 1;
                term.LastModifiedAt = DateTime.UtcNow;
                term.LastModifiedBy = _currentUserService.UserId.ToString();
                await repository.UpdateAsync(term);
            }
        }

        await _unitOfWork.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<TermDto>> GetOverlappingTermsAsync(Guid academicYearId, DateTime startDate, DateTime endDate, Guid? excludeId = null)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        var query = repository.AsQueryable().Where(t => t.AcademicYearId == academicYearId);

        if (excludeId.HasValue)
        {
            query = query.Where(t => t.Id != excludeId.Value);
        }

        var overlappingTerms = await query
            .Where(t => (startDate >= t.StartDate && startDate <= t.EndDate) ||
                       (endDate >= t.StartDate && endDate <= t.EndDate) ||
                       (startDate <= t.StartDate && endDate >= t.EndDate))
            .Select(t => new TermDto
            {
                Id = t.Id,
                AcademicYearId = t.AcademicYearId,
                Name = t.Name,
                Code = t.Code,
                Type = t.Type,
                Status = t.Status,
                StartDate = t.StartDate,
                EndDate = t.EndDate,
                OrderIndex = t.OrderIndex,
                Description = t.Description,
                Remarks = t.Remarks,
                CreatedAt = t.CreatedAt,
                LastModifiedAt = t.LastModifiedAt
            })
            .ToListAsync();

        return overlappingTerms;
    }

    public async Task<bool> AddTranslationAsync(Guid termId, CreateTermTranslationDto translationDto)
    {
        var repository = _unitOfWork.Repository<TermTranslation>();

        // Check if translation already exists
        var existingTranslation = await repository.AsQueryable()
            .FirstOrDefaultAsync(t => t.TermId == termId && t.LanguageCode == translationDto.LanguageCode);

        if (existingTranslation != null)
            return false;

        var translation = new TermTranslation
        {
            TermId = termId,
            LanguageCode = translationDto.LanguageCode,
            Name = translationDto.Name,
            Description = translationDto.Description,
            Remarks = translationDto.Remarks,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _currentUserService.UserId.ToString()
        };

        await repository.AddAsync(translation);
        await _unitOfWork.SaveChangesAsync();

        return true;
    }

    public async Task<bool> UpdateTranslationAsync(Guid termId, string languageCode, UpdateTermTranslationDto translationDto)
    {
        var repository = _unitOfWork.Repository<TermTranslation>();
        var translation = await repository.AsQueryable()
            .FirstOrDefaultAsync(t => t.TermId == termId && t.LanguageCode == languageCode);

        if (translation == null)
            return false;

        translation.Name = translationDto.Name;
        translation.Description = translationDto.Description;
        translation.Remarks = translationDto.Remarks;
        translation.LastModifiedAt = DateTime.UtcNow;
        translation.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(translation);
        await _unitOfWork.SaveChangesAsync();

        return true;
    }

    public async Task<bool> DeleteTranslationAsync(Guid termId, string languageCode)
    {
        var repository = _unitOfWork.Repository<TermTranslation>();
        var translation = await repository.AsQueryable()
            .FirstOrDefaultAsync(t => t.TermId == termId && t.LanguageCode == languageCode);

        if (translation == null)
            return false;

        await repository.DeleteAsync(translation);
        await _unitOfWork.SaveChangesAsync();

        return true;
    }

    public async Task<IEnumerable<TermTranslationDto>> GetTranslationsAsync(Guid termId)
    {
        var repository = _unitOfWork.Repository<TermTranslation>();
        var translations = await repository.AsQueryable()
            .Where(t => t.TermId == termId)
            .Select(t => new TermTranslationDto
            {
                Id = t.Id,
                TermId = t.TermId,
                LanguageCode = t.LanguageCode,
                Name = t.Name,
                Description = t.Description,
                Remarks = t.Remarks
            })
            .ToListAsync();

        return translations;
    }

    public async Task<int> GetTotalStudentsInTermAsync(Guid termId)
    {
        var repository = _unitOfWork.Repository<StudentAcademicHistory>();
        return await repository.AsQueryable()
            .Where(sh => sh.TermId == termId)
            .Select(sh => sh.StudentId)
            .Distinct()
            .CountAsync();
    }

    public async Task<Dictionary<string, int>> GetTermStatisticsAsync(Guid termId)
    {
        var stats = new Dictionary<string, int>();

        stats["TotalStudents"] = await GetTotalStudentsInTermAsync(termId);

        // Add more statistics as needed
        var calendarRepository = _unitOfWork.Repository<AcademicCalendar>();
        stats["TotalEvents"] = await calendarRepository.AsQueryable()
            .Where(ac => ac.TermId == termId)
            .CountAsync();

        return stats;
    }
}
