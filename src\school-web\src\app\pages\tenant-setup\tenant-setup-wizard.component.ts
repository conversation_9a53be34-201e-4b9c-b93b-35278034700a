import { Compo<PERSON>, OnIni<PERSON>, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatStepperModule } from '@angular/material/stepper';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { TranslateModule } from '@ngx-translate/core';
import { TenantSetupService, SchoolSetupDto, TenantSetupStep, AcademicStructureSetupDto, UserRolesSetupDto, InitialUsersSetupDto, SystemSettingsSetupDto } from '../../core/services/tenant-setup.service';
import { TenantService } from '../../core/services/tenant.service';
import { AuthService } from '../../core/services/auth.service';

@Component({
  selector: 'app-tenant-setup-wizard',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatStepperModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressBarModule,
    MatSnackBarModule,
    MatCheckboxModule,
    TranslateModule
  ],
  templateUrl: './tenant-setup-wizard.component.html',
  styleUrls: ['./tenant-setup-wizard.component.scss']
})
export class TenantSetupWizardComponent implements OnInit, OnDestroy {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private snackBar = inject(MatSnackBar);
  private tenantSetupService = inject(TenantSetupService);
  private tenantService = inject(TenantService);
  private authService = inject(AuthService);

  currentTenant: any = null;
  setupSteps: TenantSetupStep[] = [];
  currentStepIndex = 0;
  isLoading = false;
  returnUrl = '/admin';

  // Form Groups
  schoolProfileForm!: FormGroup;
  academicStructureForm!: FormGroup;
  userRolesForm!: FormGroup;
  initialUsersForm!: FormGroup;
  systemSettingsForm!: FormGroup;

  schoolTypes: string[] = [];
  affiliationBoards: string[] = [];
  timeZones: { value: string; label: string }[] = [];
  currencies: { value: string; label: string; symbol: string }[] = [];
  languages: { value: string; label: string }[] = [];

  constructor() {
    this.initializeForms();
  }

  ngOnInit() {
    console.log('🚀 Tenant Setup Wizard initialized');
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/admin';

    // Initialize currentTenant as null to ensure proper conditional rendering
    this.currentTenant = null;
    console.log('🔄 Initial currentTenant state:', this.currentTenant);

    this.initializeForms();
    this.loadTenantInfo();
    this.loadSetupData();
    this.setupSteps = this.tenantSetupService.getSetupSteps();
  }

  ngOnDestroy() {
    // Cleanup if needed
  }

  private initializeForms() {
    this.schoolProfileForm = this.fb.group({
      schoolName: ['', Validators.required],
      schoolType: ['', Validators.required],
      street: ['', Validators.required],
      city: ['', Validators.required],
      state: ['', Validators.required],
      postalCode: ['', Validators.required],
      country: ['India', Validators.required],
      phone: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      website: [''],
      establishedYear: [new Date().getFullYear(), [Validators.required, Validators.min(1800)]],
      affiliationBoard: ['', Validators.required],
      schoolCode: ['']
    });

    this.academicStructureForm = this.fb.group({
      academicYearStart: ['04-01', Validators.required], // April 1st default
      hasTerms: [true],
      termCount: [3, [Validators.min(1), Validators.max(4)]],
      gradeStructure: ['K12', Validators.required]
    });

    this.userRolesForm = this.fb.group({
      enableCustomRoles: [false],
      defaultRoles: [true]
    });

    this.initialUsersForm = this.fb.group({
      createInitialUsers: [false],
      principalEmail: [''],
      vicePrincipalEmail: [''],
      adminEmail: ['']
    });

    this.systemSettingsForm = this.fb.group({
      timeZone: ['Asia/Kolkata', Validators.required],
      currency: ['INR', Validators.required],
      language: ['en', Validators.required],
      enableNotifications: [true],
      enableSMS: [false],
      enableEmail: [true]
    });
  }

  private loadTenantInfo() {
    console.log('🔍 Loading tenant info for setup wizard...');

    // First try to get tenant from route params
    const tenantIdFromRoute = this.route.snapshot.params['tenantId'];

    if (tenantIdFromRoute) {
      console.log('📍 Using tenant ID from route:', tenantIdFromRoute);
      // Use tenant ID from route for setup validation scenarios
      this.currentTenant = { id: tenantIdFromRoute };
      this.tenantSetupService.updateSetupStatus(tenantIdFromRoute);
      return;
    }

    // Clear any stale tenant data first to ensure fresh check
    console.log('🧹 Clearing any stale tenant data...');
    this.tenantService.clearCurrentTenant();

    // Also clear localStorage to ensure no stale data
    localStorage.removeItem('currentTenant');

    console.log('🌐 Making API call to get current tenant...');
    // Fallback to current tenant service
    this.tenantService.getCurrentTenant().subscribe({
      next: (tenant) => {
        console.log('✅ Tenant API response:', tenant);
        console.log('✅ Tenant type:', typeof tenant);
        console.log('✅ Tenant === null:', tenant === null);
        console.log('✅ Tenant == null:', tenant == null);
        console.log('✅ !tenant:', !tenant);

        this.currentTenant = tenant;
        console.log('🔄 currentTenant set to:', this.currentTenant);
        console.log('🔄 currentTenant type:', typeof this.currentTenant);
        console.log('🔄 !currentTenant:', !this.currentTenant);

        if (tenant) {
          console.log('🏫 Tenant found, updating setup status for:', tenant.id);
          this.tenantSetupService.updateSetupStatus(tenant.id);
        } else {
          console.log('❌ No tenant found, showing access message');
          // No tenant context - user needs system admin to create tenant
          this.showTenantAccessMessage();
        }
      },
      error: (error) => {
        console.error('❌ Error loading tenant info:', error);
        console.log('🔄 Setting currentTenant to null due to error');
        this.currentTenant = null;
        console.log('🔄 currentTenant after error:', this.currentTenant);
        this.showTenantAccessMessage();
      }
    });
  }

  private showTenantAccessMessage() {
    console.log('🚨 Showing tenant access message - no tenant found');
    this.snackBar.open(
      'No school access found. Please contact your System Administrator to create a school and grant you access.',
      'Close',
      {
        duration: 10000,
        panelClass: ['warning-snackbar']
      }
    );

    // Redirect to a proper access denied page after a delay
    setTimeout(() => {
      console.log('🔄 Redirecting to access denied page');
      this.router.navigate(['/tenant-setup/access-denied'], {
        queryParams: { returnUrl: this.returnUrl }
      });
    }, 3000);
  }

  private loadSetupData() {
    this.schoolTypes = this.tenantSetupService.getSchoolTypes();
    this.affiliationBoards = this.tenantSetupService.getAffiliationBoards();
    this.timeZones = this.tenantSetupService.getTimeZones();
    this.currencies = this.tenantSetupService.getCurrencies();
    this.languages = this.tenantSetupService.getLanguages();
  }

  onStepChange(event: any) {
    this.currentStepIndex = event.selectedIndex;
    console.log('Step changed to:', this.currentStepIndex);
  }

  goToStep(stepIndex: number) {
    if (stepIndex >= 0 && stepIndex < this.setupSteps.length) {
      this.currentStepIndex = stepIndex;
    }
  }

  canNavigateToStep(stepIndex: number): boolean {
    // Allow navigation to current step or any completed step
    if (stepIndex <= this.currentStepIndex) {
      return true;
    }

    // Allow navigation to next step if current step is completed
    if (stepIndex === this.currentStepIndex + 1 && this.setupSteps[this.currentStepIndex]?.isCompleted) {
      return true;
    }

    return false;
  }

  async completeStep(stepIndex: number) {
    if (!this.currentTenant) {
      this.showTenantAccessMessage();
      return;
    }

    this.isLoading = true;
    const stepId = this.setupSteps[stepIndex].id;

    try {
      // Validate form before proceeding
      const isValid = this.validateStepForm(stepIndex);
      if (!isValid) {
        this.snackBar.open('Please fill in all required fields correctly', 'Close', { duration: 3000 });
        this.isLoading = false;
        return;
      }

      switch (stepId) {
        case 'school-profile':
          await this.completeSchoolProfile();
          break;
        case 'academic-structure':
          await this.completeAcademicStructure();
          break;
        case 'user-roles':
          await this.completeUserRoles();
          break;
        case 'initial-users':
          await this.completeInitialUsers();
          break;
        case 'system-settings':
          await this.completeSystemSettings();
          break;
        default:
          throw new Error(`Unknown step: ${stepId}`);
      }

      this.setupSteps[stepIndex].isCompleted = true;
      this.snackBar.open(`${this.setupSteps[stepIndex].title} completed successfully`, 'Close', { duration: 3000 });

      // Move to next step if not the last one
      if (stepIndex < this.setupSteps.length - 1) {
        this.currentStepIndex = stepIndex + 1;
      }

    } catch (error: any) {
      console.error(`Error completing ${stepId}:`, error);
      const errorMessage = error?.message || `Error completing ${this.setupSteps[stepIndex].title}`;
      this.snackBar.open(errorMessage, 'Close', { duration: 5000 });
    } finally {
      this.isLoading = false;
    }
  }

  private validateStepForm(stepIndex: number): boolean {
    switch (stepIndex) {
      case 0: // School Profile
        return this.schoolProfileForm.valid;
      case 1: // Academic Structure
        return this.academicStructureForm.valid;
      case 2: // User Roles
        return this.userRolesForm.valid;
      case 3: // Initial Users (optional)
        return true; // Always valid since it's optional
      case 4: // System Settings
        return this.systemSettingsForm.valid;
      default:
        return false;
    }
  }

  private async completeSchoolProfile() {
    if (!this.schoolProfileForm.valid) {
      throw new Error('School profile form is invalid');
    }

    const formValue = this.schoolProfileForm.value;
    const schoolData: SchoolSetupDto = {
      schoolName: formValue.schoolName,
      schoolType: formValue.schoolType,
      address: {
        street: formValue.street,
        city: formValue.city,
        state: formValue.state,
        postalCode: formValue.postalCode,
        country: formValue.country
      },
      contactInfo: {
        phone: formValue.phone,
        email: formValue.email,
        website: formValue.website
      },
      academicInfo: {
        establishedYear: formValue.establishedYear,
        affiliationBoard: formValue.affiliationBoard,
        schoolCode: formValue.schoolCode
      },
      settings: {
        timeZone: 'Asia/Kolkata',
        currency: 'INR',
        language: 'en',
        academicYearStart: '04-01'
      }
    };

    return firstValueFrom(this.tenantSetupService.completeSchoolProfile(this.currentTenant.id, schoolData));
  }

  private async completeAcademicStructure(): Promise<void> {
    if (!this.academicStructureForm.valid) {
      throw new Error('Academic structure form is invalid');
    }

    const formValue = this.academicStructureForm.value;
    const academicData: AcademicStructureSetupDto = {
      academicYearStart: formValue.academicYearStart,
      hasTerms: formValue.hasTerms,
      termCount: formValue.termCount,
      gradeStructure: formValue.gradeStructure
    };

    return firstValueFrom(this.tenantSetupService.completeAcademicStructure(this.currentTenant.id, academicData));
  }

  private async completeUserRoles(): Promise<void> {
    const formValue = this.userRolesForm.value;
    const rolesData: UserRolesSetupDto = {
      enableCustomRoles: formValue.enableCustomRoles,
      defaultRoles: formValue.defaultRoles
    };

    return firstValueFrom(this.tenantSetupService.completeUserRoles(this.currentTenant.id, rolesData));
  }

  private async completeInitialUsers(): Promise<void> {
    const formValue = this.initialUsersForm.value;
    const usersData: InitialUsersSetupDto = {
      createInitialUsers: formValue.createInitialUsers,
      principalEmail: formValue.principalEmail,
      vicePrincipalEmail: formValue.vicePrincipalEmail,
      adminEmail: formValue.adminEmail
    };

    return firstValueFrom(this.tenantSetupService.completeInitialUsers(this.currentTenant.id, usersData));
  }

  private async completeSystemSettings(): Promise<void> {
    if (!this.systemSettingsForm.valid) {
      throw new Error('System settings form is invalid');
    }

    const formValue = this.systemSettingsForm.value;
    const settingsData: SystemSettingsSetupDto = {
      timeZone: formValue.timeZone,
      currency: formValue.currency,
      language: formValue.language,
      enableNotifications: formValue.enableNotifications,
      enableSMS: formValue.enableSMS,
      enableEmail: formValue.enableEmail
    };

    return firstValueFrom(this.tenantSetupService.completeSystemSettings(this.currentTenant.id, settingsData));
  }

  async finalizeSetup() {
    if (!this.currentTenant) {
      this.showTenantAccessMessage();
      return;
    }

    this.isLoading = true;
    try {
      await firstValueFrom(this.tenantSetupService.finalizeSetup(this.currentTenant.id));
      this.snackBar.open('School setup completed successfully!', 'Close', { duration: 5000 });
      
      // Redirect to the return URL or admin dashboard
      this.router.navigate([this.returnUrl]);
    } catch (error) {
      console.error('Error finalizing setup:', error);
      this.snackBar.open('Error finalizing setup', 'Close', { duration: 3000 });
    } finally {
      this.isLoading = false;
    }
  }

  skipStep(stepIndex: number) {
    if (!this.currentTenant) {
      this.showTenantAccessMessage();
      return;
    }

    if (this.setupSteps[stepIndex].isRequired) {
      this.snackBar.open('This step is required and cannot be skipped', 'Close', { duration: 3000 });
      return;
    }

    this.tenantSetupService.skipOptionalStep(this.currentTenant.id, this.setupSteps[stepIndex].id).subscribe({
      next: () => {
        this.setupSteps[stepIndex].isCompleted = true;
        this.snackBar.open('Step skipped', 'Close', { duration: 2000 });
        
        // Move to next step
        if (stepIndex < this.setupSteps.length - 1) {
          this.currentStepIndex = stepIndex + 1;
        }
      },
      error: (error) => {
        console.error('Error skipping step:', error);
        this.snackBar.open('Error skipping step', 'Close', { duration: 3000 });
      }
    });
  }

  getProgressPercentage(): number {
    const completedSteps = this.setupSteps.filter(step => step.isCompleted).length;
    return (completedSteps / this.setupSteps.length) * 100;
  }

  canFinalize(): boolean {
    if (!this.currentTenant) return false;
    const requiredSteps = this.setupSteps.filter(step => step.isRequired);
    return requiredSteps.every(step => step.isCompleted);
  }

  isWizardDisabled(): boolean {
    return !this.currentTenant || this.currentTenant.isValidationMode;
  }
}
