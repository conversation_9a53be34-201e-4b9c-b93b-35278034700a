using School.Domain.Common;

namespace School.Domain.Entities;

public class CareerTranslation : BaseEntity
{
    public Guid CareerId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Responsibilities { get; set; } = string.Empty;
    public string Qualifications { get; set; } = string.Empty;
    public string Experience { get; set; } = string.Empty;
    public string Salary { get; set; } = string.Empty;
    public string EmploymentType { get; set; } = string.Empty;
    
    // Navigation properties
    public Career Career { get; set; } = null!;
    public DateTime UpdatedAt { get; set; }
}
