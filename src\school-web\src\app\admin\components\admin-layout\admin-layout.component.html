<div class="admin-container" [class.sidebar-collapsed]="sidebarCollapsed">
  <!-- Sidebar -->
  <aside class="sidebar" [class.collapsed]="sidebarCollapsed">
    <div class="sidebar-header">
      <div class="logo-container">
        <img src="assets/images/school-logo.png" alt="School Logo" class="logo">
        <span class="logo-text" *ngIf="!sidebarCollapsed">Admin Panel</span>
      </div>
      <button mat-icon-button class="toggle-btn" (click)="toggleSidebar()">
        <mat-icon>{{ sidebarCollapsed ? 'chevron_right' : 'chevron_left' }}</mat-icon>
      </button>
    </div>

    <div class="user-info" *ngIf="!sidebarCollapsed">
      <div class="avatar">
        <mat-icon>account_circle</mat-icon>
      </div>
      <div class="user-details">
        <h3 class="user-name">{{ currentUser?.firstName }} {{ currentUser?.lastName }}</h3>
        <span class="user-role">{{ getAdminTypeDisplayName() }}</span>
      </div>
    </div>

    <mat-divider></mat-divider>

    <nav class="sidebar-nav">
      <!-- Collapsed Mode: Show only icons with tooltips -->
      <div class="collapsed-nav" *ngIf="sidebarCollapsed">
        <ul class="nav-list">
          <!-- Dashboard (always visible) -->
          <li class="nav-item">
            <a [routerLink]="'/admin'"
               class="nav-link"
               [class.active]="isMenuItemActive('/admin')"
               matTooltip="Dashboard"
               matTooltipPosition="right"
               (click)="setActiveMenuItem('/admin')">
              <mat-icon class="nav-icon">dashboard</mat-icon>
            </a>
          </li>

          <!-- Category Icons with Hover Submenu -->
          <li class="nav-item hover-menu-parent" *ngFor="let category of menuCategories">
            <!-- Skip the main category as it's just dashboard -->
            <ng-container *ngIf="category.name !== 'admin.categories.main'">
              <a class="nav-link category-link"
                 [class.active]="isSubmenuActive(category.name)"
                 [matTooltip]="category.name | translate"
                 [matTooltipDisabled]="true"
                 matTooltipPosition="right"
                 (mouseenter)="showSubmenu($event, category.name)"
                 (click)="showSubmenu($event, category.name)">
                <mat-icon class="nav-icon">{{ category.icon }}</mat-icon>
              </a>

              <!-- Hover Submenu -->
              <div class="hover-submenu"
                   [class.active]="isSubmenuActive(category.name)"
                   (mouseleave)="hideSubmenu($event)"
                   (mouseenter)="$event.stopPropagation()">
                <div class="hover-submenu-header">
                  <span>{{ category.name | translate }}</span>
                </div>
                <ul class="hover-submenu-items">
                  <li class="hover-submenu-item" *ngFor="let item of category.items">
                    <a [routerLink]="item.route"
                       [class.active]="isMenuItemActive(item.route)"
                       class="hover-submenu-link"
                       (click)="setActiveMenuItem(item.route)">
                      <mat-icon class="hover-submenu-icon">{{ item.icon }}</mat-icon>
                      <span class="hover-submenu-text">{{ item.label | translate }}</span>
                    </a>
                  </li>
                </ul>
              </div>
            </ng-container>
          </li>
        </ul>
      </div>

      <!-- Expanded Mode: Show categories with expansion panels -->
      <div class="expanded-nav" *ngIf="!sidebarCollapsed">
        <!-- Loop through categories -->
        <div class="nav-category" *ngFor="let category of menuCategories">
          <!-- Main category (Dashboard) is special - no expansion panel -->
          <ng-container *ngIf="category.name === 'admin.categories.main'; else normalCategory">
            <ul class="nav-list">
              <li class="nav-item" *ngFor="let item of category.items">
                <a [routerLink]="item.route"
                   class="nav-link"
                   [class.active]="isMenuItemActive(item.route)"
                   (click)="setActiveMenuItem(item.route)">
                  <mat-icon class="nav-icon">{{ item.icon }}</mat-icon>
                  <span class="nav-text">{{ item.label | translate }}</span>
                </a>
              </li>
            </ul>
          </ng-container>

          <!-- Normal categories with expansion panels -->
          <ng-template #normalCategory>
            <div class="category-panel">
              <!-- Category Header (clickable to expand) -->
              <div class="category-header" (click)="toggleCategory(category.name)">
                <div class="category-header-content">
                  <mat-icon class="category-icon">{{ category.icon }}</mat-icon>
                  <span class="category-name">{{ category.name | translate }}</span>
                </div>
                <mat-icon class="expand-icon">
                  {{ isCategoryExpanded(category.name) ? 'expand_less' : 'expand_more' }}
                </mat-icon>
              </div>

              <!-- Category Items (expandable) -->
              <div class="category-items" [class.expanded]="isCategoryExpanded(category.name)">
                <ul class="nav-list">
                  <li class="nav-item" *ngFor="let item of category.items">
                    <a [routerLink]="item.route"
                       class="nav-link"
                       [class.active]="isMenuItemActive(item.route)"
                       (click)="setActiveMenuItem(item.route)">
                      <mat-icon class="nav-icon">{{ item.icon }}</mat-icon>
                      <span class="nav-text">{{ item.label | translate }}</span>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </ng-template>

          <!-- Category Divider -->
          <mat-divider></mat-divider>
        </div>
      </div>
    </nav>

    <div class="sidebar-footer">
      <button mat-button class="logout-btn" (click)="logout()">
        <mat-icon>exit_to_app</mat-icon>
        <span *ngIf="!sidebarCollapsed">{{ 'NAV.LOGOUT' | translate }}</span>
      </button>
    </div>
  </aside>

  <!-- Main Content -->
  <main class="main-content">
    <!-- Top Header -->
    <header class="header">
      <div class="header-left">
        <button mat-icon-button class="menu-toggle" (click)="toggleSidebar()">
          <mat-icon>menu</mat-icon>
        </button>
        <h1 class="page-title">{{ pageTitle }}</h1>
      </div>
      <div class="header-right">
        <!-- Language & Theme Switcher -->
        <app-language-theme-switcher
          style="compact"
          class="header-switcher">
        </app-language-theme-switcher>

        <button mat-icon-button class="action-btn" matTooltip="Notifications">
          <mat-icon [matBadge]="3" matBadgeColor="warn">notifications</mat-icon>
        </button>
        <button mat-icon-button class="action-btn" matTooltip="Settings">
          <mat-icon>settings</mat-icon>
        </button>
        <button mat-icon-button class="action-btn" matTooltip="Return to Website" routerLink="/">
          <mat-icon>home</mat-icon>
        </button>
      </div>
    </header>

    <!-- Content Area -->
    <div class="content-wrapper">
      <router-outlet></router-outlet>
    </div>
  </main>
</div>
