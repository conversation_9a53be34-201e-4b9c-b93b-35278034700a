using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class EventRegistration : BaseEntity
{
    public int EventId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string? StudentId { get; set; }
    public string? AdditionalInfo { get; set; }
    public RegistrationStatus Status { get; set; } = RegistrationStatus.Pending;

    // Navigation properties
    public Event Event { get; set; } = null!;
}
