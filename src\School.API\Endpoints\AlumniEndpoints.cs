using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.DTOs;
using School.Application.Features.Alumni;
using System;
using System.Threading.Tasks;

namespace School.API.Endpoints;

public class AlumniEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/alumni").WithTags("Alumni");

        // Get all alumni with filtering and pagination
        group.MapGet("/", async ([AsParameters] AlumniFilterDto filter, [FromServices] IAlumniService alumniService) =>
        {
            try
            {
                var (alumni, totalCount) = await alumniService.GetAllAlumniAsync(filter);
                var response = new { TotalCount = totalCount, Items = alumni };
                return ApiResults.ApiOk(response, "Alumni retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error retrieving alumni: {ex.Message}");
            }
        })
        .WithName("GetAllAlumni")
        .WithOpenApi();

        // Get alumni by ID
        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IAlumniService alumniService) =>
        {
            try
            {
                var alumni = await alumniService.GetAlumniByIdAsync(id);
                if (alumni == null)
                {
                    return ApiResults.ApiNotFound("Alumni not found");
                }
                return ApiResults.ApiOk(alumni, "Alumni retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error retrieving alumni: {ex.Message}");
            }
        })
        .WithName("GetAlumniById")
        .WithOpenApi();

        // Create new alumni
        group.MapPost("/", async ([FromBody] CreateAlumniDto alumniDto, [FromServices] IAlumniService alumniService) =>
        {
            try
            {
                var alumniId = await alumniService.CreateAlumniAsync(alumniDto);
                return ApiResults.ApiCreated($"/api/alumni/{alumniId}", alumniId.ToString(), "Alumni created successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error creating alumni: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("CreateAlumni")
        .WithOpenApi();

        // Update alumni
        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateAlumniDto alumniDto, [FromServices] IAlumniService alumniService) =>
        {
            try
            {
                var result = await alumniService.UpdateAlumniAsync(id, alumniDto);
                if (!result)
                {
                    return ApiResults.ApiNotFound("Alumni not found");
                }
                return ApiResults.ApiOk(id.ToString(), "Alumni updated successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error updating alumni: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateAlumni")
        .WithOpenApi();

        // Delete alumni
        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IAlumniService alumniService) =>
        {
            try
            {
                var result = await alumniService.DeleteAlumniAsync(id);
                if (!result)
                {
                    return ApiResults.ApiNotFound("Alumni not found");
                }
                return ApiResults.ApiOk(id.ToString(), "Alumni deleted successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error deleting alumni: {ex.Message}");
            }
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteAlumni")
        .WithOpenApi();

        // Testimonial endpoints
        var testimonialGroup = group.MapGroup("/testimonials");

        // Get testimonial by ID
        testimonialGroup.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IAlumniService alumniService) =>
        {
            try
            {
                var testimonial = await alumniService.GetTestimonialByIdAsync(id);
                if (testimonial == null)
                {
                    return ApiResults.ApiNotFound("Testimonial not found");
                }
                return ApiResults.ApiOk(testimonial, "Testimonial retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error retrieving testimonial: {ex.Message}");
            }
        })
        .WithName("GetTestimonialById")
        .WithOpenApi();

        // Create new testimonial
        testimonialGroup.MapPost("/", async ([FromBody] CreateAlumniTestimonialDto testimonialDto, [FromServices] IAlumniService alumniService) =>
        {
            try
            {
                var testimonialId = await alumniService.CreateTestimonialAsync(testimonialDto);
                return ApiResults.ApiCreated($"/api/alumni/testimonials/{testimonialId}", testimonialId.ToString(), "Testimonial created successfully");
            }
            catch (InvalidOperationException ex)
            {
                return ApiResults.ApiBadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error creating testimonial: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("CreateTestimonial")
        .WithOpenApi();

        // Update testimonial
        testimonialGroup.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateAlumniTestimonialDto testimonialDto, [FromServices] IAlumniService alumniService) =>
        {
            try
            {
                var result = await alumniService.UpdateTestimonialAsync(id, testimonialDto);
                if (!result)
                {
                    return ApiResults.ApiNotFound("Testimonial not found");
                }
                return ApiResults.ApiOk(id.ToString(), "Testimonial updated successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error updating testimonial: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateTestimonial")
        .WithOpenApi();

        // Delete testimonial
        testimonialGroup.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IAlumniService alumniService) =>
        {
            try
            {
                var result = await alumniService.DeleteTestimonialAsync(id);
                if (!result)
                {
                    return ApiResults.ApiNotFound("Testimonial not found");
                }
                return ApiResults.ApiOk(id.ToString(), "Testimonial deleted successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error deleting testimonial: {ex.Message}");
            }
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteTestimonial")
        .WithOpenApi();

        // Translation endpoints
        var translationGroup = group.MapGroup("/{alumniId}/translations");

        // Add translation
        translationGroup.MapPost("/", async ([FromRoute] Guid alumniId, [FromBody] CreateAlumniTranslationDto translationDto, [FromServices] IAlumniService alumniService) =>
        {
            try
            {
                var result = await alumniService.AddTranslationAsync(alumniId, translationDto);
                if (!result)
                {
                    return ApiResults.ApiBadRequest("Translation already exists or alumni not found");
                }
                return ApiResults.ApiOk($"{alumniId}:{translationDto.LanguageCode}", "Translation added successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error adding translation: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("AddAlumniTranslation")
        .WithOpenApi();

        // Update translation
        translationGroup.MapPut("/{languageCode}", async ([FromRoute] Guid alumniId, [FromRoute] string languageCode, [FromBody] UpdateAlumniTranslationDto translationDto, [FromServices] IAlumniService alumniService) =>
        {
            try
            {
                var result = await alumniService.UpdateTranslationAsync(alumniId, languageCode, translationDto);
                if (!result)
                {
                    return ApiResults.ApiNotFound("Translation not found");
                }
                return ApiResults.ApiOk($"{alumniId}:{languageCode}", "Translation updated successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error updating translation: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateAlumniTranslation")
        .WithOpenApi();

        // Delete translation
        translationGroup.MapDelete("/{languageCode}", async ([FromRoute] Guid alumniId, [FromRoute] string languageCode, [FromServices] IAlumniService alumniService) =>
        {
            try
            {
                var result = await alumniService.DeleteTranslationAsync(alumniId, languageCode);
                if (!result)
                {
                    return ApiResults.ApiNotFound("Translation not found");
                }
                return ApiResults.ApiOk($"{alumniId}:{languageCode}", "Translation deleted successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error deleting translation: {ex.Message}");
            }
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteAlumniTranslation")
        .WithOpenApi();
    }
}
