import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { tap, map } from 'rxjs/operators';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { UserRole } from '../models/user-role.enum';
import { TenantService } from './tenant.service';

// API response wrapper
interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  statusCode: number;
  errors?: string[];
}

// Enhanced user interface
interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  role: number;
  roleName: string;
  lastLogin?: Date;
  isActive: boolean;
  createdAt: Date;
  lastModifiedAt?: Date;

  // MFA Information
  isMfaEnabled: boolean;
  lastMfaSetup?: Date;

  // User Preferences
  preferredLanguage: string;
  preferredTheme: string;
  timeZone: string;

  // Profile Information
  profileImageUrl?: string;
  phoneNumber?: string;
  phoneNumber2?: string;
  dateOfBirth?: Date;
  address?: string;

  // Security Information
  failedLoginAttempts: number;
  lastFailedLogin?: Date;
  accountLockedUntil?: Date;
  lastPasswordChange?: Date;
}

// Login response data structure
interface AuthResponse {
  token: string;
  refreshToken?: string;
  expiration: Date;
  refreshTokenExpiration?: Date;
  requiresMfa: boolean;
  mfaToken?: string;
  user: User;
}

// MFA interfaces
interface MfaSetupResponse {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
  expiration: Date;
  refreshTokenExpiration: Date;
}

// User preferences interface
interface UserPreferences {
  preferredLanguage: string;
  preferredTheme: string;
  timeZone: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  // Enhanced storage keys
  public readonly tokenKey = 'auth_token';
  public readonly refreshTokenKey = 'refresh_token';
  public readonly userKey = 'current_user';
  public readonly preferencesKey = 'user_preferences';
  public readonly themeKey = 'user_theme';
  public readonly languageKey = 'user_language';

  private apiUrl = environment.apiUrl;

  // Theme and language subjects
  private currentThemeSubject = new BehaviorSubject<string>('light');
  public currentTheme$ = this.currentThemeSubject.asObservable();

  private currentLanguageSubject = new BehaviorSubject<string>('en-US');
  public currentLanguage$ = this.currentLanguageSubject.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router,
    private tenantService: TenantService
  ) {
    this.loadStoredUser();
    this.loadUserPreferences();
  }

  private loadStoredUser(): void {
    const storedToken = localStorage.getItem(this.tokenKey);
    const storedUser = localStorage.getItem(this.userKey);

    console.log('Loading stored user - Token exists:', !!storedToken);
    console.log('Loading stored user - User exists:', !!storedUser);

    if (storedUser) {
      try {
        const user: User = JSON.parse(storedUser);
        console.log('Successfully loaded user from localStorage:', user.username);

        // Validate token exists
        if (!storedToken) {
          console.warn('No token found in localStorage, user will need to re-authenticate');
          this.logout();
          return;
        }

        this.currentUserSubject.next(user);
      } catch (e) {
        console.error('Error parsing stored user:', e);
        this.logout();
      }
    } else {
      console.log('No stored user found in localStorage');
    }
  }

  private loadUserPreferences(): void {
    // Load theme preference
    const storedTheme = localStorage.getItem(this.themeKey);
    if (storedTheme) {
      this.currentThemeSubject.next(storedTheme);
      this.applyTheme(storedTheme);
    }

    // Load language preference
    const storedLanguage = localStorage.getItem(this.languageKey);
    if (storedLanguage) {
      this.currentLanguageSubject.next(storedLanguage);
    }
  }

  private applyTheme(theme: string): void {
    document.documentElement.setAttribute('data-theme', theme);
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${theme}`);
  }

  login(username: string, password: string, mfaCode?: string, rememberMe: boolean = false): Observable<AuthResponse> {
    // Get current tenant information
    const currentTenant = this.tenantService.getCurrentTenantSync();

    // Prepare headers with tenant information
    const headers: any = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    // Add tenant headers if available
    const tenantHeaders = this.tenantService.getTenantHeaders();
    Object.assign(headers, tenantHeaders);

    console.log(`Attempting to login with username: ${username} to API: ${this.apiUrl}/auth/login`);
    if (currentTenant) {
      console.log(`Login for tenant: ${currentTenant.name} (${currentTenant.slug})`);
    }

    // Prepare login payload with tenant information
    const loginPayload: any = { username, password, rememberMe };
    if (mfaCode) {
      loginPayload.mfaCode = mfaCode;
    }

    // Add tenant ID to login payload if available
    if (currentTenant) {
      loginPayload.tenantId = currentTenant.id;
    }

    // Make the API call and map the response
    return this.http.post<ApiResponse<AuthResponse>>(`${this.apiUrl}/auth/login`, loginPayload, { headers }).pipe(
      tap(apiResponse => {
        // Extract the actual response data from the API response wrapper
        if (apiResponse && apiResponse.success && apiResponse.data) {
          const response = apiResponse.data;
          console.log('API login response:', response);

          // Check if MFA is required
          if (response.requiresMfa) {
            console.log('MFA verification required');
            // Don't store tokens yet, just return the response
            return;
          }

          // Store the tokens and user data
          console.log('Storing tokens in localStorage');

          // Clear any existing tokens first
          this.clearStoredTokens();

          // Store access token
          if (response.token) {
            let tokenToStore = response.token;
            if (tokenToStore.startsWith('"') && tokenToStore.endsWith('"')) {
              tokenToStore = tokenToStore.substring(1, tokenToStore.length - 1);
            }
            localStorage.setItem(this.tokenKey, tokenToStore);
          }

          // Store refresh token
          if (response.refreshToken) {
            localStorage.setItem(this.refreshTokenKey, response.refreshToken);
          }

          // Store user data
          localStorage.setItem(this.userKey, JSON.stringify(response.user));

          // Update user preferences
          this.updateUserPreferences(response.user);

          // Update current user subject
          this.currentUserSubject.next(response.user);

          console.log('Login successful for user:', response.user.username);
        } else {
          console.error('Invalid API response format:', apiResponse);
          throw new Error('Invalid API response format');
        }
      }),
      // Map the API response to the expected AuthResponse type
      map((apiResponse: ApiResponse<AuthResponse>) => {
        if (apiResponse && apiResponse.success && apiResponse.data) {
          return apiResponse.data;
        }
        throw new Error('Invalid API response format');
      })
    );
  }

  register(user: any): Observable<any> {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    console.log(`Attempting to register user with API: ${this.apiUrl}/auth/register`, user);

    return this.http.post<ApiResponse<any>>(`${this.apiUrl}/auth/register`, user, { headers }).pipe(
      tap(apiResponse => {
        if (apiResponse && apiResponse.success) {
          console.log('API register response:', apiResponse);
        } else {
          console.error('Invalid API response format:', apiResponse);
          throw new Error(apiResponse.message || 'Registration failed');
        }
      }),
      map((apiResponse: ApiResponse<any>) => {
        if (apiResponse && apiResponse.success) {
          return apiResponse.data;
        }
        throw new Error(apiResponse.message || 'Registration failed');
      })
    );
  }

  logout(): void {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
    this.currentUserSubject.next(null);
    this.router.navigate(['/login']);
  }

  isLoggedIn(): boolean {
    const token = this.getToken();
    const user = this.getCurrentUser();

    console.log('isLoggedIn check - Token exists:', !!token);
    console.log('isLoggedIn check - User exists:', !!user);

    // Both token and user must exist for a valid login state
    const isValid = !!token && !!user;
    console.log('isLoggedIn result:', isValid);

    return isValid;
  }

  getToken(): string | null {
    const token = localStorage.getItem(this.tokenKey);
    console.log('getToken called - Token exists:', !!token);
    if (token) {
      console.log('Token value (first 10 chars):', token.substring(0, 10) + '...');

      // Verify token is valid JWT format (simple check)
      if (!this.isValidJwtFormat(token)) {
        console.warn('Token does not appear to be in valid JWT format');

        // Try to fix common token format issues
        if (token.startsWith('"') && token.endsWith('"')) {
          // Token might be double-quoted - remove quotes
          const fixedToken = token.substring(1, token.length - 1);
          console.log('Fixing double-quoted token');

          // Store the fixed token
          localStorage.setItem(this.tokenKey, fixedToken);
          return fixedToken;
        }
      }

      // Check if token is expired
      if (this.isTokenExpired(token)) {
        console.warn('Token appears to be expired');
        return null; // Don't use expired tokens
      }
    } else {
      console.warn('No token found in localStorage with key:', this.tokenKey);

      // Try to restore token from user object
      const storedUser = localStorage.getItem(this.userKey);
      if (storedUser) {
        try {
          const user = JSON.parse(storedUser);
          if (user._token) {
            console.warn('Found token in user object - restoring');
            localStorage.setItem(this.tokenKey, user._token);
            return user._token;
          }
        } catch (e) {
          console.error('Error parsing stored user while trying to restore token:', e);
        }
      }

      // Check if token exists with other common keys
      const altToken = localStorage.getItem('token') ||
                      localStorage.getItem('jwt_token') ||
                      localStorage.getItem('access_token');
      if (altToken) {
        console.warn('Found token with alternative key - consider migrating');

        // Use the alternative token and migrate it
        localStorage.setItem(this.tokenKey, altToken);
        return altToken;
      }
    }
    return token;
  }

  getRefreshToken(): string | null {
    return localStorage.getItem(this.refreshTokenKey);
  }

  /**
   * Check if a token is in valid JWT format
   */
  private isValidJwtFormat(token: string): boolean {
    // Simple check for JWT format (header.payload.signature)
    const parts = token.split('.');
    return parts.length === 3;
  }

  /**
   * Check if a token is expired
   */
  private isTokenExpired(token: string): boolean {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) return true;

      // Decode the payload
      const payload = JSON.parse(atob(parts[1]));

      // Check if token has expiration
      if (!payload.exp) return false;

      // Check if token is expired
      const expirationDate = new Date(payload.exp * 1000);
      const isExpired = expirationDate < new Date();

      if (isExpired) {
        console.log('Token is expired. Expiration date:', expirationDate);
        // Automatically logout if token is expired
        this.logout();
      }

      return isExpired;
    } catch (e) {
      console.error('Error checking token expiration:', e);
      return true; // Assume expired if we can't parse it
    }
  }

  getCurrentUser(): any {
    return this.currentUserSubject.value;
  }

  /**
   * Update the current user object
   * This is useful for overriding the user's role for testing purposes
   */
  updateCurrentUser(user: any): void {
    console.log('Updating current user:', user);

    // Update the BehaviorSubject
    this.currentUserSubject.next(user);

    // Update the user in localStorage
    localStorage.setItem(this.userKey, JSON.stringify(user));

    console.log('Current user updated successfully');
  }

  getCurrentUserId(): string | null {
    const user = this.getCurrentUser();
    return user ? user.id : null;
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    // Use roleName if available, otherwise use role
    const userRole = user.roleName || (user.role ? String(user.role) : null);
    if (!userRole) return false;

    // Case-insensitive comparison
    return userRole.toLowerCase() === role.toLowerCase();
  }

  isAdmin(): boolean {
    // Check for 'Admin' or 'SystemAdmin' role case-insensitively
    const user = this.getCurrentUser();
    if (!user) return false;

    // Use roleName if available, otherwise use role
    const userRole = user.roleName || (user.role ? String(user.role) : null);
    if (!userRole) return false;

    return userRole.toLowerCase() === 'admin' || userRole.toLowerCase() === 'systemadmin';
  }

  /**
   * Check if the current user is a SystemAdmin
   */
  isSystemAdmin(): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    // Check role number first (SystemAdmin = 3)
    if (user.role === 3) return true;

    // Check roleName
    const userRole = user.roleName || (user.role ? String(user.role) : null);
    if (!userRole) return false;

    return userRole.toLowerCase() === 'systemadmin';
  }

  /**
   * Check if the current user is a TenantAdmin (regular Admin)
   */
  isTenantAdmin(): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    // Check role number first (Admin = 2)
    if (user.role === 2) return true;

    // Check roleName
    const userRole = user.roleName || (user.role ? String(user.role) : null);
    if (!userRole) return false;

    return userRole.toLowerCase() === 'admin';
  }

  /**
   * Get admin type from JWT token claims
   */
  getAdminType(): 'SystemAdmin' | 'TenantAdmin' | null {
    const token = this.getToken();
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.adminType || null;
    } catch {
      return null;
    }
  }

  /**
   * Check if user can access all tenants (SystemAdmin privilege)
   */
  canAccessAllTenants(): boolean {
    const token = this.getToken();
    if (!token) return false;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.canAccessAllTenants === 'true' || payload.isSystemAdmin === 'true';
    } catch {
      return false;
    }
  }

  isEditor(): boolean {
    return this.hasRole('Admin') || this.hasRole('Editor');
  }

  /**
   * Get the user's role from the current user object
   * @returns The user's role as a string ('Student', 'Parent', 'Faculty', 'Admin') or null if not logged in
   */
  getUserRole(): string | null {
    const user = this.getCurrentUser();
    if (!user) return null;

    // Use roleName if available, otherwise use role
    return user.roleName || (user.role ? String(user.role) : null);
  }

  /**
   * Navigate directly to the admin panel
   */
  navigateToAdminPanel(): void {
    console.log('Directly navigating to admin panel');
    // Navigate to the admin dashboard
    this.router.navigate(['/admin']);
  }

  /**
   * Navigate the user to their appropriate portal based on their role
   */
  navigateToUserPortal(): void {
    const role = this.getUserRole();
    console.log('Navigating based on role:', role);

    // Double-check that token is still in localStorage
    const token = localStorage.getItem(this.tokenKey);
    console.log('Token before navigation:', token ? 'Present' : 'Missing');

    // If token is missing but we have a user, something is wrong
    if (!token && this.currentUserSubject.value) {
      console.warn('Token is missing but user is present - user needs to re-authenticate');
      this.logout();
      return;
    }

    // Convert role to lowercase for case-insensitive comparison
    const roleLower = role ? role.toLowerCase() : '';
    console.log('Navigating based on normalized role:', roleLower);

    // Use setTimeout to ensure navigation happens after the current execution context
    setTimeout(() => {
      switch (roleLower) {
        case 'student':
          console.log('Navigating to student portal');
          this.router.navigate(['/student-portal']);
          break;
        case 'parent':
          console.log('Navigating to parent portal');
          this.router.navigate(['/parent-portal']);
          break;
        case 'faculty':
          console.log('Navigating to faculty portal');
          this.router.navigate(['/faculty-portal']);
          break;
        case 'admin':
          console.log('Navigating to admin panel');
          this.router.navigate(['/admin']);
          break;
        default:
          console.log('No valid role found, navigating to home');
          this.router.navigate(['/home']);
          break;
      }

      // Check token after navigation
      setTimeout(() => {
        const tokenAfter = localStorage.getItem(this.tokenKey);
        console.log('Token after navigation:', tokenAfter ? 'Present' : 'Missing');
      }, 100);
    }, 0);
  }

  // Enhanced authentication methods
  private clearStoredTokens(): void {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.refreshTokenKey);
    localStorage.removeItem('token');
    localStorage.removeItem('jwt_token');
    localStorage.removeItem('access_token');
  }

  private updateUserPreferences(user: User): void {
    // Update theme
    if (user.preferredTheme) {
      localStorage.setItem(this.themeKey, user.preferredTheme);
      this.currentThemeSubject.next(user.preferredTheme);
      this.applyTheme(user.preferredTheme);
    }

    // Update language
    if (user.preferredLanguage) {
      localStorage.setItem(this.languageKey, user.preferredLanguage);
      this.currentLanguageSubject.next(user.preferredLanguage);
    }

    // Store complete preferences
    const preferences: UserPreferences = {
      preferredLanguage: user.preferredLanguage,
      preferredTheme: user.preferredTheme,
      timeZone: user.timeZone
    };
    localStorage.setItem(this.preferencesKey, JSON.stringify(preferences));
  }

  // MFA Methods
  setupMfa(password: string): Observable<MfaSetupResponse> {
    return this.http.post<ApiResponse<MfaSetupResponse>>(`${this.apiUrl}/auth/mfa/setup`, { password })
      .pipe(map(response => response.data!));
  }

  verifyMfa(code: string, secret?: string): Observable<{ verified: boolean }> {
    const payload: any = { code };
    if (secret) {
      payload.secret = secret;
    }
    return this.http.post<ApiResponse<{ verified: boolean }>>(`${this.apiUrl}/auth/mfa/verify`, payload)
      .pipe(map(response => response.data!));
  }

  enableMfa(secret: string, code: string): Observable<{ enabled: boolean }> {
    return this.http.post<ApiResponse<{ enabled: boolean }>>(`${this.apiUrl}/auth/mfa/enable`, { secret, code })
      .pipe(map(response => response.data!));
  }

  disableMfa(password: string, mfaCode: string): Observable<{ disabled: boolean }> {
    return this.http.post<ApiResponse<{ disabled: boolean }>>(`${this.apiUrl}/auth/mfa/disable`, { password, mfaCode })
      .pipe(map(response => response.data!));
  }

  // Token Management
  refreshToken(): Observable<RefreshTokenResponse> {
    const refreshToken = localStorage.getItem(this.refreshTokenKey);
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    return this.http.post<ApiResponse<RefreshTokenResponse>>(`${this.apiUrl}/auth/refresh`, { refreshToken })
      .pipe(
        tap(apiResponse => {
          if (apiResponse.success && apiResponse.data) {
            const response = apiResponse.data;
            localStorage.setItem(this.tokenKey, response.token);
            localStorage.setItem(this.refreshTokenKey, response.refreshToken);
          }
        }),
        map(response => response.data!)
      );
  }

  // Theme Management
  getCurrentTheme(): string {
    return this.currentThemeSubject.value;
  }

  setTheme(theme: string): void {
    localStorage.setItem(this.themeKey, theme);
    this.currentThemeSubject.next(theme);
    this.applyTheme(theme);

    // Update user preferences on server if logged in
    const user = this.getCurrentUser();
    if (user) {
      this.updateUserPreferencesOnServer({ preferredTheme: theme });
    }
  }

  // Language Management
  setLanguage(language: string): void {
    localStorage.setItem(this.languageKey, language);
    this.currentLanguageSubject.next(language);

    // Update user preferences on server if logged in
    const user = this.getCurrentUser();
    if (user) {
      this.updateUserPreferencesOnServer({ preferredLanguage: language });
    }
  }

  private updateUserPreferencesOnServer(preferences: Partial<UserPreferences>): void {
    this.http.put(`${this.apiUrl}/users/preferences`, preferences)
      .subscribe({
        next: () => console.log('User preferences updated on server'),
        error: (error) => console.error('Failed to update user preferences on server:', error)
      });
  }

  // Enhanced logout with refresh token revocation
  enhancedLogout(): Observable<any> {
    const refreshToken = localStorage.getItem(this.refreshTokenKey);

    if (refreshToken) {
      return this.http.post(`${this.apiUrl}/auth/logout`, { refreshToken })
        .pipe(
          tap(() => this.clearAllUserData()),
          map(() => true)
        );
    } else {
      this.clearAllUserData();
      return new Observable(observer => {
        observer.next(true);
        observer.complete();
      });
    }
  }

  private clearAllUserData(): void {
    this.clearStoredTokens();
    localStorage.removeItem(this.userKey);
    localStorage.removeItem(this.preferencesKey);
    this.currentUserSubject.next(null);
  }

  // Forgot Password functionality
  forgotPassword(email: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/auth/forgot-password`, { email })
      .pipe(
        tap(() => console.log('Password reset email sent successfully'))
      );
  }

  resetPassword(token: string, newPassword: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/auth/reset-password`, {
      token,
      newPassword
    })
    .pipe(
      tap(() => console.log('Password reset successfully'))
    );
  }

  // Get current user synchronously for immediate access
  getCurrentUserSync(): User | null {
    return this.currentUserSubject.value;
  }
}
