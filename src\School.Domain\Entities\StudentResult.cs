using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class StudentResult : BaseEntity
{
    public Guid StudentId { get; set; }
    public ExamType ExamType { get; set; }
    public string SubjectCode { get; set; } = string.Empty;
    public string SubjectName { get; set; } = string.Empty;
    public decimal MarksObtained { get; set; }
    public decimal TotalMarks { get; set; }
    public decimal Percentage => (MarksObtained / TotalMarks) * 100;
    public string LetterGrade { get; set; } = string.Empty; // A+, A, A-, B, etc.
    public decimal GradePoint { get; set; } // 5.0, 4.0, 3.5, etc.
    public string Remarks { get; set; } = string.Empty;
    public int AcademicYear { get; set; }
    public bool IsOptional { get; set; } = false;

    // Navigation properties
    public Student Student { get; set; } = null!;
    public int Marks { get; set; }
    public decimal? Credits { get; set; }
    public string MaxMarks { get; set; } = string.Empty;
    public string Grade { get; set; }
    public DateTime UpdatedAt { get; set; }
}
