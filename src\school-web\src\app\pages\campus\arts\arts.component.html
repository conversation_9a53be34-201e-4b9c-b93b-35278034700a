<!-- Hero Section -->
<app-default-hero
  translationPrefix="CAMPUS_LIFE"
  title="CAMPUS_LIFE.ARTS"
  subtitle="CAMPUS_LIFE.ARTS_SUBTITLE"
  theme="dark"
  size="large"
  alignment="center"
  backgroundImage="assets/images/campus/arts-hero.jpg">
</app-default-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'CAMPUS_LIFE.ARTS_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.ARTS_INTRO_P1' | translate }}</p>
      <p>{{ 'CAMPUS_LIFE.ARTS_INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Arts Philosophy Section -->
  <section class="philosophy-section">
    <h2>{{ 'CAMPUS_LIFE.ARTS_PHILOSOPHY' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.ARTS_PHILOSOPHY_INTRO' | translate }}</p>

    <div class="philosophy-grid">
      <div class="philosophy-item" *ngFor="let point of philosophyPoints">
        <h3>{{point.title}}</h3>
        <p>{{point.description}}</p>
      </div>
    </div>
  </section>

  <!-- Arts Programs Section -->
  <section class="programs-section">
    <h2>{{ 'CAMPUS_LIFE.ARTS_PROGRAMS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.ARTS_PROGRAMS_INTRO' | translate }}</p>

    <mat-tab-group animationDuration="300ms">
      <mat-tab *ngFor="let category of artsCategories" [label]="category">
        <div class="programs-grid">
          <mat-card class="program-card" *ngFor="let program of getProgramsByCategory(category)">
            <div class="program-image">
              <img [src]="program.image" [alt]="program.name">
            </div>
            <mat-card-content>
              <h3>{{program.name}}</h3>
              <p class="program-description">{{program.description}}</p>
              <div class="program-details">
                <div class="detail-item">
                  <mat-icon>person</mat-icon>
                  <span>{{program.instructor}}</span>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>
    </mat-tab-group>
  </section>

  <!-- Arts Facilities Section -->
  <section class="facilities-section">
    <h2>{{ 'CAMPUS_LIFE.ARTS_FACILITIES' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.ARTS_FACILITIES_INTRO' | translate }}</p>

    <div class="facilities-grid">
      <mat-card class="facility-card" *ngFor="let facility of facilities">
        <div class="facility-image">
          <img [src]="facility.image" [alt]="facility.name">
        </div>
        <mat-card-content>
          <h3>{{facility.name}}</h3>
          <ul class="facility-features">
            <li *ngFor="let feature of facility.features">{{feature}}</li>
          </ul>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Upcoming Events Section -->
  <section class="events-section">
    <h2>{{ 'CAMPUS_LIFE.UPCOMING_EVENTS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.EVENTS_INTRO' | translate }}</p>

    <div class="events-grid">
      <mat-card class="event-card" *ngFor="let event of upcomingEvents">
        <div class="event-image">
          <img [src]="event.image" [alt]="event.title">
        </div>
        <mat-card-content>
          <h3>{{event.title}}</h3>
          <div class="event-details">
            <div class="detail-item">
              <mat-icon>calendar_today</mat-icon>
              <span>{{event.date}}</span>
            </div>
            <div class="detail-item">
              <mat-icon>schedule</mat-icon>
              <span>{{event.time}}</span>
            </div>
            <div class="detail-item">
              <mat-icon>location_on</mat-icon>
              <span>{{event.location}}</span>
            </div>
          </div>
          <p class="event-description">{{event.description}}</p>
        </mat-card-content>
        <mat-card-actions>
          <a mat-button color="primary" routerLink="/events">
            {{ 'CAMPUS_LIFE.MORE_INFO' | translate }}
            <mat-icon>arrow_forward</mat-icon>
          </a>
        </mat-card-actions>
      </mat-card>
    </div>

    <div class="events-cta">
      <a mat-raised-button color="primary" routerLink="/events">
        {{ 'CAMPUS_LIFE.VIEW_ALL_EVENTS' | translate }}
      </a>
    </div>
  </section>

  <!-- Student Showcase Section -->
  <section class="showcase-section">
    <h2>{{ 'CAMPUS_LIFE.STUDENT_SHOWCASE' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.SHOWCASE_INTRO' | translate }}</p>

    <div class="showcase-gallery">
      <div class="gallery-item">
        <img src="assets/images/campus/arts/student-art1.jpg" alt="Student Artwork">
      </div>
      <div class="gallery-item">
        <img src="assets/images/campus/arts/student-art2.jpg" alt="Student Artwork">
      </div>
      <div class="gallery-item">
        <img src="assets/images/campus/arts/student-art3.jpg" alt="Student Artwork">
      </div>
      <div class="gallery-item">
        <img src="assets/images/campus/arts/student-art4.jpg" alt="Student Artwork">
      </div>
      <div class="gallery-item">
        <img src="assets/images/campus/arts/student-art5.jpg" alt="Student Artwork">
      </div>
      <div class="gallery-item">
        <img src="assets/images/campus/arts/student-art6.jpg" alt="Student Artwork">
      </div>
      <div class="gallery-item">
        <img src="assets/images/campus/arts/student-art7.jpg" alt="Student Artwork">
      </div>
      <div class="gallery-item">
        <img src="assets/images/campus/arts/student-art8.jpg" alt="Student Artwork">
      </div>
    </div>

    <div class="showcase-cta">
      <a mat-raised-button color="primary" routerLink="/gallery">
        {{ 'CAMPUS_LIFE.VIEW_FULL_GALLERY' | translate }}
      </a>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="contact-section">
    <div class="contact-content">
      <h2>{{ 'CAMPUS_LIFE.ARTS_DEPARTMENT' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.ARTS_DEPARTMENT_TEXT' | translate }}</p>
      <div class="contact-info">
        <div class="contact-item">
          <mat-icon>person</mat-icon>
          <div class="contact-details">
            <h3>{{ 'CAMPUS_LIFE.ARTS_DIRECTOR' | translate }}</h3>
            <p>Dr. Elizabeth Johnson</p>
            <p>ejohnson&#64;school.edu</p>
            <p>(123) 456-7890 ext. 345</p>
          </div>
        </div>

        <div class="contact-item">
          <mat-icon>location_on</mat-icon>
          <div class="contact-details">
            <h3>{{ 'CAMPUS_LIFE.ARTS_OFFICE' | translate }}</h3>
            <p>Arts Building, Room 101</p>
            <p>Monday-Friday, 8:00 AM - 4:00 PM</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
