using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class CareerApplication : BaseEntity
{
    public Guid CareerId { get; set; }
    public string ApplicantName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string CoverLetter { get; set; } = string.Empty;
    public string ResumeFilePath { get; set; } = string.Empty;
    public ApplicationStatus Status { get; set; } = ApplicationStatus.Submitted;
    public string ReviewComments { get; set; } = string.Empty;
    public string ReviewedBy { get; set; } = string.Empty;
    public DateTime? ReviewedAt { get; set; }
    
    // Navigation properties
    public Career Career { get; set; } = null!;
    public DateTime? UpdatedAt { get; set; }
}
