import { Routes } from '@angular/router';
import { HomeComponent } from './pages/home/<USER>';
import { NoticesComponent } from './pages/notices/notices.component';
import { ADMIN_ROUTES } from './admin/admin.routes';
import { authGuard } from './core/guards/auth.guard';
import { tenantSetupGuard } from './core/guards/tenant-setup.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full'
  },
  // Tenant Setup Routes (standalone, no layout)
  {
    path: 'tenant-setup/wizard',
    loadComponent: () => import('./pages/tenant-setup/tenant-setup-wizard.component').then(m => m.TenantSetupWizardComponent),
    canActivate: [authGuard],
    title: 'School Setup Wizard'
  },
  {
    path: 'tenant-setup/select',
    loadComponent: () => import('./pages/tenant-setup/tenant-select.component').then(m => m.TenantSelectComponent),
    canActivate: [authGuard],
    title: 'Select School'
  },
  {
    path: 'tenant-setup/create',
    loadComponent: () => import('./pages/tenant-setup/tenant-create.component').then(m => m.TenantCreateComponent),
    canActivate: [authGuard],
    title: 'Create School'
  },
  {
    path: 'tenant-setup/pending',
    loadComponent: () => import('./pages/tenant-setup/tenant-pending.component').then(m => m.TenantPendingComponent),
    canActivate: [authGuard],
    title: 'Setup Pending'
  },
  {
    path: 'tenant-setup/inactive',
    loadComponent: () => import('./pages/tenant-setup/tenant-inactive.component').then(m => m.TenantInactiveComponent),
    canActivate: [authGuard],
    title: 'School Inactive'
  },
  {
    path: 'tenant-setup/access-denied',
    loadComponent: () => import('./pages/tenant-setup/tenant-access-denied.component').then(m => m.TenantAccessDeniedComponent),
    canActivate: [authGuard],
    title: 'Access Denied'
  },
  {
    path: 'tenant-setup/error',
    loadComponent: () => import('./pages/tenant-setup/tenant-error.component').then(m => m.TenantErrorComponent),
    title: 'Setup Error'
  },
  {
    path: 'home',
    component: HomeComponent,
    title: 'Home'
  },
  {
    path: 'about',
    loadComponent: () => import('./pages/about/about.component').then(m => m.AboutComponent),
    title: 'About Us - Your School Name'
  },
  {
    path: 'about/history',
    loadComponent: () => import('./pages/about/history/history.component').then(m => m.HistoryComponent),
    title: 'Our History - Your School Name'
  },
  {
    path: 'about/mission',
    loadComponent: () => import('./pages/about/mission/mission.component').then(m => m.MissionComponent),
    title: 'Mission & Vision - Your School Name'
  },
  {
    path: 'about/leadership',
    loadComponent: () => import('./pages/about/leadership/leadership.component').then(m => m.LeadershipComponent),
    title: 'Leadership - Your School Name'
  },
  {
    path: 'about/parents',
    loadComponent: () => import('./pages/about/parents/parents.component').then(m => m.ParentsComponent),
    title: 'Parents - Your School Name'
  },
  {
    path: 'academics',
    loadComponent: () => import('./pages/academics/academics.component').then(m => m.AcademicsComponent),
    title: 'Academics - Your School Name'
  },
  {
    path: 'academics/elementary',
    loadComponent: () => import('./pages/academics/elementary/elementary.component').then(m => m.ElementaryComponent),
    title: 'Elementary School - Your School Name'
  },
  {
    path: 'academics/middle',
    loadComponent: () => import('./pages/academics/middle/middle.component').then(m => m.MiddleComponent),
    title: 'Middle School - Your School Name'
  },
  {
    path: 'academics/high',
    loadComponent: () => import('./pages/academics/high/high.component').then(m => m.HighComponent),
    title: 'High School - Your School Name'
  },
  {
    path: 'academics/stem',
    loadComponent: () => import('./pages/academics/stem/stem.component').then(m => m.StemComponent),
    title: 'STEM Programs - Your School Name'
  },
  {
    path: 'academics/languages',
    loadComponent: () => import('./pages/content/content.component').then(m => m.ContentComponent),
    data: { slug: 'languages' },
    title: 'Language Programs - Your School Name'
  },
  {
    path: 'academics/technology',
    loadComponent: () => import('./pages/content/content.component').then(m => m.ContentComponent),
    data: { slug: 'technology' },
    title: 'Technology - Your School Name'
  },
  {
    path: 'academics/counseling',
    loadComponent: () => import('./pages/content/content.component').then(m => m.ContentComponent),
    data: { slug: 'counseling' },
    title: 'Counseling - Your School Name'
  },
  {
    path: 'academics/arts',
    loadComponent: () => import('./pages/content/content.component').then(m => m.ContentComponent),
    data: { slug: 'academic-arts' },
    title: 'Arts Programs - Your School Name'
  },
  {
    path: 'admissions',
    loadComponent: () => import('./pages/admissions/admissions.component').then(m => m.AdmissionsComponent),
    title: 'Admissions - Your School Name'
  },
  {
    path: 'admissions/visit',
    loadComponent: () => import('./pages/content/content.component').then(m => m.ContentComponent),
    data: { slug: 'visit-campus' },
    title: 'Visit Us - Your School Name'
  },
  {
    path: 'facilities',
    loadComponent: () => import('./pages/facilities/facilities.component').then(m => m.FacilitiesComponent),
    title: 'Facilities - Your School Name'
  },
  {
    path: 'events',
    loadComponent: () => import('./pages/events/events.component').then(m => m.EventsComponent),
    title: 'Events - Your School Name'
  },
  {
    path: 'events/:id',
    loadComponent: () => import('./pages/events/event-detail/event-detail.component').then(m => m.EventDetailComponent),
    title: 'Event Details - Your School Name'
  },
  {
    path: 'events/:id/register',
    loadComponent: () => import('./pages/events/event-registration/event-registration.component').then(m => m.EventRegistrationComponent),
    title: 'Event Registration - Your School Name'
  },
  {
    path: 'gallery',
    loadComponent: () => import('./pages/gallery/gallery.component').then(m => m.GalleryComponent),
    title: 'Gallery - Your School Name'
  },
  {
    path: 'contact',
    loadComponent: () => import('./pages/contact/contact.component').then(m => m.ContactComponent),
    title: 'Contact - Your School Name'
  },
  {
    path: 'login',
    loadComponent: () => import('./pages/login/login.component').then(m => m.LoginComponent),
    title: 'Login - Your School Name'
  },
  {
    path: 'register',
    loadComponent: () => import('./pages/register/register.component').then(m => m.RegisterComponent),
    title: 'Register - Your School Name'
  },
  {
    path: 'forgot-password',
    loadComponent: () => import('./pages/forgot-password/forgot-password.component').then(m => m.ForgotPasswordComponent),
    title: 'Reset Password - Your School Name'
  },
  {
    path: 'staff-portal',
    loadComponent: () => import('./pages/staff-portal/staff-portal.component').then(m => m.StaffPortalComponent),
    title: 'Staff Portal - Your School Name'
  },
  {
    path: 'auth-test',
    loadComponent: () => import('./shared/components/auth-test/auth-test.component').then(m => m.AuthTestComponent),
    title: 'Auth Test'
  },
  {
    path: 'news',
    loadComponent: () => import('./pages/news/news.component').then(m => m.NewsComponent),
    title: 'News & Updates - Your School Name'
  },
  {
    path: 'news/announcements',
    loadComponent: () => import('./pages/content/content.component').then(m => m.ContentComponent),
    data: { slug: 'announcements' },
    title: 'Announcements - Your School Name'
  },
  {
    path: 'news/newsletter',
    loadComponent: () => import('./pages/content/content.component').then(m => m.ContentComponent),
    data: { slug: 'newsletter' },
    title: 'Newsletter - Your School Name'
  },
  {
    path: 'news/:id',
    loadComponent: () => import('./pages/news/news-detail/news-detail.component').then(m => m.NewsDetailComponent),
    title: 'News Article - Your School Name'
  },
  {
    path: 'calendar',
    loadComponent: () => import('./pages/calendar/calendar.component').then(m => m.CalendarComponent),
    title: 'Academic Calendar - Your School Name'
  },
  {
    path: 'library',
    loadComponent: () => import('./pages/library/library.component').then(m => m.LibraryComponent),
    title: 'Library - Your School Name'
  },
  {
    path: 'academics/library',
    redirectTo: 'library',
    pathMatch: 'full'
  },
  {
    path: 'faculty',
    loadComponent: () => import('./pages/faculty/faculty.component').then(m => m.FacultyComponent),
    title: 'Our Faculty - Your School Name'
  },
  {
    path: 'faculty/:id',
    loadComponent: () => import('./pages/faculty/faculty-profile/faculty-profile.component').then(m => m.FacultyProfileComponent),
    title: 'Faculty Profile - Your School Name'
  },
  {
    path: 'alumni',
    loadComponent: () => import('./pages/alumni/alumni.component').then(m => m.AlumniComponent),
    title: 'Alumni Network - Your School Name'
  },
  {
    path: 'alumni/:id',
    loadComponent: () => import('./pages/alumni/alumni-profile/alumni-profile.component').then(m => m.AlumniProfileComponent),
    title: 'Alumni Profile - Your School Name'
  },
  {
    path: 'tuition',
    loadComponent: () => import('./pages/tuition/tuition.component').then(m => m.TuitionComponent),
    title: 'Tuition & Fees - Your School Name'
  },
  {
    path: 'student-portal',
    loadChildren: () => import('./pages/student-portal/student-portal.module').then(m => m.StudentPortalModule),
    canActivate: [authGuard, tenantSetupGuard],
    title: 'Student Portal'
  },
  {
    path: 'parent-portal',
    loadChildren: () => import('./pages/parent-portal/parent-portal.module').then(m => m.ParentPortalModule),
    canActivate: [authGuard, tenantSetupGuard],
    title: 'Parent Portal'
  },
  {
    path: 'faculty-portal',
    loadChildren: () => import('./pages/faculty-portal/faculty-portal.module').then(m => m.FacultyPortalModule),
    canActivate: [authGuard, tenantSetupGuard],
    title: 'Faculty Portal'
  },

  {
    path: 'admin',
    children: ADMIN_ROUTES,
    canActivate: [tenantSetupGuard],
    title: 'Admin Panel'
  },
  {
    path: 'careers',
    loadComponent: () => import('./pages/careers/careers.component').then(m => m.CareersComponent),
    title: 'Careers - Your School Name'
  },
  {
    path: 'careers/:id',
    loadComponent: () => import('./pages/careers/job-detail/job-detail.component').then(m => m.JobDetailComponent),
    title: 'Job Details - Your School Name'
  },
  {
    path: 'campus',
    loadComponent: () => import('./pages/campus/campus.component').then(m => m.CampusComponent),
    title: 'Campus Life - Your School Name'
  },
  {
    path: 'campus/clubs',
    loadComponent: () => import('./pages/campus/clubs/clubs.component').then(m => m.ClubsComponent),
    title: 'Clubs & Activities - Your School Name'
  },
  {
    path: 'campus/clubs/:id',
    loadComponent: () => import('./pages/campus/clubs/club-detail/club-detail.component').then(m => m.ClubDetailComponent),
    title: 'Club Details - Your School Name'
  },
  {
    path: 'campus/sports',
    loadComponent: () => import('./pages/campus/sports/sports.component').then(m => m.SportsComponent),
    title: 'Sports Programs - Your School Name'
  },
  {
    path: 'campus/arts',
    loadComponent: () => import('./pages/campus/arts/arts.component').then(m => m.ArtsComponent),
    title: 'Arts Programs - Your School Name'
  },
  {
    path: 'campus/dining',
    loadComponent: () => import('./pages/campus/dining/dining.component').then(m => m.DiningComponent),
    title: 'Dining Services - Your School Name'
  },
  {
    path: 'campus/health',
    loadComponent: () => import('./pages/campus/health/health.component').then(m => m.HealthComponent),
    title: 'Health Services - Your School Name'
  },
  {
    path: 'campus/housing',
    loadComponent: () => import('./pages/campus/housing/housing.component').then(m => m.HousingComponent),
    title: 'Student Housing - Your School Name'
  },
  {
    path: 'campus/residential',
    redirectTo: 'campus/housing',
    pathMatch: 'full'
  },
  {
    path: 'campus/tour',
    loadComponent: () => import('./pages/campus/tour/tour.component').then(m => m.TourComponent),
    title: 'Campus Tour - Your School Name'
  },
  {
    path: 'notices',
    component: NoticesComponent,
    title: 'All Notices - Your School Name'
  },
  {
    path: 'content/:slug',
    loadComponent: () => import('./pages/content/content.component').then(m => m.ContentComponent),
    title: 'Content - Your School Name'
  },
  {
    path: 'hostel',
    loadComponent: () => import('./pages/hostel/hostel.component').then(m => m.HostelComponent),
    title: 'Residential Facilities - Your School Name'
  },
  {
    path: 'donate',
    loadComponent: () => import('./pages/content/content.component').then(m => m.ContentComponent),
    data: { slug: 'donate' },
    title: 'Support Our School - Your School Name'
  },
  {
    path: 'tenant-error',
    loadComponent: () => import('./pages/tenant-error/tenant-error.component').then(m => m.TenantErrorComponent),
    title: 'Tenant Error - School Management System'
  },
  {
    path: '**',
    loadComponent: () => import('./pages/not-found/not-found.component').then(m => m.NotFoundComponent),
    title: 'Page Not Found - Your School Name'
  }
];



