using System.ComponentModel.DataAnnotations;

namespace School.Application.DTOs;

/// <summary>
/// DTO for transferring class teacher between sections
/// </summary>
public class TransferClassTeacherDto
{
    /// <summary>
    /// Faculty ID to transfer
    /// </summary>
    [Required]
    public Guid FacultyId { get; set; }

    /// <summary>
    /// Source section ID
    /// </summary>
    [Required]
    public Guid FromSectionId { get; set; }

    /// <summary>
    /// Target section ID
    /// </summary>
    [Required]
    public Guid ToSectionId { get; set; }

    /// <summary>
    /// Reason for transfer
    /// </summary>
    public string? Reason { get; set; }
}
