<!-- Hero Section -->
<section class="hero-section">
  <div class="hero-content">
    <h1>{{ 'NEWS.TITLE' | translate }}</h1>
    <p class="hero-description">{{ 'NEWS.SUBTITLE' | translate }}</p>
  </div>
</section>

<!-- Main Content -->
<div class="container">
  <!-- Filters Section -->
  <div class="filters-section">
    <div class="search-filter">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>{{ 'NEWS.SEARCH' | translate }}</mat-label>
        <input matInput [(ngModel)]="searchQuery" placeholder="{{ 'NEWS.SEARCH_PLACEHOLDER' | translate }}">
        <button *ngIf="searchQuery" matSuffix mat-icon-button aria-label="Clear" (click)="searchQuery=''">
          <mat-icon>close</mat-icon>
        </button>
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
    </div>
    
    <div class="category-filter">
      <mat-form-field appearance="outline">
        <mat-label>{{ 'NEWS.CATEGORY' | translate }}</mat-label>
        <mat-select [(ngModel)]="selectedCategory">
          <mat-option *ngFor="let category of categories" [value]="category">
            {{category}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    
    <div class="reset-filter">
      <button mat-stroked-button color="primary" (click)="resetFilters()">
        <mat-icon>refresh</mat-icon>
        {{ 'NEWS.RESET_FILTERS' | translate }}
      </button>
    </div>
  </div>
  
  <!-- Results Count -->
  <div class="results-count">
    <p>{{ 'NEWS.SHOWING' | translate }} {{totalArticles}} {{ 'NEWS.RESULTS' | translate }}</p>
  </div>
  
  <!-- No Results -->
  <div class="no-results" *ngIf="filteredArticles.length === 0">
    <mat-icon>search_off</mat-icon>
    <h2>{{ 'NEWS.NO_RESULTS' | translate }}</h2>
    <p>{{ 'NEWS.NO_RESULTS_MESSAGE' | translate }}</p>
    <button mat-raised-button color="primary" (click)="resetFilters()">
      {{ 'NEWS.CLEAR_FILTERS' | translate }}
    </button>
  </div>
  
  <!-- News Grid -->
  <div class="news-grid" *ngIf="filteredArticles.length > 0">
    <mat-card class="news-card" *ngFor="let article of paginatedArticles">
      <div class="news-image">
        <img [src]="article.image" [alt]="article.title">
        <div class="news-category">{{article.category}}</div>
      </div>
      <mat-card-content>
        <div class="news-meta">
          <div class="news-date">
            <mat-icon>calendar_today</mat-icon>
            <span>{{formatDate(article.date)}}</span>
          </div>
          <div class="news-author">
            <mat-icon>person</mat-icon>
            <span>{{article.author}}</span>
          </div>
        </div>
        <h2 class="news-title">{{article.title}}</h2>
        <p class="news-excerpt">{{article.excerpt}}</p>
        <div class="news-tags">
          <mat-chip-set>
            <mat-chip *ngFor="let tag of article.tags.slice(0, 2)">{{tag}}</mat-chip>
          </mat-chip-set>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <a mat-raised-button color="primary" [routerLink]="['/news', article.id]">
          {{ 'NEWS.READ_MORE' | translate }}
        </a>
      </mat-card-actions>
    </mat-card>
  </div>
  
  <!-- Pagination -->
  <div class="pagination" *ngIf="filteredArticles.length > 0">
    <mat-paginator
      [length]="totalArticles"
      [pageSize]="pageSize"
      [pageSizeOptions]="pageSizeOptions"
      [pageIndex]="pageIndex"
      (page)="onPageChange($event)"
      aria-label="Select page">
    </mat-paginator>
  </div>
</div>
