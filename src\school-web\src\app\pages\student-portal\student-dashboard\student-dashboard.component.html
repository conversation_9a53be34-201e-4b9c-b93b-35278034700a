<div class="dashboard-container">
  <h1 class="page-title">Student Dashboard</h1>

  <div class="dashboard-content">
    <!-- Student Info Card -->
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>Student Information</mat-card-title>
      </mat-card-header>
      <mat-card-content *ngIf="student">
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">Name:</span>
            <span class="info-value">{{ student.firstName }} {{ student.lastName }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">ID:</span>
            <span class="info-value">{{ student.studentId }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Class:</span>
            <span class="info-value">{{ student.currentGrade }} - {{ student.section }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Medium:</span>
            <span class="info-value">{{ student.medium === 0 ? 'Bengali' : 'English' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Roll Number:</span>
            <span class="info-value">{{ student.rollNumber }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Academic Year:</span>
            <span class="info-value">{{ student.academicYear }}</span>
          </div>
        </div>
      </mat-card-content>
      <mat-card-content *ngIf="loading.student">
        <mat-progress-bar mode="indeterminate"></mat-progress-bar>
      </mat-card-content>
      <mat-card-content *ngIf="error.student">
        <p class="error-message">Failed to load student information</p>
      </mat-card-content>
    </mat-card>

    <!-- GPA Card -->
    <mat-card class="gpa-card">
      <mat-card-header>
        <mat-card-title>Current GPA</mat-card-title>
      </mat-card-header>
      <mat-card-content *ngIf="!loading.gpa && !error.gpa && currentGPA !== null">
        <div class="gpa-display">
          <div class="gpa-value">{{ currentGPA !== null ? currentGPA.toFixed(2) : 'N/A' }}</div>
          <div class="gpa-label">Annual Exam</div>
        </div>
      </mat-card-content>
      <mat-card-content *ngIf="loading.gpa">
        <mat-progress-spinner diameter="60" mode="indeterminate"></mat-progress-spinner>
      </mat-card-content>
      <mat-card-content *ngIf="error.gpa">
        <p class="error-message">Failed to load GPA information</p>
      </mat-card-content>
    </mat-card>

    <!-- Recent Attendance Card -->
    <mat-card class="attendance-card">
      <mat-card-header>
        <mat-card-title>Recent Attendance</mat-card-title>
      </mat-card-header>
      <mat-card-content *ngIf="!loading.attendance && !error.attendance">
        <div *ngIf="recentAttendance.length > 0" class="attendance-list">
          <div *ngFor="let attendance of recentAttendance" class="attendance-item">
            <div class="attendance-date">{{ attendance.date | date:'mediumDate' }}</div>
            <div class="attendance-status" [ngClass]="{
              'present': attendance.status === 0,
              'absent': attendance.status === 1,
              'late': attendance.status === 2,
              'excused': attendance.status === 3,
              'on-leave': attendance.status === 4
            }">
              {{ attendance.status === 0 ? 'Present' :
                 attendance.status === 1 ? 'Absent' :
                 attendance.status === 2 ? 'Late' :
                 attendance.status === 3 ? 'Excused' : 'On Leave' }}
            </div>
          </div>
        </div>
        <p *ngIf="recentAttendance.length === 0" class="no-data">No recent attendance records</p>
      </mat-card-content>
      <mat-card-content *ngIf="loading.attendance">
        <mat-progress-bar mode="indeterminate"></mat-progress-bar>
      </mat-card-content>
      <mat-card-content *ngIf="error.attendance">
        <p class="error-message">Failed to load attendance information</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" routerLink="/student-portal/attendance">View All</button>
      </mat-card-actions>
    </mat-card>

    <!-- Upcoming Fees Card -->
    <mat-card class="fees-card">
      <mat-card-header>
        <mat-card-title>Upcoming Fees</mat-card-title>
      </mat-card-header>
      <mat-card-content *ngIf="!loading.fees && !error.fees">
        <div *ngIf="upcomingFees.length > 0" class="fees-list">
          <div *ngFor="let fee of upcomingFees" class="fee-item">
            <div class="fee-info">
              <div class="fee-type">{{ fee.type === 0 ? 'Tuition' :
                                       fee.type === 1 ? 'Admission' :
                                       fee.type === 2 ? 'Examination' :
                                       fee.type === 3 ? 'Library' :
                                       fee.type === 4 ? 'Laboratory' :
                                       fee.type === 5 ? 'Transport' :
                                       fee.type === 6 ? 'Hostel' : 'Other' }}</div>
              <div class="fee-description">{{ fee.description }}</div>
            </div>
            <div class="fee-amount">
              <div class="fee-due">Due: {{ fee.dueAmount | currency:'BDT':'symbol':'1.0-0' }}</div>
              <div class="fee-date">{{ fee.dueDate | date:'mediumDate' }}</div>
            </div>
          </div>
        </div>
        <p *ngIf="upcomingFees.length === 0" class="no-data">No upcoming fees</p>
      </mat-card-content>
      <mat-card-content *ngIf="loading.fees">
        <mat-progress-bar mode="indeterminate"></mat-progress-bar>
      </mat-card-content>
      <mat-card-content *ngIf="error.fees">
        <p class="error-message">Failed to load fee information</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" routerLink="/student-portal/fees">View All</button>
      </mat-card-actions>
    </mat-card>

    <!-- Recent Results Card -->
    <mat-card class="results-card">
      <mat-card-header>
        <mat-card-title>Recent Results</mat-card-title>
      </mat-card-header>
      <mat-card-content *ngIf="!loading.results && !error.results">
        <div *ngIf="recentResults.length > 0" class="results-list">
          <div *ngFor="let result of recentResults" class="result-item">
            <div class="result-info">
              <div class="result-subject">{{ result.subjectName }}</div>
              <div class="result-exam">{{ result.examType === 0 ? 'Half Yearly' :
                                         result.examType === 1 ? 'Annual' :
                                         result.examType === 2 ? 'Class Test' : 'SSC' }}</div>
            </div>
            <div class="result-marks">
              <div class="result-grade">{{ result.letterGrade }}</div>
              <div class="result-score">{{ result.marksObtained }}/{{ result.totalMarks }}</div>
            </div>
          </div>
        </div>
        <p *ngIf="recentResults.length === 0" class="no-data">No recent results</p>
      </mat-card-content>
      <mat-card-content *ngIf="loading.results">
        <mat-progress-bar mode="indeterminate"></mat-progress-bar>
      </mat-card-content>
      <mat-card-content *ngIf="error.results">
        <p class="error-message">Failed to load result information</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" routerLink="/student-portal/results">View All</button>
      </mat-card-actions>
    </mat-card>

    <!-- Pending Leaves Card -->
    <mat-card class="leaves-card">
      <mat-card-header>
        <mat-card-title>Pending Leave Applications</mat-card-title>
      </mat-card-header>
      <mat-card-content *ngIf="!loading.leaves && !error.leaves">
        <div *ngIf="pendingLeaves.length > 0" class="leaves-list">
          <div *ngFor="let leave of pendingLeaves" class="leave-item">
            <div class="leave-info">
              <div class="leave-type">{{ leave.type === 0 ? 'Sick' :
                                        leave.type === 1 ? 'Personal' :
                                        leave.type === 2 ? 'Family' :
                                        leave.type === 3 ? 'Religious' : 'Other' }}</div>
              <div class="leave-dates">{{ leave.startDate | date:'shortDate' }} - {{ leave.endDate | date:'shortDate' }}</div>
            </div>
            <div class="leave-status pending">Pending</div>
          </div>
        </div>
        <p *ngIf="pendingLeaves.length === 0" class="no-data">No pending leave applications</p>
      </mat-card-content>
      <mat-card-content *ngIf="loading.leaves">
        <mat-progress-bar mode="indeterminate"></mat-progress-bar>
      </mat-card-content>
      <mat-card-content *ngIf="error.leaves">
        <p class="error-message">Failed to load leave information</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" routerLink="/student-portal/leaves">View All</button>
        <button mat-button color="accent" routerLink="/student-portal/leaves">Apply for Leave</button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
