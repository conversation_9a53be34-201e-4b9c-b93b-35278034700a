import { <PERSON>mpo<PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

// Angular Material
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCardModule } from '@angular/material/card';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDividerModule } from '@angular/material/divider';

// Services and Models
import { HolidayService } from '../../../../core/services/holiday.service';
import { AcademicYearService } from '../../../../core/services/academic-year.service';
import { 
  Holiday, 
  HolidayFilter, 
  HolidayType, 
  getHolidayTypeLabel, 
  getHolidayTypeColor,
  DEFAULT_HOLIDAY_FILTER 
} from '../../../../core/models/holiday.model';
import { AcademicYear, Term } from '../../../../core/models/academic-year.model';
import { PagedResult } from '../../../../core/models/paged-result.model';

@Component({
  selector: 'app-holiday-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatChipsModule,
    MatTooltipModule,
    MatMenuModule,
    MatDialogModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatCardModule,
    MatSlideToggleModule,
    MatDividerModule
  ],
  templateUrl: './holiday-list.component.html',
  styleUrls: ['./holiday-list.component.scss']
})
export class HolidayListComponent implements OnInit, OnDestroy {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  private destroy$ = new Subject<void>();
  
  // Data
  dataSource = new MatTableDataSource<Holiday>();
  holidays: Holiday[] = [];
  academicYears: AcademicYear[] = [];
  terms: Term[] = [];
  
  // State
  loading = false;
  totalCount = 0;
  
  // Filter
  filter: HolidayFilter = { ...DEFAULT_HOLIDAY_FILTER };
  searchTerm = '';
  
  // Table configuration
  displayedColumns: string[] = [
    'name',
    'type',
    'startDate',
    'endDate',
    'academicYear',
    'term',
    'status',
    'actions'
  ];
  
  // Options
  holidayTypes = [
    { value: HolidayType.National, label: 'National' },
    { value: HolidayType.Religious, label: 'Religious' },
    { value: HolidayType.Cultural, label: 'Cultural' },
    { value: HolidayType.Academic, label: 'Academic' },
    { value: HolidayType.Administrative, label: 'Administrative' },
    { value: HolidayType.Seasonal, label: 'Seasonal' },
    { value: HolidayType.Custom, label: 'Custom' }
  ];

  constructor(
    private holidayService: HolidayService,
    private academicYearService: AcademicYearService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadAcademicYears();
    this.loadHolidays();
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupSearch(): void {
    // Implement search with debounce
    const searchSubject = new Subject<string>();
    searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(searchTerm => {
      this.filter.name = searchTerm || undefined;
      this.filter.page = 1;
      this.loadHolidays();
    });

    // You would connect this to your search input in the template
  }

  private loadAcademicYears(): void {
    this.academicYearService.getAcademicYears({ page: 1, pageSize: 100, sortDescending: false })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            this.academicYears = response.data.items;
          }
        },
        error: (error) => {
          console.error('Error loading academic years:', error);
          this.snackBar.open('Error loading academic years', 'Close', { duration: 3000 });
        }
      });
  }

  loadHolidays(): void {
    this.loading = true;
    
    this.holidayService.getHolidays(this.filter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.success && response.data) {
            this.holidays = response.data.items;
            this.dataSource.data = this.holidays;
            this.totalCount = response.data.totalCount;
          }
        },
        error: (error) => {
          this.loading = false;
          console.error('Error loading holidays:', error);
          this.snackBar.open('Error loading holidays', 'Close', { duration: 3000 });
        }
      });
  }

  onPageChange(event: any): void {
    this.filter.page = event.pageIndex + 1;
    this.filter.pageSize = event.pageSize;
    this.loadHolidays();
  }

  onSortChange(event: any): void {
    this.filter.sortBy = event.active;
    this.filter.sortDirection = event.direction || 'asc';
    this.filter.page = 1;
    this.loadHolidays();
  }

  onFilterChange(): void {
    this.filter.page = 1;
    this.loadHolidays();
  }

  onAcademicYearChange(academicYearId: string): void {
    this.filter.academicYearId = academicYearId || undefined;
    this.filter.termId = undefined; // Reset term when academic year changes
    
    // Load terms for selected academic year
    if (academicYearId) {
      const selectedYear = this.academicYears.find(ay => ay.id === academicYearId);
      this.terms = selectedYear?.terms || [];
    } else {
      this.terms = [];
    }
    
    this.onFilterChange();
  }

  onTermChange(termId: string): void {
    this.filter.termId = termId || undefined;
    this.onFilterChange();
  }

  onSearch(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.filter.name = searchTerm || undefined;
    this.filter.page = 1;
    this.loadHolidays();
  }

  clearFilters(): void {
    this.filter = { ...DEFAULT_HOLIDAY_FILTER };
    this.searchTerm = '';
    this.terms = [];
    this.loadHolidays();
  }

  // Navigation
  createHoliday(): void {
    this.router.navigate(['/admin/holidays/create']);
  }

  editHoliday(holiday: Holiday): void {
    this.router.navigate(['/admin/holidays/edit', holiday.id]);
  }

  viewHoliday(holiday: Holiday): void {
    this.router.navigate(['/admin/holidays/view', holiday.id]);
  }

  // Actions
  toggleHolidayStatus(holiday: Holiday): void {
    const action = holiday.isActive ? 'deactivate' : 'activate';
    const service = holiday.isActive ? 
      this.holidayService.deactivateHoliday(holiday.id) : 
      this.holidayService.activateHoliday(holiday.id);

    service.pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success) {
            holiday.isActive = !holiday.isActive;
            this.snackBar.open(`Holiday ${action}d successfully`, 'Close', { duration: 3000 });
          }
        },
        error: (error) => {
          console.error(`Error ${action}ing holiday:`, error);
          this.snackBar.open(`Error ${action}ing holiday`, 'Close', { duration: 3000 });
        }
      });
  }

  deleteHoliday(holiday: Holiday): void {
    if (confirm(`Are you sure you want to delete "${holiday.name}"?`)) {
      this.holidayService.deleteHoliday(holiday.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.success) {
              this.loadHolidays();
              this.snackBar.open('Holiday deleted successfully', 'Close', { duration: 3000 });
            }
          },
          error: (error) => {
            console.error('Error deleting holiday:', error);
            this.snackBar.open('Error deleting holiday', 'Close', { duration: 3000 });
          }
        });
    }
  }

  // Utility methods
  getHolidayTypeLabel(type: HolidayType): string {
    return getHolidayTypeLabel(type);
  }

  getHolidayTypeColor(type: HolidayType): string {
    return getHolidayTypeColor(type);
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString();
  }

  getDuration(startDate: Date, endDate: Date): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  }

  isUpcoming(startDate: Date): boolean {
    const today = new Date();
    const start = new Date(startDate);
    return start > today;
  }

  isActive(startDate: Date, endDate: Date): boolean {
    const today = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);
    return start <= today && end >= today;
  }
}
