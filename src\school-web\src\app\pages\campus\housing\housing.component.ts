import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../../shared/components/default-hero/default-hero.component';

interface Residence {
  name: string;
  description: string;
  capacity: string;
  roomTypes: string[];
  amenities: string[];
  image: string;
  gradeLevel: string;
}

interface Staff {
  name: string;
  title: string;
  description: string;
  image: string;
}

@Component({
  selector: 'app-housing',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatExpansionModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './housing.component.html',
  styleUrls: ['./housing.component.scss']
})
export class HousingComponent {
  // Grade levels
  gradeLevels = [
    'Middle School',
    'High School'
  ];

  // Residence halls
  residences: Residence[] = [
    {
      name: 'Pioneer Hall',
      description: 'Our newest residence hall for middle school students features modern amenities, comfortable living spaces, and dedicated study areas designed to support the unique needs of younger boarding students.',
      capacity: '60 students',
      roomTypes: ['Double rooms', 'Triple rooms'],
      amenities: [
        'Common lounges on each floor',
        'Study rooms with computers',
        'Game room',
        'Kitchenette',
        'Laundry facilities',
        'Wi-Fi throughout the building',
        'Air conditioning'
      ],
      image: 'assets/images/campus/housing/pioneer-hall.jpg',
      gradeLevel: 'Middle School'
    },
    {
      name: 'Heritage House',
      description: 'This historic building has been renovated to provide a warm, home-like environment for our middle school students, with resident faculty apartments integrated into the living spaces to provide supervision and support.',
      capacity: '40 students',
      roomTypes: ['Double rooms'],
      amenities: [
        'Cozy common areas',
        'Quiet study spaces',
        'Reading nook',
        'Outdoor courtyard',
        'Laundry facilities',
        'Wi-Fi throughout the building'
      ],
      image: 'assets/images/campus/housing/heritage-house.jpg',
      gradeLevel: 'Middle School'
    },
    {
      name: 'Summit Hall',
      description: 'Our largest residence hall for high school students offers a variety of room configurations, spacious common areas, and amenities designed to foster community while providing appropriate independence for older students.',
      capacity: '120 students',
      roomTypes: ['Single rooms', 'Double rooms'],
      amenities: [
        'Large common lounges on each floor',
        'Dedicated study rooms',
        'Fitness room',
        'Media room',
        'Full kitchen',
        'Laundry facilities',
        'Wi-Fi throughout the building',
        'Air conditioning'
      ],
      image: 'assets/images/campus/housing/summit-hall.jpg',
      gradeLevel: 'High School'
    },
    {
      name: 'Lakeside Residence',
      description: 'Located near our campus lake, this residence hall for high school students offers beautiful views and a peaceful setting, with a focus on wellness and connection to nature.',
      capacity: '80 students',
      roomTypes: ['Single rooms', 'Double rooms'],
      amenities: [
        'Panoramic lake views',
        'Outdoor terrace',
        'Meditation room',
        'Study lounges',
        'Kitchenette',
        'Laundry facilities',
        'Wi-Fi throughout the building',
        'Air conditioning'
      ],
      image: 'assets/images/campus/housing/lakeside-residence.jpg',
      gradeLevel: 'High School'
    },
    {
      name: 'Global Village',
      description: 'This unique residence for high school students is designed to foster international understanding, with themed floors representing different regions of the world and programming that celebrates cultural diversity.',
      capacity: '60 students',
      roomTypes: ['Single rooms', 'Double rooms'],
      amenities: [
        'Culturally themed common spaces',
        'International kitchen',
        'Language learning lab',
        'Study areas',
        'Cultural event space',
        'Laundry facilities',
        'Wi-Fi throughout the building',
        'Air conditioning'
      ],
      image: 'assets/images/campus/housing/global-village.jpg',
      gradeLevel: 'High School'
    }
  ];

  // Residential life staff
  residentialStaff: Staff[] = [
    {
      name: 'Dr. Robert Chen',
      title: 'Director of Residential Life',
      description: 'Dr. Chen oversees all aspects of our residential program, bringing 15 years of experience in boarding school administration. He is committed to creating a supportive, engaging residential community that complements our academic program.',
      image: 'assets/images/campus/housing/director.jpg'
    },
    {
      name: 'Ms. Sarah Johnson',
      title: 'Middle School Residential Coordinator',
      description: 'Ms. Johnson coordinates programming and support for our middle school boarding students, with a focus on age-appropriate activities, social-emotional development, and the transition to boarding school life.',
      image: 'assets/images/campus/housing/ms-coordinator.jpg'
    },
    {
      name: 'Mr. David Williams',
      title: 'High School Residential Coordinator',
      description: 'Mr. Williams works with our high school boarding students, balancing supervision with opportunities for increasing independence and leadership as students prepare for college and beyond.',
      image: 'assets/images/campus/housing/hs-coordinator.jpg'
    },
    {
      name: 'Mrs. Maria Rodriguez',
      title: 'International Student Coordinator',
      description: 'Mrs. Rodriguez provides specialized support for our international boarding students, helping them navigate cultural transitions, language development, and integration into school life.',
      image: 'assets/images/campus/housing/international-coordinator.jpg'
    }
  ];

  // Residential life programs
  residentialPrograms = [
    {
      title: 'Academic Support',
      description: 'Our structured study hours and tutoring programs ensure that boarding students have the support they need to excel academically. Resident faculty and peer tutors are available to assist with homework, projects, and exam preparation.',
      icon: 'school'
    },
    {
      title: 'Weekend Activities',
      description: 'From on-campus events to off-campus excursions, our weekend activity program offers a variety of social, recreational, and cultural experiences that build community and expose students to new interests and opportunities.',
      icon: 'weekend'
    },
    {
      title: 'Leadership Development',
      description: 'Through our Residential Student Council, peer mentoring program, and dorm leadership positions, boarding students have numerous opportunities to develop and practice leadership skills within the residential community.',
      icon: 'groups'
    },
    {
      title: 'Health & Wellness',
      description: 'Our residential wellness program addresses all dimensions of student well-being, with programming focused on physical health, emotional resilience, stress management, healthy relationships, and balanced living.',
      icon: 'favorite'
    },
    {
      title: 'Cultural Exchange',
      description: 'With boarding students from around the world, our residential program emphasizes cross-cultural understanding through international celebrations, cultural sharing events, and dialogue about global perspectives.',
      icon: 'public'
    },
    {
      title: 'Life Skills Development',
      description: 'Our residential curriculum includes workshops and practical experiences that help students develop essential life skills such as time management, financial literacy, conflict resolution, and personal organization.',
      icon: 'psychology'
    }
  ];

  // FAQs
  faqs = [
    {
      question: 'What is the application process for boarding students?',
      answer: 'Boarding applicants follow the same initial application process as day students, with additional steps including a boarding student questionnaire, interview with residential life staff, and housing preference form. Early application is recommended as boarding spaces are limited. Please contact our Admissions Office for specific deadlines and requirements.'
    },
    {
      question: 'What supervision is provided in the residence halls?',
      answer: 'Each residence hall has resident faculty members who live in apartments within the building, providing 24/7 supervision and support. Additionally, we maintain appropriate student-to-staff ratios based on age group, with more intensive supervision for middle school students and a graduated approach to independence for high school students.'
    },
    {
      question: 'How are roommates assigned?',
      answer: 'New students complete a detailed roommate questionnaire covering sleep habits, study preferences, hobbies, and other factors that impact compatibility. Our residential life team carefully matches roommates based on these factors, with the goal of creating positive living situations that may also expand students\' horizons. Returning students have the opportunity to request specific roommates during the room selection process.'
    },
    {
      question: 'What items should boarding students bring?',
      answer: 'We provide a detailed packing list to all boarding families before the start of school. Generally, students should bring bedding, towels, toiletries, clothing, school supplies, and personal items. Some furniture (like desk lamps or small refrigerators) may be permitted depending on the residence hall. Storage space is limited, so we recommend bringing only essential items.'
    },
    {
      question: 'How do boarding students access medical care?',
      answer: 'Our Health Center is staffed during school hours, and we have protocols for after-hours medical needs. Resident staff are trained in first aid and can assess situations requiring medical attention. For routine medical appointments, we provide transportation to local healthcare providers, and we coordinate with families regarding any ongoing medical needs.'
    },
    {
      question: 'What is the policy on technology use in residence halls?',
      answer: 'We believe in teaching responsible technology use rather than imposing excessive restrictions. However, we do have age-appropriate guidelines, including device-free hours during study time and before bedtime for middle school students. Wi-Fi is available throughout all residence halls, and we have computer labs for students who need access to specialized software or hardware.'
    },
    {
      question: 'Are boarding students allowed to leave campus?',
      answer: 'Boarding students may leave campus with appropriate permissions based on their age, destination, and school standing. Middle school students require staff supervision for off-campus activities, while high school students have increasing privileges based on grade level and demonstrated responsibility. All students must follow sign-out procedures and adhere to curfew times.'
    }
  ];

  // Filter residences by grade level
  getResidencesByGradeLevel(gradeLevel: string): Residence[] {
    return this.residences.filter(residence => residence.gradeLevel === gradeLevel);
  }
}
