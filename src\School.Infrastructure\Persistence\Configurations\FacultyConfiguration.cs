using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class FacultyConfiguration : IEntityTypeConfiguration<Faculty>
{
    public void Configure(EntityTypeBuilder<Faculty> builder)
    {
        builder.Property(t => t.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(t => t.Title)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(t => t.Email)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(t => t.Phone)
            .HasMaxLength(50);

        builder.Property(t => t.Office)
            .HasMaxLength(100);

        builder.Property(t => t.ShortBio)
            .HasMaxLength(500);

        builder.Property(t => t.Department)
            .HasMaxLength(100);

        builder.Property(t => t.Website)
            .HasMaxLength(255);

        builder.Property(t => t.LinkedIn)
            .HasMaxLength(255);

        builder.Property(t => t.Twitter)
            .HasMaxLength(255);

        builder.Property(t => t.ResearchGate)
            .HasMaxLength(255);

        builder.HasOne(t => t.ProfileImage)
            .WithMany()
            .HasForeignKey(t => t.ProfileImageId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasQueryFilter(f => !f.IsDeleted);
    }
}
