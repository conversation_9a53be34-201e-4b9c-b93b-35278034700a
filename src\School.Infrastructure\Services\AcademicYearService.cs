using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.AcademicYear;
using School.Domain.Entities;
using School.Domain.Enums;

namespace School.Infrastructure.Services;

public class AcademicYearService : IAcademicYearService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<AcademicYearService> _logger;

    public AcademicYearService(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        ILogger<AcademicYearService> logger)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<(IEnumerable<AcademicYearDto> AcademicYears, int TotalCount)> GetAllAcademicYearsAsync(AcademicYearFilterDto filter)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
        var query = repository.AsQueryable("Terms,Translations");

        // Apply filters
        if (!string.IsNullOrEmpty(filter.Name))
        {
            query = query.Where(ay => ay.Name.Contains(filter.Name));
        }

        if (!string.IsNullOrEmpty(filter.Code))
        {
            query = query.Where(ay => ay.Code.Contains(filter.Code));
        }

        if (filter.Status.HasValue)
        {
            query = query.Where(ay => ay.Status == filter.Status.Value);
        }

        if (filter.IsCurrentYear.HasValue)
        {
            query = query.Where(ay => ay.IsCurrentYear == filter.IsCurrentYear.Value);
        }

        if (filter.StartDateFrom.HasValue)
        {
            query = query.Where(ay => ay.StartDate >= filter.StartDateFrom.Value);
        }

        if (filter.StartDateTo.HasValue)
        {
            query = query.Where(ay => ay.StartDate <= filter.StartDateTo.Value);
        }

        if (filter.EndDateFrom.HasValue)
        {
            query = query.Where(ay => ay.EndDate >= filter.EndDateFrom.Value);
        }

        if (filter.EndDateTo.HasValue)
        {
            query = query.Where(ay => ay.EndDate <= filter.EndDateTo.Value);
        }

        // Apply sorting
        if (!string.IsNullOrEmpty(filter.SortBy))
        {
            switch (filter.SortBy.ToLower())
            {
                case "name":
                    query = filter.SortDescending ? query.OrderByDescending(ay => ay.Name) : query.OrderBy(ay => ay.Name);
                    break;
                case "startdate":
                    query = filter.SortDescending ? query.OrderByDescending(ay => ay.StartDate) : query.OrderBy(ay => ay.StartDate);
                    break;
                case "enddate":
                    query = filter.SortDescending ? query.OrderByDescending(ay => ay.EndDate) : query.OrderBy(ay => ay.EndDate);
                    break;
                case "status":
                    query = filter.SortDescending ? query.OrderByDescending(ay => ay.Status) : query.OrderBy(ay => ay.Status);
                    break;
                default:
                    query = query.OrderByDescending(ay => ay.CreatedAt);
                    break;
            }
        }
        else
        {
            query = query.OrderByDescending(ay => ay.CreatedAt);
        }

        var totalCount = await query.CountAsync();

        // Apply pagination
        var academicYears = await query
            .Skip((filter.Page - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .Select(ay => new AcademicYearDto
            {
                Id = ay.Id,
                Name = ay.Name,
                DisplayName = ay.DisplayName,
                Code = ay.Code,
                StartDate = ay.StartDate,
                EndDate = ay.EndDate,
                Status = ay.Status,
                IsCurrentYear = ay.IsCurrentYear,
                Description = ay.Description,
                Remarks = ay.Remarks,
                TotalWorkingDays = ay.TotalWorkingDays,
                TotalHolidays = ay.TotalHolidays,
                RegistrationStartDate = ay.RegistrationStartDate,
                RegistrationEndDate = ay.RegistrationEndDate,
                AdmissionStartDate = ay.AdmissionStartDate,
                AdmissionEndDate = ay.AdmissionEndDate,
                CreatedAt = ay.CreatedAt,
                LastModifiedAt = ay.LastModifiedAt,
                Terms = ay.Terms.Select(t => new TermDto
                {
                    Id = t.Id,
                    AcademicYearId = t.AcademicYearId,
                    Name = t.Name,
                    Code = t.Code,
                    Type = t.Type,
                    Status = t.Status,
                    StartDate = t.StartDate,
                    EndDate = t.EndDate,
                    OrderIndex = t.OrderIndex
                }).ToList(),
                Translations = ay.Translations.Select(tr => new AcademicYearTranslationDto
                {
                    Id = tr.Id,
                    AcademicYearId = tr.AcademicYearId,
                    LanguageCode = tr.LanguageCode,
                    DisplayName = tr.DisplayName,
                    Description = tr.Description,
                    Remarks = tr.Remarks
                }).ToList()
            })
            .ToListAsync();

        return (academicYears, totalCount);
    }

    public async Task<AcademicYearDto?> GetAcademicYearByIdAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
        var academicYear = await repository.AsQueryable("Terms,Translations")
            .FirstOrDefaultAsync(ay => ay.Id == id);

        if (academicYear == null)
            return null;

        return new AcademicYearDto
        {
            Id = academicYear.Id,
            Name = academicYear.Name,
            DisplayName = academicYear.DisplayName,
            Code = academicYear.Code,
            StartDate = academicYear.StartDate,
            EndDate = academicYear.EndDate,
            Status = academicYear.Status,
            IsCurrentYear = academicYear.IsCurrentYear,
            Description = academicYear.Description,
            Remarks = academicYear.Remarks,
            TotalWorkingDays = academicYear.TotalWorkingDays,
            TotalHolidays = academicYear.TotalHolidays,
            RegistrationStartDate = academicYear.RegistrationStartDate,
            RegistrationEndDate = academicYear.RegistrationEndDate,
            AdmissionStartDate = academicYear.AdmissionStartDate,
            AdmissionEndDate = academicYear.AdmissionEndDate,
            CreatedAt = academicYear.CreatedAt,
            LastModifiedAt = academicYear.LastModifiedAt,
            Terms = academicYear.Terms.OrderBy(t => t.OrderIndex).Select(t => new TermDto
            {
                Id = t.Id,
                AcademicYearId = t.AcademicYearId,
                Name = t.Name,
                Code = t.Code,
                Type = t.Type,
                Status = t.Status,
                StartDate = t.StartDate,
                EndDate = t.EndDate,
                OrderIndex = t.OrderIndex,
                Description = t.Description,
                Remarks = t.Remarks,
                TotalWorkingDays = t.TotalWorkingDays,
                TotalHolidays = t.TotalHolidays,
                ExamStartDate = t.ExamStartDate,
                ExamEndDate = t.ExamEndDate,
                ResultPublishDate = t.ResultPublishDate,
                RegistrationDeadline = t.RegistrationDeadline,
                FeePaymentDeadline = t.FeePaymentDeadline,
                PassingGrade = t.PassingGrade,
                MaximumGrade = t.MaximumGrade,
                GradingScale = t.GradingScale,
                CreatedAt = t.CreatedAt,
                LastModifiedAt = t.LastModifiedAt
            }).ToList(),
            Translations = academicYear.Translations.Select(tr => new AcademicYearTranslationDto
            {
                Id = tr.Id,
                AcademicYearId = tr.AcademicYearId,
                LanguageCode = tr.LanguageCode,
                DisplayName = tr.DisplayName,
                Description = tr.Description,
                Remarks = tr.Remarks
            }).ToList()
        };
    }

    public async Task<AcademicYearDto?> GetCurrentAcademicYearAsync()
    {
        var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
        var currentAcademicYear = await repository.AsQueryable("Terms,Translations")
            .FirstOrDefaultAsync(ay => ay.IsCurrentYear);

        if (currentAcademicYear == null)
            return null;

        return await GetAcademicYearByIdAsync(currentAcademicYear.Id);
    }

    public async Task<Guid> CreateAcademicYearAsync(CreateAcademicYearDto academicYearDto)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();

        // Validate dates
        if (!await ValidateAcademicYearDatesAsync(academicYearDto.StartDate, academicYearDto.EndDate))
        {
            throw new InvalidOperationException("Academic year dates overlap with existing academic year.");
        }

        // If this is set as current year, unset other current years
        if (academicYearDto.IsCurrentYear)
        {
            await UnsetCurrentAcademicYearsAsync();
        }

        var academicYear = new Domain.Entities.AcademicYear
        {
            Name = academicYearDto.Name,
            DisplayName = academicYearDto.DisplayName,
            Code = academicYearDto.Code,
            StartDate = academicYearDto.StartDate,
            EndDate = academicYearDto.EndDate,
            Status = academicYearDto.Status,
            IsCurrentYear = academicYearDto.IsCurrentYear,
            Description = academicYearDto.Description,
            Remarks = academicYearDto.Remarks,
            TotalWorkingDays = academicYearDto.TotalWorkingDays,
            TotalHolidays = academicYearDto.TotalHolidays,
            RegistrationStartDate = academicYearDto.RegistrationStartDate,
            RegistrationEndDate = academicYearDto.RegistrationEndDate,
            AdmissionStartDate = academicYearDto.AdmissionStartDate,
            AdmissionEndDate = academicYearDto.AdmissionEndDate,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _currentUserService.UserId.ToString()
        };

        await repository.AddAsync(academicYear);
        await _unitOfWork.SaveChangesAsync();

        // Add translations if provided
        if (academicYearDto.Translations?.Any() == true)
        {
            foreach (var translation in academicYearDto.Translations)
            {
                await AddTranslationAsync(academicYear.Id, translation);
            }
        }

        // Add terms if provided
        if (academicYearDto.Terms?.Any() == true)
        {
            var termRepository = _unitOfWork.Repository<Domain.Entities.Term>();
            foreach (var termDto in academicYearDto.Terms)
            {
                var term = new Domain.Entities.Term
                {
                    AcademicYearId = academicYear.Id,
                    Name = termDto.Name,
                    Code = termDto.Code,
                    Type = termDto.Type,
                    Status = termDto.Status,
                    StartDate = termDto.StartDate,
                    EndDate = termDto.EndDate,
                    OrderIndex = termDto.OrderIndex,
                    Description = termDto.Description,
                    Remarks = termDto.Remarks,
                    TotalWorkingDays = termDto.TotalWorkingDays,
                    TotalHolidays = termDto.TotalHolidays,
                    ExamStartDate = termDto.ExamStartDate,
                    ExamEndDate = termDto.ExamEndDate,
                    ResultPublishDate = termDto.ResultPublishDate,
                    RegistrationDeadline = termDto.RegistrationDeadline,
                    FeePaymentDeadline = termDto.FeePaymentDeadline,
                    PassingGrade = termDto.PassingGrade,
                    MaximumGrade = termDto.MaximumGrade,
                    GradingScale = termDto.GradingScale,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = _currentUserService.UserId.ToString()
                };

                await termRepository.AddAsync(term);
            }
            await _unitOfWork.SaveChangesAsync();
        }

        _logger.LogInformation("Academic year created with ID {AcademicYearId}", academicYear.Id);
        return academicYear.Id;
    }

    private async Task UnsetCurrentAcademicYearsAsync()
    {
        var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
        var currentYears = await repository.AsQueryable()
            .Where(ay => ay.IsCurrentYear)
            .ToListAsync();

        foreach (var year in currentYears)
        {
            year.IsCurrentYear = false;
            year.LastModifiedAt = DateTime.UtcNow;
            year.LastModifiedBy = _currentUserService.UserId.ToString();
            await repository.UpdateAsync(year);
        }

        if (currentYears.Any())
        {
            await _unitOfWork.SaveChangesAsync();
        }
    }

    public async Task<bool> UpdateAcademicYearAsync(Guid id, UpdateAcademicYearDto academicYearDto)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
        var academicYear = await repository.GetByIdAsync(id);

        if (academicYear == null)
            return false;

        // Validate dates if changed
        if (academicYear.StartDate != academicYearDto.StartDate || academicYear.EndDate != academicYearDto.EndDate)
        {
            if (!await ValidateAcademicYearDatesAsync(academicYearDto.StartDate, academicYearDto.EndDate, id))
            {
                throw new InvalidOperationException("Academic year dates overlap with existing academic year.");
            }
        }

        // If this is set as current year, unset other current years
        if (academicYearDto.IsCurrentYear && !academicYear.IsCurrentYear)
        {
            await UnsetCurrentAcademicYearsAsync();
        }

        academicYear.Name = academicYearDto.Name;
        academicYear.DisplayName = academicYearDto.DisplayName;
        academicYear.Code = academicYearDto.Code;
        academicYear.StartDate = academicYearDto.StartDate;
        academicYear.EndDate = academicYearDto.EndDate;
        academicYear.Status = academicYearDto.Status;
        academicYear.IsCurrentYear = academicYearDto.IsCurrentYear;
        academicYear.Description = academicYearDto.Description;
        academicYear.Remarks = academicYearDto.Remarks;
        academicYear.TotalWorkingDays = academicYearDto.TotalWorkingDays;
        academicYear.TotalHolidays = academicYearDto.TotalHolidays;
        academicYear.RegistrationStartDate = academicYearDto.RegistrationStartDate;
        academicYear.RegistrationEndDate = academicYearDto.RegistrationEndDate;
        academicYear.AdmissionStartDate = academicYearDto.AdmissionStartDate;
        academicYear.AdmissionEndDate = academicYearDto.AdmissionEndDate;
        academicYear.LastModifiedAt = DateTime.UtcNow;
        academicYear.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(academicYear);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Academic year updated with ID {AcademicYearId}", academicYear.Id);
        return true;
    }

    public async Task<bool> DeleteAcademicYearAsync(Guid id)
    {
        if (!await CanDeleteAcademicYearAsync(id))
        {
            return false;
        }

        var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
        await repository.DeleteByIdAsync(id);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Academic year deleted with ID {AcademicYearId}", id);
        return true;
    }

    public async Task<bool> SetCurrentAcademicYearAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
        var academicYear = await repository.GetByIdAsync(id);

        if (academicYear == null)
            return false;

        await UnsetCurrentAcademicYearsAsync();

        academicYear.IsCurrentYear = true;
        academicYear.LastModifiedAt = DateTime.UtcNow;
        academicYear.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(academicYear);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Academic year set as current with ID {AcademicYearId}", id);
        return true;
    }

    public async Task<bool> ActivateAcademicYearAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
        var academicYear = await repository.GetByIdAsync(id);

        if (academicYear == null || academicYear.Status != AcademicYearStatus.Draft)
            return false;

        academicYear.Status = AcademicYearStatus.Active;
        academicYear.LastModifiedAt = DateTime.UtcNow;
        academicYear.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(academicYear);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Academic year activated with ID {AcademicYearId}", id);
        return true;
    }

    public async Task<bool> CompleteAcademicYearAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
        var academicYear = await repository.GetByIdAsync(id);

        if (academicYear == null || academicYear.Status != AcademicYearStatus.Active)
            return false;

        academicYear.Status = AcademicYearStatus.Completed;
        academicYear.IsCurrentYear = false;
        academicYear.LastModifiedAt = DateTime.UtcNow;
        academicYear.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(academicYear);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Academic year completed with ID {AcademicYearId}", id);
        return true;
    }

    public async Task<bool> ArchiveAcademicYearAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
        var academicYear = await repository.GetByIdAsync(id);

        if (academicYear == null || academicYear.Status != AcademicYearStatus.Completed)
            return false;

        academicYear.Status = AcademicYearStatus.Archived;
        academicYear.LastModifiedAt = DateTime.UtcNow;
        academicYear.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(academicYear);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Academic year archived with ID {AcademicYearId}", id);
        return true;
    }

    public async Task<bool> ValidateAcademicYearDatesAsync(DateTime startDate, DateTime endDate, Guid? excludeId = null)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
        var query = repository.AsQueryable();

        if (excludeId.HasValue)
        {
            query = query.Where(ay => ay.Id != excludeId.Value);
        }

        var overlapping = await query
            .Where(ay => (startDate >= ay.StartDate && startDate <= ay.EndDate) ||
                        (endDate >= ay.StartDate && endDate <= ay.EndDate) ||
                        (startDate <= ay.StartDate && endDate >= ay.EndDate))
            .AnyAsync();

        return !overlapping;
    }

    public async Task<bool> CanDeleteAcademicYearAsync(Guid id)
    {
        // Check if there are any students enrolled in this academic year
        var studentHistoryRepository = _unitOfWork.Repository<StudentAcademicHistory>();
        var hasStudents = await studentHistoryRepository.AsQueryable()
            .AnyAsync(sh => sh.AcademicYearId == id);

        return !hasStudents;
    }

    public async Task<bool> AddTranslationAsync(Guid academicYearId, CreateAcademicYearTranslationDto translationDto)
    {
        var repository = _unitOfWork.Repository<AcademicYearTranslation>();

        // Check if translation already exists
        var existingTranslation = await repository.AsQueryable()
            .FirstOrDefaultAsync(t => t.AcademicYearId == academicYearId && t.LanguageCode == translationDto.LanguageCode);

        if (existingTranslation != null)
            return false;

        var translation = new AcademicYearTranslation
        {
            AcademicYearId = academicYearId,
            LanguageCode = translationDto.LanguageCode,
            DisplayName = translationDto.DisplayName,
            Description = translationDto.Description,
            Remarks = translationDto.Remarks,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _currentUserService.UserId.ToString()
        };

        await repository.AddAsync(translation);
        await _unitOfWork.SaveChangesAsync();

        return true;
    }

    public async Task<bool> UpdateTranslationAsync(Guid academicYearId, string languageCode, UpdateAcademicYearTranslationDto translationDto)
    {
        var repository = _unitOfWork.Repository<AcademicYearTranslation>();
        var translation = await repository.AsQueryable()
            .FirstOrDefaultAsync(t => t.AcademicYearId == academicYearId && t.LanguageCode == languageCode);

        if (translation == null)
            return false;

        translation.DisplayName = translationDto.DisplayName;
        translation.Description = translationDto.Description;
        translation.Remarks = translationDto.Remarks;
        translation.LastModifiedAt = DateTime.UtcNow;
        translation.LastModifiedBy = _currentUserService.UserId.ToString();

        await repository.UpdateAsync(translation);
        await _unitOfWork.SaveChangesAsync();

        return true;
    }

    public async Task<bool> DeleteTranslationAsync(Guid academicYearId, string languageCode)
    {
        var repository = _unitOfWork.Repository<AcademicYearTranslation>();
        var translation = await repository.AsQueryable()
            .FirstOrDefaultAsync(t => t.AcademicYearId == academicYearId && t.LanguageCode == languageCode);

        if (translation == null)
            return false;

        await repository.DeleteAsync(translation);
        await _unitOfWork.SaveChangesAsync();

        return true;
    }

    public async Task<IEnumerable<AcademicYearTranslationDto>> GetTranslationsAsync(Guid academicYearId)
    {
        var repository = _unitOfWork.Repository<AcademicYearTranslation>();
        var translations = await repository.AsQueryable()
            .Where(t => t.AcademicYearId == academicYearId)
            .Select(t => new AcademicYearTranslationDto
            {
                Id = t.Id,
                AcademicYearId = t.AcademicYearId,
                LanguageCode = t.LanguageCode,
                DisplayName = t.DisplayName,
                Description = t.Description,
                Remarks = t.Remarks
            })
            .ToListAsync();

        return translations;
    }

    public async Task<int> GetTotalStudentsInAcademicYearAsync(Guid academicYearId)
    {
        var repository = _unitOfWork.Repository<StudentAcademicHistory>();
        return await repository.AsQueryable()
            .Where(sh => sh.AcademicYearId == academicYearId)
            .Select(sh => sh.StudentId)
            .Distinct()
            .CountAsync();
    }

    public async Task<int> GetTotalTermsInAcademicYearAsync(Guid academicYearId)
    {
        var repository = _unitOfWork.Repository<Domain.Entities.Term>();
        return await repository.AsQueryable()
            .Where(t => t.AcademicYearId == academicYearId)
            .CountAsync();
    }

    public async Task<Dictionary<string, int>> GetAcademicYearStatisticsAsync(Guid academicYearId)
    {
        var stats = new Dictionary<string, int>();

        stats["TotalStudents"] = await GetTotalStudentsInAcademicYearAsync(academicYearId);
        stats["TotalTerms"] = await GetTotalTermsInAcademicYearAsync(academicYearId);

        // Add more statistics as needed
        var calendarRepository = _unitOfWork.Repository<AcademicCalendar>();
        stats["TotalEvents"] = await calendarRepository.AsQueryable()
            .Where(ac => ac.AcademicYearId == academicYearId)
            .CountAsync();

        return stats;
    }
}
