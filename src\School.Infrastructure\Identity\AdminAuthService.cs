using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Auth;
using School.Domain.Entities;
using School.Domain.Enums;
using School.Infrastructure.Persistence;

namespace School.Infrastructure.Identity;

/// <summary>
/// Admin authentication service that works without tenant context
/// </summary>
public class AdminAuthService : IAdminAuthService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly ITokenGenerator _tokenGenerator;
    private readonly IMfaService _mfaService;
    private readonly IApplicationDbContext _context;
    private readonly ILogger<AdminAuthService> _logger;

    public AdminAuthService(
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        ITokenGenerator tokenGenerator,
        IMfaService mfaService,
        IApplicationDbContext context,
        ILogger<AdminAuthService> logger)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _tokenGenerator = tokenGenerator;
        _mfaService = mfaService;
        _context = context;
        _logger = logger;
    }

    public async Task<AdminLoginResponseDto?> AdminLoginAsync(AdminLoginDto loginDto)
    {
        // Execute without tenant filtering for admin login
        if (_context is ApplicationDbContext dbContext)
        {
            return await dbContext.ExecuteWithoutTenantFilterAsync(async () =>
            {
                return await PerformAdminLoginAsync(loginDto);
            });
        }

        // Fallback if not ApplicationDbContext
        return await PerformAdminLoginAsync(loginDto);
    }

    private async Task<AdminLoginResponseDto?> PerformAdminLoginAsync(AdminLoginDto loginDto)
    {
            var user = await _userManager.FindByNameAsync(loginDto.Username);

            if (user == null || user.IsDeleted || !user.IsActive)
            {
                _logger.LogWarning("Admin login failed: User {Username} not found or inactive", loginDto.Username);
                return null;
            }

            // Check if user has admin privileges (SystemAdmin or TenantAdmin)
            var userRoles = await _userManager.GetRolesAsync(user);
            if (!userRoles.Any(role => role.Equals("SystemAdmin", StringComparison.OrdinalIgnoreCase) ||
                                      role.Equals("Admin", StringComparison.OrdinalIgnoreCase)))
            {
                _logger.LogWarning("Admin login failed: User {Username} does not have admin privileges", loginDto.Username);
                return null;
            }

            // Check if account is locked
            if (user.AccountLockedUntil.HasValue && user.AccountLockedUntil > DateTime.UtcNow)
            {
                _logger.LogWarning("Admin login failed: Account {UserId} is locked until {LockUntil}", user.Id, user.AccountLockedUntil);
                return null;
            }

            var result = await _signInManager.CheckPasswordSignInAsync(user, loginDto.Password, false);

            if (!result.Succeeded)
            {
                await HandleFailedAdminLogin(user);
                _logger.LogWarning("Admin login failed: Invalid password for user {Username}", loginDto.Username);
                return null;
            }

            // Reset failed login attempts on successful password verification
            if (user.FailedLoginAttempts > 0)
            {
                user.FailedLoginAttempts = 0;
                user.LastFailedLogin = null;
                await _userManager.UpdateAsync(user);
            }

            // Check if MFA is required
            if (user.IsMfaEnabled)
            {
                // If MFA code is provided, verify it
                if (!string.IsNullOrEmpty(loginDto.MfaCode))
                {
                    var mfaValid = await _mfaService.VerifyMfaCodeAsync(user.Id, loginDto.MfaCode) ||
                                   await _mfaService.VerifyBackupCodeAsync(user.Id, loginDto.MfaCode);

                    if (!mfaValid)
                    {
                        _logger.LogWarning("Admin login failed: Invalid MFA code for user {Username}", loginDto.Username);
                        return null;
                    }
                }
                else
                {
                    // Return response indicating MFA is required
                    var mfaToken = _tokenGenerator.GenerateMfaToken(user.Id);

                    return new AdminLoginResponseDto
                    {
                        Token = string.Empty,
                        Expiration = DateTime.UtcNow,
                        RequiresMfa = true,
                        MfaToken = mfaToken,
                        User = CreateAdminUserDto(user),
                        UserRoles = [.. userRoles],
                        AccessibleTenants = [] // Will be populated after MFA
                    };
                }
            }

            // Generate admin tokens (with special admin claims)
            var (token, expiration) = await _tokenGenerator.GenerateAdminTokenAsync(user, userRoles);
            var refreshToken = _tokenGenerator.GenerateRefreshToken();
            var refreshTokenExpiration = DateTime.UtcNow.AddDays(7);

            // Get accessible tenants for admin
            var accessibleTenants = await GetAccessibleTenantsForAdmin(user.Id, userRoles);

            // Update last login time
            user.LastLogin = DateTime.UtcNow;
            await _userManager.UpdateAsync(user);

            _logger.LogInformation("Admin user {Username} logged in successfully", user.UserName);

            return new AdminLoginResponseDto
            {
                Token = token,
                RefreshToken = refreshToken,
                Expiration = expiration,
                RefreshTokenExpiration = refreshTokenExpiration,
                RequiresMfa = false,
                User = CreateAdminUserDto(user),
                UserRoles = [.. userRoles],
                AccessibleTenants = accessibleTenants
            };
    }

    public async Task<List<TenantAccessDto>> GetAccessibleTenantsAsync(string userId)
    {
        if (_context is ApplicationDbContext dbContext)
        {
            return await dbContext.ExecuteWithoutTenantFilterAsync(async () =>
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null) return [];

                var userRoles = await _userManager.GetRolesAsync(user);
                return await GetAccessibleTenantsForAdmin(userId, userRoles);
            });
        }

        // Fallback if not ApplicationDbContext
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null) return [];

        var userRoles = await _userManager.GetRolesAsync(user);
        return await GetAccessibleTenantsForAdmin(userId, userRoles);
    }

    private async Task<List<TenantAccessDto>> GetAccessibleTenantsForAdmin(string userId, IList<string> userRoles)
    {
        // System admins can access all tenants for system management
        if (userRoles.Any(role => role.Equals("SystemAdmin", StringComparison.OrdinalIgnoreCase)))
        {
            var allOrganizations = await _context.Organizations
                .Where(o => o.IsActive)
                .Select(o => new TenantAccessDto
                {
                    TenantId = o.Id,
                    TenantName = o.Name,
                    TenantSlug = o.Slug,
                    Role = "SystemAdmin",
                    CanManage = true
                })
                .ToListAsync();

            return allOrganizations;
        }

        // Tenant admins can access tenants they're explicitly assigned to
        var organizationUsers = await _context.OrganizationUsers
            .Where(ou => ou.UserId == userId && ou.IsActive)
            .Include(ou => ou.Organization)
            .Where(ou => ou.Organization.IsActive)
            .Select(ou => new TenantAccessDto
            {
                TenantId = ou.OrganizationId,
                TenantName = ou.Organization.Name,
                TenantSlug = ou.Organization.Slug,
                Role = "TenantAdmin",
                CanManage = ou.Role == OrganizationRole.Administrator
            })
            .ToListAsync();

        return organizationUsers;
    }

    private async Task HandleFailedAdminLogin(ApplicationUser user)
    {
        user.FailedLoginAttempts++;
        user.LastFailedLogin = DateTime.UtcNow;

        // Lock account after 5 failed attempts for 30 minutes
        if (user.FailedLoginAttempts >= 5)
        {
            user.AccountLockedUntil = DateTime.UtcNow.AddMinutes(30);
            _logger.LogWarning("Admin account {UserId} locked due to too many failed login attempts", user.Id);
        }

        await _userManager.UpdateAsync(user);
    }

    private static UserDto CreateAdminUserDto(ApplicationUser user)
    {
        return new UserDto
        {
            Id = Guid.TryParse(user.Id, out var userId) ? userId : Guid.NewGuid(),
            Username = user.UserName ?? string.Empty,
            Email = user.Email ?? string.Empty,
            FirstName = user.FirstName,
            LastName = user.LastName,
            IsActive = user.IsActive,
            LastLogin = user.LastLogin
        };
    }
}
