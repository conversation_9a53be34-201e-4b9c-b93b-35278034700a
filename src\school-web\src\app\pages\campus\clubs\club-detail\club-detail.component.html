<!-- Loading State -->
<div class="loading-container" *ngIf="loading">
  <mat-spinner></mat-spinner>
  <p>{{ 'CLUBS.LOADING_CLUB' | translate }}</p>
</div>

<!-- Error State -->
<div class="error-container" *ngIf="error">
  <mat-icon class="error-icon">error_outline</mat-icon>
  <h2>{{ 'CLUBS.CLUB_NOT_FOUND' | translate }}</h2>
  <p>{{ 'CLUBS.CLUB_NOT_FOUND_MESSAGE' | translate }}</p>
  <div class="error-actions">
    <button mat-raised-button color="primary" (click)="retryLoading()">{{ 'COMMON.RETRY' | translate }}</button>
    <button mat-stroked-button (click)="goBack()">{{ 'CLUBS.BACK_TO_CLUBS' | translate }}</button>
  </div>
</div>

<!-- Club Detail Content -->
<div class="club-detail-container" *ngIf="!loading && !error && club">
  <!-- Club Header -->
  <section class="club-header">
    <div class="container">
      <button mat-button class="back-button" (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> {{ 'CLUBS.BACK_TO_CLUBS' | translate }}
      </button>

      <div class="club-header-content">
        <div class="club-image">
          <img [src]="club.profileImageUrl || club.image" [alt]="club.name">
          <div class="club-category">{{club.category}}</div>
        </div>

        <div class="club-info">
          <h1 class="club-name">{{club.name}}</h1>
          <div class="club-meta">
            <div class="meta-item" *ngIf="club.meetingSchedule">
              <mat-icon>schedule</mat-icon>
              <span>{{club.meetingSchedule}}</span>
            </div>
            <div class="meta-item" *ngIf="club.location">
              <mat-icon>location_on</mat-icon>
              <span>{{club.location}}</span>
            </div>
            <div class="meta-item" *ngIf="club.advisor || (club.advisors && club.advisors.length > 0)">
              <mat-icon>person</mat-icon>
              <span>{{ 'CLUBS.ADVISORS' | translate }}:
                <ng-container *ngIf="club.advisor">
                  {{club.advisor}}
                </ng-container>
                <ng-container *ngIf="!club.advisor && club.advisors && club.advisors.length > 0">
                  <ng-container *ngIf="isAdvisorArray(club.advisors)">
                    {{getAdvisorNames(club.advisors)}}
                  </ng-container>
                </ng-container>
              </span>
            </div>
          </div>

          <div class="club-social">
            <a *ngIf="club.website" [href]="club.website" target="_blank" class="social-link" aria-label="Website">
              <mat-icon>language</mat-icon>
            </a>
            <a *ngIf="club.instagram" [href]="club.instagram" target="_blank" class="social-link" aria-label="Instagram">
              <mat-icon>photo_camera</mat-icon>
            </a>
            <a *ngIf="club.facebook" [href]="club.facebook" target="_blank" class="social-link" aria-label="Facebook">
              <mat-icon>facebook</mat-icon>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Club Content -->
  <section class="club-content">
    <div class="container">
      <mat-tab-group animationDuration="300ms">
        <!-- About Tab -->
        <mat-tab label="{{ 'CLUBS.ABOUT' | translate }}">
          <div class="tab-content">
            <div class="description-section">
              <h2>{{ 'CLUBS.DESCRIPTION' | translate }}</h2>
              <p>{{club.description}}</p>
            </div>

            <div class="activities-section" *ngIf="club.activities && club.activities.length > 0">
              <h2>{{ 'CLUBS.ACTIVITIES' | translate }}</h2>
              <ul class="activities-list">
                <li *ngFor="let activity of club.activities">
                  {{activity.description}}
                </li>
              </ul>
            </div>

            <div class="achievements-section" *ngIf="club.achievements && club.achievements.length > 0">
              <h2>{{ 'CLUBS.ACHIEVEMENTS' | translate }}</h2>
              <ul class="achievements-list">
                <li *ngFor="let achievement of club.achievements">
                  {{achievement.description}}
                  <span *ngIf="achievement.year">({{achievement.year}})</span>
                </li>
              </ul>
            </div>
          </div>
        </mat-tab>

        <!-- Members Tab -->
        <mat-tab label="{{ 'CLUBS.MEMBERS' | translate }}" *ngIf="club.leaders && club.leaders.length > 0">
          <div class="tab-content">
            <div class="leaders-section">
              <h2>{{ 'CLUBS.STUDENT_LEADERS' | translate }}</h2>
              <div class="leaders-grid">
                <div class="leader-card" *ngFor="let leader of club.leaders">
                  <div class="leader-image" *ngIf="leader.profileImageUrl">
                    <img [src]="leader.profileImageUrl" [alt]="leader.name">
                  </div>
                  <div class="leader-info">
                    <h3 class="leader-name">{{leader.name}}</h3>
                    <p class="leader-role">{{leader.role}}</p>
                    <p class="leader-grade" *ngIf="leader.grade">{{ 'CLUBS.GRADE' | translate }} {{leader.grade}}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </mat-tab>

        <!-- Join Tab -->
        <mat-tab label="{{ 'CLUBS.JOIN' | translate }}">
          <div class="tab-content">
            <div class="join-section">
              <h2>{{ 'CLUBS.HOW_TO_JOIN' | translate }}</h2>

              <div class="requirements-section" *ngIf="club.requirements">
                <h3>{{ 'CLUBS.REQUIREMENTS' | translate }}</h3>
                <p>{{club.requirements}}</p>
              </div>

              <div class="process-section" *ngIf="club.joinProcess">
                <h3>{{ 'CLUBS.JOIN_PROCESS' | translate }}</h3>
                <p>{{club.joinProcess}}</p>
              </div>

              <div class="contact-section" *ngIf="club.contactEmail">
                <h3>{{ 'CLUBS.CONTACT' | translate }}</h3>
                <p>{{ 'CLUBS.CONTACT_MESSAGE' | translate }}</p>
                <a mat-raised-button color="primary" [href]="'mailto:' + club.contactEmail">
                  <mat-icon>email</mat-icon>
                  {{ 'CLUBS.CONTACT_CLUB' | translate }}
                </a>
              </div>
            </div>
          </div>
        </mat-tab>

        <!-- Gallery Tab -->
        <mat-tab label="{{ 'CLUBS.GALLERY' | translate }}" *ngIf="club.galleryItems && club.galleryItems.length > 0">
          <div class="tab-content">
            <div class="gallery-section">
              <h2>{{ 'CLUBS.PHOTO_GALLERY' | translate }}</h2>
              <div class="gallery-grid">
                <div class="gallery-item" *ngFor="let item of club.galleryItems">
                  <img [src]="item.imageUrl" [alt]="item.caption || 'Club Activity'">
                  <div class="gallery-caption" *ngIf="item.caption">{{item.caption}}</div>
                </div>
              </div>
            </div>
          </div>
        </mat-tab>

        <!-- Events Tab -->
        <mat-tab label="{{ 'CLUBS.EVENTS' | translate }}" *ngIf="club.events && club.events.length > 0">
          <div class="tab-content">
            <div class="events-section">
              <h2>{{ 'CLUBS.UPCOMING_EVENTS' | translate }}</h2>
              <div class="events-list">
                <div class="event-item" *ngFor="let event of club.events">
                  <div class="event-date">{{formatDate(event.date)}}</div>
                  <div class="event-details">
                    <h3 class="event-title">{{event.title}}</h3>
                    <p class="event-location" *ngIf="event.location">
                      <mat-icon>location_on</mat-icon> {{event.location}}
                    </p>
                    <p class="event-time" *ngIf="event.time">
                      <mat-icon>access_time</mat-icon> {{event.time}}
                    </p>
                    <p class="event-description">{{event.description}}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </section>

  <!-- Call to Action -->
  <section class="cta-section">
    <div class="container">
      <h2>{{ 'CLUBS.INTERESTED' | translate }}</h2>
      <p>{{ 'CLUBS.INTERESTED_MESSAGE' | translate }}</p>

      <div class="cta-buttons">
        <a mat-raised-button color="primary" [href]="'mailto:' + club.contactEmail" *ngIf="club.contactEmail">
          <mat-icon>email</mat-icon>
          {{ 'CLUBS.CONTACT_CLUB' | translate }}
        </a>
        <button mat-stroked-button color="primary" (click)="goBack()">
          <mat-icon>groups</mat-icon>
          {{ 'CLUBS.EXPLORE_MORE_CLUBS' | translate }}
        </button>
      </div>
    </div>
  </section>
</div>
