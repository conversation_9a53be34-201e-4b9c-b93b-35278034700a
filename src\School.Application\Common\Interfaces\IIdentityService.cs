using School.Application.Common.Models;
using System.Threading.Tasks;

namespace School.Application.Common.Interfaces;

public interface IIdentityService
{
    Task<string?> GetUserNameAsync(Guid userId);

    Task<bool> IsInRoleAsync(Guid userId, string role);

    Task<bool> AuthorizeAsync(Guid userId, string policyName);

    Task<(ApiResult Result, Guid UserId)> CreateUserAsync(string userName, string password);

    Task<ApiResult> DeleteUserAsync(Guid userId);
}
