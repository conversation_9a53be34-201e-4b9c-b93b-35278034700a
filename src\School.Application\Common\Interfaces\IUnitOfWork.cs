using School.Domain.Common;

namespace School.Application.Common.Interfaces;

/// <summary>
/// Unit of Work interface to manage transactions and repositories
/// </summary>
public interface IUnitOfWork : IDisposable
{
    // Get repository for entity type
    IRepository<T> Repository<T>() where T : BaseEntity;

    // Transaction management
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task<int> SaveChangesAsync(string? userId, CancellationToken cancellationToken = default);
    Task<int> SaveChangesAndClearCacheAsync(CancellationToken cancellationToken = default);
    Task<int> SaveChangesAndClearCacheAsync(string? userId, CancellationToken cancellationToken = default);
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}
