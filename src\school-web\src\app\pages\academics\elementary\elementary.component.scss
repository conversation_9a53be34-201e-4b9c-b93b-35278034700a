@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section styles are now handled by the DefaultHeroComponent

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Section Styles
section {
  padding: $section-padding;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: $dark-gray;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Introduction Section
.intro-section {
  background-color: $white;

  .intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Features Section
.features-section {
  background-color: $light-gray;

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .feature-card {
      border-radius: $border-radius;
      padding: 30px;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .feature-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 70px;
        height: 70px;
        background-color: $primary-color;
        border-radius: 50%;
        margin-bottom: 20px;

        mat-icon {
          font-size: 35px;
          height: 35px;
          width: 35px;
          color: $white;
        }
      }

      mat-card-content {
        h3 {
          font-size: 1.5rem;
          margin-bottom: 15px;
          color: $text-color;
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }
}

// Grade Levels Section
.grade-levels-section {
  background-color: $white;

  .grade-levels-container {
    margin-top: 40px;

    ::ng-deep .mat-mdc-tab-header {
      margin-bottom: 30px;
    }

    .grade-content {
      padding: 20px;

      .grade-description {
        font-size: 1.1rem;
        line-height: 1.6;
        color: $text-color;
        margin-bottom: 30px;
      }

      h3 {
        font-size: 1.8rem;
        margin-bottom: 20px;
        color: $text-color;
        text-align: center;
      }

      .subjects-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;

        .subject-item {
          display: flex;
          align-items: center;
          background-color: $light-gray;
          border-radius: $border-radius;
          padding: 15px;
          transition: transform 0.3s, background-color 0.3s;

          &:hover {
            transform: translateY(-3px);
            background-color: #e8eaf6;
          }

          mat-icon {
            color: $primary-color;
            margin-right: 15px;
          }

          span {
            color: $text-color;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// Curriculum Section
.curriculum-section {
  background-color: $light-gray;

  .courses-container {
    margin-top: 40px;

    h3 {
      font-size: 1.8rem;
      margin-bottom: 30px;
      color: $text-color;
      text-align: center;
    }

    .courses-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 30px;

      .course-card {
        border-radius: $border-radius;
        box-shadow: $box-shadow;
        transition: transform 0.3s, box-shadow 0.3s;
        height: 100%;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }

        mat-card-content {
          padding: 20px;

          h4 {
            font-size: 1.3rem;
            margin-bottom: 5px;
            color: $text-color;
          }

          .course-grade {
            font-size: 0.9rem;
            color: $primary-color;
            margin-bottom: 15px;
            font-weight: 500;
          }

          p {
            color: $dark-gray;
            line-height: 1.6;
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

// Special Programs Section
.special-programs-section {
  background-color: $white;

  .programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .program-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .program-image {
        height: 200px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 15px;
          color: $text-color;
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }
}

// Daily Schedule Section
.schedule-section {
  background-color: $light-gray;

  .schedule-container {
    max-width: 800px;
    margin: 40px auto 30px;

    .schedule-item {
      display: flex;
      border-bottom: 1px solid $medium-gray;
      padding: 15px 0;

      &:last-child {
        border-bottom: none;
      }

      .schedule-time {
        width: 180px;
        font-weight: 500;
        color: $primary-color;
        flex-shrink: 0;
      }

      .schedule-activity {
        flex-grow: 1;
        color: $text-color;
      }
    }
  }

  .schedule-note {
    display: flex;
    align-items: flex-start;
    background-color: #e8eaf6;
    border-radius: $border-radius;
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;

    mat-icon {
      color: $primary-color;
      margin-right: 15px;
      margin-top: 3px;
    }

    p {
      margin: 0;
      color: $text-color;
      line-height: 1.6;
      font-size: 0.95rem;
    }
  }
}

// Faculty Section
.faculty-section {
  background-color: $white;

  .faculty-cta {
    text-align: center;
    margin-top: 40px;

    a {
      padding: 10px 30px;
      font-size: 1.1rem;
    }
  }
}

// Call to Action Section
.cta-section {
  background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -15%));
  color: $white;

  .cta-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h2 {
      color: $white;

      &:after {
        background-color: $white;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
    }

    .cta-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;

      a {
        padding: 10px 30px;
        font-size: 1.1rem;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  // Hero section styles removed

  section {
    padding: 60px 0;

    h2 {
      font-size: 2rem;
    }
  }

  .features-grid, .courses-grid, .programs-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  // Hero section styles removed

  .schedule-item {
    flex-direction: column;

    .schedule-time {
      margin-bottom: 5px;
    }
  }

  .cta-section {
    .cta-buttons {
      flex-direction: column;
      align-items: center;

      a {
        width: 100%;
        max-width: 300px;
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .hero-section {
    height: 250px;

    .hero-content h1 {
      font-size: 1.8rem;
    }
  }

  section h2 {
    font-size: 1.8rem;
  }

  .features-grid, .courses-grid, .programs-grid {
    grid-template-columns: 1fr;
  }

  .subjects-list {
    grid-template-columns: 1fr;
  }
}
