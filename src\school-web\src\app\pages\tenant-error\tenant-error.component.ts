import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-tenant-error',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="tenant-error-container">
      <div class="error-content">
        <div class="error-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="#f44336"/>
          </svg>
        </div>
        
        <h1 class="error-title">{{ getErrorTitle() }}</h1>
        <p class="error-message">{{ getErrorMessage() }}</p>
        
        <div class="error-details" *ngIf="showDetails">
          <p><strong>Attempted URL:</strong> {{ returnUrl }}</p>
          <p><strong>Current Domain:</strong> {{ currentDomain }}</p>
          <p><strong>Error Reason:</strong> {{ errorReason }}</p>
        </div>
        
        <div class="error-actions">
          <button class="btn btn-primary" (click)="retryTenantLoad()">
            Try Again
          </button>
          
          <button class="btn btn-secondary" (click)="goToMainSite()">
            Go to Main Site
          </button>
          
          <button class="btn btn-link" (click)="toggleDetails()">
            {{ showDetails ? 'Hide' : 'Show' }} Details
          </button>
        </div>
        
        <div class="help-section">
          <h3>Need Help?</h3>
          <p>If you're trying to access a specific school or organization:</p>
          <ul>
            <li>Check that you're using the correct subdomain (e.g., yourschool.edumanage.com)</li>
            <li>Verify that the organization is active and properly configured</li>
            <li>Contact your system administrator if the problem persists</li>
          </ul>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .tenant-error-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 20px;
    }

    .error-content {
      background: white;
      border-radius: 12px;
      padding: 40px;
      max-width: 600px;
      width: 100%;
      text-align: center;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .error-icon {
      margin-bottom: 24px;
    }

    .error-title {
      color: #333;
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 16px;
    }

    .error-message {
      color: #666;
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 32px;
    }

    .error-details {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 32px;
      text-align: left;
    }

    .error-details p {
      margin: 8px 0;
      font-family: monospace;
      font-size: 14px;
    }

    .error-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
      flex-wrap: wrap;
      margin-bottom: 40px;
    }

    .btn {
      padding: 12px 24px;
      border-radius: 6px;
      border: none;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      text-decoration: none;
      display: inline-block;
    }

    .btn-primary {
      background: #667eea;
      color: white;
    }

    .btn-primary:hover {
      background: #5a6fd8;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #5a6268;
    }

    .btn-link {
      background: transparent;
      color: #667eea;
      text-decoration: underline;
    }

    .btn-link:hover {
      color: #5a6fd8;
    }

    .help-section {
      text-align: left;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 24px;
    }

    .help-section h3 {
      color: #333;
      margin-bottom: 16px;
    }

    .help-section ul {
      margin: 16px 0;
      padding-left: 20px;
    }

    .help-section li {
      margin: 8px 0;
      color: #666;
    }

    @media (max-width: 768px) {
      .error-content {
        padding: 24px;
      }
      
      .error-actions {
        flex-direction: column;
      }
      
      .btn {
        width: 100%;
      }
    }
  `]
})
export class TenantErrorComponent implements OnInit {
  returnUrl: string = '';
  errorReason: string = '';
  currentDomain: string = '';
  showDetails: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.returnUrl = params['returnUrl'] || window.location.href;
      this.errorReason = params['reason'] || 'unknown';
    });
    
    this.currentDomain = window.location.hostname;
  }

  getErrorTitle(): string {
    switch (this.errorReason) {
      case 'invalid-tenant':
        return 'Organization Not Found';
      case 'loading-error':
        return 'Unable to Load Organization';
      case 'inactive-tenant':
        return 'Organization Inactive';
      default:
        return 'Tenant Access Error';
    }
  }

  getErrorMessage(): string {
    switch (this.errorReason) {
      case 'invalid-tenant':
        return 'The organization you\'re trying to access could not be found or is not active. Please check the URL and try again.';
      case 'loading-error':
        return 'We encountered an error while trying to load the organization information. Please try again in a moment.';
      case 'inactive-tenant':
        return 'This organization\'s account is currently inactive. Please contact the organization administrator.';
      default:
        return 'There was a problem accessing the organization. Please try again or contact support.';
    }
  }

  retryTenantLoad(): void {
    // Reload the page to retry tenant loading
    window.location.reload();
  }

  goToMainSite(): void {
    // Redirect to main site (you can customize this URL)
    window.location.href = 'https://edumanage.com';
  }

  toggleDetails(): void {
    this.showDetails = !this.showDetails;
  }
}
