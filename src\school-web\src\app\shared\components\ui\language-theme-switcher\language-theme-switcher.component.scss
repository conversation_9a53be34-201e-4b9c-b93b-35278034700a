.language-theme-switcher {
  display: flex;
  align-items: center;

  .switcher-group {
    display: flex;
    align-items: center;
    gap: 8px;

    &.full-style {
      gap: 16px;
      flex-direction: column;
      align-items: stretch;

      @media (min-width: 768px) {
        flex-direction: row;
        align-items: center;
      }
    }

    &.icon-only {
      gap: 4px;
    }
  }

  .switcher-btn {
    position: relative;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--hover-color, rgba(0, 0, 0, 0.04));
    }

    &.language-btn {
      .current-lang {
        position: absolute;
        top: -2px;
        right: -2px;
        font-size: 12px;
        background: var(--primary-color);
        color: white;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
      }
    }

    &.theme-btn {
      mat-icon {
        transition: transform 0.3s ease;
      }

      &:hover mat-icon {
        transform: rotate(180deg);
      }
    }
  }

  .theme-toggle-container {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    background: var(--surface-color, #f5f5f5);
    border: 1px solid var(--border-color, #e0e0e0);

    mat-icon {
      color: var(--text-secondary, #666);
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .theme-label {
      font-size: 14px;
      color: var(--text-primary, #333);
      font-weight: 500;
      min-width: 60px;
    }

    mat-slide-toggle {
      ::ng-deep {
        .mdc-switch {
          --mdc-switch-selected-track-color: var(--primary-color);
          --mdc-switch-selected-handle-color: var(--primary-color);
          --mdc-switch-selected-hover-track-color: var(--primary-dark);
          --mdc-switch-selected-hover-handle-color: var(--primary-dark);
        }
      }
    }
  }

  // Style variations
  &.style-compact {
    .switcher-group {
      background: var(--surface-color, rgba(255, 255, 255, 0.1));
      border-radius: 8px;
      padding: 4px;
      border: 1px solid var(--border-color, rgba(255, 255, 255, 0.2));
    }

    .switcher-btn {
      width: 36px;
      height: 36px;
      border-radius: 6px;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }

  &.style-full {
    .switcher-btn {
      padding: 8px 16px;
      border-radius: 8px;
      background: var(--surface-color, #fff);
      border: 1px solid var(--border-color, #e0e0e0);
      color: var(--text-primary, #333);

      mat-icon {
        margin-right: 8px;
      }

      span {
        font-weight: 500;
      }
    }
  }

  &.style-icon-only {
    .switcher-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;

      &:hover {
        background-color: var(--primary-light, rgba(63, 81, 181, 0.1));
      }
    }
  }

  &.style-dropdown {
    .switcher-btn {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: var(--surface-color, rgba(255, 255, 255, 0.1));
      border: 1px solid var(--border-color, rgba(255, 255, 255, 0.2));

      &:hover {
        background: var(--hover-color, rgba(255, 255, 255, 0.2));
      }
    }
  }
}

// Menu Styles
::ng-deep {
  .language-menu,
  .combined-menu {
    .mat-mdc-menu-panel {
      min-width: 200px;
      max-width: 280px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      border: 1px solid var(--border-color, #e0e0e0);
    }

    .menu-header {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      background: var(--primary-light, rgba(63, 81, 181, 0.1));
      color: var(--primary-color, #3f51b5);
      font-weight: 600;
      font-size: 14px;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }

    .mat-mdc-menu-item {
      height: auto;
      min-height: 44px;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      gap: 12px;
      transition: all 0.2s ease;

      &:hover {
        background-color: var(--hover-color, rgba(0, 0, 0, 0.04));
      }

      &.active {
        background-color: var(--primary-light, rgba(63, 81, 181, 0.1));
        color: var(--primary-color, #3f51b5);
        font-weight: 500;
      }

      .language-flag {
        font-size: 18px;
        width: 24px;
        text-align: center;
      }

      .language-name {
        flex: 1;
        font-size: 14px;
      }

      .check-icon {
        color: var(--success-color, #4caf50);
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }

    .menu-section {
      .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: var(--surface-alt, #f8f9fa);
        color: var(--text-secondary, #666);
        font-weight: 500;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
        }
      }
    }

    .mat-mdc-menu-divider {
      margin: 0;
      border-color: var(--border-color, #e0e0e0);
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .language-theme-switcher {
    &.style-compact .switcher-group {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
    }

    &.style-full .switcher-btn {
      background: var(--surface-dark, #2d2d2d);
      border-color: var(--border-dark, #404040);
      color: var(--text-dark, #fff);
    }

    &.style-dropdown .switcher-btn {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
    }

    .theme-toggle-container {
      background: var(--surface-dark, #2d2d2d);
      border-color: var(--border-dark, #404040);

      mat-icon {
        color: var(--text-secondary-dark, #aaa);
      }

      .theme-label {
        color: var(--text-dark, #fff);
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .language-theme-switcher {
    &.style-full {
      .switcher-group {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
      }

      .switcher-btn {
        justify-content: center;
      }

      .theme-toggle-container {
        justify-content: center;
      }
    }
  }
}

// Animation for theme transitions
.switcher-btn mat-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-toggle-container mat-slide-toggle {
  ::ng-deep .mdc-switch__track {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}
