using School.Domain.Common;

namespace School.Domain.Entities;

/// <summary>
/// Stores configuration and settings for an organization
/// </summary>
public class OrganizationSettings : BaseEntity
{
    /// <summary>
    /// ID of the organization these settings belong to
    /// </summary>
    public Guid OrganizationId { get; set; }

    /// <summary>
    /// Academic year settings (JSON)
    /// Contains default academic year configuration
    /// </summary>
    public string? AcademicYearSettings { get; set; }

    /// <summary>
    /// Grading system settings (JSON)
    /// Contains grading scales, passing marks, etc.
    /// </summary>
    public string? GradingSystemSettings { get; set; }

    /// <summary>
    /// Attendance settings (JSON)
    /// Contains attendance rules, minimum requirements, etc.
    /// </summary>
    public string? AttendanceSettings { get; set; }

    /// <summary>
    /// Fee management settings (JSON)
    /// Contains fee structures, payment terms, etc.
    /// </summary>
    public string? FeeSettings { get; set; }

    /// <summary>
    /// Communication settings (JSON)
    /// Contains SMS, email, notification preferences
    /// </summary>
    public string? CommunicationSettings { get; set; }

    /// <summary>
    /// Security settings (JSON)
    /// Contains password policies, session settings, etc.
    /// </summary>
    public string? SecuritySettings { get; set; }

    /// <summary>
    /// Branding settings (JSON)
    /// Contains colors, fonts, logo preferences
    /// </summary>
    public string? BrandingSettings { get; set; }

    /// <summary>
    /// Integration settings (JSON)
    /// Contains third-party service configurations
    /// </summary>
    public string? IntegrationSettings { get; set; }

    /// <summary>
    /// Backup and data retention settings (JSON)
    /// </summary>
    public string? BackupSettings { get; set; }

    /// <summary>
    /// Privacy and compliance settings (JSON)
    /// Contains GDPR, data protection settings
    /// </summary>
    public string? PrivacySettings { get; set; }

    /// <summary>
    /// Feature flags for this organization (JSON)
    /// Controls which features are enabled/disabled
    /// </summary>
    public string? FeatureFlags { get; set; }

    /// <summary>
    /// Custom fields configuration (JSON)
    /// Allows organizations to define custom fields for students, faculty, etc.
    /// </summary>
    public string? CustomFieldsConfig { get; set; }

    /// <summary>
    /// Workflow settings (JSON)
    /// Contains approval workflows, automation rules
    /// </summary>
    public string? WorkflowSettings { get; set; }

    /// <summary>
    /// Reporting settings (JSON)
    /// Contains report templates, scheduling, etc.
    /// </summary>
    public string? ReportingSettings { get; set; }

    /// <summary>
    /// Mobile app settings (JSON)
    /// Contains mobile-specific configurations
    /// </summary>
    public string? MobileAppSettings { get; set; }

    /// <summary>
    /// API access settings (JSON)
    /// Contains API keys, rate limits, etc.
    /// </summary>
    public string? ApiSettings { get; set; }

    /// <summary>
    /// Maintenance mode settings
    /// </summary>
    public bool IsMaintenanceMode { get; set; } = false;

    /// <summary>
    /// Maintenance message to display to users
    /// </summary>
    public string? MaintenanceMessage { get; set; }

    /// <summary>
    /// Whether new user registrations are allowed
    /// </summary>
    public bool AllowRegistrations { get; set; } = true;

    /// <summary>
    /// Whether guest access is allowed
    /// </summary>
    public bool AllowGuestAccess { get; set; } = false;

    /// <summary>
    /// Maximum file upload size in MB
    /// </summary>
    public int MaxFileUploadSizeMB { get; set; } = 10;

    /// <summary>
    /// Allowed file types for uploads (comma-separated)
    /// </summary>
    public string AllowedFileTypes { get; set; } = "jpg,jpeg,png,pdf,doc,docx,xls,xlsx";

    /// <summary>
    /// Session timeout in minutes
    /// </summary>
    public int SessionTimeoutMinutes { get; set; } = 60;

    /// <summary>
    /// Whether two-factor authentication is required
    /// </summary>
    public bool RequireTwoFactorAuth { get; set; } = false;

    /// <summary>
    /// Navigation properties
    /// </summary>
    public Organization Organization { get; set; } = null!;
}
