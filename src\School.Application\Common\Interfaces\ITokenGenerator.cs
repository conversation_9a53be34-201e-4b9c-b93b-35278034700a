using School.Domain.Entities;
using System.Security.Claims;

namespace School.Application.Common.Interfaces;

public interface ITokenGenerator
{
    // Legacy method for User entity
    (string token, DateTime expiration) GenerateToken(User user);

    // New method for ApplicationUser entity
    Task<(string token, DateTime expiration)> GenerateTokenAsync(object applicationUser);

    // Admin token generation with special claims
    Task<(string token, DateTime expiration)> GenerateAdminTokenAsync(object applicationUser, IList<string> roles);

    // Refresh token methods
    string GenerateRefreshToken();

    // MFA token methods
    string GenerateMfaToken(string userId);

    // Token validation methods
    ClaimsPrincipal? GetPrincipalFromExpiredToken(string token);
}
