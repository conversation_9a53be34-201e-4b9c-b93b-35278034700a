import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class HeroImageService {
  private readonly defaultHeroImage = 'assets/images/default-hero.jpg';
  
  // Map of component paths to their default hero images
  private readonly heroImageMap: Record<string, string> = {
    'about': 'assets/images/about-hero.jpg',
    'about/history': 'assets/images/history-hero.jpg',
    'about/mission': 'assets/images/mission-hero.jpg',
    'about/leadership': 'assets/images/leadership-hero.jpg',
    'about/parents': 'assets/images/parents-hero.jpg',
    'academics': 'assets/images/academics-hero.jpg',
    'academics/elementary': 'assets/images/elementary-hero.jpg',
    'academics/middle': 'assets/images/middle-hero.jpg',
    'academics/high': 'assets/images/high-hero.jpg',
    'academics/stem': 'assets/images/stem-hero.jpg',
    'admissions': 'assets/images/admissions-hero.jpg',
    'tuition': 'assets/images/tuition-hero.jpg',
    'campus': 'assets/images/campus-hero.jpg',
    'campus/clubs': 'assets/images/clubs-hero.jpg',
    'campus/sports': 'assets/images/sports-hero.jpg',
    'campus/arts': 'assets/images/arts-hero.jpg',
    'campus/dining': 'assets/images/dining-hero.jpg',
    'campus/health': 'assets/images/health-hero.jpg',
    'campus/housing': 'assets/images/housing-hero.jpg',
    'campus/tour': 'assets/images/tour-hero.jpg',
    'news': 'assets/images/news-hero.jpg',
    'events': 'assets/images/events-hero.jpg',
    'calendar': 'assets/images/calendar-hero.jpg',
    'alumni': 'assets/images/alumni-hero.jpg',
    'faculty': 'assets/images/faculty-hero.jpg',
    'contact': 'assets/images/contact-hero.jpg',
    'careers': 'assets/images/careers-hero.jpg',
    'hostel': 'assets/images/hostel-hero.jpg',
    'donate': 'assets/images/donate-hero.jpg'
  };

  constructor() { }

  /**
   * Get the hero image for a specific route path
   * @param path The route path (e.g., 'about', 'academics/elementary')
   * @returns The hero image URL
   */
  getHeroImage(path: string): string {
    return this.heroImageMap[path] || this.defaultHeroImage;
  }

  /**
   * Get the default hero image
   * @returns The default hero image URL
   */
  getDefaultHeroImage(): string {
    return this.defaultHeroImage;
  }

  /**
   * Get the school emblem image
   * @returns The school emblem image URL
   */
  getSchoolEmblem(): string {
    return 'assets/images/school-emblem.png';
  }
}
