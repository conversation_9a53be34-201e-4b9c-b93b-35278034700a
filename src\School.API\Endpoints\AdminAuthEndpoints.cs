using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Auth;

namespace School.API.Endpoints;

/// <summary>
/// Admin authentication endpoints that work without tenant context
/// </summary>
public class AdminAuthEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var adminAuthGroup = app.MapGroup("/api/auth/admin").WithTags("Admin Authentication").AllowAnonymous();
        var adminGroup = app.MapGroup("/api/admin").WithTags("Admin Management").RequireAuthorization("AdminPolicy");

        // Admin login endpoint - works without tenant context
        adminAuthGroup.MapPost("/login", async ([FromBody] AdminLoginDto loginDto, [FromServices] IAdminAuthService adminAuthService) =>
        {
            try
            {
                var response = await adminAuthService.AdminLoginAsync(loginDto);
                if (response == null)
                {
                    return ApiResults.ApiUnauthorized("Invalid admin credentials or insufficient privileges");
                }

                if (response.RequiresMfa)
                {
                    return ApiResults.ApiOk(new
                    {
                        requiresMfa = true,
                        mfaToken = response.MfaToken,
                        message = "MFA verification required"
                    }, "MFA verification required");
                }

                return ApiResults.ApiOk(response, "Admin login successful");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiBadRequest($"Admin login failed: {ex.Message}");
            }
        })
        .WithName("AdminLogin")
        .WithOpenApi(operation => new(operation)
        {
            Summary = "Admin Login",
            Description = "Authenticates admin users without requiring tenant context. Only users with SystemAdmin or SuperAdmin roles can login.",
            Tags = new List<Microsoft.OpenApi.Models.OpenApiTag> { new() { Name = "Admin Authentication" } }
        });

        // Get accessible tenants for admin
        adminGroup.MapGet("/tenants", async ([FromServices] IAdminAuthService adminAuthService, HttpContext context) =>
        {
            try
            {
                var userId = context.User.FindFirst("userId")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return ApiResults.ApiUnauthorized("User ID not found in token");
                }

                var tenants = await adminAuthService.GetAccessibleTenantsAsync(userId);
                return ApiResults.ApiOk(tenants, "Accessible tenants retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiBadRequest($"Failed to get accessible tenants: {ex.Message}");
            }
        })
        .WithName("GetAccessibleTenants")
        .WithOpenApi(operation => new(operation)
        {
            Summary = "Get Accessible Tenants",
            Description = "Gets list of tenants that the admin user can access",
            Tags = new List<Microsoft.OpenApi.Models.OpenApiTag> { new() { Name = "Admin Management" } }
        });

        // Switch tenant context for admin
        adminGroup.MapPost("/switch-tenant/{tenantId}", async ([FromRoute] Guid tenantId, [FromServices] ITenantService tenantService, HttpContext context) =>
        {
            try
            {
                var userId = context.User.FindFirst("userId")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return ApiResults.ApiUnauthorized("User ID not found in token");
                }

                // Check if admin has access to this tenant
                var adminAuthService = context.RequestServices.GetRequiredService<IAdminAuthService>();
                var accessibleTenants = await adminAuthService.GetAccessibleTenantsAsync(userId);
                
                if (!accessibleTenants.Any(t => t.TenantId == tenantId))
                {
                    return ApiResults.ApiForbidden("Access denied to this tenant");
                }

                // Get tenant by ID and set context
                var tenant = await tenantService.GetTenantByIdAsync(tenantId);
                if (tenant == null)
                {
                    return ApiResults.ApiNotFound("Tenant not found");
                }

                // Set tenant context for this request
                await tenantService.SetTenantAsync(tenant.Slug);

                return ApiResults.ApiOk(new
                {
                    tenantId = tenant.Id,
                    tenantName = tenant.Name,
                    tenantSlug = tenant.Slug,
                    message = "Tenant context switched successfully"
                }, "Tenant context switched");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiBadRequest($"Failed to switch tenant: {ex.Message}");
            }
        })
        .WithName("SwitchTenant")
        .WithOpenApi(operation => new(operation)
        {
            Summary = "Switch Tenant Context",
            Description = "Switches the admin user's context to a specific tenant",
            Tags = new List<Microsoft.OpenApi.Models.OpenApiTag> { new() { Name = "Admin Management" } }
        });

        // Get current admin user info
        adminGroup.MapGet("/me", async (HttpContext context) =>
        {
            try
            {
                var userId = context.User.FindFirst("userId")?.Value;
                var userName = context.User.FindFirst(System.Security.Claims.ClaimTypes.Name)?.Value;
                var email = context.User.FindFirst(System.Security.Claims.ClaimTypes.Email)?.Value;
                var roles = context.User.FindAll(System.Security.Claims.ClaimTypes.Role).Select(c => c.Value).ToList();
                var isAdmin = context.User.FindFirst("isAdmin")?.Value == "true";
                var adminType = context.User.FindFirst("adminType")?.Value;
                var canAccessAllTenants = context.User.FindFirst("canAccessAllTenants")?.Value == "true";

                return ApiResults.ApiOk(new
                {
                    userId,
                    userName,
                    email,
                    roles,
                    isAdmin,
                    adminType,
                    canAccessAllTenants
                }, "Admin user info retrieved");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiBadRequest($"Failed to get admin user info: {ex.Message}");
            }
        })
        .WithName("GetAdminUserInfo")
        .WithOpenApi(operation => new(operation)
        {
            Summary = "Get Admin User Info",
            Description = "Gets current admin user information from JWT token",
            Tags = new List<Microsoft.OpenApi.Models.OpenApiTag> { new() { Name = "Admin Management" } }
        });
    }
}
