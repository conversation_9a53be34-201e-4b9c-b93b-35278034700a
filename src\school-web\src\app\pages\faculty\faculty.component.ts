import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../shared/components/default-hero/default-hero.component';

// Faculty member interface
export interface FacultyMember {
  id: number;
  name: string;
  title: string;
  department: string;
  image: string;
  email: string;
  phone?: string;
  office?: string;
  education: string[];
  bio: string;
  shortBio: string;
  specializations: string[];
  courses?: string[];
  publications?: string[];
  awards?: string[];
  joinedYear: number;
  featured?: boolean;
  socialLinks?: {
    website?: string;
    linkedin?: string;
    twitter?: string;
    researchGate?: string;
  };
}

// Department interface
interface Department {
  id: string;
  name: string;
}

@Component({
  selector: 'app-faculty',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatChipsModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    FormsModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './faculty.component.html',
  styleUrl: './faculty.component.scss'
})
export class FacultyComponent implements OnInit {
  // All faculty members
  facultyMembers: FacultyMember[] = [];

  // Filtered faculty members
  filteredFaculty: FacultyMember[] = [];

  // Featured faculty members
  featuredFaculty: FacultyMember[] = [];

  // Departments
  departments: Department[] = [
    { id: 'all', name: 'All Departments' },
    { id: 'science', name: 'Science' },
    { id: 'mathematics', name: 'Mathematics' },
    { id: 'languages', name: 'Languages' },
    { id: 'social-studies', name: 'Social Studies' },
    { id: 'arts', name: 'Arts & Music' },
    { id: 'physical-education', name: 'Physical Education' },
    { id: 'technology', name: 'Technology' },
    { id: 'administration', name: 'Administration' }
  ];

  // Filter values
  searchTerm: string = '';
  selectedDepartment: string = 'all';

  constructor() { }

  ngOnInit(): void {
    this.loadFacultyData();
    this.filteredFaculty = [...this.facultyMembers];
    this.featuredFaculty = this.facultyMembers.filter(member => member.featured);
  }

  /**
   * Load faculty data
   */
  loadFacultyData(): void {
    this.facultyMembers = [
      {
        id: 1,
        name: 'Dr. Sarah Johnson',
        title: 'Principal',
        department: 'administration',
        image: 'https://via.placeholder.com/300x400?text=Dr.+Sarah+Johnson',
        email: 'sarah.johnson[at]school.edu',
        phone: '(*************',
        office: 'Admin Building, Room 101',
        education: [
          'Ph.D. in Educational Leadership, Harvard University',
          'M.Ed. in Curriculum Development, Columbia University',
          'B.A. in Education, University of Michigan'
        ],
        bio: 'Dr. Sarah Johnson has over 20 years of experience in education, with a focus on innovative teaching methods and school leadership. Before becoming the principal, she served as a teacher, department head, and vice principal. Her research on student-centered learning environments has been published in several educational journals. Dr. Johnson is committed to creating a supportive and challenging academic environment where all students can thrive.',
        shortBio: 'Dr. Johnson leads our school with over 20 years of experience in education and a passion for innovative teaching methods.',
        specializations: ['Educational Leadership', 'Curriculum Development', 'Student-Centered Learning'],
        awards: [
          'National Principal of the Year Finalist (2022)',
          'State Educational Leadership Award (2020)',
          'Excellence in School Administration (2018)'
        ],
        joinedYear: 2015,
        featured: true,
        socialLinks: {
          linkedin: 'https://linkedin.com/in/sarahjohnson',
          twitter: 'https://twitter.com/drsarahjohnson'
        }
      },
      {
        id: 2,
        name: 'Prof. Michael Chen',
        title: 'Head of Science Department',
        department: 'science',
        image: 'https://via.placeholder.com/300x400?text=Prof.+Michael+Chen',
        email: 'michael.chen[at]school.edu',
        phone: '(*************',
        office: 'Science Building, Room 203',
        education: [
          'Ph.D. in Physics, MIT',
          'M.S. in Physics, Stanford University',
          'B.S. in Physics, UC Berkeley'
        ],
        bio: 'Professor Michael Chen is an accomplished physicist with a passion for science education. He has conducted research in quantum mechanics and has published numerous papers in prestigious scientific journals. At our school, he has revolutionized the science curriculum by incorporating hands-on experiments and real-world applications. Prof. Chen believes in making science accessible and exciting for students of all ages and backgrounds.',
        shortBio: 'Prof. Chen leads our Science Department with expertise in physics and a commitment to hands-on learning.',
        specializations: ['Physics Education', 'Quantum Mechanics', 'STEM Integration'],
        courses: ['Advanced Physics', 'Introduction to Scientific Research', 'Science and Technology'],
        publications: [
          'Chen, M. et al. (2021). "Engaging Secondary Students in Quantum Concepts." Journal of Science Education.',
          'Chen, M. (2019). "Practical Applications of Physics in K-12 Education." Science Teacher Quarterly.'
        ],
        awards: [
          'Outstanding Science Educator Award (2021)',
          'Innovation in Teaching Award (2019)'
        ],
        joinedYear: 2017,
        featured: true,
        socialLinks: {
          website: 'https://michaelchen.edu',
          researchGate: 'https://researchgate.net/profile/michael_chen'
        }
      },
      {
        id: 3,
        name: 'Ms. Priya Patel',
        title: 'Mathematics Teacher',
        department: 'mathematics',
        image: 'https://via.placeholder.com/300x400?text=Ms.+Priya+Patel',
        email: 'priya.patel[at]school.edu',
        office: 'Main Building, Room 305',
        education: [
          'M.S. in Mathematics, University of Chicago',
          'B.S. in Mathematics Education, NYU'
        ],
        bio: 'Ms. Priya Patel specializes in making mathematics engaging and accessible to all students. With her innovative teaching methods, she has helped improve math scores across all grade levels. Ms. Patel is known for her ability to explain complex mathematical concepts in simple terms and for creating a supportive classroom environment where students feel comfortable asking questions and taking intellectual risks.',
        shortBio: 'Ms. Patel makes mathematics engaging and accessible with her innovative teaching methods.',
        specializations: ['Algebra', 'Geometry', 'Mathematical Problem Solving'],
        courses: ['Algebra II', 'Pre-Calculus', 'Mathematical Thinking'],
        awards: [
          'Teacher of the Year (2022)',
          'Excellence in Mathematics Education Award (2020)'
        ],
        joinedYear: 2018,
        featured: false
      },
      {
        id: 4,
        name: 'Mr. James Wilson',
        title: 'English Literature Teacher',
        department: 'languages',
        image: 'https://via.placeholder.com/300x400?text=Mr.+James+Wilson',
        email: 'james.wilson[at]school.edu',
        office: 'Languages Building, Room 102',
        education: [
          'M.A. in English Literature, Oxford University',
          'B.A. in English, University of Edinburgh'
        ],
        bio: 'Mr. James Wilson brings literature to life in his classroom through interactive discussions and creative projects. With a background in both classical and contemporary literature, he helps students develop critical thinking skills and a lifelong love of reading. Mr. Wilson is also the faculty advisor for the school literary magazine and the drama club, where he encourages students to express themselves through writing and performance.',
        shortBio: 'Mr. Wilson brings literature to life with his interactive teaching style and deep knowledge of both classical and contemporary works.',
        specializations: ['British Literature', 'Creative Writing', 'Drama'],
        courses: ['World Literature', 'Creative Writing Workshop', 'Shakespeare Studies'],
        publications: [
          'Wilson, J. (2020). "Engaging Reluctant Readers Through Drama." English Teaching Forum.',
          'Wilson, J. (2018). "The Relevance of Shakespeare in Modern Education." Literary Education Journal.'
        ],
        joinedYear: 2016,
        featured: false
      },
      {
        id: 5,
        name: 'Dr. Aisha Rahman',
        title: 'Social Studies Department Chair',
        department: 'social-studies',
        image: 'https://via.placeholder.com/300x400?text=Dr.+Aisha+Rahman',
        email: 'aisha.rahman[at]school.edu',
        phone: '(*************',
        office: 'Humanities Building, Room 204',
        education: [
          'Ph.D. in History, Yale University',
          'M.A. in International Relations, Georgetown University',
          'B.A. in Political Science, University of Pennsylvania'
        ],
        bio: 'Dr. Aisha Rahman is an expert in global history and international relations. Her research focuses on cross-cultural exchanges and their impact on societal development. In her classes, Dr. Rahman encourages students to analyze historical events from multiple perspectives and to make connections between past events and current global issues. She organizes the annual Model United Nations conference and leads international educational trips during school breaks.',
        shortBio: 'Dr. Rahman brings global perspectives to our Social Studies department with her expertise in history and international relations.',
        specializations: ['Global History', 'International Relations', 'Cultural Studies'],
        courses: ['World History', 'International Relations', 'Comparative Government'],
        publications: [
          'Rahman, A. (2022). "Teaching Global Perspectives in Secondary Education." Social Studies Educator.',
          'Rahman, A. (2019). "Cross-Cultural Understanding Through Historical Analysis." Journal of History Education.'
        ],
        awards: [
          'Global Education Excellence Award (2021)',
          'Outstanding Contribution to Social Studies Education (2019)'
        ],
        joinedYear: 2015,
        featured: true,
        socialLinks: {
          linkedin: 'https://linkedin.com/in/aisharahman',
          twitter: 'https://twitter.com/draisharahman'
        }
      },
      {
        id: 6,
        name: 'Mr. David Park',
        title: 'Physical Education Teacher & Athletics Director',
        department: 'physical-education',
        image: 'https://via.placeholder.com/300x400?text=Mr.+David+Park',
        email: 'david.park[at]school.edu',
        phone: '(*************',
        office: 'Sports Complex, Room 101',
        education: [
          'M.S. in Sports Management, University of Florida',
          'B.S. in Physical Education, UCLA'
        ],
        bio: 'Mr. David Park is a former Olympic athlete who brings his passion for sports and fitness to our school. As both a physical education teacher and the athletics director, he has developed a comprehensive program that emphasizes not only physical skills but also teamwork, sportsmanship, and healthy lifestyle choices. Mr. Park coaches the varsity swimming team and has led them to multiple state championships.',
        shortBio: 'Mr. Park, a former Olympic athlete, leads our physical education program with an emphasis on teamwork and healthy lifestyles.',
        specializations: ['Sports Training', 'Swimming', 'Athletic Program Development'],
        courses: ['Physical Education', 'Sports Leadership', 'Health and Wellness'],
        awards: [
          'Coach of the Year (2023)',
          'Outstanding Physical Education Program Award (2020)'
        ],
        joinedYear: 2014,
        featured: false,
        socialLinks: {
          twitter: 'https://twitter.com/coachpark'
        }
      },
      {
        id: 7,
        name: 'Ms. Emma Rodriguez',
        title: 'Art Teacher',
        department: 'arts',
        image: 'https://via.placeholder.com/300x400?text=Ms.+Emma+Rodriguez',
        email: 'emma.rodriguez[at]school.edu',
        office: 'Arts Building, Room 105',
        education: [
          'M.F.A. in Fine Arts, Rhode Island School of Design',
          'B.F.A. in Art Education, Pratt Institute'
        ],
        bio: 'Ms. Emma Rodriguez is an accomplished artist whose work has been exhibited in galleries across the country. She brings her creative expertise to the classroom, where she teaches various art forms including painting, sculpture, and digital art. Ms. Rodriguez believes in the power of art to foster self-expression, critical thinking, and cultural understanding. She organizes the annual student art exhibition and has established partnerships with local museums to enhance students\' artistic experiences.',
        shortBio: 'Ms. Rodriguez, an accomplished artist, inspires students to express themselves through various art forms.',
        specializations: ['Fine Arts', 'Digital Art', 'Art History'],
        courses: ['Studio Art', 'Digital Media', 'Art History and Appreciation'],
        awards: [
          'Arts Educator Excellence Award (2022)',
          'Community Arts Integration Award (2019)'
        ],
        joinedYear: 2016,
        featured: false
      },
      {
        id: 8,
        name: 'Mr. Omar Hassan',
        title: 'Technology Coordinator & Computer Science Teacher',
        department: 'technology',
        image: 'https://via.placeholder.com/300x400?text=Mr.+Omar+Hassan',
        email: 'omar.hassan[at]school.edu',
        phone: '(*************',
        office: 'Technology Center, Room 202',
        education: [
          'M.S. in Computer Science, Georgia Tech',
          'B.S. in Information Technology, Purdue University'
        ],
        bio: 'Mr. Omar Hassan has extensive experience in software development and educational technology. Before joining our school, he worked in the tech industry for several major companies. Mr. Hassan oversees the school\'s technology infrastructure and teaches computer science courses ranging from basic digital literacy to advanced programming. He has established a popular robotics club and coding camp that have inspired many students to pursue careers in technology.',
        shortBio: 'Mr. Hassan brings industry experience to our technology program, teaching everything from basic digital literacy to advanced programming.',
        specializations: ['Computer Science Education', 'Educational Technology', 'Robotics'],
        courses: ['Introduction to Computer Science', 'AP Computer Science', 'Robotics'],
        awards: [
          'Technology Educator of the Year (2021)',
          'Innovation in STEM Education Award (2019)'
        ],
        joinedYear: 2017,
        featured: false,
        socialLinks: {
          website: 'https://omarhassan.tech',
          linkedin: 'https://linkedin.com/in/omarhassan'
        }
      }
    ];
  }

  /**
   * Filter faculty based on search term and department
   */
  filterFaculty(): void {
    this.filteredFaculty = this.facultyMembers.filter(member => {
      // Filter by search term
      const matchesSearch = this.searchTerm ?
        member.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        member.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        member.specializations.some(spec => spec.toLowerCase().includes(this.searchTerm.toLowerCase())) :
        true;

      // Filter by department
      const matchesDepartment = this.selectedDepartment === 'all' ?
        true :
        member.department === this.selectedDepartment;

      return matchesSearch && matchesDepartment;
    });
  }

  /**
   * Reset all filters
   */
  resetFilters(): void {
    this.searchTerm = '';
    this.selectedDepartment = 'all';
    this.filteredFaculty = [...this.facultyMembers];
  }

  /**
   * Get department name by id
   */
  getDepartmentName(departmentId: string): string {
    const department = this.departments.find(dept => dept.id === departmentId);
    return department ? department.name : '';
  }
}