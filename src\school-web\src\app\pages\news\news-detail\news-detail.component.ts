import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';

interface NewsArticle {
  id: string;
  title: string;
  date: Date;
  author: string;
  category: string;
  image: string;
  content: string[];
  tags: string[];
}

interface RelatedArticle {
  id: string;
  title: string;
  date: Date;
  image: string;
}

@Component({
  selector: 'app-news-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatDividerModule,
    TranslateModule
  ],
  templateUrl: './news-detail.component.html',
  styleUrls: ['./news-detail.component.scss']
})
export class NewsDetailComponent implements OnInit {
  articleId: string = '';
  article: NewsArticle | null = null;
  relatedArticles: RelatedArticle[] = [];

  // Mock data for demonstration
  mockArticles: NewsArticle[] = [
    {
      id: '1',
      title: 'School Wins National Science Competition',
      date: new Date('2023-09-15'),
      author: 'Admin',
      category: 'Achievements',
      image: 'assets/images/news/science-competition.jpg',
      content: [
        'We are proud to announce that our school\'s science team has won the National Science Competition 2023. The team, consisting of five students from grades 10 and 11, competed against 50 other schools from across the country.',
        'The competition, held at the National Science Center, required participants to demonstrate their knowledge in various scientific disciplines, including physics, chemistry, and biology. Our students impressed the judges with their innovative project on renewable energy solutions.',
        'Team leader Ahmed Rahman said, "We worked really hard for this competition. Our teachers provided excellent guidance, and we spent countless hours in the lab perfecting our project. It\'s an incredible feeling to have our work recognized at the national level."',
        'The winning project, titled "Sustainable Energy Solutions for Rural Communities," proposed a hybrid solar-wind energy system that could be implemented in remote areas with limited access to electricity. The judges praised the project for its practical application and thorough research.',
        'Principal Dr. Khan expressed her pride in the team\'s achievement: "This victory reflects the dedication of our students and the quality of our science program. We encourage our students to apply their knowledge to real-world problems, and this project exemplifies that approach."',
        'The team will now represent the country at the International Science Olympiad scheduled to take place in Singapore next month. The school community is rallying behind them as they prepare for this prestigious event.',
        'This achievement adds to our school\'s growing list of accolades in STEM fields and reinforces our commitment to excellence in science education.'
      ],
      tags: ['Science', 'Competition', 'Achievement', 'STEM']
    },
    {
      id: '2',
      title: 'Annual Cultural Festival Celebrates Diversity',
      date: new Date('2023-08-20'),
      author: 'Cultural Committee',
      category: 'Events',
      image: 'assets/images/news/cultural-festival.jpg',
      content: [
        'Our school\'s Annual Cultural Festival was held last weekend, showcasing the rich diversity of our student body. The two-day event featured performances, exhibitions, and activities representing cultures from around the world.',
        'The festival opened with a colorful parade of traditional costumes, followed by dance performances that took the audience on a journey across continents. Students performed classical dances from South Asia, contemporary moves from the West, and traditional folk dances from various regions.',
        'The food fair was another highlight, offering a taste of international cuisines prepared by students and their families. Visitors enjoyed sampling dishes from Bangladesh, India, China, Italy, and many other countries.',
        'Art exhibitions displayed student work inspired by different cultural traditions, while workshops allowed participants to try their hand at crafts such as origami, henna design, and calligraphy.',
        'Ms. Fatima, the Cultural Committee Chairperson, commented, "The festival is more than just entertainment; it\'s an educational experience that promotes understanding and appreciation of different cultures. In today\'s globalized world, this kind of cultural awareness is invaluable."',
        'Parents and community members who attended the event expressed their appreciation for the school\'s commitment to celebrating diversity. One parent noted, "It\'s wonderful to see the children learning about and respecting each other\'s cultural backgrounds. This festival creates a sense of unity while honoring our differences."',
        'The festival concluded with a grand finale featuring a collaborative performance that combined elements from various cultural traditions, symbolizing the harmony that can be achieved through mutual respect and understanding.'
      ],
      tags: ['Culture', 'Festival', 'Diversity', 'Arts']
    },
    {
      id: '3',
      title: 'New Sports Complex Inauguration',
      date: new Date('2023-07-10'),
      author: 'Sports Department',
      category: 'Facilities',
      image: 'assets/images/news/sports-complex.jpg',
      content: [
        'We are excited to announce the inauguration of our new state-of-the-art sports complex. The facility was officially opened in a ceremony attended by students, staff, parents, and distinguished guests from the sports community.',
        'The complex includes an Olympic-sized swimming pool, a multipurpose indoor court for basketball, volleyball, and badminton, a fully equipped gymnasium, and outdoor fields for football, cricket, and athletics. These facilities will support our comprehensive sports program and provide students with opportunities to develop their athletic abilities.',
        'During the inauguration ceremony, the school\'s sports teams demonstrated the various facilities through exhibition matches and performances. The swimming team showcased their skills in the new pool, while the basketball team played an exciting exhibition game on the new court.',
        'The sports complex is equipped with modern amenities, including digital scoreboards, professional-grade equipment, and spectator seating. The gymnasium features the latest fitness equipment to support strength and conditioning programs for our athletes.',
        'Physical Education Director Mr. Rahman emphasized the importance of sports in education: "Physical activity is essential for the holistic development of students. This new complex will not only help us nurture sporting talent but also promote health and fitness among all students."',
        'The complex will also be available to the wider community during specified hours, as part of the school\'s commitment to community engagement. Local sports clubs and organizations will have the opportunity to use the facilities for training and competitions.',
        'The construction of the sports complex was made possible through the generous contributions of alumni, parents, and corporate sponsors, demonstrating the strong support for sports and physical education in our school community.'
      ],
      tags: ['Sports', 'Facilities', 'Physical Education', 'Infrastructure']
    }
  ];

  constructor(private route: ActivatedRoute) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.articleId = params['id'];
      this.loadArticle();
    });
  }

  loadArticle(): void {
    // In a real application, you would fetch the article from a service
    this.article = this.mockArticles.find(article => article.id === this.articleId) || null;
    
    if (this.article) {
      // Get related articles (excluding the current one)
      this.relatedArticles = this.mockArticles
        .filter(article => article.id !== this.articleId)
        .map(article => ({
          id: article.id,
          title: article.title,
          date: article.date,
          image: article.image
        }));
    }
  }

  shareOnFacebook(): void {
    const url = encodeURIComponent(window.location.href);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
  }

  shareOnTwitter(): void {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent(this.article?.title || '');
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
  }

  shareOnLinkedIn(): void {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(this.article?.title || '');
    window.open(`https://www.linkedin.com/shareArticle?mini=true&url=${url}&title=${title}`, '_blank');
  }

  shareByEmail(): void {
    const subject = encodeURIComponent(this.article?.title || 'Interesting article');
    const body = encodeURIComponent(`Check out this article: ${window.location.href}`);
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
  }
}
