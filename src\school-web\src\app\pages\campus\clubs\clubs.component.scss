@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section styles are now handled by the DefaultHeroComponent

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Section Styles
section {
  padding: $section-padding;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: $dark-gray;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Introduction Section
.intro-section {
  background-color: $white;

  .intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Clubs Section
.clubs-section {
  background-color: $light-gray;

  // Loading and error containers
  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    min-height: 300px;
  }

  .error-container {
    p {
      font-size: 1.2rem;
      color: $dark-gray;
      margin-bottom: 20px;
      text-align: center;
    }

    button {
      padding: 8px 24px;
    }
  }

  ::ng-deep .mat-mdc-tab-header {
    margin-bottom: 30px;
  }

  .clubs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    padding: 20px;

    .club-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .club-image {
        height: 200px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 10px;
          color: $text-color;
        }

        .club-description {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 20px;
          flex-grow: 1;
        }

        .club-details {
          .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;

            &:last-child {
              margin-bottom: 0;
            }

            mat-icon {
              color: $primary-color;
              margin-right: 10px;
              font-size: 20px;
              height: 20px;
              width: 20px;
            }

            span {
              color: $dark-gray;
            }
          }
        }
      }
    }
  }
}

// Start Club Section
.start-club-section {
  background-color: $white;

  .steps-container {
    max-width: 800px;
    margin: 40px auto 30px;

    .step-item {
      display: flex;
      margin-bottom: 30px;

      &:last-child {
        margin-bottom: 0;
      }

      .step-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        background-color: $primary-color;
        color: $white;
        border-radius: 50%;
        font-size: 1.5rem;
        font-weight: bold;
        margin-right: 20px;
        flex-shrink: 0;
      }

      .step-content {
        flex-grow: 1;

        h3 {
          font-size: 1.3rem;
          margin-bottom: 10px;
          color: $text-color;
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }

  .start-club-cta {
    text-align: center;
    margin-top: 40px;

    a {
      padding: 10px 30px;
      font-size: 1.1rem;
    }
  }
}

// Leadership Section
.leadership-section {
  background-color: $light-gray;

  .leadership-container {
    max-width: 800px;
    margin: 40px auto 0;

    ::ng-deep .mat-expansion-panel {
      margin-bottom: 15px;
      border-radius: $border-radius;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ::ng-deep .mat-expansion-panel-header {
      padding: 20px;
    }

    ::ng-deep .mat-expansion-panel-header-title {
      color: $text-color;
      font-weight: 500;
      font-size: 1.1rem;
    }

    .role-responsibilities {
      h4 {
        font-size: 1.1rem;
        margin-bottom: 15px;
        color: $text-color;
      }

      ul {
        padding-left: 20px;
        margin-bottom: 0;

        li {
          margin-bottom: 10px;
          color: $dark-gray;
          line-height: 1.5;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

// Benefits Section
.benefits-section {
  background-color: $white;

  .benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .benefit-item {
      text-align: center;
      padding: 30px;
      border-radius: $border-radius;
      background-color: $light-gray;
      transition: transform 0.3s, box-shadow 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: $box-shadow;
      }

      .benefit-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 70px;
        height: 70px;
        background-color: $primary-color;
        border-radius: 50%;
        margin: 0 auto 20px;

        mat-icon {
          font-size: 35px;
          height: 35px;
          width: 35px;
          color: $white;
        }
      }

      h3 {
        font-size: 1.3rem;
        margin-bottom: 15px;
        color: $text-color;
      }

      p {
        color: $dark-gray;
        line-height: 1.6;
        margin-bottom: 0;
      }
    }
  }
}

// Contact Section
.contact-section {
  background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -15%));
  color: $white;

  .contact-content {
    max-width: 800px;
    margin: 0 auto;

    h2 {
      color: $white;

      &:after {
        background-color: $white;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
      text-align: center;
    }

    .contact-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;

      .contact-item {
        display: flex;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: $border-radius;
        padding: 20px;

        mat-icon {
          font-size: 30px;
          height: 30px;
          width: 30px;
          margin-right: 20px;
          margin-top: 5px;
        }

        .contact-details {
          h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
          }

          p {
            font-size: 1rem;
            margin-bottom: 5px;
            text-align: left;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  // Hero section styles removed

  section {
    padding: 60px 0;

    h2 {
      font-size: 2rem;
    }
  }

  .clubs-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  // Hero section styles removed

  .step-item {
    flex-direction: column;

    .step-number {
      margin-right: 0;
      margin-bottom: 15px;
    }
  }

  .contact-info {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .hero-section {
    height: 250px;

    .hero-content h1 {
      font-size: 1.8rem;
    }
  }

  section h2 {
    font-size: 1.8rem;
  }

  .clubs-grid, .benefits-grid {
    grid-template-columns: 1fr;
  }
}
