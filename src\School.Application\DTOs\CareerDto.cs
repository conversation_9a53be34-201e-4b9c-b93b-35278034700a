using School.Application.DTOs.Common;
using School.Domain.Enums;

namespace School.Application.DTOs;

public class CareerDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Responsibilities { get; set; } = string.Empty;
    public string Qualifications { get; set; } = string.Empty;
    public string Experience { get; set; } = string.Empty;
    public string Salary { get; set; } = string.Empty;
    public DateTime ApplicationDeadline { get; set; }
    public DateTime PostedDate { get; set; }
    public CareerStatus Status { get; set; }
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string ApplicationUrl { get; set; } = string.Empty;
    public bool IsFeatured { get; set; }
    public int Vacancies { get; set; }
    public string EmploymentType { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public List<CareerTranslationDto> Translations { get; set; } = new List<CareerTranslationDto>();
}

public class CareerDetailDto : CareerDto
{
    public int ApplicationCount { get; set; }
    public List<CareerApplicationDto> Applications { get; set; } = new List<CareerApplicationDto>();
}

public class CareerTranslationDto
{
    public Guid Id { get; set; }
    public Guid CareerId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Responsibilities { get; set; } = string.Empty;
    public string Qualifications { get; set; } = string.Empty;
    public string Experience { get; set; } = string.Empty;
    public string Salary { get; set; } = string.Empty;
    public string EmploymentType { get; set; } = string.Empty;
}

public class CreateCareerDto
{
    public string Title { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Responsibilities { get; set; } = string.Empty;
    public string Qualifications { get; set; } = string.Empty;
    public string Experience { get; set; } = string.Empty;
    public string Salary { get; set; } = string.Empty;
    public DateTime ApplicationDeadline { get; set; }
    public DateTime PostedDate { get; set; } = DateTime.UtcNow;
    public CareerStatus Status { get; set; } = CareerStatus.Open;
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string ApplicationUrl { get; set; } = string.Empty;
    public bool IsFeatured { get; set; } = false;
    public int Vacancies { get; set; } = 1;
    public string EmploymentType { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public List<CreateCareerTranslationDto>? Translations { get; set; }
}

public class UpdateCareerDto
{
    public string Title { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Responsibilities { get; set; } = string.Empty;
    public string Qualifications { get; set; } = string.Empty;
    public string Experience { get; set; } = string.Empty;
    public string Salary { get; set; } = string.Empty;
    public DateTime ApplicationDeadline { get; set; }
    public CareerStatus Status { get; set; }
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string ApplicationUrl { get; set; } = string.Empty;
    public bool IsFeatured { get; set; }
    public int Vacancies { get; set; }
    public string EmploymentType { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

public class CreateCareerTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Responsibilities { get; set; } = string.Empty;
    public string Qualifications { get; set; } = string.Empty;
    public string Experience { get; set; } = string.Empty;
    public string Salary { get; set; } = string.Empty;
    public string EmploymentType { get; set; } = string.Empty;
}

public class UpdateCareerTranslationDto
{
    public string Title { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Responsibilities { get; set; } = string.Empty;
    public string Qualifications { get; set; } = string.Empty;
    public string Experience { get; set; } = string.Empty;
    public string Salary { get; set; } = string.Empty;
    public string EmploymentType { get; set; } = string.Empty;
}

public class CareerApplicationDto
{
    public Guid Id { get; set; }
    public Guid CareerId { get; set; }
    public string ApplicantName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string CoverLetter { get; set; } = string.Empty;
    public string ResumeFilePath { get; set; } = string.Empty;
    public ApplicationStatus Status { get; set; }
    public string ReviewComments { get; set; } = string.Empty;
    public string ReviewedBy { get; set; } = string.Empty;
    public DateTime? ReviewedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public CareerDto? Career { get; set; }
}

public class CreateCareerApplicationDto
{
    public Guid CareerId { get; set; }
    public string ApplicantName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string CoverLetter { get; set; } = string.Empty;
    public string ResumeFilePath { get; set; } = string.Empty;
}

public class UpdateCareerApplicationStatusDto
{
    public ApplicationStatus Status { get; set; }
    public string ReviewComments { get; set; } = string.Empty;
    public string ReviewedBy { get; set; } = string.Empty;
}

/// <summary>
/// Filter DTO for career queries
/// </summary>
public class CareerFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filter by title
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// Filter by department
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// Filter by location
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// Filter by status
    /// </summary>
    public CareerStatus? Status { get; set; }

    /// <summary>
    /// Filter by featured status
    /// </summary>
    public bool? IsFeatured { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter by employment type
    /// </summary>
    public string? EmploymentType { get; set; }

    /// <summary>
    /// Filter by posted date (from)
    /// </summary>
    public DateTime? PostedDateFrom { get; set; }

    /// <summary>
    /// Filter by posted date (to)
    /// </summary>
    public DateTime? PostedDateTo { get; set; }

    /// <summary>
    /// Filter by application deadline (from)
    /// </summary>
    public DateTime? DeadlineFrom { get; set; }

    /// <summary>
    /// Filter by application deadline (to)
    /// </summary>
    public DateTime? DeadlineTo { get; set; }
}

/// <summary>
/// Filter DTO for career application queries
/// </summary>
public class CareerApplicationFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filter by career ID
    /// </summary>
    public Guid? CareerId { get; set; }

    /// <summary>
    /// Filter by applicant name
    /// </summary>
    public string? ApplicantName { get; set; }

    /// <summary>
    /// Filter by email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Filter by application status
    /// </summary>
    public ApplicationStatus? Status { get; set; }

    /// <summary>
    /// Filter by application date (from)
    /// </summary>
    public DateTime? ApplicationDateFrom { get; set; }

    /// <summary>
    /// Filter by application date (to)
    /// </summary>
    public DateTime? ApplicationDateTo { get; set; }
}
