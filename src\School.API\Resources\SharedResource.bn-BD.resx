<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Authentication Messages -->
  <data name="Auth.LoginSuccess" xml:space="preserve">
    <value>সফলভাবে লগইন হয়েছে</value>
  </data>
  <data name="Auth.LoginFailed" xml:space="preserve">
    <value>ভুল তথ্য প্রদান করা হয়েছে</value>
  </data>
  <data name="Auth.MfaRequired" xml:space="preserve">
    <value>MFA যাচাইকরণ প্রয়োজন</value>
  </data>
  <data name="Auth.TokenExpired" xml:space="preserve">
    <value>টোকেনের মেয়াদ শেষ হয়ে গেছে</value>
  </data>
  <data name="Auth.Unauthorized" xml:space="preserve">
    <value>অননুমোদিত প্রবেশ</value>
  </data>
  <data name="Auth.RegistrationSuccess" xml:space="preserve">
    <value>ব্যবহারকারী সফলভাবে নিবন্ধিত হয়েছে</value>
  </data>
  <data name="Auth.RegistrationFailed" xml:space="preserve">
    <value>নিবন্ধন ব্যর্থ হয়েছে</value>
  </data>
  
  <!-- Validation Messages -->
  <data name="Validation.Required" xml:space="preserve">
    <value>{0} প্রয়োজন</value>
  </data>
  <data name="Validation.EmailInvalid" xml:space="preserve">
    <value>দয়া করে একটি বৈধ ইমেইল ঠিকানা প্রবেশ করান</value>
  </data>
  <data name="Validation.PasswordTooShort" xml:space="preserve">
    <value>পাসওয়ার্ড কমপক্ষে {0} অক্ষরের হতে হবে</value>
  </data>
  <data name="Validation.PasswordComplexity" xml:space="preserve">
    <value>পাসওয়ার্ডে বড় হাতের অক্ষর, ছোট হাতের অক্ষর, সংখ্যা এবং বিশেষ অক্ষর থাকতে হবে</value>
  </data>
  
  <!-- Academic Year Messages -->
  <data name="AcademicYear.Created" xml:space="preserve">
    <value>শিক্ষাবর্ষ সফলভাবে তৈরি হয়েছে</value>
  </data>
  <data name="AcademicYear.Updated" xml:space="preserve">
    <value>শিক্ষাবর্ষ সফলভাবে আপডেট হয়েছে</value>
  </data>
  <data name="AcademicYear.Deleted" xml:space="preserve">
    <value>শিক্ষাবর্ষ সফলভাবে মুছে ফেলা হয়েছে</value>
  </data>
  <data name="AcademicYear.NotFound" xml:space="preserve">
    <value>শিক্ষাবর্ষ পাওয়া যায়নি</value>
  </data>
  <data name="AcademicYear.AlreadyExists" xml:space="preserve">
    <value>এই সময়ের জন্য শিক্ষাবর্ষ ইতিমধ্যে বিদ্যমান</value>
  </data>
  
  <!-- Holiday Messages -->
  <data name="Holiday.Created" xml:space="preserve">
    <value>ছুটির দিন সফলভাবে তৈরি হয়েছে</value>
  </data>
  <data name="Holiday.Updated" xml:space="preserve">
    <value>ছুটির দিন সফলভাবে আপডেট হয়েছে</value>
  </data>
  <data name="Holiday.Deleted" xml:space="preserve">
    <value>ছুটির দিন সফলভাবে মুছে ফেলা হয়েছে</value>
  </data>
  <data name="Holiday.NotFound" xml:space="preserve">
    <value>ছুটির দিন পাওয়া যায়নি</value>
  </data>
  <data name="Holiday.DateConflict" xml:space="preserve">
    <value>ছুটির দিনের তারিখ বিদ্যমান ছুটির সাথে সংঘর্ষ</value>
  </data>
  
  <!-- Calendar Messages -->
  <data name="Calendar.EventCreated" xml:space="preserve">
    <value>ক্যালেন্ডার ইভেন্ট সফলভাবে তৈরি হয়েছে</value>
  </data>
  <data name="Calendar.EventUpdated" xml:space="preserve">
    <value>ক্যালেন্ডার ইভেন্ট সফলভাবে আপডেট হয়েছে</value>
  </data>
  <data name="Calendar.EventDeleted" xml:space="preserve">
    <value>ক্যালেন্ডার ইভেন্ট সফলভাবে মুছে ফেলা হয়েছে</value>
  </data>
  <data name="Calendar.EventNotFound" xml:space="preserve">
    <value>ক্যালেন্ডার ইভেন্ট পাওয়া যায়নি</value>
  </data>
  
  <!-- General Messages -->
  <data name="General.Success" xml:space="preserve">
    <value>অপারেশন সফলভাবে সম্পন্ন হয়েছে</value>
  </data>
  <data name="General.Error" xml:space="preserve">
    <value>আপনার অনুরোধ প্রক্রিয়াকরণে একটি ত্রুটি ঘটেছে</value>
  </data>
  <data name="General.NotFound" xml:space="preserve">
    <value>অনুরোধকৃত সম্পদ পাওয়া যায়নি</value>
  </data>
  <data name="General.BadRequest" xml:space="preserve">
    <value>অবৈধ অনুরোধ</value>
  </data>
  <data name="General.InternalError" xml:space="preserve">
    <value>অভ্যন্তরীণ সার্ভার ত্রুটি</value>
  </data>
</root>
