import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

export const authGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  console.log('AuthGuard - Checking authentication for route:', state.url);
  
  // Check if user is logged in
  if (authService.isLoggedIn()) {
    console.log('AuthGuard - User is authenticated, allowing access');
    return true;
  }

  console.log('AuthGuard - User is not authenticated, redirecting to login');
  
  // Store the attempted URL for redirecting after login
  router.navigate(['/login'], { 
    queryParams: { returnUrl: state.url } 
  });
  
  return false;
};
