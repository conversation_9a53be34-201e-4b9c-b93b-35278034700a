using School.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace School.Application.Features.Media
{
    public interface IMediaService
    {
        Task<(IEnumerable<MediaItemDto> MediaItems, int TotalCount)> GetAllMediaAsync(MediaFilterDto filter);
        Task<MediaItemDto?> GetMediaByIdAsync(Guid id);
        Task<Guid> UploadMediaAsync(MediaItemCreateDto mediaDto);
        Task<bool> UpdateMediaAsync(Guid id, MediaItemUpdateDto mediaDto);
        Task<bool> DeleteMediaAsync(Guid id);
    }
}