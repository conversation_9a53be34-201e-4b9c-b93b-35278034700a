using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ContentTranslationConfiguration : IEntityTypeConfiguration<ContentTranslation>
{
    public void Configure(EntityTypeBuilder<ContentTranslation> builder)
    {
        builder.Property(t => t.LanguageCode)
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(t => t.Title)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(t => t.MetaDescription)
            .HasMaxLength(500);

        builder.HasOne(t => t.Content)
            .WithMany(t => t.Translations)
            .HasForeignKey(t => t.ContentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(t => new { t.ContentId, t.LanguageCode })
            .IsUnique();

        builder.HasQueryFilter(ct => !ct.IsDeleted);
    }
}
