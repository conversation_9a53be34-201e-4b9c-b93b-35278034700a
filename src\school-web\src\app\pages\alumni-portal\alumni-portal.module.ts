import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Angular Material Modules
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatNativeDateModule } from '@angular/material/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';
import { MatDialogModule } from '@angular/material/dialog';

import { AlumniPortalComponent } from './alumni-portal.component';
import { AlumniDashboardComponent } from './alumni-dashboard/alumni-dashboard.component';
import { AlumniProfileComponent } from './alumni-profile/alumni-profile.component';
import { AlumniNetworkComponent } from './alumni-network/alumni-network.component';
import { AlumniEventsComponent } from './alumni-events/alumni-events.component';
import { AlumniDonationsComponent } from './alumni-donations/alumni-donations.component';
import { AlumniJobBoardComponent } from './alumni-job-board/alumni-job-board.component';

const routes: Routes = [
  {
    path: '',
    component: AlumniPortalComponent,
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: AlumniDashboardComponent },
      { path: 'profile', component: AlumniProfileComponent },
      { path: 'network', component: AlumniNetworkComponent },
      { path: 'events', component: AlumniEventsComponent },
      { path: 'donations', component: AlumniDonationsComponent },
      { path: 'jobs', component: AlumniJobBoardComponent }
    ]
  }
];

@NgModule({
  declarations: [
    // All components are now standalone
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatListModule,
    MatMenuModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatSidenavModule,
    MatSnackBarModule,
    MatSortModule,
    MatTableModule,
    MatTabsModule,
    MatToolbarModule,
    MatTooltipModule,
    MatNativeDateModule,
    MatExpansionModule,
    MatChipsModule,
    MatBadgeModule,
    MatDialogModule,

    // Import standalone components
    AlumniPortalComponent,
    AlumniDashboardComponent,
    AlumniProfileComponent,
    AlumniNetworkComponent,
    AlumniEventsComponent,
    AlumniDonationsComponent,
    AlumniJobBoardComponent
  ]
})
export class AlumniPortalModule { }
