<div class="fees-container">
  <h1 class="page-title">Tu<PERSON> & Fees</h1>

  <div *ngIf="loading.student" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error.student" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load student data. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadStudentData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading.student && !error.student && student" class="fees-content">
    <!-- Filter Form -->
    <mat-card class="filter-card">
      <mat-card-content>
        <form [formGroup]="filterForm" (ngSubmit)="applyFilter()">
          <div class="filter-form">
            <mat-form-field appearance="outline">
              <mat-label>Academic Year</mat-label>
              <input matInput type="number" formControlName="academicYear">
            </mat-form-field>

            <div class="filter-actions">
              <button mat-raised-button color="primary" type="submit">View Fees</button>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>

    <!-- Fee Summary -->
    <mat-card class="summary-card" *ngIf="!loading.fees && !error.fees && fees.length > 0">
      <mat-card-content>
        <div class="summary-grid">
          <div class="summary-item">
            <div class="summary-value">{{ calculateTotalFees() | currency:'BDT':'symbol':'1.0-0' }}</div>
            <div class="summary-label">Total Fees</div>
          </div>
          <div class="summary-item">
            <div class="summary-value paid">{{ calculateTotalPaid() | currency:'BDT':'symbol':'1.0-0' }}</div>
            <div class="summary-label">Total Paid</div>
          </div>
          <div class="summary-item">
            <div class="summary-value overdue">{{ calculateTotalDue() | currency:'BDT':'symbol':'1.0-0' }}</div>
            <div class="summary-label">Total Due</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Loading Indicator -->
    <div *ngIf="loading.fees" class="fees-loading">
      <mat-progress-bar mode="indeterminate"></mat-progress-bar>
    </div>

    <!-- Error Message -->
    <div *ngIf="error.fees" class="fees-error">
      <mat-error>
        <mat-icon>error</mat-icon>
        <span>Failed to load fee information. Please try again.</span>
        <button mat-button color="warn" (click)="loadFees()">Retry</button>
      </mat-error>
    </div>

    <!-- No Fees Message -->
    <div *ngIf="!loading.fees && !error.fees && fees.length === 0" class="no-fees">
      <mat-card>
        <mat-card-content>
          <p>No fee records found for the selected academic year.</p>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Fee Cards -->
    <div *ngIf="!loading.fees && !error.fees && fees.length > 0" class="fee-cards">
      <mat-card *ngFor="let fee of fees" class="fee-card" [ngClass]="{'overdue-card': isPastDue(fee.dueDate) && fee.status !== 2}">
        <mat-card-header>
          <mat-card-title>{{ getFeeTypeLabel(fee.type) }}</mat-card-title>
          <mat-card-subtitle>{{ fee.description }}</mat-card-subtitle>
          <div class="fee-status">
            <span class="status-badge" [ngClass]="getStatusClass(fee.status)">
              {{ getStatusLabel(fee.status) }}
            </span>
          </div>
        </mat-card-header>
        <mat-card-content>
          <div class="fee-details">
            <div class="fee-amount">
              <span class="fee-label">Amount:</span>
              <span class="fee-value">{{ fee.amount | currency:'BDT':'symbol':'1.0-0' }}</span>
            </div>
            <div class="fee-paid">
              <span class="fee-label">Paid:</span>
              <span class="fee-value">{{ fee.paidAmount | currency:'BDT':'symbol':'1.0-0' }}</span>
            </div>
            <div class="fee-due">
              <span class="fee-label">Due:</span>
              <span class="fee-value">{{ fee.dueAmount | currency:'BDT':'symbol':'1.0-0' }}</span>
            </div>
            <div class="fee-due-date">
              <span class="fee-label">Due Date:</span>
              <span class="fee-value" [ngClass]="{'overdue-date': isPastDue(fee.dueDate) && fee.status !== 2}">
                {{ fee.dueDate | date:'mediumDate' }}
                <mat-icon *ngIf="isPastDue(fee.dueDate) && fee.status !== 2" class="overdue-icon">warning</mat-icon>
              </span>
            </div>
            <div class="fee-paid-date" *ngIf="fee.paidDate">
              <span class="fee-label">Paid Date:</span>
              <span class="fee-value">{{ fee.paidDate | date:'mediumDate' }}</span>
            </div>
            <div class="fee-payment-method" *ngIf="fee.paymentMethod">
              <span class="fee-label">Payment Method:</span>
              <span class="fee-value">{{ fee.paymentMethod }}</span>
            </div>
            <div class="fee-transaction" *ngIf="fee.transactionId">
              <span class="fee-label">Transaction ID:</span>
              <span class="fee-value">{{ fee.transactionId }}</span>
            </div>
            <div class="fee-remarks" *ngIf="fee.remarks">
              <span class="fee-label">Remarks:</span>
              <span class="fee-value">{{ fee.remarks }}</span>
            </div>
          </div>
        </mat-card-content>
        <mat-card-actions *ngIf="fee.status !== 2 && fee.status !== 4">
          <button mat-button color="primary">Pay Now</button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</div>
