<div class="staff-portal-container">
  <!-- Header -->
  <div class="portal-header">
    <div class="header-content">
      <div class="user-info">
        <div class="avatar">
          <mat-icon>person</mat-icon>
        </div>
        <div class="user-details">
          <h2 class="user-name">{{ currentUser?.firstName }} {{ currentUser?.lastName }}</h2>
          <p class="user-role">{{ 'STAFF_PORTAL.ROLE_' + currentUser?.role | translate }}</p>
        </div>
      </div>
      
      <div class="header-actions">
        <button mat-icon-button [matMenuTriggerFor]="userMenu">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #userMenu="matMenu">
          <button mat-menu-item (click)="viewProfile()">
            <mat-icon>person</mat-icon>
            <span>{{ 'STAFF_PORTAL.PROFILE' | translate }}</span>
          </button>
          <button mat-menu-item (click)="changePassword()">
            <mat-icon>lock</mat-icon>
            <span>{{ 'STAFF_PORTAL.CHANGE_PASSWORD' | translate }}</span>
          </button>
          <mat-divider></mat-divider>
          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            <span>{{ 'STAFF_PORTAL.LOGOUT' | translate }}</span>
          </button>
        </mat-menu>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="portal-content">
    <!-- Navigation Sidebar -->
    <div class="sidebar">
      <nav class="nav-menu">
        <div class="nav-section">
          <h3 class="nav-section-title">{{ 'STAFF_PORTAL.DASHBOARD' | translate }}</h3>
          <a mat-button class="nav-item" [class.active]="activeSection === 'dashboard'" (click)="setActiveSection('dashboard')">
            <mat-icon>dashboard</mat-icon>
            <span>{{ 'STAFF_PORTAL.OVERVIEW' | translate }}</span>
          </a>
        </div>

        <!-- Academic Management (Faculty, Admin, Manager) -->
        <div class="nav-section" *ngIf="hasPermission(['FACULTY', 'ADMIN', 'MANAGER'])">
          <h3 class="nav-section-title">{{ 'STAFF_PORTAL.ACADEMIC' | translate }}</h3>
          
          <a mat-button class="nav-item" [class.active]="activeSection === 'classes'" 
             (click)="setActiveSection('classes')" *ngIf="hasPermission(['FACULTY', 'ADMIN'])">
            <mat-icon>class</mat-icon>
            <span>{{ 'STAFF_PORTAL.CLASSES' | translate }}</span>
          </a>
          
          <a mat-button class="nav-item" [class.active]="activeSection === 'students'" 
             (click)="setActiveSection('students')" *ngIf="hasPermission(['FACULTY', 'ADMIN', 'MANAGER'])">
            <mat-icon>school</mat-icon>
            <span>{{ 'STAFF_PORTAL.STUDENTS' | translate }}</span>
          </a>
          
          <a mat-button class="nav-item" [class.active]="activeSection === 'attendance'" 
             (click)="setActiveSection('attendance')" *ngIf="hasPermission(['FACULTY', 'ADMIN'])">
            <mat-icon>how_to_reg</mat-icon>
            <span>{{ 'STAFF_PORTAL.ATTENDANCE' | translate }}</span>
          </a>
          
          <a mat-button class="nav-item" [class.active]="activeSection === 'grades'" 
             (click)="setActiveSection('grades')" *ngIf="hasPermission(['FACULTY', 'ADMIN'])">
            <mat-icon>grade</mat-icon>
            <span>{{ 'STAFF_PORTAL.GRADES' | translate }}</span>
          </a>
        </div>

        <!-- Content Management (Editor, Admin) -->
        <div class="nav-section" *ngIf="hasPermission(['EDITOR', 'ADMIN'])">
          <h3 class="nav-section-title">{{ 'STAFF_PORTAL.CONTENT' | translate }}</h3>
          
          <a mat-button class="nav-item" [class.active]="activeSection === 'news'" 
             (click)="setActiveSection('news')">
            <mat-icon>article</mat-icon>
            <span>{{ 'STAFF_PORTAL.NEWS' | translate }}</span>
          </a>
          
          <a mat-button class="nav-item" [class.active]="activeSection === 'events'" 
             (click)="setActiveSection('events')">
            <mat-icon>event</mat-icon>
            <span>{{ 'STAFF_PORTAL.EVENTS' | translate }}</span>
          </a>
          
          <a mat-button class="nav-item" [class.active]="activeSection === 'notices'" 
             (click)="setActiveSection('notices')">
            <mat-icon>campaign</mat-icon>
            <span>{{ 'STAFF_PORTAL.NOTICES' | translate }}</span>
          </a>
        </div>

        <!-- Administration (Admin only) -->
        <div class="nav-section" *ngIf="hasPermission(['ADMIN'])">
          <h3 class="nav-section-title">{{ 'STAFF_PORTAL.ADMINISTRATION' | translate }}</h3>
          
          <a mat-button class="nav-item" [class.active]="activeSection === 'users'" 
             (click)="setActiveSection('users')">
            <mat-icon>people</mat-icon>
            <span>{{ 'STAFF_PORTAL.USER_MANAGEMENT' | translate }}</span>
          </a>
          
          <a mat-button class="nav-item" [class.active]="activeSection === 'reports'" 
             (click)="setActiveSection('reports')">
            <mat-icon>assessment</mat-icon>
            <span>{{ 'STAFF_PORTAL.REPORTS' | translate }}</span>
          </a>
          
          <a mat-button class="nav-item" [class.active]="activeSection === 'settings'" 
             (click)="setActiveSection('settings')">
            <mat-icon>settings</mat-icon>
            <span>{{ 'STAFF_PORTAL.SETTINGS' | translate }}</span>
          </a>
        </div>

        <!-- General -->
        <div class="nav-section">
          <h3 class="nav-section-title">{{ 'STAFF_PORTAL.GENERAL' | translate }}</h3>
          
          <a mat-button class="nav-item" [class.active]="activeSection === 'calendar'" 
             (click)="setActiveSection('calendar')">
            <mat-icon>calendar_today</mat-icon>
            <span>{{ 'STAFF_PORTAL.CALENDAR' | translate }}</span>
          </a>
          
          <a mat-button class="nav-item" [class.active]="activeSection === 'messages'" 
             (click)="setActiveSection('messages')">
            <mat-icon>message</mat-icon>
            <span>{{ 'STAFF_PORTAL.MESSAGES' | translate }}</span>
          </a>
        </div>
      </nav>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Dashboard -->
      <div class="content-section" *ngIf="activeSection === 'dashboard'">
        <div class="section-header">
          <h2>{{ 'STAFF_PORTAL.DASHBOARD_TITLE' | translate }}</h2>
          <p>{{ 'STAFF_PORTAL.WELCOME_MESSAGE' | translate }}</p>
        </div>

        <div class="dashboard-cards">
          <mat-card class="dashboard-card" *ngIf="hasPermission(['FACULTY', 'ADMIN'])">
            <mat-card-header>
              <mat-icon mat-card-avatar>class</mat-icon>
              <mat-card-title>{{ 'STAFF_PORTAL.MY_CLASSES' | translate }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="card-stat">
                <span class="stat-number">{{ dashboardStats.classes || 0 }}</span>
                <span class="stat-label">{{ 'STAFF_PORTAL.ACTIVE_CLASSES' | translate }}</span>
              </div>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button (click)="setActiveSection('classes')">{{ 'STAFF_PORTAL.VIEW_ALL' | translate }}</button>
            </mat-card-actions>
          </mat-card>

          <mat-card class="dashboard-card" *ngIf="hasPermission(['FACULTY', 'ADMIN', 'MANAGER'])">
            <mat-card-header>
              <mat-icon mat-card-avatar>school</mat-icon>
              <mat-card-title>{{ 'STAFF_PORTAL.STUDENTS' | translate }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="card-stat">
                <span class="stat-number">{{ dashboardStats.students || 0 }}</span>
                <span class="stat-label">{{ 'STAFF_PORTAL.TOTAL_STUDENTS' | translate }}</span>
              </div>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button (click)="setActiveSection('students')">{{ 'STAFF_PORTAL.VIEW_ALL' | translate }}</button>
            </mat-card-actions>
          </mat-card>

          <mat-card class="dashboard-card" *ngIf="hasPermission(['EDITOR', 'ADMIN'])">
            <mat-card-header>
              <mat-icon mat-card-avatar>article</mat-icon>
              <mat-card-title>{{ 'STAFF_PORTAL.CONTENT' | translate }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="card-stat">
                <span class="stat-number">{{ dashboardStats.content || 0 }}</span>
                <span class="stat-label">{{ 'STAFF_PORTAL.PUBLISHED_CONTENT' | translate }}</span>
              </div>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button (click)="setActiveSection('news')">{{ 'STAFF_PORTAL.MANAGE' | translate }}</button>
            </mat-card-actions>
          </mat-card>

          <mat-card class="dashboard-card">
            <mat-card-header>
              <mat-icon mat-card-avatar>event</mat-icon>
              <mat-card-title>{{ 'STAFF_PORTAL.UPCOMING_EVENTS' | translate }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="card-stat">
                <span class="stat-number">{{ dashboardStats.events || 0 }}</span>
                <span class="stat-label">{{ 'STAFF_PORTAL.THIS_MONTH' | translate }}</span>
              </div>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button (click)="setActiveSection('calendar')">{{ 'STAFF_PORTAL.VIEW_CALENDAR' | translate }}</button>
            </mat-card-actions>
          </mat-card>
        </div>
      </div>

      <!-- Other sections will be loaded dynamically -->
      <div class="content-section" *ngIf="activeSection !== 'dashboard'">
        <div class="section-header">
          <h2>{{ getSectionTitle() | translate }}</h2>
        </div>
        
        <div class="section-content">
          <div class="coming-soon">
            <mat-icon>construction</mat-icon>
            <h3>{{ 'STAFF_PORTAL.COMING_SOON' | translate }}</h3>
            <p>{{ 'STAFF_PORTAL.FEATURE_DEVELOPMENT' | translate }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
