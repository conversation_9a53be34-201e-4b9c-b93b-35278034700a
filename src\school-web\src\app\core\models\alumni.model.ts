import { BaseFilter } from './base.model';
import { MediaItem } from './media.model';

export interface Alumni {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  graduationYear: number;
  profession: string;
  organization?: string;
  designation?: string;
  biography: string;
  achievements: string;
  linkedInProfile?: string;
  facebookProfile?: string;
  twitterProfile?: string;
  isFeatured: boolean;
  isActive: boolean;
  displayOrder: number;
  profileImageId?: number;
  profileImage?: MediaItem;
  createdAt: Date;
  updatedAt?: Date;
  translations: AlumniTranslation[];
  testimonials?: AlumniTestimonial[];
}

export interface AlumniTranslation {
  id: number;
  alumniId: number;
  languageCode: string;
  name: string;
  profession?: string;
  organization?: string;
  designation?: string;
  biography?: string;
  achievements?: string;
}

export interface AlumniTestimonial {
  id: number;
  alumniId: number;
  content: string;
  isApproved: boolean;
  isActive: boolean;
  displayOrder: number;
  createdAt: Date;
  updatedAt?: Date;
  translations: AlumniTestimonialTranslation[];
}

export interface AlumniTestimonialTranslation {
  id: number;
  alumniTestimonialId: number;
  languageCode: string;
  content: string;
}

export interface AlumniFilter extends BaseFilter {
  name?: string;
  graduationYear?: number;
  profession?: string;
  organization?: string;
  isFeatured?: boolean;
  isActive?: boolean;
}

export interface CreateAlumni {
  name: string;
  email?: string;
  phone?: string;
  graduationYear: number;
  profession: string;
  organization?: string;
  designation?: string;
  biography: string;
  achievements: string;
  linkedInProfile?: string;
  facebookProfile?: string;
  twitterProfile?: string;
  isFeatured: boolean;
  isActive: boolean;
  displayOrder: number;
  profileImageId?: number;
  translations?: CreateAlumniTranslation[];
}

export interface CreateAlumniTranslation {
  languageCode: string;
  name: string;
  profession?: string;
  organization?: string;
  designation?: string;
  biography?: string;
  achievements?: string;
}

export interface UpdateAlumni {
  name?: string;
  email?: string;
  phone?: string;
  graduationYear?: number;
  profession?: string;
  organization?: string;
  designation?: string;
  biography?: string;
  achievements?: string;
  linkedInProfile?: string;
  facebookProfile?: string;
  twitterProfile?: string;
  isFeatured?: boolean;
  isActive?: boolean;
  displayOrder?: number;
  profileImageId?: number;
}

export interface UpdateAlumniTranslation {
  name?: string;
  profession?: string;
  organization?: string;
  designation?: string;
  biography?: string;
  achievements?: string;
}

export interface CreateAlumniTestimonial {
  alumniId: number;
  content: string;
  isApproved: boolean;
  isActive: boolean;
  displayOrder: number;
  translations?: CreateAlumniTestimonialTranslation[];
}

export interface CreateAlumniTestimonialTranslation {
  languageCode: string;
  content: string;
}

export interface UpdateAlumniTestimonial {
  content?: string;
  isApproved?: boolean;
  isActive?: boolean;
  displayOrder?: number;
}

export interface AlumniEvent {
  id: number;
  title: string;
  date: Date;
  location: string;
  description: string;
  image?: string;
}
