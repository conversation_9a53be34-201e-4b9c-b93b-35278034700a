import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../../shared/components/default-hero/default-hero.component';

interface Program {
  name: string;
  description: string;
  instructor: string;
  image: string;
  category: string;
}

interface Event {
  title: string;
  date: string;
  time: string;
  location: string;
  description: string;
  image: string;
}

@Component({
  selector: 'app-arts',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatExpansionModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './arts.component.html',
  styleUrls: ['./arts.component.scss']
})
export class ArtsComponent {
  // Arts categories
  artsCategories = [
    'Visual Arts',
    'Music',
    'Theater',
    'Dance'
  ];

  // Arts programs
  programs: Program[] = [
    {
      name: 'Drawing & Painting',
      description: 'Students explore various drawing and painting techniques using pencil, charcoal, pastel, watercolor, acrylic, and oil paints, developing technical skills and creative expression.',
      instructor: 'Ms. Lee',
      image: 'assets/images/campus/arts/drawing.jpg',
      category: 'Visual Arts'
    },
    {
      name: 'Ceramics & Sculpture',
      description: 'This program introduces students to three-dimensional art forms through clay, wood, metal, and mixed media, teaching hand-building, wheel-throwing, carving, and assemblage techniques.',
      instructor: 'Mr. Rodriguez',
      image: 'assets/images/campus/arts/ceramics.jpg',
      category: 'Visual Arts'
    },
    {
      name: 'Photography',
      description: 'Students learn the fundamentals of photography, including composition, lighting, and digital editing, while developing their unique visual style and storytelling abilities.',
      instructor: 'Ms. Davis',
      image: 'assets/images/campus/arts/photography.jpg',
      category: 'Visual Arts'
    },
    {
      name: 'Digital Arts',
      description: 'This program explores digital design, animation, and multimedia art using industry-standard software and techniques, preparing students for careers in digital media.',
      instructor: 'Mr. Kim',
      image: 'assets/images/campus/arts/digital.jpg',
      category: 'Visual Arts'
    },
    {
      name: 'Concert Band',
      description: 'Students develop instrumental skills, ensemble playing, and music literacy through the study and performance of diverse band literature, from classical to contemporary pieces.',
      instructor: 'Mr. Wilson',
      image: 'assets/images/campus/arts/band.jpg',
      category: 'Music'
    },
    {
      name: 'Orchestra',
      description: 'Our string orchestra program focuses on developing technique, musicianship, and collaborative performance skills through a variety of classical and modern repertoire.',
      instructor: 'Ms. Thompson',
      image: 'assets/images/campus/arts/orchestra.jpg',
      category: 'Music'
    },
    {
      name: 'Choir',
      description: 'Students develop vocal technique, music reading skills, and ensemble singing through diverse choral literature spanning different periods, cultures, and styles.',
      instructor: 'Dr. Martinez',
      image: 'assets/images/campus/arts/choir.jpg',
      category: 'Music'
    },
    {
      name: 'Jazz Ensemble',
      description: 'This specialized ensemble explores jazz styles, improvisation, and performance techniques, offering students the opportunity to develop as creative musicians.',
      instructor: 'Mr. Brown',
      image: 'assets/images/campus/arts/jazz.jpg',
      category: 'Music'
    },
    {
      name: 'Acting',
      description: 'Students develop acting techniques, character development, and stage presence through scene study, improvisation, and performance opportunities.',
      instructor: 'Ms. Williams',
      image: 'assets/images/campus/arts/acting.jpg',
      category: 'Theater'
    },
    {
      name: 'Technical Theater',
      description: 'This program teaches the behind-the-scenes aspects of theater production, including set design, lighting, sound, costumes, and stage management.',
      instructor: 'Mr. Garcia',
      image: 'assets/images/campus/arts/tech-theater.jpg',
      category: 'Theater'
    },
    {
      name: 'Playwriting',
      description: 'Students learn the fundamentals of dramatic writing, character development, dialogue, and story structure while creating original scripts for stage production.',
      instructor: 'Ms. Johnson',
      image: 'assets/images/campus/arts/playwriting.jpg',
      category: 'Theater'
    },
    {
      name: 'Musical Theater',
      description: 'This program combines acting, singing, and dancing, preparing students for musical productions and developing skills in all aspects of musical theater performance.',
      instructor: 'Mr. Taylor',
      image: 'assets/images/campus/arts/musical.jpg',
      category: 'Theater'
    },
    {
      name: 'Contemporary Dance',
      description: 'Students explore modern and contemporary dance techniques, improvisation, and choreography, developing physical skills, artistic expression, and performance abilities.',
      instructor: 'Ms. Clark',
      image: 'assets/images/campus/arts/contemporary.jpg',
      category: 'Dance'
    },
    {
      name: 'Ballet',
      description: 'This program focuses on classical ballet technique, alignment, strength, and artistry, providing a strong foundation for all dance forms.',
      instructor: 'Ms. Anderson',
      image: 'assets/images/campus/arts/ballet.jpg',
      category: 'Dance'
    },
    {
      name: 'World Dance',
      description: 'Students learn dance forms from various cultures around the world, gaining appreciation for diverse movement traditions and their cultural contexts.',
      instructor: 'Mr. Patel',
      image: 'assets/images/campus/arts/world-dance.jpg',
      category: 'Dance'
    },
    {
      name: 'Choreography',
      description: 'This advanced program teaches the principles of dance composition, spatial design, musicality, and creative process, culminating in original choreographic works.',
      instructor: 'Ms. Lopez',
      image: 'assets/images/campus/arts/choreography.jpg',
      category: 'Dance'
    }
  ];

  // Arts facilities
  facilities = [
    {
      name: 'Visual Arts Center',
      features: [
        'Painting and drawing studios',
        'Ceramics studio with kilns',
        'Photography darkroom',
        'Digital arts lab',
        'Student gallery space'
      ],
      image: 'assets/images/campus/arts/visual-arts-center.jpg'
    },
    {
      name: 'Music Building',
      features: [
        'Band and orchestra rehearsal rooms',
        'Choir room with tiered seating',
        'Recording studio',
        'Individual practice rooms',
        'Music technology lab'
      ],
      image: 'assets/images/campus/arts/music-building.jpg'
    },
    {
      name: 'Theater',
      features: [
        '400-seat main theater',
        '100-seat black box theater',
        'Scene shop',
        'Costume shop',
        'Dressing rooms',
        'Lighting and sound booths'
      ],
      image: 'assets/images/campus/arts/theater.jpg'
    },
    {
      name: 'Dance Studio',
      features: [
        'Sprung floors',
        'Wall-to-wall mirrors',
        'Ballet barres',
        'Sound system',
        'Changing rooms'
      ],
      image: 'assets/images/campus/arts/dance-studio.jpg'
    }
  ];

  // Upcoming events
  upcomingEvents: Event[] = [
    {
      title: 'Spring Art Exhibition',
      date: 'May 15-30, 2023',
      time: 'Gallery hours: 9:00 AM - 4:00 PM',
      location: 'Visual Arts Center Gallery',
      description: 'Featuring selected works from visual arts students across all grade levels, showcasing a variety of media and techniques.',
      image: 'assets/images/campus/arts/art-exhibition.jpg'
    },
    {
      title: 'Spring Concert',
      date: 'May 20, 2023',
      time: '7:00 PM',
      location: 'School Auditorium',
      description: 'Join our band, orchestra, and choir for an evening of beautiful music featuring classical and contemporary selections.',
      image: 'assets/images/campus/arts/spring-concert.jpg'
    },
    {
      title: '"A Midsummer Night\'s Dream"',
      date: 'June 2-4, 2023',
      time: '7:00 PM (2:00 PM Sunday matinee)',
      location: 'Main Theater',
      description: 'The theater department presents Shakespeare\'s beloved comedy, featuring enchanting performances by our talented student actors.',
      image: 'assets/images/campus/arts/midsummer.jpg'
    },
    {
      title: 'Dance Showcase',
      date: 'June 10, 2023',
      time: '7:00 PM',
      location: 'School Auditorium',
      description: 'Our dance program presents an evening of original choreography and performances in various styles, from ballet to contemporary.',
      image: 'assets/images/campus/arts/dance-showcase.jpg'
    }
  ];

  // Arts curriculum philosophy
  philosophyPoints = [
    {
      title: 'Creative Expression',
      description: 'We believe in nurturing each student\'s unique creative voice and providing opportunities for personal expression through various artistic media.'
    },
    {
      title: 'Technical Skill Development',
      description: 'Our programs emphasize the development of fundamental techniques and skills that provide students with the tools they need to realize their artistic visions.'
    },
    {
      title: 'Cultural Understanding',
      description: 'We explore diverse artistic traditions from around the world, fostering appreciation for different cultures and perspectives through the arts.'
    },
    {
      title: 'Critical Thinking',
      description: 'Our arts curriculum encourages analysis, interpretation, and evaluation of artistic works, developing students\' critical thinking and problem-solving abilities.'
    },
    {
      title: 'Collaboration',
      description: 'Many of our arts programs involve collaborative projects that teach students to work together, communicate effectively, and respect diverse contributions.'
    },
    {
      title: 'Connection to Other Disciplines',
      description: 'We emphasize the connections between the arts and other academic subjects, reinforcing learning across the curriculum through integrated projects.'
    }
  ];

  // Filter programs by category
  getProgramsByCategory(category: string): Program[] {
    return this.programs.filter(program => program.category === category);
  }
}
