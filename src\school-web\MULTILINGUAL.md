# Multilingual Support in School Website

This document outlines how the multilingual support is implemented in the school website, supporting both English and Bengali languages.

## Features

- Complete support for English and Bengali languages
- Easy language switching through the UI
- Automatic language detection based on browser settings
- Proper font support for both languages
- Consistent translation across all pages and components

## Implementation Details

### 1. Translation Files

Translation files are stored in JSON format in the `src/assets/i18n/` directory:
- `en.json` - English translations
- `bn.json` - Bengali translations

Each file contains the same structure of nested keys, making it easy to maintain and update translations.

### 2. Integration with Angular

The website uses `ngx-translate` library to handle translations:

- `TranslateModule` is configured in `app.config.ts`
- Translation service is injected in components that need translation
- Translation pipe (`translate`) is used in templates to display translated content

### 3. Language Switching

Language switching is implemented in the `AppComponent`:

- A language selector is available in the navigation bar
- Current language is stored in the component state
- Language can be changed at any time with immediate effect
- Selected language is remembered for future visits

### 4. Font Support

Special attention has been paid to ensure proper font support for Bengali:

- Hind Siliguri font is loaded for Bengali text
- CSS selectors based on language attribute ensure correct font rendering
- A utility class `.bengali-text` is available for manual application

## Adding New Languages

To add a new language:

1. Create a new translation file in `src/assets/i18n/` (e.g., `hi.json` for Hindi)
2. Copy the structure from an existing translation file and translate all values
3. Add the new language to the `languages` array in `AppComponent`
4. Add appropriate font support in `styles.scss` if needed

## Best Practices

- Always use translation keys instead of hardcoded text
- Keep translation keys organized by feature or section
- Use nested objects to create a logical hierarchy
- Ensure all user-facing text is translatable
- Test the UI in all supported languages to ensure proper layout

## Technical Considerations

- Text expansion: Bengali text is often longer than English, so UI layouts should accommodate varying text lengths
- Right-to-left (RTL) support: While not needed for English or Bengali, the architecture supports adding RTL languages in the future
- Font loading: Custom fonts are loaded only when needed to optimize performance
