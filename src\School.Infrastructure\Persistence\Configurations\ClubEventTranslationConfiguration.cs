using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ClubEventTranslationConfiguration : IEntityTypeConfiguration<ClubEventTranslation>
{
    public void Configure(EntityTypeBuilder<ClubEventTranslation> builder)
    {
        builder.HasKey(t => t.Id);
        
        builder.Property(t => t.LanguageCode)
            .IsRequired()
            .HasMaxLength(5);
            
        builder.Property(t => t.Title)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(t => t.Location)
            .HasMaxLength(100);
            
        builder.Property(t => t.Description)
            .IsRequired();
            
        // Create a unique constraint for ClubEventId and LanguageCode
        builder.HasIndex(t => new { t.ClubEventId, t.LanguageCode })
            .IsUnique();
    }
}
