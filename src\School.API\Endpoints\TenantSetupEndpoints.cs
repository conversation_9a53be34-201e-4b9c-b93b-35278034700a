using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.Common.Interfaces;
using School.Application.DTOs.TenantSetup;
using School.Application.Features.TenantSetup;

namespace School.API.Endpoints;

/// <summary>
/// Endpoints for tenant setup and configuration
/// </summary>
public class TenantSetupEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/tenant-setup")
            .WithTags("Tenant Setup")
            .WithOpenApi()
            .RequireAuthorization();

        // Get tenant setup status
        group.MapGet("/{tenantId:guid}/status", GetSetupStatus)
            .WithName("GetTenantSetupStatus")
            .WithSummary("Get tenant setup status")
            .WithDescription("Returns the current setup status and completed steps for a tenant")
            .Produces<TenantSetupStatusDto>(200)
            .Produces(404);

        // Complete school profile setup
        group.MapPost("/{tenantId:guid}/school-profile", CompleteSchoolProfile)
            .WithName("CompleteSchoolProfile")
            .WithSummary("Complete school profile setup")
            .WithDescription("Sets up the basic school information and profile")
            .Produces(200)
            .Produces<ValidationProblemDetails>(400)
            .Produces(404);

        // Complete academic structure setup
        group.MapPost("/{tenantId:guid}/academic-structure", CompleteAcademicStructure)
            .WithName("CompleteAcademicStructure")
            .WithSummary("Complete academic structure setup")
            .WithDescription("Sets up the academic year structure, terms, and grades")
            .Produces(200)
            .Produces<ValidationProblemDetails>(400)
            .Produces(404);

        // Complete user roles setup
        group.MapPost("/{tenantId:guid}/user-roles", CompleteUserRoles)
            .WithName("CompleteUserRoles")
            .WithSummary("Complete user roles setup")
            .WithDescription("Sets up user roles and permissions structure")
            .Produces(200)
            .Produces<ValidationProblemDetails>(400)
            .Produces(404);

        // Complete initial users setup
        group.MapPost("/{tenantId:guid}/initial-users", CompleteInitialUsers)
            .WithName("CompleteInitialUsers")
            .WithSummary("Complete initial users setup")
            .WithDescription("Creates initial faculty and staff user accounts")
            .Produces(200)
            .Produces<ValidationProblemDetails>(400)
            .Produces(404);

        // Complete system settings setup
        group.MapPost("/{tenantId:guid}/system-settings", CompleteSystemSettings)
            .WithName("CompleteSystemSettings")
            .WithSummary("Complete system settings setup")
            .WithDescription("Configures system preferences and integrations")
            .Produces(200)
            .Produces<ValidationProblemDetails>(400)
            .Produces(404);

        // Mark a step as complete
        group.MapPost("/{tenantId:guid}/complete-step", MarkStepComplete)
            .WithName("MarkStepComplete")
            .WithSummary("Mark a setup step as complete")
            .WithDescription("Marks a specific setup step as completed")
            .Produces(200)
            .Produces<ValidationProblemDetails>(400)
            .Produces(404);

        // Skip an optional step
        group.MapPost("/{tenantId:guid}/skip-step", SkipOptionalStep)
            .WithName("SkipOptionalStep")
            .WithSummary("Skip an optional setup step")
            .WithDescription("Skips an optional setup step")
            .Produces(200)
            .Produces<ValidationProblemDetails>(400)
            .Produces(404);

        // Finalize tenant setup
        group.MapPost("/{tenantId:guid}/finalize", FinalizeSetup)
            .WithName("FinalizeTenantSetup")
            .WithSummary("Finalize tenant setup")
            .WithDescription("Completes the tenant setup process and marks it as ready for use")
            .Produces(200)
            .Produces<ValidationProblemDetails>(400)
            .Produces(404);

        // Reset tenant setup
        group.MapPost("/{tenantId:guid}/reset", ResetSetup)
            .WithName("ResetTenantSetup")
            .WithSummary("Reset tenant setup")
            .WithDescription("Resets the tenant setup process to start over")
            .Produces(200)
            .Produces(404);
    }

    private static async Task<IResult> GetSetupStatus(
        Guid tenantId,
        [FromServices] ITenantSetupService tenantSetupService,
        CancellationToken cancellationToken)
    {
        try
        {
            var status = await tenantSetupService.GetSetupStatusAsync(tenantId, cancellationToken);
            if (status == null)
            {
                return ApiResults.ApiNotFound("Tenant setup status not found");
            }
            return ApiResults.ApiOk(status, "Setup status retrieved successfully");
        }
        catch (Exception ex)
        {
            return ApiResults.ApiServerError($"Error retrieving setup status: {ex.Message}");
        }
    }

    private static async Task<IResult> CompleteSchoolProfile(
        Guid tenantId,
        [FromBody] SchoolProfileSetupDto request,
        [FromServices] ITenantSetupService tenantSetupService,
        CancellationToken cancellationToken)
    {
        try
        {
            var result = await tenantSetupService.CompleteSchoolProfileAsync(tenantId, request, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk("School profile setup completed successfully");
        }
        catch (Exception ex)
        {
            return ApiResults.ApiServerError($"Error completing school profile setup: {ex.Message}");
        }
    }

    private static async Task<IResult> CompleteAcademicStructure(
        Guid tenantId,
        [FromBody] AcademicStructureSetupDto request,
        [FromServices] ITenantSetupService tenantSetupService,
        CancellationToken cancellationToken)
    {
        try
        {
            var result = await tenantSetupService.CompleteAcademicStructureAsync(tenantId, request, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk("Academic structure setup completed successfully");
        }
        catch (Exception ex)
        {
            return ApiResults.ApiServerError($"Error completing academic structure setup: {ex.Message}");
        }
    }

    private static async Task<IResult> CompleteUserRoles(
        Guid tenantId,
        [FromBody] UserRolesSetupDto request,
        [FromServices] ITenantSetupService tenantSetupService,
        CancellationToken cancellationToken)
    {
        try
        {
            var result = await tenantSetupService.CompleteUserRolesAsync(tenantId, request, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk("User roles setup completed successfully");
        }
        catch (Exception ex)
        {
            return ApiResults.ApiServerError($"Error completing user roles setup: {ex.Message}");
        }
    }

    private static async Task<IResult> CompleteInitialUsers(
        Guid tenantId,
        [FromBody] InitialUsersSetupDto request,
        [FromServices] ITenantSetupService tenantSetupService,
        CancellationToken cancellationToken)
    {
        try
        {
            var result = await tenantSetupService.CompleteInitialUsersAsync(tenantId, request, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk("Initial users setup completed successfully");
        }
        catch (Exception ex)
        {
            return ApiResults.ApiServerError($"Error completing initial users setup: {ex.Message}");
        }
    }

    private static async Task<IResult> CompleteSystemSettings(
        Guid tenantId,
        [FromBody] SystemSettingsSetupDto request,
        [FromServices] ITenantSetupService tenantSetupService,
        CancellationToken cancellationToken)
    {
        try
        {
            var result = await tenantSetupService.CompleteSystemSettingsAsync(tenantId, request, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk("System settings setup completed successfully");
        }
        catch (Exception ex)
        {
            return ApiResults.ApiServerError($"Error completing system settings setup: {ex.Message}");
        }
    }

    private static async Task<IResult> MarkStepComplete(
        Guid tenantId,
        [FromBody] MarkStepCompleteDto request,
        [FromServices] ITenantSetupService tenantSetupService,
        CancellationToken cancellationToken)
    {
        try
        {
            var result = await tenantSetupService.MarkStepCompleteAsync(tenantId, request.StepId, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk("Step marked as complete successfully");
        }
        catch (Exception ex)
        {
            return ApiResults.ApiServerError($"Error marking step as complete: {ex.Message}");
        }
    }

    private static async Task<IResult> SkipOptionalStep(
        Guid tenantId,
        [FromBody] SkipStepDto request,
        [FromServices] ITenantSetupService tenantSetupService,
        CancellationToken cancellationToken)
    {
        try
        {
            var result = await tenantSetupService.SkipOptionalStepAsync(tenantId, request.StepId, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk("Optional step skipped successfully");
        }
        catch (Exception ex)
        {
            return ApiResults.ApiServerError($"Error skipping optional step: {ex.Message}");
        }
    }

    private static async Task<IResult> FinalizeSetup(
        Guid tenantId,
        [FromServices] ITenantSetupService tenantSetupService,
        CancellationToken cancellationToken)
    {
        try
        {
            var result = await tenantSetupService.FinalizeSetupAsync(tenantId, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk("Tenant setup finalized successfully");
        }
        catch (Exception ex)
        {
            return ApiResults.ApiServerError($"Error finalizing tenant setup: {ex.Message}");
        }
    }

    private static async Task<IResult> ResetSetup(
        Guid tenantId,
        [FromServices] ITenantSetupService tenantSetupService,
        CancellationToken cancellationToken)
    {
        try
        {
            var result = await tenantSetupService.ResetSetupAsync(tenantId, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk("Tenant setup reset successfully");
        }
        catch (Exception ex)
        {
            return ApiResults.ApiServerError($"Error resetting tenant setup: {ex.Message}");
        }
    }
}
