import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface ClassTeacher {
  id: string;
  facultyId: string;
  facultyName: string;
  facultyEmail: string;
  sectionId: string;
  sectionName: string;
  sectionCode: string;
  gradeId: string;
  gradeName: string;
  academicYearId: string;
  academicYearName: string;
  startDate: Date;
  endDate?: Date;
  isPrimary: boolean;
  isActive: boolean;
  status: string;
  responsibilities: string;
  specialDuties: string;
  contactSchedule: string;
  officeHours: string;
  studentCount: number;
  workloadPercentage: number;
  createdAt: Date;
  lastModifiedAt?: Date;
}

export interface CreateClassTeacherDto {
  facultyId: string;
  sectionId: string;
  academicYearId: string;
  startDate: Date;
  isPrimary: boolean;
  responsibilities: string;
  specialDuties: string;
  contactSchedule: string;
  officeHours: string;
}

export interface UpdateClassTeacherDto {
  responsibilities: string;
  specialDuties: string;
  contactSchedule: string;
  officeHours: string;
  isPrimary: boolean;
}

export interface ClassTeacherFilterDto {
  page: number;
  pageSize: number;
  searchTerm?: string;
  facultyId?: string;
  sectionId?: string;
  gradeId?: string;
  academicYearId?: string;
  status?: string;
  isActive?: boolean;
  sortBy?: string;
  sortDirection?: string;
}

export interface ClassTeacherListResponse {
  data: ClassTeacher[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ClassTeacherWorkloadDto {
  facultyId: string;
  facultyName: string;
  totalSections: number;
  totalStudents: number;
  workloadPercentage: number;
  assignments: ClassTeacher[];
}

export interface ClassTeacherPerformanceDto {
  classTeacherId: string;
  facultyName: string;
  sectionName: string;
  studentCount: number;
  attendanceRate: number;
  performanceScore: number;
  parentSatisfaction: number;
  achievements: string[];
}

@Injectable({
  providedIn: 'root'
})
export class ClassTeacherService {
  private http = inject(HttpClient);
  private apiUrl = `${environment.apiUrl}/api/ClassTeacher`;

  getClassTeachers(filter: ClassTeacherFilterDto): Observable<ClassTeacherListResponse> {
    let params = new HttpParams()
      .set('page', filter.page.toString())
      .set('pageSize', filter.pageSize.toString());

    if (filter.searchTerm) {
      params = params.set('searchTerm', filter.searchTerm);
    }
    if (filter.facultyId) {
      params = params.set('facultyId', filter.facultyId);
    }
    if (filter.sectionId) {
      params = params.set('sectionId', filter.sectionId);
    }
    if (filter.gradeId) {
      params = params.set('gradeId', filter.gradeId);
    }
    if (filter.academicYearId) {
      params = params.set('academicYearId', filter.academicYearId);
    }
    if (filter.status) {
      params = params.set('status', filter.status);
    }
    if (filter.isActive !== undefined) {
      params = params.set('isActive', filter.isActive.toString());
    }
    if (filter.sortBy) {
      params = params.set('sortBy', filter.sortBy);
    }
    if (filter.sortDirection) {
      params = params.set('sortDirection', filter.sortDirection);
    }

    return this.http.get<ClassTeacherListResponse>(this.apiUrl, { params });
  }

  getClassTeacher(id: string): Observable<ClassTeacher> {
    return this.http.get<ClassTeacher>(`${this.apiUrl}/${id}`);
  }

  getClassTeachersByAcademicYear(academicYearId: string): Observable<ClassTeacher[]> {
    return this.http.get<ClassTeacher[]>(`${this.apiUrl}/academic-year/${academicYearId}`);
  }

  getActiveClassTeachers(academicYearId: string): Observable<ClassTeacher[]> {
    return this.http.get<ClassTeacher[]>(`${this.apiUrl}/academic-year/${academicYearId}/active`);
  }

  getClassTeachersByFaculty(facultyId: string): Observable<ClassTeacher[]> {
    return this.http.get<ClassTeacher[]>(`${this.apiUrl}/faculty/${facultyId}`);
  }

  getClassTeacherBySection(sectionId: string): Observable<ClassTeacher> {
    return this.http.get<ClassTeacher>(`${this.apiUrl}/section/${sectionId}`);
  }

  createClassTeacher(classTeacher: CreateClassTeacherDto): Observable<{ id: string }> {
    return this.http.post<{ id: string }>(this.apiUrl, classTeacher);
  }

  updateClassTeacher(id: string, classTeacher: UpdateClassTeacherDto): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, classTeacher);
  }

  deleteClassTeacher(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  reassignClassTeacher(sectionId: string, newFacultyId: string, reason?: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${sectionId}/reassign/${newFacultyId}`, { reason });
  }

  removeClassTeacher(sectionId: string, reason?: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/section/${sectionId}`, { 
      body: { reason }
    });
  }

  transferClassTeacher(facultyId: string, fromSectionId: string, toSectionId: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/transfer`, {
      facultyId,
      fromSectionId,
      toSectionId
    });
  }

  updateClassTeacherStatus(id: string, status: string, reason?: string): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/status`, { status, reason });
  }

  activateClassTeacher(id: string): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/activate`, {});
  }

  suspendClassTeacher(id: string, reason?: string): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/suspend`, { reason });
  }

  completeClassTeacherAssignment(id: string, endDate?: Date): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/complete`, { endDate });
  }

  getFacultyWorkload(facultyId: string, academicYearId: string): Observable<ClassTeacherWorkloadDto> {
    return this.http.get<ClassTeacherWorkloadDto>(`${this.apiUrl}/faculty/${facultyId}/workload/${academicYearId}`);
  }

  getAllFacultyWorkloads(academicYearId: string): Observable<ClassTeacherWorkloadDto[]> {
    return this.http.get<ClassTeacherWorkloadDto[]>(`${this.apiUrl}/academic-year/${academicYearId}/workloads`);
  }

  getOverloadedFaculty(academicYearId: string, threshold: number = 100): Observable<ClassTeacherWorkloadDto[]> {
    return this.http.get<ClassTeacherWorkloadDto[]>(`${this.apiUrl}/academic-year/${academicYearId}/overloaded`, {
      params: { threshold: threshold.toString() }
    });
  }

  getClassTeacherPerformance(id: string): Observable<ClassTeacherPerformanceDto> {
    return this.http.get<ClassTeacherPerformanceDto>(`${this.apiUrl}/${id}/performance`);
  }

  getUnassignedSections(academicYearId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/academic-year/${academicYearId}/unassigned-sections`);
  }

  getAvailableFacultyForAssignment(academicYearId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/academic-year/${academicYearId}/available-faculty`);
  }

  bulkAssignClassTeachers(sectionFacultyMappings: { [sectionId: string]: string }, academicYearId: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/bulk-assign`, {
      sectionFacultyMappings,
      academicYearId
    });
  }

  exportClassTeachers(academicYearId: string): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/academic-year/${academicYearId}/export`, {
      responseType: 'blob'
    });
  }

  validateClassTeacherAssignment(facultyId: string, sectionId: string, academicYearId: string): Observable<{ isValid: boolean }> {
    return this.http.get<{ isValid: boolean }>(`${this.apiUrl}/validate-assignment`, {
      params: {
        facultyId,
        sectionId,
        academicYearId
      }
    });
  }
}
