using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class Notice : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this notice belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public NoticePriority Priority { get; set; } = NoticePriority.Medium;
    public string Category { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;

    // Relationships
    public Guid CreatedById { get; set; }
    public User? CreatedBy { get; set; }

    public ICollection<NoticeTranslation> Translations { get; set; } = new List<NoticeTranslation>();
}
