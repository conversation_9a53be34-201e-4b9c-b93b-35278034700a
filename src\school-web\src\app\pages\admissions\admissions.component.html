<!-- Hero Section -->
<app-default-hero
  translationPrefix="ADMISSIONS"
  title="ADMISSIONS.TITLE"
  subtitle="ADMISSIONS.SUBTITLE"
  description="ADMISSIONS.DESCRIPTION"
  theme="dark"
  size="large"
  alignment="center"
  backgroundImage="assets/images/admissions-hero.jpg"
  [buttons]="[
    {label: 'ADMISSIONS.APPLY_NOW' | translate, link: '#inquiry', isPrimary: true, icon: 'edit'},
    {label: 'ADMISSIONS.LEARN_MORE' | translate, link: '#process', isPrimary: false}
  ]">
</app-default-hero>

<!-- Floating Navigation Sidebar -->
<div class="floating-nav" [class.visible]="showFloatingNav">
  <div class="floating-nav-content">
    <h3>{{ 'ADMISSIONS.QUICK_NAV' | translate }}</h3>
    <ul class="floating-nav-links">
      <li>
        <a (click)="scrollToSection('process')" [class.active]="activeSection === 'process'">
          <mat-icon>timeline</mat-icon>
          <span>{{ 'ADMISSIONS.PROCESS' | translate }}</span>
        </a>
      </li>
      <li>
        <a (click)="scrollToSection('requirements')" [class.active]="activeSection === 'requirements'">
          <mat-icon>assignment</mat-icon>
          <span>{{ 'ADMISSIONS.REQUIREMENTS' | translate }}</span>
        </a>
      </li>
      <li>
        <a (click)="scrollToSection('tuition')" [class.active]="activeSection === 'tuition'">
          <mat-icon>payments</mat-icon>
          <span>{{ 'ADMISSIONS.TUITION' | translate }}</span>
        </a>
      </li>
      <li>
        <a (click)="scrollToSection('scholarships')" [class.active]="activeSection === 'scholarships'">
          <mat-icon>school</mat-icon>
          <span>{{ 'ADMISSIONS.SCHOLARSHIPS' | translate }}</span>
        </a>
      </li>
      <li>
        <a (click)="scrollToSection('dates')" [class.active]="activeSection === 'dates'">
          <mat-icon>event</mat-icon>
          <span>{{ 'ADMISSIONS.DATES' | translate }}</span>
        </a>
      </li>
      <li>
        <a (click)="scrollToSection('faq')" [class.active]="activeSection === 'faq'">
          <mat-icon>help</mat-icon>
          <span>{{ 'ADMISSIONS.FAQ' | translate }}</span>
        </a>
      </li>
      <li>
        <a (click)="scrollToSection('inquiry')" [class.active]="activeSection === 'inquiry'">
          <mat-icon>contact_mail</mat-icon>
          <span>{{ 'ADMISSIONS.INQUIRY' | translate }}</span>
        </a>
      </li>
    </ul>
  </div>
</div>

<!-- Main Content -->
<div class="container">
  <!-- Admission Process Section -->
  <section id="process" class="content-section">
    <h2>{{ 'ADMISSIONS.PROCESS' | translate }}</h2>
    <p class="section-intro">{{ 'ADMISSIONS.PROCESS_INTRO' | translate }}</p>

    <div class="process-steps">
      <div class="step" *ngFor="let step of admissionSteps; let i = index">
        <div class="step-number">{{i + 1}}</div>
        <div class="step-content">
          <div class="step-icon">
            <mat-icon>{{step.icon}}</mat-icon>
          </div>
          <h3>{{step.title}}</h3>
          <p>{{step.description}}</p>
        </div>
      </div>
    </div>

    <div class="cta-box">
      <h3>{{ 'ADMISSIONS.READY_TO_APPLY' | translate }}</h3>
      <p>{{ 'ADMISSIONS.READY_TO_APPLY_TEXT' | translate }}</p>
      <div class="cta-buttons">
        <a mat-raised-button color="primary" href="#inquiry">{{ 'ADMISSIONS.START_APPLICATION' | translate }}</a>
        <a mat-stroked-button color="primary" href="#requirements">{{ 'ADMISSIONS.VIEW_REQUIREMENTS' | translate }}</a>
      </div>
    </div>
  </section>

  <!-- Application Requirements Section -->
  <section id="requirements" class="content-section">
    <h2>{{ 'ADMISSIONS.REQUIREMENTS' | translate }}</h2>
    <p class="section-intro">{{ 'ADMISSIONS.REQUIREMENTS_INTRO' | translate }}</p>

    <mat-tab-group animationDuration="300ms" class="requirements-tabs">
      <mat-tab [label]="'ADMISSIONS.ELEMENTARY' | translate">
        <div class="requirements-list">
          <h3>{{ 'ADMISSIONS.ELEMENTARY_REQUIREMENTS' | translate }}</h3>
          <ul>
            <li *ngFor="let requirement of applicationRequirements.elementary">
              <mat-icon>check_circle</mat-icon>
              <span>{{requirement}}</span>
            </li>
          </ul>
        </div>
      </mat-tab>
      <mat-tab [label]="'ADMISSIONS.MIDDLE' | translate">
        <div class="requirements-list">
          <h3>{{ 'ADMISSIONS.MIDDLE_REQUIREMENTS' | translate }}</h3>
          <ul>
            <li *ngFor="let requirement of applicationRequirements.middle">
              <mat-icon>check_circle</mat-icon>
              <span>{{requirement}}</span>
            </li>
          </ul>
        </div>
      </mat-tab>
      <mat-tab [label]="'ADMISSIONS.HIGH' | translate">
        <div class="requirements-list">
          <h3>{{ 'ADMISSIONS.HIGH_REQUIREMENTS' | translate }}</h3>
          <ul>
            <li *ngFor="let requirement of applicationRequirements.high">
              <mat-icon>check_circle</mat-icon>
              <span>{{requirement}}</span>
            </li>
          </ul>
        </div>
      </mat-tab>
    </mat-tab-group>

    <div class="note-box">
      <mat-icon>info</mat-icon>
      <p>{{ 'ADMISSIONS.REQUIREMENTS_NOTE' | translate }}</p>
    </div>
  </section>

  <!-- Tuition and Fees Section -->
  <section id="tuition" class="content-section">
    <h2>{{ 'ADMISSIONS.TUITION' | translate }}</h2>
    <p class="section-intro">{{ 'ADMISSIONS.TUITION_INTRO' | translate }}</p>

    <div class="tuition-cards">
      <mat-card class="tuition-card" *ngFor="let fee of tuitionFees">
        <mat-card-header>
          <mat-card-title>{{fee.level}}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="annual-fee">
            <span class="fee-amount">{{formatCurrency(fee.annualFee)}}</span>
            <span class="fee-period">{{ 'ADMISSIONS.PER_YEAR' | translate }}</span>
          </div>

          <div class="fee-details">
            <div class="fee-item">
              <span class="fee-label">{{ 'ADMISSIONS.APPLICATION_FEE' | translate }}</span>
              <span class="fee-value">{{formatCurrency(fee.applicationFee)}}</span>
            </div>
            <div class="fee-item">
              <span class="fee-label">{{ 'ADMISSIONS.REGISTRATION_FEE' | translate }}</span>
              <span class="fee-value">{{formatCurrency(fee.registrationFee)}}</span>
            </div>

            <h4>{{ 'ADMISSIONS.ADDITIONAL_COSTS' | translate }}</h4>
            <div class="fee-item" *ngFor="let cost of fee.additionalCosts">
              <span class="fee-label">{{cost.name}}</span>
              <span class="fee-value">{{formatCurrency(cost.amount)}}</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <div class="payment-options">
      <h3>{{ 'ADMISSIONS.PAYMENT_OPTIONS' | translate }}</h3>
      <p>{{ 'ADMISSIONS.PAYMENT_OPTIONS_TEXT' | translate }}</p>
      <ul>
        <li>{{ 'ADMISSIONS.PAYMENT_OPTION_1' | translate }}</li>
        <li>{{ 'ADMISSIONS.PAYMENT_OPTION_2' | translate }}</li>
        <li>{{ 'ADMISSIONS.PAYMENT_OPTION_3' | translate }}</li>
      </ul>
    </div>
  </section>

  <!-- Scholarships and Financial Aid Section -->
  <section id="scholarships" class="content-section">
    <h2>{{ 'ADMISSIONS.SCHOLARSHIPS' | translate }}</h2>
    <p class="section-intro">{{ 'ADMISSIONS.SCHOLARSHIPS_INTRO' | translate }}</p>

    <div class="scholarship-cards">
      <mat-card class="scholarship-card" *ngFor="let scholarship of scholarshipPrograms">
        <mat-card-header>
          <mat-card-title>{{scholarship.name}}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p class="scholarship-description">{{scholarship.description}}</p>

          <div class="scholarship-details">
            <div class="detail-item">
              <h4>{{ 'ADMISSIONS.ELIGIBILITY' | translate }}</h4>
              <p>{{scholarship.eligibility}}</p>
            </div>
            <div class="detail-item">
              <h4>{{ 'ADMISSIONS.AWARD' | translate }}</h4>
              <p>{{scholarship.award}}</p>
            </div>
            <div class="detail-item">
              <h4>{{ 'ADMISSIONS.DEADLINE' | translate }}</h4>
              <p>{{scholarship.deadline}}</p>
            </div>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <button mat-button color="primary">{{ 'ADMISSIONS.LEARN_MORE' | translate }}</button>
        </mat-card-actions>
      </mat-card>
    </div>

    <div class="financial-aid-info">
      <h3>{{ 'ADMISSIONS.FINANCIAL_AID' | translate }}</h3>
      <p>{{ 'ADMISSIONS.FINANCIAL_AID_TEXT' | translate }}</p>
      <a mat-raised-button color="primary" href="#inquiry">{{ 'ADMISSIONS.CONTACT_FINANCIAL_AID' | translate }}</a>
    </div>
  </section>

  <!-- Important Dates Section -->
  <section id="dates" class="content-section">
    <h2>{{ 'ADMISSIONS.DATES' | translate }}</h2>
    <p class="section-intro">{{ 'ADMISSIONS.DATES_INTRO' | translate }}</p>

    <div class="dates-timeline">
      <div class="timeline-item" *ngFor="let date of importantDates">
        <div class="timeline-content">
          <h3>{{date.event}}</h3>
          <div class="timeline-date">{{date.date}}</div>
          <p>{{date.description}}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section id="faq" class="content-section">
    <h2>{{ 'ADMISSIONS.FAQ' | translate }}</h2>
    <p class="section-intro">{{ 'ADMISSIONS.FAQ_INTRO' | translate }}</p>

    <mat-accordion class="faq-accordion">
      <mat-expansion-panel *ngFor="let faq of faqs">
        <mat-expansion-panel-header>
          <mat-panel-title>
            {{faq.question}}
          </mat-panel-title>
        </mat-expansion-panel-header>
        <p>{{faq.answer}}</p>
      </mat-expansion-panel>
    </mat-accordion>

    <div class="more-questions">
      <p>{{ 'ADMISSIONS.MORE_QUESTIONS' | translate }}</p>
      <a mat-button color="primary" href="#inquiry">{{ 'ADMISSIONS.CONTACT_US' | translate }}</a>
    </div>
  </section>

  <!-- Inquiry Form Section -->
  <section id="inquiry" class="content-section">
    <h2>{{ 'ADMISSIONS.INQUIRY' | translate }}</h2>
    <p class="section-intro">{{ 'ADMISSIONS.INQUIRY_INTRO' | translate }}</p>

    <div class="inquiry-container">
      <div class="inquiry-form">
        <form [formGroup]="inquiryForm" (ngSubmit)="submitInquiry()">
          <h3>{{ 'ADMISSIONS.INQUIRY_FORM_TITLE' | translate }}</h3>

          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>{{ 'ADMISSIONS.PARENT_NAME' | translate }}</mat-label>
              <input matInput formControlName="parentName" required>
              <mat-error *ngIf="inquiryForm.get('parentName')?.invalid && inquiryForm.get('parentName')?.touched">
                {{ 'ADMISSIONS.REQUIRED_FIELD' | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row two-columns">
            <mat-form-field appearance="outline">
              <mat-label>{{ 'ADMISSIONS.EMAIL' | translate }}</mat-label>
              <input matInput formControlName="email" required type="email">
              <mat-error *ngIf="inquiryForm.get('email')?.invalid && inquiryForm.get('email')?.touched">
                {{ 'ADMISSIONS.VALID_EMAIL' | translate }}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>{{ 'ADMISSIONS.PHONE' | translate }}</mat-label>
              <input matInput formControlName="phone" required>
              <mat-error *ngIf="inquiryForm.get('phone')?.invalid && inquiryForm.get('phone')?.touched">
                {{ 'ADMISSIONS.REQUIRED_FIELD' | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>{{ 'ADMISSIONS.STUDENT_NAME' | translate }}</mat-label>
              <input matInput formControlName="studentName" required>
              <mat-error *ngIf="inquiryForm.get('studentName')?.invalid && inquiryForm.get('studentName')?.touched">
                {{ 'ADMISSIONS.REQUIRED_FIELD' | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row two-columns">
            <mat-form-field appearance="outline">
              <mat-label>{{ 'ADMISSIONS.GRADE_LEVEL' | translate }}</mat-label>
              <mat-select formControlName="gradeLevel" required>
                <mat-option value="Pre-K">Pre-K</mat-option>
                <mat-option value="Kindergarten">Kindergarten</mat-option>
                <mat-option value="Grade 1">Grade 1</mat-option>
                <mat-option value="Grade 2">Grade 2</mat-option>
                <mat-option value="Grade 3">Grade 3</mat-option>
                <mat-option value="Grade 4">Grade 4</mat-option>
                <mat-option value="Grade 5">Grade 5</mat-option>
                <mat-option value="Grade 6">Grade 6</mat-option>
                <mat-option value="Grade 7">Grade 7</mat-option>
                <mat-option value="Grade 8">Grade 8</mat-option>
                <mat-option value="Grade 9">Grade 9</mat-option>
                <mat-option value="Grade 10">Grade 10</mat-option>
                <mat-option value="Grade 11">Grade 11</mat-option>
                <mat-option value="Grade 12">Grade 12</mat-option>
              </mat-select>
              <mat-error *ngIf="inquiryForm.get('gradeLevel')?.invalid && inquiryForm.get('gradeLevel')?.touched">
                {{ 'ADMISSIONS.REQUIRED_FIELD' | translate }}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>{{ 'ADMISSIONS.CURRENT_SCHOOL' | translate }}</mat-label>
              <input matInput formControlName="currentSchool">
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>{{ 'ADMISSIONS.MESSAGE' | translate }}</mat-label>
              <textarea matInput formControlName="message" rows="4"></textarea>
            </mat-form-field>
          </div>

          <div class="form-row two-columns">
            <mat-form-field appearance="outline">
              <mat-label>{{ 'ADMISSIONS.HOW_HEARD' | translate }}</mat-label>
              <mat-select formControlName="howHeard">
                <mat-option value="Friend">{{ 'ADMISSIONS.FRIEND' | translate }}</mat-option>
                <mat-option value="Website">{{ 'ADMISSIONS.WEBSITE' | translate }}</mat-option>
                <mat-option value="Social Media">{{ 'ADMISSIONS.SOCIAL_MEDIA' | translate }}</mat-option>
                <mat-option value="School Fair">{{ 'ADMISSIONS.SCHOOL_FAIR' | translate }}</mat-option>
                <mat-option value="Other">{{ 'ADMISSIONS.OTHER' | translate }}</mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>{{ 'ADMISSIONS.INTEREST_AREAS' | translate }}</mat-label>
              <mat-select formControlName="interestAreas">
                <mat-option value="Academics">{{ 'ADMISSIONS.ACADEMICS' | translate }}</mat-option>
                <mat-option value="Arts">{{ 'ADMISSIONS.ARTS' | translate }}</mat-option>
                <mat-option value="Athletics">{{ 'ADMISSIONS.ATHLETICS' | translate }}</mat-option>
                <mat-option value="STEM">{{ 'ADMISSIONS.STEM' | translate }}</mat-option>
                <mat-option value="Languages">{{ 'ADMISSIONS.LANGUAGES' | translate }}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="form-actions">
            <button mat-raised-button color="primary" type="submit">{{ 'ADMISSIONS.SUBMIT_INQUIRY' | translate }}</button>
          </div>
        </form>
      </div>

      <div class="contact-info">
        <h3>{{ 'ADMISSIONS.CONTACT_ADMISSIONS' | translate }}</h3>
        <div class="contact-item">
          <mat-icon>phone</mat-icon>
          <span>{{ 'ADMISSIONS.PHONE_NUMBER' | translate }}</span>
        </div>
        <div class="contact-item">
          <mat-icon>email</mat-icon>
          <span>{{ 'ADMISSIONS.EMAIL_ADDRESS' | translate }}</span>
        </div>
        <div class="contact-item">
          <mat-icon>location_on</mat-icon>
          <span>{{ 'ADMISSIONS.OFFICE_LOCATION' | translate }}</span>
        </div>
        <div class="contact-item">
          <mat-icon>access_time</mat-icon>
          <span>{{ 'ADMISSIONS.OFFICE_HOURS' | translate }}</span>
        </div>

        <div class="visit-campus">
          <h4>{{ 'ADMISSIONS.VISIT_CAMPUS' | translate }}</h4>
          <p>{{ 'ADMISSIONS.VISIT_CAMPUS_TEXT' | translate }}</p>
          <button mat-stroked-button color="primary">{{ 'ADMISSIONS.SCHEDULE_TOUR' | translate }}</button>
        </div>
      </div>
    </div>
  </section>
</div>
