import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface TenantSetupStep {
  id: string;
  title: string;
  description: string;
  isCompleted: boolean;
  isRequired: boolean;
  order: number;
}

export interface SchoolSetupDto {
  schoolName: string;
  schoolType: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  contactInfo: {
    phone: string;
    email: string;
    website?: string;
  };
  academicInfo: {
    establishedYear: number;
    affiliationBoard: string;
    schoolCode?: string;
  };
  settings: {
    timeZone: string;
    currency: string;
    language: string;
    academicYearStart: string; // MM-DD format
  };
}

export interface AcademicStructureSetupDto {
  academicYearStart: string; // MM-DD format
  hasTerms: boolean;
  termCount: number;
  gradeStructure: string; // e.g., "K12", "1-10", etc.
}

export interface UserRolesSetupDto {
  enableCustomRoles: boolean;
  defaultRoles: boolean;
}

export interface InitialUsersSetupDto {
  createInitialUsers: boolean;
  principalEmail?: string;
  vicePrincipalEmail?: string;
  adminEmail?: string;
}

export interface SystemSettingsSetupDto {
  timeZone: string;
  currency: string;
  language: string;
  enableNotifications: boolean;
  enableSMS: boolean;
  enableEmail: boolean;
}

export interface TenantSetupStatus {
  tenantId: string;
  isSetupComplete: boolean;
  completedSteps: string[];
  currentStep: string;
  setupProgress: number;
  lastUpdated: Date;
}

@Injectable({
  providedIn: 'root'
})
export class TenantSetupService {
  private http = inject(HttpClient);
  private apiUrl = `${environment.apiUrl}/api/tenant-setup`;
  
  private setupStatusSubject = new BehaviorSubject<TenantSetupStatus | null>(null);
  public setupStatus$ = this.setupStatusSubject.asObservable();

  private setupSteps: TenantSetupStep[] = [
    {
      id: 'school-profile',
      title: 'School Profile',
      description: 'Set up basic school information and contact details',
      isCompleted: false,
      isRequired: true,
      order: 1
    },
    {
      id: 'academic-structure',
      title: 'Academic Structure',
      description: 'Configure grades, academic years, and terms',
      isCompleted: false,
      isRequired: true,
      order: 2
    },
    {
      id: 'user-roles',
      title: 'User Roles & Permissions',
      description: 'Set up user roles and permission structure',
      isCompleted: false,
      isRequired: true,
      order: 3
    },
    {
      id: 'initial-users',
      title: 'Initial Users',
      description: 'Create initial faculty and staff accounts',
      isCompleted: false,
      isRequired: false,
      order: 4
    },
    {
      id: 'system-settings',
      title: 'System Settings',
      description: 'Configure system preferences and integrations',
      isCompleted: false,
      isRequired: false,
      order: 5
    }
  ];

  getSetupSteps(): TenantSetupStep[] {
    return [...this.setupSteps];
  }

  getSetupStatus(tenantId: string): Observable<TenantSetupStatus> {
    return this.http.get<TenantSetupStatus>(`${this.apiUrl}/${tenantId}/status`);
  }

  updateSetupStatus(tenantId: string): void {
    this.getSetupStatus(tenantId).subscribe({
      next: (status) => {
        this.setupStatusSubject.next(status);
        this.updateStepsCompletion(status.completedSteps);
      },
      error: (error) => {
        console.error('Error fetching setup status:', error);
      }
    });
  }

  private updateStepsCompletion(completedSteps: string[]): void {
    this.setupSteps.forEach(step => {
      step.isCompleted = completedSteps.includes(step.id);
    });
  }



  completeSchoolProfile(tenantId: string, schoolData: SchoolSetupDto): Observable<void> {
    console.log('Completing school profile for tenant:', tenantId, schoolData);
    return this.http.post<void>(`${this.apiUrl}/${tenantId}/school-profile`, schoolData);
  }

  completeAcademicStructure(tenantId: string, academicData: any): Observable<void> {
    console.log('Completing academic structure for tenant:', tenantId, academicData);
    return this.http.post<void>(`${this.apiUrl}/${tenantId}/academic-structure`, academicData);
  }

  completeUserRoles(tenantId: string, rolesData: any): Observable<void> {
    console.log('Completing user roles for tenant:', tenantId, rolesData);
    return this.http.post<void>(`${this.apiUrl}/${tenantId}/user-roles`, rolesData);
  }

  completeInitialUsers(tenantId: string, usersData: any): Observable<void> {
    console.log('Completing initial users for tenant:', tenantId, usersData);
    return this.http.post<void>(`${this.apiUrl}/${tenantId}/initial-users`, usersData);
  }

  completeSystemSettings(tenantId: string, settingsData: any): Observable<void> {
    console.log('Completing system settings for tenant:', tenantId, settingsData);
    return this.http.post<void>(`${this.apiUrl}/${tenantId}/system-settings`, settingsData);
  }

  markStepComplete(tenantId: string, stepId: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${tenantId}/complete-step`, { stepId });
  }

  finalizeSetup(tenantId: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${tenantId}/finalize`, {});
  }

  skipOptionalStep(tenantId: string, stepId: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${tenantId}/skip-step`, { stepId });
  }

  resetSetup(tenantId: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${tenantId}/reset`, {});
  }

  getSchoolTypes(): string[] {
    return [
      'Primary School',
      'Secondary School',
      'High School',
      'Combined School',
      'International School',
      'Private School',
      'Public School',
      'Charter School',
      'Vocational School',
      'Special Education School'
    ];
  }

  getAffiliationBoards(): string[] {
    return [
      'CBSE',
      'ICSE',
      'State Board',
      'IB (International Baccalaureate)',
      'Cambridge International',
      'NIOS',
      'Other'
    ];
  }

  getTimeZones(): { value: string; label: string }[] {
    return [
      { value: 'Asia/Kolkata', label: 'India Standard Time (IST)' },
      { value: 'Asia/Dhaka', label: 'Bangladesh Standard Time (BST)' },
      { value: 'America/New_York', label: 'Eastern Time (ET)' },
      { value: 'America/Chicago', label: 'Central Time (CT)' },
      { value: 'America/Denver', label: 'Mountain Time (MT)' },
      { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
      { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
      { value: 'Europe/Paris', label: 'Central European Time (CET)' },
      { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
      { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' }
    ];
  }

  getCurrencies(): { value: string; label: string; symbol: string }[] {
    return [
      { value: 'INR', label: 'Indian Rupee', symbol: '₹' },
      { value: 'BDT', label: 'Bangladeshi Taka', symbol: '৳' },
      { value: 'USD', label: 'US Dollar', symbol: '$' },
      { value: 'EUR', label: 'Euro', symbol: '€' },
      { value: 'GBP', label: 'British Pound', symbol: '£' },
      { value: 'JPY', label: 'Japanese Yen', symbol: '¥' },
      { value: 'AUD', label: 'Australian Dollar', symbol: 'A$' },
      { value: 'CAD', label: 'Canadian Dollar', symbol: 'C$' }
    ];
  }

  getLanguages(): { value: string; label: string }[] {
    return [
      { value: 'en', label: 'English' },
      { value: 'bn', label: 'Bengali' },
      { value: 'hi', label: 'Hindi' },
      { value: 'es', label: 'Spanish' },
      { value: 'fr', label: 'French' },
      { value: 'de', label: 'German' },
      { value: 'ja', label: 'Japanese' },
      { value: 'zh', label: 'Chinese' }
    ];
  }

  validateSetupData(stepId: string, data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    switch (stepId) {
      case 'school-profile':
        if (!data.schoolName?.trim()) errors.push('School name is required');
        if (!data.schoolType) errors.push('School type is required');
        if (!data.address?.city?.trim()) errors.push('City is required');
        if (!data.contactInfo?.email?.trim()) errors.push('Email is required');
        if (!data.contactInfo?.phone?.trim()) errors.push('Phone is required');
        break;

      case 'academic-structure':
        if (!data.academicYears?.length) errors.push('At least one academic year is required');
        if (!data.grades?.length) errors.push('At least one grade is required');
        break;

      case 'user-roles':
        if (!data.roles?.length) errors.push('At least one user role is required');
        break;
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
