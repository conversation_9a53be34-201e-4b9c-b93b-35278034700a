using School.Domain.Common;

namespace School.Domain.Entities;

public class AcademicCalendarTranslation : BaseEntity
{
    public Guid AcademicCalendarId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    
    // Navigation properties
    public AcademicCalendar AcademicCalendar { get; set; } = null!;
}
