import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../shared/components/default-hero/default-hero.component';

interface JobOpening {
  id: string;
  title: string;
  department: string;
  type: 'full-time' | 'part-time' | 'contract';
  location: string;
  description: string;
  responsibilities: string[];
  qualifications: string[];
  postedDate: Date;
}

interface Benefit {
  title: string;
  description: string;
  icon: string;
}

interface Testimonial {
  quote: string;
  name: string;
  position: string;
  image: string;
}

@Component({
  selector: 'app-careers',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatExpansionModule,
    MatTabsModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './careers.component.html',
  styleUrls: ['./careers.component.scss']
})
export class CareersComponent {
  // Job openings
  jobOpenings: JobOpening[] = [
    {
      id: 'JOB001',
      title: 'Mathematics Teacher',
      department: 'Academic',
      type: 'full-time',
      location: 'Main Campus',
      description: 'We are seeking an experienced Mathematics teacher to join our secondary school faculty. The ideal candidate will have a passion for teaching mathematics and the ability to inspire students to achieve their full potential.',
      responsibilities: [
        'Teach mathematics to students in grades 9-12',
        'Develop and implement engaging lesson plans aligned with curriculum standards',
        'Assess student progress and provide constructive feedback',
        'Collaborate with other faculty members to enhance the mathematics program',
        'Participate in parent-teacher conferences and school events',
        'Serve as an advisor to a group of students'
      ],
      qualifications: [
        'Bachelor\'s degree in Mathematics or related field (Master\'s preferred)',
        'Teaching certification in Mathematics',
        'Minimum 3 years of teaching experience at the secondary level',
        'Strong knowledge of mathematics curriculum and teaching methodologies',
        'Excellent communication and interpersonal skills',
        'Ability to integrate technology into classroom instruction'
      ],
      postedDate: new Date('2023-09-15')
    },
    {
      id: 'JOB002',
      title: 'School Counselor',
      department: 'Student Support Services',
      type: 'full-time',
      location: 'Main Campus',
      description: 'We are looking for a dedicated School Counselor to provide academic, social, and emotional support to our students. The successful candidate will work collaboratively with teachers, administrators, and parents to ensure student success.',
      responsibilities: [
        'Provide individual and group counseling to students',
        'Develop and implement comprehensive school counseling programs',
        'Assist students with academic planning and college/career readiness',
        'Collaborate with teachers and parents to address student needs',
        'Coordinate with external resources and agencies when necessary',
        'Maintain accurate and confidential student records'
      ],
      qualifications: [
        'Master\'s degree in School Counseling or related field',
        'Valid counseling certification/license',
        'Experience working with school-aged children',
        'Knowledge of counseling theories and techniques',
        'Strong communication and interpersonal skills',
        'Ability to work effectively in a team environment'
      ],
      postedDate: new Date('2023-09-20')
    },
    {
      id: 'JOB003',
      title: 'IT Support Specialist',
      department: 'Information Technology',
      type: 'full-time',
      location: 'Main Campus',
      description: 'We are seeking an IT Support Specialist to maintain and support our school\'s technology infrastructure. The ideal candidate will have strong technical skills and a commitment to providing excellent service to faculty, staff, and students.',
      responsibilities: [
        'Provide technical support to faculty, staff, and students',
        'Install, configure, and maintain computer hardware and software',
        'Troubleshoot and resolve technology issues in a timely manner',
        'Assist with the implementation of new technologies',
        'Maintain network security and data backup systems',
        'Train users on new software and hardware'
      ],
      qualifications: [
        'Bachelor\'s degree in Computer Science, Information Technology, or related field',
        'Minimum 2 years of experience in IT support',
        'Knowledge of Windows and Mac operating systems',
        'Experience with network administration and security',
        'Strong problem-solving and communication skills',
        'Ability to work independently and as part of a team'
      ],
      postedDate: new Date('2023-09-25')
    },
    {
      id: 'JOB004',
      title: 'Science Laboratory Assistant',
      department: 'Academic',
      type: 'part-time',
      location: 'Main Campus',
      description: 'We are looking for a Science Laboratory Assistant to support our science department. The successful candidate will assist science teachers with laboratory preparation, maintenance, and safety procedures.',
      responsibilities: [
        'Prepare laboratory materials and equipment for science classes',
        'Assist teachers during laboratory sessions',
        'Maintain laboratory equipment and supplies',
        'Ensure compliance with safety regulations and procedures',
        'Order and inventory laboratory supplies',
        'Assist with the development of laboratory activities'
      ],
      qualifications: [
        'Bachelor\'s degree in a science discipline',
        'Experience working in a laboratory setting',
        'Knowledge of laboratory safety procedures',
        'Ability to work effectively with teachers and students',
        'Strong organizational and time management skills',
        'Attention to detail and commitment to safety'
      ],
      postedDate: new Date('2023-10-01')
    },
    {
      id: 'JOB005',
      title: 'Administrative Assistant',
      department: 'Administration',
      type: 'full-time',
      location: 'Main Campus',
      description: 'We are seeking an Administrative Assistant to provide administrative support to our school office. The ideal candidate will be organized, detail-oriented, and have excellent communication skills.',
      responsibilities: [
        'Greet visitors and answer phone calls',
        'Manage correspondence and filing systems',
        'Schedule appointments and meetings',
        'Prepare and distribute documents and reports',
        'Assist with event planning and coordination',
        'Provide general administrative support to school staff'
      ],
      qualifications: [
        'High school diploma required; Associate\'s or Bachelor\'s degree preferred',
        'Minimum 2 years of administrative experience',
        'Proficiency in Microsoft Office applications',
        'Excellent organizational and time management skills',
        'Strong written and verbal communication skills',
        'Ability to maintain confidentiality and work independently'
      ],
      postedDate: new Date('2023-10-05')
    }
  ];

  // Benefits
  benefits: Benefit[] = [
    {
      title: 'Competitive Salary',
      description: 'We offer competitive salaries based on qualifications and experience, with regular performance-based increments.',
      icon: 'payments'
    },
    {
      title: 'Health Insurance',
      description: 'Comprehensive health insurance coverage for employees and their families, including medical, dental, and vision benefits.',
      icon: 'health_and_safety'
    },
    {
      title: 'Retirement Plan',
      description: 'Employer-matched retirement savings plan to help you secure your financial future.',
      icon: 'account_balance'
    },
    {
      title: 'Professional Development',
      description: 'Ongoing professional development opportunities, including workshops, conferences, and tuition reimbursement for advanced degrees.',
      icon: 'school'
    },
    {
      title: 'Work-Life Balance',
      description: 'Generous vacation time, holidays, and personal leave to ensure a healthy work-life balance.',
      icon: 'balance'
    },
    {
      title: 'Supportive Community',
      description: 'Join a collaborative and supportive community of educators and staff dedicated to student success.',
      icon: 'diversity_3'
    }
  ];

  // Testimonials
  testimonials: Testimonial[] = [
    {
      quote: 'Working at this school has been the most rewarding experience of my career. The supportive administration, collaborative colleagues, and motivated students create an environment where I can truly make a difference.',
      name: 'Sarah Johnson',
      position: 'English Teacher, 8 years',
      image: 'assets/images/careers/testimonial1.jpg'
    },
    {
      quote: 'I appreciate the professional development opportunities that have helped me grow as an educator. The school invests in its teachers, which ultimately benefits our students.',
      name: 'Michael Chen',
      position: 'Science Teacher, 5 years',
      image: 'assets/images/careers/testimonial2.jpg'
    },
    {
      quote: 'The collaborative culture here is unlike any school I\'ve worked at before. Teachers across departments work together to create integrated learning experiences for our students.',
      name: 'Aisha Rahman',
      position: 'Mathematics Teacher, 3 years',
      image: 'assets/images/careers/testimonial3.jpg'
    }
  ];

  // Application process steps
  applicationSteps = [
    {
      title: 'Submit Application',
      description: 'Complete the online application form and upload your resume, cover letter, and any required certifications or credentials.'
    },
    {
      title: 'Initial Screening',
      description: 'Our HR team will review your application materials to determine if your qualifications match the position requirements.'
    },
    {
      title: 'Phone Interview',
      description: 'Qualified candidates will be contacted for a preliminary phone interview to discuss their experience and interest in the position.'
    },
    {
      title: 'In-Person Interview',
      description: 'Selected candidates will be invited for an in-person interview with the hiring committee, which may include a teaching demonstration for teaching positions.'
    },
    {
      title: 'Reference Check',
      description: 'We will contact your professional references to learn more about your work experience and qualifications.'
    },
    {
      title: 'Job Offer',
      description: 'Successful candidates will receive a job offer outlining the position details, compensation, and benefits.'
    }
  ];

  // FAQs
  faqs = [
    {
      question: 'What qualifications do I need to teach at your school?',
      answer: 'Teaching positions typically require a bachelor\'s degree in the relevant subject area, teaching certification, and experience working with students. Specific qualifications vary by position and are detailed in each job posting.'
    },
    {
      question: 'How can I check the status of my application?',
      answer: 'After submitting your application, you will receive a confirmation email with instructions on how to check your application status. You can also contact our HR department at careers&#64;school.edu for updates.'
    },
    {
      question: 'Do you offer opportunities for professional development?',
      answer: 'Yes, we are committed to the professional growth of our staff. We offer regular in-house workshops, support attendance at conferences, and provide tuition reimbursement for approved courses and degree programs.'
    },
    {
      question: 'What is the school culture like?',
      answer: 'Our school culture is characterized by collaboration, innovation, and a student-centered approach. We value diversity, inclusion, and mutual respect among all members of our community. We work together to create a positive and supportive learning environment.'
    },
    {
      question: 'Are there opportunities for advancement within the school?',
      answer: 'Yes, we encourage internal advancement and provide pathways for career growth. Many of our department heads and administrators began as teachers in our school. We offer leadership training and mentoring to support staff who are interested in taking on new responsibilities.'
    }
  ];

  // Filter jobs by department
  departments = ['All', 'Academic', 'Administration', 'Student Support Services', 'Information Technology'];
  selectedDepartment = 'All';

  getFilteredJobs(): JobOpening[] {
    if (this.selectedDepartment === 'All') {
      return this.jobOpenings;
    }
    return this.jobOpenings.filter(job => job.department === this.selectedDepartment);
  }

  // Calculate days since posting
  getDaysSincePosting(postedDate: Date): number {
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - postedDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }
}
