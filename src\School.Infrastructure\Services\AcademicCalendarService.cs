using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.AcademicCalendar;
using School.Infrastructure.Services;
using School.Domain.Entities;
using School.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Services
{
    public class AcademicCalendarService : IAcademicCalendarService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<AcademicCalendarService> _logger;
        private readonly IHolidayService _holidayService;

        public AcademicCalendarService(
            IUnitOfWork unitOfWork,
            ICurrentUserService currentUserService,
            ILogger<AcademicCalendarService> logger,
            IHolidayService holidayService)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
            _logger = logger;
            _holidayService = holidayService;
        }

        public async Task<(IEnumerable<AcademicCalendarDto> AcademicCalendars, int TotalCount)> GetAllAcademicCalendarsAsync(AcademicCalendarFilterDto filter)
        {
            var repository = _unitOfWork.Repository<AcademicCalendar>();
            var query = repository.AsQueryable("Translations");

            // Apply filters
            if (!string.IsNullOrEmpty(filter.Title))
            {
                query = query.Where(a => a.Title.Contains(filter.Title));
            }

            if (filter.Type.HasValue)
            {
                query = query.Where(a => a.Type == filter.Type.Value);
            }

            if (filter.AcademicYearId.HasValue)
            {
                query = query.Where(a => a.AcademicYearId == filter.AcademicYearId.Value);
            }

            if (filter.TermId.HasValue)
            {
                query = query.Where(a => a.TermId == filter.TermId.Value);
            }

            // Filter by date range if provided in the filter
            DateTime? startDate = null;
            if (filter.StartDate != null)
            {
                startDate = DateTime.Parse(filter.StartDate);
                query = query.Where(a => a.StartDate >= startDate);
            }

            DateTime? endDate = null;
            if (filter.EndDate != null)
            {
                endDate = DateTime.Parse(filter.EndDate);
                query = query.Where(a => a.EndDate <= endDate);
            }

            if (filter.IsActive.HasValue)
            {
                query = query.Where(a => a.IsActive == filter.IsActive.Value);
            }

            if (filter.IsPublic.HasValue)
            {
                query = query.Where(a => a.IsPublic == filter.IsPublic.Value);
            }

            // Apply sorting
            if (!string.IsNullOrEmpty(filter.SortBy))
            {
                query = filter.SortBy.ToLower() switch
                {
                    "title" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(a => a.Title) :
                        query.OrderBy(a => a.Title),
                    "startdate" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(a => a.StartDate) :
                        query.OrderBy(a => a.StartDate),
                    "enddate" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(a => a.EndDate) :
                        query.OrderBy(a => a.EndDate),
                    "academicyear" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(a => a.AcademicYear!.StartDate) :
                        query.OrderBy(a => a.AcademicYear!.StartDate),
                    "createdat" => filter.SortDirection?.ToLower() == "desc" ?
                        query.OrderByDescending(a => a.CreatedAt) :
                        query.OrderBy(a => a.CreatedAt),
                    _ => query.OrderBy(a => a.StartDate)
                };
            }
            else
            {
                query = query.OrderBy(a => a.StartDate);
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply pagination
            var academicCalendars = await query
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(a => new AcademicCalendarDto
                {
                    Id = a.Id,
                    Title = a.Title,
                    Description = a.Description,
                    StartDate = a.StartDate,
                    EndDate = a.EndDate,
                    IsAllDay = a.IsAllDay,
                    Location = a.Location,
                    Type = a.Type,
                    Color = a.Color,
                    IsActive = a.IsActive,
                    IsPublic = a.IsPublic,
                    AcademicYearId = a.AcademicYearId,
                    TermId = a.TermId,
                    AcademicYearName = a.AcademicYear != null ? a.AcademicYear.DisplayName : string.Empty,
                    TermName = a.Term != null ? a.Term.Name : string.Empty,
                    CreatedAt = a.CreatedAt,
                    LastModifiedAt = a.LastModifiedAt,
                    Translations = a.Translations.Select(t => new AcademicCalendarTranslationDto
                    {
                        Id = t.Id,
                        AcademicCalendarId = t.AcademicCalendarId,
                        LanguageCode = t.LanguageCode,
                        Title = t.Title,
                        Description = t.Description,
                        Location = t.Location
                    }).ToList()
                })
                .ToListAsync();

            return (academicCalendars, totalCount);
        }

        public async Task<AcademicCalendarDto?> GetAcademicCalendarByIdAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<AcademicCalendar>();
            var academicCalendar = await repository.GetByIdAsync(id, new[] { "Translations" });

            if (academicCalendar == null) return null;

            return new AcademicCalendarDto
            {
                Id = academicCalendar.Id,
                Title = academicCalendar.Title,
                Description = academicCalendar.Description,
                StartDate = academicCalendar.StartDate,
                EndDate = academicCalendar.EndDate,
                IsAllDay = academicCalendar.IsAllDay,
                Location = academicCalendar.Location,
                Type = academicCalendar.Type,
                Color = academicCalendar.Color,
                IsActive = academicCalendar.IsActive,
                IsPublic = academicCalendar.IsPublic,
                AcademicYearId = academicCalendar.AcademicYearId,
                TermId = academicCalendar.TermId,
                AcademicYearName = academicCalendar.AcademicYear?.DisplayName ?? string.Empty,
                TermName = academicCalendar.Term?.Name ?? string.Empty,
                CreatedAt = academicCalendar.CreatedAt,
                LastModifiedAt = academicCalendar.LastModifiedAt,
                Translations = academicCalendar.Translations.Select(t => new AcademicCalendarTranslationDto
                {
                    Id = t.Id,
                    AcademicCalendarId = t.AcademicCalendarId,
                    LanguageCode = t.LanguageCode,
                    Title = t.Title,
                    Description = t.Description,
                    Location = t.Location
                }).ToList()
            };
        }

        public async Task<IEnumerable<AcademicCalendarEventDto>> GetCalendarEventsAsync(int academicYear = 0, string? semester = null)
        {
            var repository = _unitOfWork.Repository<AcademicCalendar>();
            var query = repository.AsQueryable();

            query = query.Where(a => a.IsActive && a.IsPublic);

            if (academicYear > 0)
            {
                query = query.Where(a => a.AcademicYear != null && a.AcademicYear.Name.Contains(academicYear.ToString()));
            }

            if (!string.IsNullOrEmpty(semester))
            {
                query = query.Where(a => a.Term != null && a.Term.Name.Contains(semester));
            }

            var events = await query
                .OrderBy(a => a.StartDate)
                .Select(a => new AcademicCalendarEventDto
                {
                    Id = a.Id,
                    Title = a.Title,
                    Start = a.StartDate,
                    End = a.EndDate,
                    AllDay = a.IsAllDay,
                    Location = a.Location,
                    Type = a.Type,
                    Color = a.Color
                })
                .ToListAsync();

            return events;
        }

        public async Task<Guid> CreateAcademicCalendarAsync(CreateAcademicCalendarDto academicCalendarDto)
        {
            var repository = _unitOfWork.Repository<AcademicCalendar>();

            var academicCalendar = new AcademicCalendar
            {
                Title = academicCalendarDto.Title,
                Description = academicCalendarDto.Description,
                StartDate = academicCalendarDto.StartDate,
                EndDate = academicCalendarDto.EndDate,
                IsAllDay = academicCalendarDto.IsAllDay,
                Location = academicCalendarDto.Location,
                Type = academicCalendarDto.Type,
                Color = academicCalendarDto.Color,
                IsActive = academicCalendarDto.IsActive,
                IsPublic = academicCalendarDto.IsPublic,
                // AcademicYearId and TermId will be set based on the DTO values
                // For now, we'll leave them null and implement proper mapping later
                CreatedAt = DateTime.UtcNow,
                CreatedBy = _currentUserService.UserId.ToString()
            };

            await repository.AddAsync(academicCalendar);
            await _unitOfWork.SaveChangesAsync();

            // Add translations if provided
            if (academicCalendarDto.Translations != null && academicCalendarDto.Translations.Any())
            {
                var translationRepository = _unitOfWork.Repository<AcademicCalendarTranslation>();

                foreach (var translationDto in academicCalendarDto.Translations)
                {
                    var translation = new AcademicCalendarTranslation
                    {
                        AcademicCalendarId = academicCalendar.Id,
                        LanguageCode = translationDto.LanguageCode,
                        Title = translationDto.Title,
                        Description = translationDto.Description,
                        Location = translationDto.Location,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = _currentUserService.UserId.ToString()
                    };

                    await translationRepository.AddAsync(translation);
                }

                await _unitOfWork.SaveChangesAsync();
            }

            _logger.LogInformation("Academic calendar created with ID {AcademicCalendarId}", academicCalendar.Id);
            return academicCalendar.Id;
        }

        public async Task<bool> UpdateAcademicCalendarAsync(Guid id, UpdateAcademicCalendarDto academicCalendarDto)
        {
            var repository = _unitOfWork.Repository<AcademicCalendar>();
            var academicCalendar = await repository.GetByIdAsync(id);

            if (academicCalendar == null) return false;

            // Update properties
            academicCalendar.Title = academicCalendarDto.Title;
            academicCalendar.Description = academicCalendarDto.Description;
            academicCalendar.StartDate = academicCalendarDto.StartDate;
            academicCalendar.EndDate = academicCalendarDto.EndDate;
            academicCalendar.IsAllDay = academicCalendarDto.IsAllDay;
            academicCalendar.Location = academicCalendarDto.Location;
            academicCalendar.Type = academicCalendarDto.Type;
            academicCalendar.Color = academicCalendarDto.Color;
            academicCalendar.IsActive = academicCalendarDto.IsActive;
            academicCalendar.IsPublic = academicCalendarDto.IsPublic;
            // AcademicYearId and TermId updates will be implemented later
            // LastModifiedAt is set below
            academicCalendar.LastModifiedBy = _currentUserService.UserId.ToString();
            academicCalendar.LastModifiedAt = DateTime.UtcNow;

            await repository.UpdateAsync(academicCalendar);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Academic calendar updated with ID {AcademicCalendarId}", academicCalendar.Id);
            return true;
        }

        public async Task<bool> DeleteAcademicCalendarAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<AcademicCalendar>();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Academic calendar deleted with ID {AcademicCalendarId}", id);
            return true;
        }

        #region Translation Methods

        public async Task<bool> AddTranslationAsync(Guid academicCalendarId, CreateAcademicCalendarTranslationDto translationDto)
        {
            var academicCalendarRepository = _unitOfWork.Repository<AcademicCalendar>();
            var academicCalendar = await academicCalendarRepository.GetByIdAsync(academicCalendarId, new[] { "Translations" });

            if (academicCalendar == null) return false;

            // Check if translation for this language already exists
            var existingTranslation = academicCalendar.Translations
                .FirstOrDefault(t => t.LanguageCode == translationDto.LanguageCode);

            if (existingTranslation != null)
            {
                _logger.LogWarning("Translation for language {LanguageCode} already exists for academic calendar {AcademicCalendarId}",
                    translationDto.LanguageCode, academicCalendarId);
                return false;
            }

            var translationRepository = _unitOfWork.Repository<AcademicCalendarTranslation>();

            var translation = new AcademicCalendarTranslation
            {
                AcademicCalendarId = academicCalendarId,
                LanguageCode = translationDto.LanguageCode,
                Title = translationDto.Title,
                Description = translationDto.Description,
                Location = translationDto.Location,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = _currentUserService.UserId.ToString()
            };

            await translationRepository.AddAsync(translation);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Translation added for academic calendar {AcademicCalendarId} in language {LanguageCode}",
                academicCalendarId, translationDto.LanguageCode);
            return true;
        }

        public async Task<bool> UpdateTranslationAsync(Guid academicCalendarId, string languageCode, UpdateAcademicCalendarTranslationDto translationDto)
        {
            var translationRepository = _unitOfWork.Repository<AcademicCalendarTranslation>();

            var translations = await translationRepository.FindAsync(
                t => t.AcademicCalendarId == academicCalendarId && t.LanguageCode == languageCode);

            if (!translations.Any()) return false;

            var translation = translations.First();

            // Update properties
            translation.Title = translationDto.Title;
            translation.Description = translationDto.Description;
            translation.Location = translationDto.Location;
            // LastModifiedAt is set below
            translation.LastModifiedBy = _currentUserService.UserId.ToString();
            translation.LastModifiedAt = DateTime.UtcNow;

            await translationRepository.UpdateAsync(translation);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Translation updated for academic calendar {AcademicCalendarId} in language {LanguageCode}",
                academicCalendarId, languageCode);
            return true;
        }

        public async Task<bool> DeleteTranslationAsync(Guid academicCalendarId, string languageCode)
        {
            var translationRepository = _unitOfWork.Repository<AcademicCalendarTranslation>();

            var translations = await translationRepository.FindAsync(
                t => t.AcademicCalendarId == academicCalendarId && t.LanguageCode == languageCode);

            if (!translations.Any()) return false;

            var translation = translations.First();

            await translationRepository.DeleteAsync(translation);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Translation deleted for academic calendar {AcademicCalendarId} in language {LanguageCode}",
                academicCalendarId, languageCode);
            return true;
        }

        #endregion

        #region Enhanced Calendar Integration Methods

        public async Task<IEnumerable<CalendarEventDto>> GetIntegratedCalendarEventsAsync(DateTime startDate, DateTime endDate, Guid? academicYearId = null, Guid? termId = null)
        {
            var events = new List<CalendarEventDto>();

            // Get Academic Calendar Events
            var academicEvents = await GetAcademicCalendarEventsInternalAsync(startDate, endDate, academicYearId, termId);
            events.AddRange(academicEvents);

            // Get Holiday Events
            var holidayEvents = await _holidayService.GetHolidayEventsAsync(startDate, endDate, academicYearId, termId);
            if (holidayEvents != null)
            {
                var holidayCalendarEvents = holidayEvents.Select(h => new CalendarEventDto
                {
                    Id = h.Id,
                    Title = h.Name,
                    Description = "",
                    StartDate = h.Start,
                    EndDate = h.End,
                    IsAllDay = h.AllDay,
                    Location = "",
                    Color = h.Color,
                    EventType = CalendarEventType.Holiday,
                    EventTypeName = "Holiday",
                    IsActive = true,
                    IsPublic = true,
                    AcademicYearId = academicYearId,
                    TermId = termId,
                    AcademicYearName = "",
                    TermName = "",
                    IsRecurring = h.IsRecurring,
                    Source = "Holiday"
                });
                events.AddRange(holidayCalendarEvents);
            }

            return events.OrderBy(e => e.StartDate);
        }

        public async Task<IEnumerable<CalendarEventDto>> GetAcademicYearCalendarAsync(Guid academicYearId)
        {
            var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
            var academicYear = await repository.GetByIdAsync(academicYearId);

            if (academicYear == null)
                return Enumerable.Empty<CalendarEventDto>();

            return await GetIntegratedCalendarEventsAsync(academicYear.StartDate, academicYear.EndDate, academicYearId);
        }

        public async Task<IEnumerable<CalendarEventDto>> GetTermCalendarAsync(Guid termId)
        {
            var repository = _unitOfWork.Repository<Term>();
            var term = await repository.GetByIdAsync(termId);

            if (term == null)
                return Enumerable.Empty<CalendarEventDto>();

            return await GetIntegratedCalendarEventsAsync(term.StartDate, term.EndDate, term.AcademicYearId, termId);
        }

        public async Task<IEnumerable<CalendarEventDto>> GetCurrentAcademicYearCalendarAsync()
        {
            var repository = _unitOfWork.Repository<Domain.Entities.AcademicYear>();
            var currentYear = await repository.AsQueryable()
                .FirstOrDefaultAsync(ay => ay.IsCurrentYear);

            if (currentYear == null)
                return Enumerable.Empty<CalendarEventDto>();

            return await GetAcademicYearCalendarAsync(currentYear.Id);
        }

        public async Task<CalendarStatisticsDto> GetCalendarStatisticsAsync(Guid? academicYearId = null, Guid? termId = null)
        {
            var repository = _unitOfWork.Repository<AcademicCalendar>();
            var query = repository.AsQueryable();

            // Apply filters
            if (academicYearId.HasValue)
                query = query.Where(ac => ac.AcademicYearId == academicYearId.Value);

            if (termId.HasValue)
                query = query.Where(ac => ac.TermId == termId.Value);

            var academicEvents = await query.ToListAsync();

            // Get holiday statistics
            var holidayStats = await _holidayService.GetHolidayStatisticsAsync(academicYearId, termId);

            var holidayCount = holidayStats?.Values.Sum() ?? 0;

            var stats = new CalendarStatisticsDto
            {
                TotalEvents = academicEvents.Count + holidayCount,
                AcademicEvents = academicEvents.Count,
                Holidays = holidayCount,
                ExamEvents = academicEvents.Count(e => e.Type == AcademicCalendarType.Exam),
                AdmissionEvents = academicEvents.Count(e => e.Type == AcademicCalendarType.Registration),
                CulturalEvents = academicEvents.Count(e => e.Type == AcademicCalendarType.Event),
                SportsEvents = academicEvents.Count(e => e.Type == AcademicCalendarType.Event),
                MeetingEvents = academicEvents.Count(e => e.Type == AcademicCalendarType.Event),
                OtherEvents = academicEvents.Count(e => e.Type == AcademicCalendarType.Other),
                PublicEvents = academicEvents.Count(e => e.IsPublic),
                PrivateEvents = academicEvents.Count(e => !e.IsPublic),
                RecurringEvents = 0, // Academic events are typically not recurring
                OneTimeEvents = academicEvents.Count,
                CurrentMonthEvents = academicEvents.Count(e => e.StartDate.Month == DateTime.Now.Month && e.StartDate.Year == DateTime.Now.Year),
                UpcomingEvents = academicEvents.Count(e => e.StartDate > DateTime.Now)
            };

            // Build monthly distribution
            var monthlyEvents = academicEvents
                .GroupBy(e => e.StartDate.ToString("yyyy-MM"))
                .ToDictionary(g => g.Key, g => g.Count());
            stats.EventsByMonth = monthlyEvents;

            // Build type distribution
            var typeEvents = academicEvents
                .GroupBy(e => e.Type.ToString())
                .ToDictionary(g => g.Key, g => g.Count());
            stats.EventsByType = typeEvents;

            return stats;
        }

        public async Task<int> GetTotalEventsInPeriodAsync(DateTime startDate, DateTime endDate, Guid? academicYearId = null, Guid? termId = null)
        {
            var repository = _unitOfWork.Repository<AcademicCalendar>();
            var query = repository.AsQueryable();

            // Apply date filter
            query = query.Where(ac => ac.StartDate >= startDate && ac.StartDate <= endDate);

            // Apply additional filters
            if (academicYearId.HasValue)
                query = query.Where(ac => ac.AcademicYearId == academicYearId.Value);

            if (termId.HasValue)
                query = query.Where(ac => ac.TermId == termId.Value);

            var academicEventCount = await query.CountAsync();
            var holidayCount = await _holidayService.GetTotalHolidaysInPeriodAsync(startDate, endDate, academicYearId, termId);

            return academicEventCount + holidayCount;
        }

        public async Task<Dictionary<string, int>> GetEventTypeDistributionAsync(Guid? academicYearId = null, Guid? termId = null)
        {
            var repository = _unitOfWork.Repository<AcademicCalendar>();
            var query = repository.AsQueryable();

            if (academicYearId.HasValue)
                query = query.Where(ac => ac.AcademicYearId == academicYearId.Value);

            if (termId.HasValue)
                query = query.Where(ac => ac.TermId == termId.Value);

            var academicEvents = await query.ToListAsync();
            var distribution = academicEvents
                .GroupBy(e => e.Type.ToString())
                .ToDictionary(g => g.Key, g => g.Count());

            // Add holiday statistics
            var holidayStats = await _holidayService.GetHolidayStatisticsAsync(academicYearId, termId);
            if (holidayStats != null)
            {
                foreach (var stat in holidayStats)
                {
                    distribution[stat.Key] = stat.Value;
                }
            }

            return distribution;
        }

        #endregion

        #region Calendar Validation Methods

        public async Task<bool> ValidateEventDatesAsync(Guid? academicYearId, Guid? termId, DateTime startDate, DateTime endDate, Guid? excludeId = null)
        {
            // Check for overlapping academic calendar events
            var repository = _unitOfWork.Repository<AcademicCalendar>();
            var query = repository.AsQueryable();

            // Apply filters
            if (academicYearId.HasValue)
                query = query.Where(ac => ac.AcademicYearId == academicYearId.Value);

            if (termId.HasValue)
                query = query.Where(ac => ac.TermId == termId.Value);

            if (excludeId.HasValue)
                query = query.Where(ac => ac.Id != excludeId.Value);

            // Check for date overlaps
            var hasOverlap = await query.AnyAsync(ac =>
                (ac.StartDate <= endDate && ac.EndDate >= startDate));

            if (hasOverlap)
                return false;

            // Check for holiday overlaps
            return await _holidayService.ValidateHolidayDatesAsync(academicYearId, termId, startDate, endDate, excludeId);
        }

        public async Task<IEnumerable<AcademicCalendarDto>> GetOverlappingEventsAsync(Guid? academicYearId, Guid? termId, DateTime startDate, DateTime endDate, Guid? excludeId = null)
        {
            var repository = _unitOfWork.Repository<AcademicCalendar>();
            var query = repository.AsQueryable("AcademicYear", "Term", "Translations");

            // Apply filters
            if (academicYearId.HasValue)
                query = query.Where(ac => ac.AcademicYearId == academicYearId.Value);

            if (termId.HasValue)
                query = query.Where(ac => ac.TermId == termId.Value);

            if (excludeId.HasValue)
                query = query.Where(ac => ac.Id != excludeId.Value);

            // Check for date overlaps
            query = query.Where(ac =>
                (ac.StartDate <= endDate && ac.EndDate >= startDate));

            var overlappingEvents = await query.ToListAsync();

            return overlappingEvents.Select(ac => new AcademicCalendarDto
            {
                Id = ac.Id,
                Title = ac.Title,
                Description = ac.Description,
                StartDate = ac.StartDate,
                EndDate = ac.EndDate,
                IsAllDay = ac.IsAllDay,
                Location = ac.Location,
                Color = ac.Color,
                Type = ac.Type,
                IsActive = ac.IsActive,
                IsPublic = ac.IsPublic,
                AcademicYearId = ac.AcademicYearId,
                TermId = ac.TermId,
                AcademicYearName = ac.AcademicYear?.DisplayName ?? "",
                TermName = ac.Term?.Name ?? "",
                CreatedAt = ac.CreatedAt,
                Translations = ac.Translations.Select(t => new AcademicCalendarTranslationDto
                {
                    Id = t.Id,
                    AcademicCalendarId = t.AcademicCalendarId,
                    LanguageCode = t.LanguageCode,
                    Title = t.Title,
                    Description = t.Description,
                    Location = t.Location
                }).ToList()
            });
        }

        #endregion

        #region Private Helper Methods

        private async Task<IEnumerable<CalendarEventDto>> GetAcademicCalendarEventsInternalAsync(DateTime startDate, DateTime endDate, Guid? academicYearId = null, Guid? termId = null)
        {
            var repository = _unitOfWork.Repository<AcademicCalendar>();
            var query = repository.AsQueryable("AcademicYear", "Term");

            // Apply date filter
            query = query.Where(ac => ac.StartDate >= startDate && ac.StartDate <= endDate);

            // Apply additional filters
            if (academicYearId.HasValue)
                query = query.Where(ac => ac.AcademicYearId == academicYearId.Value);

            if (termId.HasValue)
                query = query.Where(ac => ac.TermId == termId.Value);

            // Only active and public events
            query = query.Where(ac => ac.IsActive && ac.IsPublic);

            var events = await query.OrderBy(ac => ac.StartDate).ToListAsync();

            return events.Select(ac => new CalendarEventDto
            {
                Id = ac.Id,
                Title = ac.Title,
                Description = ac.Description,
                StartDate = ac.StartDate,
                EndDate = ac.EndDate,
                IsAllDay = ac.IsAllDay,
                Location = ac.Location,
                Color = ac.Color,
                EventType = MapToCalendarEventType(ac.Type),
                EventTypeName = ac.Type.ToString(),
                IsActive = ac.IsActive,
                IsPublic = ac.IsPublic,
                AcademicYearId = ac.AcademicYearId,
                TermId = ac.TermId,
                AcademicYearName = ac.AcademicYear?.DisplayName ?? "",
                TermName = ac.Term?.Name ?? "",
                IsRecurring = false,
                Source = "AcademicCalendar"
            });
        }

        private static CalendarEventType MapToCalendarEventType(AcademicCalendarType type)
        {
            return type switch
            {
                AcademicCalendarType.Exam => CalendarEventType.Exam,
                AcademicCalendarType.Registration => CalendarEventType.Admission,
                AcademicCalendarType.Event => CalendarEventType.Cultural,
                AcademicCalendarType.Holiday => CalendarEventType.Holiday,
                _ => CalendarEventType.AcademicEvent
            };
        }

        public async Task<IEnumerable<AcademicCalendarTranslationDto>> GetTranslationsAsync(Guid academicCalendarId)
        {
            var translationRepository = _unitOfWork.Repository<AcademicCalendarTranslation>();
            var translations = await translationRepository.AsQueryable()
                .Where(t => t.AcademicCalendarId == academicCalendarId)
                .ToListAsync();

            return translations.Select(t => new AcademicCalendarTranslationDto
            {
                Id = t.Id,
                AcademicCalendarId = t.AcademicCalendarId,
                LanguageCode = t.LanguageCode,
                Title = t.Title,
                Description = t.Description,
                Location = t.Location
            });
        }

        #endregion
    }
}
