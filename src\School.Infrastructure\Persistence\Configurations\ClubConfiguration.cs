using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ClubConfiguration : IEntityTypeConfiguration<Club>
{
    public void Configure(EntityTypeBuilder<Club> builder)
    {
        builder.HasKey(c => c.Id);
        
        builder.Property(c => c.Name)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(c => c.Description)
            .IsRequired();
            
        builder.Property(c => c.ShortDescription)
            .HasMaxLength(250);
            
        builder.Property(c => c.Category)
            .IsRequired()
            .HasMaxLength(50);
            
        builder.Property(c => c.MeetingSchedule)
            .HasMaxLength(100);
            
        builder.Property(c => c.Location)
            .HasMaxLength(100);
            
        builder.Property(c => c.ContactEmail)
            .HasMaxLength(100);
            
        builder.Property(c => c.Website)
            .HasMaxLength(255);
            
        builder.Property(c => c.Instagram)
            .HasMaxLength(255);
            
        builder.Property(c => c.Facebook)
            .HasMaxLength(255);
            
        builder.Property(c => c.IsFeatured)
            .HasDefaultValue(false);
            
        builder.Property(c => c.DisplayOrder)
            .HasDefaultValue(0);
            
        builder.Property(c => c.IsActive)
            .HasDefaultValue(true);
            
        builder.Property(c => c.ProfileImageUrl)
            .HasMaxLength(255);
            
        // Relationships
        builder.HasMany(c => c.Translations)
            .WithOne(t => t.Club)
            .HasForeignKey(t => t.ClubId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(c => c.Advisors)
            .WithOne(a => a.Club)
            .HasForeignKey(a => a.ClubId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(c => c.Leaders)
            .WithOne(l => l.Club)
            .HasForeignKey(l => l.ClubId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(c => c.Activities)
            .WithOne(a => a.Club)
            .HasForeignKey(a => a.ClubId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(c => c.Achievements)
            .WithOne(a => a.Club)
            .HasForeignKey(a => a.ClubId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(c => c.Events)
            .WithOne(e => e.Club)
            .HasForeignKey(e => e.ClubId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(c => c.GalleryItems)
            .WithOne(g => g.Club)
            .HasForeignKey(g => g.ClubId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
