using School.Application.DTOs;

namespace School.Application.Features.Term;

public interface ITermService
{
    // Term CRUD operations
    Task<(IEnumerable<TermDto> Terms, int TotalCount)> GetAllTermsAsync(TermFilterDto filter);
    Task<TermDto?> GetTermByIdAsync(Guid id);
    Task<IEnumerable<TermDto>> GetTermsByAcademicYearAsync(Guid academicYearId);
    Task<TermDto?> GetCurrentTermAsync();
    Task<TermDto?> GetActiveTermByAcademicYearAsync(Guid academicYearId);
    Task<Guid> CreateTermAsync(CreateTermDto termDto);
    Task<bool> UpdateTermAsync(Guid id, UpdateTermDto termDto);
    Task<bool> DeleteTermAsync(Guid id);
    
    // Term status management
    Task<bool> ActivateTermAsync(Guid id);
    Task<bool> CompleteTermAsync(Guid id);
    Task<bool> CancelTermAsync(Guid id);
    
    // Term validation
    Task<bool> ValidateTermDatesAsync(Guid academicYearId, DateTime startDate, DateTime endDate, Guid? excludeId = null);
    Task<bool> ValidateTermOrderAsync(Guid academicYearId, int orderIndex, Guid? excludeId = null);
    Task<bool> CanDeleteTermAsync(Guid id);
    
    // Term management utilities
    Task<bool> ReorderTermsAsync(Guid academicYearId, List<Guid> termIds);
    Task<IEnumerable<TermDto>> GetOverlappingTermsAsync(Guid academicYearId, DateTime startDate, DateTime endDate, Guid? excludeId = null);
    
    // Translation management
    Task<bool> AddTranslationAsync(Guid termId, CreateTermTranslationDto translationDto);
    Task<bool> UpdateTranslationAsync(Guid termId, string languageCode, UpdateTermTranslationDto translationDto);
    Task<bool> DeleteTranslationAsync(Guid termId, string languageCode);
    Task<IEnumerable<TermTranslationDto>> GetTranslationsAsync(Guid termId);
    
    // Term statistics
    Task<int> GetTotalStudentsInTermAsync(Guid termId);
    Task<Dictionary<string, int>> GetTermStatisticsAsync(Guid termId);
}
