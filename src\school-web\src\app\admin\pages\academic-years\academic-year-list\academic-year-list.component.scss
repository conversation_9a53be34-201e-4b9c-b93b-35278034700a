.academic-year-list-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    gap: 16px;

    .header-content {
      flex: 1;

      .page-title {
        margin: 0 0 8px 0;
        font-size: 2rem;
        font-weight: 500;
        color: var(--mat-sys-on-surface);
      }

      .page-subtitle {
        margin: 0;
        color: var(--mat-sys-on-surface-variant);
        font-size: 1rem;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
      flex-shrink: 0;

      button {
        min-width: 160px;
      }
    }
  }

  .filter-card {
    margin-bottom: 24px;

    .filter-form {
      .filter-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
        margin-bottom: 16px;

        mat-form-field {
          width: 100%;
        }
      }

      .filter-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;
      }
    }
  }

  .table-card {
    .table-container {
      position: relative;
      min-height: 400px;

      .academic-year-table {
        width: 100%;

        .name-cell {
          display: flex;
          flex-direction: column;
          gap: 4px;

          strong {
            font-weight: 500;
            color: var(--mat-sys-on-surface);
          }

          small {
            color: var(--mat-sys-on-surface-variant);
            font-size: 0.875rem;
          }
        }

        code {
          background-color: var(--mat-sys-surface-variant);
          color: var(--mat-sys-on-surface-variant);
          padding: 4px 8px;
          border-radius: 4px;
          font-family: 'Roboto Mono', monospace;
          font-size: 0.875rem;
        }

        .action-buttons {
          display: flex;
          gap: 4px;
          align-items: center;

          button {
            width: 36px;
            height: 36px;
            
            mat-icon {
              font-size: 18px;
              width: 18px;
              height: 18px;
            }
          }
        }

        .text-muted {
          color: var(--mat-sys-on-surface-variant);
        }

        mat-chip {
          font-size: 0.75rem;
          min-height: 24px;
          border-radius: 12px;
        }
      }

      .loading-container {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
      }

      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 48px 24px;
        text-align: center;
        color: var(--mat-sys-on-surface-variant);

        mat-icon {
          font-size: 64px;
          width: 64px;
          height: 64px;
          margin-bottom: 16px;
          opacity: 0.5;
        }

        h3 {
          margin: 0 0 8px 0;
          font-size: 1.25rem;
          font-weight: 500;
        }

        p {
          margin: 0 0 24px 0;
          font-size: 1rem;
        }
      }
    }

    mat-paginator {
      border-top: 1px solid var(--mat-sys-outline-variant);
      margin-top: 16px;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .academic-year-list-container {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: stretch;

      .header-actions {
        justify-content: stretch;

        button {
          min-width: auto;
          flex: 1;
        }
      }
    }

    .filter-card {
      .filter-form {
        .filter-row {
          grid-template-columns: 1fr;
        }
      }
    }

    .table-card {
      .table-container {
        overflow-x: auto;

        .academic-year-table {
          min-width: 800px;
        }
      }
    }
  }
}

// Dark Theme Support
@media (prefers-color-scheme: dark) {
  .academic-year-list-container {
    .page-header {
      .page-title {
        color: var(--mat-sys-on-surface);
      }

      .page-subtitle {
        color: var(--mat-sys-on-surface-variant);
      }
    }

    .table-card {
      .table-container {
        .academic-year-table {
          .name-cell {
            strong {
              color: var(--mat-sys-on-surface);
            }

            small {
              color: var(--mat-sys-on-surface-variant);
            }
          }

          code {
            background-color: var(--mat-sys-surface-variant);
            color: var(--mat-sys-on-surface-variant);
          }

          .text-muted {
            color: var(--mat-sys-on-surface-variant);
          }
        }

        .no-data {
          color: var(--mat-sys-on-surface-variant);
        }
      }
    }
  }
}

// High Contrast Support
@media (prefers-contrast: high) {
  .academic-year-list-container {
    .table-card {
      .table-container {
        .academic-year-table {
          border: 2px solid var(--mat-sys-outline);

          th, td {
            border: 1px solid var(--mat-sys-outline);
          }
        }
      }
    }
  }
}

// Animation Classes
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
