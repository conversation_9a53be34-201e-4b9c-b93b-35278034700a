import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatStepperModule } from '@angular/material/stepper';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';

import { AcademicYearService } from '../../../../core/services/academic-year.service';
import { TermService } from '../../../../core/services/term.service';
import { 
  AcademicYear, 
  CreateAcademicYear, 
  UpdateAcademicYear,
  AcademicYearStatus,
  TermType,
  TermStatus,
  CreateTerm
} from '../../../../core/models/academic-year.model';

@Component({
  selector: 'app-academic-year-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatStepperModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDividerModule,
    MatChipsModule,
    MatTooltipModule,
    DragDropModule
  ],
  templateUrl: './academic-year-form.component.html',
  styleUrls: ['./academic-year-form.component.scss']
})
export class AcademicYearFormComponent implements OnInit {
  academicYearForm: FormGroup;
  termsForm: FormGroup;
  reviewForm: FormGroup;
  
  isEditMode = false;
  academicYearId?: string;
  loading = false;
  saving = false;

  statusOptions = [
    { value: AcademicYearStatus.Draft, label: 'Draft' },
    { value: AcademicYearStatus.Active, label: 'Active' },
    { value: AcademicYearStatus.Completed, label: 'Completed' },
    { value: AcademicYearStatus.Archived, label: 'Archived' }
  ];

  termTypeOptions = [
    { value: TermType.Semester, label: 'Semester' },
    { value: TermType.Trimester, label: 'Trimester' },
    { value: TermType.Quarter, label: 'Quarter' },
    { value: TermType.Annual, label: 'Annual' },
    { value: TermType.Custom, label: 'Custom' }
  ];

  termStatusOptions = [
    { value: TermStatus.Planned, label: 'Planned' },
    { value: TermStatus.Active, label: 'Active' }
  ];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private academicYearService: AcademicYearService,
    private termService: TermService,
    private snackBar: MatSnackBar
  ) {
    this.academicYearForm = this.createAcademicYearForm();
    this.termsForm = this.createTermsForm();
    this.reviewForm = this.createReviewForm();
  }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.academicYearId = params['id'];
        this.loadAcademicYear();
      } else {
        this.addDefaultTerms();
      }
    });
  }

  private createAcademicYearForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      displayName: ['', [Validators.required, Validators.maxLength(200)]],
      code: ['', [Validators.required, Validators.maxLength(20)]],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      status: [AcademicYearStatus.Draft, Validators.required],
      isCurrentYear: [false],
      description: ['', Validators.maxLength(500)],
      remarks: ['', Validators.maxLength(500)],
      totalWorkingDays: [0, [Validators.min(0), Validators.max(366)]],
      totalHolidays: [0, [Validators.min(0), Validators.max(366)]],
      registrationStartDate: [''],
      registrationEndDate: [''],
      admissionStartDate: [''],
      admissionEndDate: ['']
    });
  }

  private createTermsForm(): FormGroup {
    return this.fb.group({
      terms: this.fb.array([])
    });
  }

  private createReviewForm(): FormGroup {
    return this.fb.group({
      confirmed: [false, Validators.requiredTrue]
    });
  }

  get termsArray(): FormArray {
    return this.termsForm.get('terms') as FormArray;
  }

  private createTermFormGroup(term?: CreateTerm): FormGroup {
    return this.fb.group({
      name: [term?.name || '', [Validators.required, Validators.maxLength(100)]],
      code: [term?.code || '', [Validators.required, Validators.maxLength(20)]],
      type: [term?.type || TermType.Semester, Validators.required],
      status: [term?.status || TermStatus.Planned, Validators.required],
      startDate: [term?.startDate || '', Validators.required],
      endDate: [term?.endDate || '', Validators.required],
      orderIndex: [term?.orderIndex || 1, [Validators.required, Validators.min(1)]],
      description: [term?.description || '', Validators.maxLength(500)],
      remarks: [term?.remarks || '', Validators.maxLength(500)],
      totalWorkingDays: [term?.totalWorkingDays || 0, [Validators.min(0), Validators.max(200)]],
      totalHolidays: [term?.totalHolidays || 0, [Validators.min(0), Validators.max(200)]],
      examStartDate: [term?.examStartDate || ''],
      examEndDate: [term?.examEndDate || ''],
      resultPublishDate: [term?.resultPublishDate || ''],
      registrationDeadline: [term?.registrationDeadline || ''],
      feePaymentDeadline: [term?.feePaymentDeadline || ''],
      passingGrade: [term?.passingGrade || null, [Validators.min(0), Validators.max(100)]],
      maximumGrade: [term?.maximumGrade || null, [Validators.min(0), Validators.max(100)]],
      gradingScale: [term?.gradingScale || '']
    });
  }

  private addDefaultTerms(): void {
    // Add default semester structure
    const defaultTerms = [
      {
        name: 'First Semester',
        code: 'SEM1',
        type: TermType.Semester,
        status: TermStatus.Planned,
        orderIndex: 1
      },
      {
        name: 'Second Semester',
        code: 'SEM2',
        type: TermType.Semester,
        status: TermStatus.Planned,
        orderIndex: 2
      }
    ];

    defaultTerms.forEach(term => {
      this.termsArray.push(this.createTermFormGroup(term as CreateTerm));
    });
  }

  addTerm(): void {
    const newOrderIndex = this.termsArray.length + 1;
    const newTerm = this.createTermFormGroup({
      name: `Term ${newOrderIndex}`,
      code: `T${newOrderIndex}`,
      type: TermType.Semester,
      status: TermStatus.Planned,
      orderIndex: newOrderIndex
    } as CreateTerm);
    
    this.termsArray.push(newTerm);
  }

  removeTerm(index: number): void {
    if (this.termsArray.length > 1) {
      this.termsArray.removeAt(index);
      this.updateTermOrderIndices();
    }
  }

  onTermDrop(event: CdkDragDrop<FormGroup[]>): void {
    const termsArray = this.termsArray;
    const item = termsArray.at(event.previousIndex);
    termsArray.removeAt(event.previousIndex);
    termsArray.insert(event.currentIndex, item);
    this.updateTermOrderIndices();
  }

  private updateTermOrderIndices(): void {
    this.termsArray.controls.forEach((control, index) => {
      control.get('orderIndex')?.setValue(index + 1);
    });
  }

  private loadAcademicYear(): void {
    if (!this.academicYearId) return;

    this.loading = true;
    this.academicYearService.getAcademicYear(this.academicYearId).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.populateForm(response.data);
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading academic year:', error);
        this.snackBar.open('Error loading academic year', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  private populateForm(academicYear: AcademicYear): void {
    // Populate academic year form
    this.academicYearForm.patchValue({
      name: academicYear.name,
      displayName: academicYear.displayName,
      code: academicYear.code,
      startDate: new Date(academicYear.startDate),
      endDate: new Date(academicYear.endDate),
      status: academicYear.status,
      isCurrentYear: academicYear.isCurrentYear,
      description: academicYear.description,
      remarks: academicYear.remarks,
      totalWorkingDays: academicYear.totalWorkingDays,
      totalHolidays: academicYear.totalHolidays,
      registrationStartDate: academicYear.registrationStartDate ? new Date(academicYear.registrationStartDate) : null,
      registrationEndDate: academicYear.registrationEndDate ? new Date(academicYear.registrationEndDate) : null,
      admissionStartDate: academicYear.admissionStartDate ? new Date(academicYear.admissionStartDate) : null,
      admissionEndDate: academicYear.admissionEndDate ? new Date(academicYear.admissionEndDate) : null
    });

    // Clear existing terms and populate with loaded terms
    this.termsArray.clear();
    academicYear.terms.forEach(term => {
      this.termsArray.push(this.createTermFormGroup({
        name: term.name,
        code: term.code,
        type: term.type,
        status: term.status,
        startDate: new Date(term.startDate),
        endDate: new Date(term.endDate),
        orderIndex: term.orderIndex,
        description: term.description,
        remarks: term.remarks,
        totalWorkingDays: term.totalWorkingDays,
        totalHolidays: term.totalHolidays,
        examStartDate: term.examStartDate ? new Date(term.examStartDate) : undefined,
        examEndDate: term.examEndDate ? new Date(term.examEndDate) : undefined,
        resultPublishDate: term.resultPublishDate ? new Date(term.resultPublishDate) : undefined,
        registrationDeadline: term.registrationDeadline ? new Date(term.registrationDeadline) : undefined,
        feePaymentDeadline: term.feePaymentDeadline ? new Date(term.feePaymentDeadline) : undefined,
        passingGrade: term.passingGrade,
        maximumGrade: term.maximumGrade,
        gradingScale: term.gradingScale
      } as CreateTerm));
    });
  }

  onSubmit(): void {
    if (this.academicYearForm.invalid || this.termsForm.invalid) {
      this.markFormGroupTouched(this.academicYearForm);
      this.markFormGroupTouched(this.termsForm);
      this.snackBar.open('Please fill in all required fields', 'Close', { duration: 3000 });
      return;
    }

    this.saving = true;

    const academicYearData = this.academicYearForm.value;
    const termsData = this.termsArray.value;

    if (this.isEditMode) {
      this.updateAcademicYear(academicYearData);
    } else {
      this.createAcademicYear({ ...academicYearData, terms: termsData });
    }
  }

  private createAcademicYear(data: CreateAcademicYear): void {
    this.academicYearService.createAcademicYear(data).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Academic year created successfully', 'Close', { duration: 3000 });
          this.router.navigate(['/admin/academic-years']);
        }
        this.saving = false;
      },
      error: (error) => {
        console.error('Error creating academic year:', error);
        this.snackBar.open('Error creating academic year', 'Close', { duration: 3000 });
        this.saving = false;
      }
    });
  }

  private updateAcademicYear(data: UpdateAcademicYear): void {
    if (!this.academicYearId) return;

    this.academicYearService.updateAcademicYear(this.academicYearId, data).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Academic year updated successfully', 'Close', { duration: 3000 });
          this.router.navigate(['/admin/academic-years']);
        }
        this.saving = false;
      },
      error: (error) => {
        console.error('Error updating academic year:', error);
        this.snackBar.open('Error updating academic year', 'Close', { duration: 3000 });
        this.saving = false;
      }
    });
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          }
        });
      }
    });
  }

  cancel(): void {
    this.router.navigate(['/admin/academic-years']);
  }

  getTermTypeText(type: TermType): string {
    return this.termService.getTermTypeText(type);
  }

  getTermStatusText(status: TermStatus): string {
    return this.termService.getTermStatusText(status);
  }

  getStatusLabel(status: number): string {
    const statusOption = this.statusOptions.find(s => s.value === status);
    return statusOption ? statusOption.label : 'Unknown';
  }

  isCurrentYear(): boolean {
    return this.academicYearForm.get('isCurrentYear')?.value || false;
  }

  getTermsCount(): number {
    return this.termsArray.length;
  }

  getTermControls(): FormGroup[] {
    return this.termsArray.controls as FormGroup[];
  }

  getTermName(termControl: FormGroup): string {
    return termControl.get('name')?.value || '';
  }

  getTermStatusColor(termControl: FormGroup): string {
    const status = termControl.get('status')?.value;
    return status === 1 ? 'primary' : 'accent';
  }

  getTermStatusTextForControl(termControl: FormGroup): string {
    const status = termControl.get('status')?.value;
    return this.getTermStatusText(status);
  }

  getTermTypeTextForControl(termControl: FormGroup): string {
    const type = termControl.get('type')?.value;
    return this.getTermTypeText(type);
  }

  getTermDateRange(termControl: FormGroup): string {
    const startDate = termControl.get('startDate')?.value;
    const endDate = termControl.get('endDate')?.value;
    if (startDate && endDate) {
      const start = new Date(startDate).toLocaleDateString();
      const end = new Date(endDate).toLocaleDateString();
      return `${start} - ${end}`;
    }
    return '';
  }
}
