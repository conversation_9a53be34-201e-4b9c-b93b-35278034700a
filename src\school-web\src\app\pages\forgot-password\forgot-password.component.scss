.forgot-password-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  background: linear-gradient(135deg, var(--sys-color-primary) 0%, var(--sys-color-secondary) 100%);
  overflow-x: hidden;

  // Background Elements
  .background-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;

    .bg-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;

      &.bg-circle-1 {
        width: 180px;
        height: 180px;
        top: 15%;
        left: -3%;
        animation-delay: 0s;
      }

      &.bg-circle-2 {
        width: 120px;
        height: 120px;
        top: 65%;
        right: -3%;
        animation-delay: 2s;
      }

      &.bg-circle-3 {
        width: 80px;
        height: 80px;
        top: 35%;
        right: 25%;
        animation-delay: 4s;
      }
    }
  }

  .forgot-password-content {
    position: relative;
    z-index: 2;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    max-width: 1000px;
    margin: 0 auto;
    width: 100%;

    .forgot-password-header {
      text-align: center;
      margin-bottom: 2rem;
      color: white;

      .logo-section {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        margin-bottom: 1rem;

        .logo-icon {
          font-size: 2.5rem;
          width: 2.5rem;
          height: 2.5rem;
        }

        .main-title {
          font-size: 2rem;
          font-weight: 700;
          margin: 0;
          letter-spacing: -0.025em;
        }
      }

      .subtitle {
        font-size: 1rem;
        opacity: 0.9;
        margin: 0;
        font-weight: 400;
      }
    }

    .forgot-password-card-wrapper {
      width: 100%;
      max-width: 500px;

      .forgot-password-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;

        .step-section {
          padding: 2rem;

          .step-header {
            text-align: center;
            margin-bottom: 2rem;

            .step-icon {
              font-size: 3rem;
              width: 3rem;
              height: 3rem;
              color: var(--sys-color-primary);
              margin-bottom: 1rem;

              &.success {
                color: var(--sys-color-tertiary);
              }
            }

            .step-title {
              font-size: 1.5rem;
              font-weight: 600;
              color: var(--sys-color-on-surface);
              margin-bottom: 0.5rem;
            }

            .step-description {
              font-size: 0.875rem;
              color: var(--sys-color-on-surface-variant);
              line-height: 1.5;
              margin: 0;
            }
          }

          .forgot-password-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;

            .full-width {
              width: 100%;
            }

            .password-strength {
              margin-top: -1rem;
              margin-bottom: 1rem;

              .strength-bar {
                width: 100%;
                height: 4px;
                background: var(--sys-color-outline-variant);
                border-radius: 2px;
                overflow: hidden;
                margin-bottom: 0.25rem;

                .strength-fill {
                  height: 100%;
                  transition: all 0.3s ease;
                  border-radius: 2px;

                  &.weak {
                    width: 33%;
                    background: #f44336;
                  }

                  &.medium {
                    width: 66%;
                    background: #ff9800;
                  }

                  &.strong {
                    width: 100%;
                    background: #4caf50;
                  }
                }
              }

              .strength-text {
                font-size: 0.75rem;
                font-weight: 500;
                color: var(--sys-color-on-surface-variant);
              }
            }

            .reset-button {
              width: 100%;
              height: 48px;
              font-size: 1rem;
              font-weight: 600;
              border-radius: 24px;
              background: linear-gradient(45deg, var(--sys-color-primary), var(--sys-color-secondary));
              color: white;
              border: none;
              box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
              transition: all 0.3s ease;

              &:hover:not(:disabled) {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
              }

              &:disabled {
                opacity: 0.6;
                cursor: not-allowed;
              }

              mat-icon {
                margin-right: 0.5rem;
              }
            }

            .progress-bar {
              margin-top: 1rem;
              border-radius: 2px;
            }
          }

          .success-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;

            button {
              min-width: 200px;
              height: 44px;
              border-radius: 22px;
              font-weight: 500;

              mat-icon {
                margin-right: 0.5rem;
              }
            }
          }
        }

        .card-actions {
          background: var(--sys-color-surface-variant);
          padding: 1rem 2rem;

          .login-section {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;

            span {
              color: var(--sys-color-on-surface-variant);
              font-size: 0.875rem;
            }

            button {
              font-weight: 500;
              
              mat-icon {
                margin-right: 0.25rem;
                font-size: 1rem;
                width: 1rem;
                height: 1rem;
              }
            }
          }
        }
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

// Responsive design
@media (max-width: 768px) {
  .forgot-password-container {
    .forgot-password-content {
      padding: 1rem;

      .forgot-password-header {
        margin-bottom: 1.5rem;

        .logo-section {
          .logo-icon {
            font-size: 2rem;
            width: 2rem;
            height: 2rem;
          }

          .main-title {
            font-size: 1.75rem;
          }
        }

        .subtitle {
          font-size: 0.875rem;
        }
      }

      .forgot-password-card-wrapper {
        .forgot-password-card {
          .step-section {
            padding: 1.5rem;

            .step-header {
              margin-bottom: 1.5rem;

              .step-icon {
                font-size: 2.5rem;
                width: 2.5rem;
                height: 2.5rem;
              }

              .step-title {
                font-size: 1.25rem;
              }
            }

            .success-actions {
              button {
                min-width: 100%;
              }
            }
          }

          .card-actions {
            padding: 1rem 1.5rem;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .forgot-password-container {
    .forgot-password-content {
      .forgot-password-header {
        .logo-section {
          flex-direction: column;
          gap: 0.5rem;

          .main-title {
            font-size: 1.5rem;
          }
        }
      }

      .forgot-password-card-wrapper {
        .forgot-password-card {
          .step-section {
            padding: 1rem;
          }
        }
      }
    }
  }
}
