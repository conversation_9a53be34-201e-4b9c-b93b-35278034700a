<div class="leaves-container">
  <h1 class="page-title">Student Leave Applications</h1>

  <!-- Filter Form -->
  <mat-card class="filter-card">
    <mat-card-header>
      <mat-card-title>Filter Leave Applications</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="filterForm" (ngSubmit)="loadLeaves()">
        <div class="filter-form">
          <mat-form-field appearance="outline">
            <mat-label>Status</mat-label>
            <mat-select formControlName="status">
              <mat-option [value]="null">All</mat-option>
              <mat-option *ngFor="let status of leaveStatuses" [value]="status.value">
                {{ status.label }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <div class="filter-actions">
            <button mat-raised-button color="primary" type="submit">
              Apply Filter
            </button>
            <button mat-raised-button color="accent" type="button" (click)="openLeaveApplicationDialog()">
              Apply for Leave
            </button>
          </div>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Loading Indicator -->
  <div *ngIf="loading.leaves" class="leaves-loading">
    <mat-progress-bar mode="indeterminate"></mat-progress-bar>
  </div>

  <!-- Error Message -->
  <div *ngIf="error.leaves" class="leaves-error">
    <mat-error>
      <mat-icon>error</mat-icon>
      <span>Failed to load leave records. Please try again.</span>
      <button mat-button color="warn" (click)="loadLeaves()">Retry</button>
    </mat-error>
  </div>

  <!-- No Leaves Message -->
  <div *ngIf="!loading.leaves && !error.leaves && leaveRecords.length === 0" class="no-leaves">
    <mat-card>
      <mat-card-content>
        <p>No leave applications found with the selected status.</p>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Leaves List -->
  <div *ngIf="!loading.leaves && !error.leaves && leaveRecords.length > 0" class="leaves-list">
    <mat-card *ngFor="let leave of leaveRecords" class="leave-card">
      <mat-card-header>
        <mat-card-title>{{ getLeaveTypeLabel(leave.type) }} Leave</mat-card-title>
        <mat-card-subtitle>
          {{ leave.startDate | date:'mediumDate' }} to {{ leave.endDate | date:'mediumDate' }}
          ({{ calculateLeaveDays(leave.startDate, leave.endDate) }} days)
        </mat-card-subtitle>
        <div class="leave-status">
          <span class="status-badge" [ngClass]="getStatusClass(leave.status)">
            {{ getStatusLabel(leave.status) }}
          </span>
        </div>
      </mat-card-header>
      
      <mat-card-content>
        <div class="leave-details">
          <div class="leave-reason">
            <span class="leave-label">Reason:</span>
            <span class="leave-value">{{ leave.reason }}</span>
          </div>
          
          <div class="leave-attachment" *ngIf="leave.attachmentPath">
            <span class="leave-label">Attachment:</span>
            <a class="leave-value" [href]="leave.attachmentPath" target="_blank">View Attachment</a>
          </div>
          
          <div class="leave-applied">
            <span class="leave-label">Applied On:</span>
            <span class="leave-value">{{ leave.createdAt | date:'medium' }}</span>
          </div>
          
          <div class="leave-comments" *ngIf="leave.comments">
            <span class="leave-label">Comments:</span>
            <span class="leave-value">{{ leave.comments }}</span>
          </div>
        </div>
      </mat-card-content>
      
      <mat-card-actions *ngIf="leave.status === 0">
        <button mat-button color="warn" (click)="cancelLeave(leave.id)">Cancel Application</button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
