import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-tenant-error',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    RouterModule,
    TranslateModule
  ],
  template: `
    <div class="tenant-error-container">
      <mat-card class="error-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>error</mat-icon>
            Setup Error
          </mat-card-title>
          <mat-card-subtitle>
            An error occurred during school setup
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p>We encountered an error while setting up your school system.</p>
          <p>Please try again or contact technical support if the problem persists.</p>
          <div class="actions">
            <button mat-raised-button color="primary" routerLink="/login">
              <mat-icon>refresh</mat-icon>
              Try Again
            </button>
            <button mat-button routerLink="/contact">
              <mat-icon>support</mat-icon>
              Contact Support
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .tenant-error-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
      padding: 20px;
      z-index: 9999;
      overflow-y: auto;
    }
    .error-card {
      max-width: 500px;
      width: 100%;
      text-align: center;
    }
    mat-card-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    .actions {
      margin-top: 24px;
      display: flex;
      gap: 12px;
      justify-content: center;
      flex-wrap: wrap;
    }
  `]
})
export class TenantErrorComponent {}
