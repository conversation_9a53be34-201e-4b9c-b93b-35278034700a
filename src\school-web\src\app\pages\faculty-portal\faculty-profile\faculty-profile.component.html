<div class="profile-container">
  <h1 class="page-title">Faculty Profile</h1>

  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load faculty profile. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadFacultyData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading && !error && faculty" class="profile-content">
    <mat-card class="profile-card">
      <mat-card-header>
        <div mat-card-avatar class="profile-avatar">
          <img *ngIf="faculty.profileImage" [src]="faculty.profileImage.filePath" alt="Faculty Photo">
          <mat-icon *ngIf="!faculty.profileImage">account_circle</mat-icon>
        </div>
        <mat-card-title>{{ faculty.firstName }} {{ faculty.lastName }}</mat-card-title>
        <mat-card-subtitle>{{ faculty.designation }} | {{ faculty.department }}</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <mat-tab-group>
          <mat-tab label="Personal Information">
            <div class="tab-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">Full Name</span>
                  <span class="info-value">{{ faculty.firstName }} {{ faculty.lastName }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Employee ID</span>
                  <span class="info-value">{{ faculty.employeeId }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Date of Birth</span>
                  <span class="info-value">{{ faculty.dateOfBirth | date:'mediumDate' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Gender</span>
                  <span class="info-value">{{ getGenderLabel(faculty.gender) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Email</span>
                  <span class="info-value">{{ faculty.email }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Phone</span>
                  <span class="info-value">{{ faculty.phone }}</span>
                </div>
                <div class="info-item" *ngIf="faculty.alternatePhone">
                  <span class="info-label">Alternate Phone</span>
                  <span class="info-value">{{ faculty.alternatePhone }}</span>
                </div>
                <div class="info-item full-width">
                  <span class="info-label">Address</span>
                  <span class="info-value">{{ faculty.address }}</span>
                </div>
              </div>
            </div>
          </mat-tab>
          
          <mat-tab label="Professional Information">
            <div class="tab-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">Designation</span>
                  <span class="info-value">{{ faculty.designation }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Department</span>
                  <span class="info-value">{{ faculty.department }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Joining Date</span>
                  <span class="info-value">{{ faculty.joiningDate | date:'mediumDate' }}</span>
                </div>
                <div class="info-item" *ngIf="faculty.isClassTeacher">
                  <span class="info-label">Class Teacher</span>
                  <span class="info-value">Class {{ faculty.assignedGrade }}-{{ faculty.assignedSection }}</span>
                </div>
                
                <div class="info-item full-width">
                  <span class="info-label">Qualifications</span>
                  <div class="chips-container">
                    <mat-chip-set>
                      <mat-chip *ngFor="let qualification of faculty.qualifications">{{ qualification }}</mat-chip>
                    </mat-chip-set>
                  </div>
                </div>
                
                <div class="info-item full-width">
                  <span class="info-label">Specializations</span>
                  <div class="chips-container">
                    <mat-chip-set>
                      <mat-chip *ngFor="let specialization of faculty.specializations">{{ specialization }}</mat-chip>
                    </mat-chip-set>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>
          
          <mat-tab label="Emergency Contact">
            <div class="tab-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">Contact Name</span>
                  <span class="info-value">{{ faculty.emergencyContactName }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Contact Phone</span>
                  <span class="info-value">{{ faculty.emergencyContactPhone }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Relation</span>
                  <span class="info-value">{{ faculty.emergencyContactRelation }}</span>
                </div>
              </div>
            </div>
          </mat-tab>
          
          <mat-tab label="Assigned Subjects">
            <div class="tab-content">
              <div *ngIf="faculty.assignedSubjects && faculty.assignedSubjects.length > 0" class="subjects-list">
                <table mat-table [dataSource]="faculty.assignedSubjects" class="subjects-table">
                  <ng-container matColumnDef="subjectCode">
                    <th mat-header-cell *matHeaderCellDef>Subject Code</th>
                    <td mat-cell *matCellDef="let subject">{{ subject.subjectCode }}</td>
                  </ng-container>
                  
                  <ng-container matColumnDef="subjectName">
                    <th mat-header-cell *matHeaderCellDef>Subject Name</th>
                    <td mat-cell *matCellDef="let subject">{{ subject.subjectName }}</td>
                  </ng-container>
                  
                  <ng-container matColumnDef="grade">
                    <th mat-header-cell *matHeaderCellDef>Grade</th>
                    <td mat-cell *matCellDef="let subject">{{ subject.grade }}</td>
                  </ng-container>
                  
                  <ng-container matColumnDef="section">
                    <th mat-header-cell *matHeaderCellDef>Section</th>
                    <td mat-cell *matCellDef="let subject">{{ subject.section }}</td>
                  </ng-container>
                  
                  <ng-container matColumnDef="academicYear">
                    <th mat-header-cell *matHeaderCellDef>Academic Year</th>
                    <td mat-cell *matCellDef="let subject">{{ subject.academicYear }}</td>
                  </ng-container>
                  
                  <tr mat-header-row *matHeaderRowDef="['subjectCode', 'subjectName', 'grade', 'section', 'academicYear']"></tr>
                  <tr mat-row *matRowDef="let row; columns: ['subjectCode', 'subjectName', 'grade', 'section', 'academicYear'];"></tr>
                </table>
              </div>
              
              <div *ngIf="!faculty.assignedSubjects || faculty.assignedSubjects.length === 0" class="no-data">
                <p>No subjects assigned.</p>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card-content>
      
      <mat-card-actions>
        <button mat-raised-button color="primary">Edit Profile</button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
