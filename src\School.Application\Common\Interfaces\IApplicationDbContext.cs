using Microsoft.EntityFrameworkCore;
using School.Domain.Entities;
using System.Threading;
using System.Threading.Tasks;

namespace School.Application.Common.Interfaces;

public interface IApplicationDbContext
{
    // Multi-tenant organization entities
    DbSet<Organization> Organizations { get; }
    DbSet<OrganizationUser> OrganizationUsers { get; }
    DbSet<OrganizationSubscription> OrganizationSubscriptions { get; }
    DbSet<OrganizationSettings> OrganizationSettings { get; }

    DbSet<Content> Contents { get; }
    DbSet<ContentTranslation> ContentTranslations { get; }
    DbSet<MediaItem> MediaItems { get; }
    DbSet<Notice> Notices { get; }
    DbSet<NoticeTranslation> NoticeTranslations { get; }
    DbSet<TuitionFee> TuitionFees { get; }
    DbSet<TuitionFeeTranslation> TuitionFeeTranslations { get; }
    DbSet<HostelFacility> HostelFacilities { get; }
    DbSet<HostelFacilityTranslation> HostelFacilityTranslations { get; }
    DbSet<HostelImage> HostelImages { get; }
    DbSet<Event> Events { get; }
    DbSet<EventTranslation> EventTranslations { get; }
    DbSet<EventImage> EventImages { get; }
    DbSet<EventRegistration> EventRegistrations { get; }
    DbSet<Faculty> Faculty { get; }
    DbSet<FacultyTranslation> FacultyTranslations { get; }
    DbSet<FacultyEducation> FacultyEducation { get; }
    DbSet<FacultySpecialization> FacultySpecializations { get; }
    DbSet<FacultyCourse> FacultyCourses { get; }
    DbSet<FacultyPublication> FacultyPublications { get; }
    DbSet<FacultyAward> FacultyAwards { get; }
    DbSet<FacultyEducationTranslation> FacultyEducationTranslations { get; }
    DbSet<FacultySpecializationTranslation> FacultySpecializationTranslations { get; }
    DbSet<Alumni> Alumni { get; }
    DbSet<AlumniTranslation> AlumniTranslations { get; }
    DbSet<AlumniTestimonial> AlumniTestimonials { get; }
    DbSet<AlumniTestimonialTranslation> AlumniTestimonialTranslations { get; }
    DbSet<Department> Departments { get; }
    DbSet<DepartmentTranslation> DepartmentTranslations { get; }
    DbSet<AcademicYear> AcademicYears { get; }
    DbSet<AcademicYearTranslation> AcademicYearTranslations { get; }
    DbSet<Term> Terms { get; }
    DbSet<TermTranslation> TermTranslations { get; }
    DbSet<AcademicCalendar> AcademicCalendars { get; }
    DbSet<AcademicCalendarTranslation> AcademicCalendarTranslations { get; }
    DbSet<Holiday> Holidays { get; }
    DbSet<HolidayTranslation> HolidayTranslations { get; }

    // Grade & Section Management (Sprint 3)
    DbSet<Grade> Grades { get; }
    DbSet<GradeTranslation> GradeTranslations { get; }
    DbSet<Section> Sections { get; }
    DbSet<SectionTranslation> SectionTranslations { get; }
    DbSet<ClassTeacher> ClassTeachers { get; }

    DbSet<Student> Students { get; }
    DbSet<Parent> Parents { get; }
    DbSet<StudentParent> StudentParents { get; }
    DbSet<StudentAttendance> StudentAttendances { get; }
    DbSet<StudentFee> StudentFees { get; }
    DbSet<StudentResult> StudentResults { get; }
    DbSet<StudentLeave> StudentLeaves { get; }
    DbSet<StudentAcademicHistory> StudentAcademicHistories { get; }
    DbSet<Career> Careers { get; }
    DbSet<CareerTranslation> CareerTranslations { get; }
    DbSet<CareerApplication> CareerApplications { get; }

    // Tenant Setup
    DbSet<TenantSetupStep> TenantSetupSteps { get; }

    // Campus Life - Clubs
    DbSet<Club> Clubs { get; }
    DbSet<ClubTranslation> ClubTranslations { get; }
    DbSet<ClubAdvisor> ClubAdvisors { get; }
    DbSet<ClubLeader> ClubLeaders { get; }
    DbSet<ClubActivity> ClubActivities { get; }
    DbSet<ClubActivityTranslation> ClubActivityTranslations { get; }
    DbSet<ClubAchievement> ClubAchievements { get; }
    DbSet<ClubAchievementTranslation> ClubAchievementTranslations { get; }
    DbSet<ClubEvent> ClubEvents { get; }
    DbSet<ClubEventTranslation> ClubEventTranslations { get; }
    DbSet<ClubGalleryItem> ClubGalleryItems { get; }

    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
