<div class="login-container">
  <!-- Background Elements -->
  <div class="background-elements">
    <div class="gradient-orb orb-1"></div>
    <div class="gradient-orb orb-2"></div>
    <div class="gradient-orb orb-3"></div>
  </div>

  <!-- Top Navigation -->
  <nav class="top-nav">
    <div class="nav-content">
      <div class="logo-brand">
        <div class="brand-icon">
          <mat-icon>school</mat-icon>
        </div>
        <span class="brand-text">EduInstitute</span>
      </div>

      <div class="nav-controls">
        <!-- Language Selector -->
        <div class="language-selector">
          <button mat-button class="lang-button" [matMenuTriggerFor]="langMenu">
            <span class="flag">{{ getCurrentLanguageFlag() }}</span>
            <span class="lang-text">{{ getCurrentLanguageText() }}</span>
            <mat-icon>expand_more</mat-icon>
          </button>
          <mat-menu #langMenu="matMenu" class="language-menu">
            <button mat-menu-item *ngFor="let lang of languages"
                    (click)="onLanguageChange(lang.code)"
                    [class.active]="currentLanguage === lang.code">
              <span class="flag">{{ lang.flag }}</span>
              <span>{{ lang.name }}</span>
            </button>
          </mat-menu>
        </div>

        <!-- Theme Toggle -->
        <button mat-icon-button class="theme-toggle"
                [matTooltip]="'LOGIN.TOGGLE_THEME' | translate"
                (click)="onThemeChange(currentTheme === 'light' ? 'dark' : 'light')">
          <mat-icon>{{ currentTheme === 'light' ? 'dark_mode' : 'light_mode' }}</mat-icon>
        </button>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="main-content">
    <div class="content-grid">
      <!-- Left Side - Welcome Section -->
      <div class="welcome-section">
        <div class="welcome-content">
          <div class="welcome-header">
            <h1 class="welcome-title">{{ 'LOGIN.WELCOME_TITLE' | translate }}</h1>
            <p class="welcome-subtitle">{{ 'LOGIN.WELCOME_SUBTITLE' | translate }}</p>
          </div>

          <div class="features-list">
            <div class="feature-item">
              <mat-icon>security</mat-icon>
              <span>{{ 'LOGIN.FEATURE_SECURITY' | translate }}</span>
            </div>
            <div class="feature-item">
              <mat-icon>language</mat-icon>
              <span>{{ 'LOGIN.FEATURE_MULTILINGUAL' | translate }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Side - Login Form -->
      <div class="login-section">
        <div class="login-card">
          <div class="card-header">
            <h2 class="card-title">{{ 'LOGIN.SIGN_IN_TITLE' | translate }}</h2>
          </div>

          <div class="card-content">
            <!-- User Type Selection -->
            <div class="user-type-section">
              <div class="section-header">
                <h3 class="section-title">{{ 'LOGIN.SELECT_PORTAL' | translate }}</h3>
              </div>

              <div class="user-type-grid">
                <div *ngFor="let type of userTypes"
                     class="user-type-card"
                     [class.selected]="selectedUserType === type.value"
                     (click)="selectUserType(type.value)">
                  <div class="card-icon">
                    <mat-icon>{{ type.icon }}</mat-icon>
                  </div>
                  <div class="card-content">
                    <h4 class="card-title">{{ 'LOGIN.' + type.label | translate }}</h4>
                    <p class="card-description">{{ 'LOGIN.' + type.label + '_DESC' | translate }}</p>
                  </div>
                  <div class="card-indicator">
                    <mat-icon *ngIf="selectedUserType === type.value">check_circle</mat-icon>
                  </div>
                </div>
              </div>
            </div>

            <!-- Login Form Section -->
            <div class="form-section" *ngIf="!showMfaStep && selectedUserType">
              <div class="section-header">
                <h3 class="section-title">{{ 'LOGIN.ENTER_CREDENTIALS' | translate }}</h3>
              </div>

              <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
                <!-- Username Field -->
                <div class="form-field">
                  <mat-form-field appearance="fill" class="full-width">
                    <mat-label>{{ 'LOGIN.USERNAME' | translate }}</mat-label>
                    <input
                      matInput
                      formControlName="username"
                      required
                      autocomplete="username"
                      placeholder="{{ 'LOGIN.USERNAME_PLACEHOLDER' | translate }}">
                    <mat-icon matPrefix>person_outline</mat-icon>
                    <mat-error>{{ getErrorMessage('username') }}</mat-error>
                  </mat-form-field>
                </div>

                <!-- Password Field -->
                <div class="form-field">
                  <mat-form-field appearance="fill" class="full-width">
                    <mat-label>{{ 'LOGIN.PASSWORD' | translate }}</mat-label>
                    <input
                      matInput
                      [type]="hidePassword ? 'password' : 'text'"
                      formControlName="password"
                      required
                      autocomplete="current-password"
                      placeholder="{{ 'LOGIN.PASSWORD_PLACEHOLDER' | translate }}">
                    <mat-icon matPrefix>lock_outline</mat-icon>
                    <button
                      mat-icon-button
                      matSuffix
                      (click)="hidePassword = !hidePassword"
                      type="button"
                      [matTooltip]="hidePassword ? ('LOGIN.SHOW_PASSWORD' | translate) : ('LOGIN.HIDE_PASSWORD' | translate)">
                      <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                    </button>
                    <mat-error>{{ getErrorMessage('password') }}</mat-error>
                  </mat-form-field>
                </div>

                <!-- Form Options -->
                <div class="form-options">
                  <mat-checkbox formControlName="rememberMe" color="primary" class="remember-me">
                    {{ 'LOGIN.REMEMBER_ME' | translate }}
                  </mat-checkbox>
                  <a routerLink="/forgot-password" class="forgot-password-link">
                    {{ 'LOGIN.FORGOT_PASSWORD' | translate }}
                  </a>
                </div>

                <!-- Submit Button -->
                <button
                  mat-flat-button
                  color="primary"
                  type="submit"
                  class="login-button"
                  [disabled]="loginForm.invalid || isLoading">
                  <span class="button-content">
                    <mat-icon *ngIf="!isLoading">arrow_forward</mat-icon>
                    <mat-icon *ngIf="isLoading" class="spinning">refresh</mat-icon>
                    <span>{{ isLoading ? ('LOGIN.SIGNING_IN' | translate) : ('LOGIN.SIGN_IN' | translate) }}</span>
                  </span>
                </button>
              </form>
            </div>

            <!-- MFA Verification Section -->
            <div class="form-section mfa-section" *ngIf="showMfaStep">
              <div class="section-header">
                <div class="mfa-icon-wrapper">
                  <mat-icon class="mfa-icon">security</mat-icon>
                </div>
                <h3 class="section-title">{{ 'LOGIN.MFA_VERIFICATION' | translate }}</h3>
                <p class="section-subtitle">{{ 'LOGIN.MFA_DESCRIPTION' | translate }}</p>
              </div>

              <form [formGroup]="mfaForm" (ngSubmit)="onSubmit()" class="mfa-form">
                <!-- MFA Code Field -->
                <div class="form-field">
                  <mat-form-field appearance="fill" class="full-width">
                    <mat-label>{{ 'LOGIN.MFA_CODE' | translate }}</mat-label>
                    <input
                      matInput
                      formControlName="mfaCode"
                      required
                      maxlength="6"
                      placeholder="000000"
                      autocomplete="one-time-code"
                      class="mfa-input">
                    <mat-icon matPrefix>verified_user</mat-icon>
                    <mat-hint>{{ 'LOGIN.MFA_HINT' | translate }}</mat-hint>
                    <mat-error>{{ getMfaErrorMessage() }}</mat-error>
                  </mat-form-field>
                </div>

                <!-- MFA Actions -->
                <div class="mfa-actions">
                  <button
                    mat-stroked-button
                    type="button"
                    class="back-button"
                    (click)="backToLogin()">
                    <mat-icon>arrow_back</mat-icon>
                    {{ 'LOGIN.BACK_TO_LOGIN' | translate }}
                  </button>

                  <button
                    mat-flat-button
                    color="primary"
                    type="submit"
                    class="verify-button"
                    [disabled]="mfaForm.invalid || isLoading">
                    <span class="button-content">
                      <mat-icon *ngIf="!isLoading">verified_user</mat-icon>
                      <mat-icon *ngIf="isLoading" class="spinning">refresh</mat-icon>
                      <span>{{ isLoading ? ('LOGIN.VERIFYING' | translate) : ('LOGIN.VERIFY' | translate) }}</span>
                    </span>
                  </button>
                </div>

                <!-- Backup Options -->
                <div class="backup-options">
                  <p class="backup-text">{{ 'LOGIN.MFA_TROUBLE' | translate }}</p>
                  <button
                    mat-button
                    type="button"
                    class="backup-button"
                    (click)="showBackupCodes()">
                    {{ 'LOGIN.USE_BACKUP_CODE' | translate }}
                  </button>
                </div>
              </form>
            </div>

            <!-- Additional Options -->
            <div class="additional-options" *ngIf="!showMfaStep && selectedUserType">
              <div class="inline-links">
                <button type="button" class="inline-link" (click)="navigateToRegister()">{{ 'LOGIN.CREATE_ACCOUNT' | translate }}</button>
                <span class="separator">•</span>
                <button type="button" class="inline-link" (click)="navigateToForgotPassword()">{{ 'LOGIN.FORGOT_PASSWORD' | translate }}</button>
                <span class="separator">•</span>
                <button type="button" class="inline-link" (click)="navigateToHelp()">{{ 'LOGIN.NEED_HELP' | translate }}</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="login-footer">
    <div class="footer-content">
      <div class="footer-section">
        <div class="security-badge">
          <mat-icon>shield</mat-icon>
          <span>{{ 'LOGIN.SECURITY_INFO' | translate }}</span>
        </div>
      </div>

      <div class="footer-section">
        <div class="support-info">
          <span>{{ 'LOGIN.SUPPORT_INFO' | translate }}</span>
          <a href="mailto:<EMAIL>" class="support-link">support&#64;school.edu</a>
        </div>
      </div>

      <div class="footer-section">
        <div class="footer-links">
          <a href="/privacy" class="footer-link">{{ 'LOGIN.PRIVACY_POLICY' | translate }}</a>
          <span class="separator">•</span>
          <a href="/terms" class="footer-link">{{ 'LOGIN.TERMS_OF_SERVICE' | translate }}</a>
        </div>
      </div>
    </div>
  </footer>
</div>
