<!-- Hero Section -->
<app-default-hero
  translationPrefix="ACADEMICS"
  title="ACADEMICS.STEM"
  subtitle="ACADEMICS.STEM_SUBTITLE"
  theme="dark"
  size="large"
  alignment="center"
  backgroundImage="assets/images/stem-hero.jpg">
</app-default-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'ACADEMICS.STEM_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'ACADEMICS.STEM_INTRO_P1' | translate }}</p>
      <p>{{ 'ACADEMICS.STEM_INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Key Features Section -->
  <section class="features-section">
    <h2>{{ 'ACADEMICS.KEY_FEATURES' | translate }}</h2>
    <div class="features-grid">
      <mat-card class="feature-card" *ngFor="let feature of keyFeatures">
        <div class="feature-icon">
          <mat-icon>{{feature.icon}}</mat-icon>
        </div>
        <mat-card-content>
          <h3>{{feature.title}}</h3>
          <p>{{feature.description}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- STEM Programs by Level Section -->
  <section class="programs-section">
    <h2>{{ 'ACADEMICS.STEM_PROGRAMS' | translate }}</h2>
    <div class="programs-container">
      <mat-tab-group animationDuration="300ms">
        <mat-tab *ngFor="let program of stemPrograms" [label]="program.level">
          <div class="program-content">
            <p class="program-description">{{program.description}}</p>

            <h3>{{ 'ACADEMICS.PROGRAM_FEATURES' | translate }}</h3>
            <div class="features-list">
              <div class="feature-item" *ngFor="let feature of program.features">
                <mat-icon>check_circle</mat-icon>
                <span>{{feature}}</span>
              </div>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </section>

  <!-- Curriculum Section -->
  <section class="curriculum-section">
    <h2>{{ 'ACADEMICS.STEM_CURRICULUM' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.STEM_CURRICULUM_INTRO' | translate }}</p>

    <div class="courses-container">
      <h3>{{ 'ACADEMICS.SAMPLE_COURSES' | translate }}</h3>
      <div class="courses-grid">
        <mat-card class="course-card" *ngFor="let course of sampleCourses">
          <mat-card-content>
            <h4>{{course.name}}</h4>
            <div class="course-level">{{course.level}}</div>
            <p>{{course.description}}</p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </section>

  <!-- Facilities Section -->
  <section class="facilities-section">
    <h2>{{ 'ACADEMICS.STEM_FACILITIES' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.FACILITIES_INTRO' | translate }}</p>

    <div class="facilities-grid">
      <mat-card class="facility-card" *ngFor="let facility of stemFacilities">
        <div class="facility-image">
          <img [src]="facility.image" [alt]="facility.title">
        </div>
        <mat-card-content>
          <h3>{{facility.title}}</h3>
          <p>{{facility.description}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Achievements Section -->
  <section class="achievements-section">
    <h2>{{ 'ACADEMICS.ACHIEVEMENTS' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.ACHIEVEMENTS_INTRO' | translate }}</p>

    <div class="achievements-container">
      <div class="achievement-item" *ngFor="let achievement of stemAchievements">
        <div class="achievement-year">{{achievement.year}}</div>
        <div class="achievement-content">
          <h3>{{achievement.title}}</h3>
          <p>{{achievement.description}}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Faculty Section -->
  <section class="faculty-section">
    <h2>{{ 'ACADEMICS.STEM_FACULTY' | translate }}</h2>
    <p class="section-intro">{{ 'ACADEMICS.STEM_FACULTY_INTRO' | translate }}</p>

    <div class="faculty-cta">
      <a mat-raised-button color="primary" routerLink="/faculty">
        {{ 'ACADEMICS.MEET_FACULTY' | translate }}
      </a>
    </div>
  </section>

  <!-- Call to Action Section -->
  <section class="cta-section">
    <div class="cta-content">
      <h2>{{ 'ACADEMICS.EXPLORE_STEM' | translate }}</h2>
      <p>{{ 'ACADEMICS.EXPLORE_STEM_TEXT' | translate }}</p>
      <div class="cta-buttons">
        <a mat-raised-button color="primary" routerLink="/admissions">
          {{ 'ACADEMICS.APPLY_NOW' | translate }}
        </a>
        <a mat-stroked-button color="primary" routerLink="/contact">
          {{ 'ACADEMICS.SCHEDULE_TOUR' | translate }}
        </a>
      </div>
    </div>
  </section>
</div>
