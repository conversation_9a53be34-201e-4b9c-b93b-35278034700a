// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  
  &.not-found {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 50vh;
    
    mat-card {
      max-width: 600px;
      text-align: center;
      padding: 40px;
      
      h2 {
        font-size: 2rem;
        margin-bottom: 20px;
        color: $text-color;
      }
      
      p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        color: $dark-gray;
      }
    }
  }
}

// Article Header
.article-header {
  margin-bottom: 30px;
  
  .article-category {
    display: inline-block;
    background-color: $primary-color;
    color: $white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    text-transform: uppercase;
    margin-bottom: 15px;
  }
  
  .article-title {
    font-size: 2.5rem;
    line-height: 1.3;
    margin-bottom: 20px;
    color: $text-color;
  }
  
  .article-meta {
    display: flex;
    gap: 20px;
    
    .meta-item {
      display: flex;
      align-items: center;
      color: $dark-gray;
      font-size: 0.9rem;
      
      mat-icon {
        font-size: 18px;
        height: 18px;
        width: 18px;
        margin-right: 5px;
      }
    }
  }
}

// Article Image
.article-image {
  margin-bottom: 30px;
  border-radius: $border-radius;
  overflow: hidden;
  box-shadow: $box-shadow;
  
  img {
    width: 100%;
    height: auto;
    display: block;
  }
}

// Article Content
.article-content {
  max-width: 800px;
  margin: 0 auto 40px;
  
  p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 20px;
    color: $text-color;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Article Tags
.article-tags {
  margin-bottom: 30px;
  
  h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: $text-color;
  }
  
  .tags-list {
    mat-chip-set {
      mat-chip {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
}

// Article Share
.article-share {
  margin-bottom: 40px;
  
  h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: $text-color;
  }
  
  .share-buttons {
    display: flex;
    gap: 15px;
    
    button {
      transition: transform 0.3s;
      
      &:hover {
        transform: translateY(-3px);
      }
    }
  }
}

// Related Articles
.related-articles {
  margin: 40px 0;
  
  h2 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: $text-color;
    position: relative;
    padding-bottom: 10px;
    
    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 80px;
      height: 3px;
      background-color: $primary-color;
    }
  }
  
  .related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    
    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
    
    @media (max-width: 576px) {
      grid-template-columns: 1fr;
    }
    
    .related-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }
      
      .related-image {
        height: 180px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;
          
          &:hover {
            transform: scale(1.05);
          }
        }
      }
      
      mat-card-content {
        padding: 20px 20px 10px;
        
        .related-date {
          font-size: 0.9rem;
          color: $dark-gray;
          margin-bottom: 10px;
        }
        
        .related-title {
          font-size: 1.2rem;
          margin: 0;
          color: $text-color;
          line-height: 1.4;
        }
      }
      
      mat-card-actions {
        padding: 0 20px 20px;
      }
    }
  }
}

// Back to News
.back-to-news {
  margin-top: 40px;
  text-align: center;
  
  a {
    mat-icon {
      margin-right: 5px;
    }
  }
}

// Responsive Adjustments
@media (max-width: 768px) {
  .container {
    padding: 30px 15px;
  }
  
  .article-header {
    .article-title {
      font-size: 2rem;
    }
  }
  
  .article-content {
    p {
      font-size: 1rem;
    }
  }
}

@media (max-width: 576px) {
  .article-header {
    .article-title {
      font-size: 1.8rem;
    }
    
    .article-meta {
      flex-direction: column;
      gap: 10px;
    }
  }
  
  .article-share {
    .share-buttons {
      flex-wrap: wrap;
    }
  }
}
