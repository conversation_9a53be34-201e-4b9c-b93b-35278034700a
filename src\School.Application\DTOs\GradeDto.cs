using School.Domain.Enums;
using School.Domain.Entities;

namespace School.Application.DTOs;

/// <summary>
/// DTO for Grade entity
/// </summary>
public class GradeDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public int Level { get; set; }
    public EducationLevel EducationLevel { get; set; }
    public string Description { get; set; } = string.Empty;
    public int MinAge { get; set; }
    public int MaxAge { get; set; }
    public int MaxStudents { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public Guid AcademicYearId { get; set; }
    public string AcademicYearName { get; set; } = string.Empty;
    public string PromotionCriteria { get; set; } = string.Empty;
    public decimal MinPassingGrade { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public int TotalSections { get; set; }
    public int TotalStudents { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }

    // Navigation properties
    public List<SectionDto> Sections { get; set; } = new();
}

/// <summary>
/// DTO for creating a new Grade
/// </summary>
public class CreateGradeDto
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public int Level { get; set; }
    public EducationLevel EducationLevel { get; set; }
    public string Description { get; set; } = string.Empty;
    public int MinAge { get; set; }
    public int MaxAge { get; set; }
    public int MaxStudents { get; set; } = 200;
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }
    public Guid AcademicYearId { get; set; }
    public string PromotionCriteria { get; set; } = string.Empty;
    public decimal MinPassingGrade { get; set; } = 40.0m;
    public string Remarks { get; set; } = string.Empty;

    // Translation support
    public List<CreateGradeTranslationDto> Translations { get; set; } = new();
}

/// <summary>
/// DTO for updating a Grade
/// </summary>
public class UpdateGradeDto
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public int Level { get; set; }
    public EducationLevel EducationLevel { get; set; }
    public string Description { get; set; } = string.Empty;
    public int MinAge { get; set; }
    public int MaxAge { get; set; }
    public int MaxStudents { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public Guid AcademicYearId { get; set; }
    public string PromotionCriteria { get; set; } = string.Empty;
    public decimal MinPassingGrade { get; set; }
    public string Remarks { get; set; } = string.Empty;

    // Translation support
    public List<UpdateGradeTranslationDto> Translations { get; set; } = new();
}

/// <summary>
/// DTO for Grade translation
/// </summary>
public class GradeTranslationDto
{
    public Guid Id { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string PromotionCriteria { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

/// <summary>
/// DTO for creating Grade translation
/// </summary>
public class CreateGradeTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string PromotionCriteria { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating Grade translation
/// </summary>
public class UpdateGradeTranslationDto
{
    public Guid Id { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string PromotionCriteria { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

/// <summary>
/// DTO for Grade filtering and pagination
/// </summary>
public class GradeFilterDto
{
    public string? SearchTerm { get; set; }
    public Guid? AcademicYearId { get; set; }
    public EducationLevel? EducationLevel { get; set; }
    public bool? IsActive { get; set; }
    public int? MinLevel { get; set; }
    public int? MaxLevel { get; set; }
    public string? LanguageCode { get; set; } = "en";
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string SortBy { get; set; } = "Level";
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// DTO for Grade statistics
/// </summary>
public class GradeStatisticsDto
{
    public Guid GradeId { get; set; }
    public string GradeName { get; set; } = string.Empty;
    public int TotalSections { get; set; }
    public int TotalStudents { get; set; }
    public int TotalCapacity { get; set; }
    public decimal UtilizationPercentage { get; set; }
    public int ActiveSections { get; set; }
    public int InactiveSections { get; set; }
    public List<SectionStatisticsDto> SectionStatistics { get; set; } = new();
}

/// <summary>
/// DTO for Section statistics within a Grade
/// </summary>
public class SectionStatisticsDto
{
    public Guid SectionId { get; set; }
    public string SectionName { get; set; } = string.Empty;
    public int StudentCount { get; set; }
    public int Capacity { get; set; }
    public decimal UtilizationPercentage { get; set; }
    public bool HasClassTeacher { get; set; }
    public string? ClassTeacherName { get; set; }
}
