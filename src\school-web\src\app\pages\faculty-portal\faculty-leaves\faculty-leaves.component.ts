import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { <PERSON><PERSON>uilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Services and models
import { FacultyService } from '../../../core/services/faculty.service';
import { AuthService } from '../../../core/services/auth.service';
import { FacultyDetail } from '../../../core/models/faculty.model';
import { StudentLeave, LeaveStatus, LeaveType } from '../../../core/models/student.model';

@Component({
  selector: 'app-faculty-leaves',
  templateUrl: './faculty-leaves.component.html',
  styleUrls: ['./faculty-leaves.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    DatePipe,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatTableModule,
    MatDialogModule,
    MatSnackBarModule
  ]
})
export class FacultyLeavesComponent implements OnInit {
  faculty: FacultyDetail | null = null;
  leaves: StudentLeave[] = [];
  filterForm: FormGroup;

  loading = {
    faculty: true,
    leaves: false
  };

  error = {
    faculty: false,
    leaves: false
  };

  leaveTypes = [
    { value: LeaveType.Sick, label: 'Sick' },
    { value: LeaveType.Personal, label: 'Personal' },
    { value: LeaveType.Family, label: 'Family' },
    { value: LeaveType.Religious, label: 'Religious' },
    { value: LeaveType.Other, label: 'Other' }
  ];

  leaveStatuses = [
    { value: LeaveStatus.Pending, label: 'Pending' },
    { value: LeaveStatus.Approved, label: 'Approved' },
    { value: LeaveStatus.Rejected, label: 'Rejected' },
    { value: LeaveStatus.Cancelled, label: 'Cancelled' }
  ];

  constructor(
    private facultyService: FacultyService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    this.filterForm = this.formBuilder.group({
      status: [LeaveStatus.Pending]
    });
  }

  ngOnInit(): void {
    this.loadFacultyData();
  }

  loadFacultyData(): void {
    this.loading.faculty = true;

    // In a real application, you would fetch the faculty by user ID
    // For now, we'll use a mock faculty ID
    this.facultyService.getFaculty(1)
      .subscribe({
        next: (faculty) => {
          this.faculty = faculty;
          this.loading.faculty = false;
          this.loadLeaves();
        },
        error: (err) => {
          console.error('Error loading faculty data:', err);
          this.error.faculty = true;
          this.loading.faculty = false;
          this.snackBar.open('Failed to load faculty data', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  loadLeaves(): void {
    if (!this.faculty) return;

    this.loading.leaves = true;
    this.error.leaves = false;

    // For pending leaves, use the dedicated endpoint
    if (this.filterForm.value.status === LeaveStatus.Pending) {
      this.facultyService.getPendingLeaveApplications(this.faculty.id)
        .subscribe({
          next: (leaves) => {
            this.leaves = leaves;
            this.loading.leaves = false;
          },
          error: (err) => {
            console.error('Error loading leaves:', err);
            this.error.leaves = true;
            this.loading.leaves = false;
            this.snackBar.open('Failed to load leave applications', 'Close', {
              duration: 3000,
              panelClass: ['error-snackbar']
            });
          }
        });
    } else {
      // For other statuses, we would use a different endpoint
      // This is a placeholder for the actual implementation
      this.loading.leaves = false;
      this.leaves = [];
    }
  }

  applyFilter(): void {
    this.loadLeaves();
  }

  approveLeave(leaveId: number, comments: string = 'Approved by class teacher'): void {
    if (!this.faculty) return;

    this.facultyService.approveLeaveApplication(this.faculty.id, leaveId, comments)
      .subscribe({
        next: () => {
          this.loadLeaves();
          this.snackBar.open('Leave application approved successfully', 'Close', {
            duration: 3000
          });
        },
        error: (err) => {
          console.error('Error approving leave:', err);
          this.snackBar.open('Failed to approve leave application', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  rejectLeave(leaveId: number, comments: string = 'Rejected by class teacher'): void {
    if (!this.faculty) return;

    this.facultyService.rejectLeaveApplication(this.faculty.id, leaveId, comments)
      .subscribe({
        next: () => {
          this.loadLeaves();
          this.snackBar.open('Leave application rejected successfully', 'Close', {
            duration: 3000
          });
        },
        error: (err) => {
          console.error('Error rejecting leave:', err);
          this.snackBar.open('Failed to reject leave application', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
  }

  getLeaveTypeLabel(type: number): string {
    return this.leaveTypes.find(t => t.value === type)?.label || 'Unknown';
  }

  getStatusLabel(status: number): string {
    return this.leaveStatuses.find(s => s.value === status)?.label || 'Unknown';
  }

  getStatusClass(status: number): string {
    switch (status) {
      case LeaveStatus.Approved: return 'approved';
      case LeaveStatus.Pending: return 'pending';
      case LeaveStatus.Rejected: return 'rejected';
      case LeaveStatus.Cancelled: return 'cancelled';
      default: return '';
    }
  }

  calculateLeaveDays(startDate: Date, endDate: Date): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  }
}
