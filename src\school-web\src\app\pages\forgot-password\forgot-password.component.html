<div class="forgot-password-container">
  <!-- Background Elements -->
  <div class="background-elements">
    <div class="bg-circle bg-circle-1"></div>
    <div class="bg-circle bg-circle-2"></div>
    <div class="bg-circle bg-circle-3"></div>
  </div>

  <div class="forgot-password-content">
    <!-- Header Section -->
    <div class="forgot-password-header">
      <div class="logo-section">
        <mat-icon class="logo-icon">lock_reset</mat-icon>
        <h1 class="main-title">{{ 'FORGOT_PASSWORD.TITLE' | translate }}</h1>
      </div>
      <p class="subtitle">{{ 'FORGOT_PASSWORD.SUBTITLE' | translate }}</p>
    </div>

    <!-- Forgot Password Card -->
    <div class="forgot-password-card-wrapper">
      <mat-card class="forgot-password-card">
        <mat-card-content>
          <!-- Step 1: Email Input -->
          <div class="step-section" *ngIf="currentStep === 1">
            <div class="step-header">
              <mat-icon class="step-icon">email</mat-icon>
              <h3 class="step-title">{{ 'FORGOT_PASSWORD.ENTER_EMAIL' | translate }}</h3>
              <p class="step-description">{{ 'FORGOT_PASSWORD.EMAIL_INSTRUCTION' | translate }}</p>
            </div>

            <form [formGroup]="emailForm" (ngSubmit)="onSendResetLink()" class="forgot-password-form">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>{{ 'FORGOT_PASSWORD.EMAIL_LABEL' | translate }}</mat-label>
                <input matInput formControlName="email" type="email" required>
                <mat-icon matPrefix>email</mat-icon>
                <mat-error *ngIf="emailForm.get('email')?.hasError('required')">
                  {{ 'FORGOT_PASSWORD.EMAIL_REQUIRED' | translate }}
                </mat-error>
                <mat-error *ngIf="emailForm.get('email')?.hasError('email')">
                  {{ 'FORGOT_PASSWORD.EMAIL_INVALID' | translate }}
                </mat-error>
              </mat-form-field>

              <button
                mat-raised-button
                color="primary"
                type="submit"
                class="reset-button"
                [disabled]="emailForm.invalid || isLoading">
                <mat-icon>{{ isLoading ? 'hourglass_empty' : 'send' }}</mat-icon>
                {{ isLoading ? ('FORGOT_PASSWORD.SENDING' | translate) : ('FORGOT_PASSWORD.SEND_LINK' | translate) }}
              </button>

              <mat-progress-bar *ngIf="isLoading" mode="indeterminate" class="progress-bar"></mat-progress-bar>
            </form>
          </div>

          <!-- Step 2: Success Message -->
          <div class="step-section" *ngIf="currentStep === 2">
            <div class="step-header success">
              <mat-icon class="step-icon success">mark_email_read</mat-icon>
              <h3 class="step-title">{{ 'FORGOT_PASSWORD.EMAIL_SENT' | translate }}</h3>
              <p class="step-description">{{ 'FORGOT_PASSWORD.CHECK_EMAIL' | translate }}</p>
            </div>

            <div class="success-actions">
              <button mat-button color="primary" (click)="resendEmail()" [disabled]="resendCooldown > 0">
                <mat-icon>refresh</mat-icon>
                {{ resendCooldown > 0 ? ('FORGOT_PASSWORD.RESEND_IN' | translate) + ' ' + resendCooldown + 's' : ('FORGOT_PASSWORD.RESEND' | translate) }}
              </button>

              <button mat-raised-button color="accent" routerLink="/login">
                <mat-icon>arrow_back</mat-icon>
                {{ 'FORGOT_PASSWORD.BACK_TO_LOGIN' | translate }}
              </button>
            </div>
          </div>

          <!-- Step 3: Reset Password Form -->
          <div class="step-section" *ngIf="currentStep === 3">
            <div class="step-header">
              <mat-icon class="step-icon">lock_reset</mat-icon>
              <h3 class="step-title">{{ 'FORGOT_PASSWORD.RESET_PASSWORD' | translate }}</h3>
              <p class="step-description">{{ 'FORGOT_PASSWORD.NEW_PASSWORD_INSTRUCTION' | translate }}</p>
            </div>

            <form [formGroup]="resetForm" (ngSubmit)="onResetPassword()" class="forgot-password-form">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>{{ 'FORGOT_PASSWORD.NEW_PASSWORD' | translate }}</mat-label>
                <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" required>
                <mat-icon matPrefix>lock</mat-icon>
                <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                  <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                </button>
                <mat-error *ngIf="resetForm.get('password')?.hasError('required')">
                  {{ 'FORGOT_PASSWORD.PASSWORD_REQUIRED' | translate }}
                </mat-error>
                <mat-error *ngIf="resetForm.get('password')?.hasError('minlength')">
                  {{ 'FORGOT_PASSWORD.PASSWORD_LENGTH' | translate }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>{{ 'FORGOT_PASSWORD.CONFIRM_PASSWORD' | translate }}</mat-label>
                <input matInput [type]="hideConfirmPassword ? 'password' : 'text'" formControlName="confirmPassword" required>
                <mat-icon matPrefix>lock</mat-icon>
                <button mat-icon-button matSuffix (click)="hideConfirmPassword = !hideConfirmPassword" type="button">
                  <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                </button>
                <mat-error *ngIf="resetForm.get('confirmPassword')?.hasError('required')">
                  {{ 'FORGOT_PASSWORD.CONFIRM_PASSWORD_REQUIRED' | translate }}
                </mat-error>
                <mat-error *ngIf="resetForm.get('confirmPassword')?.hasError('passwordMismatch')">
                  {{ 'FORGOT_PASSWORD.PASSWORD_MISMATCH' | translate }}
                </mat-error>
              </mat-form-field>

              <!-- Password Strength Indicator -->
              <div class="password-strength" *ngIf="resetForm.get('password')?.value">
                <div class="strength-bar">
                  <div class="strength-fill" [ngClass]="getPasswordStrength()"></div>
                </div>
                <span class="strength-text">{{ getPasswordStrengthText() | translate }}</span>
              </div>

              <button
                mat-raised-button
                color="primary"
                type="submit"
                class="reset-button"
                [disabled]="resetForm.invalid || isLoading">
                <mat-icon>{{ isLoading ? 'hourglass_empty' : 'lock_reset' }}</mat-icon>
                {{ isLoading ? ('FORGOT_PASSWORD.RESETTING' | translate) : ('FORGOT_PASSWORD.RESET_PASSWORD' | translate) }}
              </button>

              <mat-progress-bar *ngIf="isLoading" mode="indeterminate" class="progress-bar"></mat-progress-bar>
            </form>
          </div>
        </mat-card-content>

        <!-- Footer Actions -->
        <mat-card-actions class="card-actions">
          <div class="login-section">
            <span>{{ 'FORGOT_PASSWORD.REMEMBER_PASSWORD' | translate }}</span>
            <button mat-button color="accent" routerLink="/login">
              <mat-icon>login</mat-icon>
              {{ 'FORGOT_PASSWORD.BACK_TO_LOGIN' | translate }}
            </button>
          </div>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</div>
