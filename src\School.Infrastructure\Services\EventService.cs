using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Event;
using School.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Services
{
    public class EventService : IEventService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<EventService> _logger;

        public EventService(
            IUnitOfWork unitOfWork,
            ICurrentUserService currentUserService,
            ILogger<EventService> logger)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        public async Task<(IEnumerable<EventDto> Events, int TotalCount)> GetAllEventsAsync(EventFilterDto filter)
        {
            _logger.LogInformation("Getting all events with filter: {Filter}", new { filter.Name, filter.StartDateFrom, filter.StartDateTo, filter.Type, filter.Status, filter.Page, filter.PageSize });

            var repository = _unitOfWork.Repository<Event>();
            var query = repository.AsQueryable("Translations", "Images", "Images.MediaItem", "Registrations");

            // Apply filters
            if (!string.IsNullOrEmpty(filter.Name))
            {
                query = query.Where(e => e.Title.Contains(filter.Name));
            }

            if (filter.StartDateFrom.HasValue)
            {
                query = query.Where(e => e.StartDate >= filter.StartDateFrom.Value);
            }

            if (filter.StartDateTo.HasValue)
            {
                query = query.Where(e => e.StartDate <= filter.StartDateTo.Value);
            }

            if (filter.Type.HasValue)
            {
                query = query.Where(e => e.Type == filter.Type.Value);
            }

            if (filter.Status.HasValue)
            {
                query = query.Where(e => e.Status == filter.Status.Value);
            }

            if (filter.IsActive.HasValue)
            {
                query = query.Where(e => e.IsActive == filter.IsActive.Value);
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Get paginated results
            var events = await query
                .OrderByDescending(e => e.StartDate)
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(e => new EventDto
                {
                    Id = e.Id,
                    Title = e.Title,
                    Description = e.Description,
                    StartDate = e.StartDate,
                    EndDate = e.EndDate,
                    StartTime = e.StartTime,
                    EndTime = e.EndTime,
                    Location = e.Location,
                    Organizer = e.Organizer,
                    ContactInfo = e.ContactInfo,
                    Type = e.Type,
                    Status = e.Status,
                    IsAllDay = e.IsAllDay,
                    IsRecurring = e.IsRecurring,
                    RecurrenceType = e.RecurrenceType,
                    RecurrenceInterval = e.RecurrenceInterval,
                    RecurrenceEndDate = e.RecurrenceEndDate,
                    IsPublic = e.IsPublic,
                    IsActive = e.IsActive,
                    MaxAttendees = e.MaxAttendees,
                    RequiresRegistration = e.RequiresRegistration,
                    RegistrationsCount = e.Registrations.Count,
                    CreatedAt = e.CreatedAt,
                    LastModifiedAt = e.LastModifiedAt,
                    Translations = e.Translations.Select(t => new EventTranslationDto
                    {
                        Id = t.Id,
                        EventId = t.EventId,
                        LanguageCode = t.LanguageCode,
                        Title = t.Title,
                        Description = t.Description,
                        Location = t.Location,
                        Organizer = t.Organizer
                    }).ToList(),
                    Images = e.Images.Select(i => new EventImageDto
                    {
                        Id = i.Id,
                        EventId = i.EventId,
                        MediaItemId = i.MediaItemId,
                        DisplayOrder = i.DisplayOrder,
                        ImagePath = i.MediaItem.FilePath ?? string.Empty,
                        Caption = i.MediaItem.Caption ?? string.Empty,
                        IsMain = i.DisplayOrder == 0
                    }).ToList()
                })
                .ToListAsync();

            _logger.LogInformation($"Retrieved {events.Count} events out of {totalCount}");
            return (events, totalCount);
        }

        public async Task<EventDto?> GetEventByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting event by ID: {EventId}", id);

            var repository = _unitOfWork.Repository<Event>();
            var eventEntity = await repository.GetByIdAsync(id, new[] { "Translations", "Images", "Images.MediaItem", "Registrations" });

            if (eventEntity == null)
            {
                _logger.LogWarning("Event with ID {EventId} not found", id);
                return null;
            }

            return new EventDto
            {
                Id = eventEntity.Id,
                Title = eventEntity.Title,
                Description = eventEntity.Description,
                StartDate = eventEntity.StartDate,
                EndDate = eventEntity.EndDate,
                StartTime = eventEntity.StartTime,
                EndTime = eventEntity.EndTime,
                Location = eventEntity.Location,
                Organizer = eventEntity.Organizer,
                ContactInfo = eventEntity.ContactInfo,
                Type = eventEntity.Type,
                Status = eventEntity.Status,
                IsAllDay = eventEntity.IsAllDay,
                IsRecurring = eventEntity.IsRecurring,
                RecurrenceType = eventEntity.RecurrenceType,
                RecurrenceInterval = eventEntity.RecurrenceInterval,
                RecurrenceEndDate = eventEntity.RecurrenceEndDate,
                IsPublic = eventEntity.IsPublic,
                IsActive = eventEntity.IsActive,
                MaxAttendees = eventEntity.MaxAttendees,
                RequiresRegistration = eventEntity.RequiresRegistration,
                RegistrationsCount = eventEntity.Registrations.Count,
                CreatedAt = eventEntity.CreatedAt,
                LastModifiedAt = eventEntity.LastModifiedAt,
                Translations = eventEntity.Translations.Select(t => new EventTranslationDto
                {
                    Id = t.Id,
                    EventId = t.EventId,
                    LanguageCode = t.LanguageCode,
                    Title = t.Title,
                    Description = t.Description,
                    Location = t.Location,
                    Organizer = t.Organizer
                }).ToList(),
                Images = eventEntity.Images.Select(i => new EventImageDto
                {
                    Id = i.Id,
                    EventId = i.EventId,
                    MediaItemId = i.MediaItemId,
                    DisplayOrder = i.DisplayOrder,
                    ImagePath = i.MediaItem?.FilePath ?? string.Empty,
                    Caption = i.MediaItem?.Caption ?? string.Empty,
                    IsMain = i.DisplayOrder == 0
                }).ToList()
            };
        }

        public async Task<Guid> CreateEventAsync(CreateEventDto eventDto)
        {
            _logger.LogInformation("Creating new event with title: {Title}", eventDto.Title);

            try
            {
                var repository = _unitOfWork.Repository<Event>();

                // Validate event dates
                if (eventDto.EndDate.HasValue && eventDto.StartDate > eventDto.EndDate.Value)
                {
                    _logger.LogWarning("Invalid event dates: Start date {StartDate} is after end date {EndDate}", eventDto.StartDate, eventDto.EndDate);
                    throw new InvalidOperationException("End date cannot be before start date");
                }

                var eventEntity = new Event
                {
                    Title = eventDto.Title,
                    Description = eventDto.Description,
                    StartDate = eventDto.StartDate,
                    EndDate = eventDto.EndDate,
                    StartTime = eventDto.StartTime,
                    EndTime = eventDto.EndTime,
                    Location = eventDto.Location,
                    Organizer = eventDto.Organizer,
                    ContactInfo = eventDto.ContactInfo,
                    Type = eventDto.Type,
                    Status = eventDto.Status,
                    IsAllDay = eventDto.IsAllDay,
                    IsRecurring = eventDto.IsRecurring,
                    RecurrenceType = eventDto.RecurrenceType,
                    RecurrenceInterval = eventDto.RecurrenceInterval,
                    RecurrenceEndDate = eventDto.RecurrenceEndDate,
                    IsPublic = eventDto.IsPublic,
                    IsActive = eventDto.IsActive,
                    MaxAttendees = eventDto.MaxAttendees,
                    RequiresRegistration = eventDto.RequiresRegistration,
                    // CreatedAt and CreatedBy will be set by the repository
                };

                // Add translations if provided
                if (eventDto.Translations != null)
                {
                    var translations = eventDto.Translations as IEnumerable<CreateEventTranslationDto>;
                    if (translations != null && translations.Any())
                    {
                        eventEntity.Translations = translations.Select(t => new EventTranslation
                        {
                            LanguageCode = t.LanguageCode,
                            Title = t.Title,
                            Description = t.Description,
                            Location = t.Location,
                            Organizer = t.Organizer,
                            // CreatedAt and CreatedBy will be set by the repository
                        }).ToList();
                    }
                }

                // Add images if provided
                if (eventDto.ImageIds != null && eventDto.ImageIds.Any())
                {
                    var mediaRepository = _unitOfWork.Repository<MediaItem>();
                    var mediaItems = await mediaRepository.FindAsync(m => eventDto.ImageIds.Contains(m.Id));

                    if (mediaItems.Any())
                    {
                        eventEntity.Images = mediaItems.Select((m, index) => new EventImage
                        {
                            MediaItemId = m.Id,
                            DisplayOrder = index,
                            // CreatedAt and CreatedBy will be set by the repository
                        }).ToList();
                    }
                }

                // Get the current user ID for audit trail
                var userId = _currentUserService.UserId?.ToString();

                // Pass the current user ID to the repository for audit trail
                await repository.AddAsync(eventEntity, userId);
                await _unitOfWork.SaveChangesAsync(userId);

                _logger.LogInformation("Event created successfully with ID: {EventId}", eventEntity.Id);
                return eventEntity.Id;
            }
            catch (Exception ex) when (ex is not InvalidOperationException)
            {
                _logger.LogError(ex, "Error creating event: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        public async Task<bool> UpdateEventAsync(Guid id, UpdateEventDto eventDto)
        {
            _logger.LogInformation("Updating event with ID: {EventId}", id);

            try
            {
                var repository = _unitOfWork.Repository<Event>();
                var eventEntity = await repository.GetByIdAsync(id, new[] { "Translations", "Images", "Images.MediaItem" });

                if (eventEntity == null)
                {
                    _logger.LogWarning("Event with ID {EventId} not found for update", id);
                    return false;
                }

                // Validate event dates if both are provided
                var startDate = eventDto.StartDate;
                if (eventDto.EndDate.HasValue && startDate > eventDto.EndDate.Value)
                {
                    _logger.LogWarning("Invalid event dates: Start date {StartDate} is after end date {EndDate}", startDate, eventDto.EndDate);
                    throw new InvalidOperationException("End date cannot be before start date");
                }

                // Update properties
                eventEntity.Title = eventDto.Title;
                eventEntity.Description = eventDto.Description;
                eventEntity.StartDate = eventDto.StartDate;
                eventEntity.EndDate = eventDto.EndDate;
                eventEntity.StartTime = eventDto.StartTime;
                eventEntity.EndTime = eventDto.EndTime;
                eventEntity.Location = eventDto.Location;
                eventEntity.Organizer = eventDto.Organizer;
                eventEntity.ContactInfo = eventDto.ContactInfo;
                eventEntity.Type = eventDto.Type;
                eventEntity.Status = eventDto.Status;
                eventEntity.IsAllDay = eventDto.IsAllDay;
                eventEntity.IsRecurring = eventDto.IsRecurring;
                eventEntity.RecurrenceType = eventDto.RecurrenceType;
                eventEntity.RecurrenceInterval = eventDto.RecurrenceInterval;
                eventEntity.RecurrenceEndDate = eventDto.RecurrenceEndDate;
                eventEntity.IsPublic = eventDto.IsPublic;
                eventEntity.IsActive = eventDto.IsActive;
                eventEntity.MaxAttendees = eventDto.MaxAttendees;
                eventEntity.RequiresRegistration = eventDto.RequiresRegistration;
                // LastModifiedBy and LastModifiedAt will be set by the repository

                // Update translations if provided
                if (eventDto.Translations != null)
                {
                    var translationRepository = _unitOfWork.Repository<EventTranslation>();

                    // Remove existing translations
                    if (eventEntity.Translations != null && eventEntity.Translations.Any())
                    {
                        foreach (var translation in eventEntity.Translations.ToList())
                        {
                            var translationUserId = _currentUserService.UserId?.ToString();
                            await translationRepository.DeleteAsync(translation, translationUserId);
                        }
                    }

                    // Add new translations
                    var translations = eventDto.Translations as IEnumerable<UpdateEventTranslationDto>;
                    if (translations != null && translations.Any())
                    {
                        eventEntity.Translations = translations.Select(t => new EventTranslation
                        {
                            EventId = eventEntity.Id,
                            LanguageCode = t.LanguageCode,
                            Title = t.Title,
                            Description = t.Description,
                            Location = t.Location,
                            Organizer = t.Organizer,
                            // CreatedAt and CreatedBy will be set by the repository
                        }).ToList();

                        foreach (var translation in eventEntity.Translations)
                        {
                            var translationAddUserId = _currentUserService.UserId?.ToString();
                            await translationRepository.AddAsync(translation, translationAddUserId);
                        }
                    }
                }

                // Update images if provided
                if (eventDto.ImageIds != null)
                {
                    var eventImageRepository = _unitOfWork.Repository<EventImage>();

                    // Remove existing images
                    if (eventEntity.Images != null && eventEntity.Images.Any())
                    {
                        foreach (var image in eventEntity.Images.ToList())
                        {
                            var imageDeleteUserId = _currentUserService.UserId?.ToString();
                            await eventImageRepository.DeleteAsync(image, imageDeleteUserId);
                        }
                    }

                    // Add new images
                    if (eventDto.ImageIds.Any())
                    {
                        var mediaRepository = _unitOfWork.Repository<MediaItem>();
                        var mediaItems = await mediaRepository.FindAsync(m => eventDto.ImageIds.Contains(m.Id));

                        if (mediaItems.Any())
                        {
                            var newImages = mediaItems.Select((m, index) => new EventImage
                            {
                                EventId = eventEntity.Id,
                                MediaItemId = m.Id,
                                DisplayOrder = index,
                                // CreatedAt and CreatedBy will be set by the repository
                            }).ToList();

                            foreach (var image in newImages)
                            {
                                var imageAddUserId = _currentUserService.UserId?.ToString();
                                await eventImageRepository.AddAsync(image, imageAddUserId);
                            }
                        }
                    }
                }

                // Get the current user ID for audit trail
                var userId = _currentUserService.UserId?.ToString();

                // Pass the current user ID to the repository for audit trail
                await repository.UpdateAsync(eventEntity, userId);
                await _unitOfWork.SaveChangesAsync(userId);

                _logger.LogInformation("Event with ID {EventId} updated successfully", id);
                return true;
            }
            catch (Exception ex) when (ex is not InvalidOperationException)
            {
                _logger.LogError(ex, "Error updating event with ID {EventId}: {ErrorMessage}", id, ex.Message);
                throw;
            }
        }

        public async Task<bool> DeleteEventAsync(Guid id)
        {
            _logger.LogInformation("Deleting event with ID: {EventId}", id);

            try
            {
                var repository = _unitOfWork.Repository<Event>();

                // Check if event exists
                var eventEntity = await repository.GetByIdAsync(id);
                if (eventEntity == null)
                {
                    _logger.LogWarning("Event with ID {EventId} not found for deletion", id);
                    return false;
                }

                // Use the repository's DeleteByIdAsync method which handles soft delete
                // Get the current user ID for audit trail
                var userId = _currentUserService.UserId?.ToString();

                // Pass the current user ID to the repository for audit trail
                await repository.DeleteByIdAsync(id, userId);
                await _unitOfWork.SaveChangesAsync(userId);

                _logger.LogInformation("Event with ID {EventId} deleted successfully", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting event with ID {EventId}: {ErrorMessage}", id, ex.Message);
                throw;
            }
        }
    }
}
