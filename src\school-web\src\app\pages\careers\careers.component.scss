@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section styles are now handled by the DefaultHeroComponent

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Section Styles
section {
  padding: $section-padding;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: $dark-gray;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Introduction Section
.intro-section {
  background-color: $white;

  .intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
}

// Openings Section
.openings-section {
  background-color: $light-gray;

  .department-filter {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
    }

    .filter-label {
      font-size: 1.1rem;
      color: $text-color;
      margin-right: 15px;
      margin-bottom: 10px;
    }

    .filter-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      button {
        padding: 8px 16px;
        background-color: $white;
        border: 1px solid $medium-gray;
        border-radius: 20px;
        color: $dark-gray;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba($primary-color, 0.1);
          color: $primary-color;
        }

        &.active {
          background-color: $primary-color;
          color: $white;
          border-color: $primary-color;
        }
      }
    }
  }

  .job-listings {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;

    @media (max-width: 576px) {
      grid-template-columns: 1fr;
    }

    .job-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      mat-card-header {
        padding: 20px 20px 0;

        mat-card-title {
          font-size: 1.5rem;
          margin-bottom: 10px;
          color: $text-color;
        }

        mat-card-subtitle {
          display: flex;
          gap: 10px;

          .job-department {
            color: $dark-gray;
          }

          .job-type {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            text-transform: uppercase;

            &.full-time {
              background-color: rgba($primary-color, 0.1);
              color: $primary-color;
            }

            &.part-time {
              background-color: rgba($accent-color, 0.1);
              color: $accent-color;
            }

            &.contract {
              background-color: rgba(#ff9800, 0.1);
              color: #ff9800;
            }
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;

        .job-meta {
          display: flex;
          gap: 20px;
          margin-bottom: 15px;

          .meta-item {
            display: flex;
            align-items: center;
            color: $dark-gray;
            font-size: 0.9rem;

            mat-icon {
              font-size: 18px;
              height: 18px;
              width: 18px;
              margin-right: 5px;
            }
          }
        }

        .job-description {
          color: $text-color;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }

      mat-card-actions {
        padding: 0 20px 20px;
        display: flex;
        gap: 10px;

        a {
          flex-grow: 1;
        }
      }
    }
  }
}

// Benefits Section
.benefits-section {
  background-color: $white;

  .benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;

    @media (max-width: 576px) {
      grid-template-columns: 1fr;
    }

    .benefit-card {
      background-color: $light-gray;
      border-radius: $border-radius;
      padding: 30px;
      text-align: center;
      transition: transform 0.3s, box-shadow 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: $box-shadow;
      }

      .benefit-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 70px;
        height: 70px;
        background-color: $primary-color;
        border-radius: 50%;
        margin: 0 auto 20px;

        mat-icon {
          font-size: 35px;
          height: 35px;
          width: 35px;
          color: $white;
        }
      }

      h3 {
        font-size: 1.3rem;
        margin-bottom: 15px;
        color: $text-color;
      }

      p {
        color: $dark-gray;
        line-height: 1.6;
        margin-bottom: 0;
      }
    }
  }
}

// Testimonials Section
.testimonials-section {
  background-color: $light-gray;

  .testimonials-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;

    @media (max-width: 576px) {
      grid-template-columns: 1fr;
    }

    .testimonial-card {
      background-color: $white;
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      display: flex;
      flex-direction: column;
      height: 100%;

      .testimonial-image {
        height: 200px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .testimonial-content {
        padding: 30px;
        flex-grow: 1;
        position: relative;

        .quote-icon {
          position: absolute;
          top: -20px;
          left: 30px;
          width: 40px;
          height: 40px;
          background-color: $primary-color;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          mat-icon {
            color: $white;
            font-size: 20px;
            height: 20px;
            width: 20px;
          }
        }

        .testimonial-quote {
          font-style: italic;
          color: $text-color;
          line-height: 1.6;
          margin-bottom: 20px;
        }

        .testimonial-author {
          .author-name {
            font-weight: 500;
            color: $text-color;
            margin-bottom: 5px;
          }

          .author-position {
            color: $dark-gray;
            font-size: 0.9rem;
            margin: 0;
          }
        }
      }
    }
  }
}

// Process Section
.process-section {
  background-color: $white;

  .process-steps {
    max-width: 800px;
    margin: 0 auto;

    .step {
      display: flex;
      margin-bottom: 40px;

      &:last-child {
        margin-bottom: 0;
      }

      .step-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        background-color: $primary-color;
        color: $white;
        border-radius: 50%;
        font-size: 1.5rem;
        font-weight: 500;
        margin-right: 20px;
        flex-shrink: 0;
      }

      .step-content {
        h3 {
          font-size: 1.3rem;
          margin: 0 0 10px;
          color: $text-color;
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          margin: 0;
        }
      }

      @media (max-width: 576px) {
        flex-direction: column;

        .step-number {
          margin-right: 0;
          margin-bottom: 15px;
        }
      }
    }
  }
}

// FAQs Section
.faqs-section {
  background-color: $light-gray;

  .faqs-container {
    max-width: 800px;
    margin: 0 auto;

    mat-accordion {
      mat-expansion-panel {
        margin-bottom: 15px;
        border-radius: $border-radius !important;
        overflow: hidden;

        &:last-child {
          margin-bottom: 0;
        }

        mat-expansion-panel-header {
          padding: 20px;

          mat-panel-title {
            color: $text-color;
            font-size: 1.1rem;
            font-weight: 500;
          }
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          padding: 0 20px 20px;
          margin: 0;
        }
      }
    }
  }
}

// Contact Section
.contact-section {
  background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -15%));
  color: $white;

  .contact-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h2 {
      color: $white;

      &:after {
        background-color: $white;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
    }

    .contact-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;

      @media (max-width: 576px) {
        flex-direction: column;
        align-items: center;

        a {
          width: 100%;
          max-width: 250px;
        }
      }

      a {
        display: flex;
        align-items: center;
        padding: 10px 20px;

        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  // Hero section styles removed

  section {
    padding: 60px 0;

    h2 {
      font-size: 2rem;
    }
  }
}

@media (max-width: 768px) {
  // Hero section styles removed
}

@media (max-width: 576px) {
  // Hero section styles removed

  section h2 {
    font-size: 1.8rem;
  }
}