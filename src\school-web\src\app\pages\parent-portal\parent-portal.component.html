<div class="parent-portal-container">
  <mat-toolbar color="primary" class="portal-header">
    <button mat-icon-button (click)="sidenav.toggle()">
      <mat-icon>menu</mat-icon>
    </button>
    <span>Parent Portal</span>
    <span class="spacer"></span>

    <!-- Language & Theme Switcher -->
    <app-language-theme-switcher
      style="icon-only"
      class="header-switcher">
    </app-language-theme-switcher>

    <button mat-icon-button [matMenuTriggerFor]="userMenu">
      <mat-icon>account_circle</mat-icon>
    </button>
    <mat-menu #userMenu="matMenu">
      <button mat-menu-item routerLink="/parent-portal/profile">
        <mat-icon>person</mat-icon>
        <span>Profile</span>
      </button>
      <button mat-menu-item (click)="logout()">
        <mat-icon>exit_to_app</mat-icon>
        <span>Logout</span>
      </button>
    </mat-menu>
  </mat-toolbar>

  <mat-sidenav-container class="sidenav-container">
    <mat-sidenav #sidenav mode="side" opened class="sidenav">
      <div class="parent-info" *ngIf="parent">
        <div class="parent-avatar">
          <img *ngIf="parent.profileImage" [src]="parent.profileImage.filePath" alt="Parent Photo">
          <mat-icon *ngIf="!parent.profileImage">account_circle</mat-icon>
        </div>
        <div class="parent-details">
          <h3>{{ parent.fullName }}</h3>
          <p>{{ parent.email }}</p>
        </div>
      </div>

      <mat-nav-list>
        <a mat-list-item *ngFor="let item of navItems" [routerLink]="item.route" routerLinkActive="active-link">
          <mat-icon matListItemIcon>{{ item.icon }}</mat-icon>
          <span matListItemTitle>{{ item.label }}</span>
        </a>

        <mat-divider *ngIf="parent && parent.students && parent.students.length"></mat-divider>

        <div *ngFor="let student of parent && parent.students ? parent.students : []">
          <h3 matSubheader>{{ student.student.firstName }} {{ student.student.lastName }}</h3>
          <a mat-list-item *ngFor="let item of studentNavItems"
             [routerLink]="getStudentRoute(student.studentId, item.route)"
             routerLinkActive="active-link">
            <mat-icon matListItemIcon>{{ item.icon }}</mat-icon>
            <span matListItemTitle>{{ item.label }}</span>
          </a>
        </div>
      </mat-nav-list>
    </mat-sidenav>

    <mat-sidenav-content class="content">
      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
      </div>

      <div *ngIf="error" class="error-container">
        <mat-card>
          <mat-card-content>
            <p>Unable to load parent data. Please try again later.</p>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button color="primary" (click)="loadParentData()">Retry</button>
          </mat-card-actions>
        </mat-card>
      </div>

      <div *ngIf="!loading && !error" class="router-container">
        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>
