<!-- Hero Section -->
<app-default-hero
  translationPrefix="CAMPUS_LIFE"
  title="CAMPUS_LIFE.CLUBS"
  subtitle="CAMPUS_LIFE.CLUBS_SUBTITLE"
  theme="dark"
  size="large"
  alignment="center"
  backgroundImage="assets/images/campus/clubs-hero.jpg">
</app-default-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'CAMPUS_LIFE.CLUBS_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.CLUBS_INTRO_P1' | translate }}</p>
      <p>{{ 'CAMPUS_LIFE.CLUBS_INTRO_P2' | translate }}</p>
    </div>
  </section>

  <!-- Club Categories Section -->
  <section class="clubs-section">
    <h2>{{ 'CAMPUS_LIFE.EXPLORE_CLUBS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.CLUBS_EXPLORE_INTRO' | translate }}</p>

    <!-- Loading Spinner -->
    <div *ngIf="loading" class="loading-container">
      <mat-spinner></mat-spinner>
    </div>

    <!-- Error Message -->
    <div *ngIf="error && !loading" class="error-container">
      <p>{{ 'COMMON.ERROR_LOADING_DATA' | translate }}</p>
      <button mat-raised-button color="primary" (click)="loadClubs()">
        {{ 'COMMON.RETRY' | translate }}
      </button>
    </div>

    <!-- Club Tabs -->
    <mat-tab-group *ngIf="!loading && !error" animationDuration="300ms">
      <mat-tab *ngFor="let category of clubCategories" [label]="category">
        <div class="clubs-grid">
          <mat-card class="club-card" *ngFor="let club of clubs | clubFilter:category">
            <div class="club-image">
              <img [src]="club.profileImageUrl || club.image" [alt]="club.name">
            </div>
            <mat-card-content>
              <h3>{{club.name}}</h3>
              <p class="club-description">{{club.shortDescription || club.description}}</p>
              <div class="club-details">
                <div class="detail-item" *ngIf="club.meetingSchedule">
                  <mat-icon>schedule</mat-icon>
                  <span>{{club.meetingSchedule}}</span>
                </div>
                <div class="detail-item" *ngIf="club.advisor || (club.advisors && club.advisors.length > 0)">
                  <mat-icon>person</mat-icon>
                  <span *ngIf="club.advisor">{{club.advisor}}</span>
                  <span *ngIf="!club.advisor && club.advisors && club.advisors.length > 0">{{club.advisors[0]}}</span>
                </div>
                <div class="detail-item" *ngIf="club.location">
                  <mat-icon>location_on</mat-icon>
                  <span>{{club.location}}</span>
                </div>
              </div>
            </mat-card-content>
            <mat-card-actions>
              <a mat-button color="primary" [routerLink]="['/campus/clubs', club.id]">
                {{ 'CLUBS.VIEW_DETAILS' | translate }}
              </a>
            </mat-card-actions>
          </mat-card>
        </div>
      </mat-tab>
    </mat-tab-group>
  </section>

  <!-- Starting a Club Section -->
  <section class="start-club-section">
    <h2>{{ 'CAMPUS_LIFE.START_CLUB' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.START_CLUB_INTRO' | translate }}</p>

    <div class="steps-container">
      <div class="step-item" *ngFor="let step of formationSteps; let i = index">
        <div class="step-number">{{i + 1}}</div>
        <div class="step-content">
          <h3>{{step.title}}</h3>
          <p>{{step.description}}</p>
        </div>
      </div>
    </div>

    <div class="start-club-cta">
      <a mat-raised-button color="primary" href="assets/documents/club-application.pdf" target="_blank">
        {{ 'CAMPUS_LIFE.DOWNLOAD_APPLICATION' | translate }}
      </a>
    </div>
  </section>

  <!-- Leadership Opportunities Section -->
  <section class="leadership-section">
    <h2>{{ 'CAMPUS_LIFE.LEADERSHIP_OPPORTUNITIES' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.LEADERSHIP_INTRO' | translate }}</p>

    <div class="leadership-container">
      <mat-accordion>
        <mat-expansion-panel *ngFor="let role of leadershipRoles">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{role.title}}
            </mat-panel-title>
          </mat-expansion-panel-header>

          <div class="role-responsibilities">
            <h4>{{ 'CAMPUS_LIFE.RESPONSIBILITIES' | translate }}</h4>
            <ul>
              <li *ngFor="let responsibility of role.responsibilities">{{responsibility}}</li>
            </ul>
          </div>
        </mat-expansion-panel>
      </mat-accordion>
    </div>
  </section>

  <!-- Club Benefits Section -->
  <section class="benefits-section">
    <h2>{{ 'CAMPUS_LIFE.CLUB_BENEFITS' | translate }}</h2>

    <div class="benefits-grid">
      <div class="benefit-item">
        <div class="benefit-icon">
          <mat-icon>groups</mat-icon>
        </div>
        <h3>{{ 'CAMPUS_LIFE.BENEFIT_COMMUNITY' | translate }}</h3>
        <p>{{ 'CAMPUS_LIFE.BENEFIT_COMMUNITY_DESC' | translate }}</p>
      </div>

      <div class="benefit-item">
        <div class="benefit-icon">
          <mat-icon>psychology</mat-icon>
        </div>
        <h3>{{ 'CAMPUS_LIFE.BENEFIT_SKILLS' | translate }}</h3>
        <p>{{ 'CAMPUS_LIFE.BENEFIT_SKILLS_DESC' | translate }}</p>
      </div>

      <div class="benefit-item">
        <div class="benefit-icon">
          <mat-icon>explore</mat-icon>
        </div>
        <h3>{{ 'CAMPUS_LIFE.BENEFIT_EXPLORATION' | translate }}</h3>
        <p>{{ 'CAMPUS_LIFE.BENEFIT_EXPLORATION_DESC' | translate }}</p>
      </div>

      <div class="benefit-item">
        <div class="benefit-icon">
          <mat-icon>school</mat-icon>
        </div>
        <h3>{{ 'CAMPUS_LIFE.BENEFIT_COLLEGE' | translate }}</h3>
        <p>{{ 'CAMPUS_LIFE.BENEFIT_COLLEGE_DESC' | translate }}</p>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="contact-section">
    <div class="contact-content">
      <h2>{{ 'CAMPUS_LIFE.QUESTIONS' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.QUESTIONS_TEXT' | translate }}</p>
      <div class="contact-info">
        <div class="contact-item">
          <mat-icon>person</mat-icon>
          <div class="contact-details">
            <h3>{{ 'CAMPUS_LIFE.ACTIVITIES_DIRECTOR' | translate }}</h3>
            <p>Ms. Jennifer Wilson</p>
            <p>jwilson&#64;school.edu</p>
            <p>(123) 456-7890 ext. 123</p>
          </div>
        </div>

        <div class="contact-item">
          <mat-icon>location_on</mat-icon>
          <div class="contact-details">
            <h3>{{ 'CAMPUS_LIFE.STUDENT_ACTIVITIES_OFFICE' | translate }}</h3>
            <p>Student Center, Room 102</p>
            <p>Monday-Friday, 8:00 AM - 4:00 PM</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
