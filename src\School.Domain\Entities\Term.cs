using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class Term : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this term belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public Guid AcademicYearId { get; set; }
    public string Name { get; set; } = string.Empty; // e.g., "First Semester", "Spring Term"
    public string Code { get; set; } = string.Empty; // e.g., "S1", "SPRING"
    public TermType Type { get; set; } = TermType.Semester;
    public TermStatus Status { get; set; } = TermStatus.Planned;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int OrderIndex { get; set; } // Order within the academic year (1, 2, 3, etc.)
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    
    // Term configuration
    public int TotalWorkingDays { get; set; }
    public int TotalHolidays { get; set; }
    public DateTime? ExamStartDate { get; set; }
    public DateTime? ExamEndDate { get; set; }
    public DateTime? ResultPublishDate { get; set; }
    public DateTime? RegistrationDeadline { get; set; }
    public DateTime? FeePaymentDeadline { get; set; }
    
    // Grading configuration
    public decimal? PassingGrade { get; set; }
    public decimal? MaximumGrade { get; set; }
    public string GradingScale { get; set; } = string.Empty; // JSON or reference to grading system
    
    // Navigation properties
    public AcademicYear AcademicYear { get; set; } = null!;
    public ICollection<AcademicCalendar> CalendarEvents { get; set; } = new List<AcademicCalendar>();
    public ICollection<Holiday> Holidays { get; set; } = new List<Holiday>();
    public ICollection<TermTranslation> Translations { get; set; } = new List<TermTranslation>();
}
