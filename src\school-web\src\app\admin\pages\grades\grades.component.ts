import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { GradeService } from '../../../core/services/grade.service';
import { GradeCreateEditDialogComponent } from './grade-create-edit-dialog.component';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';

export interface Grade {
  id: string;
  name: string;
  code: string;
  level: number;
  educationLevel: string;
  description: string;
  minAge: number;
  maxAge: number;
  maxStudents: number;
  isActive: boolean;
  displayOrder: number;
  academicYearId: string;
  academicYearName: string;
  promotionCriteria: string;
  minPassingGrade: number;
  remarks: string;
  createdAt: Date;
  lastModifiedAt?: Date;
}

@Component({
  selector: 'app-grades',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatSnackBarModule,
    MatCardModule,
    MatToolbarModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './grades.component.html',
  styleUrls: ['./grades.component.scss']
})
export class GradesComponent implements OnInit {
  private dialog = inject(MatDialog);
  private snackBar = inject(MatSnackBar);
  private gradeService = inject(GradeService);

  displayedColumns: string[] = [
    'code',
    'name', 
    'level',
    'educationLevel',
    'maxStudents',
    'academicYear',
    'isActive',
    'actions'
  ];

  grades: Grade[] = [];
  loading = false;
  totalCount = 0;
  pageSize = 10;
  pageIndex = 0;
  searchTerm = '';
  selectedAcademicYear = '';

  ngOnInit() {
    this.loadGrades();
  }

  loadGrades() {
    this.loading = true;
    const filter = {
      page: this.pageIndex + 1,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm,
      academicYearId: this.selectedAcademicYear || undefined,
      isActive: undefined
    };

    this.gradeService.getGrades(filter).subscribe({
      next: (response) => {
        this.grades = response.data;
        this.totalCount = response.totalCount;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading grades:', error);
        this.snackBar.open('Error loading grades', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  onSearch() {
    this.pageIndex = 0;
    this.loadGrades();
  }

  onPageChange(event: any) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadGrades();
  }

  openCreateDialog() {
    const dialogRef = this.dialog.open(GradeCreateEditDialogComponent, {
      width: '600px',
      data: { mode: 'create' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadGrades();
        this.snackBar.open('Grade created successfully', 'Close', { duration: 3000 });
      }
    });
  }

  openEditDialog(grade: Grade) {
    const dialogRef = this.dialog.open(GradeCreateEditDialogComponent, {
      width: '600px',
      data: { mode: 'edit', grade: { ...grade } }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadGrades();
        this.snackBar.open('Grade updated successfully', 'Close', { duration: 3000 });
      }
    });
  }

  deleteGrade(grade: Grade) {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Grade',
        message: `Are you sure you want to delete the grade "${grade.name}"? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.gradeService.deleteGrade(grade.id).subscribe({
          next: () => {
            this.loadGrades();
            this.snackBar.open('Grade deleted successfully', 'Close', { duration: 3000 });
          },
          error: (error) => {
            console.error('Error deleting grade:', error);
            this.snackBar.open('Error deleting grade', 'Close', { duration: 3000 });
          }
        });
      }
    });
  }

  toggleGradeStatus(grade: Grade) {
    const newStatus = !grade.isActive;
    this.gradeService.updateGradeStatus(grade.id, newStatus).subscribe({
      next: () => {
        grade.isActive = newStatus;
        this.snackBar.open(
          `Grade ${newStatus ? 'activated' : 'deactivated'} successfully`, 
          'Close', 
          { duration: 3000 }
        );
      },
      error: (error) => {
        console.error('Error updating grade status:', error);
        this.snackBar.open('Error updating grade status', 'Close', { duration: 3000 });
      }
    });
  }

  getEducationLevelDisplay(level: string): string {
    const levels: { [key: string]: string } = {
      'PreSchool': 'Pre-School',
      'Primary': 'Primary',
      'Secondary': 'Secondary',
      'HigherSecondary': 'Higher Secondary',
      'Undergraduate': 'Undergraduate',
      'Postgraduate': 'Postgraduate'
    };
    return levels[level] || level;
  }
}
