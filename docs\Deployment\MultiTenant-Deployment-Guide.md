# Multi-Tenant Deployment Guide

## Overview

This guide covers the deployment of the multi-tenant School Management System, including infrastructure setup, DNS configuration, and production considerations.

## Architecture Overview

```
Internet
    ↓
DNS (*.edumanage.com)
    ↓
Load Balancer (Nginx/CloudFlare)
    ↓
Application Server (ASP.NET Core)
    ↓
Database (SQL Server with tenant isolation)
```

## Prerequisites

- Domain name (e.g., `edumanage.com`)
- SSL certificate (wildcard recommended)
- SQL Server database
- Web server (IIS, Nginx, or Apache)
- .NET 8 Runtime

## DNS Configuration

### Wildcard DNS Setup

Configure wildcard DNS to point all subdomains to your server:

```dns
Type: A
Name: *
Value: YOUR_SERVER_IP
TTL: 300
```

### Custom Domain Support

For schools with custom domains, add CNAME records:

```dns
# For myschool.edu → greenwood-school.edumanage.com
Type: CNAME
Name: @
Value: greenwood-school.edumanage.com
TTL: 300
```

## SSL Certificate Configuration

### Wildcard Certificate (Recommended)

Using Let's Encrypt with DNS validation:

```bash
# Install certbot
sudo apt-get install certbot python3-certbot-dns-cloudflare

# Create Cloudflare credentials file
cat > ~/.secrets/certbot/cloudflare.ini << EOF
dns_cloudflare_email = <EMAIL>
dns_cloudflare_api_key = your-cloudflare-api-key
EOF

# Generate wildcard certificate
sudo certbot certonly \
  --dns-cloudflare \
  --dns-cloudflare-credentials ~/.secrets/certbot/cloudflare.ini \
  -d "*.edumanage.com" \
  -d "edumanage.com"
```

### Individual Certificates

For custom domains, generate individual certificates:

```bash
sudo certbot certonly --webroot -w /var/www/html -d myschool.edu
```

## Web Server Configuration

### Nginx Configuration

```nginx
# /etc/nginx/sites-available/edumanage
server {
    listen 80;
    server_name *.edumanage.com edumanage.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name *.edumanage.com edumanage.com;

    ssl_certificate /etc/letsencrypt/live/edumanage.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/edumanage.com/privkey.pem;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # API proxy
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static files
    location / {
        root /var/www/edumanage/wwwroot;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}

# Custom domain support
server {
    listen 443 ssl http2;
    server_name myschool.edu;  # Add custom domains here

    ssl_certificate /etc/letsencrypt/live/myschool.edu/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/myschool.edu/privkey.pem;

    # Same configuration as above
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        # *** other headers
    }

    location / {
        root /var/www/edumanage/wwwroot;
        try_files $uri $uri/ /index.html;
    }
}
```

### IIS Configuration

```xml
<!-- web.config for IIS -->
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" 
                  arguments=".\School.API.dll" 
                  stdoutLogEnabled="false" 
                  stdoutLogFile=".\logs\stdout" 
                  hostingModel="inprocess" />
      
      <!-- URL Rewrite for SPA -->
      <rewrite>
        <rules>
          <rule name="Angular Routes" stopProcessing="true">
            <match url=".*" />
            <conditions logicalGrouping="MatchAll">
              <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
              <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
              <add input="{REQUEST_URI}" pattern="^/(api)" negate="true" />
            </conditions>
            <action type="Rewrite" url="/index.html" />
          </rule>
        </rules>
      </rewrite>
      
      <!-- Security headers -->
      <httpProtocol>
        <customHeaders>
          <add name="X-Frame-Options" value="DENY" />
          <add name="X-Content-Type-Options" value="nosniff" />
          <add name="X-XSS-Protection" value="1; mode=block" />
          <add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />
        </customHeaders>
      </httpProtocol>
    </system.webServer>
  </location>
</configuration>
```

## Application Configuration

### Production appsettings.json

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=your-sql-server;Database=SchoolManagement;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"
  },
  "JWT": {
    "Secret": "your-super-secure-jwt-secret-key-here",
    "Issuer": "https://edumanage.com",
    "Audience": "https://edumanage.com",
    "ExpiryInMinutes": 60
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "School.Infrastructure.Services.TenantService": "Information",
      "School.Infrastructure.Middleware.TenantMiddleware": "Information"
    }
  },
  "AllowedHosts": "*.edumanage.com;edumanage.com",
  "MultiTenant": {
    "BaseDomain": "edumanage.com",
    "EnableCustomDomains": true,
    "CacheExpirationMinutes": 15
  },
  "CORS": {
    "AllowedOrigins": [
      "https://*.edumanage.com",
      "https://edumanage.com"
    ]
  }
}
```

### Environment Variables

```bash
# Production environment
export ASPNETCORE_ENVIRONMENT=Production
export ASPNETCORE_URLS="http://localhost:5000"

# Database connection
export ConnectionStrings__DefaultConnection="Server=***;Database=***;User Id=***;Password=***;"

# JWT configuration
export JWT__Secret="your-production-jwt-secret"
export JWT__Issuer="https://edumanage.com"
export JWT__Audience="https://edumanage.com"

# Multi-tenant configuration
export MultiTenant__BaseDomain="edumanage.com"
export MultiTenant__EnableCustomDomains="true"
```

## Database Setup

### Production Database Configuration

```sql
-- Create production database
CREATE DATABASE SchoolManagement;
GO

-- Create application user
CREATE LOGIN SchoolManagementApp WITH PASSWORD = 'SecurePassword123!';
GO

USE SchoolManagement;
GO

CREATE USER SchoolManagementApp FOR LOGIN SchoolManagementApp;
GO

-- Grant necessary permissions
ALTER ROLE db_datareader ADD MEMBER SchoolManagementApp;
ALTER ROLE db_datawriter ADD MEMBER SchoolManagementApp;
ALTER ROLE db_ddladmin ADD MEMBER SchoolManagementApp;
GO
```

### Run Migrations

```bash
# Apply database migrations
dotnet ef database update --project src/School.Infrastructure --startup-project src/School.API
```

### Create Initial Organizations

```sql
-- Create sample organizations
INSERT INTO Organizations (Id, Name, Slug, DisplayName, Type, Status, IsActive, CreatedAt, DefaultLanguage, TimeZone, Currency)
VALUES 
  (NEWID(), 'Demo Elementary School', 'demo-elementary', 'Demo Elementary', 3, 2, 1, GETUTCDATE(), 'en-US', 'America/New_York', 'USD'),
  (NEWID(), 'Sample High School', 'sample-high', 'Sample High School', 4, 2, 1, GETUTCDATE(), 'en-US', 'America/Los_Angeles', 'USD');
```

## Monitoring and Logging

### Application Insights (Azure)

```json
{
  "ApplicationInsights": {
    "ConnectionString": "InstrumentationKey=your-key;IngestionEndpoint=https://***"
  }
}
```

### Serilog Configuration

```json
{
  "Serilog": {
    "Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"],
    "MinimumLevel": "Information",
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "/var/log/edumanage/app-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30,
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
        }
      },
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "{Timestamp:HH:mm:ss} [{Level:u3}] {Message:lj}{NewLine}{Exception}"
        }
      }
    ],
    "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]
  }
}
```

## Security Considerations

### Firewall Configuration

```bash
# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow SSH (change port as needed)
sudo ufw allow 22/tcp

# Block all other incoming traffic
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Enable firewall
sudo ufw enable
```

### Database Security

1. **Use strong passwords** for database accounts
2. **Enable encryption** at rest and in transit
3. **Regular backups** with encryption
4. **Network isolation** - database should not be publicly accessible
5. **Audit logging** enabled for all database operations

### Application Security

1. **HTTPS only** - redirect all HTTP to HTTPS
2. **Security headers** - implement CSP, HSTS, etc.
3. **Rate limiting** - implement per-tenant rate limiting
4. **Input validation** - validate all user inputs
5. **Regular updates** - keep dependencies updated

## Backup and Disaster Recovery

### Database Backups

```sql
-- Full backup script
BACKUP DATABASE SchoolManagement 
TO DISK = '/backups/SchoolManagement_Full.bak'
WITH FORMAT, INIT, COMPRESSION;

-- Differential backup
BACKUP DATABASE SchoolManagement 
TO DISK = '/backups/SchoolManagement_Diff.bak'
WITH DIFFERENTIAL, COMPRESSION;
```

### Application Backups

```bash
#!/bin/bash
# Backup script for application files
tar -czf /backups/app-$(date +%Y%m%d).tar.gz /var/www/edumanage/
```

## Performance Optimization

### Caching Strategy

1. **Memory caching** for tenant information
2. **Redis caching** for session data (if using multiple servers)
3. **CDN** for static assets
4. **Database query optimization** with proper indexing

### Database Indexing

```sql
-- Tenant-specific indexes
CREATE INDEX IX_Students_TenantId ON Students (TenantId);
CREATE INDEX IX_Faculty_TenantId ON Faculty (TenantId);
CREATE INDEX IX_Parents_TenantId ON Parents (TenantId);

-- Composite indexes for common queries
CREATE INDEX IX_Students_TenantId_StudentId ON Students (TenantId, StudentId);
CREATE INDEX IX_Organizations_Slug ON Organizations (Slug);
CREATE INDEX IX_Organizations_CustomDomain ON Organizations (CustomDomain);
```

## Troubleshooting

### Common Issues

1. **Tenant not found**: Check DNS configuration and organization setup
2. **SSL certificate errors**: Verify certificate installation and renewal
3. **Database connection issues**: Check connection strings and firewall rules
4. **Performance issues**: Monitor database queries and implement caching

### Health Checks

```csharp
// Add to Program.cs
builder.Services.AddHealthChecks()
    .AddDbContext<ApplicationDbContext>()
    .AddCheck<TenantHealthCheck>("tenant-check");
```

This comprehensive deployment guide ensures a secure, scalable, and maintainable multi-tenant deployment.
