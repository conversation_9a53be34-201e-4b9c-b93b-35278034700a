<!-- Hero Section -->
<section class="hero-section">
  <div class="hero-content">
    <h1>{{ 'CONTACT.TITLE' | translate }}</h1>
    <p class="subtitle">{{ 'CONTACT.SUBTITLE' | translate }}</p>
  </div>
</section>

<!-- Main Content -->
<div class="container">
  <!-- Contact Form Section -->
  <section class="contact-form-section">
    <div class="form-container">
      <h2>{{ 'CONTACT.FORM_TITLE' | translate }}</h2>
      <p class="section-intro">{{ 'CONTACT.FORM_INTRO' | translate }}</p>

      <form [formGroup]="contactForm" (ngSubmit)="submitForm()">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>{{ 'CONTACT.NAME' | translate }}</mat-label>
            <input matInput formControlName="name" required>
            <mat-error *ngIf="contactForm.get('name')?.invalid && contactForm.get('name')?.touched">
              {{ 'CONTACT.REQUIRED_FIELD' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row two-columns">
          <mat-form-field appearance="outline">
            <mat-label>{{ 'CONTACT.EMAIL' | translate }}</mat-label>
            <input matInput formControlName="email" required type="email">
            <mat-error *ngIf="contactForm.get('email')?.invalid && contactForm.get('email')?.touched">
              {{ 'CONTACT.VALID_EMAIL' | translate }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>{{ 'CONTACT.PHONE' | translate }}</mat-label>
            <input matInput formControlName="phone">
          </mat-form-field>
        </div>

        <div class="form-row two-columns">
          <mat-form-field appearance="outline">
            <mat-label>{{ 'CONTACT.SUBJECT' | translate }}</mat-label>
            <input matInput formControlName="subject" required>
            <mat-error *ngIf="contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched">
              {{ 'CONTACT.REQUIRED_FIELD' | translate }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>{{ 'CONTACT.DEPARTMENT' | translate }}</mat-label>
            <mat-select formControlName="department">
              <mat-option value="">{{ 'CONTACT.SELECT_DEPARTMENT' | translate }}</mat-option>
              <mat-option *ngFor="let dept of departments" [value]="dept.name">{{dept.name}}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>{{ 'CONTACT.MESSAGE' | translate }}</mat-label>
            <textarea matInput formControlName="message" rows="6" required></textarea>
            <mat-error *ngIf="contactForm.get('message')?.invalid && contactForm.get('message')?.touched">
              {{ 'CONTACT.REQUIRED_FIELD' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-actions">
          <button mat-raised-button color="primary" type="submit">{{ 'CONTACT.SUBMIT' | translate }}</button>
        </div>
      </form>
    </div>

    <div class="contact-info-sidebar">
      <div class="info-card">
        <h3>{{ 'CONTACT.GENERAL_INQUIRIES' | translate }}</h3>
        <div class="info-item">
          <mat-icon>phone</mat-icon>
          <span>{{ 'CONTACT.PHONE_NUMBER' | translate }}</span>
        </div>
        <div class="info-item">
          <mat-icon>email</mat-icon>
          <span>{{ 'CONTACT.EMAIL_ADDRESS' | translate }}</span>
        </div>
        <div class="info-item">
          <mat-icon>access_time</mat-icon>
          <span>{{ 'CONTACT.OFFICE_HOURS' | translate }}</span>
        </div>
      </div>

      <div class="social-media-links">
        <h3>{{ 'CONTACT.CONNECT' | translate }}</h3>
        <div class="social-icons">
          <a *ngFor="let social of socialMedia" [href]="social.url" target="_blank" class="social-icon">
            <i [class]="getSocialIconClass(social.icon)"></i>
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Locations Section -->
  <section class="locations-section">
    <h2>{{ 'CONTACT.LOCATIONS' | translate }}</h2>
    <p class="section-intro">{{ 'CONTACT.LOCATIONS_INTRO' | translate }}</p>

    <div class="locations-container">
      <mat-tab-group animationDuration="300ms">
        <mat-tab *ngFor="let location of locations" [label]="location.name">
          <div class="location-details">
            <div class="location-info">
              <h3>{{location.name}}</h3>

              <div class="info-item">
                <mat-icon>location_on</mat-icon>
                <span>{{location.address}}</span>
              </div>

              <div class="info-item">
                <mat-icon>phone</mat-icon>
                <span>{{location.phone}}</span>
              </div>

              <div class="info-item">
                <mat-icon>email</mat-icon>
                <span>{{location.email}}</span>
              </div>

              <div class="info-item">
                <mat-icon>access_time</mat-icon>
                <span>{{location.hours}}</span>
              </div>

              <div class="directions-button">
                <a mat-raised-button color="primary" [href]="'https://maps.google.com/?q=' + location.address" target="_blank">
                  <mat-icon>directions</mat-icon>
                  {{ 'CONTACT.GET_DIRECTIONS' | translate }}
                </a>
              </div>
            </div>

            <div class="location-map">
              <iframe [src]="location.mapUrl | safe: 'resourceUrl'" width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </section>

  <!-- Departments Section -->
  <section class="departments-section">
    <h2>{{ 'CONTACT.DEPARTMENTS' | translate }}</h2>
    <p class="section-intro">{{ 'CONTACT.DEPARTMENTS_INTRO' | translate }}</p>

    <div class="departments-grid">
      <mat-card class="department-card" *ngFor="let dept of departments">
        <div class="dept-icon">
          <mat-icon>{{dept.icon}}</mat-icon>
        </div>
        <h3>{{dept.name}}</h3>
        <p>{{dept.description}}</p>
        <div class="dept-contact">
          <div class="info-item">
            <mat-icon>email</mat-icon>
            <span>{{dept.email}}</span>
          </div>
          <div class="info-item">
            <mat-icon>phone</mat-icon>
            <span>{{dept.phone}}</span>
          </div>
        </div>
      </mat-card>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="faq-section">
    <h2>{{ 'CONTACT.FAQ' | translate }}</h2>
    <p class="section-intro">{{ 'CONTACT.FAQ_INTRO' | translate }}</p>

    <div class="faq-container">
      <mat-accordion>
        <mat-expansion-panel *ngFor="let faq of faqs">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{faq.question}}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <p>{{faq.answer}}</p>
        </mat-expansion-panel>
      </mat-accordion>
    </div>
  </section>
</div>
