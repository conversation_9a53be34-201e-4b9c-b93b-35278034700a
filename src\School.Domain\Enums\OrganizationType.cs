namespace School.Domain.Enums;

/// <summary>
/// Types of educational organizations
/// </summary>
public enum OrganizationType
{
    /// <summary>
    /// Primary/Elementary School (K-5)
    /// </summary>
    PrimarySchool = 1,

    /// <summary>
    /// Secondary School (6-12)
    /// </summary>
    SecondarySchool = 2,

    /// <summary>
    /// Combined School (K-12)
    /// </summary>
    School = 3,

    /// <summary>
    /// College/Higher Secondary (11-12)
    /// </summary>
    College = 4,

    /// <summary>
    /// University
    /// </summary>
    University = 5,

    /// <summary>
    /// Technical/Vocational Institute
    /// </summary>
    TechnicalInstitute = 6,

    /// <summary>
    /// Training Center
    /// </summary>
    TrainingCenter = 7,

    /// <summary>
    /// Academy/Specialized School
    /// </summary>
    Academy = 8,

    /// <summary>
    /// International School
    /// </summary>
    InternationalSchool = 9,

    /// <summary>
    /// Online/Virtual School
    /// </summary>
    OnlineSchool = 10
}
