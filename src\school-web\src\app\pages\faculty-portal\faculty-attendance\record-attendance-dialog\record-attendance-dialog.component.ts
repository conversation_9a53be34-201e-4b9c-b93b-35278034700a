import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>rray, FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

// Services
import { FacultyService } from '../../../../core/services/faculty.service';

@Component({
  selector: 'app-record-attendance-dialog',
  templateUrl: './record-attendance-dialog.component.html',
  styleUrls: ['./record-attendance-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatProgressSpinnerModule
  ]
})
export class RecordAttendanceDialogComponent implements OnInit {
  attendanceForm: FormGroup;
  loading = false;

  statusOptions = [
    { value: 0, label: 'Present' },
    { value: 1, label: 'Absent' },
    { value: 2, label: 'Late' },
    { value: 3, label: 'Excused' },
    { value: 4, label: 'On Leave' }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private facultyService: FacultyService,
    public dialogRef: MatDialogRef<RecordAttendanceDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: {
      facultyId: number;
      grade: number;
      section: string;
      date: Date;
      attendanceData: any[];
    }
  ) {
    this.attendanceForm = this.formBuilder.group({
      students: this.formBuilder.array([])
    });
  }

  ngOnInit(): void {
    this.loadStudents();
  }

  get students(): FormArray {
    return this.attendanceForm.get('students') as FormArray;
  }

  loadStudents(): void {
    this.loading = true;

    this.facultyService.getFacultyStudents(
      this.data.facultyId,
      this.data.grade,
      this.data.section
    ).subscribe({
      next: (students) => {
        // Clear existing form array
        while (this.students.length) {
          this.students.removeAt(0);
        }

        // Add students to form array
        students.forEach(student => {
          // Check if there's existing attendance data for this student
          const existingData = this.data.attendanceData?.find(a => a.studentId === student.id);

          this.students.push(this.formBuilder.group({
            studentId: [student.id, Validators.required],
            rollNumber: [student.rollNumber],
            firstName: [student.firstName],
            lastName: [student.lastName],
            status: [existingData?.status ?? 0, Validators.required], // Default to Present
            remarks: [existingData?.remarks ?? '']
          }));
        });

        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading students:', err);
        this.loading = false;
        this.dialogRef.close();
      }
    });
  }

  onSubmit(): void {
    if (this.attendanceForm.invalid) {
      return;
    }

    this.loading = true;

    const attendanceRecords = this.students.value.map((student: any) => ({
      studentId: student.studentId,
      status: student.status,
      remarks: student.remarks
    }));

    this.facultyService.recordClassAttendance(
      this.data.facultyId,
      this.data.grade,
      this.data.section,
      this.data.date,
      attendanceRecords
    ).subscribe({
      next: () => {
        this.loading = false;
        this.dialogRef.close(true);
      },
      error: (err) => {
        console.error('Error recording attendance:', err);
        this.loading = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  markAllPresent(): void {
    this.students.controls.forEach(control => {
      control.get('status')?.setValue(0); // Present
    });
  }

  markAllAbsent(): void {
    this.students.controls.forEach(control => {
      control.get('status')?.setValue(1); // Absent
    });
  }

  getStatusClass(status: number): string {
    switch (status) {
      case 0: return 'present';
      case 1: return 'absent';
      case 2: return 'late';
      case 3: return 'excused';
      case 4: return 'on-leave';
      default: return '';
    }
  }
}
