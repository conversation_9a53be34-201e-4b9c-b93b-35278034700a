import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-tenant-pending',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    RouterModule,
    TranslateModule
  ],
  template: `
    <div class="tenant-pending-container">
      <mat-card class="pending-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>hourglass_empty</mat-icon>
            School Setup Pending
          </mat-card-title>
          <mat-card-subtitle>
            Your school setup is not yet complete
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p>Your school administrator needs to complete the setup process before you can access the system.</p>
          <p>Please contact your school administrator or wait for the setup to be completed.</p>
          <div class="actions">
            <button mat-raised-button color="primary" routerLink="/login">
              <mat-icon>refresh</mat-icon>
              Check Again
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .tenant-pending-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
      padding: 20px;
      z-index: 9999;
      overflow-y: auto;
    }
    .pending-card {
      max-width: 500px;
      width: 100%;
      text-align: center;
    }
    mat-card-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    .actions {
      margin-top: 24px;
    }
  `]
})
export class TenantPendingComponent {}
