﻿START TRANSACTION;
ALTER TABLE "Students" DROP CONSTRAINT "FK_Students_Faculty_ClassTeacherId";

ALTER TABLE "Students" DROP CONSTRAINT "FK_Students_MediaItems_ProfileImageId";

ALTER TABLE "Students" ALTER COLUMN "UserId" TYPE character varying(450);

ALTER TABLE "Students" ALTER COLUMN "Phone" TYPE character varying(20);

ALTER TABLE "Students" ALTER COLUMN "LastName" TYPE character varying(100);

ALTER TABLE "Students" ALTER COLUMN "LastModifiedBy" TYPE character varying(450);

ALTER TABLE "Students" ALTER COLUMN "FirstName" TYPE character varying(100);

ALTER TABLE "Students" ALTER COLUMN "Email" TYPE character varying(100);

ALTER TABLE "Students" ALTER COLUMN "CreatedBy" TYPE character varying(450);

ALTER TABLE "Students" ALTER COLUMN "BloodGroup" TYPE character varying(10);

ALTER TABLE "Students" ALTER COLUMN "Address" TYPE character varying(500);

ALTER TABLE "Students" ADD "CurrentClassTeacherId" uuid;

ALTER TABLE "Students" ADD "CurrentGradeEntityId" uuid;

ALTER TABLE "Students" ADD "CurrentGradeId" uuid;

ALTER TABLE "Students" ADD "CurrentSectionEntityId" uuid;

ALTER TABLE "Students" ADD "CurrentSectionId" uuid;

CREATE TABLE "Grades" (
    "Id" uuid NOT NULL,
    "TenantId" uuid NOT NULL,
    "Name" character varying(100) NOT NULL,
    "Code" character varying(20) NOT NULL,
    "Level" integer NOT NULL,
    "EducationLevel" integer NOT NULL,
    "Description" character varying(500) NOT NULL,
    "MinAge" integer NOT NULL,
    "MaxAge" integer NOT NULL,
    "MaxStudents" integer NOT NULL,
    "IsActive" boolean NOT NULL,
    "DisplayOrder" integer NOT NULL,
    "AcademicYearId" uuid NOT NULL,
    "PromotionCriteria" character varying(1000) NOT NULL,
    "MinPassingGrade" numeric(5,2) NOT NULL,
    "Remarks" character varying(500) NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" character varying(450),
    "LastModifiedAt" timestamp with time zone,
    "LastModifiedBy" character varying(450),
    "IsDeleted" boolean NOT NULL,
    CONSTRAINT "PK_Grades" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_Grades_AcademicYears_AcademicYearId" FOREIGN KEY ("AcademicYearId") REFERENCES "AcademicYears" ("Id") ON DELETE CASCADE
);

CREATE TABLE "GradeTranslations" (
    "Id" uuid NOT NULL,
    "GradeId" uuid NOT NULL,
    "LanguageCode" character varying(10) NOT NULL,
    "Name" character varying(100) NOT NULL,
    "Description" character varying(500) NOT NULL,
    "PromotionCriteria" character varying(1000) NOT NULL,
    "Remarks" character varying(500) NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" character varying(450),
    "LastModifiedAt" timestamp with time zone,
    "LastModifiedBy" character varying(450),
    "IsDeleted" boolean NOT NULL,
    CONSTRAINT "PK_GradeTranslations" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_GradeTranslations_Grades_GradeId" FOREIGN KEY ("GradeId") REFERENCES "Grades" ("Id") ON DELETE CASCADE
);

CREATE TABLE "ClassTeachers" (
    "Id" uuid NOT NULL,
    "TenantId" uuid NOT NULL,
    "FacultyId" uuid NOT NULL,
    "SectionId" uuid NOT NULL,
    "AcademicYearId" uuid NOT NULL,
    "TermId" uuid,
    "StartDate" timestamp with time zone NOT NULL,
    "EndDate" timestamp with time zone,
    "IsActive" boolean NOT NULL,
    "IsPrimary" boolean NOT NULL,
    "Responsibilities" character varying(1000) NOT NULL,
    "SpecialDuties" character varying(1000) NOT NULL,
    "ContactSchedule" character varying(500) NOT NULL,
    "OfficeHours" character varying(500) NOT NULL,
    "Remarks" character varying(500) NOT NULL,
    "Status" integer NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" character varying(450),
    "LastModifiedAt" timestamp with time zone,
    "LastModifiedBy" character varying(450),
    "IsDeleted" boolean NOT NULL,
    CONSTRAINT "PK_ClassTeachers" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_ClassTeachers_AcademicYears_AcademicYearId" FOREIGN KEY ("AcademicYearId") REFERENCES "AcademicYears" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ClassTeachers_Faculty_FacultyId" FOREIGN KEY ("FacultyId") REFERENCES "Faculty" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ClassTeachers_Terms_TermId" FOREIGN KEY ("TermId") REFERENCES "Terms" ("Id")
);

CREATE TABLE "Sections" (
    "Id" uuid NOT NULL,
    "TenantId" uuid NOT NULL,
    "GradeId" uuid NOT NULL,
    "Name" character varying(100) NOT NULL,
    "Code" character varying(20) NOT NULL,
    "Type" integer NOT NULL,
    "Medium" integer NOT NULL,
    "Shift" integer NOT NULL,
    "Capacity" integer NOT NULL,
    "CurrentEnrollment" integer NOT NULL,
    "IsActive" boolean NOT NULL,
    "DisplayOrder" integer NOT NULL,
    "AcademicYearId" uuid NOT NULL,
    "ClassTeacherId" uuid,
    "Classroom" character varying(100) NOT NULL,
    "RoomNumber" character varying(50) NOT NULL,
    "Description" character varying(500) NOT NULL,
    "Requirements" character varying(1000) NOT NULL,
    "Remarks" character varying(500) NOT NULL,
    "ClassTeacherId1" uuid,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" character varying(450),
    "LastModifiedAt" timestamp with time zone,
    "LastModifiedBy" character varying(450),
    "IsDeleted" boolean NOT NULL,
    CONSTRAINT "PK_Sections" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_Sections_AcademicYears_AcademicYearId" FOREIGN KEY ("AcademicYearId") REFERENCES "AcademicYears" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_Sections_ClassTeachers_ClassTeacherId1" FOREIGN KEY ("ClassTeacherId1") REFERENCES "ClassTeachers" ("Id"),
    CONSTRAINT "FK_Sections_Grades_GradeId" FOREIGN KEY ("GradeId") REFERENCES "Grades" ("Id") ON DELETE CASCADE
);

CREATE TABLE "SectionTranslations" (
    "Id" uuid NOT NULL,
    "SectionId" uuid NOT NULL,
    "LanguageCode" character varying(10) NOT NULL,
    "Name" character varying(100) NOT NULL,
    "Description" character varying(500) NOT NULL,
    "Requirements" character varying(1000) NOT NULL,
    "Remarks" character varying(500) NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" character varying(450),
    "LastModifiedAt" timestamp with time zone,
    "LastModifiedBy" character varying(450),
    "IsDeleted" boolean NOT NULL,
    CONSTRAINT "PK_SectionTranslations" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_SectionTranslations_Sections_SectionId" FOREIGN KEY ("SectionId") REFERENCES "Sections" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_Students_CurrentClassTeacherId" ON "Students" ("CurrentClassTeacherId");

CREATE INDEX "IX_Students_CurrentGradeEntityId" ON "Students" ("CurrentGradeEntityId");

CREATE INDEX "IX_Students_CurrentGradeId" ON "Students" ("CurrentGradeId");

CREATE INDEX "IX_Students_CurrentSectionEntityId" ON "Students" ("CurrentSectionEntityId");

CREATE INDEX "IX_Students_CurrentSectionId" ON "Students" ("CurrentSectionId");

CREATE INDEX "IX_Students_Email" ON "Students" ("Email");

CREATE INDEX "IX_Students_Phone" ON "Students" ("Phone");

CREATE INDEX "IX_Students_RollNumber" ON "Students" ("RollNumber");

CREATE INDEX "IX_Students_UserId" ON "Students" ("UserId");

CREATE INDEX "IX_ClassTeachers_AcademicYearId" ON "ClassTeachers" ("AcademicYearId");

CREATE INDEX "IX_ClassTeachers_EndDate" ON "ClassTeachers" ("EndDate");

CREATE INDEX "IX_ClassTeachers_FacultyId_AcademicYearId_IsActive" ON "ClassTeachers" ("FacultyId", "AcademicYearId", "IsActive");

CREATE INDEX "IX_ClassTeachers_SectionId_IsActive" ON "ClassTeachers" ("SectionId", "IsActive");

CREATE INDEX "IX_ClassTeachers_StartDate" ON "ClassTeachers" ("StartDate");

CREATE INDEX "IX_ClassTeachers_Status" ON "ClassTeachers" ("Status");

CREATE INDEX "IX_ClassTeachers_TermId" ON "ClassTeachers" ("TermId");

CREATE INDEX "IX_Grades_AcademicYearId" ON "Grades" ("AcademicYearId");

CREATE UNIQUE INDEX "IX_Grades_Code_AcademicYearId" ON "Grades" ("Code", "AcademicYearId");

CREATE INDEX "IX_Grades_EducationLevel" ON "Grades" ("EducationLevel");

CREATE INDEX "IX_Grades_IsActive" ON "Grades" ("IsActive");

CREATE UNIQUE INDEX "IX_Grades_Level_AcademicYearId" ON "Grades" ("Level", "AcademicYearId");

CREATE UNIQUE INDEX "IX_GradeTranslations_GradeId_LanguageCode" ON "GradeTranslations" ("GradeId", "LanguageCode");

CREATE INDEX "IX_GradeTranslations_LanguageCode" ON "GradeTranslations" ("LanguageCode");

CREATE INDEX "IX_Sections_AcademicYearId" ON "Sections" ("AcademicYearId");

CREATE INDEX "IX_Sections_ClassTeacherId1" ON "Sections" ("ClassTeacherId1");

CREATE UNIQUE INDEX "IX_Sections_Code_GradeId" ON "Sections" ("Code", "GradeId");

CREATE INDEX "IX_Sections_GradeId" ON "Sections" ("GradeId");

CREATE INDEX "IX_Sections_IsActive" ON "Sections" ("IsActive");

CREATE INDEX "IX_Sections_Medium" ON "Sections" ("Medium");

CREATE UNIQUE INDEX "IX_Sections_Name_GradeId" ON "Sections" ("Name", "GradeId");

CREATE INDEX "IX_Sections_Shift" ON "Sections" ("Shift");

CREATE INDEX "IX_Sections_Type" ON "Sections" ("Type");

CREATE INDEX "IX_SectionTranslations_LanguageCode" ON "SectionTranslations" ("LanguageCode");

CREATE UNIQUE INDEX "IX_SectionTranslations_SectionId_LanguageCode" ON "SectionTranslations" ("SectionId", "LanguageCode");

ALTER TABLE "Students" ADD CONSTRAINT "FK_Students_ClassTeachers_ClassTeacherId" FOREIGN KEY ("ClassTeacherId") REFERENCES "ClassTeachers" ("Id") ON DELETE SET NULL;

ALTER TABLE "Students" ADD CONSTRAINT "FK_Students_ClassTeachers_CurrentClassTeacherId" FOREIGN KEY ("CurrentClassTeacherId") REFERENCES "ClassTeachers" ("Id");

ALTER TABLE "Students" ADD CONSTRAINT "FK_Students_Faculty_ClassTeacherId" FOREIGN KEY ("ClassTeacherId") REFERENCES "Faculty" ("Id") ON DELETE SET NULL;

ALTER TABLE "Students" ADD CONSTRAINT "FK_Students_Grades_CurrentGradeEntityId" FOREIGN KEY ("CurrentGradeEntityId") REFERENCES "Grades" ("Id");

ALTER TABLE "Students" ADD CONSTRAINT "FK_Students_Grades_CurrentGradeId" FOREIGN KEY ("CurrentGradeId") REFERENCES "Grades" ("Id") ON DELETE SET NULL;

ALTER TABLE "Students" ADD CONSTRAINT "FK_Students_MediaItems_ProfileImageId" FOREIGN KEY ("ProfileImageId") REFERENCES "MediaItems" ("Id") ON DELETE SET NULL;

ALTER TABLE "Students" ADD CONSTRAINT "FK_Students_Sections_CurrentSectionEntityId" FOREIGN KEY ("CurrentSectionEntityId") REFERENCES "Sections" ("Id");

ALTER TABLE "Students" ADD CONSTRAINT "FK_Students_Sections_CurrentSectionId" FOREIGN KEY ("CurrentSectionId") REFERENCES "Sections" ("Id") ON DELETE SET NULL;

ALTER TABLE "ClassTeachers" ADD CONSTRAINT "FK_ClassTeachers_Sections_SectionId" FOREIGN KEY ("SectionId") REFERENCES "Sections" ("Id") ON DELETE CASCADE;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250719143336_Sprint3_GradeSectionClassTeacher', '9.0.7');

COMMIT;

