@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Loading and Error States
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
  text-align: center;

  .loading-icon, .error-icon {
    font-size: 60px;
    height: 60px;
    width: 60px;
    margin-bottom: 20px;
    color: $primary-color;
  }

  h2 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: $text-color;
  }

  p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: $dark-gray;
    max-width: 600px;
  }
}

// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Profile Header
.profile-header {
  background-color: $light-gray;
  padding: 40px 0;

  .back-button {
    margin-bottom: 20px;

    mat-icon {
      margin-right: 5px;
    }
  }

  .profile-header-content {
    display: flex;
    gap: 40px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
      text-align: center;
    }

    .profile-image-container {
      flex-shrink: 0;
      width: 250px;
      height: 250px;
      border-radius: 50%;
      overflow: hidden;
      border: 5px solid $white;
      box-shadow: $box-shadow;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .profile-header-info {
      flex-grow: 1;

      h1 {
        font-size: 2.5rem;
        margin: 0 0 10px;
        color: $text-color;
      }

      h2 {
        font-size: 1.5rem;
        margin: 0 0 10px;
        color: $primary-color;
      }

      .graduation-year {
        font-size: 1.2rem;
        margin: 0 0 20px;
        color: $dark-gray;
      }

      .profile-contact {
        margin-bottom: 20px;

        .contact-item {
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }

          mat-icon {
            font-size: 20px;
            height: 20px;
            width: 20px;
            margin-right: 10px;
            color: $primary-color;
          }

          span {
            color: $dark-gray;
          }
        }
      }

      .profile-social {
        display: flex;
        gap: 15px;

        .social-link {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          background-color: $primary-color;
          border-radius: 50%;
          color: $white;
          transition: transform 0.3s, background-color 0.3s;

          &:hover {
            transform: translateY(-3px);
            background-color: color.adjust($primary-color, $lightness: -10%);
          }

          mat-icon {
            font-size: 20px;
            height: 20px;
            width: 20px;
          }
        }
      }
    }
  }
}

// Profile Content
.profile-content {
  padding: 40px 0;

  .tab-content {
    padding: 30px 0;

    h3 {
      font-size: 1.5rem;
      margin: 0 0 20px;
      color: $text-color;
      position: relative;
      padding-bottom: 10px;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 3px;
        background-color: $primary-color;
      }
    }

    .bio-section {
      margin-bottom: 40px;

      p {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 20px;
        color: $text-color;
      }

      .years-since-graduation {
        color: $dark-gray;
        font-size: 1rem;
      }
    }

    .education-section {
      margin-bottom: 40px;

      .education-list {
        list-style-type: none;
        padding: 0;
        margin: 0;

        li {
          position: relative;
          padding-left: 25px;
          margin-bottom: 15px;
          color: $text-color;
          font-size: 1.1rem;

          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 8px;
            width: 10px;
            height: 10px;
            background-color: $primary-color;
            border-radius: 50%;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .testimonial-section {
      .testimonial {
        background-color: $light-gray;
        border-left: 5px solid $primary-color;
        padding: 20px;
        margin: 0;

        p {
          font-size: 1.2rem;
          font-style: italic;
          line-height: 1.6;
          margin-bottom: 10px;
          color: $text-color;
        }

        footer {
          font-size: 1rem;
          color: $dark-gray;
          text-align: right;
        }
      }
    }

    .achievements-list, .contributions-list {
      list-style-type: none;
      padding: 0;
      margin: 0;

      li {
        position: relative;
        padding-left: 25px;
        margin-bottom: 15px;
        color: $text-color;
        font-size: 1.1rem;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 8px;
          width: 10px;
          height: 10px;
          background-color: $primary-color;
          border-radius: 50%;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// Connect Section
.connect-section {
  background-color: $primary-color;
  color: $white;
  padding: 60px 0;
  text-align: center;

  h2 {
    font-size: 2rem;
    margin-bottom: 20px;
  }

  p {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .connect-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;

    @media (max-width: 576px) {
      flex-direction: column;
      align-items: center;

      a, button {
        width: 100%;
        max-width: 250px;
      }
    }

    a, button {
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .profile-header {
    padding: 30px 0;

    .profile-header-content {
      .profile-image-container {
        width: 200px;
        height: 200px;
      }

      .profile-header-info {
        h1 {
          font-size: 2rem;
        }

        h2 {
          font-size: 1.3rem;
        }
      }
    }
  }

  .profile-content {
    padding: 30px 0;
  }
}

@media (max-width: 576px) {
  .profile-header {
    .profile-header-content {
      .profile-image-container {
        width: 150px;
        height: 150px;
      }

      .profile-header-info {
        h1 {
          font-size: 1.8rem;
        }

        h2 {
          font-size: 1.2rem;
        }

        .graduation-year {
          font-size: 1rem;
        }
      }
    }
  }

  .profile-content {
    .tab-content {
      h3 {
        font-size: 1.3rem;
      }

      .bio-section p {
        font-size: 1rem;
      }

      .education-list li, .achievements-list li, .contributions-list li {
        font-size: 1rem;
      }

      .testimonial p {
        font-size: 1.1rem;
      }
    }
  }

  .connect-section {
    padding: 40px 0;

    h2 {
      font-size: 1.8rem;
    }

    p {
      font-size: 1.1rem;
    }
  }
}
