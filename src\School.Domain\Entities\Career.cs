using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class Career : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this career posting belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Responsibilities { get; set; } = string.Empty;
    public string Qualifications { get; set; } = string.Empty;
    public string Experience { get; set; } = string.Empty;
    public string Salary { get; set; } = string.Empty;
    public DateTime ApplicationDeadline { get; set; }
    public DateTime PostedDate { get; set; }
    public CareerStatus Status { get; set; } = CareerStatus.Open;
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string ApplicationUrl { get; set; } = string.Empty;
    public bool IsFeatured { get; set; } = false;
    public int Vacancies { get; set; } = 1;
    public string EmploymentType { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public ICollection<CareerTranslation> Translations { get; set; } = new List<CareerTranslation>();
    public ICollection<CareerApplication> Applications { get; set; } = new List<CareerApplication>();
    public DateTime? UpdatedAt { get; set; }
}
