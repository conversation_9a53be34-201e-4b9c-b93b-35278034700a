using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class TuitionFee : BaseEntity, ITenantEntity
{
    /// <summary>
    /// ID of the organization/tenant this tuition fee belongs to
    /// </summary>
    public Guid TenantId { get; set; }
    public EducationLevel Level { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public FeeType Type { get; set; }
    public FeeFrequency Frequency { get; set; }
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }

    // Navigation properties
    public ICollection<TuitionFeeTranslation> Translations { get; set; } = new List<TuitionFeeTranslation>();
}
