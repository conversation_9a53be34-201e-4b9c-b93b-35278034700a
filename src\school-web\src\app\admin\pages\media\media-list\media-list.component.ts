import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { MediaService } from '../../../../core/services/media.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-media-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    MatGridListModule,
    MatChipsModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './media-list.component.html',
  styleUrls: ['./media-list.component.scss']
})
export class MediaListComponent implements OnInit {
  mediaItems: any[] = [];
  filterForm!: FormGroup;
  isLoading = true;
  error: string | null = null;
  totalCount = 0;
  pageSize = 12;
  pageIndex = 0;
  selectedMedia: any = null;

  constructor(
    private mediaService: MediaService,
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private translate: TranslateService
  ) { }

  ngOnInit(): void {
    this.filterForm = this.formBuilder.group({
      type: [''],
      search: ['']
    });

    this.loadMediaItems();
  }

  loadMediaItems(page: number = 1): void {
    this.isLoading = true;
    this.error = null;

    const filters = {
      ...this.filterForm.value,
      page,
      pageSize: this.pageSize
    };

    // Remove empty filters
    Object.keys(filters).forEach(key => {
      if (filters[key] === '' || filters[key] === null) {
        delete filters[key];
      }
    });

    this.mediaService.getAllMedia(filters).subscribe({
      next: (response) => {
        this.mediaItems = response.items;
        this.totalCount = response.totalCount;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading media items:', error);
        this.error = 'Failed to load media items. Please try again.';
        this.isLoading = false;
        this.snackBar.open(
          this.translate.instant('admin.media.loadError'),
          this.translate.instant('common.close'),
          { duration: 5000 }
        );
      }
    });
  }

  applyFilter(): void {
    this.pageIndex = 0;
    this.loadMediaItems(1);
  }

  resetFilter(): void {
    this.filterForm.reset({
      type: '',
      search: ''
    });
    this.applyFilter();
  }

  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadMediaItems(event.pageIndex + 1);
  }

  selectMedia(media: any): void {
    this.selectedMedia = media;
  }

  deleteMedia(id: number): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: this.translate.instant('admin.media.deleteConfirmTitle'),
        message: this.translate.instant('admin.media.deleteConfirmMessage'),
        confirmText: this.translate.instant('common.delete'),
        cancelText: this.translate.instant('common.cancel')
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.mediaService.deleteMedia(id).subscribe({
          next: () => {
            this.snackBar.open(
              this.translate.instant('admin.media.deleteSuccess'),
              this.translate.instant('common.close'),
              { duration: 3000 }
            );

            if (this.selectedMedia && this.selectedMedia.id === id) {
              this.selectedMedia = null;
            }

            this.loadMediaItems(this.pageIndex + 1);
          },
          error: (error) => {
            console.error('Error deleting media:', error);
            this.snackBar.open(
              this.translate.instant('admin.media.deleteError'),
              this.translate.instant('common.close'),
              { duration: 5000 }
            );
          }
        });
      }
    });
  }

  getMediaTypeLabel(type: number): string {
    switch (type) {
      case 0: return this.translate.instant('admin.media.types.image');
      case 1: return this.translate.instant('admin.media.types.document');
      case 2: return this.translate.instant('admin.media.types.video');
      case 3: return this.translate.instant('admin.media.types.audio');
      default: return this.translate.instant('admin.media.types.other');
    }
  }

  getMediaTypeIcon(type: number): string {
    switch (type) {
      case 0: return 'image';
      case 1: return 'description';
      case 2: return 'videocam';
      case 3: return 'audiotrack';
      default: return 'insert_drive_file';
    }
  }

  getFileSize(size: number): string {
    if (size < 1024) {
      return `${size} B`;
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(2)} KB`;
    } else if (size < 1024 * 1024 * 1024) {
      return `${(size / (1024 * 1024)).toFixed(2)} MB`;
    } else {
      return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
    }
  }

  copyToClipboard(text: string): void {
    navigator.clipboard.writeText(text).then(() => {
      this.snackBar.open(
        this.translate.instant('admin.media.copiedToClipboard'),
        this.translate.instant('common.close'),
        { duration: 2000 }
      );
    });
  }
}
