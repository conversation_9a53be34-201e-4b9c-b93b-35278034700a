<div class="academic-year-list-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">Academic Years</h1>
      <p class="page-subtitle">Manage academic years and their terms</p>
    </div>
    <div class="header-actions">
      <button mat-raised-button color="primary" routerLink="/admin/academic-years/create">
        <mat-icon>add</mat-icon>
        Create Academic Year
      </button>
    </div>
  </div>

  <!-- Filters -->
  <mat-card class="filter-card">
    <mat-card-header>
      <mat-card-title>Filters</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="filterForm" class="filter-form">
        <div class="filter-row">
          <mat-form-field appearance="outline">
            <mat-label>Name</mat-label>
            <input matInput formControlName="name" placeholder="Search by name">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Code</mat-label>
            <input matInput formControlName="code" placeholder="Search by code">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Status</mat-label>
            <mat-select formControlName="status">
              <mat-option value="">All Statuses</mat-option>
              <mat-option *ngFor="let status of statusOptions" [value]="status.value">
                {{status.label}}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Current Year</mat-label>
            <mat-select formControlName="isCurrentYear">
              <mat-option value="">All</mat-option>
              <mat-option value="true">Current Year</mat-option>
              <mat-option value="false">Not Current</mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="filter-row">
          <mat-form-field appearance="outline">
            <mat-label>Start Date From</mat-label>
            <input matInput [matDatepicker]="startFromPicker" formControlName="startDateFrom">
            <mat-datepicker-toggle matSuffix [for]="startFromPicker"></mat-datepicker-toggle>
            <mat-datepicker #startFromPicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Start Date To</mat-label>
            <input matInput [matDatepicker]="startToPicker" formControlName="startDateTo">
            <mat-datepicker-toggle matSuffix [for]="startToPicker"></mat-datepicker-toggle>
            <mat-datepicker #startToPicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>End Date From</mat-label>
            <input matInput [matDatepicker]="endFromPicker" formControlName="endDateFrom">
            <mat-datepicker-toggle matSuffix [for]="endFromPicker"></mat-datepicker-toggle>
            <mat-datepicker #endFromPicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>End Date To</mat-label>
            <input matInput [matDatepicker]="endToPicker" formControlName="endDateTo">
            <mat-datepicker-toggle matSuffix [for]="endToPicker"></mat-datepicker-toggle>
            <mat-datepicker #endToPicker></mat-datepicker>
          </mat-form-field>
        </div>

        <div class="filter-actions">
          <button mat-button type="button" (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            Clear Filters
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Data Table -->
  <mat-card class="table-card">
    <mat-card-content>
      <div class="table-container">
        <table mat-table [dataSource]="dataSource" matSort class="academic-year-table">
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
            <td mat-cell *matCellDef="let academicYear">
              <div class="name-cell">
                <strong>{{academicYear.displayName}}</strong>
                <small>{{academicYear.name}}</small>
              </div>
            </td>
          </ng-container>

          <!-- Code Column -->
          <ng-container matColumnDef="code">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Code</th>
            <td mat-cell *matCellDef="let academicYear">
              <code>{{academicYear.code}}</code>
            </td>
          </ng-container>

          <!-- Start Date Column -->
          <ng-container matColumnDef="startDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Start Date</th>
            <td mat-cell *matCellDef="let academicYear">
              {{academicYear.startDate | date:'mediumDate'}}
            </td>
          </ng-container>

          <!-- End Date Column -->
          <ng-container matColumnDef="endDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>End Date</th>
            <td mat-cell *matCellDef="let academicYear">
              {{academicYear.endDate | date:'mediumDate'}}
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let academicYear">
              <mat-chip [color]="getStatusColor(academicYear.status)" selected>
                {{getStatusText(academicYear.status)}}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Current Year Column -->
          <ng-container matColumnDef="isCurrentYear">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Current</th>
            <td mat-cell *matCellDef="let academicYear">
              <mat-icon *ngIf="academicYear.isCurrentYear" color="primary" matTooltip="Current Academic Year">
                star
              </mat-icon>
              <span *ngIf="!academicYear.isCurrentYear" class="text-muted">-</span>
            </td>
          </ng-container>

          <!-- Total Terms Column -->
          <ng-container matColumnDef="totalTerms">
            <th mat-header-cell *matHeaderCellDef>Terms</th>
            <td mat-cell *matCellDef="let academicYear">
              <mat-chip color="accent" selected>
                {{academicYear.terms?.length || 0}}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let academicYear">
              <div class="action-buttons">
                <button mat-icon-button 
                        [routerLink]="['/admin/academic-years', academicYear.id]"
                        matTooltip="View Details">
                  <mat-icon>visibility</mat-icon>
                </button>

                <button mat-icon-button 
                        [routerLink]="['/admin/academic-years/edit', academicYear.id]"
                        matTooltip="Edit">
                  <mat-icon>edit</mat-icon>
                </button>

                <button mat-icon-button 
                        *ngIf="!academicYear.isCurrentYear"
                        (click)="setCurrentYear(academicYear)"
                        matTooltip="Set as Current Year">
                  <mat-icon>star_border</mat-icon>
                </button>

                <button mat-icon-button 
                        *ngIf="academicYear.status === 0"
                        (click)="activateAcademicYear(academicYear)"
                        matTooltip="Activate">
                  <mat-icon>play_arrow</mat-icon>
                </button>

                <button mat-icon-button 
                        *ngIf="academicYear.status === 1"
                        (click)="completeAcademicYear(academicYear)"
                        matTooltip="Complete">
                  <mat-icon>check_circle</mat-icon>
                </button>

                <button mat-icon-button 
                        color="warn"
                        (click)="deleteAcademicYear(academicYear)"
                        matTooltip="Delete">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- Loading Spinner -->
        <div *ngIf="loading" class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
        </div>

        <!-- No Data Message -->
        <div *ngIf="!loading && dataSource.data.length === 0" class="no-data">
          <mat-icon>school</mat-icon>
          <h3>No Academic Years Found</h3>
          <p>Create your first academic year to get started.</p>
          <button mat-raised-button color="primary" routerLink="/admin/academic-years/create">
            Create Academic Year
          </button>
        </div>
      </div>

      <!-- Paginator -->
      <mat-paginator 
        [length]="totalCount"
        [pageSize]="currentFilter.pageSize"
        [pageSizeOptions]="[5, 10, 25, 50]"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
