.dialog-container {
  max-width: 600px;
  width: 100%;

  h2[mat-dialog-title] {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 16px 0;
    color: #1976d2;
    font-weight: 500;

    mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
  }
}

.assignment-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;

  .form-section {
    .section-title {
      font-size: 1.1rem;
      font-weight: 500;
      color: #333;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #e0e0e0;
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      .form-field {
        flex: 1;
      }

      .primary-toggle {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 4px;
        min-width: 200px;

        .toggle-hint {
          color: #666;
          font-size: 0.75rem;
          margin-left: 4px;
        }
      }
    }

    .full-width {
      width: 100%;
    }
  }
}

mat-dialog-actions {
  padding: 16px 0 0 0;
  margin: 0;

  button {
    margin-left: 8px;

    .button-spinner {
      margin-right: 8px;
    }
  }
}

// Form field customizations
mat-form-field {
  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
  }

  .mat-mdc-form-field-hint-wrapper {
    margin-top: 4px;
  }

  &.mat-focused {
    .mat-mdc-form-field-outline-thick {
      color: #1976d2;
    }
  }

  &.mat-form-field-invalid {
    .mat-mdc-form-field-outline-thick {
      color: #f44336;
    }
  }
}

// Select dropdown customization
mat-select {
  .mat-mdc-select-trigger {
    min-height: 56px;
  }
}

// Assigned indicator styling
.assigned-indicator {
  color: #f44336;
  font-size: 0.75rem;
  font-style: italic;
}

// Textarea customization
textarea {
  resize: vertical;
  min-height: 60px;
}

// Slide toggle customization
mat-slide-toggle {
  .mat-mdc-slide-toggle-bar {
    height: 20px;
    border-radius: 10px;
  }

  .mat-mdc-slide-toggle-thumb {
    width: 16px;
    height: 16px;
  }
}

// Date picker customization
mat-datepicker-toggle {
  .mat-mdc-icon-button {
    width: 40px;
    height: 40px;
  }
}

// Responsive design
@media (max-width: 600px) {
  .dialog-container {
    max-width: 100%;
    margin: 0;
  }

  .assignment-form {
    .form-section {
      .form-row {
        flex-direction: column;
        gap: 8px;

        .primary-toggle {
          min-width: auto;
        }
      }
    }
  }

  mat-dialog-actions {
    flex-direction: column-reverse;
    gap: 8px;

    button {
      margin: 0;
      width: 100%;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .dialog-container {
    h2[mat-dialog-title] {
      color: #90caf9;
    }
  }

  .assignment-form {
    .form-section {
      .section-title {
        color: #fff;
        border-bottom-color: #424242;
      }

      .primary-toggle {
        .toggle-hint {
          color: #ccc;
        }
      }
    }
  }

  .assigned-indicator {
    color: #ff5722;
  }
}

// Animation for form sections
.form-section {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Custom scrollbar for form
.assignment-form::-webkit-scrollbar {
  width: 6px;
}

.assignment-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.assignment-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.assignment-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// Button loading state
button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-spinner {
  display: inline-block;
  vertical-align: middle;
}

// Validation styling
.mat-mdc-form-field-error {
  font-size: 0.75rem;
  margin-top: 4px;
}

// Hint styling
.mat-mdc-form-field-hint {
  font-size: 0.75rem;
  color: #666;
}

// Focus trap for accessibility
.dialog-container {
  outline: none;
}

// High contrast mode support
@media (prefers-contrast: high) {
  .assignment-form {
    .form-section {
      .section-title {
        border-bottom-width: 3px;
        border-bottom-color: #000;
      }
    }
  }

  mat-form-field {
    .mat-mdc-form-field-outline {
      border-width: 2px;
    }
  }

  .assigned-indicator {
    font-weight: bold;
  }
}

// Loading state for select options
mat-option[disabled] {
  opacity: 0.5;
  
  .assigned-indicator {
    font-weight: 500;
  }
}
