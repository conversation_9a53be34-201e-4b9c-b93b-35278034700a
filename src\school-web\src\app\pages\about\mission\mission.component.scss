@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section
.hero-section {
  // Global hero styles are applied from _hero.scss
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('/assets/images/mission-hero.jpg');
  margin-bottom: 2rem;

  .hero-content {
    max-width: 800px;
    padding: 0 20px;

    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    .hero-description {
      font-size: 1.5rem;
      font-weight: 300;
    }
  }
}

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Section Styles
section {
  padding: $section-padding;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: $dark-gray;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Mission Statement Section
.mission-statement-section {
  background-color: $white;

  .mission-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    .mission-box {
      background-color: $light-gray;
      border-radius: $border-radius;
      padding: 40px;
      margin-top: 30px;
      box-shadow: $box-shadow;
      border-left: 5px solid $primary-color;

      .mission-text {
        font-size: 1.5rem;
        line-height: 1.6;
        color: $text-color;
        font-style: italic;
        margin: 0;
      }
    }
  }
}

// Vision Statement Section
.vision-statement-section {
  background-color: $light-gray;

  .vision-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    .vision-box {
      background-color: $white;
      border-radius: $border-radius;
      padding: 40px;
      margin-top: 30px;
      box-shadow: $box-shadow;
      border-left: 5px solid $accent-color;

      .vision-text {
        font-size: 1.5rem;
        line-height: 1.6;
        color: $text-color;
        font-style: italic;
        margin: 0;
      }
    }
  }
}

// Core Values Section
.core-values-section {
  background-color: $white;

  .values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .value-card {
      border-radius: $border-radius;
      padding: 30px;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .value-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        background-color: $primary-color;
        border-radius: 50%;
        margin-bottom: 20px;

        mat-icon {
          font-size: 40px;
          height: 40px;
          width: 40px;
          color: $white;
        }
      }

      mat-card-content {
        flex-grow: 1;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 15px;
          color: $text-color;
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }
}

// Strategic Goals Section
.strategic-goals-section {
  background-color: $light-gray;

  .goals-container {
    margin-top: 40px;

    .goal-item {
      display: flex;
      align-items: flex-start;
      background-color: $white;
      border-radius: $border-radius;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .goal-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background-color: $primary-color;
        color: $white;
        border-radius: 50%;
        font-size: 1.8rem;
        font-weight: bold;
        margin-right: 30px;
        flex-shrink: 0;
      }

      .goal-content {
        flex-grow: 1;

        .goal-icon {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 50px;
          height: 50px;
          background-color: $light-gray;
          border-radius: 50%;
          margin-bottom: 15px;

          mat-icon {
            font-size: 25px;
            height: 25px;
            width: 25px;
            color: $primary-color;
          }
        }

        h3 {
          font-size: 1.5rem;
          margin-bottom: 10px;
          color: $text-color;
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }
}

// Philosophy Section
.philosophy-section {
  background-color: $white;

  .philosophy-content {
    max-width: 800px;
    margin: 0 auto;

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Call to Action Section
.cta-section {
  background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -15%));
  color: $white;

  .cta-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h2 {
      color: $white;

      &:after {
        background-color: $white;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
    }

    .cta-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;

      a {
        padding: 10px 30px;
        font-size: 1.1rem;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .hero-section {
    // Height is controlled by global hero styles

    .hero-content h1 {
      font-size: 2.5rem;
    }
  }

  section {
    padding: 60px 0;

    h2 {
      font-size: 2rem;
    }
  }

  .mission-statement-section, .vision-statement-section {
    .mission-box, .vision-box {
      padding: 30px;

      .mission-text, .vision-text {
        font-size: 1.3rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .hero-section {
    // Height is controlled by global hero styles

    .hero-content {
      h1 {
        font-size: 2rem;
      }

      .hero-description {
        font-size: 1.2rem;
      }
    }
  }

  .goal-item {
    flex-direction: column;

    .goal-number {
      margin-right: 0;
      margin-bottom: 20px;
    }
  }

  .cta-section {
    .cta-buttons {
      flex-direction: column;
      align-items: center;

      a {
        width: 100%;
        max-width: 300px;
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .hero-section {
    // Height is controlled by global hero styles

    .hero-content h1 {
      font-size: 1.8rem;
    }
  }

  section h2 {
    font-size: 1.8rem;
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .mission-statement-section, .vision-statement-section {
    .mission-box, .vision-box {
      padding: 20px;

      .mission-text, .vision-text {
        font-size: 1.2rem;
      }
    }
  }
}
