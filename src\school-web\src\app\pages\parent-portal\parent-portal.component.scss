@use '../../../styles/portal-theme' as portal;

// Apply consistent portal theme
:host {
  @include portal.apply-portal-theme;
}

.parent-portal-container {
  @include portal.portal-base-layout;
  flex-direction: column;
  height: 100vh;
}

.portal-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2;

  .header-switcher {
    margin-right: 16px;

    ::ng-deep .language-theme-switcher {
      .switcher-btn {
        color: rgba(255, 255, 255, 0.9);

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          color: white;
        }
      }
    }
  }
}

.spacer {
  flex: 1 1 auto;
}

.sidenav-container {
  flex: 1;
  margin-top: 64px; // Height of the toolbar
}

.sidenav {
  width: 250px;
  padding-top: 16px;
}

.parent-info {
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  margin-bottom: 16px;
}

.parent-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  margin-bottom: 16px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  mat-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #757575;
  }
}

.parent-details {
  text-align: center;

  h3 {
    margin: 0 0 8px 0;
    font-weight: 500;
  }

  p {
    margin: 0 0 4px 0;
    color: rgba(0, 0, 0, 0.6);
    font-size: 14px;
  }
}

.content {
  padding: 24px;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.router-container {
  height: 100%;
}

.active-link {
  background-color: rgba(0, 0, 0, 0.04);
  color: #3f51b5;
  font-weight: 500;
}
