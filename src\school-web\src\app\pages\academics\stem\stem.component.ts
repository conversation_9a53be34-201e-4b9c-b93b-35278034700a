import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../../shared/components/default-hero/default-hero.component';

interface Program {
  title: string;
  description: string;
  icon: string;
}

interface Course {
  name: string;
  description: string;
  level: string;
}

@Component({
  selector: 'app-stem',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatDividerModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './stem.component.html',
  styleUrls: ['./stem.component.scss']
})
export class StemComponent {
  // Key features of the STEM program
  keyFeatures: Program[] = [
    {
      title: 'Integrated Curriculum',
      description: 'Our STEM curriculum integrates science, technology, engineering, and mathematics to help students understand how these disciplines work together to solve real-world problems.',
      icon: 'hub'
    },
    {
      title: 'Hands-on Learning',
      description: 'Students engage in hands-on experiments, design challenges, and project-based learning that encourage exploration, critical thinking, and problem-solving.',
      icon: 'engineering'
    },
    {
      title: 'Advanced Technology',
      description: 'Our state-of-the-art labs and makerspaces are equipped with the latest technology, including 3D printers, robotics kits, coding platforms, and scientific instruments.',
      icon: 'precision_manufacturing'
    },
    {
      title: 'Expert Faculty',
      description: 'Our STEM teachers bring industry experience and academic expertise to the classroom, providing students with real-world insights and mentorship.',
      icon: 'school'
    },
    {
      title: 'Competitions & Challenges',
      description: 'Students participate in regional and national STEM competitions, hackathons, and challenges that foster teamwork, innovation, and excellence.',
      icon: 'emoji_events'
    },
    {
      title: 'Industry Partnerships',
      description: 'Collaborations with local businesses, research institutions, and universities provide students with mentorship opportunities, internships, and real-world applications of STEM concepts.',
      icon: 'handshake'
    }
  ];

  // STEM programs by level
  stemPrograms = [
    {
      level: 'Elementary School',
      description: 'Our elementary STEM program introduces young learners to scientific inquiry, computational thinking, and engineering design through playful, hands-on activities that spark curiosity and build foundational skills.',
      features: [
        'STEM Discovery Labs',
        'Coding Fundamentals',
        'Junior Engineering Challenges',
        'Math Olympiad',
        'Science Exploration Kits',
        'Nature Studies'
      ]
    },
    {
      level: 'Middle School',
      description: 'Middle school students deepen their STEM knowledge and skills through more complex projects, interdisciplinary connections, and collaborative problem-solving that prepares them for advanced study.',
      features: [
        'Robotics Club',
        'App Development',
        'Environmental Science Projects',
        'Engineering Design Challenges',
        'Math Competition Team',
        'Science Olympiad'
      ]
    },
    {
      level: 'High School',
      description: 'Our high school STEM program offers advanced coursework, research opportunities, and specialized tracks that prepare students for college majors and careers in STEM fields.',
      features: [
        'Advanced Placement STEM Courses',
        'Research Methods & Independent Projects',
        'Internship Program',
        'Robotics Team',
        'Hackathons & Coding Competitions',
        'STEM Mentorship Program'
      ]
    }
  ];

  // Sample courses
  sampleCourses: Course[] = [
    {
      name: 'Introduction to Robotics',
      description: 'Students learn the fundamentals of robotics, including mechanical design, electronics, and programming, while building and programming robots to complete specific tasks.',
      level: 'Middle School'
    },
    {
      name: 'AP Computer Science Principles',
      description: 'This course introduces students to the foundational concepts of computer science and challenges them to explore how computing and technology impact the world.',
      level: 'High School'
    },
    {
      name: 'Environmental Engineering',
      description: 'Students apply engineering principles to environmental challenges, designing solutions for water purification, renewable energy, waste management, and sustainable development.',
      level: 'High School'
    },
    {
      name: 'Coding & Game Design',
      description: 'This course teaches programming fundamentals through game design, allowing students to create their own interactive games while learning computational thinking.',
      level: 'Middle School'
    },
    {
      name: 'STEM Explorations',
      description: 'Young learners engage in hands-on activities that introduce basic concepts in science, technology, engineering, and mathematics through playful exploration.',
      level: 'Elementary School'
    },
    {
      name: 'Biomedical Science',
      description: 'Students explore concepts of human medicine, physiology, genetics, microbiology, and public health through hands-on projects and problems.',
      level: 'High School'
    }
  ];

  // STEM facilities
  stemFacilities = [
    {
      title: 'Innovation Lab',
      description: 'A flexible space equipped with 3D printers, laser cutters, electronics workstations, and design software where students can prototype and build their ideas.',
      image: 'assets/images/academics/stem-innovation-lab.jpg'
    },
    {
      title: 'Robotics Workshop',
      description: 'Dedicated space for robotics design, construction, and testing, featuring competition-grade equipment, programming stations, and practice fields.',
      image: 'assets/images/academics/stem-robotics.jpg'
    },
    {
      title: 'Science Laboratories',
      description: 'State-of-the-art labs for biology, chemistry, and physics with advanced scientific instruments and safety equipment for conducting sophisticated experiments.',
      image: 'assets/images/academics/stem-lab.jpg'
    },
    {
      title: 'Computer Science Studio',
      description: 'Modern computing facility with powerful workstations, specialized software, and collaborative spaces for coding, app development, and digital projects.',
      image: 'assets/images/academics/stem-cs.jpg'
    }
  ];

  // STEM achievements
  stemAchievements = [
    {
      title: 'Regional Robotics Champions',
      description: 'Our robotics team won the regional FIRST Robotics Competition and qualified for the national championship.',
      year: '2023'
    },
    {
      title: 'Science Olympiad State Finalists',
      description: 'Six students qualified for the state Science Olympiad competition, with two earning gold medals in their events.',
      year: '2023'
    },
    {
      title: 'App Development Award',
      description: 'A team of high school students developed an environmental monitoring app that won recognition at the National Youth Technology Challenge.',
      year: '2022'
    },
    {
      title: 'Mathematics Competition',
      description: 'Our math team placed in the top 10 at the state mathematics competition, with three students receiving individual honors.',
      year: '2022'
    },
    {
      title: 'Research Publication',
      description: 'Two senior students had their research on water quality analysis published in a peer-reviewed scientific journal.',
      year: '2021'
    },
    {
      title: 'Engineering Design Challenge',
      description: 'Middle school students won the regional Engineering Design Challenge with their innovative solution for sustainable urban transportation.',
      year: '2021'
    }
  ];
}
