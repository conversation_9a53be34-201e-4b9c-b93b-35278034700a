using AutoMapper;
using School.Application.Common.Interfaces;
using School.Application.Common.Models;
using School.Application.DTOs;
using School.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace School.Application.Features.Clubs;

public class ClubService : IClubService
{
    private readonly IClubRepository _clubRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ICurrentUserService _currentUserService;

    public ClubService(
        IUnitOfWork unitOfWork,
        IClubRepository clubRepository,
        IMapper mapper,
        ICurrentUserService currentUserService)
    {
        _unitOfWork = unitOfWork;
        _clubRepository = clubRepository;
        _mapper = mapper;
        _currentUserService = currentUserService;
    }

    public async Task<ApiResult<PagedList<ClubDto>>> GetClubsAsync(ClubFilterDto filter, CancellationToken cancellationToken = default)
    {
        try
        {
            var pagedClubs = await _clubRepository.GetClubsAsync(filter, cancellationToken);

            // Map to DTOs
            var clubDtos = _mapper.Map<List<ClubDto>>(pagedClubs.Items);

            // Create paged list
            var pagedList = new PagedList<ClubDto>(
                clubDtos,
                pagedClubs.TotalCount,
                pagedClubs.CurrentPage,
                pagedClubs.PageSize);

            return ApiResult<PagedList<ClubDto>>.SuccessResult(pagedList);
        }
        catch (Exception ex)
        {
            return ApiResult<PagedList<ClubDto>>.FailureResult(new[] { ex.Message });
        }
    }

    public async Task<ApiResult<ClubDto>> GetClubByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var club = await _clubRepository.GetClubByIdWithDetailsAsync(id, cancellationToken);

            if (club == null)
            {
                return ApiResult<ClubDto>.FailureResult(new[] { "Club not found" });
            }

            var clubDto = _mapper.Map<ClubDto>(club);
            return ApiResult<ClubDto>.SuccessResult(clubDto);
        }
        catch (Exception ex)
        {
            return ApiResult<ClubDto>.FailureResult(new[] { ex.Message });
        }
    }

    public async Task<ApiResult<ClubDto>> CreateClubAsync(ClubCreateDto clubDto, CancellationToken cancellationToken = default)
    {
        try
        {
            var club = _mapper.Map<Club>(clubDto);

            await _clubRepository.AddAsync(club,cancellationToken: cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Reload the club with all related entities
            var createdClub = await _clubRepository.GetClubByIdWithDetailsAsync(club.Id, cancellationToken);

            var result = _mapper.Map<ClubDto>(createdClub);
            return ApiResult<ClubDto>.SuccessResult(result);
        }
        catch (Exception ex)
        {
            return ApiResult<ClubDto>.FailureResult(new[] { ex.Message });
        }
    }

    public async Task<ApiResult<ClubDto>> UpdateClubAsync(Guid id, ClubUpdateDto clubDto, CancellationToken cancellationToken = default)
    {
        try
        {
            var club = await _clubRepository.GetClubByIdWithDetailsAsync(id, cancellationToken);

            if (club == null)
            {
                return ApiResult<ClubDto>.FailureResult(new[] { "Club not found" });
            }

            // Update basic properties
            if (!string.IsNullOrEmpty(clubDto.Name))
                club.Name = clubDto.Name;

            if (clubDto.Description != null)
                club.Description = clubDto.Description;

            if (clubDto.ShortDescription != null)
                club.ShortDescription = clubDto.ShortDescription;

            if (!string.IsNullOrEmpty(clubDto.Category))
                club.Category = clubDto.Category;

            if (clubDto.MeetingSchedule != null)
                club.MeetingSchedule = clubDto.MeetingSchedule;

            if (clubDto.Location != null)
                club.Location = clubDto.Location;

            if (clubDto.Requirements != null)
                club.Requirements = clubDto.Requirements;

            if (clubDto.JoinProcess != null)
                club.JoinProcess = clubDto.JoinProcess;

            if (clubDto.ContactEmail != null)
                club.ContactEmail = clubDto.ContactEmail;

            if (clubDto.Website != null)
                club.Website = clubDto.Website;

            if (clubDto.Instagram != null)
                club.Instagram = clubDto.Instagram;

            if (clubDto.Facebook != null)
                club.Facebook = clubDto.Facebook;

            if (clubDto.IsFeatured.HasValue)
                club.IsFeatured = clubDto.IsFeatured.Value;

            if (clubDto.DisplayOrder.HasValue)
                club.DisplayOrder = clubDto.DisplayOrder.Value;

            if (clubDto.IsActive.HasValue)
                club.IsActive = clubDto.IsActive.Value;

            if (clubDto.ProfileImageId.HasValue)
                club.ProfileImageId = clubDto.ProfileImageId;

            if (clubDto.ProfileImageUrl != null)
                club.ProfileImageUrl = clubDto.ProfileImageUrl;

            // Update translations
            if (clubDto.Translations != null && clubDto.Translations.Any())
            {
                foreach (var translationDto in clubDto.Translations)
                {
                    var translation = club.Translations.FirstOrDefault(t => t.LanguageCode == translationDto.LanguageCode);

                    if (translation != null)
                    {
                        // Update existing translation
                        if (translationDto.Name != null)
                            translation.Name = translationDto.Name;

                        if (translationDto.Description != null)
                            translation.Description = translationDto.Description;

                        if (translationDto.ShortDescription != null)
                            translation.ShortDescription = translationDto.ShortDescription;

                        if (translationDto.Requirements != null)
                            translation.Requirements = translationDto.Requirements;

                        if (translationDto.JoinProcess != null)
                            translation.JoinProcess = translationDto.JoinProcess;
                    }
                    else
                    {
                        // Add new translation
                        club.Translations.Add(new ClubTranslation
                        {
                            LanguageCode = translationDto.LanguageCode,
                            Name = translationDto.Name ?? club.Name,
                            Description = translationDto.Description ?? club.Description,
                            ShortDescription = translationDto.ShortDescription ?? club.ShortDescription,
                            Requirements = translationDto.Requirements ?? club.Requirements,
                            JoinProcess = translationDto.JoinProcess ?? club.JoinProcess
                        });
                    }
                }
            }

            // Update advisors, leaders, activities, achievements, events, and gallery items
            // (This would be similar to the translations update but more complex)
            // For brevity, we're not implementing all of these updates here

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Reload the club with all related entities
            var updatedClub = await _clubRepository.GetClubByIdWithDetailsAsync(id, cancellationToken);

            var result = _mapper.Map<ClubDto>(updatedClub);
            return ApiResult<ClubDto>.SuccessResult(result);
        }
        catch (Exception ex)
        {
            return ApiResult<ClubDto>.FailureResult(new[] { ex.Message });
        }
    }

    public async Task<ApiResult<bool>> DeleteClubAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var club = await _clubRepository.GetByIdAsync(id, cancellationToken);

            if (club == null)
            {
                return ApiResult<bool>.FailureResult(new[] { "Club not found" });
            }

            await _clubRepository.DeleteAsync(club,softDelete: true, cancellationToken: cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            return ApiResult<bool>.SuccessResult(true);
        }
        catch (Exception ex)
        {
            return ApiResult<bool>.FailureResult(new[] { ex.Message });
        }
    }

    public async Task<ApiResult<PagedList<ClubDto>>> GetFeaturedClubsAsync(int page = 1, int pageSize = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            var pagedClubs = await _clubRepository.GetFeaturedClubsAsync(page, pageSize, cancellationToken);

            // Map to DTOs
            var clubDtos = _mapper.Map<List<ClubDto>>(pagedClubs.Items);

            // Create paged list
            var pagedList = new PagedList<ClubDto>(
                clubDtos,
                pagedClubs.TotalCount,
                pagedClubs.CurrentPage,
                pagedClubs.PageSize);

            return ApiResult<PagedList<ClubDto>>.SuccessResult(pagedList);
        }
        catch (Exception ex)
        {
            return ApiResult<PagedList<ClubDto>>.FailureResult(new[] { ex.Message });
        }
    }

    public async Task<ApiResult<List<string>>> GetClubCategoriesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var categories = await _clubRepository.GetClubCategoriesAsync(cancellationToken);
            return ApiResult<List<string>>.SuccessResult(categories);
        }
        catch (Exception ex)
        {
            return ApiResult<List<string>>.FailureResult(new[] { ex.Message });
        }
    }
}
