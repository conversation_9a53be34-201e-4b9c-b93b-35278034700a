using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

public class AcademicCalendar : BaseEntity
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsAllDay { get; set; }
    public string Location { get; set; } = string.Empty;
    public AcademicCalendarType Type { get; set; }
    public string Color { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public bool IsPublic { get; set; } = true;

    // Foreign key relationships
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }

    // Navigation properties
    public AcademicYear? AcademicYear { get; set; }
    public Term? Term { get; set; }
    public ICollection<AcademicCalendarTranslation> Translations { get; set; } = new List<AcademicCalendarTranslation>();
}
