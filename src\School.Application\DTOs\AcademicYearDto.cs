using School.Domain.Enums;

namespace School.Application.DTOs;

public class AcademicYearDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public AcademicYearStatus Status { get; set; }
    public bool IsCurrentYear { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public int TotalWorkingDays { get; set; }
    public int TotalHolidays { get; set; }
    public DateTime? RegistrationStartDate { get; set; }
    public DateTime? RegistrationEndDate { get; set; }
    public DateTime? AdmissionStartDate { get; set; }
    public DateTime? AdmissionEndDate { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public List<TermDto> Terms { get; set; } = new List<TermDto>();
    public List<AcademicYearTranslationDto> Translations { get; set; } = new List<AcademicYearTranslationDto>();
}

public class CreateAcademicYearDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public AcademicYearStatus Status { get; set; } = AcademicYearStatus.Draft;
    public bool IsCurrentYear { get; set; } = false;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public int TotalWorkingDays { get; set; }
    public int TotalHolidays { get; set; }
    public DateTime? RegistrationStartDate { get; set; }
    public DateTime? RegistrationEndDate { get; set; }
    public DateTime? AdmissionStartDate { get; set; }
    public DateTime? AdmissionEndDate { get; set; }
    public List<CreateTermDto>? Terms { get; set; }
    public List<CreateAcademicYearTranslationDto>? Translations { get; set; }
}

public class UpdateAcademicYearDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public AcademicYearStatus Status { get; set; }
    public bool IsCurrentYear { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public int TotalWorkingDays { get; set; }
    public int TotalHolidays { get; set; }
    public DateTime? RegistrationStartDate { get; set; }
    public DateTime? RegistrationEndDate { get; set; }
    public DateTime? AdmissionStartDate { get; set; }
    public DateTime? AdmissionEndDate { get; set; }
}

public class AcademicYearFilterDto
{
    public string? Name { get; set; }
    public string? Code { get; set; }
    public AcademicYearStatus? Status { get; set; }
    public bool? IsCurrentYear { get; set; }
    public DateTime? StartDateFrom { get; set; }
    public DateTime? StartDateTo { get; set; }
    public DateTime? EndDateFrom { get; set; }
    public DateTime? EndDateTo { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = false;
}

public class AcademicYearTranslationDto
{
    public Guid Id { get; set; }
    public Guid AcademicYearId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

public class CreateAcademicYearTranslationDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

public class UpdateAcademicYearTranslationDto
{
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}
