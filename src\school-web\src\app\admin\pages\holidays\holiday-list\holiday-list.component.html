<div class="holiday-list-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <mat-icon>event</mat-icon>
        Holidays Management
      </h1>
      <button mat-raised-button color="primary" (click)="createHoliday()">
        <mat-icon>add</mat-icon>
        Add Holiday
      </button>
    </div>
  </div>

  <!-- Filters Card -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-row">
        <!-- Search -->
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search holidays</mat-label>
          <input matInput 
                 [(ngModel)]="searchTerm" 
                 (input)="onSearch($any($event.target).value || '')"
                 placeholder="Search by name...">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <!-- Holiday Type Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Holiday Type</mat-label>
          <mat-select [(ngModel)]="filter.type" (selectionChange)="onFilterChange()">
            <mat-option [value]="undefined">All Types</mat-option>
            <mat-option *ngFor="let type of holidayTypes" [value]="type.value">
              {{ type.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Academic Year Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Academic Year</mat-label>
          <mat-select [(ngModel)]="filter.academicYearId" 
                     (selectionChange)="onAcademicYearChange($event.value)">
            <mat-option [value]="undefined">All Years</mat-option>
            <mat-option *ngFor="let year of academicYears" [value]="year.id">
              {{ year.displayName }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Term Filter -->
        <mat-form-field appearance="outline">
          <mat-label>Term</mat-label>
          <mat-select [(ngModel)]="filter.termId" 
                     (selectionChange)="onTermChange($event.value)"
                     [disabled]="!filter.academicYearId">
            <mat-option [value]="undefined">All Terms</mat-option>
            <mat-option *ngFor="let term of terms" [value]="term.id">
              {{ term.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Status Filters -->
        <div class="status-filters">
          <mat-slide-toggle [(ngModel)]="filter.isActive" 
                           (change)="onFilterChange()"
                           [checked]="filter.isActive === true">
            Active Only
          </mat-slide-toggle>
          
          <mat-slide-toggle [(ngModel)]="filter.isPublic" 
                           (change)="onFilterChange()"
                           [checked]="filter.isPublic === true">
            Public Only
          </mat-slide-toggle>
        </div>

        <!-- Clear Filters -->
        <button mat-stroked-button (click)="clearFilters()">
          <mat-icon>clear</mat-icon>
          Clear Filters
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Data Table Card -->
  <mat-card class="table-card">
    <mat-card-content>
      <!-- Loading Spinner -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Loading holidays...</p>
      </div>

      <!-- Data Table -->
      <div *ngIf="!loading" class="table-container">
        <table mat-table [dataSource]="dataSource" matSort 
               (matSortChange)="onSortChange($event)" class="holidays-table">

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
            <td mat-cell *matCellDef="let holiday">
              <div class="holiday-name">
                <div class="color-indicator" [style.background-color]="holiday.color"></div>
                <div class="name-content">
                  <span class="name">{{ holiday.name }}</span>
                  <span *ngIf="holiday.description" class="description">{{ holiday.description }}</span>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Type Column -->
          <ng-container matColumnDef="type">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Type</th>
            <td mat-cell *matCellDef="let holiday">
              <mat-chip [style.background-color]="getHolidayTypeColor(holiday.type)"
                       [style.color]="'white'">
                {{ getHolidayTypeLabel(holiday.type) }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Start Date Column -->
          <ng-container matColumnDef="startDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Start Date</th>
            <td mat-cell *matCellDef="let holiday">
              <div class="date-info">
                <span class="date">{{ formatDate(holiday.startDate) }}</span>
                <mat-chip *ngIf="isUpcoming(holiday.startDate)" 
                         class="upcoming-chip" color="accent">
                  Upcoming
                </mat-chip>
              </div>
            </td>
          </ng-container>

          <!-- End Date Column -->
          <ng-container matColumnDef="endDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>End Date</th>
            <td mat-cell *matCellDef="let holiday">
              <div class="date-info">
                <span class="date">{{ formatDate(holiday.endDate) }}</span>
                <span class="duration">({{ getDuration(holiday.startDate, holiday.endDate) }} days)</span>
              </div>
            </td>
          </ng-container>

          <!-- Academic Year Column -->
          <ng-container matColumnDef="academicYear">
            <th mat-header-cell *matHeaderCellDef>Academic Year</th>
            <td mat-cell *matCellDef="let holiday">
              {{ holiday.academicYearName || 'N/A' }}
            </td>
          </ng-container>

          <!-- Term Column -->
          <ng-container matColumnDef="term">
            <th mat-header-cell *matHeaderCellDef>Term</th>
            <td mat-cell *matCellDef="let holiday">
              {{ holiday.termName || 'N/A' }}
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let holiday">
              <div class="status-indicators">
                <mat-chip *ngIf="holiday.isActive" class="active-chip" color="primary">
                  Active
                </mat-chip>
                <mat-chip *ngIf="!holiday.isActive" class="inactive-chip">
                  Inactive
                </mat-chip>
                <mat-chip *ngIf="holiday.isPublic" class="public-chip">
                  Public
                </mat-chip>
                <mat-chip *ngIf="holiday.isRecurring" class="recurring-chip">
                  Recurring
                </mat-chip>
                <mat-chip *ngIf="isActive(holiday.startDate, holiday.endDate)" 
                         class="current-chip" color="accent">
                  Current
                </mat-chip>
              </div>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let holiday">
              <div class="actions-container">
                <button mat-icon-button [matMenuTriggerFor]="actionMenu" 
                       matTooltip="More actions">
                  <mat-icon>more_vert</mat-icon>
                </button>
                
                <mat-menu #actionMenu="matMenu">
                  <button mat-menu-item (click)="viewHoliday(holiday)">
                    <mat-icon>visibility</mat-icon>
                    <span>View Details</span>
                  </button>
                  
                  <button mat-menu-item (click)="editHoliday(holiday)">
                    <mat-icon>edit</mat-icon>
                    <span>Edit</span>
                  </button>
                  
                  <button mat-menu-item (click)="toggleHolidayStatus(holiday)">
                    <mat-icon>{{ holiday.isActive ? 'visibility_off' : 'visibility' }}</mat-icon>
                    <span>{{ holiday.isActive ? 'Deactivate' : 'Activate' }}</span>
                  </button>
                  
                  <mat-divider></mat-divider>
                  
                  <button mat-menu-item (click)="deleteHoliday(holiday)" class="delete-action">
                    <mat-icon>delete</mat-icon>
                    <span>Delete</span>
                  </button>
                </mat-menu>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No Data Message -->
        <div *ngIf="!loading && holidays.length === 0" class="no-data">
          <mat-icon>event_busy</mat-icon>
          <h3>No holidays found</h3>
          <p>Try adjusting your filters or create a new holiday.</p>
          <button mat-raised-button color="primary" (click)="createHoliday()">
            <mat-icon>add</mat-icon>
            Create Holiday
          </button>
        </div>

        <!-- Paginator -->
        <mat-paginator *ngIf="holidays.length > 0"
                      [length]="totalCount"
                      [pageSize]="filter.pageSize"
                      [pageSizeOptions]="[5, 10, 25, 50, 100]"
                      [pageIndex]="filter.page - 1"
                      (page)="onPageChange($event)"
                      showFirstLastButtons>
        </mat-paginator>
      </div>
    </mat-card-content>
  </mat-card>
</div>
