using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ClubTranslationConfiguration : IEntityTypeConfiguration<ClubTranslation>
{
    public void Configure(EntityTypeBuilder<ClubTranslation> builder)
    {
        builder.HasKey(t => t.Id);
        
        builder.Property(t => t.LanguageCode)
            .IsRequired()
            .HasMaxLength(5);
            
        builder.Property(t => t.Name)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(t => t.Description)
            .IsRequired();
            
        builder.Property(t => t.ShortDescription)
            .HasMaxLength(250);
            
        // Create a unique constraint for ClubId and LanguageCode
        builder.HasIndex(t => new { t.ClubId, t.LanguageCode })
            .IsUnique();
    }
}
