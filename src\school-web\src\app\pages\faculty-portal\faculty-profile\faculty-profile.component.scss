.profile-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.profile-content {
  max-width: 1000px;
  margin: 0 auto;
}

.profile-card {
  margin-bottom: 24px;
}

.profile-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  mat-icon {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #757575;
  }
}

.tab-content {
  padding: 24px 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.info-item {
  display: flex;
  flex-direction: column;

  &.full-width {
    grid-column: span 3;
  }
}

.info-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
}

.chips-container {
  margin-top: 8px;
}

.subjects-list {
  width: 100%;
  overflow-x: auto;
}

.subjects-table {
  width: 100%;
}

.no-data {
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
  padding: 24px 0;
}

mat-card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px 16px;
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .info-item.full-width {
    grid-column: span 1;
  }
}
