using School.Application.Common.Mappings;
using School.Application.DTOs.Common;
using School.Domain.Entities;

namespace School.Application.DTOs;

public class FacultyDto : IMapFrom<Faculty>
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Office { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string ShortBio { get; set; } = string.Empty;
    public int JoinedYear { get; set; }
    public bool IsFeatured { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public string Department { get; set; } = string.Empty;

    // Social links
    public string Website { get; set; } = string.Empty;
    public string LinkedIn { get; set; } = string.Empty;
    public string Twitter { get; set; } = string.Empty;
    public string ResearchGate { get; set; } = string.Empty;

    // Image
    public Guid? ProfileImageId { get; set; }
    public string? ProfileImageUrl { get; set; }

    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Related collections
    public List<FacultyTranslationDto> Translations { get; set; } = new();
    public List<FacultyEducationDto> Education { get; set; } = new();
    public List<FacultySpecializationDto> Specializations { get; set; } = new();
    public List<FacultyCourseDto> Courses { get; set; } = new();
    public List<FacultyPublicationDto> Publications { get; set; } = new();
    public List<FacultyAwardDto> Awards { get; set; } = new();
    // Remove this property as it's causing conflicts
}

public class FacultyTranslationDto : IMapFrom<FacultyTranslation>
{
    public Guid Id { get; set; }
    public Guid FacultyId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string ShortBio { get; set; } = string.Empty;
}

public class FacultyEducationDto : IMapFrom<FacultyEducation>
{
    public Guid Id { get; set; }
    public Guid FacultyId { get; set; }
    public string Degree { get; set; } = string.Empty;
    public string Institution { get; set; } = string.Empty;
    public int? Year { get; set; }
    public int DisplayOrder { get; set; }
    public List<FacultyEducationTranslationDto> Translations { get; set; } = new();
    public string Major { get; set; } = string.Empty;
}

public class FacultyEducationTranslationDto : IMapFrom<FacultyEducationTranslation>
{
    public Guid Id { get; set; }
    public Guid FacultyEducationId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Degree { get; set; } = string.Empty;
    public string Institution { get; set; } = string.Empty;
}

public class FacultySpecializationDto : IMapFrom<FacultySpecialization>
{
    public Guid Id { get; set; }
    public Guid FacultyId { get; set; }
    public string Name { get; set; } = string.Empty;
    public int DisplayOrder { get; set; }
    public List<FacultySpecializationTranslationDto> Translations { get; set; } = new();
    public string Description { get; set; } = string.Empty;
}

public class FacultySpecializationTranslationDto : IMapFrom<FacultySpecializationTranslation>
{
    public Guid Id { get; set; }
    public Guid FacultySpecializationId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
}

public class FacultyCourseDto : IMapFrom<FacultyCourse>
{
    public Guid Id { get; set; }
    public Guid FacultyId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int DisplayOrder { get; set; }
    public string Code { get; set; } = string.Empty;
}

public class FacultyPublicationDto : IMapFrom<FacultyPublication>
{
    public Guid Id { get; set; }
    public Guid FacultyId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Journal { get; set; }
    public int? Year { get; set; }
    public string? Url { get; set; }
    public int DisplayOrder { get; set; }
}

public class FacultyAwardDto : IMapFrom<FacultyAward>
{
    public Guid Id { get; set; }
    public Guid FacultyId { get; set; }
    public string Name { get; set; } = string.Empty;
    public int? Year { get; set; }
    public string? Organization { get; set; }
    public int DisplayOrder { get; set; }
    public string Description { get; set; } = string.Empty;
}

// Create DTOs
public class FacultyCreateDto
{
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Office { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string ShortBio { get; set; } = string.Empty;
    public int JoinedYear { get; set; }
    public bool IsFeatured { get; set; }
    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }
    public string Department { get; set; } = string.Empty;

    // Social links
    public string Website { get; set; } = string.Empty;
    public string LinkedIn { get; set; } = string.Empty;
    public string Twitter { get; set; } = string.Empty;
    public string ResearchGate { get; set; } = string.Empty;

    // Image
    public Guid? ProfileImageId { get; set; }

    // Related collections
    public List<FacultyTranslationCreateDto>? Translations { get; set; }
    public List<FacultyEducationCreateDto>? Education { get; set; }
    public List<FacultySpecializationCreateDto>? Specializations { get; set; }
    public List<FacultyCourseCreateDto>? Courses { get; set; }
    public List<FacultyPublicationCreateDto>? Publications { get; set; }
    public List<FacultyAwardCreateDto>? Awards { get; set; }
    // Remove this property as it's causing conflicts
}

public class FacultyTranslationCreateDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Biography { get; set; } = string.Empty;
    public string ShortBio { get; set; } = string.Empty;
}

public class FacultyEducationCreateDto
{
    public string Degree { get; set; } = string.Empty;
    public string Institution { get; set; } = string.Empty;
    public int? Year { get; set; }
    public int DisplayOrder { get; set; }
    public List<FacultyEducationTranslationCreateDto>? Translations { get; set; }
}

public class FacultyEducationTranslationCreateDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Degree { get; set; } = string.Empty;
    public string Institution { get; set; } = string.Empty;
}

public class FacultySpecializationCreateDto
{
    public string Name { get; set; } = string.Empty;
    public int DisplayOrder { get; set; }
    public List<FacultySpecializationTranslationCreateDto>? Translations { get; set; }
}

public class FacultySpecializationTranslationCreateDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
}

public class FacultyCourseCreateDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int DisplayOrder { get; set; }
}

public class FacultyPublicationCreateDto
{
    public string Title { get; set; } = string.Empty;
    public string? Journal { get; set; }
    public int? Year { get; set; }
    public string? Url { get; set; }
    public int DisplayOrder { get; set; }
}

public class FacultyAwardCreateDto
{
    public string Name { get; set; } = string.Empty;
    public int? Year { get; set; }
    public string? Organization { get; set; }
    public int DisplayOrder { get; set; }
}

// Update DTOs
public class FacultyUpdateDto
{
    public string? Name { get; set; }
    public string? Title { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? Office { get; set; }
    public string? Biography { get; set; }
    public string? ShortBio { get; set; }
    public int? JoinedYear { get; set; }
    public bool? IsFeatured { get; set; }
    public bool? IsActive { get; set; }
    public int? DisplayOrder { get; set; }
    public string? Department { get; set; }

    // Social links
    public string? Website { get; set; }
    public string? LinkedIn { get; set; }
    public string? Twitter { get; set; }
    public string? ResearchGate { get; set; }

    // Image
    public Guid? ProfileImageId { get; set; }

    // Related collections
    public List<FacultyTranslationUpdateDto>? Translations { get; set; }
    public List<FacultyEducationUpdateDto>? Education { get; set; }
    public List<FacultySpecializationUpdateDto>? Specializations { get; set; }
    public List<FacultyCourseUpdateDto>? Courses { get; set; }
    public List<FacultyPublicationUpdateDto>? Publications { get; set; }
    public List<FacultyAwardUpdateDto>? Awards { get; set; }
}

public class FacultyTranslationUpdateDto
{
    public string? Name { get; set; }
    public string? Title { get; set; }
    public string? Biography { get; set; }
    public string? ShortBio { get; set; }
    public string LanguageCode { get; set; }
}

public class FacultyEducationUpdateDto
{
    public string? Degree { get; set; }
    public string? Institution { get; set; }
    public int? Year { get; set; }
    public int? DisplayOrder { get; set; }
}

public class FacultyEducationTranslationUpdateDto
{
    public string? Degree { get; set; }
    public string? Institution { get; set; }
}

public class FacultySpecializationUpdateDto
{
    public string? Name { get; set; }
    public int? DisplayOrder { get; set; }
}

public class FacultySpecializationTranslationUpdateDto
{
    public string? Name { get; set; }
}

public class FacultyCourseUpdateDto
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public int? DisplayOrder { get; set; }
}

public class FacultyPublicationUpdateDto
{
    public string? Title { get; set; }
    public string? Journal { get; set; }
    public int? Year { get; set; }
    public string? Url { get; set; }
    public int? DisplayOrder { get; set; }
}

public class FacultyAwardUpdateDto
{
    public string? Name { get; set; }
    public int? Year { get; set; }
    public string? Organization { get; set; }
    public int? DisplayOrder { get; set; }
}

/// <summary>
/// Filter DTO for faculty queries
/// </summary>
public class FacultyFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filter by faculty name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Filter by department
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter by featured status
    /// </summary>
    public bool? IsFeatured { get; set; }
}
