// Hero Section
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('/assets/images/admissions-hero.jpg');
  background-size: cover;
  background-position: center;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  margin-bottom: 2rem;

  .hero-content {
    max-width: 800px;
    padding: 0 20px;

    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    .subtitle {
      font-size: 1.5rem;
      font-weight: 300;
      margin-bottom: 2rem;
    }

    .hero-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;

      @media (max-width: 576px) {
        flex-direction: column;
        align-items: center;
      }

      .apply-button {
        padding: 8px 24px;
        font-size: 1.1rem;
      }

      .tour-button {
        padding: 8px 24px;
        font-size: 1.1rem;
      }
    }
  }
}

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Content Sections
.content-section {
  margin-bottom: 4rem;
  padding-top: 2rem;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: #333;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100px;
      height: 4px;
      background-color: #3f51b5;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #555;
  }
}

// Floating Navigation
.floating-nav {
  position: fixed;
  top: 50%;
  right: -200px;
  transform: translateY(-50%);
  background-color: white;
  border-radius: 8px 0 0 8px;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  z-index: 100;

  &.visible {
    right: 0;
  }

  .floating-nav-content {
    padding: 20px;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 1.2rem;
      color: #333;
    }

    .floating-nav-links {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 10px;

        a {
          display: flex;
          align-items: center;
          color: #666;
          text-decoration: none;
          padding: 8px 10px;
          border-radius: 4px;
          transition: background-color 0.2s;
          cursor: pointer;

          &:hover {
            background-color: #f5f5f5;
            color: #3f51b5;
          }

          &.active {
            background-color: #e8eaf6;
            color: #3f51b5;
            font-weight: 500;
          }

          mat-icon {
            margin-right: 10px;
          }
        }
      }
    }
  }
}

// Admission Process Steps
.process-steps {
  display: flex;
  flex-direction: column;
  gap: 30px;
  margin: 40px 0;

  .step {
    display: flex;
    align-items: flex-start;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .step-number {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      background-color: #3f51b5;
      color: white;
      border-radius: 50%;
      font-size: 1.5rem;
      font-weight: bold;
      margin-right: 20px;
      flex-shrink: 0;
    }

    .step-content {
      flex-grow: 1;

      .step-icon {
        display: inline-block;
        background-color: #e8eaf6;
        border-radius: 50%;
        padding: 10px;
        margin-bottom: 15px;

        mat-icon {
          color: #3f51b5;
          font-size: 24px;
          height: 24px;
          width: 24px;
        }
      }

      h3 {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 1.4rem;
        color: #333;
      }

      p {
        margin-bottom: 0;
        color: #666;
        line-height: 1.6;
      }
    }
  }
}

// CTA Box
.cta-box {
  background: linear-gradient(135deg, #3f51b5, #1a237e);
  color: white;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  margin-top: 40px;

  h3 {
    margin-top: 0;
    font-size: 1.8rem;
    margin-bottom: 15px;
  }

  p {
    font-size: 1.1rem;
    margin-bottom: 25px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }

  .cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;

    @media (max-width: 576px) {
      flex-direction: column;
      align-items: center;
    }
  }
}

// Requirements Tabs
.requirements-tabs {
  margin-top: 30px;
  margin-bottom: 30px;

  .requirements-list {
    padding: 30px 20px;

    h3 {
      font-size: 1.6rem;
      margin-bottom: 20px;
      color: #333;
    }

    ul {
      list-style: none;
      padding: 0;

      li {
        display: flex;
        align-items: flex-start;
        margin-bottom: 15px;

        mat-icon {
          color: #4caf50;
          margin-right: 10px;
          font-size: 20px;
        }

        span {
          line-height: 1.5;
          color: #555;
        }
      }
    }
  }
}

// Note Box
.note-box {
  display: flex;
  align-items: flex-start;
  background-color: #e8f5e9;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;

  mat-icon {
    color: #4caf50;
    margin-right: 15px;
    font-size: 24px;
  }

  p {
    margin: 0;
    color: #2e7d32;
    line-height: 1.6;
  }
}

// Tuition Cards
.tuition-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin: 40px 0;

  .tuition-card {
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    mat-card-header {
      background-color: #3f51b5;
      padding: 20px;

      mat-card-title {
        color: white;
        font-size: 1.4rem;
        margin-bottom: 0;
      }
    }

    mat-card-content {
      padding: 20px;

      .annual-fee {
        text-align: center;
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;

        .fee-amount {
          display: block;
          font-size: 2.5rem;
          font-weight: bold;
          color: #3f51b5;
          margin-bottom: 5px;
        }

        .fee-period {
          color: #666;
          font-size: 1rem;
        }
      }

      .fee-details {
        h4 {
          font-size: 1.2rem;
          margin-top: 20px;
          margin-bottom: 15px;
          color: #333;
        }

        .fee-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;

          .fee-label {
            color: #555;
          }

          .fee-value {
            font-weight: 500;
            color: #333;
          }
        }
      }
    }
  }
}

// Payment Options
.payment-options {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 30px;
  margin-top: 30px;

  h3 {
    font-size: 1.6rem;
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
  }

  p {
    margin-bottom: 20px;
    color: #555;
    line-height: 1.6;
  }

  ul {
    padding-left: 20px;

    li {
      margin-bottom: 10px;
      color: #555;
      line-height: 1.6;
    }
  }
}

// Scholarship Cards
.scholarship-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin: 40px 0;

  .scholarship-card {
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    mat-card-header {
      background-color: #673ab7;
      padding: 20px;

      mat-card-title {
        color: white;
        font-size: 1.3rem;
        margin-bottom: 0;
      }
    }

    mat-card-content {
      padding: 20px;
      flex-grow: 1;

      .scholarship-description {
        margin-bottom: 20px;
        color: #555;
        line-height: 1.6;
      }

      .scholarship-details {
        .detail-item {
          margin-bottom: 15px;

          h4 {
            font-size: 1.1rem;
            margin-top: 0;
            margin-bottom: 5px;
            color: #333;
          }

          p {
            margin: 0;
            color: #555;
            line-height: 1.5;
          }
        }
      }
    }

    mat-card-actions {
      padding: 16px;
      border-top: 1px solid #eee;
    }
  }
}

// Financial Aid Info
.financial-aid-info {
  background: linear-gradient(135deg, #673ab7, #311b92);
  color: white;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  margin-top: 40px;

  h3 {
    margin-top: 0;
    font-size: 1.8rem;
    margin-bottom: 15px;
  }

  p {
    font-size: 1.1rem;
    margin-bottom: 25px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Dates Timeline
.dates-timeline {
  position: relative;
  margin: 40px 0;
  padding-left: 30px;

  &:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 4px;
    background-color: #e0e0e0;
  }

  .timeline-item {
    position: relative;
    margin-bottom: 30px;

    &:last-child {
      margin-bottom: 0;
    }

    &:before {
      content: '';
      position: absolute;
      top: 15px;
      left: -34px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-color: #3f51b5;
      border: 4px solid white;
      z-index: 1;
    }

    .timeline-content {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 25px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

      h3 {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 1.4rem;
        color: #333;
      }

      .timeline-date {
        display: inline-block;
        background-color: #e8eaf6;
        color: #3f51b5;
        font-weight: 500;
        padding: 5px 12px;
        border-radius: 20px;
        margin-bottom: 15px;
      }

      p {
        margin-bottom: 0;
        color: #555;
        line-height: 1.6;
      }
    }
  }
}

// FAQ Accordion
.faq-accordion {
  margin: 40px 0;

  ::ng-deep .mat-expansion-panel {
    margin-bottom: 15px;
    border-radius: 8px;
    overflow: hidden;
  }

  ::ng-deep .mat-expansion-panel-header {
    padding: 20px;
  }

  ::ng-deep .mat-expansion-panel-header-title {
    color: #333;
    font-weight: 500;
  }

  ::ng-deep .mat-expansion-panel-body {
    padding: 0 24px 20px;

    p {
      color: #555;
      line-height: 1.6;
      margin-bottom: 0;
    }
  }
}

// More Questions
.more-questions {
  text-align: center;
  margin-top: 30px;

  p {
    margin-bottom: 15px;
    color: #555;
  }
}

// Inquiry Form
.inquiry-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  margin-top: 40px;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }

  .inquiry-form {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

    h3 {
      font-size: 1.6rem;
      margin-top: 0;
      margin-bottom: 25px;
      color: #333;
    }

    .form-row {
      margin-bottom: 20px;

      &.two-columns {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }
      }

      mat-form-field {
        width: 100%;
      }
    }

    .form-actions {
      margin-top: 30px;
      text-align: right;

      button {
        padding: 8px 24px;
      }
    }
  }

  .contact-info {
    background-color: #e8eaf6;
    border-radius: 8px;
    padding: 30px;

    h3 {
      font-size: 1.6rem;
      margin-top: 0;
      margin-bottom: 25px;
      color: #333;
    }

    .contact-item {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      mat-icon {
        color: #3f51b5;
        margin-right: 15px;
      }

      span {
        color: #555;
        line-height: 1.5;
      }
    }

    .visit-campus {
      margin-top: 40px;
      padding-top: 30px;
      border-top: 1px solid #ccc;

      h4 {
        font-size: 1.3rem;
        margin-top: 0;
        margin-bottom: 15px;
        color: #333;
      }

      p {
        margin-bottom: 20px;
        color: #555;
        line-height: 1.6;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .hero-section {
    height: 350px;

    .hero-content h1 {
      font-size: 2.5rem;
    }
  }

  .floating-nav {
    display: none;
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 300px;

    .hero-content {
      h1 {
        font-size: 2rem;
      }

      .subtitle {
        font-size: 1.2rem;
      }
    }
  }

  .content-section h2 {
    font-size: 2rem;
  }

  .step {
    flex-direction: column;

    .step-number {
      margin-right: 0;
      margin-bottom: 15px;
    }
  }
}

@media (max-width: 576px) {
  .hero-section {
    height: 250px;

    .hero-content h1 {
      font-size: 1.8rem;
    }
  }

  .tuition-cards,
  .scholarship-cards {
    grid-template-columns: 1fr;
  }
}
