using System;
using System.Collections.Generic;

namespace School.Application.DTOs
{
    public class ClubEventDto
    {
        public Guid id { get; set; }
        public int ClubId { get; set; }
        public string Title { get; set; }
        public DateTime Date { get; set; }
        public string Time { get; set; }
        public string Location { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        
        // Related translations
        public ICollection<ClubEventTranslationDto> Translations { get; set; }
    }
}
