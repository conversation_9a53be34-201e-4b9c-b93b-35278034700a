# Authentication Resources Documentation

This document provides a comprehensive overview of all authentication-related resources in the School Management System.

## Backend Authentication Resources

### Services

| Service | Path | Description |
|---------|------|-------------|
| `IAuthService` | `School.Application/Features/Auth/IAuthService.cs` | Interface defining authentication operations |
| `AuthService` | `School.Infrastructure/Identity/AuthService.cs` | Implementation of authentication service |
| `IMfaService` | `School.Application/Features/Auth/IMfaService.cs` | Multi-factor authentication service interface |
| `MfaService` | `School.Infrastructure/Identity/MfaService.cs` | Multi-factor authentication implementation |
| `ITokenGenerator` | `School.Application/Features/Auth/ITokenGenerator.cs` | Token generation interface |
| `JwtTokenGenerator` | `School.Infrastructure/Identity/JwtTokenGenerator.cs` | JWT token generation implementation |

### API Endpoints

| Endpoint | Path | Description |
|----------|------|-------------|
| Auth Endpoints | `School.API/Endpoints/AuthEndpoints.cs` | Authentication API endpoints |
| MFA Endpoints | `School.API/Endpoints/AuthEndpoints.cs` | Multi-factor authentication endpoints |

### DTOs

| DTO | Description |
|-----|-------------|
| `LoginDto` | Login request data transfer object |
| `LoginResponseDto` | Login response with tokens and user info |
| `RefreshTokenDto` | Refresh token request |
| `RefreshTokenResponseDto` | Refresh token response with new tokens |
| `UserCreateDto` | User registration data |

## Frontend Authentication Resources

### Services

| Service | Path | Description |
|---------|------|-------------|
| `AuthService` | `src/app/core/services/auth.service.ts` | Angular authentication service |
| `ThemeService` | `src/app/core/services/theme.service.ts` | Theme management service |
| `LanguageService` | `src/app/core/services/language.service.ts` | Language management service |

### Components

| Component | Path | Description |
|-----------|------|-------------|
| `LoginComponent` | `src/app/pages/login/login.component.ts` | Login page component |
| `RegisterComponent` | `src/app/pages/register/register.component.ts` | Registration page component |
| `AuthTestComponent` | `src/app/shared/components/auth-test/auth-test.component.ts` | Authentication testing component |

### Guards

| Guard | Path | Description |
|-------|------|-------------|
| `adminAuthGuard` | `src/app/core/guards/admin-auth.guard.ts` | Admin authentication guard |
| `authGuard` | `src/app/core/guards/auth.guard.ts` | General authentication guard |

### Interceptors

| Interceptor | Path | Description |
|-------------|------|-------------|
| `AuthInterceptor` | `src/app/core/interceptors/auth.interceptor.ts` | Authentication HTTP interceptor |
| `HttpRequestInterceptor` | `src/app/core/interceptors/http-request.interceptor.ts` | Consolidated HTTP request interceptor |

### Routes

| Route | Path | Description |
|-------|------|-------------|
| `/login` | `src/app/app.routes.ts` | Login page route |
| `/register` | `src/app/app.routes.ts` | Registration page route |
| `/auth-test` | `src/app/app.routes.ts` | Authentication test route |

## Authentication Flow

1. **Login Process**:
   - User enters credentials on the login page
   - Credentials are sent to `/api/auth/login` endpoint
   - If valid, JWT token and refresh token are returned
   - If MFA is enabled, user is prompted for MFA code
   - After successful authentication, user is redirected to appropriate dashboard

2. **Token Management**:
   - Access token stored in localStorage under `auth_token` key
   - Refresh token stored in localStorage under `refresh_token` key
   - Tokens automatically added to API requests via HTTP interceptors
   - Expired tokens trigger automatic logout or refresh

3. **Registration Process**:
   - User fills registration form
   - Data sent to `/api/auth/register` endpoint
   - User account created and confirmation email sent (if configured)
   - User redirected to login page or confirmation page

4. **Security Features**:
   - Multi-factor authentication support
   - JWT token-based authentication
   - Role-based authorization
   - Secure password storage with hashing
   - Token refresh mechanism
   - Session timeout handling

## User Types

The system supports multiple user types for authentication:

| User Type | Value | Description |
|-----------|-------|-------------|
| Student | `student` | Student portal access |
| Parent | `parent` | Parent/guardian portal access |
| Faculty | `faculty` | Teachers and staff portal access |
| Admin | `admin` | Administrative portal access |
| Alumni | `alumni` | Alumni portal access |

## Implementation Notes

- Authentication uses JWT tokens with refresh token rotation
- Password storage uses ASP.NET Identity with secure hashing
- MFA implementation supports TOTP (Time-based One-Time Password)
- Angular interceptors automatically handle token management
- Guards protect routes based on authentication status and roles
