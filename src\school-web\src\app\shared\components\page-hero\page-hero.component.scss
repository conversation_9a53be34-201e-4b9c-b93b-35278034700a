.page-hero {
  // The global hero styles from _hero.scss will be applied
  position: relative;

  // Add overlay for better text readability
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
    z-index: 1;
  }

  .hero-content {
    position: relative;
    z-index: 2; // Ensure content is above the overlay
  }
}
