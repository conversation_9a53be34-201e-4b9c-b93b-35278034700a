using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class ClassTeacherConfiguration : IEntityTypeConfiguration<ClassTeacher>
{
    public void Configure(EntityTypeBuilder<ClassTeacher> builder)
    {
        builder.ToTable("ClassTeachers");

        builder.HasKey(ct => ct.Id);

        // Configure properties
        builder.Property(ct => ct.Responsibilities)
            .HasMaxLength(1000);

        builder.Property(ct => ct.SpecialDuties)
            .HasMaxLength(1000);

        builder.Property(ct => ct.ContactSchedule)
            .HasMaxLength(500);

        builder.Property(ct => ct.OfficeHours)
            .HasMaxLength(500);

        builder.Property(ct => ct.Remarks)
            .HasMaxLength(500);

        builder.Property(ct => ct.CreatedBy)
            .HasMaxLength(450);

        builder.Property(ct => ct.LastModifiedBy)
            .HasMaxLength(450);

        // Configure relationships
        builder.HasOne(ct => ct.Faculty)
            .WithMany()
            .HasForeignKey(ct => ct.FacultyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ct => ct.Section)
            .WithMany()
            .HasForeignKey(ct => ct.SectionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ct => ct.AcademicYear)
            .WithMany()
            .HasForeignKey(ct => ct.AcademicYearId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(ct => ct.Students)
            .WithOne()
            .HasForeignKey("ClassTeacherId")
            .OnDelete(DeleteBehavior.SetNull);

        // Configure indexes
        builder.HasIndex(ct => new { ct.FacultyId, ct.AcademicYearId, ct.IsActive });
        builder.HasIndex(ct => new { ct.SectionId, ct.IsActive });
        builder.HasIndex(ct => ct.AcademicYearId);
        builder.HasIndex(ct => ct.Status);
        builder.HasIndex(ct => ct.StartDate);
        builder.HasIndex(ct => ct.EndDate);

        // Global query filter for multi-tenancy
        builder.HasQueryFilter(ct => EF.Property<string>(ct, "TenantId") == null || 
                                    EF.Property<string>(ct, "TenantId") == "");
    }
}
