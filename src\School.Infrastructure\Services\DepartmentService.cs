using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Department;
using School.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace School.Infrastructure.Services
{
    public class DepartmentService : IDepartmentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<DepartmentService> _logger;

        public DepartmentService(
            IUnitOfWork unitOfWork,
            ICurrentUserService currentUserService,
            ILogger<DepartmentService> logger)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        public async Task<(IEnumerable<DepartmentDto> Departments, int TotalCount)> GetAllDepartmentsAsync(DepartmentFilterDto filter)
        {
            var repository = _unitOfWork.Repository<Department>();
            var query = repository.AsQueryable("Translations", "Image");

            // Apply filters
            if (!string.IsNullOrEmpty(filter.Name))
            {
                query = query.Where(d => d.Name.Contains(filter.Name));
            }

            if (!string.IsNullOrEmpty(filter.Code))
            {
                query = query.Where(d => d.Code.Contains(filter.Code));
            }

            if (filter.IsActive.HasValue)
            {
                query = query.Where(d => d.IsActive == filter.IsActive.Value);
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Get paginated results
            var departments = await query
                .OrderBy(d => d.DisplayOrder)
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(d => new DepartmentDto
                {
                    Id = d.Id,
                    Name = d.Name,
                    Code = d.Code,
                    Description = d.Description,
                    HeadOfDepartment = d.HeadOfDepartment,
                    ContactEmail = d.ContactEmail,
                    ContactPhone = d.ContactPhone,
                    Location = d.Location,
                    IsActive = d.IsActive,
                    DisplayOrder = d.DisplayOrder,
                    ImageId = d.ImageId,
                    Image = d.Image != null ? new MediaItemDto
                    {
                        Id = d.Image.Id,
                        FileName = d.Image.FileName,
                        FilePath = d.Image.FilePath,
                        MimeType = d.Image.MimeType
                    } : null,
                    CreatedAt = d.CreatedAt,
                    LastModifiedAt = d.LastModifiedAt,
                    Translations = d.Translations.Select(t => new DepartmentTranslationDto
                    {
                        Id = t.Id,
                        DepartmentId = t.DepartmentId,
                        LanguageCode = t.LanguageCode,
                        Name = t.Name,
                        Description = t.Description,
                        HeadOfDepartment = t.HeadOfDepartment
                    }).ToList()
                })
                .ToListAsync();

            return (departments, totalCount);
        }

        public async Task<DepartmentDto?> GetDepartmentByIdAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<Department>();
            var department = await repository.GetByIdAsync(id, new[] { "Translations", "Image" });

            if (department == null) return null;

            return MapToDepartmentDto(department);
        }

        public async Task<DepartmentDto?> GetDepartmentByCodeAsync(string code)
        {
            var repository = _unitOfWork.Repository<Department>();
            var departments = await repository.FindAsync(d => d.Code == code, new[] { "Translations", "Image" });

            if (!departments.Any()) return null;

            var department = departments.First();
            return MapToDepartmentDto(department);
        }

        public async Task<Guid> CreateDepartmentAsync(CreateDepartmentDto departmentDto)
        {
            var repository = _unitOfWork.Repository<Department>();

            // Check if department code already exists
            var existingDepartments = await repository.FindAsync(d => d.Code == departmentDto.Code);
            if (existingDepartments.Any())
            {
                _logger.LogWarning("Department creation failed: Code {Code} already exists", departmentDto.Code);
                throw new InvalidOperationException($"Department with code '{departmentDto.Code}' already exists");
            }

            var department = new Department
            {
                Name = departmentDto.Name,
                Code = departmentDto.Code,
                Description = departmentDto.Description,
                HeadOfDepartment = departmentDto.HeadOfDepartment,
                ContactEmail = departmentDto.ContactEmail,
                ContactPhone = departmentDto.ContactPhone,
                Location = departmentDto.Location,
                IsActive = departmentDto.IsActive,
                DisplayOrder = departmentDto.DisplayOrder,
                ImageId = departmentDto.ImageId,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.AddAsync(department, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            // Add translations if provided
            if (departmentDto.Translations != null && departmentDto.Translations.Any())
            {
                var translationRepository = _unitOfWork.Repository<DepartmentTranslation>();

                foreach (var translationDto in departmentDto.Translations)
                {
                    var translation = new DepartmentTranslation
                    {
                        DepartmentId = department.Id,
                        LanguageCode = translationDto.LanguageCode,
                        Name = translationDto.Name,
                        Description = translationDto.Description,
                        HeadOfDepartment = translationDto.HeadOfDepartment,
                        // CreatedAt and CreatedBy will be set by the repository
                    };

                    var translationUserId = _currentUserService.UserId?.ToString();
                    await translationRepository.AddAsync(translation, translationUserId);
                }

                var saveUserId = _currentUserService.UserId?.ToString();
                await _unitOfWork.SaveChangesAsync(saveUserId);
            }

            _logger.LogInformation("Department created with ID {DepartmentId}", department.Id);
            return department.Id;
        }

        public async Task<bool> UpdateDepartmentAsync(Guid id, UpdateDepartmentDto departmentDto)
        {
            var repository = _unitOfWork.Repository<Department>();
            var department = await repository.GetByIdAsync(id);

            if (department == null) return false;

            // Check if code is being changed and if the new code already exists
            if (departmentDto.Code != department.Code)
            {
                var existingDepartments = await repository.FindAsync(d => d.Code == departmentDto.Code && d.Id != id);
                if (existingDepartments.Any())
                {
                    _logger.LogWarning("Department update failed: Code {Code} already exists", departmentDto.Code);
                    throw new InvalidOperationException($"Department with code '{departmentDto.Code}' already exists");
                }
            }

            // Update properties
            department.Name = departmentDto.Name;
            department.Code = departmentDto.Code;
            department.Description = departmentDto.Description;
            department.HeadOfDepartment = departmentDto.HeadOfDepartment;
            department.ContactEmail = departmentDto.ContactEmail;
            department.ContactPhone = departmentDto.ContactPhone;
            department.Location = departmentDto.Location;
            department.IsActive = departmentDto.IsActive;
            department.DisplayOrder = departmentDto.DisplayOrder;
            department.ImageId = departmentDto.ImageId;
            // LastModifiedBy and LastModifiedAt will be set by the repository

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await repository.UpdateAsync(department, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Department updated with ID {DepartmentId}", department.Id);
            return true;
        }

        public async Task<bool> DeleteDepartmentAsync(Guid id)
        {
            var repository = _unitOfWork.Repository<Department>();

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Use the repository's DeleteByIdAsync method which handles soft delete
            await repository.DeleteByIdAsync(id, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Department deleted with ID {DepartmentId}", id);
            return true;
        }

        #region Translation Methods

        public async Task<bool> AddTranslationAsync(Guid departmentId, CreateDepartmentTranslationDto translationDto)
        {
            var departmentRepository = _unitOfWork.Repository<Department>();
            var department = await departmentRepository.GetByIdAsync(departmentId, new[] { "Translations" });

            if (department == null) return false;

            // Check if translation for this language already exists
            var existingTranslation = department.Translations
                .FirstOrDefault(t => t.LanguageCode == translationDto.LanguageCode);

            if (existingTranslation != null)
            {
                _logger.LogWarning("Translation for language {LanguageCode} already exists for department {DepartmentId}",
                    translationDto.LanguageCode, departmentId);
                return false;
            }

            var translationRepository = _unitOfWork.Repository<DepartmentTranslation>();

            var translation = new DepartmentTranslation
            {
                DepartmentId = departmentId,
                LanguageCode = translationDto.LanguageCode,
                Name = translationDto.Name,
                Description = translationDto.Description,
                HeadOfDepartment = translationDto.HeadOfDepartment,
                // CreatedAt and CreatedBy will be set by the repository
            };

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await translationRepository.AddAsync(translation, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Translation added for department {DepartmentId} in language {LanguageCode}",
                departmentId, translationDto.LanguageCode);
            return true;
        }

        public async Task<bool> UpdateTranslationAsync(Guid departmentId, string languageCode, UpdateDepartmentTranslationDto translationDto)
        {
            var translationRepository = _unitOfWork.Repository<DepartmentTranslation>();

            var translations = await translationRepository.FindAsync(
                t => t.DepartmentId == departmentId && t.LanguageCode == languageCode);

            if (!translations.Any()) return false;

            var translation = translations.First();

            // Update properties
            translation.Name = translationDto.Name ?? translation.Name;
            translation.Description = translationDto.Description ?? translation.Description;
            translation.HeadOfDepartment = translationDto.HeadOfDepartment ?? translation.HeadOfDepartment;
            // LastModifiedBy and LastModifiedAt will be set by the repository

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await translationRepository.UpdateAsync(translation, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Translation updated for department {DepartmentId} in language {LanguageCode}",
                departmentId, languageCode);
            return true;
        }

        public async Task<bool> DeleteTranslationAsync(Guid departmentId, string languageCode)
        {
            var translationRepository = _unitOfWork.Repository<DepartmentTranslation>();

            var translations = await translationRepository.FindAsync(
                t => t.DepartmentId == departmentId && t.LanguageCode == languageCode);

            if (!translations.Any()) return false;

            var translation = translations.First();

            // Get the current user ID for audit trail
            var userId = _currentUserService.UserId?.ToString();

            // Pass the current user ID to the repository for audit trail
            await translationRepository.DeleteAsync(translation, userId);
            await _unitOfWork.SaveChangesAsync(userId);

            _logger.LogInformation("Translation deleted for department {DepartmentId} in language {LanguageCode}",
                departmentId, languageCode);
            return true;
        }

        #endregion

        #region Helper Methods

        private DepartmentDto MapToDepartmentDto(Department department)
        {
            return new DepartmentDto
            {
                Id = department.Id,
                Name = department.Name,
                Code = department.Code,
                Description = department.Description,
                HeadOfDepartment = department.HeadOfDepartment,
                ContactEmail = department.ContactEmail,
                ContactPhone = department.ContactPhone,
                Location = department.Location,
                IsActive = department.IsActive,
                DisplayOrder = department.DisplayOrder,
                ImageId = department.ImageId,
                Image = department.Image != null ? new MediaItemDto
                {
                    Id = department.Image.Id,
                    FileName = department.Image.FileName,
                    FilePath = department.Image.FilePath,
                    MimeType = department.Image.MimeType
                } : null,
                CreatedAt = department.CreatedAt,
                LastModifiedAt = department.LastModifiedAt,
                Translations = department.Translations.Select(t => new DepartmentTranslationDto
                {
                    Id = t.Id,
                    DepartmentId = t.DepartmentId,
                    LanguageCode = t.LanguageCode,
                    Name = t.Name,
                    Description = t.Description,
                    HeadOfDepartment = t.HeadOfDepartment
                }).ToList()
            };
        }

        #endregion
    }
}
