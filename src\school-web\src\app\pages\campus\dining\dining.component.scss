@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$green: #4caf50;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section styles are now handled by the DefaultHeroComponent

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Section Styles
section {
  padding: $section-padding;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $primary-color;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: $dark-gray;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Introduction Section
.intro-section {
  background-color: $white;

  .intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Dining Locations Section
.locations-section {
  background-color: $light-gray;

  .locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .location-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .location-image {
        height: 200px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 10px;
          color: $text-color;
        }

        .location-description {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 20px;
          flex-grow: 1;
        }

        .location-details {
          .detail-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;

            &:last-child {
              margin-bottom: 0;
            }

            mat-icon {
              color: $primary-color;
              margin-right: 10px;
              font-size: 20px;
              height: 20px;
              width: 20px;
              margin-top: 3px;
              flex-shrink: 0;
            }

            span {
              color: $dark-gray;
            }
          }
        }
      }
    }
  }
}

// Menu Section
.menu-section {
  background-color: $white;

  ::ng-deep .mat-mdc-tab-header {
    margin-bottom: 30px;
  }

  .menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    padding: 20px;

    .menu-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .menu-image {
        height: 200px;
        overflow: hidden;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }

        .menu-badge {
          position: absolute;
          top: 10px;
          right: 10px;
          background-color: $green;
          color: $white;
          border-radius: 50%;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;

          mat-icon {
            font-size: 18px;
            height: 18px;
            width: 18px;
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;

        .menu-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          h3 {
            font-size: 1.3rem;
            color: $text-color;
            margin: 0;
          }

          .menu-price {
            font-weight: 500;
            color: $primary-color;
          }
        }

        .menu-description {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 15px;
          flex-grow: 1;
        }

        .menu-allergens {
          font-size: 0.9rem;
          color: $dark-gray;

          .allergen-label {
            font-weight: 500;
            margin-right: 5px;
          }
        }
      }
    }
  }

  .menu-legend {
    display: flex;
    justify-content: center;
    margin-top: 30px;

    .legend-item {
      display: flex;
      align-items: center;
      margin: 0 15px;

      mat-icon {
        color: $green;
        margin-right: 5px;
      }

      span {
        color: $dark-gray;
      }
    }
  }
}

// Meal Plans Section
.meal-plans-section {
  background-color: $light-gray;

  .meal-plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .plan-card {
      border-radius: $border-radius;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      mat-card-header {
        padding: 20px 20px 0;

        mat-card-title {
          font-size: 1.5rem;
          margin-bottom: 5px;
          color: $text-color;
        }

        mat-card-subtitle {
          color: $primary-color;
          font-weight: 500;
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;

        .plan-description {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 20px;
        }

        .plan-features {
          padding-left: 20px;
          margin-bottom: 0;

          li {
            color: $dark-gray;
            line-height: 1.6;
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      mat-card-actions {
        padding: 0 20px 20px;
        margin-bottom: 0;
      }
    }
  }
}

// Initiatives Section
.initiatives-section {
  background-color: $white;

  .initiatives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .initiative-item {
      text-align: center;
      padding: 30px;
      border-radius: $border-radius;
      background-color: $light-gray;
      transition: transform 0.3s, box-shadow 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: $box-shadow;
      }

      .initiative-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 70px;
        height: 70px;
        background-color: $primary-color;
        border-radius: 50%;
        margin: 0 auto 20px;

        mat-icon {
          font-size: 35px;
          height: 35px;
          width: 35px;
          color: $white;
        }
      }

      h3 {
        font-size: 1.3rem;
        margin-bottom: 15px;
        color: $text-color;
      }

      p {
        color: $dark-gray;
        line-height: 1.6;
        margin-bottom: 0;
      }
    }
  }
}

// Dietary Needs Section
.dietary-needs-section {
  background-color: $light-gray;

  .dietary-content {
    display: flex;
    align-items: center;
    gap: 40px;
    margin-top: 40px;

    .dietary-image {
      flex: 1;
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;

      img {
        width: 100%;
        height: auto;
        display: block;
      }
    }

    .dietary-text {
      flex: 1;

      p {
        color: $text-color;
        line-height: 1.6;
        margin-bottom: 20px;

        &:last-of-type {
          margin-bottom: 30px;
        }
      }

      .dietary-cta {
        a {
          padding: 10px 20px;
        }
      }
    }
  }
}

// Contact Section
.contact-section {
  background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -15%));
  color: $white;

  .contact-content {
    max-width: 800px;
    margin: 0 auto;

    h2 {
      color: $white;

      &:after {
        background-color: $white;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
      text-align: center;
    }

    .contact-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;

      .contact-item {
        display: flex;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: $border-radius;
        padding: 20px;

        mat-icon {
          font-size: 30px;
          height: 30px;
          width: 30px;
          margin-right: 20px;
          margin-top: 5px;
        }

        .contact-details {
          h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
          }

          p {
            font-size: 1rem;
            margin-bottom: 5px;
            text-align: left;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  // Hero section styles removed

  section {
    padding: 60px 0;

    h2 {
      font-size: 2rem;
    }
  }

  .locations-grid, .menu-grid, .meal-plans-grid, .initiatives-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .dietary-content {
    flex-direction: column;

    .dietary-image, .dietary-text {
      flex: none;
      width: 100%;
    }
  }
}

@media (max-width: 768px) {
  // Hero section styles removed

  .contact-info {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  // Hero section styles removed

  section h2 {
    font-size: 1.8rem;
  }

  .locations-grid, .menu-grid, .meal-plans-grid, .initiatives-grid {
    grid-template-columns: 1fr;
  }
}
