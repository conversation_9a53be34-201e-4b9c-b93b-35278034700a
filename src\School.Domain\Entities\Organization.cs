using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

/// <summary>
/// Represents a school/organization in the multi-tenant system
/// Each organization is a separate tenant with isolated data
/// </summary>
public class Organization : BaseEntity
{
    /// <summary>
    /// Official name of the school/organization
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Unique identifier for the organization (used in subdomains)
    /// e.g., "greenwood-school" for greenwood-school.edumanage.com
    /// </summary>
    public string Slug { get; set; } = string.Empty;

    /// <summary>
    /// Display name for branding purposes
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Short description of the organization
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Organization type (School, College, University, etc.)
    /// </summary>
    public OrganizationType Type { get; set; } = OrganizationType.School;

    /// <summary>
    /// Current status of the organization
    /// </summary>
    public OrganizationStatus Status { get; set; } = OrganizationStatus.Active;

    /// <summary>
    /// Primary contact email for the organization
    /// </summary>
    public string ContactEmail { get; set; } = string.Empty;

    /// <summary>
    /// Primary contact phone number
    /// </summary>
    public string ContactPhone { get; set; } = string.Empty;

    /// <summary>
    /// Physical address of the organization
    /// </summary>
    public string Address { get; set; } = string.Empty;

    /// <summary>
    /// City where the organization is located
    /// </summary>
    public string City { get; set; } = string.Empty;

    /// <summary>
    /// State/Province where the organization is located
    /// </summary>
    public string State { get; set; } = string.Empty;

    /// <summary>
    /// Country where the organization is located
    /// </summary>
    public string Country { get; set; } = string.Empty;

    /// <summary>
    /// Postal/ZIP code
    /// </summary>
    public string PostalCode { get; set; } = string.Empty;

    /// <summary>
    /// Website URL of the organization
    /// </summary>
    public string Website { get; set; } = string.Empty;

    /// <summary>
    /// Custom domain for the organization (optional)
    /// e.g., "myschool.edu" instead of subdomain
    /// </summary>
    public string? CustomDomain { get; set; }

    /// <summary>
    /// Primary language/locale for the organization
    /// </summary>
    public string DefaultLanguage { get; set; } = "en-US";

    /// <summary>
    /// Timezone for the organization
    /// </summary>
    public string TimeZone { get; set; } = "UTC";

    /// <summary>
    /// Currency used by the organization for fees, etc.
    /// </summary>
    public string Currency { get; set; } = "USD";

    /// <summary>
    /// Date when the organization was established
    /// </summary>
    public DateTime? EstablishedDate { get; set; }

    /// <summary>
    /// Date when the organization joined the platform
    /// </summary>
    public DateTime JoinedDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Maximum number of students allowed for this organization
    /// </summary>
    public int? MaxStudents { get; set; }

    /// <summary>
    /// Maximum number of faculty members allowed
    /// </summary>
    public int? MaxFaculty { get; set; }

    /// <summary>
    /// Maximum storage space in MB
    /// </summary>
    public long? MaxStorageMB { get; set; }

    /// <summary>
    /// Whether the organization is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Whether the organization is in trial period
    /// </summary>
    public bool IsTrialActive { get; set; } = true;

    /// <summary>
    /// Trial end date
    /// </summary>
    public DateTime? TrialEndDate { get; set; }

    /// <summary>
    /// Logo/branding image for the organization
    /// </summary>
    public Guid? LogoImageId { get; set; }
    public MediaItem? LogoImage { get; set; }

    /// <summary>
    /// Banner image for the organization
    /// </summary>
    public Guid? BannerImageId { get; set; }
    public MediaItem? BannerImage { get; set; }

    /// <summary>
    /// Navigation properties
    /// </summary>
    public ICollection<OrganizationUser> Users { get; set; } = new List<OrganizationUser>();
    public ICollection<OrganizationSubscription> Subscriptions { get; set; } = new List<OrganizationSubscription>();
    public OrganizationSettings? Settings { get; set; }
}
