using School.Application.DTOs;
using School.Domain.Enums;
using School.Domain.Entities;

namespace School.Application.Features.Grade;

/// <summary>
/// Service interface for Grade management operations
/// </summary>
public interface IGradeService
{
    // Grade CRUD operations
    Task<(IEnumerable<GradeDto> Grades, int TotalCount)> GetAllGradesAsync(GradeFilterDto filter);
    Task<GradeDto?> GetGradeByIdAsync(Guid id);
    Task<IEnumerable<GradeDto>> GetGradesByAcademicYearAsync(Guid academicYearId);
    Task<IEnumerable<GradeDto>> GetActiveGradesAsync(Guid academicYearId);
    Task<Guid> CreateGradeAsync(CreateGradeDto gradeDto);
    Task<bool> UpdateGradeAsync(Guid id, UpdateGradeDto gradeDto);
    Task<bool> DeleteGradeAsync(Guid id);

    // Grade status management
    Task<bool> ActivateGradeAsync(Guid id);
    Task<bool> DeactivateGradeAsync(Guid id);
    Task<bool> UpdateDisplayOrderAsync(Guid id, int newOrder);

    // Grade validation
    Task<bool> ValidateGradeCodeAsync(string code, Guid academicYearId, Guid? excludeId = null);
    Task<bool> ValidateGradeLevelAsync(int level, Guid academicYearId, Guid? excludeId = null);
    Task<bool> CanDeleteGradeAsync(Guid id);

    // Grade statistics and analytics
    Task<GradeStatisticsDto> GetGradeStatisticsAsync(Guid id);
    Task<IEnumerable<GradeStatisticsDto>> GetAllGradeStatisticsAsync(Guid academicYearId);
    Task<int> GetTotalStudentsByGradeAsync(Guid gradeId);
    Task<int> GetTotalSectionsByGradeAsync(Guid gradeId);

    // Grade capacity management
    Task<bool> UpdateGradeCapacityAsync(Guid id, int newCapacity);
    Task<bool> CheckGradeCapacityAsync(Guid gradeId);
    Task<IEnumerable<GradeDto>> GetOverCapacityGradesAsync(Guid academicYearId);

    // Grade promotion and progression
    Task<IEnumerable<GradeDto>> GetPromotionEligibleGradesAsync(Guid academicYearId);
    Task<bool> PromoteStudentsToNextGradeAsync(Guid fromGradeId, Guid toGradeId, List<Guid> studentIds);

    // Translation management
    Task<bool> AddTranslationAsync(Guid gradeId, CreateGradeTranslationDto translationDto);
    Task<bool> UpdateTranslationAsync(Guid gradeId, UpdateGradeTranslationDto translationDto);
    Task<bool> DeleteTranslationAsync(Guid gradeId, string languageCode);
    Task<IEnumerable<GradeTranslationDto>> GetTranslationsAsync(Guid gradeId);

    // Bulk operations
    Task<bool> BulkUpdateGradeStatusAsync(List<Guid> gradeIds, bool isActive);
    Task<bool> BulkDeleteGradesAsync(List<Guid> gradeIds);
    Task<bool> BulkUpdateDisplayOrderAsync(Dictionary<Guid, int> gradeOrders);

    // Import/Export operations
    Task<bool> ImportGradesFromCsvAsync(Stream csvStream, Guid academicYearId);
    Task<Stream> ExportGradesToCsvAsync(Guid academicYearId);
    Task<bool> DuplicateGradesToNewAcademicYearAsync(Guid sourceAcademicYearId, Guid targetAcademicYearId);

    // Grade configuration
    Task<bool> UpdatePromotionCriteriaAsync(Guid gradeId, string criteria);
    Task<bool> UpdatePassingGradeAsync(Guid gradeId, decimal passingGrade);
    Task<IEnumerable<GradeDto>> GetGradesByEducationLevelAsync(Guid academicYearId, EducationLevel educationLevel);

    // Grade relationships
    Task<IEnumerable<SectionDto>> GetGradeSectionsAsync(Guid gradeId);
    Task<IEnumerable<StudentDto>> GetGradeStudentsAsync(Guid gradeId);
    Task<int> GetAvailableCapacityAsync(Guid gradeId);

    // Academic year transitions
    Task<bool> ArchiveGradeAsync(Guid gradeId);
    Task<bool> RestoreGradeAsync(Guid gradeId);
    Task<IEnumerable<GradeDto>> GetArchivedGradesAsync(Guid academicYearId);
}
