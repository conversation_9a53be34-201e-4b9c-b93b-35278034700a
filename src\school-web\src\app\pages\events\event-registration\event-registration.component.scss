.event-registration-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.back-button {
  margin-bottom: 1.5rem;
  
  button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  text-align: center;
}

.error-message {
  color: #f44336;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.registration-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.registration-header {
  padding: 2rem;
  text-align: center;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  
  h1 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
  }
  
  h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #3f51b5;
    margin-bottom: 0.5rem;
  }
  
  .event-date {
    font-size: 1.1rem;
    color: #666;
  }
}

.registration-success {
  padding: 3rem 2rem;
  text-align: center;
  
  .success-icon {
    font-size: 4rem;
    width: 4rem;
    height: 4rem;
    color: #4CAF50;
    margin-bottom: 1rem;
  }
  
  h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
  }
  
  p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }
  
  button {
    padding: 0.5rem 2rem;
    font-size: 1.1rem;
  }
}

.registration-form-container {
  padding: 2rem;
  
  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1.5rem;
  }
  
  .form-field {
    margin-bottom: 1.5rem;
    
    mat-form-field {
      width: 100%;
    }
  }
  
  mat-card-actions {
    padding: 1rem;
    
    button {
      margin-left: 1rem;
    }
  }
}

.spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
