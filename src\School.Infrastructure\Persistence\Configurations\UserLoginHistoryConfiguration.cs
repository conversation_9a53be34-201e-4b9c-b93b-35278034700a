using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Infrastructure.Identity;

namespace School.Infrastructure.Persistence.Configurations
{
    public class UserLoginHistoryConfiguration : IEntityTypeConfiguration<UserLoginHistory>
    {
        public void Configure(EntityTypeBuilder<UserLoginHistory> builder)
        {
            builder.ToTable("UserLoginHistory");

            builder.HasKey(lh => lh.Id);

            builder.Property(lh => lh.UserId)
                .IsRequired()
                .HasMaxLength(450); // Standard Identity user ID length

            builder.Property(lh => lh.LoginTime)
                .IsRequired()
                .HasDefaultValueSql("NOW()");

            builder.Property(lh => lh.IpAddress)
                .HasMaxLength(45); // IPv6 max length

            builder.Property(lh => lh.UserAgent)
                .HasMaxLength(500);

            builder.Property(lh => lh.IsSuccessful)
                .IsRequired();

            builder.Property(lh => lh.FailureReason)
                .HasMaxLength(200);

            builder.Property(lh => lh.IsMfaUsed)
                .IsRequired()
                .HasDefaultValue(false);

            // Relationships
            builder.HasOne(lh => lh.User)
                .WithMany(u => u.LoginHistory)
                .HasForeignKey(lh => lh.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(lh => lh.UserId)
                .HasDatabaseName("IX_UserLoginHistory_UserId");

            builder.HasIndex(lh => lh.LoginTime)
                .HasDatabaseName("IX_UserLoginHistory_LoginTime");

            builder.HasIndex(lh => new { lh.UserId, lh.LoginTime })
                .HasDatabaseName("IX_UserLoginHistory_UserId_LoginTime");

            builder.HasIndex(lh => new { lh.UserId, lh.IsSuccessful })
                .HasDatabaseName("IX_UserLoginHistory_UserId_IsSuccessful");
        }
    }
}
