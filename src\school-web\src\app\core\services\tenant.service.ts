import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap, switchMap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export interface TenantInfo {
  id: string;
  name: string;
  slug: string;
  displayName: string;
  type: string;
  status: string;
  isActive: boolean;
  isSetupComplete: boolean;
  isTrialActive: boolean;
  trialEndDate?: string;
  customDomain?: string;
  defaultLanguage: string;
  timeZone: string;
  currency: string;
}

export interface TenantExistsResponse {
  exists: boolean;
  identifier: string;
  tenantId?: string;
  tenantName?: string;
  foundBy?: string;
}

@Injectable({
  providedIn: 'root'
})
export class TenantService {
  private readonly apiUrl = environment.apiUrl;
  private currentTenantSubject = new BehaviorSubject<TenantInfo | null>(null);
  private tenantLoadedSubject = new BehaviorSubject<boolean>(false);
  
  public currentTenant$ = this.currentTenantSubject.asObservable();
  public tenantLoaded$ = this.tenantLoadedSubject.asObservable();

  constructor(private http: HttpClient) {
    this.initializeTenant();
  }

  /**
   * Initialize tenant detection on service creation
   */
  private initializeTenant(): void {
    // Don't auto-initialize tenant on service creation
    // Tenant will be loaded when explicitly requested via getCurrentTenant()
    this.tenantLoadedSubject.next(true);
  }

  /**
   * Extract tenant identifier from current URL
   */
  private extractTenantFromUrl(): string | null {
    const hostname = window.location.hostname;
    
    // Skip localhost and IP addresses during development
    if (hostname === 'localhost' || this.isIpAddress(hostname)) {
      // For development, check query parameters or use default
      const urlParams = new URLSearchParams(window.location.search);
      const tenantParam = urlParams.get('tenant');
      
      if (tenantParam) {
        return tenantParam;
      }
      
      // For development, don't assume a default tenant
      // Let the user select or create one after login
      return null;
    }

    // Extract subdomain from hostname
    const subdomain = this.extractSubdomain(hostname);
    if (subdomain) {
      return subdomain;
    }

    // If no subdomain, treat the full domain as custom domain
    return hostname;
  }

  /**
   * Extract subdomain from hostname
   */
  private extractSubdomain(hostname: string): string | null {
    // Define main domain patterns
    const mainDomainPatterns = [
      /^([a-zA-Z0-9-]+)\.edumanage\.com$/,
      /^([a-zA-Z0-9-]+)\.schoolmanage\.com$/,
      /^([a-zA-Z0-9-]+)\.localhost$/
    ];

    for (const pattern of mainDomainPatterns) {
      const match = hostname.match(pattern);
      if (match && match[1]) {
        const subdomain = match[1];
        
        // Exclude common subdomains that are not tenants
        const excludedSubdomains = ['www', 'api', 'admin', 'app', 'mail', 'ftp'];
        if (!excludedSubdomains.includes(subdomain.toLowerCase())) {
          return subdomain;
        }
      }
    }

    return null;
  }

  /**
   * Check if string is an IP address
   */
  private isIpAddress(hostname: string): boolean {
    const ipPattern = /^(\d{1,3}\.){3}\d{1,3}$/;
    return ipPattern.test(hostname);
  }

  /**
   * Load tenant information from API
   */
  private loadTenantInfo(identifier: string): Observable<TenantInfo | null> {
    // First check if tenant exists
    return this.checkTenantExists(identifier).pipe(
      switchMap(response => {
        if (response.exists && response.tenantId) {
          // If tenant exists, get current tenant info
          return this.getCurrentTenant();
        }
        return of(null);
      }),
      catchError(error => {
        console.error('Error loading tenant info:', error);
        return of(null);
      })
    );
  }

  /**
   * Get current tenant information from API based on authenticated user
   */
  getCurrentTenant(): Observable<TenantInfo | null> {
    console.log('🌐 TenantService: Making API call to /api/tenant/current');
    return this.http.get<TenantInfo>(`${this.apiUrl}/tenant/current`).pipe(
      tap(tenant => {
        console.log('✅ TenantService: API response received:', tenant);
        if (tenant) {
          console.log('🏫 TenantService: Setting current tenant:', tenant);
          this.setCurrentTenant(tenant);
        } else {
          console.log('❌ TenantService: No tenant in response, clearing current tenant');
          this.currentTenantSubject.next(null);
        }
      }),
      catchError(error => {
        console.log('❌ TenantService: API error - No tenant access found for current user:', error);
        console.log('❌ Error details:', error.status, error.error);

        // Check if it's a 404 (no tenant found) or other error
        if (error.status === 404) {
          console.log('🚫 TenantService: 404 - No tenant context found');
        }

        // Clear any stale tenant data
        this.currentTenantSubject.next(null);
        return of(null);
      })
    );
  }

  /**
   * Check if tenant exists by identifier
   */
  checkTenantExists(identifier: string): Observable<TenantExistsResponse> {
    return this.http.get<TenantExistsResponse>(`${this.apiUrl}/tenant/check/${identifier}`).pipe(
      catchError(error => {
        console.error('Error checking tenant existence:', error);
        return of({ exists: false, identifier });
      })
    );
  }

  /**
   * Set current tenant and notify subscribers
   */
  private setCurrentTenant(tenant: TenantInfo): void {
    this.currentTenantSubject.next(tenant);
    this.tenantLoadedSubject.next(true);
    
    // Store tenant info in localStorage for persistence
    localStorage.setItem('currentTenant', JSON.stringify(tenant));
    
    // Update document title and other tenant-specific settings
    this.updateTenantSettings(tenant);
  }

  /**
   * Update tenant-specific settings
   */
  private updateTenantSettings(tenant: TenantInfo): void {
    // Update document title
    document.title = `${tenant.displayName} - School Management System`;
    
    // Update meta tags
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', `${tenant.displayName} - Educational Institution Management`);
    }
    
    // You can add more tenant-specific customizations here
    // such as theme colors, favicon, etc.
  }

  /**
   * Handle case when tenant is not found
   */
  private handleTenantNotFound(identifier: string): void {
    console.warn(`Tenant not found: ${identifier}`);
    this.tenantLoadedSubject.next(true);
    
    // You might want to redirect to a tenant selection page
    // or show an error message
    // For now, we'll just log the error
  }

  /**
   * Handle tenant loading errors
   */
  private handleTenantError(error: any): void {
    console.error('Tenant loading error:', error);
    this.tenantLoadedSubject.next(true);
    
    // You might want to show an error message to the user
    // or redirect to an error page
  }

  /**
   * Handle case when no tenant identifier is found
   */
  private handleNoTenantIdentifier(): void {
    console.warn('No tenant identifier found in URL');
    this.tenantLoadedSubject.next(true);
    
    // For development, you might want to redirect to a tenant selection page
    // or use a default tenant
  }

  /**
   * Get current tenant synchronously
   */
  getCurrentTenantSync(): TenantInfo | null {
    return this.currentTenantSubject.value;
  }

  /**
   * Check if tenant is loaded
   */
  isTenantLoaded(): boolean {
    return this.tenantLoadedSubject.value;
  }

  /**
   * Clear current tenant (for logout or tenant switching)
   */
  clearCurrentTenant(): void {
    console.log('🧹 TenantService: Clearing current tenant');
    this.currentTenantSubject.next(null);
    this.tenantLoadedSubject.next(false);
    localStorage.removeItem('currentTenant');
    console.log('✅ TenantService: Current tenant cleared');
  }

  /**
   * Switch to a different tenant
   */
  switchTenant(tenantSlug: string): void {
    // Construct new URL with tenant subdomain
    const protocol = window.location.protocol;
    const port = window.location.port ? `:${window.location.port}` : '';
    const newUrl = `${protocol}//${tenantSlug}.${environment.baseDomain}${port}`;
    
    // Redirect to new tenant URL
    window.location.href = newUrl;
  }

  /**
   * Get tenant-specific API headers
   */
  getTenantHeaders(): { [key: string]: string } {
    const tenant = this.getCurrentTenantSync();
    const headers: { [key: string]: string } = {};

    if (tenant) {
      headers['X-Tenant-Id'] = tenant.id;
    }

    // For development, also add tenant identifier
    const tenantIdentifier = this.extractTenantFromUrl();
    if (tenantIdentifier) {
      headers['X-Tenant'] = tenantIdentifier;
    }
    // Note: Don't add fallback tenant here - let the backend handle missing tenant

    return headers;
  }

  /**
   * Set tenant for development purposes
   * Usage: Add ?tenant=your-tenant-slug to URL or call this method
   */
  setDevelopmentTenant(tenantSlug: string): void {
    if (window.location.hostname === 'localhost') {
      // Update URL with tenant parameter
      const url = new URL(window.location.href);
      url.searchParams.set('tenant', tenantSlug);
      window.history.replaceState({}, '', url.toString());

      // Reinitialize tenant
      this.initializeTenant();
    }
  }
}
