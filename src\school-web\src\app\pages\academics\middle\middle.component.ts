import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';
import { DefaultHeroComponent } from '../../../shared/components/default-hero/default-hero.component';

interface Program {
  title: string;
  description: string;
  icon: string;
}

interface Course {
  name: string;
  description: string;
  grade: string;
}

@Component({
  selector: 'app-middle',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatDividerModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './middle.component.html',
  styleUrls: ['./middle.component.scss']
})
export class MiddleComponent {
  // Key features of the middle school program
  keyFeatures: Program[] = [
    {
      title: 'Academic Excellence',
      description: 'Our rigorous academic program challenges students to develop critical thinking, problem-solving, and communication skills across all subject areas.',
      icon: 'school'
    },
    {
      title: 'Advisory Program',
      description: 'Each student is assigned to a faculty advisor who provides academic guidance, social-emotional support, and serves as a liaison between school and home.',
      icon: 'groups'
    },
    {
      title: 'Interdisciplinary Learning',
      description: 'Our curriculum integrates multiple subjects through thematic units and project-based learning, helping students make connections across disciplines.',
      icon: 'hub'
    },
    {
      title: 'Digital Citizenship',
      description: 'Students develop responsible technology use, digital literacy, and online safety skills as they use technology for research, collaboration, and creation.',
      icon: 'computer'
    },
    {
      title: 'Leadership Development',
      description: 'Through student government, community service, and other opportunities, students develop leadership skills and learn to make positive contributions.',
      icon: 'emoji_people'
    },
    {
      title: 'Social-Emotional Learning',
      description: 'Our program addresses the unique developmental needs of adolescents, helping them build self-awareness, relationship skills, and responsible decision-making.',
      icon: 'psychology'
    }
  ];

  // Grade levels
  gradeLevels = [
    {
      name: 'Grade 6',
      description: 'Sixth grade serves as a transition from elementary to middle school, with a focus on developing organizational skills, study habits, and increasing independence while providing appropriate support.',
      subjects: ['Language Arts', 'Mathematics', 'Science', 'Social Studies', 'World Languages', 'Physical Education', 'Arts Rotation', 'Technology']
    },
    {
      name: 'Grade 7',
      description: 'Seventh grade builds on the foundation established in sixth grade, with increased academic rigor and opportunities for students to explore their interests and strengths.',
      subjects: ['Language Arts', 'Pre-Algebra/Algebra I', 'Life Science', 'World Geography', 'World Languages', 'Physical Education', 'Arts Electives', 'Technology']
    },
    {
      name: 'Grade 8',
      description: 'Eighth grade prepares students for the transition to high school, with a focus on advanced academic skills, increased responsibility, and leadership within the middle school community.',
      subjects: ['Language Arts', 'Algebra I/Geometry', 'Physical Science', 'American History', 'World Languages', 'Physical Education', 'Arts Electives', 'Technology']
    }
  ];

  // Sample courses
  sampleCourses: Course[] = [
    {
      name: 'Language Arts',
      description: 'Students develop reading comprehension, analytical thinking, and writing skills through the study of literature, grammar, and various writing genres.',
      grade: 'Grades 6-8'
    },
    {
      name: 'Mathematics',
      description: 'Our math sequence includes Pre-Algebra, Algebra I, and Geometry, with differentiated instruction to meet students at their level and challenge them appropriately.',
      grade: 'Grades 6-8'
    },
    {
      name: 'Science',
      description: 'Through laboratory investigations and research projects, students explore earth science, life science, and physical science concepts while developing scientific inquiry skills.',
      grade: 'Grades 6-8'
    },
    {
      name: 'Social Studies',
      description: 'Students examine ancient civilizations, world geography, and American history, developing research skills, critical thinking, and global awareness.',
      grade: 'Grades 6-8'
    },
    {
      name: 'World Languages',
      description: 'Students can choose from Spanish, French, or Mandarin, developing listening, speaking, reading, and writing skills while exploring the cultures of the target language.',
      grade: 'Grades 6-8'
    },
    {
      name: 'Design Thinking',
      description: 'This project-based course challenges students to identify problems, brainstorm solutions, create prototypes, and implement their ideas across various disciplines.',
      grade: 'Grades 7-8'
    }
  ];

  // Special programs
  specialPrograms = [
    {
      title: 'Outdoor Education',
      description: 'Annual grade-level trips that combine team building, environmental education, and adventure activities to foster personal growth and community connections.',
      image: 'assets/images/academics/middle-outdoor.jpg'
    },
    {
      title: 'STEM Challenge',
      description: 'Interdisciplinary projects that engage students in solving real-world problems using science, technology, engineering, and mathematical principles.',
      image: 'assets/images/academics/middle-stem.jpg'
    },
    {
      title: 'Arts Showcase',
      description: 'Opportunities for students to develop and showcase their talents in visual arts, music, drama, and digital media through performances and exhibitions.',
      image: 'assets/images/academics/middle-arts.jpg'
    },
    {
      title: 'Community Service',
      description: 'Service learning projects that connect classroom learning to community needs, developing empathy, social responsibility, and leadership skills.',
      image: 'assets/images/academics/middle-service.jpg'
    }
  ];

  // Daily schedule
  sampleSchedule = [
    { time: '8:00 - 8:15 AM', activity: 'Advisory' },
    { time: '8:20 - 9:10 AM', activity: 'Period 1' },
    { time: '9:15 - 10:05 AM', activity: 'Period 2' },
    { time: '10:05 - 10:20 AM', activity: 'Break' },
    { time: '10:25 - 11:15 AM', activity: 'Period 3' },
    { time: '11:20 - 12:10 PM', activity: 'Period 4' },
    { time: '12:10 - 12:50 PM', activity: 'Lunch' },
    { time: '12:55 - 1:45 PM', activity: 'Period 5' },
    { time: '1:50 - 2:40 PM', activity: 'Period 6' },
    { time: '2:45 - 3:30 PM', activity: 'Period 7 / Electives' }
  ];

  // Extracurricular activities
  extracurriculars = [
    {
      category: 'Athletics',
      activities: ['Basketball', 'Soccer', 'Track & Field', 'Volleyball', 'Cross Country', 'Tennis']
    },
    {
      category: 'Arts',
      activities: ['Band', 'Choir', 'Drama Club', 'Art Club', 'Digital Media', 'Photography']
    },
    {
      category: 'Academic Clubs',
      activities: ['Math Club', 'Science Olympiad', 'Debate Team', 'Model UN', 'Robotics', 'Coding Club']
    },
    {
      category: 'Student Leadership',
      activities: ['Student Council', 'Peer Mentoring', 'Yearbook Committee', 'Environmental Club', 'Community Service Club']
    }
  ];
}
