import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

export type Language = 'en' | 'bn';

export interface LanguageOption {
  code: Language;
  name: string;
  flag: string;
  direction: 'ltr' | 'rtl';
  locale: string;
}

@Injectable({
  providedIn: 'root'
})
export class LanguageService {
  private readonly LANGUAGE_STORAGE_KEY = 'schoolWebLanguage';
  private currentLanguageSubject = new BehaviorSubject<Language>('en');

  // Available languages configuration
  readonly availableLanguages: LanguageOption[] = [
    {
      code: 'en',
      name: 'English (US)',
      flag: '🇺🇸',
      direction: 'ltr',
      locale: 'en-US'
    },
    {
      code: 'bn',
      name: 'বাংলা (বাংলাদেশ)',
      flag: '🇧🇩',
      direction: 'ltr',
      locale: 'bn-BD'
    }
  ];

  constructor(private translateService: TranslateService) {
    this.initializeLanguage();
  }

  /**
   * Get current language as observable
   */
  get currentLanguage$(): Observable<Language> {
    return this.currentLanguageSubject.asObservable();
  }

  /**
   * Get current language value
   */
  get currentLanguage(): Language {
    return this.currentLanguageSubject.value;
  }

  /**
   * Get current language info
   */
  get currentLanguageInfo(): LanguageOption {
    return this.availableLanguages.find(lang => lang.code === this.currentLanguage) || this.availableLanguages[0];
  }

  /**
   * Initialize language from local storage or browser preference
   */
  private initializeLanguage(): void {
    try {
      // Set up translate service
      this.translateService.setDefaultLang('en');
      this.translateService.addLangs(['en', 'bn']);

      // Try to get language from local storage first
      const storedLanguage = localStorage.getItem(this.LANGUAGE_STORAGE_KEY);

      if (storedLanguage && this.isValidLanguage(storedLanguage)) {
        this.setLanguage(storedLanguage as Language, false);
        console.log('Using language from local storage:', storedLanguage);
      } else {
        // Check browser preference
        const browserLang = this.translateService.getBrowserLang();
        const mappedLang = this.mapBrowserLanguage(browserLang);
        this.setLanguage(mappedLang, true);
        console.log('Using browser/default language:', mappedLang);
      }
    } catch (error) {
      console.error('Error initializing language:', error);
      this.setLanguage('en', true);
    }
  }

  /**
   * Set language
   */
  setLanguage(language: Language, saveToStorage: boolean = true): void {
    if (!this.isValidLanguage(language)) {
      console.error(`Invalid language: ${language}`);
      return;
    }

    // Update the subject
    this.currentLanguageSubject.next(language);

    // Update translate service
    this.translateService.use(language).subscribe({
      next: (translations) => {
        console.log(`Successfully switched to ${language}`, {
          translationKeys: Object.keys(translations).length
        });

        // Apply language to document
        this.applyLanguage(language);

        // Save to storage if requested
        if (saveToStorage) {
          this.saveLanguagePreference(language);
        }
      },
      error: (error) => {
        console.error(`Error switching to language ${language}:`, error);
      }
    });

    console.log('Language set to:', language);
  }

  /**
   * Apply language to document
   */
  private applyLanguage(language: Language): void {
    try {
      const languageInfo = this.availableLanguages.find(lang => lang.code === language);
      if (!languageInfo) return;

      // Set HTML lang attribute for proper language rendering
      document.documentElement.lang = language;
      
      // Set direction attribute
      document.documentElement.dir = languageInfo.direction;
      
      // Add language-specific body class for CSS targeting
      document.body.classList.remove('lang-en', 'lang-bn');
      document.body.classList.add(`lang-${language}`);
      
      console.log('Applied language:', language);
    } catch (error) {
      console.error('Error applying language:', error);
    }
  }

  /**
   * Save language preference to local storage
   */
  private saveLanguagePreference(language: Language): void {
    try {
      localStorage.setItem(this.LANGUAGE_STORAGE_KEY, language);
      console.log('Language preference saved:', language);
    } catch (error) {
      console.error('Error saving language preference:', error);
    }
  }

  /**
   * Check if language is valid
   */
  private isValidLanguage(language: string): boolean {
    return this.availableLanguages.some(lang => lang.code === language);
  }

  /**
   * Map browser language to supported language
   */
  private mapBrowserLanguage(browserLang: string | undefined): Language {
    if (!browserLang) return 'en';

    // Direct match
    if (this.isValidLanguage(browserLang)) {
      return browserLang as Language;
    }

    // Language code mapping
    const langCode = browserLang.split('-')[0].toLowerCase();
    switch (langCode) {
      case 'bn':
        return 'bn';
      case 'en':
      default:
        return 'en';
    }
  }

  /**
   * Get language flag emoji
   */
  getLanguageFlag(language?: Language): string {
    const lang = language || this.currentLanguage;
    const languageInfo = this.availableLanguages.find(l => l.code === lang);
    return languageInfo?.flag || '🇺🇸';
  }

  /**
   * Get language name
   */
  getLanguageName(language?: Language): string {
    const lang = language || this.currentLanguage;
    const languageInfo = this.availableLanguages.find(l => l.code === lang);
    return languageInfo?.name || 'English (US)';
  }

  /**
   * Toggle between available languages
   */
  toggleLanguage(): void {
    const currentIndex = this.availableLanguages.findIndex(lang => lang.code === this.currentLanguage);
    const nextIndex = (currentIndex + 1) % this.availableLanguages.length;
    const nextLanguage = this.availableLanguages[nextIndex].code;
    this.setLanguage(nextLanguage);
  }

  /**
   * Get available languages for forms and components
   */
  getAvailableLanguages(): Observable<{ code: string; name: string }[]> {
    const languages = this.availableLanguages.map(lang => ({
      code: lang.code,
      name: lang.name
    }));
    return new Observable(observer => {
      observer.next(languages);
      observer.complete();
    });
  }
}
