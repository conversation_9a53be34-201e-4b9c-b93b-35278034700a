import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';

import { NoticeService } from '../../../../core/services/notice.service';
import { Notice, CreateNotice, UpdateNotice } from '../../../../core/models/notice.model';


@Component({
  selector: 'app-notice-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTabsModule,
    TranslateModule
  ],
  templateUrl: './notice-form.component.html',
  styleUrls: ['./notice-form.component.scss']
})
export class NoticeFormComponent implements OnInit {
  noticeForm!: FormGroup;
  translationForms: { [key: string]: FormGroup } = {};
  isEditMode = false;
  noticeId?: string;
  isLoading = false;
  isSaving = false;
  error = false;

  categories = [
    { value: 'Academic', label: 'Academic' },
    { value: 'Admission', label: 'Admission' },
    { value: 'Event', label: 'Event' },
    { value: 'Exam', label: 'Exam' },
    { value: 'Holiday', label: 'Holiday' },
    { value: 'General', label: 'General' }
  ];

  priorities = [
    { value: 1, label: 'Low' },
    { value: 2, label: 'Medium' },
    { value: 3, label: 'High' },
    { value: 4, label: 'Urgent' }
  ];

  languages = [
    { code: 'en', name: 'English' },
    { code: 'bn', name: 'Bengali' }
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private noticeService: NoticeService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();

    // Check if we're in edit mode
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.noticeId = params['id'];
        this.loadNotice(params['id']);
      }
    });
  }

  initForm(): void {
    this.noticeForm = this.fb.group({
      title: ['', [Validators.required, Validators.maxLength(200)]],
      content: ['', [Validators.required]],
      category: ['General', [Validators.required]],
      startDate: [new Date(), [Validators.required]],
      endDate: [null],
      priority: [2, [Validators.required]],
      isActive: [true]
    }, { validators: this.dateRangeValidator });

    // Initialize translation forms for each language
    this.languages.forEach(lang => {
      if (lang.code !== 'en') { // Skip English as it's the default
        this.translationForms[lang.code] = this.fb.group({
          title: ['', [Validators.required, Validators.maxLength(200)]],
          content: ['', [Validators.required]]
        });
      }
    });
  }

  loadNotice(id: string): void {
    this.isLoading = true;
    this.error = false;

    this.noticeService.getNoticeById(id).subscribe({
      next: (notice) => {
        this.patchFormValues(notice);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading notice:', error);
        this.error = true;
        this.isLoading = false;
        this.snackBar.open('Failed to load notice', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  patchFormValues(notice: Notice): void {
    this.noticeForm.patchValue({
      title: notice.title,
      content: notice.content,
      category: notice.category,
      startDate: new Date(notice.startDate),
      endDate: notice.endDate ? new Date(notice.endDate) : null,
      priority: notice.priority,
      isActive: notice.isActive
    });

    // Patch translation values if available
    if (notice.translations) {
      notice.translations.forEach(translation => {
        if (this.translationForms[translation.languageCode]) {
          this.translationForms[translation.languageCode].patchValue({
            title: translation.title,
            content: translation.content
          });
        }
      });
    }
  }

  onSubmit(): void {
    if (this.noticeForm.invalid) {
      this.markFormGroupTouched(this.noticeForm);
      return;
    }

    // Check if any translation form is invalid
    let translationsValid = true;
    Object.keys(this.translationForms).forEach(langCode => {
      const form = this.translationForms[langCode];
      // Only validate forms that have been touched or have some content
      if ((form.touched || this.hasFormContent(form)) && form.invalid) {
        this.markFormGroupTouched(form);
        translationsValid = false;
      }
    });

    if (!translationsValid) {
      this.snackBar.open('Please fix the errors in the translation forms', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    this.isSaving = true;

    // Prepare translations
    const translations: Array<{languageCode: string, title: string, content: string}> = [];
    Object.keys(this.translationForms).forEach(langCode => {
      const form = this.translationForms[langCode];
      // Include forms that have been touched or have content and are valid
      if ((form.touched || this.hasFormContent(form)) && form.valid) {
        translations.push({
          languageCode: langCode,
          title: form.value.title,
          content: form.value.content
        });
      }
    });

    if (this.isEditMode && this.noticeId) {
      // Update existing notice
      const formValues = this.noticeForm.value;

      // Ensure dates are properly formatted
      const updateNotice: UpdateNotice = {
        title: formValues.title,
        content: formValues.content,
        category: formValues.category,
        startDate: formValues.startDate,
        endDate: formValues.endDate,
        priority: formValues.priority,
        isActive: formValues.isActive,
        translations
      };

      this.noticeService.updateNotice(this.noticeId, updateNotice).subscribe({
        next: () => {
          this.isSaving = false;
          this.snackBar.open('Notice updated successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/admin/notices']);
        },
        error: (error) => {
          console.error('Error updating notice:', error);
          this.isSaving = false;
          this.snackBar.open('Failed to update notice', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
    } else {
      // Create new notice
      const formValues = this.noticeForm.value;

      // Ensure dates are properly formatted
      const createNotice: CreateNotice = {
        title: formValues.title,
        content: formValues.content,
        category: formValues.category,
        startDate: formValues.startDate,
        endDate: formValues.endDate,
        priority: formValues.priority,
        isActive: formValues.isActive,
        translations
      };

      this.noticeService.createNotice(createNotice).subscribe({
        next: () => {
          this.isSaving = false;
          this.snackBar.open('Notice created successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/admin/notices']);
        },
        error: (error) => {
          console.error('Error creating notice:', error);
          this.isSaving = false;
          this.snackBar.open('Failed to create notice', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  // Helper method to mark all controls in a form group as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if ((control as any).controls) {
        this.markFormGroupTouched(control as FormGroup);
      }
    });
  }

  // Cancel and go back to notices list
  cancel(): void {
    this.router.navigate(['/admin/notices']);
  }

  // Get non-English languages
  getNonEnglishLanguages(): { code: string, name: string }[] {
    return this.languages.filter(lang => lang.code !== 'en');
  }

  // Validator to ensure end date is after start date
  dateRangeValidator = (formGroup: FormGroup): { [key: string]: any } | null => {
    const startDate = formGroup.get('startDate')?.value;
    const endDate = formGroup.get('endDate')?.value;

    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (end < start) {
        return { 'endDateBeforeStartDate': true };
      }
    }

    return null;
  }

  // Check if a form has any content
  hasFormContent(form: FormGroup): boolean {
    let hasContent = false;

    Object.keys(form.controls).forEach(key => {
      const control = form.get(key);
      if (control && control.value) {
        const value = control.value.toString().trim();
        if (value.length > 0) {
          hasContent = true;
        }
      }
    });

    return hasContent;
  }
}
