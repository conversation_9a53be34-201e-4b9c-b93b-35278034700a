import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatStepperModule } from '@angular/material/stepper';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTabsModule } from '@angular/material/tabs';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { TranslateModule } from '@ngx-translate/core';
import { EnhancedHeroComponent } from '../../shared/components/enhanced-hero/enhanced-hero.component';
import { DefaultHeroComponent } from '../../shared/components/default-hero/default-hero.component';

interface FAQ {
  question: string;
  answer: string;
}

interface ScholarshipProgram {
  name: string;
  description: string;
  eligibility: string;
  award: string;
  deadline: string;
}

interface TuitionFee {
  level: string;
  annualFee: number;
  applicationFee: number;
  registrationFee: number;
  additionalCosts: { name: string; amount: number }[];
}

@Component({
  selector: 'app-admissions',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatStepperModule,
    MatExpansionModule,
    MatTabsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    TranslateModule,
    DefaultHeroComponent
  ],
  templateUrl: './admissions.component.html',
  styleUrl: './admissions.component.scss'
})
export class AdmissionsComponent implements OnInit {
  activeSection: string = '';
  showFloatingNav: boolean = false;
  inquiryForm: FormGroup;

  // Admission process steps
  admissionSteps = [
    {
      title: 'Inquiry',
      description: 'Contact our admissions office to learn more about our programs, schedule a tour, or request information.',
      icon: 'contact_support'
    },
    {
      title: 'Application',
      description: 'Complete and submit the online application form along with the required documents and application fee.',
      icon: 'edit_document'
    },
    {
      title: 'Assessment',
      description: 'Students may be required to take placement tests or participate in interviews as part of the admissions process.',
      icon: 'quiz'
    },
    {
      title: 'Decision',
      description: 'Admissions decisions are made based on a holistic review of the application, assessments, and available space.',
      icon: 'fact_check'
    },
    {
      title: 'Enrollment',
      description: 'Upon acceptance, complete the enrollment process by submitting required forms and paying the registration fee.',
      icon: 'how_to_reg'
    }
  ];

  // Application requirements by grade level
  applicationRequirements = {
    elementary: [
      'Completed application form',
      'Birth certificate',
      'Previous school records (if applicable)',
      'Immunization records',
      'Application fee payment'
    ],
    middle: [
      'Completed application form',
      'Previous school records (2 years)',
      'Standardized test scores (if available)',
      'Teacher recommendation letter',
      'Immunization records',
      'Application fee payment'
    ],
    high: [
      'Completed application form',
      'Previous school records (3 years)',
      'Standardized test scores',
      'Two teacher recommendation letters',
      'Personal statement/essay',
      'Interview with admissions team',
      'Immunization records',
      'Application fee payment'
    ]
  };

  // Important dates
  importantDates = [
    {
      event: 'Application Opens',
      date: 'September 1, 2025',
      description: 'Online application portal opens for the 2026-2027 academic year.'
    },
    {
      event: 'Open House Events',
      date: 'October - December 2025',
      description: 'Multiple opportunities to visit campus, meet faculty, and learn about our programs.'
    },
    {
      event: 'Application Deadline (Priority)',
      date: 'January 15, 2026',
      description: 'Priority consideration for admission and financial aid for applications received by this date.'
    },
    {
      event: 'Application Deadline (Regular)',
      date: 'March 1, 2026',
      description: 'Final deadline for applications for the 2026-2027 academic year.'
    },
    {
      event: 'Admission Decisions',
      date: 'March 15, 2026',
      description: 'Admission decisions sent to families who applied by the regular deadline.'
    },
    {
      event: 'Enrollment Deadline',
      date: 'April 15, 2026',
      description: 'Deadline to confirm enrollment and submit registration fee.'
    }
  ];

  // Scholarship programs
  scholarshipPrograms: ScholarshipProgram[] = [
    {
      name: 'Academic Excellence Scholarship',
      description: 'Awarded to students who demonstrate exceptional academic achievement and potential.',
      eligibility: 'Students with outstanding academic records, standardized test scores, and teacher recommendations.',
      award: 'Up to 50% of tuition',
      deadline: 'January 15, 2026'
    },
    {
      name: 'Leadership Scholarship',
      description: 'Recognizes students who have shown exceptional leadership abilities in their schools or communities.',
      eligibility: 'Students with demonstrated leadership experience and strong character references.',
      award: 'Up to 30% of tuition',
      deadline: 'January 15, 2026'
    },
    {
      name: 'Arts & Music Scholarship',
      description: 'Supports talented students in visual arts, music, theater, or dance.',
      eligibility: 'Students with exceptional artistic abilities, portfolio or audition required.',
      award: 'Up to 40% of tuition',
      deadline: 'January 15, 2026'
    },
    {
      name: 'Need-Based Financial Aid',
      description: 'Provides assistance to families who demonstrate financial need.',
      eligibility: 'Based on family financial information and circumstances.',
      award: 'Varies based on demonstrated need',
      deadline: 'January 15, 2026'
    }
  ];

  // Tuition and fees
  tuitionFees: TuitionFee[] = [
    {
      level: 'Elementary School (K-5)',
      annualFee: 15000,
      applicationFee: 100,
      registrationFee: 500,
      additionalCosts: [
        { name: 'Technology Fee', amount: 300 },
        { name: 'Books and Materials', amount: 400 },
        { name: 'Field Trips', amount: 250 }
      ]
    },
    {
      level: 'Middle School (6-8)',
      annualFee: 18000,
      applicationFee: 100,
      registrationFee: 500,
      additionalCosts: [
        { name: 'Technology Fee', amount: 400 },
        { name: 'Books and Materials', amount: 500 },
        { name: 'Field Trips', amount: 350 }
      ]
    },
    {
      level: 'High School (9-12)',
      annualFee: 22000,
      applicationFee: 100,
      registrationFee: 500,
      additionalCosts: [
        { name: 'Technology Fee', amount: 500 },
        { name: 'Books and Materials', amount: 600 },
        { name: 'Field Trips', amount: 450 },
        { name: 'College Counseling', amount: 300 }
      ]
    }
  ];

  // FAQs
  faqs: FAQ[] = [
    {
      question: 'What is the student-to-teacher ratio?',
      answer: 'Our school maintains a low student-to-teacher ratio of approximately 15:1, allowing for personalized attention and support for each student.'
    },
    {
      question: 'Do you offer transportation services?',
      answer: 'Yes, we provide bus transportation for students living within a 15-mile radius of the school. Transportation fees are not included in tuition and vary based on distance.'
    },
    {
      question: 'Is financial aid available?',
      answer: 'Yes, we offer need-based financial aid and merit scholarships. Families must complete a financial aid application by January 15 to be considered for assistance.'
    },
    {
      question: 'What are the school hours?',
      answer: 'School hours are from 8:00 AM to 3:30 PM, Monday through Friday. Extended day programs are available from 7:00 AM to 6:00 PM for an additional fee.'
    },
    {
      question: 'Do you accept mid-year transfers?',
      answer: 'Yes, we accept mid-year transfers based on space availability. Please contact the admissions office to discuss the process for mid-year enrollment.'
    },
    {
      question: 'What support services do you offer for students with learning differences?',
      answer: 'We have a dedicated learning support team that provides accommodations and support for students with mild to moderate learning differences. Additional fees may apply for specialized services.'
    },
    {
      question: 'Is there a sibling discount?',
      answer: 'Yes, we offer a 10% tuition discount for siblings of currently enrolled students.'
    },
    {
      question: 'What is your acceptance rate?',
      answer: 'Our acceptance rate varies by grade level and year, typically ranging from 60-75% of applicants. We look for students who will thrive in our academic environment and contribute positively to our community.'
    }
  ];

  constructor(private fb: FormBuilder) {
    this.inquiryForm = this.fb.group({
      parentName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', Validators.required],
      studentName: ['', Validators.required],
      gradeLevel: ['', Validators.required],
      currentSchool: [''],
      message: [''],
      howHeard: [''],
      interestAreas: ['']
    });
  }

  ngOnInit(): void {
    // Initialize the component
  }

  @HostListener('window:scroll', [])
  onWindowScroll() {
    // Show floating nav after scrolling past the hero section
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
    this.showFloatingNav = scrollPosition > 300;

    // Determine active section based on scroll position
    this.updateActiveSection();
  }

  updateActiveSection(): void {
    const sections = ['process', 'requirements', 'tuition', 'scholarships', 'dates', 'faq', 'inquiry'];

    for (const section of sections) {
      const element = document.getElementById(section);
      if (element) {
        const rect = element.getBoundingClientRect();
        if (rect.top <= 100 && rect.bottom >= 100) {
          this.activeSection = section;
          break;
        }
      }
    }
  }

  scrollToSection(sectionId: string): void {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  submitInquiry(): void {
    if (this.inquiryForm.valid) {
      console.log('Form submitted:', this.inquiryForm.value);
      // Here you would typically send the form data to a server
      // Reset the form after submission
      this.inquiryForm.reset();
      // Show success message
      alert('Thank you for your inquiry. We will contact you soon.');
    } else {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.inquiryForm.controls).forEach(key => {
        const control = this.inquiryForm.get(key);
        control?.markAsTouched();
      });
    }
  }

  // Helper method to format currency
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  }
}
