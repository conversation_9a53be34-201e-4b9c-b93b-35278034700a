<div class="dashboard-container">
  <h1 class="page-title">Parent Dashboard</h1>

  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error" class="error-container">
    <mat-card>
      <mat-card-content>
        <p>Unable to load parent data. Please try again later.</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" (click)="loadParentData()">Retry</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div *ngIf="!loading && !error && parent" class="dashboard-content">
    <!-- Parent Info Card -->
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>Parent Information</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">Name:</span>
            <span class="info-value">{{ parent.firstName }} {{ parent.lastName }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Email:</span>
            <span class="info-value">{{ parent.email }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Phone:</span>
            <span class="info-value">{{ parent.phone }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Alternate Phone:</span>
            <span class="info-value">{{ parent.alternatePhone || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Occupation:</span>
            <span class="info-value">{{ parent.occupation || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Address:</span>
            <span class="info-value">{{ parent.address || 'N/A' }}</span>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary" routerLink="/parent-portal/profile">Edit Profile</button>
      </mat-card-actions>
    </mat-card>

    <!-- Students Cards -->
    <div *ngIf="parent.students.length === 0" class="no-students">
      <mat-card>
        <mat-card-content>
          <p>No students associated with this parent account.</p>
        </mat-card-content>
      </mat-card>
    </div>

    <div *ngFor="let studentParent of parent.students" class="student-card">
      <mat-card>
        <mat-card-header>
          <div mat-card-avatar class="student-avatar">
            <img *ngIf="studentParent.student.profileImage" [src]="studentParent.student.profileImage.filePath" alt="Student Photo">
            <mat-icon *ngIf="!studentParent.student.profileImage">account_circle</mat-icon>
          </div>
          <mat-card-title>{{ studentParent.student.firstName }} {{ studentParent.student.lastName }}</mat-card-title>
          <mat-card-subtitle>
            Class {{ studentParent.student.currentGrade }}-{{ studentParent.student.section }} | 
            Roll: {{ studentParent.student.rollNumber }} | 
            ID: {{ studentParent.student.studentId }}
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="student-info-grid">
            <div class="student-info-item">
              <span class="info-label">Medium:</span>
              <span class="info-value">{{ studentParent.student.medium === 0 ? 'Bengali' : 'English' }}</span>
            </div>
            <div class="student-info-item">
              <span class="info-label">Shift:</span>
              <span class="info-value">{{ 
                studentParent.student.shift === 0 ? 'Morning' : 
                studentParent.student.shift === 1 ? 'Day' : 'Evening' 
              }}</span>
            </div>
            <div class="student-info-item">
              <span class="info-label">Academic Year:</span>
              <span class="info-value">{{ studentParent.student.academicYear }}</span>
            </div>
            <div class="student-info-item">
              <span class="info-label">Relation:</span>
              <span class="info-value">{{ 
                studentParent.relationType === 0 ? 'Father' : 
                studentParent.relationType === 1 ? 'Mother' : 
                studentParent.relationType === 2 ? 'Guardian' : 'Other' 
              }}</span>
            </div>
            <div class="student-info-item">
              <span class="info-label">Primary Contact:</span>
              <span class="info-value">{{ studentParent.isPrimaryContact ? 'Yes' : 'No' }}</span>
            </div>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <button mat-button color="primary" [routerLink]="getStudentRoute(studentParent.studentId, 'attendance')">
            <mat-icon>event_available</mat-icon> Attendance
          </button>
          <button mat-button color="primary" [routerLink]="getStudentRoute(studentParent.studentId, 'fees')">
            <mat-icon>payment</mat-icon> Fees
          </button>
          <button mat-button color="primary" [routerLink]="getStudentRoute(studentParent.studentId, 'results')">
            <mat-icon>assessment</mat-icon> Results
          </button>
          <button mat-button color="primary" [routerLink]="getStudentRoute(studentParent.studentId, 'leaves')">
            <mat-icon>event_busy</mat-icon> Leaves
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</div>
