using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class FacultyAwardConfiguration : IEntityTypeConfiguration<FacultyAward>
{
    public void Configure(EntityTypeBuilder<FacultyAward> builder)
    {
        builder.Property(t => t.Name)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(t => t.Organization)
            .HasMaxLength(200);

        builder.HasOne(t => t.Faculty)
            .WithMany(t => t.Awards)
            .HasForeignKey(t => t.FacultyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasQueryFilter(fa => !fa.IsDeleted);
    }
}
