.dashboard-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-card {
  margin-bottom: 16px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
}

.student-card {
  margin-bottom: 16px;
}

.student-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  mat-icon {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #757575;
  }
}

.student-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.student-info-item {
  display: flex;
  flex-direction: column;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.no-students {
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
}

mat-card-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

@media (max-width: 768px) {
  .info-grid,
  .student-info-grid {
    grid-template-columns: 1fr;
  }
}
