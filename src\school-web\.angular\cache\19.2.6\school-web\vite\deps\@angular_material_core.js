import {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MatNativeDateModule,
  NativeDateAdapter,
  NativeDateModule,
  VERSION,
  provideNativeDateAdapter
} from "./chunk-36CSAXNY.js";
import {
  MatLine,
  MatLineModule,
  setLines
} from "./chunk-4LT4CQ76.js";
import {
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition
} from "./chunk-JYQJKYF5.js";
import {
  MatPseudoCheckbox,
  MatPseudoCheckboxModule
} from "./chunk-476ZWWU3.js";
import {
  _MatInternalFormField
} from "./chunk-LLXQ6I4S.js";
import {
  _ErrorStateTracker
} from "./chunk-JXBYLQRD.js";
import {
  ErrorStateMatcher,
  ShowOnDirtyErrorStateMatcher
} from "./chunk-2ZBKODAN.js";
import {
  MatRippleLoader
} from "./chunk-ZXBBTRBG.js";
import {
  MatRippleModule
} from "./chunk-YFFWYPDS.js";
import {
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatRipple,
  RippleRef,
  RippleRenderer,
  RippleState,
  defaultRippleAnimationConfig
} from "./chunk-UZ4XLZVX.js";
import "./chunk-Q7P257OE.js";
import "./chunk-F5YF3NDX.js";
import "./chunk-SZS4RJEH.js";
import {
  _StructuralStylesLoader
} from "./chunk-OVWAZMGZ.js";
import "./chunk-2AA2HD2T.js";
import "./chunk-CIGKH54X.js";
import "./chunk-TRES2BGH.js";
import {
  MATERIAL_SANITY_CHECKS,
  MatCommonModule
} from "./chunk-HREK5O4N.js";
import "./chunk-M3HR6BUY.js";
import "./chunk-5H5MO7KS.js";
import "./chunk-3QK5PMD7.js";
import "./chunk-BB6JFOEG.js";
import "./chunk-DRO7G5KZ.js";
import "./chunk-P52WORV2.js";
import "./chunk-ABKFZ3BG.js";
import "./chunk-HJS2BXAE.js";
import "./chunk-6Q4RANH6.js";
import "./chunk-FFZIAYYX.js";
import "./chunk-CXCX2JKZ.js";
export {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  ErrorStateMatcher,
  MATERIAL_SANITY_CHECKS,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatCommonModule,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  MatRipple,
  MatRippleLoader,
  MatRippleModule,
  NativeDateAdapter,
  NativeDateModule,
  RippleRef,
  RippleRenderer,
  RippleState,
  ShowOnDirtyErrorStateMatcher,
  VERSION,
  _ErrorStateTracker,
  _MatInternalFormField,
  _StructuralStylesLoader,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition,
  defaultRippleAnimationConfig,
  provideNativeDateAdapter,
  setLines
};
