import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-tenant-inactive',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    RouterModule,
    TranslateModule
  ],
  template: `
    <div class="tenant-inactive-container">
      <mat-card class="inactive-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>block</mat-icon>
            School Account Inactive
          </mat-card-title>
          <mat-card-subtitle>
            This school account is currently inactive
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p>The school account has been deactivated. This could be due to:</p>
          <ul>
            <li>Subscription expiration</li>
            <li>Administrative suspension</li>
            <li>Account maintenance</li>
          </ul>
          <p>Please contact your system administrator for assistance.</p>
          <div class="actions">
            <button mat-raised-button color="primary" routerLink="/login">
              <mat-icon>arrow_back</mat-icon>
              Back to Login
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .tenant-inactive-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
      padding: 20px;
      z-index: 9999;
      overflow-y: auto;
    }
    .inactive-card {
      max-width: 500px;
      width: 100%;
      text-align: center;
    }
    mat-card-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    ul {
      text-align: left;
      margin: 16px 0;
    }
    .actions {
      margin-top: 24px;
    }
  `]
})
export class TenantInactiveComponent {}
