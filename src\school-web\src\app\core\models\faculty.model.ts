import { MediaItem } from './media.model';
import { GenderType } from './student.model';

/**
 * Faculty model aligned with the API FacultyDto
 */
export interface Faculty {
  id: number;
  // Basic information
  name: string;
  title: string;
  email: string;
  phone: string;
  office: string;
  biography: string;
  shortBio: string;
  joinedYear: number;
  isFeatured: boolean;
  isActive: boolean;
  displayOrder: number;
  department: string;

  // Social links
  website?: string;
  linkedIn?: string;
  twitter?: string;
  researchGate?: string;

  // Image
  profileImageId?: number;
  profileImageUrl?: string;

  // Timestamps
  createdAt: Date;
  updatedAt?: Date;

  // Related collections
  translations?: FacultyTranslation[];
  education?: FacultyEducation[];
  specializations?: FacultySpecialization[];
  courses?: FacultyCourse[];
  publications?: FacultyPublication[];
  awards?: FacultyAward[];

  // Legacy properties for backward compatibility
  employeeId?: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  dateOfBirth?: Date;
  gender?: GenderType;
  alternatePhone?: string;
  address?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelation?: string;
  joiningDate?: Date;
  designation?: string;
  isClassTeacher?: boolean;
  assignedGrade?: number;
  assignedSection?: string;
  userId?: string;
  profileImage?: MediaItem;
}

/**
 * Faculty detail model with additional information
 */
export interface FacultyDetail extends Faculty {
  assignedSubjects?: FacultySubject[];
  assignedStudents?: FacultyStudent[];
  schedule?: FacultySchedule[];
  qualifications?: string[];
}

/**
 * Faculty translation model aligned with FacultyTranslationDto
 */
export interface FacultyTranslation {
  id: number;
  facultyId: number;
  languageCode: string;
  name: string;
  title: string;
  biography: string;
  shortBio: string;
}

/**
 * Faculty education model aligned with FacultyEducationDto
 */
export interface FacultyEducation {
  id: number;
  facultyId: number;
  degree: string;
  institution: string;
  year?: number;
  displayOrder: number;
  translations?: FacultyEducationTranslation[];
  major?: string;
}

/**
 * Faculty education translation model aligned with FacultyEducationTranslationDto
 */
export interface FacultyEducationTranslation {
  id: number;
  facultyEducationId: number;
  languageCode: string;
  degree: string;
  institution: string;
}

/**
 * Faculty specialization model aligned with FacultySpecializationDto
 */
export interface FacultySpecialization {
  id: number;
  facultyId: number;
  name: string;
  displayOrder: number;
  translations?: FacultySpecializationTranslation[];
  description?: string;
}

/**
 * Faculty specialization translation model aligned with FacultySpecializationTranslationDto
 */
export interface FacultySpecializationTranslation {
  id: number;
  facultySpecializationId: number;
  languageCode: string;
  name: string;
}

/**
 * Faculty course model aligned with FacultyCourseDto
 */
export interface FacultyCourse {
  id: number;
  facultyId: number;
  name: string;
  description?: string;
  displayOrder: number;
  code?: string;
}

/**
 * Faculty publication model aligned with FacultyPublicationDto
 */
export interface FacultyPublication {
  id: number;
  facultyId: number;
  title: string;
  journal?: string;
  year?: number;
  url?: string;
  displayOrder: number;
}

/**
 * Faculty award model aligned with FacultyAwardDto
 */
export interface FacultyAward {
  id: number;
  facultyId: number;
  name: string;
  year?: number;
  organization?: string;
  displayOrder: number;
  description?: string;
}

/**
 * Faculty subject model for assigned subjects
 */
export interface FacultySubject {
  id: number;
  facultyId: number;
  subjectCode: string;
  subjectName: string;
  grade: number;
  section: string;
  academicYear: number;
}

export interface FacultyStudent {
  id: number;
  studentId: number;
  firstName: string;
  lastName: string;
  fullName?: string;
  studentIdForYear: string;
  rollNumber: number;
  grade: number;
  section: string;
}

export interface FacultySchedule {
  id: number;
  facultyId: number;
  day: number;
  periodNumber: number;
  startTime: string;
  endTime: string;
  subjectCode: string;
  subjectName: string;
  grade: number;
  section: string;
  roomNumber: string;
}

/**
 * Create faculty model aligned with FacultyCreateDto
 */
export interface CreateFaculty {
  name: string;
  title: string;
  email: string;
  phone: string;
  office: string;
  biography: string;
  shortBio: string;
  joinedYear: number;
  isFeatured: boolean;
  isActive: boolean;
  displayOrder: number;
  department: string;

  // Social links
  website?: string;
  linkedIn?: string;
  twitter?: string;
  researchGate?: string;

  // Image
  profileImageId?: number;

  // Related collections
  translations?: CreateFacultyTranslation[];
  education?: CreateFacultyEducation[];
  specializations?: CreateFacultySpecialization[];
  courses?: CreateFacultyCourse[];
  publications?: CreateFacultyPublication[];
  awards?: CreateFacultyAward[];

  // Legacy properties for backward compatibility
  employeeId?: string;
  firstName?: string;
  lastName?: string;
  dateOfBirth?: Date;
  gender?: GenderType;
  alternatePhone?: string;
  address?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelation?: string;
  joiningDate?: Date;
  designation?: string;
  isClassTeacher?: boolean;
  assignedGrade?: number;
  assignedSection?: string;
  userId?: string;
}

/**
 * Create faculty translation model aligned with FacultyTranslationCreateDto
 */
export interface CreateFacultyTranslation {
  languageCode: string;
  name: string;
  title: string;
  biography: string;
  shortBio: string;
}

/**
 * Create faculty education model aligned with FacultyEducationCreateDto
 */
export interface CreateFacultyEducation {
  degree: string;
  institution: string;
  year?: number;
  displayOrder: number;
  translations?: CreateFacultyEducationTranslation[];
}

/**
 * Create faculty education translation model aligned with FacultyEducationTranslationCreateDto
 */
export interface CreateFacultyEducationTranslation {
  languageCode: string;
  degree: string;
  institution: string;
}

/**
 * Create faculty specialization model aligned with FacultySpecializationCreateDto
 */
export interface CreateFacultySpecialization {
  name: string;
  displayOrder: number;
  translations?: CreateFacultySpecializationTranslation[];
}

/**
 * Create faculty specialization translation model aligned with FacultySpecializationTranslationCreateDto
 */
export interface CreateFacultySpecializationTranslation {
  languageCode: string;
  name: string;
}

/**
 * Create faculty course model aligned with FacultyCourseCreateDto
 */
export interface CreateFacultyCourse {
  name: string;
  description?: string;
  displayOrder: number;
}

/**
 * Create faculty publication model aligned with FacultyPublicationCreateDto
 */
export interface CreateFacultyPublication {
  title: string;
  journal?: string;
  year?: number;
  url?: string;
  displayOrder: number;
}

/**
 * Create faculty award model aligned with FacultyAwardCreateDto
 */
export interface CreateFacultyAward {
  name: string;
  year?: number;
  organization?: string;
  displayOrder: number;
}

/**
 * Update faculty model aligned with FacultyUpdateDto
 */
export interface UpdateFaculty {
  name?: string;
  title?: string;
  email?: string;
  phone?: string;
  office?: string;
  biography?: string;
  shortBio?: string;
  joinedYear?: number;
  isFeatured?: boolean;
  isActive?: boolean;
  displayOrder?: number;
  department?: string;

  // Social links
  website?: string;
  linkedIn?: string;
  twitter?: string;
  researchGate?: string;

  // Image
  profileImageId?: number;

  // Related collections
  translations?: UpdateFacultyTranslation[];
  education?: UpdateFacultyEducation[];
  specializations?: UpdateFacultySpecialization[];
  courses?: UpdateFacultyCourse[];
  publications?: UpdateFacultyPublication[];
  awards?: UpdateFacultyAward[];

  // Legacy properties for backward compatibility
  firstName?: string;
  lastName?: string;
  dateOfBirth?: Date;
  gender?: GenderType;
  alternatePhone?: string;
  address?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelation?: string;
  designation?: string;
  isClassTeacher?: boolean;
  assignedGrade?: number;
  assignedSection?: string;
}

/**
 * Update faculty translation model aligned with FacultyTranslationUpdateDto
 */
export interface UpdateFacultyTranslation {
  languageCode: string;
  name?: string;
  title?: string;
  biography?: string;
  shortBio?: string;
}

/**
 * Update faculty education model aligned with FacultyEducationUpdateDto
 */
export interface UpdateFacultyEducation {
  degree?: string;
  institution?: string;
  year?: number;
  displayOrder?: number;
}

/**
 * Update faculty education translation model aligned with FacultyEducationTranslationUpdateDto
 */
export interface UpdateFacultyEducationTranslation {
  degree?: string;
  institution?: string;
}

/**
 * Update faculty specialization model aligned with FacultySpecializationUpdateDto
 */
export interface UpdateFacultySpecialization {
  name?: string;
  displayOrder?: number;
}

/**
 * Update faculty specialization translation model aligned with FacultySpecializationTranslationUpdateDto
 */
export interface UpdateFacultySpecializationTranslation {
  name?: string;
}

/**
 * Update faculty course model aligned with FacultyCourseUpdateDto
 */
export interface UpdateFacultyCourse {
  name?: string;
  description?: string;
  displayOrder?: number;
}

/**
 * Update faculty publication model aligned with FacultyPublicationUpdateDto
 */
export interface UpdateFacultyPublication {
  title?: string;
  journal?: string;
  year?: number;
  url?: string;
  displayOrder?: number;
}

/**
 * Update faculty award model aligned with FacultyAwardUpdateDto
 */
export interface UpdateFacultyAward {
  name?: string;
  year?: number;
  organization?: string;
  displayOrder?: number;
}

/**
 * Faculty filter model aligned with FacultyFilterDto
 */
export interface FacultyFilter {
  name?: string;
  department?: string;
  isActive?: boolean;
  isFeatured?: boolean;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: string;

  // Legacy properties for backward compatibility
  employeeId?: string;
  designation?: string;
  isClassTeacher?: boolean;
}

export enum DayOfWeek {
  Sunday = 0,
  Monday = 1,
  Tuesday = 2,
  Wednesday = 3,
  Thursday = 4,
  Friday = 5,
  Saturday = 6
}
