using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Domain.Entities;
using School.Domain.Enums;

namespace School.Application.Features.ClassTeacher;

/// <summary>
/// Service implementation for ClassTeacher management operations
/// </summary>
public class ClassTeacherService : IClassTeacherService
{
    private readonly IApplicationDbContext _context;
    private readonly IMapper _mapper;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<ClassTeacherService> _logger;

    public ClassTeacherService(
        IApplicationDbContext context,
        IMapper mapper,
        ICurrentUserService currentUserService,
        ILogger<ClassTeacherService> logger)
    {
        _context = context;
        _mapper = mapper;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<(IEnumerable<ClassTeacherDto> ClassTeachers, int TotalCount)> GetAllClassTeachersAsync(ClassTeacherFilterDto filter)
    {
        // Global query filters automatically handle tenant isolation
        var query = _context.ClassTeachers
            .Include(ct => ct.Faculty)
            .Include(ct => ct.Section)
                .ThenInclude(s => s.Grade)
            .Include(ct => ct.AcademicYear)
            .Include(ct => ct.Term)
            .Include(ct => ct.Students)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(filter.SearchTerm))
        {
            query = query.Where(ct => ct.Faculty.Name.Contains(filter.SearchTerm) ||
                                    ct.Section.Name.Contains(filter.SearchTerm) ||
                                    ct.Section.Grade.Name.Contains(filter.SearchTerm));
        }

        if (filter.FacultyId.HasValue)
        {
            query = query.Where(ct => ct.FacultyId == filter.FacultyId.Value);
        }

        if (filter.SectionId.HasValue)
        {
            query = query.Where(ct => ct.SectionId == filter.SectionId.Value);
        }

        if (filter.GradeId.HasValue)
        {
            query = query.Where(ct => ct.Section.GradeId == filter.GradeId.Value);
        }

        if (filter.AcademicYearId.HasValue)
        {
            query = query.Where(ct => ct.AcademicYearId == filter.AcademicYearId.Value);
        }

        if (filter.TermId.HasValue)
        {
            query = query.Where(ct => ct.TermId == filter.TermId.Value);
        }

        if (filter.IsActive.HasValue)
        {
            query = query.Where(ct => ct.IsActive == filter.IsActive.Value);
        }

        if (filter.IsPrimary.HasValue)
        {
            query = query.Where(ct => ct.IsPrimary == filter.IsPrimary.Value);
        }

        if (filter.Status.HasValue)
        {
            query = query.Where(ct => ct.Status == filter.Status.Value);
        }

        if (filter.StartDateFrom.HasValue)
        {
            query = query.Where(ct => ct.StartDate >= filter.StartDateFrom.Value);
        }

        if (filter.StartDateTo.HasValue)
        {
            query = query.Where(ct => ct.StartDate <= filter.StartDateTo.Value);
        }

        if (filter.EndDateFrom.HasValue)
        {
            query = query.Where(ct => ct.EndDate >= filter.EndDateFrom.Value);
        }

        if (filter.EndDateTo.HasValue)
        {
            query = query.Where(ct => ct.EndDate <= filter.EndDateTo.Value);
        }

        // Apply sorting
        query = filter.SortBy.ToLower() switch
        {
            "facultyname" => filter.SortDescending
                ? query.OrderByDescending(ct => ct.Faculty.Name)
                : query.OrderBy(ct => ct.Faculty.Name),
            "sectionname" => filter.SortDescending 
                ? query.OrderByDescending(ct => ct.Section.Name) 
                : query.OrderBy(ct => ct.Section.Name),
            "startdate" => filter.SortDescending 
                ? query.OrderByDescending(ct => ct.StartDate) 
                : query.OrderBy(ct => ct.StartDate),
            "enddate" => filter.SortDescending 
                ? query.OrderByDescending(ct => ct.EndDate) 
                : query.OrderBy(ct => ct.EndDate),
            _ => query.OrderByDescending(ct => ct.StartDate)
        };

        var totalCount = await query.CountAsync();

        var classTeachers = await query
            .Skip((filter.Page - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        var classTeacherDtos = _mapper.Map<IEnumerable<ClassTeacherDto>>(classTeachers);

        return (classTeacherDtos, totalCount);
    }

    public async Task<ClassTeacherDto?> GetClassTeacherByIdAsync(Guid id)
    {
        // Global query filters automatically handle tenant isolation
        var classTeacher = await _context.ClassTeachers
            .Where(ct => ct.Id == id)
            .Include(ct => ct.Faculty)
            .Include(ct => ct.Section)
                .ThenInclude(s => s.Grade)
            .Include(ct => ct.AcademicYear)
            .Include(ct => ct.Term)
            .Include(ct => ct.Students)
            .FirstOrDefaultAsync();

        return classTeacher != null ? _mapper.Map<ClassTeacherDto>(classTeacher) : null;
    }

    public async Task<ClassTeacherDto?> GetClassTeacherBySectionAsync(Guid sectionId)
    {
        // Global query filters automatically handle tenant isolation
        var classTeacher = await _context.ClassTeachers
            .Where(ct => ct.SectionId == sectionId && ct.IsActive && ct.IsPrimary)
            .Include(ct => ct.Faculty)
            .Include(ct => ct.Section)
                .ThenInclude(s => s.Grade)
            .Include(ct => ct.AcademicYear)
            .Include(ct => ct.Term)
            .Include(ct => ct.Students)
            .FirstOrDefaultAsync();

        return classTeacher != null ? _mapper.Map<ClassTeacherDto>(classTeacher) : null;
    }

    public async Task<IEnumerable<ClassTeacherDto>> GetClassTeachersByFacultyAsync(Guid facultyId)
    {
        // Global query filters automatically handle tenant isolation
        var classTeachers = await _context.ClassTeachers
            .Where(ct => ct.FacultyId == facultyId)
            .Include(ct => ct.Faculty)
            .Include(ct => ct.Section)
                .ThenInclude(s => s.Grade)
            .Include(ct => ct.AcademicYear)
            .Include(ct => ct.Term)
            .OrderByDescending(ct => ct.StartDate)
            .ToListAsync();

        return _mapper.Map<IEnumerable<ClassTeacherDto>>(classTeachers);
    }

    public async Task<IEnumerable<ClassTeacherDto>> GetClassTeachersByAcademicYearAsync(Guid academicYearId)
    {
        // Global query filters automatically handle tenant isolation
        var classTeachers = await _context.ClassTeachers
            .Where(ct => ct.AcademicYearId == academicYearId)
            .Include(ct => ct.Faculty)
            .Include(ct => ct.Section)
                .ThenInclude(s => s.Grade)
            .Include(ct => ct.AcademicYear)
            .Include(ct => ct.Term)
            .OrderBy(ct => ct.Section.Grade.Level)
            .ThenBy(ct => ct.Section.Name)
            .ToListAsync();

        return _mapper.Map<IEnumerable<ClassTeacherDto>>(classTeachers);
    }

    public async Task<IEnumerable<ClassTeacherDto>> GetActiveClassTeachersAsync(Guid academicYearId)
    {
        // Global query filters automatically handle tenant isolation
        var classTeachers = await _context.ClassTeachers
            .Where(ct => ct.AcademicYearId == academicYearId && ct.IsActive)
            .Include(ct => ct.Faculty)
            .Include(ct => ct.Section)
                .ThenInclude(s => s.Grade)
            .Include(ct => ct.AcademicYear)
            .Include(ct => ct.Term)
            .OrderBy(ct => ct.Section.Grade.Level)
            .ThenBy(ct => ct.Section.Name)
            .ToListAsync();

        return _mapper.Map<IEnumerable<ClassTeacherDto>>(classTeachers);
    }

    public async Task<Guid> CreateClassTeacherAsync(CreateClassTeacherDto classTeacherDto)
    {
        var userId = _currentUserService.UserId;

        var classTeacher = _mapper.Map<Domain.Entities.ClassTeacher>(classTeacherDto);        classTeacher.CreatedBy = userId?.ToString();
        classTeacher.CreatedAt = DateTime.UtcNow;

        _context.ClassTeachers.Add(classTeacher);
        await _context.SaveChangesAsync();

        _logger.LogInformation("ClassTeacher assignment created successfully for faculty {FacultyId} and section {SectionId}", 
            classTeacher.FacultyId, classTeacher.SectionId);

        return classTeacher.Id;
    }

    public async Task<bool> UpdateClassTeacherAsync(Guid id, UpdateClassTeacherDto classTeacherDto)
    {
        var userId = _currentUserService.UserId;

        // Global query filters automatically handle tenant isolation
        var classTeacher = await _context.ClassTeachers
            .Where(ct => ct.Id == id)
            .FirstOrDefaultAsync();

        if (classTeacher == null)
        {
            return false;
        }

        _mapper.Map(classTeacherDto, classTeacher);
        classTeacher.LastModifiedBy = userId?.ToString();
        classTeacher.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("ClassTeacher {ClassTeacherId} updated successfully", id);

        return true;
    }

    public async Task<bool> DeleteClassTeacherAsync(Guid id)
    {
        // Global query filters automatically handle tenant isolation
        var classTeacher = await _context.ClassTeachers
            .Where(ct => ct.Id == id)
            .FirstOrDefaultAsync();

        if (classTeacher == null)
        {
            return false;
        }

        _context.ClassTeachers.Remove(classTeacher);
        await _context.SaveChangesAsync();

        _logger.LogInformation("ClassTeacher {ClassTeacherId} deleted successfully", id);

        return true;
    }

    public async Task<bool> AssignClassTeacherAsync(Guid facultyId, Guid sectionId, Guid academicYearId, DateTime? startDate = null)
    {
        var userId = _currentUserService.UserId;

        var classTeacher = new Domain.Entities.ClassTeacher
        {

            FacultyId = facultyId,
            SectionId = sectionId,
            AcademicYearId = academicYearId,
            StartDate = startDate ?? DateTime.UtcNow,
            IsActive = true,
            IsPrimary = true,
            Status = ClassTeacherStatus.Active,
            CreatedBy = userId?.ToString(),
            CreatedAt = DateTime.UtcNow
        };

        _context.ClassTeachers.Add(classTeacher);
        await _context.SaveChangesAsync();

        _logger.LogInformation("ClassTeacher assigned successfully for faculty {FacultyId} and section {SectionId}", 
            facultyId, sectionId);

        return true;
    }

    public async Task<bool> ReassignClassTeacherAsync(Guid sectionId, Guid newFacultyId, string reason = "")
    {
        var userId = _currentUserService.UserId;

        // Deactivate current assignment
        var currentAssignment = await _context.ClassTeachers
            .Where(ct => ct.SectionId == sectionId && ct.IsActive)
            .FirstOrDefaultAsync();

        if (currentAssignment != null)
        {
            currentAssignment.IsActive = false;
            currentAssignment.Status = ClassTeacherStatus.Completed;
            currentAssignment.EndDate = DateTime.UtcNow;
            currentAssignment.LastModifiedBy = userId?.ToString() ?? "System";
            currentAssignment.LastModifiedAt = DateTime.UtcNow;
        }

        // Create new assignment
        var section = await _context.Sections
            .Where(s => s.Id == sectionId)
            .Include(s => s.Grade)
            .FirstOrDefaultAsync();

        if (section == null) return false;

        var newAssignment = new Domain.Entities.ClassTeacher
        {

            FacultyId = newFacultyId,
            SectionId = sectionId,
            AcademicYearId = section.AcademicYearId,
            StartDate = DateTime.UtcNow,
            IsActive = true,
            IsPrimary = true,
            Status = ClassTeacherStatus.Active,
            Remarks = reason,
            CreatedBy = userId?.ToString() ?? "System",
            CreatedAt = DateTime.UtcNow
        };

        _context.ClassTeachers.Add(newAssignment);
        await _context.SaveChangesAsync();

        _logger.LogInformation("ClassTeacher reassigned for section {SectionId} from previous teacher to faculty {FacultyId}",
            sectionId, newFacultyId);

        return true;
    }
    public async Task<bool> RemoveClassTeacherAsync(Guid sectionId, string reason = "")
    {
        var userId = _currentUserService.UserId;
        var classTeacher = await _context.ClassTeachers
            .Where(ct => ct.SectionId == sectionId && ct.IsActive)
            .FirstOrDefaultAsync();

        if (classTeacher == null) return false;

        classTeacher.IsActive = false;
        classTeacher.Status = ClassTeacherStatus.Completed;
        classTeacher.EndDate = DateTime.UtcNow;
        classTeacher.Remarks = reason;
        classTeacher.LastModifiedBy = userId?.ToString() ?? "System";
        classTeacher.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> TransferClassTeacherAsync(Guid facultyId, Guid fromSectionId, Guid toSectionId)
    {
        // Remove from current section
        await RemoveClassTeacherAsync(fromSectionId, "Transferred to another section");

        // Assign to new section
        var toSection = await _context.Sections
            .Where(s => s.Id == toSectionId)
            .FirstOrDefaultAsync();

        if (toSection == null) return false;

        return await AssignClassTeacherAsync(facultyId, toSectionId, toSection.AcademicYearId);
    }

    public async Task<bool> ActivateClassTeacherAsync(Guid id)
    {
        return await UpdateClassTeacherStatusAsync(id, ClassTeacherStatus.Active);
    }

    public async Task<bool> SuspendClassTeacherAsync(Guid id, string reason = "")
    {
        return await UpdateClassTeacherStatusAsync(id, ClassTeacherStatus.Suspended, reason);
    }

    public async Task<bool> CompleteClassTeacherAssignmentAsync(Guid id, DateTime? endDate = null)
    {
        var userId = _currentUserService.UserId;
        var classTeacher = await _context.ClassTeachers
            .Where(ct => ct.Id == id)
            .FirstOrDefaultAsync();

        if (classTeacher == null) return false;

        classTeacher.Status = ClassTeacherStatus.Completed;
        classTeacher.IsActive = false;
        classTeacher.EndDate = endDate ?? DateTime.UtcNow;
        classTeacher.LastModifiedBy = userId?.ToString() ?? "System";
        classTeacher.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> CancelClassTeacherAssignmentAsync(Guid id, string reason = "")
    {
        return await UpdateClassTeacherStatusAsync(id, ClassTeacherStatus.Cancelled, reason);
    }

    public async Task<bool> UpdateClassTeacherStatusAsync(Guid id, ClassTeacherStatus status, string reason = "")
    {
        var userId = _currentUserService.UserId;
        var classTeacher = await _context.ClassTeachers
            .Where(ct => ct.Id == id)
            .FirstOrDefaultAsync();

        if (classTeacher == null) return false;

        classTeacher.Status = status;
        classTeacher.IsActive = status == ClassTeacherStatus.Active;
        if (!string.IsNullOrEmpty(reason))
        {
            classTeacher.Remarks = reason;
        }
        classTeacher.LastModifiedBy = userId?.ToString() ?? "System";
        classTeacher.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }
    public async Task<bool> ValidateClassTeacherAssignmentAsync(Guid facultyId, Guid sectionId, Guid academicYearId)
    {
        // Check if faculty exists and is active
        var faculty = await _context.Faculty
            .Where(f => f.Id == facultyId && f.IsActive)
            .FirstOrDefaultAsync();

        if (faculty == null) return false;

        // Check if section exists and is active
        var section = await _context.Sections
            .Where(s => s.Id == sectionId && s.IsActive)
            .FirstOrDefaultAsync();

        if (section == null) return false;

        // Check for conflicting assignments
        return !await HasConflictingAssignmentAsync(facultyId, sectionId, academicYearId);
    }

    public async Task<bool> CanAssignClassTeacherAsync(Guid facultyId, Guid sectionId)
    {
        // Check if section already has an active class teacher
        var existingAssignment = await _context.ClassTeachers
            .Where(ct => ct.SectionId == sectionId && ct.IsActive)
            .FirstOrDefaultAsync();

        return existingAssignment == null;
    }

    public async Task<bool> HasConflictingAssignmentAsync(Guid facultyId, Guid sectionId, Guid academicYearId)
    {
        // Check if faculty already has an active assignment for the same academic year
        var conflictingAssignment = await _context.ClassTeachers
            .Where(ct => ct.FacultyId == facultyId &&
                        ct.AcademicYearId == academicYearId &&
                        ct.IsActive &&
                        ct.SectionId != sectionId)
            .FirstOrDefaultAsync();

        return conflictingAssignment != null;
    }

    public async Task<bool> CanDeleteClassTeacherAsync(Guid id)
    {
        var classTeacher = await _context.ClassTeachers
            .Where(ct => ct.Id == id)
            .FirstOrDefaultAsync();

        // Can delete if assignment exists and is not currently active
        return classTeacher != null && !classTeacher.IsActive;
    }
    public async Task<ClassTeacherWorkloadDto> GetFacultyWorkloadAsync(Guid facultyId, Guid academicYearId)
    {
        var faculty = await _context.Faculty
            .Where(f => f.Id == facultyId)
            .FirstOrDefaultAsync();

        if (faculty == null)
            throw new ArgumentException("Faculty not found", nameof(facultyId));

        var assignments = await _context.ClassTeachers
            .Where(ct => ct.FacultyId == facultyId && ct.AcademicYearId == academicYearId && ct.IsActive)
            .Include(ct => ct.Section)
                .ThenInclude(s => s.Grade)
            .Include(ct => ct.Students)
            .ToListAsync();

        return new ClassTeacherWorkloadDto
        {
            FacultyId = facultyId,
            FacultyName = faculty.Name,
            TotalSections = assignments.Count,
            TotalStudents = assignments.Sum(a => a.Students.Count),
            PrimarySections = assignments.Count(a => a.IsPrimary),
            SecondarySections = assignments.Count(a => !a.IsPrimary),
            GradeNames = assignments.Select(a => a.Section.Grade.Name).Distinct().ToList(),
            SectionNames = assignments.Select(a => a.Section.Name).ToList(),
            WorkloadScore = assignments.Sum(a => a.Students.Count) * 1.0m, // Simple calculation
            IsOverloaded = assignments.Sum(a => a.Students.Count) > 100,
            Recommendations = assignments.Sum(a => a.Students.Count) > 100 ? "Consider reducing workload" : "Workload is manageable"
        };
    }

    public async Task<IEnumerable<ClassTeacherWorkloadDto>> GetAllFacultyWorkloadsAsync(Guid academicYearId)
    {
        var facultyIds = await _context.ClassTeachers
            .Where(ct => ct.AcademicYearId == academicYearId && ct.IsActive)
            .Select(ct => ct.FacultyId)
            .Distinct()
            .ToListAsync();

        var workloads = new List<ClassTeacherWorkloadDto>();
        foreach (var facultyId in facultyIds)
        {
            var workload = await GetFacultyWorkloadAsync(facultyId, academicYearId);
            workloads.Add(workload);
        }

        return workloads;
    }

    public async Task<IEnumerable<ClassTeacherWorkloadDto>> GetOverloadedFacultyAsync(Guid academicYearId, decimal threshold = 100.0m)
    {
        var allWorkloads = await GetAllFacultyWorkloadsAsync(academicYearId);
        return allWorkloads.Where(w => w.WorkloadScore > threshold);
    }

    public async Task<bool> OptimizeWorkloadDistributionAsync(Guid academicYearId)
    {
        // Simplified implementation - just return true
        await Task.CompletedTask;
        return true;
    }

    public async Task<decimal> CalculateFacultyWorkloadScoreAsync(Guid facultyId, Guid academicYearId)
    {
        var workload = await GetFacultyWorkloadAsync(facultyId, academicYearId);
        return workload.WorkloadScore;
    }
    public async Task<ClassTeacherAssignmentWizardDto> GetAssignmentWizardDataAsync(Guid academicYearId)
    {
        var unassignedSections = await GetUnassignedSectionsAsync(academicYearId);

        var assignments = unassignedSections.Select(section => new ClassTeacherAssignmentDto
        {
            SectionId = section.Id,
            SectionName = section.Name,
            GradeName = section.GradeName,
            IsPrimary = true,
            StartDate = DateTime.UtcNow
        }).ToList();

        return new ClassTeacherAssignmentWizardDto
        {
            AcademicYearId = academicYearId,
            Assignments = assignments
        };
    }

    public async Task<bool> ProcessAssignmentWizardAsync(ClassTeacherAssignmentWizardDto wizardDto)
    {
        var userId = _currentUserService.UserId;

        foreach (var assignment in wizardDto.Assignments.Where(a => a.FacultyId.HasValue))
        {
            var classTeacher = new Domain.Entities.ClassTeacher
            {

                FacultyId = assignment.FacultyId!.Value,
                SectionId = assignment.SectionId,
                AcademicYearId = wizardDto.AcademicYearId,
                StartDate = assignment.StartDate,
                IsActive = true,
                IsPrimary = assignment.IsPrimary,
                Status = ClassTeacherStatus.Active,
                Responsibilities = assignment.Responsibilities,
                ContactSchedule = assignment.ContactSchedule,
                OfficeHours = assignment.OfficeHours,
                CreatedBy = userId?.ToString() ?? "System",
                CreatedAt = DateTime.UtcNow
            };

            _context.ClassTeachers.Add(classTeacher);
        }

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<FacultyDto>> GetAvailableFacultyForAssignmentAsync(Guid academicYearId)
    {
        var assignedFacultyIds = await _context.ClassTeachers
            .Where(ct => ct.AcademicYearId == academicYearId && ct.IsActive)
            .Select(ct => ct.FacultyId)
            .ToListAsync();

        var availableFaculty = await _context.Faculty
            .Where(f => f.IsActive && !assignedFacultyIds.Contains(f.Id))
            .ToListAsync();

        return _mapper.Map<IEnumerable<FacultyDto>>(availableFaculty);
    }

    public async Task<IEnumerable<SectionDto>> GetUnassignedSectionsAsync(Guid academicYearId)
    {
        var assignedSectionIds = await _context.ClassTeachers
            .Where(ct => ct.AcademicYearId == academicYearId && ct.IsActive)
            .Select(ct => ct.SectionId)
            .ToListAsync();

        var unassignedSections = await _context.Sections
            .Where(s => s.AcademicYearId == academicYearId && s.IsActive && !assignedSectionIds.Contains(s.Id))
            .Include(s => s.Grade)
            .Include(s => s.AcademicYear)
            .OrderBy(s => s.Grade.Level)
            .ThenBy(s => s.DisplayOrder)
            .ToListAsync();

        return _mapper.Map<IEnumerable<SectionDto>>(unassignedSections);
    }
    public async Task<ClassTeacherPerformanceDto> GetClassTeacherPerformanceAsync(Guid id)
    {
        var classTeacher = await _context.ClassTeachers
            .Where(ct => ct.Id == id)
            .Include(ct => ct.Faculty)
            .Include(ct => ct.Section)
            .Include(ct => ct.Students)
            .FirstOrDefaultAsync();

        if (classTeacher == null)
            throw new ArgumentException("ClassTeacher not found", nameof(id));

        return new ClassTeacherPerformanceDto
        {
            ClassTeacherId = id,
            FacultyName = classTeacher.Faculty.Name,
            SectionName = classTeacher.Section.Name,
            StudentCount = classTeacher.Students.Count,
            AverageAttendance = 85.0m, // Placeholder
            AverageGrades = 75.0m, // Placeholder
            ParentMeetings = 5, // Placeholder
            StudentCounselingSessions = 10, // Placeholder
            PerformanceNotes = classTeacher.Remarks,
            LastUpdated = DateTime.UtcNow
        };
    }

    public async Task<IEnumerable<ClassTeacherPerformanceDto>> GetAllClassTeacherPerformanceAsync(Guid academicYearId)
    {
        var classTeachers = await _context.ClassTeachers
            .Where(ct => ct.AcademicYearId == academicYearId && ct.IsActive)
            .ToListAsync();

        var performances = new List<ClassTeacherPerformanceDto>();
        foreach (var ct in classTeachers)
        {
            var performance = await GetClassTeacherPerformanceAsync(ct.Id);
            performances.Add(performance);
        }

        return performances;
    }

    public async Task<bool> UpdatePerformanceNotesAsync(Guid id, string notes)
    {
        var userId = _currentUserService.UserId;
        var classTeacher = await _context.ClassTeachers
            .Where(ct => ct.Id == id)
            .FirstOrDefaultAsync();

        if (classTeacher == null) return false;

        classTeacher.Remarks = notes;
        classTeacher.LastModifiedBy = userId?.ToString() ?? "System";
        classTeacher.LastModifiedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<decimal> CalculateClassTeacherEffectivenessAsync(Guid id)
    {
        var performance = await GetClassTeacherPerformanceAsync(id);
        // Simple effectiveness calculation based on performance metrics
        return (performance.AverageAttendance + performance.AverageGrades) / 2;
    }

    public async Task<bool> BulkAssignClassTeachersAsync(Dictionary<Guid, Guid> sectionFacultyMappings, Guid academicYearId)
    {
        var userId = _currentUserService.UserId;

        foreach (var mapping in sectionFacultyMappings)
        {
            var classTeacher = new Domain.Entities.ClassTeacher
            {

                FacultyId = mapping.Value,
                SectionId = mapping.Key,
                AcademicYearId = academicYearId,
                StartDate = DateTime.UtcNow,
                IsActive = true,
                IsPrimary = true,
                Status = ClassTeacherStatus.Active,
                CreatedBy = userId?.ToString() ?? "System",
                CreatedAt = DateTime.UtcNow
            };

            _context.ClassTeachers.Add(classTeacher);
        }

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> BulkUpdateClassTeacherStatusAsync(List<Guid> classTeacherIds, ClassTeacherStatus status, string reason = "")
    {
        var userId = _currentUserService.UserId;
        var classTeachers = await _context.ClassTeachers
            .Where(ct => classTeacherIds.Contains(ct.Id))
            .ToListAsync();

        foreach (var classTeacher in classTeachers)
        {
            classTeacher.Status = status;
            classTeacher.IsActive = status == ClassTeacherStatus.Active;
            if (!string.IsNullOrEmpty(reason))
            {
                classTeacher.Remarks = reason;
            }
            classTeacher.LastModifiedBy = userId?.ToString() ?? "System";
            classTeacher.LastModifiedAt = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> BulkTransferClassTeachersAsync(Dictionary<Guid, Guid> facultySectionMappings)
    {
        foreach (var mapping in facultySectionMappings)
        {
            await TransferClassTeacherAsync(mapping.Key, Guid.Empty, mapping.Value); // Simplified
        }
        return true;
    }

    public async Task<bool> ProcessBulkOperationAsync(BulkClassTeacherOperationDto operationDto)
    {
        return operationDto.Operation.ToLower() switch
        {
            "activate" => await BulkUpdateClassTeacherStatusAsync(operationDto.ClassTeacherIds, ClassTeacherStatus.Active),
            "deactivate" => await BulkUpdateClassTeacherStatusAsync(operationDto.ClassTeacherIds, ClassTeacherStatus.Suspended),
            "complete" => await BulkUpdateClassTeacherStatusAsync(operationDto.ClassTeacherIds, ClassTeacherStatus.Completed),
            "cancel" => await BulkUpdateClassTeacherStatusAsync(operationDto.ClassTeacherIds, ClassTeacherStatus.Cancelled, operationDto.Reason),
            _ => false
        };
    }
    // Utility and management methods - simplified implementations
    public async Task<bool> UpdateContactScheduleAsync(Guid id, string contactSchedule)
    {
        var userId = _currentUserService.UserId;
        var classTeacher = await _context.ClassTeachers.Where(ct => ct.Id == id).FirstOrDefaultAsync();
        if (classTeacher == null) return false;

        classTeacher.ContactSchedule = contactSchedule;
        classTeacher.LastModifiedBy = userId?.ToString() ?? "System";
        classTeacher.LastModifiedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UpdateOfficeHoursAsync(Guid id, string officeHours)
    {
        var userId = _currentUserService.UserId;
        var classTeacher = await _context.ClassTeachers.Where(ct => ct.Id == id).FirstOrDefaultAsync();
        if (classTeacher == null) return false;

        classTeacher.OfficeHours = officeHours;
        classTeacher.LastModifiedBy = userId?.ToString() ?? "System";
        classTeacher.LastModifiedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UpdateResponsibilitiesAsync(Guid id, string responsibilities)
    {
        var userId = _currentUserService.UserId;
        var classTeacher = await _context.ClassTeachers.Where(ct => ct.Id == id).FirstOrDefaultAsync();
        if (classTeacher == null) return false;

        classTeacher.Responsibilities = responsibilities;
        classTeacher.LastModifiedBy = userId?.ToString() ?? "System";
        classTeacher.LastModifiedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UpdateSpecialDutiesAsync(Guid id, string specialDuties)
    {
        var userId = _currentUserService.UserId;
        var classTeacher = await _context.ClassTeachers.Where(ct => ct.Id == id).FirstOrDefaultAsync();
        if (classTeacher == null) return false;

        classTeacher.SpecialDuties = specialDuties;
        classTeacher.LastModifiedBy = userId?.ToString() ?? "System";
        classTeacher.LastModifiedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ImportClassTeachersFromCsvAsync(Stream csvStream, Guid academicYearId)
    {
        await Task.CompletedTask;
        return false; // Simplified implementation
    }

    public async Task<Stream> ExportClassTeachersToCsvAsync(Guid academicYearId)
    {
        var classTeachers = await _context.ClassTeachers
            .Where(ct => ct.AcademicYearId == academicYearId)
            .Include(ct => ct.Faculty)
            .Include(ct => ct.Section)
                .ThenInclude(s => s.Grade)
            .ToListAsync();

        var csv = new System.Text.StringBuilder();
        csv.AppendLine("FacultyName,SectionName,GradeName,StartDate,EndDate,IsActive,Status");

        foreach (var ct in classTeachers)
        {
            csv.AppendLine($"{ct.Faculty.Name},{ct.Section.Name},{ct.Section.Grade.Name},{ct.StartDate:yyyy-MM-dd},{ct.EndDate?.ToString("yyyy-MM-dd") ?? ""},{ct.IsActive},{ct.Status}");
        }

        var bytes = System.Text.Encoding.UTF8.GetBytes(csv.ToString());
        return new MemoryStream(bytes);
    }

    public async Task<bool> DuplicateAssignmentsToNewAcademicYearAsync(Guid sourceAcademicYearId, Guid targetAcademicYearId)
    {
        var sourceAssignments = await _context.ClassTeachers
            .Where(ct => ct.AcademicYearId == sourceAcademicYearId && ct.IsActive)
            .ToListAsync();

        var userId = _currentUserService.UserId;

        foreach (var source in sourceAssignments)
        {
            var newAssignment = new Domain.Entities.ClassTeacher
            {

                FacultyId = source.FacultyId,
                SectionId = source.SectionId, // Note: This might need mapping to new sections
                AcademicYearId = targetAcademicYearId,
                StartDate = DateTime.UtcNow,
                IsActive = true,
                IsPrimary = source.IsPrimary,
                Status = ClassTeacherStatus.Active,
                Responsibilities = source.Responsibilities,
                SpecialDuties = source.SpecialDuties,
                ContactSchedule = source.ContactSchedule,
                OfficeHours = source.OfficeHours,
                CreatedBy = userId?.ToString() ?? "System",
                CreatedAt = DateTime.UtcNow
            };

            _context.ClassTeachers.Add(newAssignment);
        }

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<ClassTeacherDto>> GetClassTeacherHistoryAsync(Guid facultyId)
    {
        var history = await _context.ClassTeachers
            .Where(ct => ct.FacultyId == facultyId)
            .Include(ct => ct.Faculty)
            .Include(ct => ct.Section)
                .ThenInclude(s => s.Grade)
            .Include(ct => ct.AcademicYear)
            .OrderByDescending(ct => ct.StartDate)
            .ToListAsync();

        return _mapper.Map<IEnumerable<ClassTeacherDto>>(history);
    }

    public async Task<IEnumerable<ClassTeacherDto>> GetSectionHistoryAsync(Guid sectionId)
    {
        var history = await _context.ClassTeachers
            .Where(ct => ct.SectionId == sectionId)
            .Include(ct => ct.Faculty)
            .Include(ct => ct.Section)
                .ThenInclude(s => s.Grade)
            .Include(ct => ct.AcademicYear)
            .OrderByDescending(ct => ct.StartDate)
            .ToListAsync();

        return _mapper.Map<IEnumerable<ClassTeacherDto>>(history);
    }

    public async Task<bool> ArchiveClassTeacherAsync(Guid id)
    {
        return await UpdateClassTeacherStatusAsync(id, ClassTeacherStatus.Completed);
    }

    public async Task<bool> RestoreClassTeacherAsync(Guid id)
    {
        return await UpdateClassTeacherStatusAsync(id, ClassTeacherStatus.Active);
    }

    public async Task<bool> RolloverClassTeachersToNewYearAsync(Guid sourceAcademicYearId, Guid targetAcademicYearId)
    {
        return await DuplicateAssignmentsToNewAcademicYearAsync(sourceAcademicYearId, targetAcademicYearId);
    }

    public async Task<IEnumerable<ClassTeacherDto>> GetExpiredAssignmentsAsync(Guid academicYearId)
    {
        var expired = await _context.ClassTeachers
            .Where(ct => ct.AcademicYearId == academicYearId &&
                        ct.EndDate.HasValue &&
                        ct.EndDate.Value < DateTime.UtcNow)
            .Include(ct => ct.Faculty)
            .Include(ct => ct.Section)
                .ThenInclude(s => s.Grade)
            .ToListAsync();

        return _mapper.Map<IEnumerable<ClassTeacherDto>>(expired);
    }

    public async Task<bool> RenewClassTeacherAssignmentAsync(Guid id, Guid newAcademicYearId)
    {
        var existing = await _context.ClassTeachers.Where(ct => ct.Id == id).FirstOrDefaultAsync();
        if (existing == null) return false;

        var userId = _currentUserService.UserId;

        var renewed = new Domain.Entities.ClassTeacher
        {

            FacultyId = existing.FacultyId,
            SectionId = existing.SectionId,
            AcademicYearId = newAcademicYearId,
            StartDate = DateTime.UtcNow,
            IsActive = true,
            IsPrimary = existing.IsPrimary,
            Status = ClassTeacherStatus.Active,
            Responsibilities = existing.Responsibilities,
            SpecialDuties = existing.SpecialDuties,
            ContactSchedule = existing.ContactSchedule,
            OfficeHours = existing.OfficeHours,
            CreatedBy = userId?.ToString() ?? "System",
            CreatedAt = DateTime.UtcNow
        };

        _context.ClassTeachers.Add(renewed);
        await _context.SaveChangesAsync();
        return true;
    }

    // Simplified implementations for remaining methods
    public async Task<IEnumerable<FacultyDto>> GetRecommendedFacultyForSectionAsync(Guid sectionId)
    {
        var availableFaculty = await _context.Faculty
            .Where(f => f.IsActive)
            .Take(5)
            .ToListAsync();
        return _mapper.Map<IEnumerable<FacultyDto>>(availableFaculty);
    }

    public async Task<IEnumerable<SectionDto>> GetRecommendedSectionsForFacultyAsync(Guid facultyId, Guid academicYearId)
    {
        var unassignedSections = await GetUnassignedSectionsAsync(academicYearId);
        return unassignedSections.Take(5);
    }

    public async Task<IEnumerable<string>> GetAssignmentOptimizationSuggestionsAsync(Guid academicYearId)
    {
        await Task.CompletedTask;
        return new[] { "Consider balancing workload across faculty", "Assign experienced teachers to challenging sections" };
    }

    public async Task<Stream> GenerateClassTeacherReportAsync(Guid academicYearId, string reportType = "summary")
    {
        return await ExportClassTeachersToCsvAsync(academicYearId);
    }

    public async Task<Stream> GenerateWorkloadAnalysisReportAsync(Guid academicYearId)
    {
        var workloads = await GetAllFacultyWorkloadsAsync(academicYearId);
        var csv = new System.Text.StringBuilder();
        csv.AppendLine("FacultyName,TotalSections,TotalStudents,WorkloadScore,IsOverloaded");

        foreach (var workload in workloads)
        {
            csv.AppendLine($"{workload.FacultyName},{workload.TotalSections},{workload.TotalStudents},{workload.WorkloadScore},{workload.IsOverloaded}");
        }

        var bytes = System.Text.Encoding.UTF8.GetBytes(csv.ToString());
        return new MemoryStream(bytes);
    }

    public async Task<Stream> GeneratePerformanceReportAsync(Guid academicYearId)
    {
        var performances = await GetAllClassTeacherPerformanceAsync(academicYearId);
        var csv = new System.Text.StringBuilder();
        csv.AppendLine("FacultyName,SectionName,StudentCount,AverageAttendance,AverageGrades");

        foreach (var performance in performances)
        {
            csv.AppendLine($"{performance.FacultyName},{performance.SectionName},{performance.StudentCount},{performance.AverageAttendance},{performance.AverageGrades}");
        }

        var bytes = System.Text.Encoding.UTF8.GetBytes(csv.ToString());
        return new MemoryStream(bytes);
    }

    public async Task<IEnumerable<string>> GetClassTeacherAlertsAsync(Guid facultyId)
    {
        await Task.CompletedTask;
        return new[] { "No alerts at this time" };
    }

    public async Task<bool> SendAssignmentNotificationAsync(Guid classTeacherId)
    {
        await Task.CompletedTask;
        return true; // Simplified implementation
    }

    public async Task<bool> SendWorkloadWarningAsync(Guid facultyId)
    {
        await Task.CompletedTask;
        return true; // Simplified implementation
    }
}
