import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

// Angular Material imports
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

// Services and models
import { ParentService } from '../../../../core/services/parent.service';
import { LeaveType } from '../../../../core/models/student.model';

@Component({
  selector: 'app-leave-application-dialog',
  templateUrl: './leave-application-dialog.component.html',
  styleUrls: ['./leave-application-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
    MatProgressSpinnerModule
  ]
})
export class LeaveApplicationDialogComponent implements OnInit {
  leaveForm: FormGroup;
  loading = false;

  leaveTypes = [
    { value: LeaveType.Sick, label: 'Sick' },
    { value: LeaveType.Personal, label: 'Personal' },
    { value: LeaveType.Family, label: 'Family' },
    { value: LeaveType.Religious, label: 'Religious' },
    { value: LeaveType.Other, label: 'Other' }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private parentService: ParentService,
    public dialogRef: MatDialogRef<LeaveApplicationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: {
      studentId: number;
    }
  ) {
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    this.leaveForm = this.formBuilder.group({
      type: [LeaveType.Sick, Validators.required],
      startDate: [today, Validators.required],
      endDate: [tomorrow, Validators.required],
      reason: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(500)]],
      attachment: ['']
    });
  }

  ngOnInit(): void {
  }

  onSubmit(): void {
    if (this.leaveForm.invalid) {
      return;
    }

    this.loading = true;

    const formValues = this.leaveForm.value;
    const parentId = 1; // In a real app, get from auth service

    const leave: any = {
      studentId: this.data.studentId,
      type: formValues.type,
      startDate: formValues.startDate,
      endDate: formValues.endDate,
      reason: formValues.reason,
      attachmentPath: formValues.attachment
    };

    this.parentService.createLeaveByParent(parentId, leave).subscribe({
      next: () => {
        this.loading = false;
        this.dialogRef.close(true);
      },
      error: (err) => {
        console.error('Error applying for leave:', err);
        this.loading = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onFileSelected(event: Event): void {
    const fileInput = event.target as HTMLInputElement;
    if (fileInput.files && fileInput.files.length > 0) {
      const file = fileInput.files[0];
      // In a real app, you would upload the file to a server and get a URL
      this.leaveForm.patchValue({
        attachment: file.name
      });
    }
  }
}
