using School.Domain.Common;

namespace School.Domain.Entities;

public class FacultyPublication : BaseEntity
{
    public Guid FacultyId { get; set; }
    public Faculty? Faculty { get; set; }
    
    public string Title { get; set; } = string.Empty;
    public string? Journal { get; set; }
    public int? Year { get; set; }
    public string? Url { get; set; }
    public int DisplayOrder { get; set; }
}
