using School.Domain.Common;

using System;
using System.Collections.Generic;

namespace School.Domain.Entities
{
    public class Club : BaseEntity
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string ShortDescription { get; set; }
        public string Category { get; set; }
        public string MeetingSchedule { get; set; }
        public string Location { get; set; }
        public string Requirements { get; set; }
        public string JoinProcess { get; set; }
        public string ContactEmail { get; set; }
        public string Website { get; set; }
        public string Instagram { get; set; }
        public string Facebook { get; set; }
        public bool IsFeatured { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; }
        public int? ProfileImageId { get; set; }
        public string ProfileImageUrl { get; set; }
        
        // Navigation properties
        public ICollection<ClubTranslation> Translations { get; set; }
        public ICollection<ClubAdvisor> Advisors { get; set; }
        public ICollection<ClubLeader> Leaders { get; set; }
        public ICollection<ClubActivity> Activities { get; set; }
        public ICollection<ClubAchievement> Achievements { get; set; }
        public ICollection<ClubEvent> Events { get; set; }
        public ICollection<ClubGalleryItem> GalleryItems { get; set; }
    }
}
