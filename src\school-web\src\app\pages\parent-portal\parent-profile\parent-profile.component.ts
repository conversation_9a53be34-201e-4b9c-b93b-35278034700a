import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { ParentService } from '../../../core/services/parent.service';
import { AuthService } from '../../../core/services/auth.service';
import { ParentDetail } from '../../../core/models/parent.model';

@Component({
  selector: 'app-parent-profile',
  templateUrl: './parent-profile.component.html',
  styleUrls: ['./parent-profile.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ]
})
export class ParentProfileComponent implements OnInit {
  parent: ParentDetail | null = null;
  loading = true;
  error = false;

  constructor(
    private parentService: ParentService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadParentData();
  }

  loadParentData(): void {
    this.loading = true;
    const user = this.authService.getCurrentUser();
    const userId = user?.id;

    if (userId) {
      // If we have a user ID, get the parent by user ID
      this.parentService.getParentByUserId(userId).subscribe({
        next: (parent) => {
          this.parent = parent;
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading parent data:', err);
          this.error = true;
          this.loading = false;
          this.snackBar.open('Failed to load parent profile', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
    } else {
      // Fallback to mock data if no user ID is available
      this.parentService.getParent(1).subscribe({
        next: (parent) => {
          this.parent = parent;
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading parent data:', err);
          this.error = true;
          this.loading = false;
          this.snackBar.open('Failed to load parent profile', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  getGenderLabel(gender: number): string {
    return gender === 0 ? 'Male' : gender === 1 ? 'Female' : 'Other';
  }
}
