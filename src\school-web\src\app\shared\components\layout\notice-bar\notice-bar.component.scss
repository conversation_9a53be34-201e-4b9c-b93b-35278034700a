@use 'sass:color';
@use '../../../../../styles/_variables' as *;

// Animation keyframes for scrolling
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

// Animation keyframes for notice bar closing
@keyframes slideUp {
  0% {
    max-height: 50px;
    opacity: 1;
  }
  100% {
    max-height: 0;
    opacity: 0;
    padding: 0;
  }
}

.notice-bar {
  background: linear-gradient(90deg, $primary-dark 0%, $primary-color 50%, $primary-dark 100%);
  color: white;
  padding: 10px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 1000;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 50%, rgba(255,255,255,0.1) 100%);
    z-index: -1;
  }

  &.closed {
    animation: slideUp 0.5s forwards;
  }

  .notice-content {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 24px;
    height: 40px;
    box-sizing: border-box;

    .notice-icon {
      margin-right: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      width: 32px;
      height: 32px;
      flex-shrink: 0;
      margin-left: 0;

      mat-icon {
        font-size: 20px;
        height: 20px;
        width: 20px;
        color: white;
      }
    }

    .notice-scroll-container {
      flex: 1;
      overflow: hidden;
      position: relative;
      height: 24px;
      margin: 0 16px;

      &::before, &::after {
        content: '';
        position: absolute;
        top: 0;
        width: 30px;
        height: 100%;
        z-index: 2;
      }

      &::before {
        left: 0;
        background: linear-gradient(90deg, $primary-color 0%, rgba($primary-color, 0) 100%);
      }

      &::after {
        right: 0;
        background: linear-gradient(90deg, rgba($primary-color, 0) 0%, $primary-color 100%);
      }

      .notice-scroll {
        display: flex;
        animation: scroll 30s linear infinite;
        width: fit-content;

        &:hover {
          animation-play-state: paused;
        }

        .notice-items {
          display: flex;
          align-items: center;
          white-space: nowrap;
        }
      }

      .notice-item {
        display: inline-flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.15);
          cursor: pointer;
        }

        mat-icon {
          font-size: 16px;
          height: 16px;
          width: 16px;
          margin-right: 6px;
        }
      }

      .notice-separator {
        margin: 0 12px;
        font-size: 14px;
        opacity: 0.7;
      }
    }

    .notice-actions {
      display: flex;
      align-items: center;
      margin-left: auto;
      flex-shrink: 0;
      margin-right: 0;

      .view-all-btn {
        color: white;
        font-size: 14px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 0 12px;
        height: 32px;
        line-height: 32px;
        border-radius: 16px;
        background-color: rgba(255, 255, 255, 0.1);
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
        }
      }

      .close-btn {
        margin-left: 8px;
        width: 24px;
        height: 24px;
        line-height: 24px;
        color: white;
        opacity: 0.7;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 1;
          background-color: rgba(255, 255, 255, 0.1);
        }

        mat-icon {
          font-size: 18px;
          height: 18px;
          width: 18px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .notice-bar {
    .notice-content {
      height: auto;
      padding: 8px 12px;

      .notice-icon {
        width: 28px;
        height: 28px;
        margin-right: 12px;

        mat-icon {
          font-size: 16px;
          height: 16px;
          width: 16px;
        }
      }

      .notice-scroll-container {
        height: 20px;
      }

      .notice-actions {
        margin-left: auto;

        .view-all-btn {
          display: none;
        }

        .close-btn {
          margin-left: 8px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .notice-bar {
    .notice-content {
      .notice-icon {
        display: none;
      }
    }
  }
}
