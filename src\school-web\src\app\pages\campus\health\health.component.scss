@use "sass:color";

// Variables
$primary-color: #3f51b5;
$accent-color: #ff4081;
$text-color: #333;
$light-gray: #f5f5f5;
$medium-gray: #e0e0e0;
$dark-gray: #757575;
$white: #ffffff;
$health-green: #4caf50;
$section-padding: 80px 0;
$container-padding: 0 20px;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Hero Section styles are now handled by the DefaultHeroComponent

// Container for main content
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $container-padding;
}

// Section Styles
section {
  padding: $section-padding;

  h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: $text-color;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: $health-green;
    }
  }

  .section-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: $dark-gray;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Introduction Section
.intro-section {
  background-color: $white;

  .intro-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      color: $text-color;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Health Center Section
.health-center-section {
  background-color: $light-gray;

  .health-center-info {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 40px;

    .info-card {
      background-color: $white;
      border-radius: $border-radius;
      padding: 30px;
      box-shadow: $box-shadow;
      text-align: center;
      flex: 1;
      min-width: 250px;
      max-width: 350px;
      transition: transform 0.3s, box-shadow 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .info-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 70px;
        height: 70px;
        background-color: $health-green;
        border-radius: 50%;
        margin: 0 auto 20px;

        mat-icon {
          font-size: 35px;
          height: 35px;
          width: 35px;
          color: $white;
        }
      }

      h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        color: $text-color;
      }

      p {
        color: $dark-gray;
        line-height: 1.6;
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .emergency-info {
    display: flex;
    align-items: center;
    background-color: #ffebee;
    border-radius: $border-radius;
    padding: 20px;
    max-width: 800px;
    margin: 40px auto 0;

    mat-icon {
      color: #f44336;
      font-size: 30px;
      height: 30px;
      width: 30px;
      margin-right: 20px;
      flex-shrink: 0;
    }

    p {
      color: $text-color;
      margin: 0;
      line-height: 1.6;
    }
  }
}

// Services Section
.services-section {
  background-color: $white;

  .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .service-card {
      background-color: $light-gray;
      border-radius: $border-radius;
      padding: 30px;
      text-align: center;
      transition: transform 0.3s, box-shadow 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: $box-shadow;
      }

      .service-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 70px;
        height: 70px;
        background-color: $health-green;
        border-radius: 50%;
        margin: 0 auto 20px;

        mat-icon {
          font-size: 35px;
          height: 35px;
          width: 35px;
          color: $white;
        }
      }

      h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        color: $text-color;
      }

      p {
        color: $dark-gray;
        line-height: 1.6;
        margin-bottom: 0;
      }
    }
  }
}

// Staff Section
.staff-section {
  background-color: $light-gray;

  .staff-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .staff-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .staff-image {
        height: 250px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 5px;
          color: $text-color;
        }

        .staff-title {
          font-weight: 500;
          color: $health-green;
          margin-bottom: 5px;
        }

        .staff-credentials {
          font-size: 0.9rem;
          color: $dark-gray;
          margin-bottom: 15px;
        }

        .staff-bio {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }
}

// Wellness Programs Section
.wellness-section {
  background-color: $white;

  .wellness-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .wellness-card {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      .wellness-image {
        height: 200px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }

      mat-card-content {
        padding: 20px;
        flex-grow: 1;

        h3 {
          font-size: 1.5rem;
          margin-bottom: 15px;
          color: $text-color;
        }

        p {
          color: $dark-gray;
          line-height: 1.6;
          margin-bottom: 0;
        }
      }
    }
  }
}

// Resources Section
.resources-section {
  background-color: $light-gray;

  .resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;

    .resource-card {
      background-color: $white;
      border-radius: $border-radius;
      padding: 30px;
      box-shadow: $box-shadow;
      transition: transform 0.3s, box-shadow 0.3s;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      }

      h3 {
        font-size: 1.3rem;
        margin-bottom: 15px;
        color: $text-color;
      }

      p {
        color: $dark-gray;
        line-height: 1.6;
        margin-bottom: 20px;
        flex-grow: 1;
      }

      a {
        display: flex;
        align-items: center;
        justify-content: center;

        mat-icon {
          margin-left: 5px;
        }
      }
    }
  }
}

// FAQs Section
.faqs-section {
  background-color: $white;

  .faqs-container {
    max-width: 800px;
    margin: 40px auto 0;

    ::ng-deep .mat-expansion-panel {
      margin-bottom: 15px;
      border-radius: $border-radius;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ::ng-deep .mat-expansion-panel-header {
      padding: 20px;
    }

    ::ng-deep .mat-expansion-panel-header-title {
      color: $text-color;
      font-weight: 500;
      font-size: 1.1rem;
    }

    p {
      color: $dark-gray;
      line-height: 1.6;
      margin-bottom: 0;
    }
  }
}

// Contact Section
.contact-section {
  background: linear-gradient(135deg, $health-green, color.adjust($health-green, $lightness: -15%));
  color: $white;

  .contact-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h2 {
      color: $white;

      &:after {
        background-color: $white;
      }
    }

    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 30px;
    }

    .contact-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;

      a {
        display: flex;
        align-items: center;
        padding: 10px 20px;

        mat-icon {
          margin-right: 10px;
        }
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 992px) {
  .hero-section {
    height: 350px;

    .hero-content h1 {
      font-size: 2.5rem;
    }
  }

  section {
    padding: 60px 0;

    h2 {
      font-size: 2rem;
    }
  }

  .services-grid, .staff-grid, .wellness-grid, .resources-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  // Hero section styles removed

  .health-center-info {
    flex-direction: column;
    align-items: center;

    .info-card {
      width: 100%;
      max-width: 100%;
    }
  }

  .contact-buttons {
    flex-direction: column;
    align-items: center;

    a {
      width: 100%;
      max-width: 300px;
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

@media (max-width: 576px) {
  // Hero section styles removed

  section h2 {
    font-size: 1.8rem;
  }

  .services-grid, .staff-grid, .wellness-grid, .resources-grid {
    grid-template-columns: 1fr;
  }
}
