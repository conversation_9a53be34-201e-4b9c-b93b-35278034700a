using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class SectionTranslationConfiguration : IEntityTypeConfiguration<SectionTranslation>
{
    public void Configure(EntityTypeBuilder<SectionTranslation> builder)
    {
        builder.ToTable("SectionTranslations");

        builder.HasKey(st => st.Id);

        // Configure properties
        builder.Property(st => st.LanguageCode)
            .IsRequired()
            .HasMaxLength(10);

        builder.Property(st => st.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(st => st.Description)
            .HasMaxLength(500);

        builder.Property(st => st.Requirements)
            .HasMaxLength(1000);

        builder.Property(st => st.Remarks)
            .HasMaxLength(500);

        builder.Property(st => st.CreatedBy)
            .HasMaxLength(450);

        builder.Property(st => st.LastModifiedBy)
            .HasMaxLength(450);

        // Configure relationships
        builder.HasOne(st => st.Section)
            .WithMany(s => s.Translations)
            .HasForeignKey(st => st.SectionId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(st => new { st.SectionId, st.LanguageCode })
            .IsUnique();

        builder.HasIndex(st => st.LanguageCode);

        // Global query filter for multi-tenancy
        builder.HasQueryFilter(st => EF.Property<string>(st, "TenantId") == null || 
                                    EF.Property<string>(st, "TenantId") == "");
    }
}
