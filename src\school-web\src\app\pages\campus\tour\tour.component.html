<!-- Hero Section -->
<app-default-hero
  translationPrefix="CAMPUS_LIFE"
  title="CAMPUS_LIFE.TOUR"
  subtitle="CAMPUS_LIFE.TOUR_SUBTITLE"
  theme="light"
  size="medium"
  alignment="center"
  backgroundImage="assets/images/campus/tour/campus-tour-hero.jpg">
</app-default-hero>

<!-- Main Content -->
<div class="container">
  <!-- Introduction Section -->
  <section class="intro-section">
    <div class="intro-content">
      <h2>{{ 'CAMPUS_LIFE.TOUR_INTRO_TITLE' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.TOUR_INTRO_P1' | translate }}</p>
      <p>{{ 'CAMPUS_LIFE.TOUR_INTRO_P2' | translate }}</p>

      <div class="tour-cta">
        <a mat-raised-button color="primary" routerLink="/admissions/visit">
          {{ 'CAMPUS_LIFE.SCHEDULE_TOUR' | translate }}
        </a>
      </div>
    </div>
  </section>

  <!-- Campus Map Section -->
  <section class="map-section">
    <h2>{{ 'CAMPUS_LIFE.CAMPUS_MAP' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.MAP_INTRO' | translate }}</p>

    <div class="map-container">
      <img src="assets/images/campus/tour/campus-map.jpg" alt="Campus Map" class="campus-map">

      <div class="map-legend">
        <div class="legend-item">
          <div class="legend-color academic"></div>
          <span>{{ 'CAMPUS_LIFE.ACADEMIC_BUILDINGS' | translate }}</span>
        </div>
        <div class="legend-item">
          <div class="legend-color athletic"></div>
          <span>{{ 'CAMPUS_LIFE.ATHLETIC_FACILITIES' | translate }}</span>
        </div>
        <div class="legend-item">
          <div class="legend-color residential"></div>
          <span>{{ 'CAMPUS_LIFE.RESIDENTIAL_BUILDINGS' | translate }}</span>
        </div>
        <div class="legend-item">
          <div class="legend-color support"></div>
          <span>{{ 'CAMPUS_LIFE.SUPPORT_FACILITIES' | translate }}</span>
        </div>
        <div class="legend-item">
          <div class="legend-color parking"></div>
          <span>{{ 'CAMPUS_LIFE.PARKING' | translate }}</span>
        </div>
      </div>
    </div>
  </section>

  <!-- Campus Facilities Section -->
  <section class="facilities-section">
    <h2>{{ 'CAMPUS_LIFE.CAMPUS_FACILITIES' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.FACILITIES_INTRO' | translate }}</p>

    <div class="facilities-grid">
      <mat-card class="facility-card" *ngFor="let location of tourLocations">
        <div class="facility-image">
          <img [src]="location.image" [alt]="location.name">
        </div>
        <mat-card-content>
          <h3>{{location.name}}</h3>
          <p class="facility-description">{{location.description}}</p>

          <div class="facility-features">
            <h4>{{ 'CAMPUS_LIFE.FEATURES' | translate }}</h4>
            <ul>
              <li *ngFor="let feature of location.features">{{feature}}</li>
            </ul>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Virtual Tour Section -->
  <section class="virtual-tour-section">
    <h2>{{ 'CAMPUS_LIFE.VIRTUAL_TOUR' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.VIRTUAL_TOUR_INTRO' | translate }}</p>

    <div class="videos-grid">
      <mat-card class="video-card" *ngFor="let video of tourVideos">
        <div class="video-thumbnail">
          <img [src]="video.thumbnail" [alt]="video.title">
          <div class="play-button">
            <mat-icon>play_circle_filled</mat-icon>
          </div>
        </div>
        <mat-card-content>
          <h3>{{video.title}}</h3>
          <p>{{video.description}}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- 360° Panoramas Section -->
  <section class="panoramas-section">
    <h2>{{ 'CAMPUS_LIFE.PANORAMAS' | translate }}</h2>
    <p class="section-intro">{{ 'CAMPUS_LIFE.PANORAMAS_INTRO' | translate }}</p>

    <div class="panoramas-grid">
      <div class="panorama-item" *ngFor="let panorama of panoramas">
        <div class="panorama-image">
          <img [src]="panorama.image" [alt]="panorama.title">
          <div class="panorama-icon">
            <mat-icon>360</mat-icon>
          </div>
        </div>
        <h3>{{panorama.title}}</h3>
        <p>{{panorama.description}}</p>
      </div>
    </div>
  </section>

  <!-- Visit Section -->
  <section class="visit-section">
    <div class="visit-content">
      <h2>{{ 'CAMPUS_LIFE.VISIT_CAMPUS' | translate }}</h2>
      <p>{{ 'CAMPUS_LIFE.VISIT_TEXT' | translate }}</p>

      <div class="visit-details">
        <div class="visit-item">
          <mat-icon>event</mat-icon>
          <div class="visit-info">
            <h3>{{ 'CAMPUS_LIFE.TOUR_TIMES' | translate }}</h3>
            <p>{{ 'CAMPUS_LIFE.WEEKDAY_TOURS' | translate }}</p>
            <p>{{ 'CAMPUS_LIFE.WEEKEND_TOURS' | translate }}</p>
          </div>
        </div>

        <div class="visit-item">
          <mat-icon>location_on</mat-icon>
          <div class="visit-info">
            <h3>{{ 'CAMPUS_LIFE.STARTING_POINT' | translate }}</h3>
            <p>{{ 'CAMPUS_LIFE.ADMISSIONS_OFFICE' | translate }}</p>
            <p>{{ 'CAMPUS_LIFE.MAIN_BUILDING' | translate }}</p>
          </div>
        </div>
      </div>

      <div class="visit-cta">
        <a mat-raised-button color="primary" routerLink="/admissions/visit">
          {{ 'CAMPUS_LIFE.SCHEDULE_TOUR' | translate }}
        </a>
        <a mat-stroked-button color="primary" routerLink="/contact">
          {{ 'CAMPUS_LIFE.CONTACT_ADMISSIONS' | translate }}
        </a>
      </div>
    </div>
  </section>
</div>
