<div class="fees-container">
  <h1 class="page-title">Student Fees</h1>

  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="fees-loading">
    <mat-progress-bar mode="indeterminate"></mat-progress-bar>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="fees-error">
    <mat-error>
      <mat-icon>error</mat-icon>
      <span>Failed to load fee records. Please try again.</span>
      <button mat-button color="warn" (click)="loadStudentFees()">Retry</button>
    </mat-error>
  </div>

  <!-- Student Info -->
  <div *ngIf="!isLoading && !error && feeDetails" class="student-info">
    <mat-card>
      <mat-card-header>
        <mat-card-title>{{ studentName }}</mat-card-title>
        <mat-card-subtitle>Student ID: {{ studentId }}</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="fee-summary">
          <div class="fee-item">
            <span class="fee-label">Total Annual Fee:</span>
            <span class="fee-value">{{ feeDetails.totalAnnual | currency:'BDT':'symbol':'1.0-0' }}</span>
          </div>
          <div class="fee-item">
            <span class="fee-label">Total Paid:</span>
            <span class="fee-value">{{ feeDetails.totalPaid | currency:'BDT':'symbol':'1.0-0' }}</span>
          </div>
          <div class="fee-item">
            <span class="fee-label">Current Due:</span>
            <span class="fee-value">{{ feeDetails.currentDue | currency:'BDT':'symbol':'1.0-0' }}</span>
          </div>
          <div class="fee-item">
            <span class="fee-label">Due Date:</span>
            <span class="fee-value">{{ feeDetails.dueDate | date:'mediumDate' }}</span>
          </div>
          <div class="fee-item">
            <span class="fee-label">Status:</span>
            <span class="fee-value status-badge" [ngClass]="feeDetails.paymentStatus === 'Paid' ? 'status-paid' : 'status-due'">
              {{ feeDetails.paymentStatus }}
            </span>
          </div>
        </div>

        <div *ngIf="feeDetails.currentDue > 0" class="payment-actions">
          <button mat-raised-button color="primary" (click)="makePayment()">
            <mat-icon>payment</mat-icon> Make Payment
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Fees Table -->
  <div *ngIf="!isLoading && !error && feeHistory.length > 0" class="fees-table-container mat-elevation-z8">
    <h2>Payment History</h2>
    <table mat-table [dataSource]="feeHistory" class="fees-table">
      <!-- Fee Type Column -->
      <ng-container matColumnDef="feeType">
        <th mat-header-cell *matHeaderCellDef>Fee Type</th>
        <td mat-cell *matCellDef="let record">{{ record.feeType }}</td>
      </ng-container>

      <!-- Due Date Column -->
      <ng-container matColumnDef="dueDate">
        <th mat-header-cell *matHeaderCellDef>Due Date</th>
        <td mat-cell *matCellDef="let record">{{ record.dueDate | date:'mediumDate' }}</td>
      </ng-container>

      <!-- Amount Column -->
      <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef>Amount</th>
        <td mat-cell *matCellDef="let record">{{ record.amount | currency:'BDT':'symbol':'1.0-0' }}</td>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>Status</th>
        <td mat-cell *matCellDef="let record">
          <span class="status-badge" [ngClass]="record.status === 'Paid' ? 'status-paid' : 'status-due'">
            {{ record.status }}
          </span>
        </td>
      </ng-container>

      <!-- Paid Date Column -->
      <ng-container matColumnDef="paidDate">
        <th mat-header-cell *matHeaderCellDef>Paid Date</th>
        <td mat-cell *matCellDef="let record">
          {{ record.paidDate ? (record.paidDate | date:'mediumDate') : '-' }}
        </td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let record">
          <button mat-button color="primary" *ngIf="record.status === 'Due'" (click)="makePayment()">
            Pay Now
          </button>
          <button mat-button color="accent" *ngIf="record.status === 'Paid' && record.receiptNo" (click)="downloadReceipt(record.receiptNo)">
            Receipt
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>

  <!-- No Data Message -->
  <div *ngIf="!isLoading && !error && feeHistory.length === 0" class="no-data">
    <mat-card>
      <mat-card-content>
        <p>No fee records found for this student.</p>
      </mat-card-content>
    </mat-card>
  </div>
</div>
