.history-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.history-content {
  max-width: 800px;
  margin: 0 auto;
}

.history-loading,
.history-error {
  margin-bottom: 24px;
}

.history-error {
  text-align: center;
  padding: 16px;

  mat-icon {
    vertical-align: middle;
    margin-right: 8px;
  }
}

.no-history {
  text-align: center;
  padding: 16px;
}

.timeline {
  position: relative;
  padding: 16px 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 24px;
    width: 4px;
    background-color: #e0e0e0;
    z-index: 0;
  }
}

.timeline-item {
  position: relative;
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }

  &.current-year {
    .timeline-badge {
      background-color: #3f51b5;
      color: white;
    }

    .timeline-card {
      border-left: 4px solid #3f51b5;
    }
  }
}

.timeline-badge {
  position: absolute;
  top: 16px;
  left: 0;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.year {
  font-size: 14px;
  font-weight: 500;
}

.timeline-card {
  margin-left: 64px;
}

.current-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  background-color: #3f51b5;
  color: white;
  margin-left: 8px;
  vertical-align: middle;
}

.history-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.grade-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
  margin-left: 8px;
}

.promoted {
  color: #2e7d32;
}

.not-promoted {
  color: #c62828;
}

@media (max-width: 768px) {
  .history-details {
    grid-template-columns: 1fr;
  }
}
