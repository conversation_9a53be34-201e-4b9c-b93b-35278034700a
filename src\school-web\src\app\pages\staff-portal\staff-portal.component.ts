import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AuthService } from '../../core/services/auth.service';

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  permissions: string[];
}

interface DashboardStats {
  classes?: number;
  students?: number;
  content?: number;
  events?: number;
}

@Component({
  selector: 'app-staff-portal',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatDividerModule,
    MatSnackBarModule,
    TranslateModule
  ],
  templateUrl: './staff-portal.component.html',
  styleUrl: './staff-portal.component.scss'
})
export class StaffPortalComponent implements OnInit {
  currentUser: User | null = null;
  activeSection = 'dashboard';
  dashboardStats: DashboardStats = {};

  // Role-based permissions mapping
  private rolePermissions: { [key: string]: string[] } = {
    'ADMIN': ['FACULTY', 'ADMIN', 'MANAGER', 'EDITOR', 'USER'],
    'MANAGER': ['MANAGER', 'USER'],
    'FACULTY': ['FACULTY', 'USER'],
    'EDITOR': ['EDITOR', 'USER'],
    'USER': ['USER']
  };

  constructor(
    private router: Router,
    private snackBar: MatSnackBar,
    private translateService: TranslateService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    this.loadDashboardStats();
  }

  loadCurrentUser(): void {
    // Get current user from auth service
    this.authService.currentUser$.subscribe({
      next: (user: any) => {
        if (user) {
          this.currentUser = {
            ...user,
            permissions: this.rolePermissions[user.role] || ['USER']
          };
        } else {
          this.router.navigate(['/login']);
        }
      },
      error: (error: any) => {
        console.error('Failed to load current user', error);
        this.router.navigate(['/login']);
      }
    });
  }

  loadDashboardStats(): void {
    // Load dashboard statistics based on user role
    // This would typically come from various services
    this.dashboardStats = {
      classes: 5,
      students: 120,
      content: 25,
      events: 8
    };
  }

  hasPermission(requiredRoles: string[]): boolean {
    if (!this.currentUser) return false;
    
    return requiredRoles.some(role => 
      this.currentUser?.permissions.includes(role)
    );
  }

  setActiveSection(section: string): void {
    // Check if user has permission to access this section
    const sectionPermissions: { [key: string]: string[] } = {
      'dashboard': ['USER'],
      'classes': ['FACULTY', 'ADMIN'],
      'students': ['FACULTY', 'ADMIN', 'MANAGER'],
      'attendance': ['FACULTY', 'ADMIN'],
      'grades': ['FACULTY', 'ADMIN'],
      'news': ['EDITOR', 'ADMIN'],
      'events': ['EDITOR', 'ADMIN'],
      'notices': ['EDITOR', 'ADMIN'],
      'users': ['ADMIN'],
      'reports': ['ADMIN'],
      'settings': ['ADMIN'],
      'calendar': ['USER'],
      'messages': ['USER']
    };

    const requiredPermissions = sectionPermissions[section] || ['USER'];
    
    if (!this.hasPermission(requiredPermissions)) {
      this.snackBar.open(
        this.translateService.instant('STAFF_PORTAL.ACCESS_DENIED'),
        this.translateService.instant('COMMON.CLOSE'),
        { duration: 3000, panelClass: ['error-snackbar'] }
      );
      return;
    }

    this.activeSection = section;
  }

  getSectionTitle(): string {
    const titleMap: { [key: string]: string } = {
      'classes': 'STAFF_PORTAL.CLASSES_TITLE',
      'students': 'STAFF_PORTAL.STUDENTS_TITLE',
      'attendance': 'STAFF_PORTAL.ATTENDANCE_TITLE',
      'grades': 'STAFF_PORTAL.GRADES_TITLE',
      'news': 'STAFF_PORTAL.NEWS_TITLE',
      'events': 'STAFF_PORTAL.EVENTS_TITLE',
      'notices': 'STAFF_PORTAL.NOTICES_TITLE',
      'users': 'STAFF_PORTAL.USERS_TITLE',
      'reports': 'STAFF_PORTAL.REPORTS_TITLE',
      'settings': 'STAFF_PORTAL.SETTINGS_TITLE',
      'calendar': 'STAFF_PORTAL.CALENDAR_TITLE',
      'messages': 'STAFF_PORTAL.MESSAGES_TITLE'
    };

    return titleMap[this.activeSection] || 'STAFF_PORTAL.SECTION_TITLE';
  }

  viewProfile(): void {
    // Navigate to profile page or open profile modal
    this.snackBar.open(
      this.translateService.instant('STAFF_PORTAL.PROFILE_FEATURE_COMING_SOON'),
      this.translateService.instant('COMMON.CLOSE'),
      { duration: 3000 }
    );
  }

  changePassword(): void {
    // Navigate to change password page or open modal
    this.snackBar.open(
      this.translateService.instant('STAFF_PORTAL.CHANGE_PASSWORD_FEATURE_COMING_SOON'),
      this.translateService.instant('COMMON.CLOSE'),
      { duration: 3000 }
    );
  }

  logout(): void {
    this.authService.enhancedLogout().subscribe({
      next: () => {
        this.snackBar.open(
          this.translateService.instant('STAFF_PORTAL.LOGOUT_SUCCESS'),
          this.translateService.instant('COMMON.CLOSE'),
          { duration: 2000, panelClass: ['success-snackbar'] }
        );
        this.router.navigate(['/login']);
      },
      error: (error: any) => {
        console.error('Logout failed', error);
        // Force logout even if API call fails
        this.router.navigate(['/login']);
      }
    });
  }
}
