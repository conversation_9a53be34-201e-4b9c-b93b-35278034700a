using Microsoft.EntityFrameworkCore;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.TuitionFee;
using School.Domain.Entities;
using School.Domain.Enums;
using System.Threading;
using System.Threading.Tasks;

namespace School.Infrastructure.Services;

public class TuitionFeeService : ITuitionFeeService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;

    public TuitionFeeService(IUnitOfWork unitOfWork, ICurrentUserService currentUserService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
    }

    public async Task<List<TuitionFeeDto>> GetAllTuitionFeesAsync(EducationLevel? level, FeeType? type, bool? isActive)
    {
        var repository = _unitOfWork.Repository<TuitionFee>();
        var query = repository.AsQueryable("Translations");

        // Apply filters
        if (level.HasValue)
        {
            query = query.Where(tf => tf.Level == level.Value);
        }

        if (type.HasValue)
        {
            query = query.Where(tf => tf.Type == type.Value);
        }

        if (isActive.HasValue)
        {
            query = query.Where(tf => tf.IsActive == isActive.Value);
        }

        // Get results with ordering
        var tuitionFees = await query
            .OrderBy(tf => tf.Level)
            .ThenBy(tf => tf.DisplayOrder)
            .ToListAsync();

        // Map to DTOs
        return tuitionFees.Select(tf => new TuitionFeeDto
        {
            Id = tf.Id,
            Level = tf.Level,
            Name = tf.Name,
            Description = tf.Description,
            Amount = tf.Amount,
            Type = tf.Type,
            Frequency = tf.Frequency,
            IsActive = tf.IsActive,
            DisplayOrder = tf.DisplayOrder,
            CreatedAt = tf.CreatedAt,
            LastModifiedAt = tf.LastModifiedAt,
            Translations = tf.Translations.Select(t => new TuitionFeeTranslationDto
            {
                Id = t.Id,
                TuitionFeeId = t.TuitionFeeId,
                LanguageCode = t.LanguageCode,
                Name = t.Name,
                Description = t.Description
            }).ToList()
        }).ToList();
    }

    public async Task<TuitionFeeDto?> GetTuitionFeeByIdAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<TuitionFee>();
        var tuitionFee = await repository.GetByIdAsync(id, new[] { "Translations" });

        if (tuitionFee == null) return null;

        return new TuitionFeeDto
        {
            Id = tuitionFee.Id,
            Level = tuitionFee.Level,
            Name = tuitionFee.Name,
            Description = tuitionFee.Description,
            Amount = tuitionFee.Amount,
            Type = tuitionFee.Type,
            Frequency = tuitionFee.Frequency,
            IsActive = tuitionFee.IsActive,
            DisplayOrder = tuitionFee.DisplayOrder,
            CreatedAt = tuitionFee.CreatedAt,
            LastModifiedAt = tuitionFee.LastModifiedAt,
            Translations = tuitionFee.Translations.Select(t => new TuitionFeeTranslationDto
            {
                Id = t.Id,
                TuitionFeeId = t.TuitionFeeId,
                LanguageCode = t.LanguageCode,
                Name = t.Name,
                Description = t.Description
            }).ToList()
        };
    }

    public async Task<TuitionFeeDto> CreateTuitionFeeAsync(CreateTuitionFeeDto createDto)
    {
        var repository = _unitOfWork.Repository<TuitionFee>();

        var tuitionFee = new TuitionFee
        {
            Level = createDto.Level,
            Name = createDto.Name,
            Description = createDto.Description,
            Amount = createDto.Amount,
            Type = createDto.Type,
            Frequency = createDto.Frequency,
            IsActive = createDto.IsActive,
            DisplayOrder = createDto.DisplayOrder,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _currentUserService.UserId?.ToString()
        };

        await repository.AddAsync(tuitionFee);
        await _unitOfWork.SaveChangesAsync();

        return new TuitionFeeDto
        {
            Id = tuitionFee.Id,
            Level = tuitionFee.Level,
            Name = tuitionFee.Name,
            Description = tuitionFee.Description,
            Amount = tuitionFee.Amount,
            Type = tuitionFee.Type,
            Frequency = tuitionFee.Frequency,
            IsActive = tuitionFee.IsActive,
            DisplayOrder = tuitionFee.DisplayOrder,
            CreatedAt = tuitionFee.CreatedAt,
            LastModifiedAt = tuitionFee.LastModifiedAt,
            Translations = new List<TuitionFeeTranslationDto>()
        };
    }

    public async Task<bool> UpdateTuitionFeeAsync(Guid id, UpdateTuitionFeeDto updateDto)
    {
        var repository = _unitOfWork.Repository<TuitionFee>();
        var tuitionFee = await repository.GetByIdAsync(id);

        if (tuitionFee == null) return false;

        tuitionFee.Level = updateDto.Level;
        tuitionFee.Name = updateDto.Name;
        tuitionFee.Description = updateDto.Description;
        tuitionFee.Amount = updateDto.Amount;
        tuitionFee.Type = updateDto.Type;
        tuitionFee.Frequency = updateDto.Frequency;
        tuitionFee.IsActive = updateDto.IsActive;
        tuitionFee.DisplayOrder = updateDto.DisplayOrder;
        // LastModifiedAt is set below
        tuitionFee.LastModifiedBy = _currentUserService.UserId?.ToString();
        tuitionFee.LastModifiedAt = DateTime.UtcNow;

        await repository.UpdateAsync(tuitionFee);
        await _unitOfWork.SaveChangesAsync();

        return true;
    }

    public async Task<bool> DeleteTuitionFeeAsync(Guid id)
    {
        var repository = _unitOfWork.Repository<TuitionFee>();
        var tuitionFee = await repository.GetByIdAsync(id);

        if (tuitionFee == null) return false;

        // Use soft delete by default
        await repository.DeleteAsync(tuitionFee);
        await _unitOfWork.SaveChangesAsync();

        return true;
    }
}