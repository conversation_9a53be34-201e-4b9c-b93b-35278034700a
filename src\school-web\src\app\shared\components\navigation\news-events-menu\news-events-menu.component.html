<div class="menu-item" (mouseenter)="openMenu(newsEventsMenuTrigger)" (mouseleave)="closeMenu()">
  <button mat-button [matMenuTriggerFor]="newsEventsMenu" #newsEventsMenuTrigger="matMenuTrigger"
          class="menu-trigger" [class.active]="activeRoute.startsWith('/news') || activeRoute.startsWith('/events') || activeRoute.startsWith('/calendar') || activeRoute.startsWith('/alumni')">
    {{ 'NAV.NEWS_EVENTS' | translate }}
    <mat-icon>arrow_drop_down</mat-icon>
  </button>
  <mat-menu #newsEventsMenu="matMenu" class="mega-menu-panel" [overlapTrigger]="false" [hasBackdrop]="false">
    <div class="mega-menu-content" (mouseenter)="clearCloseTimeout()" (mouseleave)="closeMenu()">
      <div class="menu-column">
        <h3>{{ 'NEWS.TITLE' | translate }}</h3>
        <a mat-menu-item routerLink="/news" [class.active]="activeRoute === '/news'">{{ 'NEWS.ALL_NEWS' | translate }}</a>
        <a mat-menu-item routerLink="/news/announcements" [class.active]="activeRoute === '/news/announcements'">{{ 'NEWS.ANNOUNCEMENTS' | translate }}</a>
        <a mat-menu-item routerLink="/news/newsletter" [class.active]="activeRoute === '/news/newsletter'">{{ 'NEWS.NEWSLETTER' | translate }}</a>
      </div>
      <div class="menu-column">
        <h3>{{ 'EVENTS.TITLE' | translate }}</h3>
        <a mat-menu-item routerLink="/events" [class.active]="activeRoute === '/events'">{{ 'EVENTS.UPCOMING' | translate }}</a>
        <a mat-menu-item routerLink="/calendar" [class.active]="activeRoute === '/calendar'">{{ 'EVENTS.CALENDAR' | translate }}</a>
        <a mat-menu-item routerLink="/news/press" [class.active]="activeRoute === '/news/press'">{{ 'NEWS.PRESS' | translate }}</a>
      </div>
      <div class="menu-column">
        <h3>{{ 'EVENTS.COMMUNITY' | translate }}</h3>
        <a mat-menu-item routerLink="/alumni" [class.active]="activeRoute === '/alumni'">{{ 'ABOUT.ALUMNI' | translate }}</a>
        <a mat-menu-item routerLink="/events/community" [class.active]="activeRoute === '/events/community'">{{ 'EVENTS.COMMUNITY_EVENTS' | translate }}</a>
      </div>
    </div>
  </mat-menu>
</div>
