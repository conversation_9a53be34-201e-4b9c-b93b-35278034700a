using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace School.Infrastructure.Logging
{
    public class FileLogger : ILogger
    {
        private readonly string _categoryName;
        private readonly FileLoggerOptions _options;
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        public FileLogger(string categoryName, FileLoggerOptions options)
        {
            _categoryName = categoryName;
            _options = options;
            
            // Ensure log directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(_options.ErrorLogPath));
            Directory.CreateDirectory(Path.GetDirectoryName(_options.OperationLogPath));
        }

        public IDisposable BeginScope<TState>(TState state) => default;

        public bool IsEnabled(LogLevel logLevel)
        {
            return logLevel >= _options.MinLevel;
        }

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            if (!IsEnabled(logLevel))
                return;

            var message = formatter(state, exception);
            
            // Skip empty messages
            if (string.IsNullOrEmpty(message) && exception == null)
                return;

            var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{logLevel}] [{_categoryName}] {message}";
            
            if (exception != null)
            {
                logEntry += $"{Environment.NewLine}Exception: {exception}{Environment.NewLine}Stack Trace: {exception.StackTrace}";
            }

            // Determine which log file to write to
            string logFilePath;
            if (logLevel >= LogLevel.Error)
            {
                logFilePath = _options.ErrorLogPath;
            }
            else
            {
                logFilePath = _options.OperationLogPath;
            }

            // Write to log file asynchronously
            Task.Run(async () =>
            {
                try
                {
                    await _semaphore.WaitAsync();
                    await File.AppendAllTextAsync(logFilePath, logEntry + Environment.NewLine);
                }
                catch
                {
                    // Swallow exceptions from logging to avoid cascading failures
                }
                finally
                {
                    _semaphore.Release();
                }
            });
        }
    }

    public class FileLoggerOptions
    {
        public LogLevel MinLevel { get; set; } = LogLevel.Information;
        public string ErrorLogPath { get; set; } = "logs/error.log";
        public string OperationLogPath { get; set; } = "logs/operation.log";
    }
}
