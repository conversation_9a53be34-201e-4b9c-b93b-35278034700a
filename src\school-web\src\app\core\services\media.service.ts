import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { ErrorHandlerService } from './error-handler.service';
import { ApiResponseHandlerService } from './api-response-handler.service';
import { MediaItem, MediaFilter, MediaUpload } from '../models/media.model';
import { ApiResponse } from '../models/api-response.model';
import { PaginatedResponse } from '../models/base.model';

@Injectable({
  providedIn: 'root'
})
export class MediaService extends BaseApiService {
  constructor(
    protected override http: HttpClient,
    protected override errorHandler: ErrorHandlerService,
    private apiResponseHandler: ApiResponseHandlerService
  ) {
    super(http, errorHandler);
  }

  /**
   * Get all media items with filtering and pagination
   */
  getAllMedia(filter: MediaFilter = {}): Observable<PaginatedResponse<MediaItem>> {
    const params = this.buildParams(filter);
    return this.apiResponseHandler.processResponse<PaginatedResponse<MediaItem>>(
      this.http.get<ApiResponse<PaginatedResponse<MediaItem>>>(`${this.apiUrl}/media`, { params }),
      false,
      'Failed to retrieve media items'
    );
  }

  /**
   * Get media item by ID
   */
  getMediaById(id: number): Observable<MediaItem> {
    return this.apiResponseHandler.processResponse<MediaItem>(
      this.http.get<ApiResponse<MediaItem>>(`${this.apiUrl}/media/${id}`),
      false,
      'Failed to retrieve media item'
    );
  }

  /**
   * Upload media file(s)
   */
  uploadMedia(formData: FormData): Observable<{ id: number }> {
    return this.apiResponseHandler.processResponse<{ id: number }>(
      this.http.post<ApiResponse<{ id: number }>>(`${this.apiUrl}/media/upload`, formData),
      true,
      'Media uploaded successfully'
    );
  }

  /**
   * Update media item
   */
  updateMedia(id: number, media: Partial<MediaItem>): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.put<ApiResponse<any>>(`${this.apiUrl}/media/${id}`, media),
      true,
      'Media updated successfully'
    );
  }

  /**
   * Delete media item
   */
  deleteMedia(id: number): Observable<any> {
    return this.apiResponseHandler.processResponse<any>(
      this.http.delete<ApiResponse<any>>(`${this.apiUrl}/media/${id}`),
      true,
      'Media deleted successfully'
    );
  }
}
