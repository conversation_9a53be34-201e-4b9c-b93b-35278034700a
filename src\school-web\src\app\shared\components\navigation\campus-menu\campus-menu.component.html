<div class="menu-item" (mouseenter)="openMenu(campusMenuTrigger)" (mouseleave)="closeMenu()">
  <button mat-button [matMenuTriggerFor]="campusMenu" #campusMenuTrigger="matMenuTrigger"
          class="menu-trigger" [class.active]="activeRoute.startsWith('/campus')">
    {{ 'NAV.CAMPUS_LIFE' | translate }}
    <mat-icon>arrow_drop_down</mat-icon>
  </button>
  <mat-menu #campusMenu="matMenu" class="mega-menu-panel" [overlapTrigger]="false" [hasBackdrop]="false">
    <div class="mega-menu-content" (mouseenter)="clearCloseTimeout()" (mouseleave)="closeMenu()">
      <div class="menu-column">
        <h3>{{ 'CAMPUS_LIFE.ACTIVITIES' | translate }}</h3>
        <a mat-menu-item routerLink="/campus" [class.active]="activeRoute === '/campus'">{{ 'CAMPUS_LIFE.OVERVIEW' | translate }}</a>
        <a mat-menu-item routerLink="/campus/clubs" [class.active]="activeRoute === '/campus/clubs'">{{ 'CAMPUS_LIFE.CLUBS' | translate }}</a>
        <a mat-menu-item routerLink="/campus/sports" [class.active]="activeRoute === '/campus/sports'">{{ 'CAMPUS_LIFE.SPORTS' | translate }}</a>
      </div>
      <div class="menu-column">
        <h3>{{ 'CAMPUS_LIFE.EVENTS' | translate }}</h3>
        <a mat-menu-item routerLink="/campus/events" [class.active]="activeRoute === '/campus/events'">{{ 'CAMPUS_LIFE.EVENTS' | translate }}</a>
        <a mat-menu-item routerLink="/campus/tour" [class.active]="activeRoute === '/campus/tour'">{{ 'CAMPUS_LIFE.TOUR' | translate }}</a>
      </div>
      <div class="menu-column">
        <h3>{{ 'CAMPUS_LIFE.FACILITIES' | translate }}</h3>
        <a mat-menu-item routerLink="/campus/dining" [class.active]="activeRoute === '/campus/dining'">{{ 'CAMPUS_LIFE.DINING' | translate }}</a>
        <a mat-menu-item routerLink="/campus/housing" [class.active]="activeRoute === '/campus/housing'">{{ 'CAMPUS_LIFE.HOUSING' | translate }}</a>
        <a mat-menu-item routerLink="/campus/health" [class.active]="activeRoute === '/campus/health'">{{ 'CAMPUS_LIFE.HEALTH' | translate }}</a>
      </div>
    </div>
  </mat-menu>
</div>
