using System.ComponentModel.DataAnnotations;

namespace School.Application.DTOs;

/// <summary>
/// DTO for bulk class teacher assignments
/// </summary>
public class BulkClassTeacherAssignmentDto
{
    /// <summary>
    /// Academic year ID for the assignments
    /// </summary>
    [Required]
    public Guid AcademicYearId { get; set; }

    /// <summary>
    /// Dictionary mapping section IDs to faculty IDs
    /// </summary>
    [Required]
    public Dictionary<Guid, Guid> SectionFacultyMappings { get; set; } = new();
}
