using School.Domain.Common;
using School.Domain.Enums;

namespace School.Domain.Entities;

/// <summary>
/// Represents a subscription plan for an organization
/// Manages billing, features, and usage limits
/// </summary>
public class OrganizationSubscription : BaseEntity
{
    /// <summary>
    /// ID of the organization this subscription belongs to
    /// </summary>
    public Guid OrganizationId { get; set; }

    /// <summary>
    /// Type of subscription plan
    /// </summary>
    public SubscriptionPlan Plan { get; set; } = SubscriptionPlan.Trial;

    /// <summary>
    /// Current status of the subscription
    /// </summary>
    public SubscriptionStatus Status { get; set; } = SubscriptionStatus.Active;

    /// <summary>
    /// Billing cycle for the subscription
    /// </summary>
    public BillingCycle BillingCycle { get; set; } = BillingCycle.Monthly;

    /// <summary>
    /// Amount charged per billing cycle
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Currency for the subscription
    /// </summary>
    public string Currency { get; set; } = "USD";

    /// <summary>
    /// Date when the subscription started
    /// </summary>
    public DateTime StartDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Date when the subscription ends (for fixed-term subscriptions)
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Date of the next billing cycle
    /// </summary>
    public DateTime? NextBillingDate { get; set; }

    /// <summary>
    /// Date when the subscription was cancelled (if applicable)
    /// </summary>
    public DateTime? CancelledDate { get; set; }

    /// <summary>
    /// Reason for cancellation
    /// </summary>
    public string? CancellationReason { get; set; }

    /// <summary>
    /// Whether the subscription auto-renews
    /// </summary>
    public bool AutoRenew { get; set; } = true;

    /// <summary>
    /// External subscription ID from payment provider (e.g., Stripe)
    /// </summary>
    public string? ExternalSubscriptionId { get; set; }

    /// <summary>
    /// External customer ID from payment provider
    /// </summary>
    public string? ExternalCustomerId { get; set; }

    /// <summary>
    /// Maximum number of students allowed
    /// </summary>
    public int? MaxStudents { get; set; }

    /// <summary>
    /// Maximum number of faculty members allowed
    /// </summary>
    public int? MaxFaculty { get; set; }

    /// <summary>
    /// Maximum storage space in MB
    /// </summary>
    public long? MaxStorageMB { get; set; }

    /// <summary>
    /// Features included in this subscription (JSON)
    /// </summary>
    public string? IncludedFeatures { get; set; }

    /// <summary>
    /// Usage limits for this subscription (JSON)
    /// </summary>
    public string? UsageLimits { get; set; }

    /// <summary>
    /// Trial period end date (if applicable)
    /// </summary>
    public DateTime? TrialEndDate { get; set; }

    /// <summary>
    /// Whether this subscription is currently in trial
    /// </summary>
    public bool IsTrialActive { get; set; } = false;

    /// <summary>
    /// Discount percentage applied to this subscription
    /// </summary>
    public decimal? DiscountPercentage { get; set; }

    /// <summary>
    /// Discount amount applied to this subscription
    /// </summary>
    public decimal? DiscountAmount { get; set; }

    /// <summary>
    /// Coupon code used for discount
    /// </summary>
    public string? CouponCode { get; set; }

    /// <summary>
    /// Notes about this subscription
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Navigation properties
    /// </summary>
    public Organization Organization { get; set; } = null!;
}
