# Authentication Language Resources

This document registers all language translation keys used in authentication-related components.

## Language Files Structure

```
src/assets/i18n/
├── en/
│   ├── login.json
│   ├── register.json
│   ├── mfa.json
│   └── reset-password.json
├── bn/
│   ├── login.json
│   ├── register.json
│   ├── mfa.json
│   └── reset-password.json
└── modules.json
```

## Login Component Translation Keys

### File: `login.json`

#### Welcome Section
- `LOGIN.WELCOME_TITLE` - Main welcome title
- `LOGIN.WELCOME_SUBTITLE` - Welcome subtitle description
- `LOGIN.FEATURE_SECURITY` - Security feature description
- `LOGIN.FEATURE_MULTILINGUAL` - Multilingual feature description

#### Sign In Section
- `LOGIN.SIGN_IN_TITLE` - Sign in form title
- `LOGIN.SIGN_IN_SUBTITLE` - Sign in form subtitle

#### Portal Selection
- `LOGIN.SELECT_PORTAL` - Portal selection title
- `LOGIN.SELECT_PORTAL_DESC` - Portal selection description

#### User Types
- `LOGIN.STUDENT` - Student portal label
- `LOGIN.STUDENT_DESC` - Student portal description
- `LOGIN.PARENT` - Parent portal label
- `LOGIN.PARENT_DESC` - Parent portal description
- `LOGIN.FACULTY` - Faculty portal label
- `LOGIN.FACULTY_DESC` - Faculty portal description
- `LOGIN.ADMIN` - Admin portal label
- `LOGIN.ADMIN_DESC` - Admin portal description
- `LOGIN.ALUMNI` - Alumni portal label
- `LOGIN.ALUMNI_DESC` - Alumni portal description

#### Credentials Form
- `LOGIN.ENTER_CREDENTIALS` - Credentials section title
- `LOGIN.SECURE_LOGIN_DESC` - Security description
- `LOGIN.USERNAME` - Username field label
- `LOGIN.USERNAME_PLACEHOLDER` - Username placeholder text
- `LOGIN.PASSWORD` - Password field label
- `LOGIN.PASSWORD_PLACEHOLDER` - Password placeholder text
- `LOGIN.SHOW_PASSWORD` - Show password tooltip
- `LOGIN.HIDE_PASSWORD` - Hide password tooltip
- `LOGIN.REMEMBER_ME` - Remember me checkbox
- `LOGIN.FORGOT_PASSWORD` - Forgot password link

#### Form Actions
- `LOGIN.SIGN_IN` - Sign in button text
- `LOGIN.SIGNING_IN` - Loading state text

#### Multi-Factor Authentication
- `LOGIN.MFA_VERIFICATION` - MFA section title
- `LOGIN.MFA_DESCRIPTION` - MFA description
- `LOGIN.MFA_CODE` - MFA code field label
- `LOGIN.MFA_HINT` - MFA code hint text
- `LOGIN.MFA_TROUBLE` - MFA trouble text
- `LOGIN.USE_BACKUP_CODE` - Backup code button
- `LOGIN.VERIFY` - Verify button text
- `LOGIN.VERIFYING` - Verifying state text
- `LOGIN.BACK_TO_LOGIN` - Back to login button

#### Quick Actions
- `LOGIN.OR` - Divider text
- `LOGIN.CREATE_ACCOUNT` - Create account link
- `LOGIN.NEED_HELP` - Help link

#### Validation Messages
- `LOGIN.USERNAME_REQUIRED` - Username required error
- `LOGIN.PASSWORD_REQUIRED` - Password required error
- `LOGIN.PASSWORD_MIN_LENGTH` - Password minimum length error
- `LOGIN.MFA_CODE_REQUIRED` - MFA code required error
- `LOGIN.MFA_CODE_INVALID` - MFA code invalid error
- `LOGIN.INVALID_MFA_CODE` - Invalid MFA code error

#### Status Messages
- `LOGIN.SUCCESS_MESSAGE` - Login success message
- `LOGIN.ERROR_MESSAGE` - Login error message
- `LOGIN.BACKUP_CODES_FEATURE_COMING_SOON` - Backup codes coming soon

#### Footer
- `LOGIN.SECURITY_INFO` - Security information text
- `LOGIN.SUPPORT_INFO` - Support information text
- `LOGIN.PRIVACY_POLICY` - Privacy policy link
- `LOGIN.TERMS_OF_SERVICE` - Terms of service link

#### UI Controls
- `LOGIN.LANGUAGE` - Language selector label
- `LOGIN.TOGGLE_THEME` - Theme toggle tooltip

## Register Component Translation Keys

### File: `register.json`

#### Header
- `REGISTER.TITLE` - Registration form title
- `REGISTER.SUBTITLE` - Registration form subtitle

#### Form Fields
- `REGISTER.FIRST_NAME` - First name field label
- `REGISTER.LAST_NAME` - Last name field label
- `REGISTER.EMAIL` - Email field label
- `REGISTER.PHONE` - Phone field label
- `REGISTER.USERNAME` - Username field label
- `REGISTER.PASSWORD` - Password field label
- `REGISTER.CONFIRM_PASSWORD` - Confirm password field label

#### Validation Messages
- `REGISTER.FIRST_NAME_REQUIRED` - First name required error
- `REGISTER.LAST_NAME_REQUIRED` - Last name required error
- `REGISTER.EMAIL_REQUIRED` - Email required error
- `REGISTER.EMAIL_INVALID` - Email invalid error
- `REGISTER.PHONE_REQUIRED` - Phone required error
- `REGISTER.USERNAME_REQUIRED` - Username required error
- `REGISTER.PASSWORD_REQUIRED` - Password required error
- `REGISTER.PASSWORD_LENGTH` - Password length error
- `REGISTER.CONFIRM_PASSWORD_REQUIRED` - Confirm password required error
- `REGISTER.PASSWORD_MISMATCH` - Password mismatch error

#### Terms and Privacy
- `REGISTER.TERMS` - Terms acceptance checkbox
- `REGISTER.PRIVACY` - Privacy acceptance checkbox
- `REGISTER.TERMS_REQUIRED` - Terms required error
- `REGISTER.PRIVACY_REQUIRED` - Privacy required error

#### Actions
- `REGISTER.SUBMIT` - Submit button text
- `REGISTER.ALREADY_ACCOUNT` - Already have account text
- `REGISTER.LOGIN` - Login link text

## MFA Component Translation Keys

### File: `mfa.json`

#### MFA Setup and Verification
- `MFA.SETUP_TITLE` - MFA setup title
- `MFA.SETUP_DESCRIPTION` - MFA setup description
- `MFA.VERIFY_TITLE` - MFA verification title
- `MFA.VERIFY_DESCRIPTION` - MFA verification description
- `MFA.CODE_INPUT_LABEL` - Code input field label
- `MFA.CODE_INPUT_PLACEHOLDER` - Code input placeholder
- `MFA.VERIFY_BUTTON` - Verify button text
- `MFA.CANCEL_BUTTON` - Cancel button text

## Implementation Notes

### Language Loading
- Translation files are loaded dynamically based on user language preference
- Default language is English (`en`)
- Supported languages: English (`en`), Bengali (`bn`)

### Missing Translation Handling
- Missing translations fall back to the key name
- Development mode shows warnings for missing translations
- Production mode gracefully handles missing translations

### Translation Key Naming Convention
- Format: `COMPONENT.SECTION_ELEMENT`
- All uppercase with underscores
- Descriptive and hierarchical structure
- Consistent across all language files

### File Organization
- Each major component has its own translation file
- Common translations are in `core.json`
- Authentication-specific translations are separated by functionality

## Development Guidelines for Language Resources

### MANDATORY: Always Register Language Resources When Implementing Features

When implementing any new feature or component, you MUST:

1. **Identify all text content** that needs translation
2. **Create translation keys** following the naming convention
3. **Add keys to ALL supported language files** (currently `en` and `bn`)
4. **Update this documentation** with the new translation keys
5. **Test with both languages** to ensure proper functionality

### Step-by-Step Process:

1. **Before coding**: List all user-facing text in the feature
2. **Create translation keys**: Use descriptive, hierarchical naming
3. **Add to English file first**: `src/assets/i18n/en/[component].json`
4. **Add to Bengali file**: `src/assets/i18n/bn/[component].json`
5. **Use in template**: `{{ 'KEY_NAME' | translate }}`
6. **Document here**: Add keys to this documentation
7. **Test thoroughly**: Verify both languages work correctly

### Quality Checklist:
- [ ] All text is translatable (no hardcoded strings)
- [ ] Keys exist in ALL language files
- [ ] Bengali translations are culturally appropriate
- [ ] Documentation is updated
- [ ] Both languages tested in browser

### Common Mistakes to Avoid:
- ❌ Hardcoding text in templates
- ❌ Adding keys to only one language file
- ❌ Using generic key names like "BUTTON" or "TEXT"
- ❌ Forgetting to update documentation
- ❌ Not testing with language switching
- ❌ Using wrong language codes (use 'en' and 'bn', not 'en-US' and 'bn-BD')

## Recent Updates

### Language Service Fix (Latest)
- **Fixed language codes**: Changed from `'en-US'/'bn-BD'` to `'en'/'bn'` to match translation file structure
- **Global translation setup**: Ensured TranslateModule works globally across all components
- **Role-based authentication**: Implemented proper portal access validation
- **User type selection**: Changed from hyperlinks to proper role-based authentication flow

### Key Changes Made:
1. **LanguageService**: Updated to use correct language codes (`'en'`, `'bn'`)
2. **Translation files**: Organized in `/assets/i18n/en/` and `/assets/i18n/bn/` folders
3. **Login component**: Implemented role-based portal access with proper error handling
4. **User experience**: Removed subtitle descriptions to save space, converted to compact design
5. **Authentication flow**: User selects portal type → authenticates → system validates role → redirects or shows error

### Translation Status:
- ✅ **English (en)**: Complete with 30+ login keys
- ✅ **Bengali (bn)**: Complete with cultural adaptations
- ✅ **Global setup**: Working across all components
- ✅ **Role validation**: Portal access control implemented
