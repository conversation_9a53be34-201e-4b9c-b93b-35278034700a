﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace School.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddHolidayEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>mes<PERSON>",
                table: "AcademicCalendarTranslations");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "AcademicCalendarTranslations");

            migrationBuilder.CreateTable(
                name: "Holidays",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsRecurring = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    RecurrenceType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    RecurrenceInterval = table.Column<int>(type: "integer", nullable: true, defaultValue: 1),
                    RecurrenceEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RecurrenceMaxOccurrences = table.Column<int>(type: "integer", nullable: true),
                    RecurrenceDaysOfWeek = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    RecurrenceDayOfMonth = table.Column<int>(type: "integer", nullable: true),
                    RecurrenceMonthOfYear = table.Column<int>(type: "integer", nullable: true),
                    RecurrenceCustomPattern = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Color = table.Column<string>(type: "character varying(7)", maxLength: 7, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsPublic = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    Remarks = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    AcademicYearId = table.Column<Guid>(type: "uuid", nullable: true),
                    TermId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Holidays", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Holidays_AcademicYears_AcademicYearId",
                        column: x => x.AcademicYearId,
                        principalTable: "AcademicYears",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_Holidays_Terms_TermId",
                        column: x => x.TermId,
                        principalTable: "Terms",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "HolidayTranslations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    HolidayId = table.Column<Guid>(type: "uuid", nullable: false),
                    LanguageCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Remarks = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HolidayTranslations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HolidayTranslations_Holidays_HolidayId",
                        column: x => x.HolidayId,
                        principalTable: "Holidays",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Holidays_AcademicYearId",
                table: "Holidays",
                column: "AcademicYearId");

            migrationBuilder.CreateIndex(
                name: "IX_Holidays_DateRange_AcademicYear",
                table: "Holidays",
                columns: new[] { "StartDate", "EndDate", "AcademicYearId" });

            migrationBuilder.CreateIndex(
                name: "IX_Holidays_DateRange_Term",
                table: "Holidays",
                columns: new[] { "StartDate", "EndDate", "TermId" });

            migrationBuilder.CreateIndex(
                name: "IX_Holidays_EndDate",
                table: "Holidays",
                column: "EndDate");

            migrationBuilder.CreateIndex(
                name: "IX_Holidays_IsActive",
                table: "Holidays",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Holidays_IsPublic",
                table: "Holidays",
                column: "IsPublic");

            migrationBuilder.CreateIndex(
                name: "IX_Holidays_StartDate",
                table: "Holidays",
                column: "StartDate");

            migrationBuilder.CreateIndex(
                name: "IX_Holidays_TermId",
                table: "Holidays",
                column: "TermId");

            migrationBuilder.CreateIndex(
                name: "IX_Holidays_Type",
                table: "Holidays",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_HolidayTranslations_Holiday_Language_Unique",
                table: "HolidayTranslations",
                columns: new[] { "HolidayId", "LanguageCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_HolidayTranslations_HolidayId",
                table: "HolidayTranslations",
                column: "HolidayId");

            migrationBuilder.CreateIndex(
                name: "IX_HolidayTranslations_LanguageCode",
                table: "HolidayTranslations",
                column: "LanguageCode");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "HolidayTranslations");

            migrationBuilder.DropTable(
                name: "Holidays");

            migrationBuilder.AddColumn<string>(
                name: "Semester",
                table: "AcademicCalendarTranslations",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "AcademicCalendarTranslations",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));
        }
    }
}
