using School.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace School.Application.Features.Department
{
    public interface IDepartmentService
    {
        Task<(IEnumerable<DepartmentDto> Departments, int TotalCount)> GetAllDepartmentsAsync(DepartmentFilterDto filter);
        Task<DepartmentDto?> GetDepartmentByIdAsync(Guid id);
        Task<DepartmentDto?> GetDepartmentByCodeAsync(string code);
        Task<Guid> CreateDepartmentAsync(CreateDepartmentDto departmentDto);
        Task<bool> UpdateDepartmentAsync(Guid id, UpdateDepartmentDto departmentDto);
        Task<bool> DeleteDepartmentAsync(Guid id);

        // Translation methods
        Task<bool> AddTranslationAsync(Guid departmentId, CreateDepartmentTranslationDto translationDto);
        Task<bool> UpdateTranslationAsync(Guid departmentId, string languageCode, UpdateDepartmentTranslationDto translationDto);
        Task<bool> DeleteTranslationAsync(Guid departmentId, string languageCode);
    }
}
