import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatMenuModule } from '@angular/material/menu';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { AuthService } from '../../core/services/auth.service';
import { ParentService } from '../../core/services/parent.service';
import { ParentDetail } from '../../core/models/parent.model';
import { LanguageThemeSwitcherComponent } from '../../shared/components/ui/language-theme-switcher/language-theme-switcher.component';

@Component({
  selector: 'app-parent-portal',
  templateUrl: './parent-portal.component.html',
  styleUrls: ['./parent-portal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatSidenavModule,
    MatToolbarModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatMenuModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    LanguageThemeSwitcherComponent
  ]
})
export class ParentPortalComponent implements OnInit {
  parent: ParentDetail | null = null;
  loading = true;
  error = false;

  navItems = [
    { label: 'Dashboard', icon: 'dashboard', route: '/parent-portal/dashboard' },
    { label: 'Profile', icon: 'person', route: '/parent-portal/profile' }
  ];

  studentNavItems = [
    { label: 'Attendance', icon: 'event_available', route: 'attendance' },
    { label: 'Fees', icon: 'payment', route: 'fees' },
    { label: 'Results', icon: 'assessment', route: 'results' },
    { label: 'Leave Applications', icon: 'event_busy', route: 'leaves' }
  ];

  constructor(
    private authService: AuthService,
    private parentService: ParentService,
    private router: Router,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadParentData();
  }

  loadParentData(): void {
    this.loading = true;
    const user = this.authService.getCurrentUser();
    const userId = user?.id;

    if (!userId) {
      this.error = true;
      this.loading = false;
      this.router.navigate(['/login']);
      return;
    }

    // Check if the user has the correct role
    if (!this.authService.hasRole('Parent')) {
      this.error = true;
      this.loading = false;
      this.authService.navigateToUserPortal();
      return;
    }

    // Fetch the parent by user ID
    this.parentService.getParentByUserId(userId)
      .subscribe({
        next: (parent) => {
          this.parent = parent;
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading parent data:', err);
          this.error = true;
          this.loading = false;
          this.showErrorMessage('Failed to load parent data. Please try again later.');
        }
      });
  }

  getStudentRoute(studentId: number, route: string): string {
    return `/parent-portal/student/${studentId}/${route}`;
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
    this.showSuccessMessage('You have been successfully logged out.');
  }

  /**
   * Show a success message
   */
  private showSuccessMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  /**
   * Show an error message
   */
  private showErrorMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }
}
