.schedule-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.schedule-content {
  max-width: 800px;
  margin: 0 auto;
}

.day-selection-card {
  margin-bottom: 24px;
}

.day-selection {
  display: flex;
  justify-content: center;
}

.schedule-loading,
.schedule-error {
  margin-bottom: 24px;
}

.schedule-error {
  text-align: center;
  padding: 16px;

  mat-icon {
    vertical-align: middle;
    margin-right: 8px;
  }
}

.no-schedule {
  text-align: center;
  padding: 16px;
}

.timeline-header {
  text-align: center;
  margin-bottom: 24px;

  h2 {
    font-weight: 500;
    color: #333;
    margin: 0;
  }
}

.timeline {
  position: relative;
  padding: 16px 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 24px;
    width: 4px;
    background-color: #e0e0e0;
    z-index: 0;
  }
}

.timeline-item {
  position: relative;
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.timeline-badge {
  position: absolute;
  top: 16px;
  left: 0;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #3f51b5;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.period-number {
  font-size: 18px;
  font-weight: 500;
  color: white;
}

.timeline-card {
  margin-left: 64px;
}

.period-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .period-details {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}
