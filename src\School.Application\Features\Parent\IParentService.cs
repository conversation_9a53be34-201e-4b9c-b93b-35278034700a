using School.Application.DTOs;
using School.Domain.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace School.Application.Features.Parent
{
    public interface IParentService
    {
        Task<(IEnumerable<ParentDto> Parents, int TotalCount)> GetAllParentsAsync(ParentFilterDto filter);
        Task<ParentDetailDto?> GetParentByIdAsync(Guid id);
        Task<ParentDto?> GetParentByUserIdAsync(string userId);
        Task<Guid> CreateParentAsync(CreateParentDto parentDto);
        Task<bool> UpdateParentAsync(Guid id, UpdateParentDto parentDto);
        Task<bool> DeleteParentAsync(Guid id);

        // Student association methods
        Task<IEnumerable<ParentStudentDto>> GetParentStudentsAsync(Guid parentId);
        Task<bool> AddStudentAsync(Guid parentId, Guid studentId, ParentRelationType relationType, bool isPrimaryContact);
        Task<bool> UpdateStudentRelationAsync(Guid parentId, Guid studentId, ParentRelationType relationType, bool isPrimaryContact);
        Task<bool> RemoveStudentAsync(Guid parentId, Guid studentId);

        // Dashboard methods for parent portal
        Task<IEnumerable<StudentAttendanceDto>> GetStudentAttendanceAsync(Guid parentId, Guid studentId, string? fromDate = null, string? toDate = null);
        Task<IEnumerable<StudentFeeDto>> GetStudentFeesAsync(Guid parentId, Guid studentId, int? academicYear = null);
        Task<IEnumerable<StudentResultDto>> GetStudentResultsAsync(Guid parentId, Guid studentId, string? academicYear = null, string? examType = null);
        Task<IEnumerable<StudentLeaveDto>> GetStudentLeavesAsync(Guid parentId, Guid studentId, string? fromDate = null, string? toDate = null, LeaveStatus? status = null);
        Task<Guid> CreateStudentLeaveAsync(Guid parentId, CreateStudentLeaveDto leaveDto);
    }
}
