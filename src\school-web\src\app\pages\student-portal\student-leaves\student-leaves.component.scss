.leaves-container {
  padding: 16px;
}

.page-title {
  margin-bottom: 24px;
  font-weight: 500;
  color: #333;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.leaves-content {
  max-width: 1000px;
  margin: 0 auto;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-form {
  display: flex;
  align-items: center;
  gap: 8px;
}

.leaves-loading,
.leaves-error {
  margin-bottom: 24px;
}

.leaves-error {
  text-align: center;
  padding: 16px;

  mat-icon {
    vertical-align: middle;
    margin-right: 8px;
  }
}

.no-leaves {
  text-align: center;
  padding: 16px;
}

.leave-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.leave-card {
  margin-bottom: 16px;
}

.leave-avatar {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;

  &.approved {
    background-color: #e8f5e9;
  }

  &.pending {
    background-color: #fff8e1;
  }

  &.rejected {
    background-color: #ffebee;
  }

  &.cancelled {
    background-color: #f5f5f5;
  }

  mat-icon {
    color: #757575;

    .approved & {
      color: #2e7d32;
    }

    .pending & {
      color: #ff8f00;
    }

    .rejected & {
      color: #c62828;
    }

    .cancelled & {
      color: #757575;
    }
  }
}

.leave-status {
  margin-left: auto;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.approved {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.pending {
  background-color: #fff8e1;
  color: #ff8f00;
}

.rejected {
  background-color: #ffebee;
  color: #c62828;
}

.cancelled {
  background-color: #f5f5f5;
  color: #757575;
}

.leave-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.leave-reason {
  margin-bottom: 16px;
}

.leave-dates {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.leave-approval {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.leave-label {
  display: block;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.leave-value {
  font-size: 16px;
  font-weight: 500;
}

mat-card-actions {
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }

  .leave-dates,
  .leave-approval {
    grid-template-columns: 1fr;
  }
}
