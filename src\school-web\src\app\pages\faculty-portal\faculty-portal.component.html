<div class="faculty-portal-container">
  <mat-toolbar color="primary" class="portal-header">
    <button mat-icon-button (click)="sidenav.toggle()">
      <mat-icon>menu</mat-icon>
    </button>
    <span>Faculty Portal</span>
    <span class="spacer"></span>

    <!-- Language & Theme Switcher -->
    <app-language-theme-switcher
      style="icon-only"
      class="header-switcher">
    </app-language-theme-switcher>

    <button mat-icon-button [matMenuTriggerFor]="userMenu">
      <mat-icon>account_circle</mat-icon>
    </button>
    <mat-menu #userMenu="matMenu">
      <button mat-menu-item routerLink="/faculty-portal/profile">
        <mat-icon>person</mat-icon>
        <span>Profile</span>
      </button>
      <button mat-menu-item (click)="logout()">
        <mat-icon>exit_to_app</mat-icon>
        <span>Logout</span>
      </button>
    </mat-menu>
  </mat-toolbar>

  <mat-sidenav-container class="sidenav-container">
    <mat-sidenav #sidenav mode="side" opened class="sidenav">
      <div class="faculty-info" *ngIf="faculty">
        <div class="faculty-avatar">
          <img *ngIf="faculty.profileImage" [src]="faculty.profileImage.filePath" alt="Faculty Photo">
          <mat-icon *ngIf="!faculty.profileImage">account_circle</mat-icon>
        </div>
        <div class="faculty-details">
          <h3>{{ faculty.firstName }} {{ faculty.lastName }}</h3>
          <p>{{ faculty.designation }}</p>
          <p>{{ faculty.department }}</p>
        </div>
      </div>

      <mat-nav-list>
        <a mat-list-item *ngFor="let item of navItems" [routerLink]="item.route" routerLinkActive="active-link">
          <mat-icon matListItemIcon>{{ item.icon }}</mat-icon>
          <span matListItemTitle>{{ item.label }}</span>
        </a>
      </mat-nav-list>
    </mat-sidenav>

    <mat-sidenav-content class="content">
      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
      </div>

      <div *ngIf="error" class="error-container">
        <mat-card>
          <mat-card-content>
            <p>Unable to load faculty data. Please try again later.</p>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button color="primary" (click)="loadFacultyData()">Retry</button>
          </mat-card-actions>
        </mat-card>
      </div>

      <div *ngIf="!loading && !error" class="router-container">
        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>
