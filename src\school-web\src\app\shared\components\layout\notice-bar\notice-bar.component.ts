import { Component, ViewChild, ElementRef, AfterViewInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-notice-bar',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule
  ],
  templateUrl: './notice-bar.component.html',
  styleUrls: ['./notice-bar.component.scss']
})
export class NoticeBarComponent implements AfterViewInit, OnDestroy {
  @ViewChild('noticeScroll') noticeScrollElement!: ElementRef;

  isClosed = false;
  private noticeBarKey = 'notice_bar_closed';

  constructor(private router: Router) {
    // Check if the notice bar was previously closed
    const storedValue = localStorage.getItem(this.noticeBarKey);
    if (storedValue) {
      const { closed, timestamp } = JSON.parse(storedValue);
      // Check if it was closed within the last 24 hours
      const twentyFourHoursAgo = Date.now() - 24 * 60 * 60 * 1000;
      if (closed && timestamp > twentyFourHoursAgo) {
        this.isClosed = true;
      } else {
        // Reset if it's been more than 24 hours
        localStorage.removeItem(this.noticeBarKey);
      }
    }
  }

  ngAfterViewInit(): void {
    // Adjust animation speed based on content width
    this.adjustScrollSpeed();
  }

  ngOnDestroy(): void {
    // Clean up any resources if needed
  }

  /**
   * Adjust the scroll animation speed based on the content width
   */
  private adjustScrollSpeed(): void {
    if (this.noticeScrollElement) {
      const scrollElement = this.noticeScrollElement.nativeElement;
      const scrollWidth = scrollElement.scrollWidth / 2; // Half because we duplicated the content

      // Calculate a reasonable duration based on content width (1 second per 100px)
      const calculatedDuration = Math.max(10, Math.min(60, scrollWidth / 100));

      // Apply the calculated duration
      scrollElement.style.animationDuration = `${calculatedDuration}s`;
    }
  }

  /**
   * Navigate to the notices page
   */
  viewAllNotices(): void {
    this.router.navigate(['/notices']);
  }

  /**
   * Close the notice bar and save the state to localStorage
   */
  closeNoticeBar(): void {
    this.isClosed = true;

    // Save the closed state with a timestamp
    const closeData = {
      closed: true,
      timestamp: Date.now()
    };
    localStorage.setItem(this.noticeBarKey, JSON.stringify(closeData));
  }

  /**
   * Navigate to a specific notice when clicked
   * @param noticeId The ID of the notice to navigate to
   */
  viewNotice(noticeId: string): void {
    this.router.navigate(['/notices', noticeId]);
  }
}
