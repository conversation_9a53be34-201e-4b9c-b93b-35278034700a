import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-auth-test',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatProgressSpinnerModule
  ],
  template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Authentication Test</mat-card-title>
        <mat-card-subtitle>Testing the consolidated HttpRequestInterceptor</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="isLoading" class="loading-spinner">
          <mat-spinner diameter="40"></mat-spinner>
        </div>
        <div *ngIf="!isLoading">
          <h3>Authentication Status</h3>
          <p><strong>Token Present:</strong> {{ !!token }}</p>
          <p><strong>Token:</strong> {{ token ? (token | slice:0:20) + '...' : 'No token' }}</p>
          <p><strong>Token Length:</strong> {{ token ? token.length : 'N/A' }}</p>
          <p><strong>Token Storage Key:</strong> {{ authService.tokenKey }}</p>
          <p><strong>User:</strong> {{ user ? user.username : 'Not logged in' }}</p>
          <p><strong>Role:</strong> {{ user ? user.roleName : 'N/A' }}</p>

          <h3>Test Results</h3>
          <p><strong>Standard Request:</strong> {{ standardTestResult }}</p>
          <p><strong>Explicit Token Request:</strong> {{ explicitTestResult }}</p>
          <p><strong>Interceptor Request:</strong> {{ interceptorTestResult }}</p>

          <h3>Raw Headers</h3>
          <pre>{{ requestHeaders }}</pre>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button color="primary" (click)="testStandardRequest()">
          Test Standard Request
        </button>
        <button mat-raised-button color="accent" (click)="testExplicitToken()">
          Test With Explicit Token
        </button>
        <button mat-raised-button color="warn" (click)="testWithInterceptor()">
          Test With Interceptor
        </button>
      </mat-card-actions>
    </mat-card>
  `,
  styles: [`
    mat-card {
      max-width: 600px;
      margin: 20px auto;
    }
    .loading-spinner {
      display: flex;
      justify-content: center;
      margin: 20px 0;
    }
  `]
})
export class AuthTestComponent implements OnInit {
  token: string | null = null;
  user: any = null;
  standardTestResult: string = '';
  explicitTestResult: string = '';
  interceptorTestResult: string = '';
  requestHeaders: string = '';
  isLoading: boolean = false;

  constructor(
    private http: HttpClient,
    public authService: AuthService
  ) {}

  ngOnInit(): void {
    this.token = this.authService.getToken();
    this.user = this.authService.getCurrentUser();
  }

  testStandardRequest(): void {
    this.isLoading = true;
    this.standardTestResult = '';

    // Check if token exists and is valid
    const token = this.authService.getToken();
    if (!token) {
      this.standardTestResult = 'No valid token available. Please log in again.';
      this.isLoading = false;
      return;
    }

    // Make a standard request to a protected endpoint
    this.http.get(`${environment.apiUrl}/students/by-user-id/${this.user?.id}`).subscribe({
      next: (response) => {
        console.log('Standard API Response:', response);
        this.standardTestResult = 'Success! Authentication is working correctly.';
        this.isLoading = false;

        // Check if we can create a notice to test write operations
        this.testCreateNotice();
      },
      error: (error) => {
        console.error('Standard API Error:', error);
        this.standardTestResult = `Error: ${error.status} - ${error.statusText}. ${error.error?.message || 'Check console for details.'}`;
        this.isLoading = false;
      }
    });
  }

  private testCreateNotice(): void {
    // Simple test notice
    const testNotice = {
      title: 'Test Notice',
      content: 'This is a test notice created to verify authentication',
      category: 'Test',
      startDate: new Date().toISOString(),
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      priority: 1,
      isActive: true,
      translations: []
    };

    // Try to create a notice
    this.http.post(`${environment.apiUrl}/notices`, testNotice).subscribe({
      next: (response) => {
        console.log('Create Notice Response:', response);
        this.standardTestResult += ' Create operation successful!';
      },
      error: (error) => {
        console.error('Create Notice Error:', error);
        this.standardTestResult += ` Create operation failed: ${error.status} - ${error.statusText}`;
      }
    });
  }

  testExplicitToken(): void {
    this.isLoading = true;
    this.explicitTestResult = '';

    // Get the token
    const token = this.authService.getToken();

    if (!token) {
      this.explicitTestResult = 'No token available';
      this.isLoading = false;
      return;
    }

    // Create headers with the token
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };

    // Make a request with explicit token to the students endpoint
    this.http.get(`${environment.apiUrl}/students/by-user-id/${this.user?.id}`, { headers }).subscribe({
      next: (response) => {
        console.log('Explicit Token API Response:', response);
        this.explicitTestResult = 'Success! Authentication with explicit token is working.';
        this.isLoading = false;

        // Now try to create a notice with explicit token
        this.testCreateNoticeWithExplicitToken(token);
      },
      error: (error) => {
        console.error('Explicit Token API Error:', error);
        this.explicitTestResult = `Error: ${error.status} - ${error.statusText}. Check console for details.`;
        this.isLoading = false;
      }
    });
  }

  private testCreateNoticeWithExplicitToken(token: string): void {
    // Simple test notice
    const testNotice = {
      title: 'Test Notice with Explicit Token',
      content: 'This is a test notice created to verify authentication with explicit token',
      category: 'Test',
      startDate: new Date().toISOString(),
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      priority: 1,
      isActive: true,
      translations: []
    };

    // Create headers with the token
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };

    // Try to create a notice with explicit token
    this.http.post(`${environment.apiUrl}/notices`, testNotice, { headers }).subscribe({
      next: (response) => {
        console.log('Create Notice with Explicit Token Response:', response);
        this.explicitTestResult += ' Create operation successful!';
      },
      error: (error) => {
        console.error('Create Notice with Explicit Token Error:', error);
        this.explicitTestResult += ` Create operation failed: ${error.status} - ${error.statusText}`;
      }
    });
  }

  testWithInterceptor(): void {
    this.isLoading = true;
    this.interceptorTestResult = '';

    // Check if token exists and is valid
    const token = this.authService.getToken();
    if (!token) {
      this.interceptorTestResult = 'No valid token available. Please log in again.';
      this.isLoading = false;
      return;
    }

    // Make a request to the students endpoint which requires authentication
    this.http.get(`${environment.apiUrl}/students/by-user-id/${this.user?.id}`).subscribe({
      next: (response) => {
        console.log('Notices API Response:', response);
        this.interceptorTestResult = 'Success! Authentication via interceptor is working.';
        this.isLoading = false;

        // Capture request headers for display
        this.requestHeaders = JSON.stringify(response, null, 2);

        // Now try to create a notice to test write operations with authentication
        this.testCreateNoticeWithInterceptor();
      },
      error: (error) => {
        console.error('Notices API Error:', error);
        this.interceptorTestResult = `Error: ${error.status} - ${error.statusText}. ${error.error?.message || 'Check console for details.'}`;
        this.isLoading = false;
      }
    });
  }

  private testCreateNoticeWithInterceptor(): void {
    // Simple test notice
    const testNotice = {
      title: 'Test Notice via Interceptor',
      content: 'This is a test notice created to verify authentication via interceptor',
      category: 'Test',
      startDate: new Date().toISOString(),
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      priority: 1,
      isActive: true,
      translations: []
    };

    // Try to create a notice - this should use the interceptor to add the token
    this.http.post(`${environment.apiUrl}/notices`, testNotice).subscribe({
      next: (response) => {
        console.log('Create Notice via Interceptor Response:', response);
        this.interceptorTestResult += ' Create operation successful!';
      },
      error: (error) => {
        console.error('Create Notice via Interceptor Error:', error);
        this.interceptorTestResult += ` Create operation failed: ${error.status} - ${error.statusText}`;
      }
    });
  }
}
