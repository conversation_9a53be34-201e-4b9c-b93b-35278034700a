using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using School.Domain.Entities;

namespace School.Infrastructure.Persistence.Configurations;

public class FacultySpecializationConfiguration : IEntityTypeConfiguration<FacultySpecialization>
{
    public void Configure(EntityTypeBuilder<FacultySpecialization> builder)
    {
        builder.Property(t => t.Name)
            .HasMaxLength(200)
            .IsRequired();

        builder.HasOne(t => t.Faculty)
            .WithMany(t => t.Specializations)
            .HasForeignKey(t => t.FacultyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasQueryFilter(fs => !fs.IsDeleted);
    }
}
